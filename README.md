# Matrix Dashboard
## Steps to Run 

##### Clone Project Repository
Get the link below from bitbucket and clone project in your local system.
```
git clone https://<your_user_name>@bitbucket.org/ETECHACES/matrixdashboard.git
```

##### Run npm install to install dependencies

For frontend ( move to client directory and run the following command)
```
cd client
npm install --legacy-peer-deps
```

For backend ( switch to the root directory of the project and the run the following command)
```
npm install
```

##### Creating .env file ( don't remove .env from .gitignore )

Create a .env file in root directory and paste the below code

```
ENVIRONMENT="DEVELOPMENT"
```

##### Run npm start to run the project

for Client project
```
change config.jsx to local for running react project
cd client
npm start
```

for Backend project
```
npm start
```
##### Precautions to take before contribution
- Don't make any changes in auth.js and IpAuth.js
- Please review before making any changes in gitignore.
- Don't merge branch to live created from QA.
- Don't contribute without creating a Pull request.

### Happy Coding :)


