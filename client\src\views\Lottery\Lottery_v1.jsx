import React, { Suspense, useEffect, useRef, useState } from "react";
import { Button } from 'react-bootstrap';

import { LotteryData } from "../../store/actions/CommonAction";

import { connect } from "react-redux";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import './Lottery.css';

import Slots from './SlotMachine';

import Confetti from 'react-confetti'

import { Form, Modal } from 'react-bootstrap';


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
    Table
} from "reactstrap";
//import { getuser } from "utility/utility";
import _ from 'underscore';

let sectors = [
    { color: "#f82", label: "0" },
    { color: "#0bf", label: "1" },
    { color: "#fb0", label: "2" },
    { color: "#0fb", label: "3" },
    { color: "#b0f", label: "4" },
    { color: "#f0b", label: "5" },
    { color: "#bf0", label: "6" },
    { color: "#f82", label: "7" },
    { color: "#0bf", label: "8" },
    { color: "#fb0", label: "9" }
];

function Lottery_v1() {

    const [mustSpin, setMustSpin] = useState(false);
    const [prize, setPrize] = useState('');

    const [ticketCount, setTicketCount] = useState(0);
    const [agentCount, setAgentCount] = useState(0);
    const [eligibleAgentCount, setEligibleAgentCount] = useState(0);

    const [wheelCount, setWheelCount] = useState(0);
    const [firstWheel, setFirstWheel] = useState([]);
    const [WheelResult, setWheelResult] = useState([]);
    const [CurrentQuiz, setCurrentQuiz] = useState([]);
    const [BuList, setBuList] = useState([]);
    const [Tickets, setTickets] = useState([]);
    const [RewardsList, setRewardsList] = useState([]);
    const [RewardsDropdownList, setRewardsDropdownList] = useState([]);
    const [SelectedProduct, setSelectedProduct] = useState(0);
    const [SelectedBuId, setSelectedBuId] = useState(0);
    const [SelectedRewardId, setSelectedRewardId] = useState(0);
    const [RewardCurrentStatus, setRewardCurrentStatus] = useState({});
    const [WinnerData, setWinnerData] = useState(null);
    const [Winners, setWinners] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [changeMachine, setChangeMachine] = useState(true);
    const slotRef = useRef(null);

    const handleClose = () => {
        setShowModal(false);
    }

    useEffect(() => {
        if (ticketCount > 99) {
            setWheelCount(3)
        }
        else if (ticketCount > 9) {
            setWheelCount(2)
        }
        else {
            setWheelCount(1)
        }

        let tc = ticketCount.toString()
        let tc1 = tc.split('')[0];
        tc1 = parseInt(tc1);
        let fw = [];
        for (let index = 0; index <= tc1; index++) {
            const element = sectors[index];
            fw.push(element)
        }

        setFirstWheel(fw)

    }, [ticketCount]);

    useEffect(() => {
        debugger;
        if(CurrentQuiz && CurrentQuiz.length==0){
        getCurrentQuiz(SelectedProduct);
        }

    }, [CurrentQuiz])

    useEffect(() => {
        if (SelectedRewardId) {
            var rewardStatus = _.filter(RewardsList, function (item) { return item.RewardId == SelectedRewardId; });
            console.log(rewardStatus);
            setRewardCurrentStatus(rewardStatus[0]);
        }
    }, [SelectedRewardId, RewardsList])

    const onBUChange = (e) => {
        setRewardsList([]);
        setSelectedRewardId(0);

        setSelectedBuId(e.target.value);
        getRewardsList(e.target.value);
        setRewardsDropdownList([]);
        setWheelCount(0);
    }

    const onProductChange = (e) => {
        setRewardsList([]);
        setSelectedProduct(e.target.value);
        setSelectedRewardId(0);

        setSelectedBuId(0);
        getBuListData(e.target.value);
        setRewardsDropdownList([]);
        setWheelCount(0);
    }

    const onRewardChange = (e) => {
        setWheelCount(0);
        setChangeMachine(true);
        setSelectedRewardId(e.target.value);

        getTicketCount(e.target.value);
        getWinners(e.target.value);
        //showRewardsCurrentStatus();
    }

    const getTicketCount = (RewardId) => {
        LotteryData({
            root: "getTicketCount",
            params: { BUId: SelectedBuId, RewardId: RewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                setTicketCount(result.data.data[0][0].TicketCount);
                setAgentCount(result.data.data[0][0].AgentCount);
                setEligibleAgentCount(result.data.data[0][0].EligibleAgents);
            }
        });
        LotteryData({
            root: "GetTickets",
            params: { BUId: SelectedBuId, RewardId: RewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let tickets = result.data.data[0];
                let tarray = [];
                for (let index = 0; index < tickets.length; index++) {
                    const element = tickets[index];
                    let e =element.TicketNumber;
                    //tarray.push(e.substr(e.length - 7));
                    tarray.push(e);
                }
                setTickets(tarray)
            }
        });
    }

    const getBuListData = (ProductId) => {
        console.log(CurrentQuiz)
        if(CurrentQuiz)
        LotteryData({
            root: "getBuList",
            params: { ProductId: ProductId, QuizId : CurrentQuiz && CurrentQuiz[0] && CurrentQuiz[0].ID}
        }, (result) => {
            //console.log(result.data);
            if (result.data && result.data.status == 200) {
                setBuList(result.data.data[0]);
            }
            return;
        });
    }
    const getCurrentQuiz = (ProductId) => {
        var url_string = document.location;
        var url = new URL(url_string);
        var q = url.searchParams.get("q");
        LotteryData({
            root: "getCurrentQuiz",
            params: {QuizId: q}
        }, (result) => {
            //console.log(result.data);
            if (result.data && result.data.status == 200) {
                setCurrentQuiz(result.data.data[0]);
            }
            return;
        });
    }

    const getRewardsList = (BuId) => {
        LotteryData({
            root: "getRewardsList",
            params: { BUId: BuId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let resData = [];
                result.data.data[0] && result.data.data[0].map((value) => {
                    resData.push({ Id: value.RewardId, Display: value.RewardTitle });
                })
                setRewardsDropdownList(resData);
                //debugger;
                setRewardsList(result.data.data[0]);
            }
            return;
        });
    }
    const getRewardDetails = (BuId, RewardId) => {
        LotteryData({
            root: "GetRewardDetails",
            params: { BUId: BuId, RewardId: RewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let resData = result.data.data[0][0];
                if (resData.PendingRewards > 0) {
                    setTimeout(function () {
                        onClickDraw();
                    }, 1000)

                }
                else {
                    setShowModal(true);
                }


            }
            return;
        });
    }

    const getWinnerDetail = (prize, cb) => {
        LotteryData({
            root: "GetWinnerDetail",
            method: "post",
            params: { BUId: SelectedBuId, RewardId: SelectedRewardId, TicketNumber: prize }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                // let resData = result.data.data[0][0];
                let resData = result.data.data;
                setWinnerData(resData[0][0]);
                if (cb) {
                    cb(resData);
                }
            }
            return;
        }, 'post');
    }
    const getWinners = (SelectedRewardId) => {
        LotteryData({
            root: "GetWinners",
            params: { BUId: SelectedBuId, RewardId: SelectedRewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                //debugger;
                // let resData = result.data.data[0];
                let resData = result.data.data;
                setWinners(resData);
            }
            return;
        }, 'get');
    }

    const bindWheels = (tickets) => {
        let result = <></>;
        if (tickets && tickets.length > 0)

            result = <Col md="12">
                
                <Slots Tickets={Tickets} onResult={onResult} ref={slotRef} changeMachine = {changeMachine}/>
            </Col>

        return result;
    }


    // useEffect(() => {
    //     debugger;
    //     let _p = prize;
    //     for (let index = 0; index < WheelResult.length; index++) {
    //         const e = WheelResult[index];
    //         _p[e.id] = e.label;
    //     }
    //     setPrize(_p);

    // }, [WheelResult, prize]);


    const onResult = (e) => {
        //alert(e)
        //debugger;

        // let wr = WheelResult
        // wr.push(e);
        // setWheelResult(wr);


        onFinalResult(e);
        // setTimeout(function () {
        //     getWinnerDetail();
        // }, 1000);


    }

    const onFinalResult = (e) => {
        //let res = e.substr(e.length - 3);
        let res = e;
        // let EL_spin = document.getElementsByClassName("spin");
        // for (let index = 0; index < EL_spin.length; index++) {
        //     const element = EL_spin[index];
        //     res += element.innerHTML;
        // }

        //let _p = prize;
        //_p[e.id] = e.label;
        setPrize(res);
        getWinnerDetail(res, (data) => {
            if (data.RETVAL == 1) {
                setTimeout(function () {
                    onClickReset();
                }, 5000)
            }
            else {
                setTimeout(function () {
                    onClickReset();
                }, 2000)
            }
        });



    }

    const onClickReset = () => {
        setPrize('');
        // setMustSpin(!mustSpin);
        setWheelResult([]);
        setTicketCount(0);
        setAgentCount(0);

        setWheelCount(0);
        getTicketCount(SelectedRewardId);
        getRewardsList(SelectedBuId);
        getWinners(SelectedRewardId)
        setWinnerData(null);
        console.log(RewardCurrentStatus);

        getRewardDetails(SelectedBuId, SelectedRewardId);
        //window.location.reload();
    }
    const onClickDraw = () => {
        //setMustSpin(true)
        //debugger;
        try {
            slotRef.current.roll();
            setPrize('');
            setChangeMachine(false);
            // let EL_spin = document.getElementsByClassName("spin");
            // for (let index = 0; index < EL_spin.length; index++) {
            //     const element = EL_spin[index];
            //     element.click();
            // }
            setWheelResult([]);
            // setTimeout(function () {
            //     onFinalResult();
            //     // setTimeout(function () {
            //     //     getWinnerDetail();
            //     // }, 1000);
            // }, 12000)
        }
        catch (e) {
            console.log(e)
        }
    }

    const showDrawBtn = () => {
        let result = true;

        if (prize) {
            result = false
        }
        if (SelectedBuId == 4 && SelectedRewardId == 2) {
            result = false
        }
        if (SelectedBuId == 4 && SelectedRewardId == 3) {
            result = false
        }

        return result;
    }

    return (
        <>
            <div className="content">
                <ToastContainer />
                {WinnerData && WinnerData.RETVAL == 1 && <Confetti />}
                <Row>
               
                    <Col md="3" className="text-left"><img src="/lottery/pblogo.png" className="pbLogo" /></Col>
                    <Col md="6" className="contest-name text-center">
                    {CurrentQuiz && CurrentQuiz[0] && CurrentQuiz[0].QuizName }
                    </Col>
                    </Row>
                <Row>
                    <Col md="3">
                    </Col>
                    <Col md="2">
                        <label>Product</label>
                        <Form.Control as="select" name="Product" onChange={(e) => onProductChange(e)}>
                            <option key={0} value=''>Select Product</option>
                            <option key={1} value='2'>Health</option>
                            <option key={2} value='7'>Term</option>
                            <option key={3} value='115'>Saving</option>
                            <option key={3} value='117'>Motor Fresh</option>
                        </Form.Control>
                    </Col>
                    <Col md="2">
                        <label>Process</label>
                        <Form.Control as="select" name="BU" onChange={(e) => onBUChange(e)}>
                            <option key={0} value=''>Select Process</option>
                            {
                                BuList.map(item => (
                                    <option key={item.Id} value={item.Id}>{item.Display}</option>
                                ))
                            }
                        </Form.Control>
                    </Col>
                    <Col md="2">
                        <label>Prize</label>
                        <Form.Control as="select" name="Reward" onChange={(e) => onRewardChange(e)}>
                            <option key={0} value=''>Select Reward</option>
                            {
                                RewardsDropdownList.map(item => (
                                    <option key={item.Id} value={item.Id}>{item.Display}</option>
                                ))
                            }
                        </Form.Control>
                    </Col>
                    <Col md="3">
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("ACTIVA") > -1 && <img src="/lottery/activa.png" className="winnerprize" />}
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("LED") > -1 && <img src="/lottery/led.png" className="winnerprize" />}
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("CAR") > -1 && <img src="/lottery/car.svg" className="winnerprize" />}

                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle &&
                            // <div className={"details"}>

                            //     <p>
                            //         {RewardCurrentStatus.RewardTitle}

                            //     </p>
                            //     <span>{RewardCurrentStatus.PendingRewards}/{RewardCurrentStatus.MaxRewards}</span>
                            // </div>
                            <ul className={"details"}>
                                <li onClick={() => setShowModal(true)}><p> {RewardCurrentStatus.RewardTitle}</p><span>{RewardCurrentStatus.PendingRewards}/{RewardCurrentStatus.MaxRewards}</span></li>
                                <li><p>Total Agents</p><span>{agentCount}</span></li>
                                <li><p>Eligible Agents</p><span>{eligibleAgentCount}</span></li>
                                <li><p>Total tickets</p><span>{ticketCount}</span></li>
                            </ul>
                        }

                    </Col>
                </Row>
                <br />
                <Row className="justify-content-md-center mt-1">
                   

                    
                        {bindWheels(Tickets)}
                    

                    {/* <ul>
                        {Tickets.map(item => (
                                <li><p>{item.TicketNumber}</p><span>{item.AssignToUserId}</span></li>

                            ))}
                            </ul> */}



                </Row>


                {RewardCurrentStatus && RewardCurrentStatus.PendingRewards > 0 &&
                    <Row className="justify-content-md-center winnerSection">
                        <Col md="auto" className="mt-1">
                            {prize &&
                                <div className="winner">
                                    <h3>Winner Ticket No. {prize}</h3>
                                    {/* {WinnerData && WinnerData.RETVAL == 0 && <p>Invalid Ticket</p>} */}
                                    {/* WinnerData.RETVAL == 1  */}
                                    {WinnerData && <p>{WinnerData.EmployeeId} {WinnerData.UserName} {WinnerData.Location || 'Gurgaon'}</p>}
                                </div>
                            }
                           
                            {showDrawBtn() && <Button onClick={() => onClickDraw()} className="startBtn">Start Draw</Button>}
                        </Col>
                        {prize && <Button onClick={() => onClickReset()} className="resetBtn"><i class="fas fa-undo"></i></Button>}
                    </Row>
                }

                <Row>
                    <Col>

                        <ul className={"winners"}>
                            {Winners && Winners.map(item => (
                                <li><p> Winner {item.winno} <br />{item.TicketNumber}</p><span>{item.EmployeeId}<br />{item.UserName}</span></li>

                            ))}
                        </ul>
                    </Col>
                </Row>
                <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
                    <Modal.Header closeButton>
                        <Modal.Title>Winners</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>

                        <Row>

                            <ul className={"winners top0"}>
                                {Winners && Winners.map(item => (
                                    <li><p>
                                        Winner {item.winno}<br />
                                        {item.TicketNumber}</p><span>{item.EmployeeId}<br />{item.UserName}<br />{item.Location || 'Gurgaon'}</span></li>

                                ))}
                            </ul>
                        </Row>

                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary"  className="startBtn mt-0" onClick={handleClose}>
                            Close
                        </Button>

                    </Modal.Footer>
                </Modal>
            </div>
        </>
    );

}

export default Lottery_v1;

