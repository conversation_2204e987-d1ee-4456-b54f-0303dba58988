import React, { useEffect, useState } from "react";
import { FetchHealthRenewalPaymentLinkService } from "../../store/actions/CommonAction";
import ViewRequestDetails from "./ViewRequestDetails"
import "react-toastify/dist/ReactToastify.css";
import { TableContainer, TableHead, TableCell, TableRow, TableBody, Button, ButtonGroup } from "@mui/material";
import { Form, Table, Modal } from 'react-bootstrap';
import { Card, CardHeader, CardBody, CardTitle, Row, Col } from "reactstrap";
import Moment from "react-moment";


const ViewPaymentLinkRequests = (props) => {
    let [data, setdata] = useState({});
    let [showDetails, setshowDetails] = useState(false);
    let [rowId, setrowId] = useState(0);
    let isSupervisor = props.menuaccess == "Supervisor" ? 1 : 0

    useEffect(() => {
        FetchHealthRenewalPaymentLinkService({ Type: isSupervisor == 1 ? 3 : 4 }, function (resultData) {
            if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                setdata(resultData.data.data[0]);
            }
        })

    }, []);

    const handleViewDetails = (row) => {
        setshowDetails(true);
        setrowId(row.Id);
    }

    const handleViewDetailsClose = () => {
        setshowDetails(false);
        setrowId(0);
    }

    return (
        <div>
            <Row>
                <Col md="12">
                    <Form>
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">Payment Link Request</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                {data && Array.isArray(data) && data.length > 0 ?
                                    <div className="RenewalPaymentLink">
                                        <br />
                                        <TableContainer className="w-full rounded-lg shadow-md">
                                            <Table striped bordered hover>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell align="center">Lead ID</TableCell>
                                                        <TableCell align="center">Amount</TableCell>
                                                        <TableCell align="center">CreatedOn</TableCell>
                                                        <TableCell align="center">RequestAgent</TableCell>
                                                        <TableCell align="center">Supervisor</TableCell>
                                                        {isSupervisor == 0 && <TableCell align="center">L2ApprovalReason</TableCell>}
                                                        <TableCell align="center">Action</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {data.map((row) => (
                                                        <TableRow>
                                                            <TableCell align="center">
                                                                {row.LeadID ? row.LeadID : "N/A"}
                                                            </TableCell>

                                                            <TableCell align="center">
                                                                {row.PremiumAmount ? row.PremiumAmount : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.CreatedOn ? <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
                                                                    {row.CreatedOn}
                                                                </Moment> : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.RequestAgent ? row.RequestAgent : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.Supervisor ? row.Supervisor : "N/A"}
                                                            </TableCell>
                                                            {isSupervisor == 0 && <>
                                                                <TableCell align="center">
                                                                    {row.L2ApprovalReasonID ? (row.L2ApprovalReasonID).replace("_", " ") : "N/A"}
                                                                </TableCell>
                                                            </>}
                                                            <TableCell align="center">
                                                                <ButtonGroup aria-label="Basic example">
                                                                    <Button fullWidth variant="contained" className="btn btnSmall" color="success" onClick={() => handleViewDetails(row)}>
                                                                        View Details
                                                                    </Button>
                                                                </ButtonGroup>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>

                                            </Table>
                                        </TableContainer>
                                    </div>
                                    :
                                    <Row>
                                        <Col>
                                            <br />
                                            <h5 className="text-danger">No Data Found!</h5>
                                        </Col>
                                    </Row>
                                }
                            </CardBody>
                        </Card>
                    </Form>
                </Col>
            </Row>
            <div className="content">
                <Modal
                    show={showDetails}
                    onHide={handleViewDetailsClose}
                    dialogClassName="modal-90w"
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Agent Request Details</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="uploadLeadPopup">
                        <ViewRequestDetails Id={rowId} RequestPanel={isSupervisor == 1 ? "Supervisor" : "L2Request"} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleViewDetailsClose}>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
        </div>
    )

}

export default ViewPaymentLinkRequests;