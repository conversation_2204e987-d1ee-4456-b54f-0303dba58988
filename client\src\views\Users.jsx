
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData,GetCommonspData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from './Common/DataTableWithFilter';
import { fnRenderfrmControl,fnBindRootData, fnDatatableCol,joinObject, fnCleanData, GetJsonToArray, getUrlParameter, getuser } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDown from './Common/DropDown';

class Users extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      activePage: 1,
      root: "GetDashboardUserDetails",
      PageTitle: "Users",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      asteriskUrl:'',
      userid: 90096,
      fields: [],
      condition: {}
    };
    this.handleClose = this.handleClose.bind(this);

    this.handleSave = this.handleSave.bind(this);
    this.selectedrow = { "UserID": 0, "UserName": "", "EmployeeId": "", "Asterisk_IP": "", "Asterisk_Url": "", "IsProgressive": "", "CreatedOn": new Date() }
    this.columnlist = [
      {
        name: "UserName",
        label: "UserName",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true
      },
      {
        name: "UserID",
        label: "UserID",
        type: "hidden",
        hide: true,
        sortable: false,
        //searchable: false
      },
      {
        name: "EmployeeId",
        label: "EmployeeId",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true
      },
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        config: {
          root: "GetUserProductList",
          cols: ["ProductId AS Id", "ProductDisplayName AS Display"],
          con: [{ "UserId": this.state.userid }],
          sp: true,
          Idfield: /ProductId/g,
          Displayfield: /ProductDisplayName/g,
        },
        //searchable: true,
        hide: true,
      },
      {
        name: "Asterisk_Url",
        label: "Asterisk_Url",
        type: "dropdown",
        config: {
          root: "FetchAsteriskUrl",
          cols: ["DISTINCT Id AS Id", "Display AS Display","ProductID"],
        },
        sortable: true,
        //searchable: true,
      },
      {
        name: "Asterisk_IP",
        label: "Asterisk_IP",
        type: "string",
        sortable: true,
        //searchable: true,
        editable: false,
      },    
      {
        name: "CallingCompany",
        label: "CallingCompany",
        type: "dropdown",
        config: {
          root: "FetchCallingCompany",
          cols: ["Id AS Id", "Display AS Display"],
        },       
        sortable: true,
        //searchable: true
      },
      {
        name: "DIDNo",
        label: "DIDNo",
        type: "string",
        sortable: true,
        //searchable: true
      }, {
        name: "IsProgressive",
        label: "IsProgressive",
        type: "bool",
        sortable: true,
        editable: false,
        //searchable: true  
      }, 
      {
        name: "ISLDapEnable",
        label: "ISLDapEnable",
        type: "bool",
        sortable: true,
        editable: false,
      }, 
      {
        name: "IsWFH",
        label: "IsWFH",
        type: "bool",
        sortable: true,
        //searchable: true  
      },
      {
        name: "AswatPosition",
        label: "IsAswatRegistered",
        type: "bool",
        cell: row => <div>{row.AswatPosition ? true : false}</div>,
        editable: false
      }
    ];

  }

  componentDidMount() {
  
     var columns = this.columnlist;
     columns = columns.filter((res) => res.label !== "Product");
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    // this.props.GetCommonData({
    //   limit: 10,
    //   skip: 0,
    //   root: this.state.root,
    //   cols: GetJsonToArray(columns, "name"),
    //   //con: [{ "Isactive": 1 ,"UserName_likesearch": "Gunjan"}]
    //   //con: [{ "Isactive": 1 ,"DIDNo":"8279338060"}]
    //   con: [{ "Isactive": 1 ,"IsBMS": 0}]
    // });
  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root][0], nextProps.CommonData[col.config.root], col.name)
        if(this.state.condition){
        this.setState({ items: items });
        }else{
          this.setState({ items: [] });
        }
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if(this.state.condition){
      this.setState({ items: (nextProps.CommonData[this.state.root])?nextProps.CommonData[this.state.root][0] :[]});
      }else{
        this.setState({ items : []})
      }
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }

  }

  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        {/* <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button> */}
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }


  handleEdit(row) {
    row.Asterisk_Url= row.Asterisk_IP
     this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }

  handleClose() {
    this.setState({ showModal: false });
  }


  handleSave() {

    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    //this.fnCleanData(formvalue, true)
    formvalue["Asterisk_Url"] = (this.state.asteriskUrl != 'Select')?this.state.asteriskUrl:''
    delete formvalue["ProductID"]

    if (this.state.event === "Edit") {
      if(formvalue["Is_Editable"] == 0){
        delete formvalue["Asterisk_Url"]
        delete formvalue["Asterisk_IP"]
      }
      if(formvalue["IsWFH"] && formvalue["IsWFH"] == 1 && !formvalue["DIDNo"]){
        alert("DIDNo is required ");
        return false;
      }
      if(formvalue["DIDNo"]){
        if(['WFH','WFH_NEW'].includes(formvalue["CallingCompany"]) || (formvalue["IsWFH"] && formvalue["IsWFH"] == 1)){
        var pat1=/^\d{10}$/;
        if(!pat1.test(formvalue["DIDNo"]))
        {
        alert("DID No should be of 10 digits");
        return false;
        }
      }else{
        let pat2 = /^\s+|\s+$/g; 
        if(pat2.test(formvalue["DIDNo"]))
        {
        alert("DID No should not contain spaces at start and end");
        return false;
        }
      }
      }
      let id = formvalue["UserID"];
      delete formvalue["UserID"]
      delete formvalue["CallingCompany_display"]
      delete formvalue["Asterisk_Url_display"]
      delete formvalue["Is_Editable"]
      //this.fnCleanData(formvalue, true);
      this.props.UpdateData({
        root: 'Users',
        body: formvalue,
        c: "L",
        querydata: { "UserID": id }
      }, function (data) {
        toast("Record updated successfully...", { type: 'success' });
      });


      this.props.addRecord({
        root: "History",
        body: {
          module: "Users",
          od: this.state.od,
          nd: formvalue,
          ts: new Date(),
          by: getuser().UserID
        }
      });
      this.setState({ showModal: false });

    } else if (this.state.event === "Copy") {

    } else {

    }
    var columns = this.columnlist;
    columns = columns.filter((res) => res.label !== "Product");
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    setTimeout(function () {
      this.props.GetCommonspData({
        root: this.state.root,
        cols: GetJsonToArray(columns, "name"),
        params: this.state.condition,
      });
    }.bind(this), 2000);
    this.setState({ showModal: false });
    return false;
  }
  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;

    var selAsteriskUrl = document.getElementById("Asterisk_Url");
    var text= selAsteriskUrl.options[selAsteriskUrl.selectedIndex].text;
    this.setState({ asteriskUrl: text })
    if (e.target && e.target.type === "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else if (e.target && e.target.type === "select-one") {
      formvalue[e.target.id] = e.target.value === "" ? null : e.target.value;
      if(e.target.id == 'Asterisk_Url'){
          formvalue['Asterisk_IP'] = e.target.value
          var index = e.target.selectedIndex;
          this.setState({asteriskUrl: e.target[index].text})
      }
    }
    else {
      formvalue[e.target.id] = e.target.value === "" ? null : e.target.value;
    }

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  fnRenderfrmControl(col, formvalue, handleChange, event) {

    if (col.name == "ProductID") {
      if (formvalue && formvalue.UserID) {
        col.config = {
          root: "GetUserProductList",
          cols: ["ProductId AS Id", "ProductDisplayName AS Display"],
          con: [{ "UserId": formvalue.UserID }],
          sp: true,
          Idfield: /ProductId/g,
          Displayfield: /ProductDisplayName/g,
        }
      }
    }

    if(col.name == "Asterisk_Url"){
      if(formvalue && formvalue.Is_Editable == 0){
        col.editable = false;
      }else{
        col.editable = true;
      }
    }
    if(col.name == "DIDNo"){
      if(formvalue && formvalue["IsWFH"] && formvalue["IsWFH"] == 1){
        col.maxLength = 10;
      }else{
        col.maxLength = '';
      }
    }
    return fnRenderfrmControl(col, formvalue, handleChange, event)

  }


  getUsers(){
    var columns = this.columnlist;
    columns = columns.filter((res) => res.label !== "Product");
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    const inputEmpId = this.state.fields['empid'];
    const inputDIDNo = this.state.fields['DIDno'];
    const inputUserName = this.state.fields['UserName'];
    // if(inputEmpId || inputDIDNo || inputUserName){
    //   condition.push({ "IsBMS": 0 ,"Isactive": 1});
    // }
    // if (inputEmpId) {
    //   condition.push({ "EmployeeId":inputEmpId});
    // }
    // if (inputDIDNo) {
    //   condition.push({ "DIDNo":inputDIDNo});
    // }
    // if (inputUserName) {
    //   condition.push({ "UserName_likesearch":inputUserName});
    // }
    this.setState({ condition : [{EmployeeId : inputEmpId, UserName: inputUserName, inputDIDNo: inputDIDNo}] })
    if(this.state.condition){
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      c: "L",
      cols: GetJsonToArray(columns, "name"),
      params: [{EmployeeId : inputEmpId, UserName: inputUserName, inputDIDNo: inputDIDNo}]
    });
    }
  }

  handleChangeFields(field, e){         
    let fields = this.state.fields;
    fields[field] = e.target.value;        
    this.setState({fields});
}

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={2}>
                    <Form.Control
                        type="text"
                        name="empid" value={this.state.fields['empid']} className="empid" id="empid" onChange={this.handleChangeFields.bind(this, "empid")}
                        placeholder={"Enter EmployeeId"}
                      />
                      </Col>
                      <Col md={2}>
                      <Form.Control
                        type="text"
                        name="UserName" value={this.state.fields['UserName']}  onChange={this.handleChangeFields.bind(this, "UserName")}
                        placeholder={"Enter Username"}
                      />
                    </Col>
                    <Col md={2}>
                    <Form.Control
                        type="text"
                        name="DIDno" value={this.state.fields['DIDno']} className="DIDno" id="DIDno" onChange={this.handleChangeFields.bind(this, "DIDno")}
                        placeholder={"Enter DIDno"}
                      />
                    </Col>
                    
                    <Col md={1}>
                      <Button variant="primary" onClick={this.getUsers.bind(this)}>
                        Fetch
                      </Button>
                    </Col>

                
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form>
                <Row>
                  {this.columnlist.map(col => (
                    this.fnRenderfrmControl(col, this.state.formvalue, this.handleChange, this.state.event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <Button variant="primary" onClick={this.handleSave}>Save Changes</Button>
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    InsertData,
    UpdateData,
    addRecord
  }
)(Users);