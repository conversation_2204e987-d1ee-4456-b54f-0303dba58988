const { Kafka } = require('kafkajs');
const moment = require('moment');

async function connectToKafka() {

    const AWSSecretConfig = global.SecretConfig;
   
    if (AWSSecretConfig) {
      let KafkaConfig = AWSSecretConfig.Kafka_Server;
      let kafkaBrokers = KafkaConfig.KafkaBootstrapServers.split(',');
  
      try {
  
        let kafka = await new Kafka({
  
          brokers: kafkaBrokers,
          sasl: {
            mechanism: 'plain',
            username: KafkaConfig.KafkaUserName,
            password: KafkaConfig.KafkaPassword
          },
          retry: {
            retries: 2
          },
        });
  
  
        // const topic = "fos-appointmentData-topic";
        
  
        const consumer =  kafka.consumer({ groupId: `group-${moment().format('YYYYMMDDHHmmssSSS')}`, autoOffsetReset: 'earliest' });
        // console.log("The consumer is ", consumer);

        await consumer.connect();
  
        return consumer;
      
    }
    catch(e)
    {
      console.log("Error in ConnectToKafka: ", e);
      return null
    }
  }
    
}

module.exports = { connectToKafka };