import { Button, Modal, Table } from 'react-bootstrap';
import { ModifyType, RemarksTable } from "./Utility";

const ViewRemarksModal = (props) => {
  const { data } = props;

  return (
    <>
      <Modal
        {...props}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        fullscreen={true}
        onHide={props.onViewRemarksCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            View Remarks
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Table responsive>
            <thead>
              <tr>
                {
                  RemarksTable && RemarksTable.length > 0 &&
                  RemarksTable.map((item, index) => {
                    return <th key={index}>{item.name}</th>
                  })
                }
              </tr>
            </thead>
            <tbody>
              {
                data && data.length > 0 &&
                data.map((row, serial) =>
                  RemarksTable && RemarksTable.length > 0 &&
                  <tr>
                    {
                      RemarksTable.map((item, index) => {
                        const ModifiedColumnData = ModifyType(row[item.accessor], item.type);
                        if (index === 0) {
                          return <td key={index} data-title={item.name}>{serial + 1}</td>
                        }
                        return <td key={index} data-title={item.name}>{ModifiedColumnData}</td>
                      }
                      )
                    }
                  </tr>
                )
              }
            </tbody>
          </Table>

        </Modal.Body>
        <Modal.Footer>
          <Button onClick={props.onViewRemarksCancel}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default ViewRemarksModal;