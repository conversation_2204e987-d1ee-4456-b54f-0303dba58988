import React from 'react';
import { getUrlParameter } from '../../utility/utility';

const FullScreenNewTab = () => {

  const expand = getUrlParameter('expand');

  const handleOpenUrlNewTab = () => {
    let url = document.location.href + "?expand=0"
    window.open(url, "_blank");
  }

  return (
    <>
      {expand !== '0' &&  <i title='Full Screen' className="fa fa-arrows-alt" onClick={handleOpenUrlNewTab}></i>}
    </>
  );
}

export default FullScreenNewTab;