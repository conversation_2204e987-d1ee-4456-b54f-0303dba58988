
import React from "react";
import {
  GetCommonData, GetCommonspData, SalesView
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { LeadView, getuser, OpenNewSalesView } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDown from './Common/DropDown';
import ManagerHierarchy from './Common/ManagerHierarchy';
import FOSTLDashboardBoxes from "./FOSTLDashboardBoxes";
// import result from "./../data/FosTLData.json";

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { Form, Button, Modal } from 'react-bootstrap';
import './customStyling.css';
import { hhmmss } from "utility/utility";
import moment from 'moment';
import FOSSelfieDetails from "./FOS/FOSSelfieDetails";
import FosTLDashboardModal from "./FosTLDashboardModal";

class FOSTLDashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "FOS Realtime Panel",
      formvalue: {},
      FOSTLDashboardData: [],
      FOSTLDashboardDataCount: [],
      OriginalData:[],
      key: "ALL",
      AppointmentSlots: [],
      AgentList: [],
      Agentkey: "ALL",
      IsLoading: false,
      appointmentId: null,
      statusId: null,
      clickImage: false,
      clickModalUrl: null,
      row: [],
      SelectedSupervisors: [],
      EmployeeIds: [],
      category: 1,
    };
    this.handleShow = this.handleShow.bind(this);
    this.dtRef = React.createRef();
    this.myInputRef = React.createRef();
    this.slotchange = this.slotchange.bind(this);
    this.agentchange = this.agentchange.bind(this);
    this.handleModal = this.handleModal.bind(this);

    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 24) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );

    this.columnlist = [
      // {
      //   name: "UserID",
      //   selector: "UserID",        
      // },
      {
        name: "Emp. Code",
        selector: "EmployeeId",      
        cell: row => row.EmployeeId ? row.EmployeeId : "N.A",
        width: "100px",  
      },
      {
        name: "Agent Name",
        selector: "Agent_Name",     
        cell: row => row.Agent_Name ? row.Agent_Name : "N.A"   ,
        width: "120px",
      },
      {
        name: "Lead ID",
        selector: "Leadid",   
        width: "100px",   
        // cell: row => row.Leadid ? row.Leadid : "N.A"   
        cell: row => <div>
                {/* <a href={LeadView(row.CustomerID, row.Leadid, row.ProductID, getuser().UserID)} target="_blank">{row.Leadid} </a> */}
              <a href={OpenNewSalesView(row.CustomerID, row.Leadid, row.ProductID)} target="_blank">{row.Leadid} </a>
            </div>,
        // // cell: row => <div style={{ color: '#0d6efd', textDecoration: 'underline' }}>
        // //         <a onClick={(e) => this.openSalesView(e, row.Leadid)} >{row.Leadid}</a>
        // //     </div>,
      },
      // {
      //   name: "Appointment Slot",
      //   selector: "Slot",  
      //   cell: row => row.Slot ? row.Slot : "N.A"
      // },
      {
        name: "Appointment Date Time",
        selector: "Appointment_Datetime", 
        cell: row => <div className="calldate">{row.Appointment_Datetime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Appointment_Datetime}</Moment> : "N.A"}</div>,  
        type: "datetime", 
        sortable: true,  
        width: "100px",   
        format: 'YYYY-MM-DD HH:mm:ss',   
      },
      {
        name: "Agent Status",
        selector: "Custom_Status",     
        cell: row => <div className={row.Color + " RealtimeStatus"}>{row.Custom_Status ? row.Custom_Status : "N.A"}</div>,
        //cell: row => <div>{row.Custom_Status ? row.Custom_Status : "N.A"}</div>,
        width: "172px",  
      },
      {
        name: "Appointment Status",
        selector: "Custom_AppointmentStatus",     
        cell: row => row.Custom_AppointmentStatus ? row.Custom_AppointmentStatus : "N.A",
        
      },
      {
        name: "Fresh Appt.",
        selector: "FreshAppointment",     
        cell: row => row.FreshAppointment ? row.FreshAppointment : "N.A",
        width: "80px", 
      },
      {
        name: "Is F2F",
        selector: "CreatedByFloorAdvisor",
        cell: row=>  row.CreatedByFloorAdvisor!=null  ?  (row.CreatedByFloorAdvisor==true ? 1:0) : "-"
      },
      {
        name: "Last Comment",
        selector:"CommentByEmpId",
        width: "200px",      
        cell: row => <div >
          {row.CommentByEmpId ?  row.CommentByEmpId : ""} || {row.CommentByUserName ?  row.CommentByUserName : ""} 
          <br /> ({ row.CommentCreatedOn ? <Moment format="Do MMM, h:mm" utc={true}
        >{row.CommentCreatedOn}</Moment>: "N.A" })<br /> {row.LastComment? row.LastComment:""}</div>,  
      },
      {
        name: "Updated Appt. Time",
        selector: "Updated_AppointmentTime", 
        cell: row => <div className="calldate">{row.Updated_AppointmentTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Updated_AppointmentTime}</Moment> : "N.A"}</div>,  
        type: "datetime", 
        sortable: true,  
        width: "100px",     
        format: 'YYYY-MM-DD HH:mm:ss', 
      },
      {
        name: "ProductName",
        selector: "ProductName", 
        searchable: true,
        width: "150px", 
      },
      {
        name: "OTP Verified",
        selector: "OTP_Verified_Flag",    
        type: "bool",
        cell: row => row.OTP_Verified_Flag ? "Yes" : "No"              
      },
      {
        name: "Visit Date Time",
        selector: "Visit_Date_Time",   
        cell: row => <div className="calldate">{row.Visit_Date_Time? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Visit_Date_Time}</Moment> : "N.A"}</div>,  
        type: "datetime",
        sortable: true,   
        width: "150px",
        format: 'YYYY-MM-DD HH:mm:ss',    
      },
      {
        name: "OTP Verified Distance",
        selector: "Distance", 
        cell: row => row.Distance ? row.Distance : "N.A" 
      },
      {
        name: "Today Call Flag",
        selector: "Call_Flag",
        type: "bool",
        cell: row => row.Call_Flag ? "Yes" : "No"          
      },
      {
        name: "Call Before Appointment",
        selector: "CALL_BEFORE_APPOINTMENT",  
        cell: row => row.CALL_BEFORE_APPOINTMENT ? "Yes" : "No" 
      },
      {
        name: "First Call Date",
        selector: "FIRST_CALL_DATE",  
        cell: row => <div className="calldate">{row.FIRST_CALL_DATE ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.FIRST_CALL_DATE}</Moment> : "N.A"}</div>,  
        type: "datetime",
        sortable: true,  
        width: "150px", 
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      {
        name: "First Call Talktime In Sec",
        selector: "FIRST_CALL_TALKTIME_IN_SEC",  
        cell: row => row.FIRST_CALL_TALKTIME_IN_SEC ? row.FIRST_CALL_TALKTIME_IN_SEC : row.FIRST_CALL_TALKTIME_IN_SEC == 0 ? 0: "N.A" ,
      },
     
      {
        name: "Appointment Generated On",
        selector: "AppointmentGenerated_Time", 
        cell: row => <div className="calldate">{row.AppointmentGenerated_Time ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.AppointmentGenerated_Time}</Moment> : "N.A"}</div>,  
        type: "datetime", 
        sortable: true,  
        width: "150px",  
        format: 'YYYY-MM-DD HH:mm:ss',    
      },
      {
        name: "IncomeDocs",
        selector: "IncomeDocs",     
        cell: row => row.IncomeDocs ? row.IncomeDocs : "N.A",
        
      },
      {
        name: "IncomeSlab",
        selector: "IncomeSlab",     
        cell: row => row.IncomeSlab ? row.IncomeSlab : "N.A",
        
      },
      {
        name: "Education",
        selector: "Education",     
        cell: row => row.Education ? row.Education : "N.A",
        
      },
      {
        name: "TL Last Call date(today)",
        selector: "TLLastCalldate",  
        cell: row => row.TLLastCalldate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.TLLastCalldate}</Moment>  : "-",
        type: "datetime",
        format: 'YYYY-MM-DD HH:mm:ss',  
      },
      {
        name: "TL Today Call flag",
        selector: "TLCallingFlag",  
        cell: row => row.TLCallingFlag==1 ? "Yes" : "No",
      },
      {
        name: "Appointment Active Flag",
        selector: "AppointmentActiveFlag",    
        type: "bool",
        cell: row => row.AppointmentActiveFlag ? "Yes" : "No"                  
      },
     
      {
        name: "Pincode",
        selector: "Pincode",  
        cell: row => row.Pincode ? row.Pincode : "N.A"
      },

      {
        name: "City",
        selector: "City",  
        cell: row => row.City ? row.City : "N.A"
      },
      {
        name: "Booking Flag",
        selector: "Bkng_Done",    
        type: "bool",
        cell: row => row.Bkng_Done ? "Yes" : "No"                  
      },
      // {
      //   name: "Current Appointment Status",
      //   selector: "Appointment_Last_Status",  
      //   cell: row => <Cell v={row.Appointment_Last_Status} />,    
      //   width: "150px"  
      // },
      {
        name: "Status Update Date Time",
        selector: "Appointment_Last_Status_Time", 
        cell: row => <div className="calldate">{row.Appointment_Last_Status_Time ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Appointment_Last_Status_Time}</Moment> : "N.A"}</div>,  
        type: "datetime", 
        sortable: true,   
        width: "150px", 
        format: 'YYYY-MM-DD HH:mm:ss',    
      },
      // {
      //   name: "Visit Agent ID",
      //   selector: "VisitAgentID", 
      //   cell: row => row.VisitAgentID ? row.VisitAgentID : "N.A" 
      // },
      // {
      //   name: "Visit Agent Name",
      //   selector: "VisitedAgentName", 
      //   cell: row => row.VisitedAgentName ? row.VisitedAgentName : "N.A" 
      // },
      {
        name: "Last Call Date",
        selector: "Last_Call_Time",   
        cell: row => <div className="calldate">{row.Last_Call_Time ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Last_Call_Time}</Moment> : "N.A"}</div>,  
        type: "datetime",
        sortable: true,  
        width: "150px", 
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      {
        name: "Last Call Talktime in Sec",
        selector: "Last_Call_Talktime_in_Sec", 
        cell: row => row.Last_Call_Talktime_in_Sec ? row.Last_Call_Talktime_in_Sec : "N.A" 
      },
      {
        name: "Total Attempts Till Date",
        selector: "Total_Attempts_Assigned",  
        cell: row => row.Total_Attempts_Assigned ? row.Total_Attempts_Assigned : "N.A"
      },
      {
        name: "Calls Picked",
        selector: "Calls_Picked", 
        cell: row => row.Calls_Picked ? row.Calls_Picked : "N.A" 
      },
      {
        name: "Total TalkTime Till Date",
        selector: "Total_Talktime",  
        cell: row => hhmmss(row.Total_Talktime),
      },
      {
        name: "Team Leader",
        selector: "ManagerName",  
        cell: row => row.ManagerName ? row.ManagerName : "N.A" 
      },
      {
        name: "Assistant Manager",
        selector: "AMName",  
        cell: row => row.AMName ? row.AMName : "N.A" 
      },
      {
        name: "Image Link",
        selector: row => row.SelfieUrl == '' ? 'No Image Found' : "",
        cell: row => (row.SelfieUrl) ?
          <div className="listenUserDetails">
            <Button onClick={(e) => this.getImageLink(e, row)} variant="primary">View Image</Button>
          </div> : 'No Image Found',
        editable: false,
        addable: false,
        hideonmodal: true,

      },
   
    ];
  }
  
  componentWillReceiveProps(nextProps) {

  }

  componentDidMount() {
    const user = getuser();
    this.setState({ SelectedSupervisors: [user.UserID] });

    //this.fetchFOSTLDashboardData();
    setTimeout(function () {
      this.fetchFOSTLDashboardData();
    }.bind(this), 1000)
    // setInterval(function () {
    //   //if (this.state.winactive == 1 || document.hasFocus()) {
    //     this.fetchFOSTLDashboardData();
    //   //}
    // }.bind(this), 300000)
    // 300000
    this.fetchSlotData();
    this.fetchAdvisorList();
  }

  fetchAdvisorList(){
    var SelectedSupervisors = this.state.SelectedSupervisors;
    const user = getuser();

    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetAgentListByManagerId",
      //params: [{ ManagerID: getuser().UserID }]//90022 }]
      params: [{ ManagerIds: SelectedSupervisors.join() }]//90022 }]
      
    }, function (result) {
      if(result.data && result.data.data[0].length > 0){debugger;
        let ExportData = result.data.data[0];
        let res = [];
        Object.keys(ExportData).forEach(function (key) {
          let AgentData = ExportData[key]
          var obj = {
            Id : AgentData.UserId,
            Display : AgentData.UserName + ' ('+ AgentData.EmployeeID + ')'
          }
          res.push(obj);
        })
      this.setState({ AgentList: res });
      }

    }.bind(this));
  }

  // openSalesView(e,Leadid) {
  //  e.preventDefault();

  //   SalesView(Leadid, function (results) {
  //     if (results.data.status == 200) {
  //         console.log(results.data.data[0])
  //         let val = results.data.data[0]
  //         // console.log(val[0].CustomerID)
  //         // console.log(val[0].ProductID)

  //         let url = OpenNewSalesView(val[0].CustomerID, Leadid, val[0].ProductID);
  //         window.open(url)
           
  //     } else {
  //         toast(results.data.message, { type: 'error' });
  //         return;
  //     }
  //   });

  // }

  fetchSlotData(){
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetAppointmentSlots",
    }, function (result) {
      if(result.data && result.data.data[0].length > 0){
        let ExportData = result.data.data[0];
        let res = [];
        Object.keys(ExportData).forEach(function (key) {
          let SlotData = ExportData[key]
          var obj = {
            Id : SlotData.SlotId,
            Display : SlotData.StartTime + ' to '+ SlotData.EndTime
          }
          res.push(obj);
        })
      this.setState({ AppointmentSlots: res });
      }

    }.bind(this));
  }

  fetchFOSTLDashboardData() {
    try{
      let that = this;
      var SelectedSupervisors = this.state.SelectedSupervisors;
      const user = getuser();
      this.setState({ Agentkey: 'ALL' });
          
      this.setState({ IsLoading : true})
      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        //root: "GetAppointmentsTracking",
        root: "GetFosTLDashboardData",
        params: [{ ManagerIds: SelectedSupervisors.join() }, { StartDate: moment().format("YYYY-MM-DD") },
        { EndDate: moment().add(1, 'days').format("YYYY-MM-DD") }]//90022 }]
      }, function (result) {
        // console.log("The result is ", result.data.data[0]);
        this.setState({category: 1});
        if(result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0){
          this.setState({ FOSTLDashboardData: result.data.data[0] , IsLoading : false});
          this.setState({ OriginalData: result.data.data[0] , IsLoading : false})
          this.setState({ FOSTLDashboardDataCount: result.data.data[1] , IsLoading : false});
          
        }else{
          this.setState({FOSTLDashboardData: [] , IsLoading : false });  
          this.setState({ OriginalData: [] , IsLoading : false})
          this.setState({ FOSTLDashboardDataCount: [] , IsLoading : false});  
        }
      }.bind(this));

      // console.log(result)
      // console.log(result.data)
      //   if(result && result.data && result.data.length > 0){
      //     this.setState({ FOSTLDashboardData: result.data[0] , IsLoading : false});
      //     this.setState({ OriginalData: result.data[0] , IsLoading : false})
      //     this.setState({ FOSTLDashboardDataCount: result.data[1] , IsLoading : false});
      //   }else{
      //     this.setState({FOSTLDashboardData: [] , IsLoading : false });  
      //     this.setState({ OriginalData: [] , IsLoading : false})
      //     this.setState({ FOSTLDashboardDataCount: [] , IsLoading : false});  
      //   }
    }
    catch(ex){
      this.setState({FOSTLDashboardData: [] , IsLoading : false });  
      this.setState({ OriginalData: [] , IsLoading : false})
      this.setState({ FOSTLDashboardDataCount: [] , IsLoading : false});  
      console.log(ex);
    }

  }

  getImageLink(e, row) {
    this.setState({ clickImage: true, clickModalUrl: row.SelfieUrl, appointmentId: row.AppointmentID, statusId: row.SelfieStatus, row: row });
  }

  handleOnCloseImage() {
    this.setState({ clickImage: false });
  }

  slotchange(e) {
    this.setState({ key: e.target.value });
  }

  agentchange(e){
    this.setState({ Agentkey: e.target.value });
  }

  handleShow(e) {
    let arr = [];
    for(let i = 0; i< e.nodesData.length ; i++){
      arr.push(e.nodesData[i].EmployeeId)
    }
    this.setState({ SelectedSupervisors: e.SelectedSupervisors, EmployeeIds:arr });
    setTimeout(function () {
      this.fetchFOSTLDashboardData();
      this.fetchAdvisorList();
    }.bind(this), 500);

  }

  handleCount(getSelection) {
    //if(getSelection==2){
      this.setState({category: getSelection})
      let AgentData = []
      if(this.state.OriginalData){
        if(getSelection==1){
          this.setState({ FOSTLDashboardData: this.state.OriginalData });
        }
        else{
          let alldata = this.state.OriginalData
          alldata.forEach(element => {
            if (getSelection == (element.Category)) {
              AgentData.push(element);
            }
          });
          this.setState({ FOSTLDashboardData: AgentData });
        }
      }
    //}
  }

  filterdata(e) {

    // let alldata = this.state.FOSTLDashboardData
    let that = this;
    if (this.state.key === "ALL" && this.state.Agentkey === "ALL") {
      //return alldata;
      return this.state.FOSTLDashboardData
    }

    let AgentData = [];
    if (this.state.key != "ALL" && this.state.Agentkey === "ALL") {
      let alldata = this.state.FOSTLDashboardData
      alldata.forEach(element => {
        if (this.state.key == (element.SlotId)) {
          AgentData.push(element);
        }
      });
      return AgentData;
    }

    if (this.state.key === "ALL" && this.state.Agentkey != "ALL") {
      let alldata = this.state.FOSTLDashboardData
      alldata.forEach(element => {
        if (this.state.Agentkey == (element.UserID)) {
          AgentData.push(element);
        }
      });
      return AgentData;
    }

    if (this.state.key != "ALL" && this.state.Agentkey != "ALL") {
      let alldata = this.state.FOSTLDashboardData
      alldata.forEach(element => {
        if (this.state.Agentkey == (element.UserID) && this.state.key == (element.SlotId)) {
          AgentData.push(element);
        }
      });
      return AgentData;
    }

  }

  handleModal() {
    this.setState({ modalOpen: true })
  }

  render() {
    const columns = this.columnlist;
    const data = this.filterdata();
    // console.log("dddddd", data);
    const { PageTitle, showAlert, AlertMsg, AlertVarient, IsLoading } = this.state;
    return (
      <>
        <div className="content">
          <Row>
            {this.state.modalOpen && <FosTLDashboardModal columns={columns} SelectedSupervisors={this.state.SelectedSupervisors}
              handleClose={() => {
                this.setState({ modalOpen: false });
              }}
            ></FosTLDashboardModal>}
          </Row>
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                <Row>
                    <Col md={3}> <CardTitle tag="h4">{PageTitle}</CardTitle></Col>
                    
                    </Row>
                    <Row>                   
                    
                    <Col md={12}>
                      <FOSTLDashboardBoxes Count= {this.state.FOSTLDashboardDataCount} handleCount = {this.handleCount.bind(this)} Category={this.state.category}></FOSTLDashboardBoxes>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                      <Form.Label>{"Appointment Slots"}</Form.Label>
                        <DropDown firstoption="ALL" firstoptionValue="ALL" items={this.state.AppointmentSlots} onChange={this.slotchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                      <Form.Label>{"Agents"}</Form.Label>
                        <DropDown firstoption="ALL" firstoptionValue="ALL" items={this.state.AgentList} onChange={this.agentchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    <Col md={2}>                     
                      <button className="btn btn-primary mt-4" onClick={()=>this.fetchFOSTLDashboardData()}>
                                            Refresh {IsLoading && <i className="nc-icon nc-refresh-69" />}                                                   
                     </button>
                    </Col>
                    <Col md={2}>
                      <button style={{ backgroundColor: '#87b3f4', border: '#327be7' }} className="btn btn-primary mt-4" onClick={this.handleModal}>Historical Data</button>
                    </Col>
                    <Col md={12}>
                          {/* <small><strong>Please Note</strong>: Data on this dashboard is refreshed after every 5 minutes, in case you want see the latest data click on refresh.</small> */}
                          {/* <Form.Label>{"Please Note: Data on this dashboard is refreshed after every 5 minutes, in case you want see the latest data click on refresh."}</Form.Label> */}
                    </Col>
                    {/* <Col md={8}>
                        <CardTitle tag="h6">{this.state.EmployeeIds && Array.isArray(this.state.EmployeeIds) && this.state.EmployeeIds.join()}</CardTitle>
                      </Col> */}
                  </Row>
                    
                  <ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>

                      

                </CardHeader>
                <CardBody className="fosTLDashboardDataTble">
                  <DataTable
                    columns={columns}
                    data={(data && data.length > 0) ? data : []}
                    defaultSortAsc={false}
                    export={false}                  
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
          <Modal show={this.state.clickImage} onHide={() => this.setState({ clickImage: false })} dialogClassName="modal-98w fosSelfiePopop">
          <img src="/close.svg" className="closeBtn" onClick={this.handleOnCloseImage.bind(this)} alt=""/>
            <Modal.Body className="viewImage">
              <FOSSelfieDetails URL={this.state.clickModalUrl} row={this.state.row} EmployeeId={this.state.row.VisitAgentID} 
              UserName={this.state.row.VisitedAgentName} LeadID={this.state.row.Leadid}
              statusId={this.state.statusId} appointmentId={this.state.appointmentId}
               view={1} onCloseImage={this.handleOnCloseImage.bind(this)} source='fostldash' />
            </Modal.Body>

          </Modal>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(FOSTLDashboard);