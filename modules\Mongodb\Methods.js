
async function bulkInsertBooking(recordArray){
  if(!recordArray){
    return {
      status: 200,
      message: "Array is Null"
    };
  }else if(recordArray.length === 0){
    return {
      status: 200,
      message: "Array Length is zero"
    };
  }else{
    try{
      var bulk = matrixdb.collection('BookingDetailsDump').initializeOrderedBulkOp();
      recordArray.forEach((record)=>{
        record.isRead = false;
        bulk.insert(record);
      });
      const result = await bulk.execute();
      return {
        status: 200,
        message: "Insertion Success"
      };
    }catch(err){
      console.log("Error in bulkInsertBooking: ",err);
      return {
        status : 400,
        message : err
      };
    }
  }
  
}
async function bulkInsertIncentiveData(recordArray, collection, connection){
  if(!recordArray){
    return {
      status: 200,
      message: "Array is Null"
    };
  }else if(recordArray.length === 0){
    return {
      status: 200,
      message: "Array Length is zero"
    };
  }else{
    try{
      var bulk
      if(connection == 'MD'){
        matrixdashboarddb.collection(collection).deleteMany({
          productID: recordArray[0].productID,
          IncentiveMonth: recordArray[0].IncentiveMonth
        })

        bulk = matrixdashboarddb.collection(collection).initializeOrderedBulkOp();
      }
      else{
        bulk = matrixdb.collection(collection).initializeOrderedBulkOp();
      }
      


      recordArray.forEach((record)=>{
        bulk.insert(record);
      });
      const result = await bulk.execute();
      return {
        status: 200,
        message: "Insertion Success"
      };
    }catch(err){
      console.log("Error in bulkInsertIncentiveData: ",err);
      return {
        status : 400,
        message : err
      };
    }
  }
  
}
async function InsertFileLog(recordArray){
  if(!recordArray){
    return {
      status: 200,
      message: "Array is Null"
    };
  }else if(recordArray.length === 0){
    return {
      status: 200,
      message: "Array Length is zero"
    };
  }else{
    try{
      var bulk = await loggerdb.collection("MonthlyIncentiveFileLog").insertOne(recordArray);
      return {
        status: 200,
        message: "Insertion Success"
      };
    }catch(err){
      console.log("Error in InsertFileLog: ",err);
      return {
        status : 400,
        message : err
      };
    }
  }
  
}

async function InsertFileUploadLog(recordArray){
  if(!recordArray){
    return {
      status: 200,
      message: "Array is Null"
    };
  }else if(recordArray.length === 0){
    return {
      status: 200,
      message: "Array Length is zero"
    };
  }else{
    try{
      var bulk = await matrixdashboarddb.collection("FileUpload").insertOne(recordArray);
      return {
        status: 200,
        message: "Insertion Success"
      };
    }catch(err){
      console.log("Error in InsertFileUploadLog: ",err);
      return {
        status : 400,
        message : err
      };
    }
  }
  
}
module.exports = {
  bulkInsertBooking,
  bulkInsertIncentiveData,
  InsertFileLog,
  InsertFileUploadLog
}