const {
  GET_BMS_URL,
  AUTO_PAY_STATUS,
  BMS_VERIFICATION_STATUS,
  ADD_CALL_TO_QUEUE,
  DOC_POINT_BMS_URL,
  MY_BOOKINGS_BMS_API,
  PENDING_PF_LEAD,
  PENDING_DOC_LEAD,
  PENDING_DOCUMENT_LEADLIST,
  GET_CJ_URL,
  GET_VERIFICATION_CAMPAIGN,
  GET_CONTINUE_JOURNEY_URL,
  ADD_TO_VERIFICATION_QUEUE,
  OPT_FOR_WA,
  MY_BOOKINGS_BMS_API2,
  AUTODEBIT_PROMPT
} = require("./ActionTypes")
const config = require('../../env_config');
const { AesEncryption, encrypt } = require('./CommonMethods');

const GetHeaders = ({ User, Type, Data }) => {

  const { userId, token } = User || {};
  const { UserId } = Data || {};

  try {
    switch (Type) {
      case GET_BMS_URL:
        return {
          "AgentId": userId,
          "Source": "Matrix",
          "Content-Type": "application/json",
          "token": token
        }
        case GET_VERIFICATION_CAMPAIGN :
          return{
            "AgentId": userId,
            "Source": "Matrix",
            "Content-Type": "application/json",
            "token": token
          }
      case AUTO_PAY_STATUS:
        return {
          "auth": config.AUTO_PAY_AUTH,
          "mid": config.AUTO_PAY_MID
        }
      case BMS_VERIFICATION_STATUS:
        return {
          "AppId": config.BMS_APP_SERVICE_V2_ID,
          "AppKey": config.BMS_APP_SERVICE_V2_KEY,
          "Content-Type": 'application/json-patch+json',
        }
      case DOC_POINT_BMS_URL:
        return {
          "Authorization": config.BMS_DOC_POINT_AUTH_HEADER,
          "Content-Type": 'application/json-patch+json',
        }
      case MY_BOOKINGS_BMS_API:
        return {
          "AppId": config.BMS_APP_SERVICE_V2_ID,
          "AppKey": config.BMS_APP_SERVICE_V2_KEY,
          "Content-Type": 'application/json',
        }

      case  MY_BOOKINGS_BMS_API2:
        return {
          "source":"matrix",
          "clientKey": config.BMSCROMA_CLIENTKEY,
          "authKey": config.BMSCROMA_AUTHKEY
        }
      
      case PENDING_PF_LEAD:
        return {
          "AppId": config.BMS_APP_SERVICE_V2_ID,
          "AppKey": config.BMS_APP_SERVICE_V2_KEY,
          "Content-Type": 'application/json-patch+json',
        }
      case PENDING_DOC_LEAD:
        return {
          "AppId": config.BMS_APP_SERVICE_V2_ID,
          "AppKey": config.BMS_APP_SERVICE_V2_KEY,
          "Content-Type": 'application/json-patch+json',
        }
      case PENDING_DOCUMENT_LEADLIST:
      return {
        "clientKey": config.BMSCROMA_CLIENTKEY,
        "Source": "matrix",
        "Content-Type": "application/json",
        "authKey": config.BMSCROMA_AUTHKEY
      }
      case GET_CJ_URL:
        return {
          "clientKey": "L6YWNav",
          "Source": "matrix",
          "Content-Type": "application/json",
          "authKey": "LGsaWLYmF6YWNav"
        }

      case GET_CONTINUE_JOURNEY_URL:
        return {
          "source": "matrix",
          "clientKey": config.INTERNAL_MATRIX_API_CLIENTKEY,
          "authKey":  config.INTERNAL_MATRIX_API_AUTHKEY
        }
      case ADD_TO_VERIFICATION_QUEUE:
        return {
          "AppId": config.BMS_APP_SERVICE_V2_ID,
          "AppKey": config.BMS_APP_SERVICE_V2_KEY,
          "Content-Type": 'application/json',
        }
      
      case OPT_FOR_WA:
        return{
          "clientKey": config.CORESERVICES_CLIENTKEY,
          "source": "MatrixDashboard",
          "authKey": config.CORESERVICES_AUTHKEY,
          "Content-Type": 'application/json',
        }
      
      case AUTODEBIT_PROMPT:
        return{
          "clientKey": config.FOSCLIENTKEY,
          "source": "Dashboard",
          "authKey": config.FOSAUTHKEY,
          "Content-Type": 'application/json',
        }

      default:
        return {
          "Content-Type": "application/json",
        }
    }

  } catch (err) {
    console.log('inside GetHeaders', err);
    return null
  }

}

const GetUrl = ({ User, Type, Data }) => {

  try {
    const { userId } = User || {};

    switch (Type) {
      case GET_BMS_URL:
        const BookingId = Data?.BookingID
        return `${config.MATRIX_CORE_URL}/api/LeadDetails/GetbmsLink/${BookingId}/${userId}/SalesView`;

      case AUTO_PAY_STATUS:
        return config.AUTO_PAY_API;

      case BMS_VERIFICATION_STATUS:
        return `${config.BMS_SERVICE_URL}api/Partner/InsertUpdateVerificationStatus`;

      case ADD_CALL_TO_QUEUE:
        return config.ADD_LEAD_TO_PRIORITYQUEUE;

      case DOC_POINT_BMS_URL:
        return config.BMS_DOC_POINT_API;
      
      case GET_VERIFICATION_CAMPAIGN:
        const LeadID = Data?.BookingID;
        // return config.GET_VERIFICATION_CAMPAIGN + '?LeadID=' + LeadID+ '&TransferType=SALES_TO_VERIFICATION';
        return `${config.GET_VERIFICATION_CAMPAIGN}?LeadID=${LeadID}&TransferType=SALES_TO_VERIFICATION`;
      
      case MY_BOOKINGS_BMS_API:
        return `${config.BMS_SERVICE_URL}api/booking/GetBookingDetailsPrimary`
      
      case MY_BOOKINGS_BMS_API2:
        return `${config.BMS_CROMA_URL}api/Matrix/GetMyBookingDetails`;

      case PENDING_PF_LEAD:
        return config.BMS_SERVICE_URL + "api/booking/GetProposalFormLeadList"

      case PENDING_DOC_LEAD:
        return config.BMS_SERVICE_URL + "api/booking/GetPendingDocumentLeadList"

      case PENDING_DOCUMENT_LEADLIST:
        return config.BMS_CROMA_URL + "api/Matrix/GetPendingDocumentLeadList"

      case GET_CJ_URL:
        const LeadId = Data?.LeadId;
        const ProductId = Data?.ProductID;
        const SupplierId = Data?.SupplierId;
        return `${config.MATRIXCOREAPI}/api/LeadDetails/GetCJUrl?LeadID=${LeadId}&ProductId=${ProductId}&SupplierId=${SupplierId}&Process='booking'`;

      case GET_CONTINUE_JOURNEY_URL:
        return `${config.MATRIXCOREAPI}/api/LeadDetails/GetJourneyLink?LeadID=${Data?.BookingID}`

      case ADD_TO_VERIFICATION_QUEUE:
        return `${config.BMS_SERVICE_URL}api/booking/RequestVerificationCallback`

      case OPT_FOR_WA:
        let key= config.CORESERVICES_ENCRYPTIONKEY;
        let iv= config.CORESERVICES_ENCRYPTIONSALT;
        let CustomerId= Data;
        let obj={
          message: CustomerId,
          key: key,
          iv: iv
        }
        let EncryptedCustomerId= encrypt(obj);
        let EncodedCustID= encodeURIComponent(EncryptedCustomerId);
        // console.log(`${config.CORE_OPTFORWA_URL}customerId=${EncodedCustID}&commTypeID=5`);
        return `${config.CORESERVICES_OPTFORWA_URL}customerId=${EncodedCustID}&commTypeID=5`
      
      case AUTODEBIT_PROMPT:
        return `${config.MATRIXCOREAPI}/api/SalesView/SendEmandateEnableCommunicationTermInv`

      default:
        return null
    }

  } catch (err) {
    console.log('inside GetUrl', err);
    return null;
  }

}

const GetBody = ({ User, Type, Data }) => {

  try {
    const { IsInternalDomain, userId } = User || {};
    let IsInternal = false;
    if (IsInternalDomain) {
      IsInternal = true;
    }
    let body = {};
    switch (Type) {

      case AUTO_PAY_STATUS:

        let Key = config.AUTO_PAY_SECRET_KEY;
        let Iv = config.AUTO_PAY_SECRET_IV;
        let encrypted = AesEncryption({ data: Data?.BookingID, key: Key, iv: Iv });
        body = { leadId: encrypted };
        return body;

      case BMS_VERIFICATION_STATUS:
        
        body = {
          BookingId: Data?.BookingID,
          VerificationStatus: Data?.VerificationStatus,
          VerificationSubStatus: Data?.VerificationSubStatus,
          Remarks: Data?.Remarks,
          UserId: Data?.UserID
        }
        return body;

      case ADD_CALL_TO_QUEUE:
        let reqData = Data;
        reqData = { ...reqData, UserID: userId }
        body = {
          "UserId": userId,
          "Leads": [reqData]
        };
        return body;

      case DOC_POINT_BMS_URL:
        // console.log('DOC_POINT_BMS IsInternalNetwork', IsInternal);
        return {
          "BookingId": Data?.BookingID,
          "UserId": Data?.UserID,
          "ServiceRoleType": 5,
          "EmployeeId": Data?.EmployeeId,
          "EmployeeName": Data?.UserName,
          "IsInternalNetwork": IsInternal
        }
        case MY_BOOKINGS_BMS_API:
          return {
            "AgentID": userId,
            "BookingType": Data?.BookingType,
            "BookingMonth": Data?.BookingMonth,
            "ManagerIds": Data?.IsSupervisor ? userId.toString() : Data?.ManagerIds,
            "ProductIds": Data?.ProductIds,
            "FilterProduct": Data?.FilterProduct,
            "ProductPresent": Data?.ProductPresent,
            "LeadIDs": Data?.LeadIDs || []
          }
      
      case MY_BOOKINGS_BMS_API2:

        return {
          "AgentIDs": Data?.AgentIds || [],
          "BookingType": Data?.BookingType,
          "FromDate":Data?.FromDate,
          "ToDate": Data?.ToDate,
          "ProductIds": Data?.ProductIds,
          "ProductPresent": Data?.ProductPresent,
          "LeadIDs": Data?.LeadIDs || [],
          "Isadvisor": Data?.Isadvisor

        }

      case PENDING_PF_LEAD:

        return {
          "SupplierId": Data?.SupplierId,
          "ProductID": Data?.ProductID,
          "FromDate": Data?.FromDate,
          "ToDate": Data?.ToDate,
          "GroupId": Data?.GroupId,
          "SupervisorId": Data?.SupervisorId,
          "Age": Data?.Age,
          "InsurerIds": Data?.InsurerIds,
          "MedicalType": Data?.MedicalType
        }

      case PENDING_DOC_LEAD:
        return {
          "SupplierId": Data?.SupplierId,
          "ProductID": Data?.ProductID,
          "FromDate": Data?.FromDate,
          "ToDate": Data?.ToDate,
          "GroupId": Data?.GroupId,
          "SupervisorId": Data?.SupervisorId,
          "Age": Data?.Age,
          "InsurerIds": Data?.InsurerIds,
          "MedicalType": Data?.MedicalType
        }

      case PENDING_DOCUMENT_LEADLIST:
        return {
          "SupplierId": Data?.SupplierId,
          "ProductID": Data?.ProductID,
          "FromDate": Data?.FromDate,
          "ToDate": Data?.ToDate,
          "GroupId": Data?.GroupId,
          "SupervisorId": Data?.SupervisorId,
          //"Age": Data?.Age,
          "InsurerIds": Data?.InsurerIds,
          "MedicalType": Data?.MedicalType,
          "UserList":Data?.UserList,
          "MinAgeing": Data?.MinAgeing,
          "MaxAgeing": Data?.MaxAgeing,
          "RequestType": Data?.RequestType
        }

      case ADD_TO_VERIFICATION_QUEUE:
        return  {
          "CallbackType": Data?.CallbackType,
          "LeadId": Data?.LeadId,
          "ProductID": Data?.ProductId
        }
      
      case AUTODEBIT_PROMPT:
        return{
          "LeadId":Data?.LeadId,
          "CustomerName":Data?.CustomerName,
          "ProductID":Data?.ProductId,
          "ProductName": Data?.ProductName,
          "InsurerName":Data?.InsurerName,
          "PolicyNo":Data?.PolicyNo || null,
          "ApplicationNo": Data?.ApplicationNo || null
        }

      default:
        return null
    }

  } catch (err) {
    console.log('inside GetUrl', err);
    return null;
  }

}

module.exports = {
  GetHeaders: GetHeaders,
  GetUrl: GetUrl,
  GetBody: GetBody
}