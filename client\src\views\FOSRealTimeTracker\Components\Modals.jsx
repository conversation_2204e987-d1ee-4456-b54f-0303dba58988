import React, {useState} from 'react';
import Typography from '@mui/material/Typography';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import TextField from '@mui/material/TextField';
import { SearchOutlined } from '@mui/icons-material';
import '../../../assets/scss/_modals.scss';

const Modals = (props) => {

  const [show, setShow] = useState(true);
  
  const closeModal = () => {
    setShow(false);
    props.closeModal();
  }
  
  return (  

  <div className='modals'>

    {/* City Modal */}
    {props.modalNum === 1 &&
      <Dialog className='cityModal'
        open={show}
        onClose={closeModal}
      >
        <DialogTitle>
          <span>Select city </span>
          <IconButton onClick={closeModal}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Typography className='search'>
            <button type="submit" class="searchButton">
              <SearchOutlined/>
            </button>
            <TextField
             autoFocus
              required
              id="name"
              type="text"
              fullWidth
              variant="standard"
              class="searchTerm"
              placeholder='Search city'
          />
            {/* <input type="text" class="searchTerm" id="input_text" placeholder='Search city' /> */}
          </Typography>
          <ul>
                <li>Bhiwani</li>
                <li>Ghaziabad</li>
                <li>Ambattur</li>
                <li>Jalgaon</li>
                <li className='active'>Gurugram</li>
                <li>Delhi</li>
                <li>Mumbai</li>
              <li>Chennai</li>
            </ul>
        </DialogContent>
      </Dialog>
    }

    {/* Appointment Modal */}
    {props.modalNum === 2 &&
      <Dialog className='appointmentModal'
        open={show}
        onClose={closeModal}
      >
        <DialogTitle>
          <span>Select appointment</span>
          <IconButton onClick={closeModal}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <ul>
            <li>All day</li>
            <li>08:00AM - 10:00AM</li>
            <li className='active'>10:00AM - 12:00PM</li>
            <li>12:00PM - 02:00PM</li>
            <li>02:00PM - 04:00PM</li>
            <li>04:00PM - 06:00PM</li>
            <li>06:00PM - 08:00PM</li>
            <li>08:00PM - 10:00PM</li>
          </ul>
        </DialogContent>
      </Dialog>
    }

  </div>

  
  );
}

export default Modals;