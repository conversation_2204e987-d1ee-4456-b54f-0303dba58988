const tblList = require("../constants");
const methods = require("./ApiCommMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../Libs/sqlHelper");



async function AddLeadValidation(req, res) {
    try {
        methods.AddLeadValidation(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}
async function ValidateAddLeadToPriorityQueue(req, res) {
    try {
        methods.ValidateAddLeadToPriorityQueue(req.body, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function GetInvalidLeads(request, result){
    try{
        methods.GetInvalidLeads(request, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: err
        });
    }
}

async function RemoveInvalidLeads(request, result){
    try{
        methods.RemoveInvalidLeads(request, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: err
        });
    }
}


module.exports = {
    AddLeadValidation : AddLeadValidation,
    ValidateAddLeadToPriorityQueue : ValidateAddLeadToPriorityQueue,
    GetInvalidLeads : GetInvalidLeads,
    RemoveInvalidLeads : RemoveInvalidLeads
};