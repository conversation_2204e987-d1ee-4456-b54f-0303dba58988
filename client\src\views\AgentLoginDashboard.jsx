
import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import DataTable from './Common/DataTableWithFilter';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import { getuser } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  
  Row,
  Col
} from "reactstrap";

class AgentLoginDashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Agent Login Dashboard",
      AgentLoginTracker: []
    };
    this.handleShow = this.handleShow.bind(this);
    this.columnlist = [
      {
        name: "EMPLOYEE ID",
        selector: "EMPLOYEEID",
        sortable: true,
      },
      {
        name: "USER NAME",
        selector: "USERNAME",
        sortable: true,
      },
      {
        name: "FIRST LOGIN",
        selector: "FIRSTLOGIN",
        sortable: true,
        cell: row => <div className={row.AgentCode}>{row.FIRSTLOGIN ? <Moment utc={true} format="HH:mm:ss A">{row.FIRSTLOGIN}</Moment> : "N.A"}</div>

      },
      {
        name: "SYSTEM LOGIN",
        selector: "SYSTEMLOGIN",
        sortable: true,
      },          
      {
        name: "REMAINING LOGIN TIME",
        cell: row => this.calculateremainingtime(row)
      },
      {
        name: "IDLE TIME MINS",
        selector: "IDLETIME",
        sortable: true
      },
      {
        name: "BREAK COUNT",
        selector: "BREAKCOUNT",
        sortable: true        
      },
      {
        name: "LUNCH MINS",
        selector: "LUNCHMINS",
        sortable: true,
      },
      {
        name: "MEETING MINS",
        selector: "MEETINGMINS",
        sortable: true,
      },
      {
        name: "TEA MINS",
        selector: "TEAMINS",
        sortable: true,
      },
      {
        name: "TRAINING MINS",
        selector: "TRAININGMINS",
        sortable: true,
      },
      {
        name: "DAYEND MINS",
        selector: "DAYENDMINS",
        sortable: true,
      },
      {
        name: "BIO BREAK MINS",
        selector: "BIOBREAKMINS",
        sortable: true,
      },
      {
        name: "TOTAL BREAK MINS",
        selector: "SUM_DISPOSITIONMINS",
        sortable: true,
      },
    
    ];
  }

  calculateremainingtime(row) {
    var syslogin = 8-row.SYSTEMLOGIN;
    var remainingtime = parseFloat(syslogin.toFixed(2));
    remainingtime = remainingtime < 0 ? 0 : remainingtime;
    return remainingtime;
  }

  componentDidMount() {
    
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "AgentLoginTracker",
      params: [{ ManagerIds: getuser().UserID }]
    });
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
      this.setState({ AgentLoginTracker: nextProps.CommonData["AgentLoginTracker"] });
    }
  }
  handleShow(e) {
    var SelectedSupervisors = e.SelectedSupervisors;
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "AgentLoginTracker",
      params: [{ ManagerIds: SelectedSupervisors.join() }]
    });
  }

  render() {
    const columns = this.columnlist;
    const { PageTitle, AgentLoginTracker } = this.state;
    console.log("AgentLoginTracker", AgentLoginTracker);
    return (
      <>
        <div className="content">
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={8}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      {/* <ProductSupervisorFilter handleShow={this.handleShow} showModal={true} /> */}
                      <ManagerHierarchy handleShow={this.handleShow} value={/UserID/g}></ManagerHierarchy>
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={AgentLoginTracker[0]}
                    defaultSortField="USERNAME"
                    defaultSortAsc={true}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(AgentLoginDashboard);