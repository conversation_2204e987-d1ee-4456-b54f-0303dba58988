
import React from "react";
import { <PERSON><PERSON>, Form } from 'react-bootstrap';
import moment from 'moment';
// import {
//   GetCommonData, addRecord, UpdateData, DeleteData
// } from "../store/actions/CommonMongoAction";

import { GetCommonData, InsertData, UpdateData, GetCommonspData } from "../store/actions/CommonAction";

//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { GetJsonToArray, getUrlParameter } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

const Question = (props) => {
  // console.log('--------',QuestionText, FieldOptions, FieldType) ; 
  let asterix = <span>*</span>;
  let FieldOptions = props.FieldOptions.split(',');
  // console.log('--------',QuestionText, FieldOptions, FieldType, IsRequired) ;

  // let NestedQuestion ;
  // for(let i=0 ; i<props.items.length ; i++){
  //   if(props.items[i]['QuestionID'] == props.NestedQuestionId)
  //   {
  //     NestedQuestion = props.items[i] ; break;
  //   }
  // }
  let NestedQuestions = props.items.filter(item => {
    return item.QuestionID === props.NestedQuestionId
  });

  let formEntry;
  if (props.display) {
    if (props.FieldType.toLowerCase() == 'radio') {
      formEntry = (
        <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
          {<div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>}
          <ul className='survey-radio'>
            {FieldOptions.map((option, index) => {
              return (
                <li
                  onClick={props.handleChange}
                  value={option}
                  name={props.QuestionID}
                  type="radio"
                  className={props.formvalue[props.QuestionID] === option ? 'active-rating' : ""}>
                  {option}
                </li>

              )
            })}
          </ul>
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'checkbox') {
      formEntry = (
        <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
          {<div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>}
          {FieldOptions.map((option, index) => {
            return (
              <Form.Check
                // required={props.IsRequired&&index==0}
                onChange={props.handleChange}
                custom
                className={(props.formvalue[props.QuestionID] && props.formvalue[props.QuestionID].indexOf(option)>-1)  ? 'survey-checkbox-radio active-rating' : "survey-checkbox-radio"}
                //className="survey-checkbox-radio"
                // inline
                label={option}
                value={option}
                type={props.FieldType.toLowerCase()}
                name={props.QuestionID}
                id={props.QuestionID + '_' + option + '_' + props.FieldType.toLowerCase()}
              />
            )
          })}
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'textarea') {
      formEntry = (
        <Form.Group as={Col} md={12} controlId={props.QuestionID} key={props.QuestionID}>
          <div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>
          <Form.Control required={props.IsRequired}
            type="text" onChange={props.handleChange} as="textarea" placeholder="Enter" />
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'email') {
      formEntry = (
        <Form.Group as={Col} md={12} controlId={props.QuestionID} key={props.QuestionID}>
          <div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>
          <Form.Control required={props.IsRequired}
            type="email" onChange={props.handleChange} placeholder="Enter email" />
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'number') {
      formEntry = (
        <Form.Group as={Col} md={12} controlId={props.QuestionID} key={props.QuestionID}>
          <div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>
          <Form.Control required={props.IsRequired}
            type="number" onChange={props.handleChange} placeholder="Enter" />
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'dropdown') {
      formEntry = (
        <Form.Group as={Col} md={12} controlId={props.QuestionID} key={props.QuestionID}>
          <div><Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label></div>
          <Form.Control as="select" onChange={props.handleChange}
            placeholder="Select" required={props.IsRequired}>
            <option key='0_dropdown' value="" disabled selected>Select your option</option>
            {FieldOptions.map((option, index) => {
              return (
                <option key={index + 1 + '_dropdown'} value={option}>{option}</option>
              )
            })}
          </Form.Control>
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'string') {
      formEntry = (
        <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
          <Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label>
          <Form.Control
            required={props.IsRequired}
            type="text" onChange={props.handleChange} placeholder={"Enter"} />
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'empty') {
      formEntry = (
        <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
          <Form.Label>{props.QuestionText}{props.IsRequired && asterix}</Form.Label>
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'link') {
      formEntry = (
        <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
          <a target="_blank" href={props.QuestionText}>{props.QuestionText}</a>
        </Form.Group>
      )
    }
    if (props.FieldType.toLowerCase() == 'embedded') {
      formEntry = (
        // <Form.Group as={Col} md={12} key={props.QuestionID} controlId={props.QuestionID}>
        <iframe src={props && props.QuestionText} allow='geolocation; microphone; camera' style={
          {
            width: "100%",
            border: 0,
            height: '250px',
            border: 'none'
          }
        } ></iframe>
        //</Form.Group>          
      )
    }
  }

  return (<div>
    {formEntry}
    {NestedQuestions.map((NestedQuestion, index) => {
      return (
        <Question
          QuestionID={NestedQuestion.QuestionID}
          QuestionText={NestedQuestion.QuestionText}
          FieldOptions={NestedQuestion.FieldOptions}
          FieldType={NestedQuestion.FieldType}
          IsRequired={NestedQuestion.IsRequired}
          SelectedOption={NestedQuestion.SelectedOption}
          NestedQuestionId={NestedQuestion.NestedQuestionId}
          items={props.items}
          display={props.formvalue[props.QuestionID] == props.SelectedOption && index == 0}
          formvalue={props.formvalue}
          handleChange={props.handleChange} />
      )
    })}
  </div>);

}

class SurveyForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "SurveyQuestionMasterJoin",
      departmentId: "0",
      stepOnePart: 1,
      saved: false,

      PageTitle: "",
      PageDescription: "",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {}
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleFormvalue = this.handleFormvalue.bind(this);
    this.selectedrow = {
      "QuestionText": "", "FieldOptions": "", "SurveyID": getUrlParameter("sid")
    }
    this.SurveyMasterList = [
      {
        name: "SurveyId",
        label: "SurveyId",
        type: "number"
      },
      {
        name: "SurveyName",
        label: "SurveyName",
        type: "string"
      },
      {
        name: "Description",
        label: "Description",
        type: "string"
      }
    ];
    this.columnlist = [
      {
        name: "QuestionID",
        label: "QuestionID",
        type: "number",
        searchable: true,
        editable: false
      },
      {
        name: "QuestionText",
        label: "QuestionText",
        type: "string",
        searchable: true,
        required: true
        // editable: false
      },
      {
        name: "SurveyID",
        label: "SurveyID",
        type: "number",
        // searchable: true,
        editable: false
      },
      {
        name: "FieldType",
        label: "FieldType",
        type: "string",
        searchable: true,
        editable: false
      },
      {
        name: "FieldOptions",
        label: "FieldOptions",
        type: "string",
        searchable: true,
        required: true
      },
      {
        name: "IsRequired",
        label: "IsRequired",
        type: "bool",
        searchable: true,
        required: true
      },
      {
        name: "SelectedOption",
        label: "SelectedOption",
        type: "string",
        searchable: true,
        required: true
      },
      {
        name: "NestedQuestionId",
        label: "NestedQuestionId",
        type: "number",
        searchable: true,
        required: true
      }
    ];
  }



  componentDidMount() {
    let sid = getUrlParameter("sid");
    let uid = getUrlParameter("u");
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      //cols: GetJsonToArray(this.columnlist, "name"),
      "SurveyID": sid,
      //order : 1,
      //direction: 'ASC',
      c: "L",
    }, (result) => {
    });

    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: "SurveyMaster",
      cols: GetJsonToArray(this.SurveyMasterList, "name"),
      con: [{ "SurveyId": sid }],
      c: "R",
    });

    if (uid) {
      this.props.GetCommonspData({
        root: 'CheckSurveyAgent',
        c: "L",
        params: [{ "UserId": uid, "SurveyId": sid }],
      }, function (result) {
        if (result.data && result.data.data[0].length > 0) {
          console.log(result.data.data[0][0].Eligible, result.data.data[0][0].IsComplete)
          if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == false) {
            this.setState({ stepOnePart: 4 });
          } else if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == true) {
            this.setState({ stepOnePart: 3 });
          } else {
            this.setState({ stepOnePart: 2 });
          }
        }
      }.bind(this));
    } else {
      this.setState({ stepOnePart: 2 });
    }

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);
  }


  componentWillReceiveProps(nextProps) {
    debugger;
    if (!nextProps.CommonData.isError) {

      if (nextProps.CommonData['SurveyMaster'] && nextProps.CommonData['SurveyMaster'][0]) {
        this.setState(() => ({
          PageTitle: nextProps.CommonData['SurveyMaster'][0].SurveyName,
          PageDescription: nextProps.CommonData['SurveyMaster'][0].Description,
        }))
      }

      this.setState(() => ({
        items: nextProps.CommonData[this.state.root]
      }));

      this.setState({ store: nextProps.CommonData });

    }

  }


  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Survey" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: { ...this.selectedrow }, event: "Add", showModal: true, FormTitle: "Add New Survey" });
  }

  handleSave(e) {
    e.preventDefault();
    let uid = getUrlParameter("u");
    let sid = getUrlParameter("sid");
    if (document.getElementsByName("frmChatAgentConfigure").length > 0 &&
      document.getElementsByName("frmChatAgentConfigure")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      console.log("formvalue", formvalue);
      let items = this.state.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].IsRequired && (items[i].FieldType == 'radio' || items[i].FieldType == 'checkbox')) {
          if (!formvalue[items[i].QuestionID] &&
            document.getElementsByName(items[i].QuestionID).length > 0) {
            toast.error("Please fill all the required fields!")
            return;
          }
        }
      }
      // this.fnCleanData(formvalue);
      debugger;
      if (this.state.event == "Edit") {
      } else {
        // formvalue.SurveyId = this.state.items[this.state.items.length-1].SurveyId + 1; 
        // formvalue.CreatedOn = moment().format("YYYY-MM-DD HH:mm:ss") ;
        let flag = true;
        for (let key in formvalue) {
          let body = {
            SurveyId: sid, UserId: uid,
            CreatedOn: moment().format("YYYY-MM-DD HH:mm:ss")
          };
          body.QuestionId = key;
          body.AnswerText = formvalue[key];
          body.AnswerId = 1;
          body.IsActive = 1;
          this.props.InsertData({
            root: "SurveyResponse",
            body: body
          }, function (data) {
            if (data.data.status != 200) {
              flag = false;
            }
          });
        }
        if (flag) {

          // Update survey reponse completed
          let res = this.props.UpdateData({
            root: "SurveyAgentMapping",
            body: {
              'IsCompleted': 1,
              'UpdatedOn': moment().format("YYYY-MM-DD HH:mm:ss")
            },
            querydata: { "UserId": uid, "SurveyId": sid },
            c: "L",
          });

          alert('Form submitted successfully');
          this.setState(() => ({
            saved: true
          }));
        } else {
          toast.error("Cannot Submit Survey!");
        }
      }
      // let departmentid = formvalue["departmentId"];

      setTimeout(function () {
        this.props.GetCommonData({
          limit: 10,
          skip: 0,
          root: this.state.root,
          cols: GetJsonToArray(this.columnlist, "name"),
          con: [{ "SurveyID": sid }],
          c: "L",
        });
      }.bind(this), 1000);

      this.setState({ showModal: false });
    }
    return false;
  }

  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "radio") {
      this.handleFormvalue(e.target.getAttribute('name'));
      formvalue[e.target.getAttribute('name')] = e.target.getAttribute('value');
    }
    else if (e.target && e.target.type == "checkbox") {
      if (!(e.target.name in formvalue)) {
        formvalue[e.target.name] = [e.target.value];
      } else {
        const index = formvalue[e.target.name].indexOf(e.target.value);
        if (index > -1) {
          formvalue[e.target.name].splice(index, 1);
          if (formvalue[e.target.name].length == 0) {
            delete formvalue[e.target.name];
          }
        } else
          formvalue[e.target.name].push(e.target.value);
      }
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      this.handleFormvalue(e.target.id);
      formvalue[e.target.id] = e.target.type === 'number' ? parseInt(e.target.value) : e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  handleFormvalue = (QuestionID) => {
    let formvalue = this.state.formvalue; let items = this.state.items;
    delete formvalue[QuestionID];
    for (let i = 0; i < items.length; i++) {
      if (items[i].QuestionID == QuestionID && items[i].NestedQuestionId != 0) {
        console.log('-------------------', items[i].NestedQuestionId);
        this.handleFormvalue(items[i].NestedQuestionId)
      }
    }
    this.setState(() => ({
      formvalue
    }));
  }

  renderSwitch(stepOnePart, items) {
    if (stepOnePart == 1)
      return <i className="fa fa-spinner fa-spin"></i>

    if (stepOnePart == 2)
      return <p className="survey-response info">Invalid action</p>

    if (stepOnePart == 3)
      return <p className="survey-response info">Your survey response is already recorded</p>

    if (stepOnePart == 4) {
      if (this.state.saved) {
        return <p className="survey-response info">Your response is submitted successfully</p>
      }
      let nested = this.state.nested; let formvalue = this.state.formvalue;
      let count = {};
      return (
        <>
          {this.state.PageDescription ? <div dangerouslySetInnerHTML={{ __html: this.state.PageDescription }} /> : null}
          <Form name="frmChatAgentConfigure">
            {items && items.map(item => {

              if (!count[item.QuestionID]) count[item.QuestionID] = 0;
              count[item.QuestionID] = count[item.QuestionID] + 1;

              return (<div>
                {!item.IsNestedQuestion &&
                  <Question
                    QuestionID={item.QuestionID}
                    QuestionText={item.QuestionText}
                    FieldOptions={item.FieldOptions}
                    FieldType={item.FieldType}
                    IsRequired={item.IsRequired}
                    SelectedOption={item.SelectedOption}
                    NestedQuestionId={item.NestedQuestionId}
                    items={this.state.items}
                    formvalue={formvalue}
                    display={count[item.QuestionID] == 1}
                    handleChange={this.handleChange} />
                }
              </div>)
            })}
            <Form.Group as={Row}>
              <Col>
                <Button type="submit" onClick={this.handleSave}>Submit survey</Button>
              </Col>
            </Form.Group>
          </Form></>)
    }
  }


  render() {
    const { stepOnePart, items, PageTitle, PageDescription, showModal, FormTitle, formvalue, ModalValueChanged, event } = this.state;
    console.log("renderitems", items, formvalue);


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card className="surveyform col-md-6 col-xs-12">
                <CardHeader>
                  <Row>
                    <Col md={6} className="col-md-12 text-center">
                      <CardTitle tag="h3">
                        {PageTitle}
                      </CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>

                  {this.renderSwitch(stepOnePart, items)}
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    GetCommonspData
  }
)(SurveyForm);