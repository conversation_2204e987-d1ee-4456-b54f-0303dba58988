import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists, PostIncentiveFormData
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, joinObject } from '../utility/utility.jsx';
import { getUrlParameter } from "../utility/utility.jsx";
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DropDown from './Common/DropDownList';
import RealTimePanel from './RealTimePanel/RealTimePanel';
import ProcessIncentiveFile from './ProcessIncentiveFile';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import DataTable from './Common/DataTableWithFilter';
import Date from "./Common/Date"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class UploadIncentiveData extends React.Component {

   type= getUrlParameter('type');
   

    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            selectedIvrProduct: "",
            ivrType: "0",
            ivrProducts: [],
            addClass: '',
            UserId: '',
            selectedFile: null,
            uploadFile: this.type,
            ManagerId: '',
            DownloadedFile:'',
            ResponseData: '',
            UploadStatusColumns: [],
            StartDate: moment().format("YYYY-MM-01"),
            prodId: '',
            TypeAwsoptions: [8, 9, 10, 15, 16],
            IncentiveFileData: [],
            isUpload: false,
        };
        

        if(!this.type){
            this.type=0;
        }
    

        this.UploadFileList = {
            config:
            {
                data: [
                    { Id: 1, Display: "First Premium Paid only" },
                    { Id: 2, Display: "Free Look Cancellation"},
                    { Id: 3, Display: "Quality Score" },
                    { Id: 4, Display: "Warning Letters"  },
                    { Id: 5, Display: "Arrears and Claw backs"},
                    { Id: 6, Display: "Incentivemade" },
                    { Id: 7, Display: "Payout"  },
                    { Id: 8, Display: "DocuploadData" },
                    { Id: 9, Display: "SI" },
                    { Id: 14, Display: "E2E" },
                    { Id: 10, Display: "Selections" },
                    { Id: 12, Display: "Other Incentive" },
                    { Id: 11, Display: "Agent Salary"},
                    { Id: 13, Display: "User Group"},
                    { Id: 15, Display: "BookingSection" },
                    { Id: 16, Display: "AgentSection" },
                    { Id: 17, Display: "SlabSystem" },
                    { Id: 18, Display: "FloorProcessMapping" },
                    { Id: 20, Display: "AgentActiveSheet" },
                    { Id: 21, Display: "Arrears and Clawbacks Booking Details" },  
                    { Id: 22, Display: "Reopen Leads" }
                ]
            }
        };

       
          

        this.ProductList = {
            config:
            {
                root: "Products",
                cols: ["ID AS Id", "ProductName AS Display"],
                con: [{ "Isactive": 1 }],
            }
        };
        this.dateRangeRef = React.createRef();

    }

    componentDidMount() {
        this.UserList();
       
        if(this.type==22)
        {
            this.uploadfilechange({target:{
                value: this.type
            }})
           
            
        }
    }
    

    fnDatatableCol(columnlist) {

        var columns = fnDatatableCol(columnlist);
        return columns;
    }

    UserList() {
        debugger;
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ ManagerId: managerid });
    }

    // On file select (from the pop up) 
    onFileChange(event) {
        debugger;
        console.log(event.target.files[0]);
        // Update the state 
        this.setState({ selectedFile: event.target.files[0], isUpload: false });

    };

    // On file upload (click the upload button) 
    onFileUpload(e) {
        e.preventDefault();
        let a =this.state.uploadFile;
       
        if (this.state.prodId == '' && a!=22) {
            toast("Please choose Product", { type: 'error' });
            return;
        }
        if (this.state.uploadFile == '') {
            toast("Please select Upload File Type", { type: 'error' });
            return;
        }
        if (this.state.selectedFile == null) {
            toast("Please choose Excel File", { type: 'error' });
            return;
        }
        // Create an object of formData 
        const formData = new FormData();
        console.log(formData);
        // Update the formData object 
        formData.append(
            "myFile",
            this.state.selectedFile,
            this.state.selectedFile.name
        );
        formData.append('TypeId', this.state.uploadFile);
        formData.append('UserId', this.state.ManagerId);
        formData.append('ProductId', this.state.prodId);
        formData.append('ValidFrom', moment(this.state.StartDate).format("YYYY-MM-01"));
        if (this.state.TypeAwsoptions.indexOf(parseInt(this.state.uploadFile)) > -1) {
            formData.append('TypeIsXml', JSON.stringify(false));
        } else {
            formData.append('TypeIsXml', JSON.stringify(true));
        }

        // Details of the uploaded file 
        console.log(this.state.selectedFile);
        document.getElementById('uploadbutton').innerHTML = 'Upload! <i class="fa fa-spinner fa-spin"></i>';
        // Request made to the backend api 
        // Send formData object 

        PostIncentiveFormData(formData, function (results) {
            debugger
            console.log(results);
            document.getElementById('uploadbutton').innerHTML = 'Upload!';
            document.getElementById('files-upload').value = null;
            if (results && results.data && results.data.status == 200) {
                this.setState({ ResponseData: results  && results.data && results.data.data, isUpload: true, selectedFile: null });
                alert('File uploaded');
            } else {
                this.setState({ ResponseData: '', isUpload: true, selectedFile: null });
                // console.log('dd', results.data.message)
                toast(results.data.message, { type: 'error' });
                return;
            }
            this.renderProcessFiles();

        }.bind(this));
    }

    fileData() {
        debugger;
        if (this.state.selectedFile) {

            return (
                <div>
                    {/* <span>File Details:</span>  */}
                    <span>File Name: {this.state.selectedFile.name}</span>
                    <p>File Type: {this.state.selectedFile.type}</p>
                    {/* <p> 
                Last Modified:{" "} 
                {this.state.selectedFile.lastModifiedDate.toDateString()} 
              </p>  */}
                </div>
            );
        } else {
            return (
                <div>
                    <br />
                    <h4>Choose before Pressing the Upload button</h4>
                </div>
            );
        }
    }

    uploadfilechange(e, props) {
        debugger;
        this.setState({
            uploadFile: e.target.value,
            DownloadedFile:'',
            isUpload: false
        }, () => {
            this.renderProcessFiles();
        });
        if (e.target.value == 1) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/FirstPremPaid.xlsx" });
        } else if (e.target.value == 2) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/FreeLookCancellation.xlsx" });
        }
        else if (e.target.value == 3) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/AgentScore.xlsx" });
        }
        else if (e.target.value == 4) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/agentwarnings.xlsx" });
        }
        else if (e.target.value == 5) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/Arrears.xlsx" });
        }
        else if (e.target.value == 6) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/incentive.xlsx" });
        }
        else if (e.target.value == 7) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/payout.xlsx" });
        }
        else if (e.target.value == 12) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/OtherIncentive.xlsx" });
        }
        else if (e.target.value == 11) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/UploadCTC.xlsx" });
        }
        else if (e.target.value == 13) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/uploadUserGroup.xlsx" });
        }
        else if (e.target.value == 14) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/E2E.xlsx" });
        }
        else if (e.target.value == 15) {
            if (this.state.prodId == 117) {
                this.setState({ DownloadedFile: "/SampleExcelfiles/Booking_Motor.xlsx" });
            } else {
                this.setState({ DownloadedFile: "/SampleExcelfiles/BookingSection.xlsx" });
            }
        }
        else if (e.target.value == 16) {
            if (this.state.prodId == 117) {
                this.setState({ DownloadedFile: "/SampleExcelfiles/Agent_motor.xlsx" });
            } else {
                this.setState({ DownloadedFile: "/SampleExcelfiles/AgentSection.xlsx" });
            }
        }
        else if (e.target.value == 17) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/SlabMapping.xlsx" });
        }
        else if (e.target.value == 18) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/FloorMapping.xlsx" });
        }

        else if (e.target.value == 20) {
            this.setState({ DownloadedFile: "/SampleExcelfiles/AgentActiveSheet.xlsx" });
        }
        else if(e.target.value == 21){
            this.setState({DownloadedFile : "/SampleExcelfiles/Arrears&ClawbacksBookingDetails.xlsx"});
        }
        else if(e.target.value == 22){
            this.setState({DownloadedFile : "/SampleExcelfiles/SampleReopenLeads.xlsx"});
        }
    }

    renderDownloadFile() {

        if (this.state.DownloadedFile) {
            return <Link to={this.state.DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    renderProcessFiles() {
        debugger;
        if (this.state.prodId && this.state.uploadFile) {
            return <ProcessIncentiveFile ProductId={this.state.prodId} TypeId={this.state.uploadFile}
                IncentiveMonth={moment(this.state.StartDate).format("YYYY-MM-01")} isUpload={this.state.isUpload}></ProcessIncentiveFile>
        }
    }
    renderUploadStatus() {
        debugger;

        if (this.state.ResponseData && this.state.ResponseData.length > 0) {
            let UploadStatusColumns = [];
            let ResponseData = this.state.ResponseData;

            Object.entries(ResponseData && ResponseData.length>0 && ResponseData[0]).map(([key, value]) => {
                if (key == 'Status') {
                    UploadStatusColumns.push({
                        label: "Status",
                        name: "Status",
                        cell: row => <div className="Status">{row.Status ? row.Status : "N.A"}</div>,
                    })
                } else if (key == 'FirstPremPaid' || key == 'FLC') {
                    UploadStatusColumns.push({
                        label: key.toString(),
                        name: key.toString(),
                        type: "bool",
                    })
                } else {
                    UploadStatusColumns.push({
                        "name": key.toString(),
                        "label": key.toString(),
                        searchable: true,
                    })
                }
            })

            const columns = this.fnDatatableCol(UploadStatusColumns);

            return <Row>
                <Col md="8">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={8}>
                                    <CardTitle tag="h6">Upload Data Status</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={columns} data={this.state.ResponseData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderMonthField() {
        let type = this.state.uploadFile
        //if(type == 3 || type == 4 || type ==5 || type ==6 || type ==7 || type ==8){
        return <Col md={2}><Date onStartDate={this.handleStartDate.bind(this)}> </Date></Col>
        //}
    }

    handleStartDate = (StartDateValue) => {
        debugger;
        this.setState({ StartDate: StartDateValue, isUpload: false }, () => {
            this.renderProcessFiles();
        });
    }

    productchange(e, props) {
        this.setState({ prodId: e.target.value, isUpload: false }, () => {
            this.renderProcessFiles();
        });
    }
    render() {
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            {/* <Card> */}
                            <CardHeader>
                                <Row>
                                    <Col md={2}>
                                        <Form.Group controlId="product_dropdown">
                                            <DropDown firstoption="Select Product" valueTobefiltered={[2, 115, 7, 117, 147,217, 131, 3]} col={this.ProductList} onChange={this.productchange.bind(this)}>
                                            </DropDown>
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group controlId="upload_file_dropdown">
<DropDown firstoption="Select Upload File" disabled={this.type>0 ? true: false} value={this.state.uploadFile} col={this.UploadFileList} onChange={this.uploadfilechange.bind(this)}>
                                            </DropDown>
                                        </Form.Group>
                                        {this.renderDownloadFile()}
                                    </Col>
                                    {this.renderMonthField()}
                                    <form ref="form" onSubmit={this.onFileUpload.bind(this)}>
                                        <input type="file" id="files-upload" onChange={this.onFileChange.bind(this)} />
                                        <button type="submit" id="uploadbutton" className="btn btn-primary">Upload!</button>
                                    </form>
                                    {/* <Col md={3}>
                                            <Form.Group controlId="Incentivefile_upload" >
                                                <Form.Label><i>*</i> File Upload </Form.Label>
                                                <input type="file" onChange={this.onFileChange.bind(this)} /> 
                                            </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                    <Button onClick={this.onFileUpload.bind(this)}> Upload! </Button> 
                                    </Col> */}

                                </Row>

                            </CardHeader>
                        </Col>
                    </Row>
                    {/* <CardBody> */}
                    {this.fileData()}
                    {this.renderProcessFiles()}
                    {this.renderUploadStatus()}
                    {/* </CardBody> */}
                    {/* </Card> */}

                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData,
    }
)(UploadIncentiveData);