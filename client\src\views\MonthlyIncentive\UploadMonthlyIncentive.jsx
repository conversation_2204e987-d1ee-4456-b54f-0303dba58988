import React from "react";
import DropDown from '../Common/DropDownList';
import { connect } from "react-redux";
import Date from "../Common/Date"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'
import Datetime from 'react-datetime';
import { JsonToTable } from "react-json-to-table";
import {
    PostMonthlyIncentiveFormData
} from "../../store/actions/CommonAction";
import {
    addRecord,GetCommonData
  } from "../../store/actions/CommonMongoAction";
//import {PostMonthlyIncentiveData} from '../../store/actions/CommonMongoAction'
import {
    CardHeader,
    Row,
    Col
} from "reactstrap";
import { Button, Form } from 'react-bootstrap';


class UploadMonthlyIncentive extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            uploadFile: "",
            UserId: "",
            isUploaded: false,
            SampleFile: "",
            ProductID: "",
            currDate: moment().format("MM-YYYY"),
            IncentiveMonth: moment().format("MM-YYYY"),
            selectedFile: "",
            xlsFile: [],
            columnValidation: null,
            incorrectColumns: [],
            columnList: [],
            incorrectRowNumber: "",
            incorrectColumnsName: "",
            uploadStatus: false,
            DataCount: 0,
            ErrorDataCount: 0,
            errorRecords: "",
            jsonError: {},
            c: "M"
        };
        this.UploadList = {
            config:
            {
                data:[
                    {Id: 1, Display: "Agent Level Data"},
                    {Id: 2, Display: "Booking Level Data"},
                    {Id: 3, Display: "Other Incentive"},
                    {Id: 4, Display: "Arrears & Clawback"},
                    {Id: 5, Display: "Policy Type"},
                    {Id: 6, Display: "Miss Sell"},
                    {Id: 7, Display: "Slab System"}
                ]
            }
        };
        this.ProductList = {
            config:
            {
                root: "Products",
                cols: ["ID AS Id", "ProductName AS Display"],
                con: [{ "Isactive": 1 }],
            }
        };
        
    }
    productChange(e){
        this.setState({ProductID : e.target.value})
    }
    renderSampleFile(){
        if(this.state.SampleFile){
            return <Link to={this.state.SampleFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }
    setIncentiveMonth(e){
        this.setState({IncentiveMonth : e.format("MM-YYYY"), currDate:  '01-' + e.format("MM-YYYY") })

    }
    validation = (currentDate) => {
        return currentDate.isBefore(moment());
    };
    onFileChange(e){
        let file = e.target.files[0]
        this.file = file
        //console.log(readXlsxFile(e.target.files[0]))
        // var data = read(e.target.files[0], {type: "array"})
        // var t = utils.sheet_to_json(data)
        
        

    }
    getColumnData(keys, columnList){
        this.setState({columns: keys})

        var dataArray = Object.keys(columnList).map(function(k){return columnList[k].name});
        
        let check1=keys.filter(function(value) 
        {
          return dataArray.indexOf(value) == -1; 

        });
        let check2= dataArray.filter(function(value) 
        {
                return keys.indexOf(value) == -1; 
            
            });

        let output=check1.concat(check2);
        if(output.length == 0 ){
            this.setState({columnValidation: true})
        }
        if(output.length > 0){
            this.setState({columnValidation: false, incorrectColumns: output})
        }

        }
    
    renderMonthField(){
        return <Col md={2}>
            <Datetime value = {new Date()}
            dateFormat = "MM-YYYY"
            value = {this.state.IncentiveMonth}
            isValidDate={this.validation.bind(this)}
            onChange = {this.setIncentiveMonth.bind(this)}
            utc={true}
            timeFormat={false}
            />
        </Col>
    }
    //,"Process","Supergroupid","SourcedAPE","IssuedAPE","WeightedAPE","SourceBKGs","IssuedBkg","AmountMade","FinalIncentive","IncentiveMonth"
    uploadFileChange(e){
        this.setState({
            uploadFile: e.target.value
        })
        if(e.target.value == 1 && this.state.ProductID == 7){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_Agent_Level_Term.xlsx", root: "TermAgentLevelData"})
        }
        if(e.target.value == 2 && this.state.ProductID == 7){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_Booking_Level_Term.xlsx", root: "TermBookingLevelData"})
        }
        if(e.target.value == 1 && this.state.ProductID == 115){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/Sample_agent_saving.xlsx", root: "InvestmentAgentLevelData"})
        }
        if(e.target.value == 2 && this.state.ProductID == 115){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/sample_booking_saving.xlsx", root: "InvestmentBookingData"})
        }
        if(e.target.value == 1 && this.state.ProductID == 2){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/Sample_agent_health.xlsx", root: "HealthAgentLevelData"})
        }
        if(e.target.value == 2 && this.state.ProductID == 2){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/Sample_booking_health.xlsx", root: "HealthBookingLevelData"})
        }
        if(e.target.value == 1 && this.state.ProductID == 117){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/sample_motor_agent.xlsx", root: "MotorAgentLevelData"})
        }
        if(e.target.value == 2 && this.state.ProductID == 117){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/sample_moto_booking.xlsx", root: "MotorBookingLevelData"})
        }
        if(e.target.value == 5 && this.state.ProductID == 117){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/Sample_motor_pol_type_v2.xlsx", root: "MotorPolicyTypeData"})
        }
        if(e.target.value == 1 && this.state.ProductID == 147){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_Agent_level_HR.xlsx", root: "HealthRenewalAgentLevelData"})
        }
        if(e.target.value == 6){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_Mis_Sell.xlsx", root: "MissSellData", c: "MD"})
        }
        if(e.target.value == 1 && this.state.ProductID == 217){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_motorrenewal_Agent.xlsx", root: "MotorRenewalAgentLevelData"})
        }
        if(e.target.value == 5 && this.state.ProductID == 217){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_motorrenewal_PolicyType.xlsx", root: "MotorRenewalPolicyTypeData"})
        }
        if(e.target.value == 2 && this.state.ProductID == 217){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_motorrenewal_Booking.xlsx", root: "MotorRenewalSlabSystem"})
        }
        if(e.target.value == 7 && this.state.ProductID == 217){
            this.setState({SampleFile : "/SampleExcelfiles/MonthlyIncentive/SampleFile_motorrenewal_Slab.xlsx", root: "MotorRenewalBookingLevelData"})
        }
    }
    setStateOfXlsFile(file){
        this.setState({xlsFile: file})
        console.log(this.state.xlsFile)
        
    }
    typeConvert(value, datatype){
        if(datatype == "string"){
            return String(value)
        }
        if(datatype == "numericalString"){
            return value.toLocaleString()
        }
    }
    setStateOfXlsFile(file){
        this.setState({xlsFile: file})
        console.log(this.state.xlsFile)
        
    }
    GetRecordCount(e){
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: {},
            type: "count"
        }, function(data){
            this.setState({DataCount: data.data.data})
        }.bind(this))
        return;
    }
    GetErrorCount(e){
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: {error : {$exists: true}},
            type: "count"
        }, function(data){
            this.setState({ErrorDataCount: data.data.data})
        }.bind(this))
        return;

    }

    GetErrorRecords(e){
        e.preventDefault()
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: {error : {$exists: true}},
        }, function(data){
            console.log("Ins", data)
            this.setState({errorRecords: JSON.stringify(data.data.data), jsonError: data.data.data})

        }.bind(this))
    }
     
    onFileUpload(e){
        e.preventDefault()
        if(this.state.ProductID == ""){
            toast("Please choose Product", { type: 'error' });
            return;
        }
        if(this.state.uploadFile == ""){
            toast("Please Upload File", { type: 'error' });
            return;
        }
        if(this.state.currDate == ""){
            toast("Please choose a date", { type: 'error' });
            return;
        }
        if(this.state.root == ""){
            toast("Invalid Operation", {type: 'error'});
        }
        console.log(this.file, this.file.name, this.state.uploadFile, this.state.ProductID)
        
        const formData = new FormData();
        console.log(formData)
        formData.append("myFile", this.file, this.file.name);
        formData.append('FileType', this.state.uploadFile);
        formData.append('ProductID', this.state.ProductID);
        formData.append('UploadDate', this.state.currDate);
        PostMonthlyIncentiveFormData(formData, function (results){
            //alert("File Uploaded")
            console.log(results.data)
            if(results.data.status == 403){
                this.setState({incorrectColumns : results.data.data})
                console.log(results.data.data)
                alert("Incorrect Columns")
            }
            if(results.data.status == 40){
                this.setState({incorrectRowNumber : results.data.row, incorrectColumnsName: results.data.column })
                alert("Column Data Incorrect")
            }
            if(results.data.status == 41){
                alert(results.data.message) // date error
            }
            if(results.data.status == 200){
                this.setState({uploadStatus: true})
                alert(results.data.message)

            }
            if(results.data.status == 501){
                alert(results.data.message)
            }

        }.bind(this))

        
    }
    

    
    render() {
        
        return (
            <>
            <div className="content">
                    <ToastContainer />
             <Row>
                        <Col md="12">
                            {/* <Card> */}
                            <CardHeader>
                                <Row>
                                    <Col md={2}>
                                        <Form.Group controlId="product_dropdown">
                                            <DropDown firstoption="Select Product" valueTobefiltered={[2, 115, 7, 117, 147, 217, 131]} col={this.ProductList} onChange={this.productChange.bind(this)}>
                                            </DropDown>
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group controlId="upload_file_dropdown">
                                            <DropDown firstoption="Select Upload File" value={this.state.uploadFile} col={this.UploadList} onChange={this.uploadFileChange.bind(this)}>
                                            </DropDown>
                                        </Form.Group>
                                        {this.renderSampleFile()}
                                    </Col>
                                    {this.renderMonthField()}
                    
                                    <form ref="form" onSubmit={this.onFileUpload.bind(this)}>
                                        <input type="file" id="files-upload" onChange={this.onFileChange.bind(this)} />
                                        <button type="submit" id="uploadbutton" className="btn btn-primary">Upload!</button>
                                    </form>
                                

                                </Row>

                            </CardHeader>
                        </Col>
                    </Row>
                    {this.state.incorrectColumns.length > 0 && <h4>Error while processing file. Please Check columns {this.state.incorrectColumns}</h4>}
                    {this.state.incorrectColumnsName != "" && this.state.incorrectRowNumber != "" && <h4>Error while processing file. Please Check columns {this.state.incorrectColumnsName} at line {this.state.incorrectRowNumber}</h4>}
                    <Row>
                    <Col md={3}>
                        {this.state.uploadStatus && <Button variant = "secondary" onClick = {this.GetRecordCount.bind(this)}>Total Records</Button>}
                        {this.state.uploadStatus && <h6>Records in DB : {this.state.DataCount}</h6> }
                    </Col>
                    <Col md={3}>
                        {this.state.uploadStatus && <Button variant = "secondary" onClick = {this.GetErrorCount.bind(this)}>Error Records</Button>}
                        {this.state.uploadStatus && <h6>Error Records in DB : {this.state.ErrorDataCount}</h6> }
                    </Col>
                    <Col md={3}>
                        {this.state.uploadStatus && this.state.ErrorDataCount != 0 && <Button variant = "secondary" onClick = {this.GetErrorRecords.bind(this)}>Get Error Records</Button>}
                        
                        
                    </Col>
                    </Row>
                    {this.state.ErrorDataCount != 0 && this.state.errorRecords != ""  && <JsonToTable json={this.state.jsonError} />}
                    
                    
                    
                    </div>
            </>
        )
    }

}
function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,{addRecord, GetCommonData}
) (UploadMonthlyIncentive)



