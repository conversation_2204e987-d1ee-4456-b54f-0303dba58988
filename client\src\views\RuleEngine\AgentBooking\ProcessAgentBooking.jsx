import axios from 'axios';
import React, { Component } from 'react';
import { Form , Button } from 'react-bootstrap';
import BeatLoader from "react-spinners/BeatLoader";
import Pagination from 'react-bootstrap/Pagination';
import config from '../../../config';
import SweetAlert from 'react-bootstrap-sweetalert';
import Panel from '../Panel/Panel';
import { connect } from 'react-redux';
import { addCalculatedAgentIncentive} from '../../../store/actions/RuleActions/Ruleset';

class ProcessAgentBooking extends Component{
    constructor(props){
        super(props);
        this.state = {
            agentId : '',
            incentiveMonth : '',
            agentData : [],
            bookingData : [],
            addonData: [],
            deductionData: [],
            finalIncentive: 0,
            agentLevelResult: 0,
            // currentPage: 1,
            // firstPage: 1,
            // lastPage: 1,
            // allPages: Array.from({ length: 1 }, (_, index) => index + 1),
            response : false,
            loading : false,
            product : '',
            textToCopy : "",
            errorStatus: false
        }
        this.handleAgentIdChange = this.handleAgentIdChange.bind(this);
        this.handleIncentiveMonthChange = this.handleIncentiveMonthChange.bind(this);
        this.handleProductChange = this.handleProductChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.fun= this.fun.bind(this);
        // this.handleFirst = this.handleFirst.bind(this);
        // this.handleLast = this.handleLast.bind(this);
        // this.handleClick = this.handleClick.bind(this);
        // this.handlePrev = this.handlePrev.bind(this);
        // this.handleNext = this.handleNext.bind(this);
    }
    
    // handleClick(event) {
    //     const x = parseInt(event.target.innerText);
    //     this.setState({ currentPage: x });
    // }

    // handleFirst(event) {
    //     this.setState({ currentPage: 1 });
    // }

    // handleLast(event) {
    //     this.setState({ currentPage: this.state.lastPage });
    // }

    // handleNext(event) {
    //     const next = Math.min(this.state.lastPage, this.state.currentPage + 1);
    //     this.setState({
    //         currentPage: next,
    //     })
    // }

    // handlePrev(event) {
    //     const prev = Math.max(1, this.state.currentPage - 1);
    //     this.setState({
    //         currentPage: prev,
    //     })
    // }


    handleAgentIdChange(event){
        event.preventDefault();
        this.setState({
            agentId : event.target.value,
        });
    }
    
    handleIncentiveMonthChange(event){
        event.preventDefault();
        this.setState({
            [event.target.name]: event.target.value,
        });
    }

    handleProductChange(event){
        event.preventDefault();
        this.setState({
            product : event.target.value
        })
    }

    handleSubmit(event){
        event.preventDefault();
        const incentiveMonth = this.state.incentiveMonth + "-01";
        const productId = parseInt(this.state.product);

        this.setState({
            loading : true,
        })
        const URL = `${config.api.base_url}/ruleengine/AgentIncentive/?agentId=${this.state.agentId}&incentiveMonth=${incentiveMonth}&productId=${productId}`;
        axios.get(URL)
        .then((res)=>{
            let data = {}
            
            if(res !== null && res.data !== undefined &&
                res.data !== null && res.data.data !== undefined) {
                    data = res.data.data;
                }
            if(data !== {} && data !== null) {
                this.props.addCalculatedAgentIncentive(data);
            }
            
            const agentData = res.data.data['agent'];
            // const bookingData = res.data.data['booking'];
            // const addonData = res.data.data['addon'];
            // const deductionData = res.data.data['deduction'];
            // const agentLevelResult = res.data.data['AgentLevelResult'];
            // const finalIncentive = res.data.data['FinalIncentive'];

            // const len = res.data.data.length;
            // const lastPage  = Math.floor((len+29)/30);
            this.setState({
                agentData : agentData,
                // bookingData : bookingData,
                // addonData : addonData,
                // deductionData : deductionData,
                // agentLevelResult : agentLevelResult,
                // finalIncentive : finalIncentive,
                // currentPage : 1,
                // lastPage : lastPage,
                // allPages: Array.from({ length: lastPage }, (_, index) => index + 1),
                textToCopy : JSON.stringify({
                    searchKeys: [
                        {
                            key: 'Product',
                            value: productId
                        },
                        {
                            key: 'IncentiveMonth',
                            value: incentiveMonth
                        }
                    ],
                    facts : agentData.length > 0 ? agentData[0].facts : "{}",
                    criteria : "agent",
                    operationType: "basic"
                },null,4)
                ,
                loading : false,
                response : true,
            });
        }).catch((err) => {
            this.setState({
                loading : false,
                response: true,
                errorStatus: true
            })
        });

    }

    fun() {
        this.setState({ copied: true})
    }


    render(){
        const {
            agent,
            booking,
            addon,
            deduction,
            AgentLevelResult,
            FinalIncentive,
            AgentDetails
        } = this.props.calculatedAgentIncentive;

        return (
            <React.Fragment>
                <Panel>
                <h4>Agent Level Processing</h4>
                <div>
                    <form onSubmit={this.handleSubmit}>
                        <table>
                            <tr>
                                <td>
                                    <label>Agent Id</label>    
                                    <input 
                                        name="agentId" 
                                        type = "string"
                                        value={this.state.agentId}
                                        placeholder="PW00012"
                                        onChange = {this.handleAgentIdChange}
                                        required
                                    />
                                </td>
                                <td>
                                    <label 
                                        style={{marginLeft:"50px"}}
                                    >Incentive Month</label>    
                                    <input 
                                        name="incentiveMonth" 
                                        type = "month"
                                        value={this.state.incentiveMonth}
                                        onChange = {this.handleIncentiveMonthChange}
                                        required
                                    />
                                </td>
                                <td>
                                <label htmlFor="product"
                                    style={{marginLeft:"50px"}}
                                >Product</label>
                                <select 
                                    name="product" 
                                    id="product"
                                    onChange={this.handleProductChange}
                                    required>
                                    <option value="">Select Product</option>
                                    <option value="7">Term</option>
                                    <option value="117">Motor</option>
                                    <option value="2">Health</option>
                                    <option value="115">Investment</option>
                                </select>
                                </td>
                                <td>
                                    {!this.state.loading && booking !== undefined && booking.length > 0  &&
                                        <>
                                             
                                            <i 
                                                className="icon fa fa-clipboard fa-2x"
                                                style={{marginLeft : "20px"}}
                                                onClick={
                                                            () => {
                                                                navigator.clipboard.writeText(this.state.textToCopy);
                                                                this.fun();
                                                            }

                                                        }
                                            />
                                        </>
                                    }
                                    {
                                        this.state.copied &&
                                        <SweetAlert
                                            title="Copied to clipboard"
                                            onConfirm={() => this.setState({ copied: false})}
                                        >
                                            Copied!
                                        </SweetAlert>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <div style={{ marginTop : "20px" }}> 
                                <td colspan="2">
                                    <Button type="submit">
                                        Process Incentive
                                    </Button>
                                </td>
                                </div>
                      
                            </tr>
                        </table>
                    </form>
                </div>
                </Panel>
                {this.state.loading &&
                    <div style={{textAlign:"center"}}>
                        <BeatLoader size={20}
                            color={"#3498db"}
                            loading={true}
                        />
                    </div>
                }
                {!this.state.loading && this.state.response &&
                    this.state.errorStatus &&
                    <div style={{textAlign: 'center'}}>
                        <h4>Error Occured !!</h4>
                    </div>
                         
                }
                {!this.state.loading && AgentDetails !== undefined && AgentDetails !== null  &&
                <div className="RulesListTable">
                     
                    <h4>Agent Details</h4>
                    <table>
                        <thead>
                            <tr key='Heading'>
                                <th itemScope="col">S No.</th>
                                <th itemScope="col">AgentId</th>
                                <th itemScope="col"> Process</th>
                                <th itemScope="col">SubProcess</th>
                                <th itemScope="col">Team</th>
                                <th itemScope="col">Tenure On Floor</th>
                            </tr>
                        </thead>
                        <tbody>
                            {                    

                                    <tr key={1}>
                                        <td>{1}</td>
                                        <td>{AgentDetails.empid}</td>
                                        <td>{AgentDetails.agentprocessgroup}</td>
                                        <td>{AgentDetails.agentsubprocessgroup}</td>
                                        <td>{AgentDetails.team}</td>
                                        <td>{AgentDetails.agentonfloortenure}</td>
                                       
                                        
                                    </tr>
                                        
                            }
                        </tbody>
                    </table>
                </div>
                }
                {!this.state.loading && booking !== undefined && booking.length > 0  &&
                <div className="RulesListTable">
                     
                    <h4>Booking Level Incentive</h4>
                    <table>
                        <thead>
                            <tr key='Heading'>
                                <th itemScope="col">S No.</th>
                                <th itemScope="col">Lead ID</th>
                                <th itemScope="col"> Description</th>
                                <th itemScope="col">Amount</th>
                                <th itemScope="col">Expression</th>
                                <th itemScope="col">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                booking.map((book,i) => 
                                    
                                        book.events.map((item,j)=>{

                                                return (
                                                    <tr key={i}>
                                                        <td>{i + 1}.{j+1}</td>
                                                        <td>{book.leadid}</td>
                                                        <td>{item.params.description}</td>
                                                        <td>{item.params.amount}</td>
                                                        <td>{item.params.EXPRESSION}</td>
                                                        <td>{item.params.value}</td>
                                                        
                                                    </tr>
                                                )
                                        })
                                    
                                )
                            }
                        </tbody>
                    </table>
                </div>
                }
                {!this.state.loading &&agent !== undefined && agent.length > 0  &&
                <div className="RulesListTable">
                    <h4>Agent Level Incentive</h4>
                    <table>
                        <thead>
                            <tr key='Heading'>
                                <th itemScope="col">S No.</th>
                                <th itemScope="col">type</th>
                                <th itemScope="col"> Description</th>
                                <th itemScope="col">Amount</th>
                                <th itemScope="col">Expression</th>
                                <th itemScope="col">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                agent.map((agent,i) => 
                                    agent.events.map((item,j)=>{
                                            return (
                                                <tr key={i}>
                                                    <td>{i + 1}.{j+1}</td>
                                                    <td>{item.type}</td>
                                                    <td>{item.params.description}</td>
                                                    <td>{item.params.amount}{item.params.deduction}</td>
                                                    <td>{item.params.EXPRESSION}</td>
                                                    <td>{item.params.value}</td>
                                                    
                                                </tr>
                                            )
                                    })
                                )
                            }
                        </tbody>
                    </table>
                    <h4>Agent Level Result : {AgentLevelResult}</h4>
                </div>
                }

                {!this.state.loading && addon !== undefined && addon.length > 0  &&
                    <div className="RulesListTable">
                        <h4>Addons</h4>
                        <table>
                            <thead>
                                <tr key='Heading'>
                                    <th itemScope="col">S No.</th>
                                    <th itemScope="col">type</th>
                                    <th itemScope="col"> Description</th>
                                    <th itemScope="col">Addon</th>
                                    <th itemScope="col">Expression</th>
                                    <th itemScope="col">Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    addon.map((agent,i) => 
                                        agent.events.map((item,j)=>{
                                                return (
                                                    <tr key={i}>
                                                        <td>{i + 1}.{j+1}</td>
                                                        <td>{item.type}</td>
                                                        <td>{item.params.description}</td>
                                                        <td>{item.params.amount}{item.params.addon}</td>
                                                        <td>{item.params.EXPRESSION}</td>
                                                        <td>{item.params.value}</td>
                                                        
                                                    </tr>
                                                )
                                        })
                                    )
                                }
                            </tbody>
                        </table>
                         
                    </div>
                }

                
                {!this.state.loading &&  deduction !== undefined && deduction.length > 0  &&
                    <div className="RulesListTable">
                        <h4>Deductions</h4>
                        <table>
                            <thead>
                                <tr key='Heading'>
                                    <th itemScope="col">S No.</th>
                                    <th itemScope="col">type</th>
                                    <th itemScope="col">Description</th>
                                    <th itemScope="col">Deduction</th>
                                    <th itemScope="col">Expression</th>
                                    <th itemScope="col">Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    deduction.map((agent,i) => 
                                        agent.events.map((item,j)=>{
                                                return (
                                                    <tr key={i}>
                                                        <td>{i + 1}.{j+1}</td>
                                                        <td>{item.type}</td>
                                                        <td>{item.params.description}</td>
                                                        <td>{item.params.amount}{item.params.deduction}</td>
                                                        <td>{item.params.EXPRESSION}</td>
                                                        <td>{item.params.value}</td>
                                                        
                                                    </tr>
                                                )
                                        })
                                    )
                                }
                            </tbody>
                        </table>
                        <h4>Final Incentive : {FinalIncentive}</h4>
                    </div>
                }

                {FinalIncentive !== undefined && FinalIncentive === 0 &&
                                <div style={{ textAlign: 'center'}}>
                                    <p>No Rules Applied !!</p>
                                </div>    
                }

                
                
                {/* {!this.state.loading && this.state.bookings.length>0 && 
                    <Pagination>
                        <Pagination.First onClick={this.handleFirst} />
                        <Pagination.Prev onClick={this.handlePrev} />
                        {
                            this.state.allPages.map(item => {
                                if (Math.abs(item - this.state.currentPage) <= 3) {
                                    return <Pagination.Item 
                                            key={item} 
                                            value={item} 
                                            active={item == this.state.currentPage} 
                                            onClick={this.handleClick}
                                            >
                                                {item}
                                            </Pagination.Item>
                                }
                            })
                        }
                        <Pagination.Next onClick={this.handleNext} />
                        <Pagination.Last key={this.state.lastPage} onClick={this.handleLast} />
                    </Pagination>
                } */}
                {/* {!this.state.agentData.length>0 && this.state.response && !this.state.loading &&
                    <h4>No bookings found</h4>
                } */}
            </React.Fragment>
        )
    }

}

const mapStateToProps = (state) => ({
    calculatedAgentIncentive: state?.Ruleset?.calculatedAgentIncentive,
});

const mapDispatchToProps = dispatch => ({
    addCalculatedAgentIncentive: (payload) => dispatch(addCalculatedAgentIncentive(payload))
});
  
export default connect(mapStateToProps, mapDispatchToProps)(ProcessAgentBooking);