import React from "react";
import { GetCommonData, InsertData, UpdateData, GetCommonspData} from "../../store/actions/CommonAction";
import { connect } from "react-redux";


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
//import DecoupledEditor from '@ckeditor/ckeditor5-build-decoupled-document';
//import { CKEditor, CKEditorContext } from '@ckeditor/ckeditor5-react';

class CKTextEditor extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            uid: '',

        }; 

    }


    componentDidMount() {
       debugger;
    }

    componentDidUpdate(){
        debugger;

    }

    componentWillMount(){
        debugger;
    }

    render() {
        const { Error, UserInfo } = this.state;
        const editor = null;

        return (
            <>
            <Col md="12">
            {/* <CKEditor
                onReady={ editor => {
                    console.log( 'Editor is ready to use!', editor );

                    // Insert the toolbar before the editable area.
                    editor.ui.getEditableElement().parentElement.insertBefore(
                        editor.ui.view.toolbar.element,
                        editor.ui.getEditableElement()
                    );

                    this.editor = editor;
                } }
                onError={ ( { willEditorRestart } ) => {
                    // If the editor is restarted, the toolbar element will be created once again.
                    // The `onReady` callback will be called again and the new toolbar will be added.
                    // This is why you need to remove the older toolbar.
                    if ( willEditorRestart ) {
                        this.editor.ui.view.toolbar.element.remove();
                    }
                } }
                onChange={ ( event, editor ) => {debugger;
                    const data = editor.getData();
                    this.props.onEditorTextChange(data);

                    console.log('dataaa',data)
                    console.log( { event, editor } )} 
                }
                editor={ DecoupledEditor }
                data=""
               
            /> */}
            </Col>
            </>
        )

    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        InsertData,
        UpdateData,
        GetCommonspData
    }
)(CKTextEditor);