import React from "react";
import {
  GetCommonData, GetCommonspData, GetComunicationData
} from "../store/actions/CommonAction";
import { getUrlParameter, hhmmss, getuser,fnDatatableCol } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { GetDashboardLink } from "store/actions/CommonAction";
import config from "../config.jsx"
import DataTableWithFilter from "./Common/DataTableWithFilter";

class AgentDashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Agent Dashboard",
      items: {},
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      key: "-1",
      Performancekey: "-1",
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    // const Cell = ({ v }) => (
    //   <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    // );
    this.columnlist = [

      {
        name: "EmployeeId",
        label: "EmployeeId",

        sortable: true,
      },
      {
        name: "UserId",
        label: "UserId",
        hide: true,
        type: "number",
        sortable: true,
      },
      {
        name: "UserName",
        label: "UserName",

        sortable: true,
      },
      {
        name: "GroupName",
        label: "GroupName",

        sortable: true,
      },
      {
        name: "LoginHours (hrs)",
        label: "LoginHours",
        sortable: true,
        cell: row => <span >{(row.LoginHours / 60).toFixed(1)} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "LoginHours")}</span>
      },
      {
        name: "Link",
        label: "Link Dashboard",
        type: "string",
        editable: true,
        cell: row => <a style={{cursor: "pointer",color: "#0075FF"}} target="_blank" onClick={(e) => this.CreateDashboardURL(e, row)}>View Dashboard</a>,
        width: "200px"
      },
      
    ];
  }

  CreateDashboardURL(e, row){debugger;
    var json = {"b7cc6fb9c3d246689d889f2ee238e6df": btoa(Number(row.UserId))}
    GetDashboardLink(json, function (results) {
      if(results && results.data){
      let link = results.data.link;
      let url = config.api.DashboardUrl+"NX8YdJgx9CDkBLrqwgOLLSRdAHIRYEsmenC6BJCP2tV9fm9v3iMRBP4jievcFbK7D/"+link;
      window.open(url, "_blank");
      }

  }.bind(this));
  }
  handleShow(e) {

    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);

  }
  statuschange(e) {
    this.setState({ key: e.target.value });
  }

  CheckPerformance(e) {
    this.setState({ Performancekey: e.target.value });
  }
  filterdata(e) {

    let alldata = (this.state.items.UsersList)?this.state.items.UsersList:[]
    let that = this;
    if (this.state.key === "-1" && this.state.Performancekey === "-1") {
      return alldata;
    }

    let AgentData = [];
    alldata.forEach(element => {
      if (this.state.key != -1 && this.state.key.indexOf(element.GroupId) > -1) {
        AgentData.push(element);
      }      
    });

    if(AgentData && AgentData.length == 0){
      
    }
    



    return AgentData;
  }
  PerformanceCalculation(row, AvgGroupList, field, MissedCB) {
    let groupid = row.GroupId;
    let grpavg = {}
    if (AvgGroupList.length > 1) {
      AvgGroupList.forEach(item => {
        if (item.GroupId == groupid) {
          grpavg = item;
        }
      });

    }
    else {
      grpavg = AvgGroupList[0];
    }

    let data = ((row[field] / (grpavg[field] == 0 ? 1 : grpavg[field])) - 1) * 100;
    let color;
    if (MissedCB == true) {
      if (data > 0) {
        color = "fa fa-long-arrow-up userstatred";
      }
      else {
        color = "fa fa-long-arrow-down userstatgreen";
      }
    }
    else {
      if (data < 0) {
        color = "fa fa-long-arrow-down userstatred";
      }
      else {
        color = "fa fa-long-arrow-up userstatgreen";
      }
    }

    if (data < 0) {
      data = data * -1;
    }

    return <>
      <i className={color}> {data.toFixed(0)}%</i>
    </>
  }

  componentDidMount() {

    var that = this;
    const user = getuser();
    this.setState({ ReportTime: new Date(), SelectedSupervisors: [user.UserID] });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);

  }

  fetchData() {
    let that = this;
    var SelectedSupervisors = this.state.SelectedSupervisors;
    this.props.GetCommonspData({
      root: "GetUserStats",
      params: [{ ManagerIds: SelectedSupervisors.join() }, { UserId: 0 }],
    }, function (data) {
      if (data && data.data && data.data.data) {
        let items = { UsersList: data.data.data[0], AvgGroupList: data.data.data[1] }
        that.setState({ items: items });
      }
    });

    if (that.state.SelectedRows.length > 0) {
      that.dtRef.current.handleClearRows();
    }

  }


  componentWillUnmount() {

  }


  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {

    }
  }


  render() {
    //const columns = this.columnlist;
    const columns = fnDatatableCol(this.columnlist);

    const { items, PageTitle } = this.state;
    console.log(items);
    const data = this.filterdata();
    let ddl = [];
    if (items && items.AvgGroupList) {
      items.AvgGroupList.forEach(item => {
        ddl.push(<option value={item.GroupId}>{item.GroupName}</option>)
      });
    }


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={5}>

                    </Col>
                    {/* <Col md={2}>
                      <div className="form-group">
                        <select className="form-control" onChange={this.CheckPerformance.bind(this)}>
                          <option value="-1">ALL</option>
                          <option value="80">Less than 80</option>
                          <option value="60">Less than 60</option>
                          <option value="50">Less than 50</option>
                          <option value="40">Less than 40</option>

                        </select>
                      </div>
                    </Col> */}
                    <Col md={2}>
                      <div className="form-group">
                        <select className="form-control" onChange={this.statuschange}>
                          <option value="-1">ALL</option>
                          {ddl}
                        </select>
                      </div>
                      <ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>
                    </Col>

                  </Row>

                </CardHeader>

                <CardBody>

                  <div className="statusdata">
                    <DataTableWithFilter
                      columns={columns}
                      data={data}
                      pagination={true}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense
                      ref={this.dtRef}
                    />

                  </div>
                </CardBody>

              </Card>
            </Col>

          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetComunicationData
  }
)(AgentDashboard);




