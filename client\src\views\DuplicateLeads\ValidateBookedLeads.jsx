import { <PERSON>, Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import {SendDuplicateOtp,VerifyDuplicateOtp} from "../../store/actions/CommonAction";
import { toast } from 'react-toastify';


const ValidateBookedLeads = (props) => {
    const [IsOptRequested,setIsOptRequested] = useState(false);
    const [OTP, setOTP] = useState('');
    const [OTPMessage, setOTPMessage] = useState('');
    const [seconds, setSeconds] = useState(1000);
    const [isResendEnabled, setIsResendEnabled] = useState(false);
    const [ID, setID] = useState(0)
  
    useEffect(() => {
      if (seconds > 0) {
        const interval = setInterval(() => {
          setSeconds((prevSeconds) => prevSeconds - 1);
        }, 1000);
        return () => clearInterval(interval); // Clear interval on component unmount
      } else {
        setIsResendEnabled(true); // Enable resend OTP button when time runs out
      }
    }, [seconds]);

    const handleResendOtp = () => {
      setSeconds(60); // Reset timer
      setIsResendEnabled(false); // Disable the resend button
      SendOpt(); // Call the resend OTP function
      let message = OTPMessage;
      setOTPMessage(message);
    };

    const SendOpt = () => {
        let IsNRICust = false;
        setOTP('');
        setID(0);
        setOTPMessage('');
        if(props.IsNRICust == "1")
            IsNRICust = true;
        var reqData =
        {
            "OtpType" : "1",
            "ProcessType" : "DuplicateLead",
            "CustId" : props.customerId,
            "IsNRICust" : IsNRICust
        };
        SendDuplicateOtp(reqData, function (resultData) {
            try {
              if (resultData != null) {
                if(resultData.data.data.Response.status){
                    setIsOptRequested(true);
                    setOTPMessage(resultData.data.data.Response.message);
                    setSeconds(60);
                    setID(resultData.data.data.ID);
                }
                else{
                    alert(resultData.data.data.Response.message)
                    props.onClose()
                }
              }
            } catch (e) {
              toast(`${e}`, { type: 'error' });
            }
        })
    }

    const VerifyOtp = () => {
        var reqData =
        {
          "OtpType" : "2",
          "CustId" : props.customerId,
            "OTP" : OTP,
            "ID" : ID
        };
        VerifyDuplicateOtp(reqData, function (resultData) {
            try {
              if (resultData != null) {
                if(resultData.data.data.Response.status){
                    props.duplicateLeads();
                }
                else{
                    alert(resultData.data.data.Response.message)
                }
              }
            } catch (e) {
              toast(`${e}`, { type: 'error' });
            }
        })
    }

    const handleOTPChange = (e) => {
        let OTP = e.target.value
        setOTP(OTP);
    }

    return (
        <div>
            <Dialog open={props.open} onClose={props.onClose} className="VerificationPopup">
                <DialogTitle>Verification Required</DialogTitle>
                {!IsOptRequested && <><DialogContent><p>A verification code is required to view customer details.This code will be sent to the customer's primary mobile number/Email ID.</p>
                    <Button className="sendVerificationButton" onClick={SendOpt}>Send Verification Code</Button>
                </DialogContent>
                <DialogActions>
                    <Button onClick={props.onClose} className="closeBtn">Close</Button>
                </DialogActions></>}

                {IsOptRequested && <><DialogContent><p>{OTPMessage}</p>

                    
                        <label>OTP</label>
                        <TextField
                            variant="outlined"
                            placeholder="Enter OTP"                         
                            className="OtpInput"
                            onChange={handleOTPChange}
                        />                        
                        {!isResendEnabled && <Typography variant="body2" className="resendOTP">
                            Resend OTP : <span>{seconds}</span> 
                        </Typography>}
                        {isResendEnabled && <Typography variant="body2" className="resendOtpButton">
                          <Button onClick={handleResendOtp}>Resend OTP</Button>
                        </Typography>
                        }
                                             
                    <Button className="sendVerificationButton" onClick={VerifyOtp}>Verify OTP</Button>
                </DialogContent>
                </>}

            </Dialog>
        </div>
    )
}

export default ValidateBookedLeads;