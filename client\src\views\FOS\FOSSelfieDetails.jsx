
import React from "react";
import { Col, Form, Row } from 'react-bootstrap';
import { getuser } from "utility/utility";
import { GetCommonspData } from "../../store/actions/CommonAction.jsx"
import { connect } from "react-redux";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import config from "../../config.jsx";
import { GetAwsRecordingUrl } from "store/actions/CommonAction";
import RadioItems from "views/Common/RadioItems";
import moment from "moment";

class FOSSelfieDetails extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            selfieImg: null,
            profileImg: null,
            status: null,
            CheckboxItem: [],
            checkedItems: {},
            selectedOptions: {},
            radioItems: [],
            CommentOnSelfie: '',
        }
    }

    componentDidMount() {
        this.getAWSImageUrl(this.props.URL, this.props.row.ProfileSelfieUrl);
        if (this.props.statusId != null) {
            this.setState({ status: this.props.statusId })
        }
        this.getSelfieResponse();
    }

    getSelfieResponse() {
        this.props.GetCommonspData({
            root: 'GetFOSSelfieData',
            params: [{ appointmentId: this.props.appointmentId }],
        }, function (result) {
            if (result && result.data && result.data.data && result.data.data[0]) {
                let checkboxarray = result.data.data[0];
                this.setState({ CommentOnSelfie: checkboxarray[0].Comment })
                //this.setState({ viewOnly : !!checkboxarray[0].Comment})

                checkboxarray.map(function (item) {
                    this.setState((prevState) => ({
                        checkedItems: { ...prevState.checkedItems, [item.QuestionId]: item.ResponseId }
                    }));

                }.bind(this))
            }
        }.bind(this));
    }

    getAWSImageUrl(url, profileSelfieUrl) {
        GetAwsRecordingUrl(url, config.FOSImageUploadBucket, function (results) {
            if (results.data.status == 200) {
                let url = results.data.data;
                this.setState({ selfieImg: url })
            }
        }.bind(this))
        if (profileSelfieUrl) {
            GetAwsRecordingUrl(profileSelfieUrl, config.FOSImageUploadBucket, function (results) {
                if (results.data.status == 200) {
                    let url = results.data.data;
                    this.setState({ profileImg: url })
                }
            }.bind(this))
        }
    }

    handlePass() {
        document.getElementById('failedList').style.display = 'none';
        this.setState({ status: 1 })
    }

    handleFail() {
        document.getElementById('failedList').style.display = 'block';
        this.setState({ status: 0 })
    }

    handleSubmit() {
        let status = ''
        let comment = this.state.comment;

        let selectedOptions = this.state.selectedOptions

        if (Object.keys(selectedOptions).length === 0) {
            toast('Please select Verification questions', { type: 'error' });
            return;
        }
        if (!selectedOptions.hasOwnProperty(2) || selectedOptions[2] == 'undefined') {
            toast('Please select Verification questions', { type: 'error' });
            return;
        }
        if (selectedOptions[2] === 1 && Object.keys(selectedOptions).length < 4) {
            toast('Please select Verification questions', { type: 'error' });
            return;
        }
        if (!comment) {
            toast('Please enter comment', { type: 'error' });
            return;
        }

        if (selectedOptions[2] === 2 || selectedOptions[2] === 3) {
            status = 0
            selectedOptions = { 2: selectedOptions[2] };
        } else if (selectedOptions[4] === 8 || selectedOptions[5] === 9) {
            status = 0
        } else {
            status = 1
        }
        let ResponseIds = (selectedOptions) ? Object.values(selectedOptions).join(',') : '';

        this.props.GetCommonspData({
            root: 'InsertSelfieData',
            params: [{ "appointmentId": this.props.appointmentId, "statusId": status, "comment": comment, "responseids": ResponseIds, "createdBy": getuser().UserID }],
            c: 'L'
        }, function (result) {
            if (result && result.data && result.data.status == 200) {
                toast('Your Response is successfully captured', { type: "success" })
            }
            this.props.onSubmitResponse();
            //this.getSelfieResponse();
        }.bind(this));
    }

    handleCheckboxValue(CheckboxItem) {
        this.setState({ CheckboxItem: CheckboxItem, checkedItems: {} });
    }

    handleRadioOptionValue(questionId, value) {
        this.setState(
            (prevState) => ({
                selectedOptions: {
                    ...prevState.selectedOptions,
                    [questionId]: value,
                },
            }),
            () => {
                console.log('State has been updated:', this.state.selectedOptions);
            }
        );

        this.setState({ radioItems: [] })
    }

    handleQuestionsDivClick(e) {
        if ((Object.keys(this.state.checkedItems).length === 0)) {
            const clickedClassName = e.currentTarget.className;
            let quesid = (clickedClassName) ? clickedClassName.split('_') : 0
            // Get all elements with class name starting with "question_"
            const questionElements = document.querySelectorAll('[class^="divquestion_"]');
            const dynamicName = (quesid && quesid.length > 0) ? 'question_' + quesid[1] : 0;
            const selectedOptionvalue = (document.querySelector(`input[name="${dynamicName}"]:checked`)) ? document.querySelector(`input[name="${dynamicName}"]:checked`).value : 0;

            // Loop through the elements and hide them except for "question_1"
            if (parseInt(selectedOptionvalue) === 2 || parseInt(selectedOptionvalue) === 3) {
                questionElements.forEach((element) => {
                    const className = element.className;
                    if (className !== 'divquestion_' + quesid[1]) {
                        element.style.display = 'none';
                    }
                });
            } else {
                questionElements.forEach((element) => {
                    element.style.display = 'block';
                });
            }
        }

    }

    handleCommentChange(e) {
        this.setState({ comment: e.target.value });
    }

    render() {
        const row = this.props.row
        return (
            <>
                <div className="SelfieVerificationPopup">
                    <Row>
                        <Col md={5}>
                            <div className="leftSide">
                                <h3>{this.props.UserName} ({this.props.EmployeeId})</h3>
                                <Row>
                                    <Col md={5} className="text-center">
                                        {(this.state.profileImg) ? <img alt="" src={this.state.profileImg} className="ProfilePic" /> : <div className="NoImage"><p>No Image Found</p></div>}

                                    </Col>
                                    <Col md={7}>
                                        <h4>Profile Picture</h4>
                                        <p>Last Updated On</p>
                                        <p><b>{(row && row.ProfileSelfieCreateTime) ? moment(new Date(row.ProfileSelfieCreateTime)).format("DD MMM YYYY") : 'N.A'}</b></p>
                                    </Col>
                                    <hr />

                                    <Col md={5} className="text-center">
                                        {(this.state.selfieImg) && <img alt="" src={this.state.selfieImg} className="ProfilePic" />}

                                        {(this.state.selfieImg == null) && <div className="spinner">
                                            <div className="bounce1"></div>
                                            <div className="bounce2"></div>
                                            <div className="bounce3"></div>
                                        </div>}
                                    </Col>
                                    <Col md={7}>

                                        <h4 className="Heading">Selfie Authentication</h4>
                                        <p className="details">Lead Id: <b>{this.props.LeadID} </b></p>
                                        <p className="details">Appointment Date and Time:<br /><b>{(row && row.Appointment_Datetime) ? moment.utc(new Date(row.Appointment_Datetime)).format("DD MMM YYYY") : 'N.A'}</b> <br />
                                            <b>{(row && row.Appointment_Datetime) ? moment.utc(new Date(row.Appointment_Datetime)).format("hh:mm A") : 'N.A'}</b></p>
                                        {(this.props.source && this.props.source === 'fostldash') && <p className="details">Appointment Started at:<br /><b>Date: {(row && row.Visit_Date_Time) ? moment.utc(new Date(row.Visit_Date_Time)).format("DD MMM YYYY") : 'N.A'}</b> <br />
                                            <b>Time: {(row && row.Visit_Date_Time) ? moment.utc(new Date(row.Visit_Date_Time)).format("hh:mm A") : 'N.A'}</b></p>}
                                        {(this.props.source && this.props.source === 'fosselfie') && <p className="details">Appointment Started at:<br /><b>Date: {(row && row.Visit_Date_Time) ? moment.utc(row.Visit_Date_Time, 'MMM D YYYY h:mma').format("DD MMM YYYY") : 'N.A'}</b> <br />
                                            <b>Time: {(row && row.Visit_Date_Time) ? moment.utc(row.Visit_Date_Time, 'MMM D YYYY h:mma').format("hh:mm A") : 'N.A'}</b></p>}
                                        <p className="details">Selfie Captured Distance<br /> (OTP Verified)<br /><b>{(row && row.Distance) || (row && row.Distance === 0) ? row.Distance + ' KM' : 'N.A'}</b></p>

                                    </Col>
                                    {(row.LastComment) && <Col md={12}>
                                        <div className="AgentComment">
                                            <h4>Appointment Completion Remarks</h4>
                                            <p title={row.LastComment? row.LastComment:""}>{row.LastComment? row.LastComment.substring(0, 200) :''} {(row.LastComment && row.LastComment.length > 201) ? '...' : ''}</p> </div>
                                    </Col>}
                                </Row>
                            </div>
                        </Col>
                        <Col md={7}>
                            <div className="rightSide">
                                <h5>Verification Questions</h5>
                                <Row key={`inline-radio`} className="text-center">
                                    <RadioItems root='GetSelfieQuestionsOptions' onDivClick={this.handleQuestionsDivClick.bind(this)}
                                        setRadioOptionValue={this.handleRadioOptionValue.bind(this)} checkedItems={this.state.checkedItems} status={this.props.row.Status}
                                        view={this.props.view} />
                                </Row>
                                <Row>
                                    <Col md={12}>
                                        <Form.Control
                                            as="textarea"
                                            placeholder="Review Comments"
                                            name="Comment"
                                            value={(this.state.CommentOnSelfie) ? this.state.CommentOnSelfie : this.state.comment}
                                            onChange={this.handleCommentChange.bind(this)}
                                            disabled={((this.props.view) || ([0, 1].includes(this.props.row.Status)))}
                                            maxLength={100}
                                        />
                                    </Col>
                                    {((!this.props.view) && !([0, 1].includes(this.props.row.Status))) && <button className="submitButton" disabled={(this.state.CheckboxItem.length !== 0)} onClick={() => this.handleSubmit()}>Submit</button>}
                                </Row>
                            </div>
                        </Col>
                    </Row>
                </div>
            </>)
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(FOSSelfieDetails);
