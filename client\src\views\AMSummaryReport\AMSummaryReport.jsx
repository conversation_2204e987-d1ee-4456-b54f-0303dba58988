import React from 'react';
import { useEffect, useState } from 'react';
import { Form, Container, Row, Col } from "react-bootstrap";
import ManagerHierarchy from "../Common/ManagerHierarchy_v2"
import { getuser, handleDownload } from '../../utility/utility';
import { NumberOfPages } from '../MyBookings/Utility'
import { GetCommonspData, GetCommonData, GetDataDirect } from '../../store/actions/CommonAction';
import { connect } from "react-redux";
import { Button } from 'react-bootstrap';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Spinner from "../FosTLSlotDistribute/Spinner";
import Pagination from "../MyBookings/Pagination";

const AMSummaryReport = (props) => {

    const [avgAgents, setAvgAgents] = useState(5);
    // const [refTime, setRefTime] = useState('30');
    const [selectedSupervisors, setSelectedSupervisors] = useState([]);
    const [EmployeeIds, setEmployeeIds] = useState([]);
    const [TWALoader, setTWALoader] = useState(false);
    const [AMLoader, setAMLoader] = useState(false);
    const [User, setUser] = useState({});
    const [TWData, setTWData] = useState([]);
    const [AMData, setAMData] = useState([]);
    const [showHierarchy, setShowHierarchy] = useState(true);
    const [avgAgentsChange, setAvgAgentsChange] = useState(5);
    const [RowsPerPage, setRowsPerPage] = useState(20);
    const [itemOffset, setItemOffset] = useState(0);
    const [SelectedPage, setSelectedPage] = useState(1);
    const [mode, setMode] = useState(1);
    const [amLevel, setAMLevel] = useState(false);

    const labeKeyMapping = {
        UserName: 'AM/ TL/ Agent Name',
        LoggedIn:  'LoggedIn Agents',
        // BucketSize: 'AVG Bucket Size',
        TotalAssignLeads: 'Assigned Leads',
        AppointmentsCreated: 'Appointment Booked',
        CustomerLocationCaptured: 'Appointment Address Captured',
        UniqueDialsPerAgent: 'Unique Dials/ Agent',
        TalkTimePerAgent: 'Talktime/Agent (Minutes)',
        MissedCall:  'Missed CallBacks',
        PendingCall:  'Pending CallBacks',
        // MonthTarget: 'Target',
        APEToday: 'Achieved',
        // ToGo: 'Balance',
        Bookings: 'Bookings',
        limitpayflag: 'Limited Pay Bookings',
        limitpayAPE: 'Limited Pay APE'
        


    }

    const endOffset = itemOffset + RowsPerPage;
    let currentItems = Array.isArray(AMData) && AMData.slice(itemOffset, endOffset);
    currentItems = currentItems && currentItems.length > 0 && currentItems.sort((a, b) => a.Id - b.Id);
    const pageCount = Array.isArray(AMData) && Math.ceil(AMData.length / RowsPerPage) || 0;
    let SERIAL = itemOffset + 1;
    
    let totalLogIns = [0, 0], totalAssignedLeads = 0, totalAppointmentsCreated = 0, totalCustomerLocationCaptured = 0, avgUniqueDialsPerAgent = 0, avgTalktimePerAgent = 0, totalMissedCallbacks = 0, totalPendingCallbacks = 0, totalAPEToday = 0, totalBookings = 0, totalLimitPayFlag = 0, totalLimitPayAPE = 0;
    if (Array.isArray(currentItems) && currentItems.length > 0) {
        currentItems.map((data) => {
            totalLogIns[0] += Number(data.LoggedIn.split("/")[0]);
            totalLogIns[1] += Number(data.LoggedIn.split("/")[1]);
            totalAssignedLeads += data.TotalAssignLeads;
            totalAppointmentsCreated += data.AppointmentsCreated;
            totalCustomerLocationCaptured += data.CustomerLocationCaptured;
            avgUniqueDialsPerAgent += data.UniqueDialsPerAgent;
            avgTalktimePerAgent += data.TalkTimePerAgent;
            totalMissedCallbacks += data.MissedCall;
            totalPendingCallbacks += data.PendingCall;
            totalAPEToday += data.APEToday;
            totalBookings += data.Bookings;
            totalLimitPayFlag += data.limitpayflag;
            totalLimitPayAPE += data.limitpayAPE;
        })
        avgUniqueDialsPerAgent = Math.round(avgUniqueDialsPerAgent / currentItems.length * 100) / 100;
        avgTalktimePerAgent = Math.round(avgTalktimePerAgent / currentItems.length * 100) / 100;
        totalAPEToday = Math.round(totalAPEToday * 100) / 100;
    }

    const getTWAchievement = (selectedSupervisors, avgAgentsChange) => {

        //if (avgAgentsChange && selectedSupervisors && selectedSupervisors.length > 0) {
        setTWALoader(true);
        const user = getuser();

        let MgrId = (selectedSupervisors && selectedSupervisors.length > 0) ? selectedSupervisors[0] : user.UserID;

        props.GetCommonspData({
            root: "GetTenureWiseAchievement",
            params: [{ MgrId: MgrId, TLlist: selectedSupervisors.join(), TopN: avgAgentsChange }],
            c: "L"

        }, (result) => {
            setTWALoader(false);
            if (result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0) {
                setTWData(result.data.data[0]);
            }
        })
        //}

    }

    const getAMSummary = (selectedSupervisors) => {

        //if (Array.isArray(selectedSupervisors) && selectedSupervisors.length > 0) {
        setAMLoader(true);
        const user = getuser();
        let MgrId = (selectedSupervisors && selectedSupervisors.length > 0) ? selectedSupervisors[0] : user.UserID;
        //let AMHierarchy = (selectedSupervisors && selectedSupervisors.length > 0) ? 1 : 0;
        props.GetCommonspData({
            root: "GetAMSummary",
            params: [{ MgrID: MgrId, TLlist: selectedSupervisors.join(), AMHierarchy: amLevel ? 1 : 0 }],
            c:"L"
            //params: [{ TLlist: selectedSupervisors.join()}]
        }
            , (result) => {
                // console.log("THe result is ", result);
                setAMLoader(false);
                if (Array.isArray(result?.data?.data[0]) && result.data.data[0].length > 0) {
                    setAMData(result.data.data[0]);
                    setItemOffset(0)
                }
            })
        //}

    }

    useEffect(() => {
        setTWData([]);
        getTWAchievement(selectedSupervisors, avgAgentsChange);
    }, [selectedSupervisors, avgAgentsChange])

    useEffect(() => {
        setAMData([]);
        getAMSummary(selectedSupervisors);
    }, [selectedSupervisors, amLevel])

    useEffect(() => {
        const user = getuser();
        // console.log("The user is ", user);
        //let arr = [];
        //arr.push(user.UserID);

        setUser(user);
        const params = new URLSearchParams(window.location.search);
        const myParam = params.get('mode');

        if (myParam) {
            setMode(0);
        }

        // setSelectedSupervisors(arr);

    }, [])


    useEffect(() => {
        const timer = setTimeout(() => {
            setAvgAgentsChange(avgAgents)
        }, 1000)

        return () => clearTimeout(timer)
    }, [avgAgents])

    const onRefresh = () => {
        if (selectedSupervisors.length < 1) {

            let arr = [];
            arr.push(User.UserID);

            setSelectedSupervisors(arr);
        }
        else {

            setTWData([]);
            setAMData([]);

            getTWAchievement(selectedSupervisors, avgAgentsChange);
            getAMSummary(selectedSupervisors);
        }
    }

    const numChange = (e) => {
        setAvgAgents(e.target.value);
    }

    const handleShow = (e) => {

        let arr = [];
        if (e.SelectedSupervisors.length < 1) {
            let arr = [];
            arr.push(User.UserID);
            setSelectedSupervisors(arr);
        }
        else {
            for (let i = 0; i < e.nodesData.length; i++) {
                arr.push(e.nodesData[i].EmployeeId)
            }
            setEmployeeIds(arr);
            setSelectedSupervisors(e.SelectedSupervisors);
        }
    }

    const handleRowsCountChange = (e) => {
        const element = document.getElementById(`tr-${e.target.value - 1}`);
        element && element.scrollIntoView();
        setRowsPerPage(parseInt(e.target.value));
    }

    const handlePageClick = (event) => {
        setSelectedPage(parseInt(event.selected));
        const newOffset = (event.selected * RowsPerPage) % AMData.length;
        setItemOffset(newOffset);
    };

    const newUrl = () => {
        let url = document.location.href + "?mode=1"
        window.open(url, "_blank");
    }

    const handleExport=()=>{
      
        let DownloadData=[];
        for(let i=0;i<AMData.length;i++)
        {
            DownloadData.push({});
        }

        // const start = performance.now();

        // console.time('Execution Time1');

        for(let i=0;i<DownloadData.length;i++)
        {
            
            Object.keys(labeKeyMapping).map((key)=>{
                if(key=='limitpayAPE')
                {
                    DownloadData[i][labeKeyMapping[key]] = AMData[i][key]!=null && !isNaN(AMData[i][key]) ? parseInt(AMData[i][key]).toLocaleString('en-IN'):'-';
                }
                else{
                DownloadData[i][labeKeyMapping[key]]= AMData[i][key];
                }
            })
        }
       
    
      
        // let index=0;
        // DownloadData.map((data)=>{
        //     Object.keys(labeKeyMapping).map((key)=>{
                
        //         data[labeKeyMapping[key]] =  AMData[index][key];
        //     }
        // );
        // index=index+1;
        // });
        
       

        handleDownload(DownloadData,"AMData");
    }

   


    return (


        <div className="content AMSummaryReport">
            <ToastContainer />
            {/* <h1> {mode != 1 && <img onClick={newURL} src="/fosTlDashboard/Maximize.jpeg" />}</h1> */}

            <h1 className='NewTab'>
                <Button variant='success' onClick={onRefresh} style={{ marginRight: '20px', marginBottom: '5px' }}>Refresh</Button>
                {mode == 1 ? <img onClick={newUrl} style={{ width: '30px' }} src="/fosTlDashboard/Maximize.jpeg" /> : null}</h1>
            {showHierarchy &&
                <ManagerHierarchy handleShow={handleShow} selectAll={false} value={/UserID/g} noCascade={true}></ManagerHierarchy>
            }

            <h3 className="tlName">
                Reporting AM/TL: {User.UserName} ({User.EmployeeId})
            </h3>

            <h1 className="t1heading">
                Tenure Wise Performance (Avg of Top <input type="number" className="textBox1" value={avgAgents} onChange={numChange}></input> Agents)
            </h1>

            <div className="t1">
                <table className="table">
                    <thead>
                        <tr>
                            <th>Tenure</th>
                            <th>GWP Achieved (Thousand)</th>
                            {/* <th>Assigned Leads</th> */}
                            <th>Bookings</th>
                            <th>Multiyear</th>
                            <th>Cancer Care</th>
                            <th>Limit Pay BKG</th>
                            <th>Limit Pay APE</th>
                        </tr>
                    </thead>
                    {TWALoader && <div className='Backdrop'><Spinner></Spinner></div>}
                    <tbody>
                        {
                            TWData && TWData.length > 0 &&
                            TWData.map((data) => (
                                <tr>
                                    <td>{data.Tenure}</td>
                                    <td>{data.APE}</td>
                                    {/* <td>{data.LeadCount || "-"}</td> */}
                                    <td>{data.Bookings}</td>
                                    <td>{data.Multiyear}</td>
                                    <td>{data.CancerCare}</td>
                                    <td>{data.limipayBKG}</td>
                                    <td>{data.limipayAPE}</td>
                                </tr>
                            ))
                        }

                    </tbody>
                </table>
            </div>
            <br />

            {/* AM/TL Summary (Refreshes in <input type="text" className='textBox2' value={refTime} onChange={(e)=>setRefTime(e.target.value)}></input> mins) */}




            {/* <div className="position-absolute top-10 end-0 ManagerHierarchy"> */}
            {/* <div className="ManagerHierarchy"> */}
            <Container>
                <Row style={{ textAlign: 'end' }}>
                    <Form.Group >
                        <Form.Check type={"checkbox"} style={{ width: '200px' }}>
                            <Form.Check.Input
                                style={{ visibility: 'visible', opacity: '1000' }}
                                type={"checkbox"}
                                checked={amLevel}
                                defaultChecked={false}
                                onClick={(e) => {
                                    setAMLevel(e.target.checked)
                                }}
                            />
                            <Form.Check.Label style={{ fontWeight: 'bold' }}>Show AM Level</Form.Check.Label>
                        </Form.Check>

                        {AMData && AMData.length > 0 && <Button onClick={() => { handleExport() }} style={{ marginLeft: '20px', marginBottom: '5px' }}>Export</Button>}
                    </Form.Group>

                    {/* <Button onClick={onRefresh} style={{marginBottom: '10px', fontSize:"10px"}}>Refresh</Button> */}
                </Row>
            </Container>

            <h1 className='t1heading'>
                AM/TL Summary
            </h1>

            <div className="t1">
                <table className="table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>AM/ TL/ Agent Name </th>
                            <th>LoggedIn Agents</th>
                            <th>Assigned Leads</th>
                            <th>Appointment Booked</th>
                            <th>Appt. Address Captured</th>
                            <th>Unique Dials/ Agent</th>
                            <th>Talktime/Agent (Minutes)</th>
                            <th>Missed CallBacks</th>
                            <th>Pending CallBacks</th>
                            <th>APE (Thousands)</th>
                            {/* <th>GWP(Thousand)
                                <table>
                               
                                    <tr>
                                        <th>Target</th>
                                        <th style={{ border: "1px solid #ddd" }}>Achieved</th>
                                        <th style={{ backgroundColor: "#FA1A1A", color: "white" }}>Balance</th>
                                    </tr>
                                  
                                </table>
                            </th>  */}
                            <th>Bookings</th>
                            <th>Limited Pay Bkgs</th>
                            <th>Limited Pay APE</th>
                        </tr>
                    </thead>
                    {AMLoader && <div className='Backdrop'><Spinner></Spinner></div>}
                    <tbody>
                        {
                            Array.isArray(currentItems) &&
                            currentItems.length > 0 &&
                            currentItems.map((data2, index) => (
                                <tr>
                                    <td>{index + 1}</td>
                                    <td>{data2.UserName}</td>
                                    <td>{data2.LoggedIn}</td>
                                    <td>{data2.TotalAssignLeads? data2.TotalAssignLeads : '-'}</td>
                                    <td>{data2.AppointmentsCreated? data2.AppointmentsCreated: '-'}</td>
                                    <td>{data2.CustomerLocationCaptured? data2.CustomerLocationCaptured: '-'}</td>
                                    {/* <td>{data2.BucketSize}</td> */}
                                    <td>{data2.UniqueDialsPerAgent}</td>
                                    <td>{data2.TalkTimePerAgent}</td>
                                    <td>{data2.MissedCall}</td>
                                    <td>{data2.PendingCall}</td>
                                    <td>{data2.APEToday}</td>
                                    {/* <table className="t2">
                                        <thead>
                                            <tr>
                                                <td>{data2.MonthTarget}</td>
                                                <td>{data2.APEToday}</td>
                                                <td>{data2.ToGo}</td>
                                            </tr>
                                        </thead>
                                    </table> */}
                                    <td>{data2.Bookings}</td>
                                    <td>{data2.limitpayflag!=null && !isNaN(data2.limitpayflag) ? data2.limitpayflag : '-'}</td>
                                    <td>{data2.limitpayAPE!=null && !isNaN(data2.limitpayAPE) ? parseInt(data2.limitpayAPE).toLocaleString('en-IN') : '-'}</td>
                                    
                                </tr>
                            ))
                        }
                        {
                            Array.isArray(currentItems) &&
                            currentItems.length > 0 &&
                            <tr className='total_row'>
                                <td></td>
                                <td>Total</td>
                                <td>{`${totalLogIns[0]}/${totalLogIns[1]}`}</td>
                                <td>{totalAssignedLeads}</td>
                                <td>{totalAppointmentsCreated}</td>
                                <td>{totalCustomerLocationCaptured}</td>
                                <td>{avgUniqueDialsPerAgent}</td>
                                <td>{avgTalktimePerAgent}</td>
                                <td>{totalMissedCallbacks}</td>
                                <td>{totalPendingCallbacks}</td>
                                <td>{totalAPEToday}</td>
                                <td>{totalBookings}</td>
                                <td>{totalLimitPayFlag}</td>
                                <td>{totalLimitPayAPE!=null && !isNaN(totalLimitPayAPE) ? parseInt(totalLimitPayAPE).toLocaleString('en-IN'):'-' }</td>
                            </tr>
                        }

                    </tbody>
                </table>

            </div>
            {
                pageCount > 0 &&
                <div id="bottomPagination" className="bottomPagination">
                    <Form.Select aria-label="Default select" width="20%" onChange={handleRowsCountChange} value={RowsPerPage}>
                        {NumberOfPages && NumberOfPages.map(item => {
                            return <option key={item.key} value={item.value}>{item.name}</option>
                        })}
                    </Form.Select>
                    <Pagination handlePageClick={handlePageClick} pageCount={pageCount} />
                </div>
            }
        </div>

    )

}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}
export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetDataDirect
    }
)(AMSummaryReport);