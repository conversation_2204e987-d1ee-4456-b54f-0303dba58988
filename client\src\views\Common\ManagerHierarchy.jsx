import React from 'react';

import {
    GetDataDirect
} from "../../store/actions/CommonAction";
import {  getUserDetails, getuser } from '../../utility/utility.jsx';
import Offcanvas from 'react-bootstrap/Offcanvas';
import { Row, Col, Button } from 'react-bootstrap';
import {
    CardTitle,
} from "reactstrap";
import 'react-checkbox-tree/lib/react-checkbox-tree.css';
import CheckboxTree from 'react-checkbox-tree';
import { useEffect } from 'react';
import { connect } from "react-redux";
import { useState, useRef } from 'react';
import config from "../../config.jsx";
import axios from 'axios';



const ManagerHierarchy=(props)=> {
const [nodes,setNodes]=useState([]);
// const [selectedNodes, setSelectedNodes]= useState([]);
const [checked, setChecked]= useState([]);
const [EmployeeIds,setEmployeeIds]= useState([]);
const [showManagerHierarchy, setShowManagerHierarchy]= useState(false);
const [IsLoading, setIsLoading]= useState(false);
const [expanded,setExpanded]= useState([]);


let selectedNodes=[];
let TLnodes=[];

const searchChildNodes=(node)=>{
  for(let i=0;i<node.length;i++)
  {
    TLnodes.push(node[i].value);
    if(node[i].hasOwnProperty('children'))
    {
      searchChildNodes(node[i].children);
    }
  }
  return;
}

useEffect(()=>{
 
  let UserId = parseInt(getUserDetails('RoleId')) === 2 ? 75 : parseInt(getUserDetails('UserId'));

  if(!UserId)
  {
    UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;
  }

  if(UserId)
  {  
  GetDataDirect({
      root: "Hierarchy",
      ManagerId: UserId,
      statename: "Hierarchy-" + UserId,
      Hierarchy : 1,
      state: true
  }, function (result) {
 
     let expired=false;
      if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
      {
      expired=true;
      }
      // result.pop();
      let str = JSON.stringify(result);
      // console.log("The str is ", str);
      var res = str.replace(/UserName/g, "label");
      res = res.replace(/UserID/g, "value");
      let nodes= JSON.parse(res);
      
      // for(let i=0;i<nodes.length;i++)
      // {
      //   TLnodes.push(nodes[i].value);
      //   if(nodes[i].hasOwnProperty("children"))
      //   {
      //        searchChildNodes(nodes[i].children)
      //   }
      // }
      
      
     
      setNodes(nodes);
      // handleShow({
      //   SelectedSupervisors: TLnodes,
      //   nodesData: selectedNodes,
      // });
      
      // if(selectAll===true){
       
      //   setChecked(TLnodes);
      // }
      // else{
        // setChecked([getuser().UserID]);
      // }
   
      if(expired)
      {
        axios
        .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
        // .then(function(response){
        //   if(response.data.status===200)
        //   {
        //      return;
        //   }
        // })
      }
  });
}
},[])



const getNodesData=(nodesData, SelectedSupervisors)=> {
    
    for (let i = 0; i < nodesData.length; i++) {
        let element = nodesData[i];
        if (SelectedSupervisors.indexOf(element.value) > -1) {
            selectedNodes.push(element);
        }
        if (element.children) {
            getNodesData(element.children, SelectedSupervisors)
        }
    }
}

const OnRefreshHierarchy=()=>{
  let UserId = parseInt(getUserDetails('RoleId')) === 2 ? 75 : parseInt(getUserDetails('UserId'));

  if(!UserId)
  {
    UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;
  }
  if(UserId)
  {
    GetDataDirect({
      root: "Hierarchy",
      ManagerId: UserId,
      statename: "Hierarchy-" + UserId,
      ClearLocalStorage: true,
      state: true,
      Hierarchy : 1
  }, function (result) {
 
     let expired=false;
      if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
      {
      expired=true;
      }
      // result.pop();
      let str = JSON.stringify(result);
      var res = str.replace(/UserName/g, "label");
      res = res.replace(/UserID/g, "value");
      let nodes= JSON.parse(res);
      
      // for(let i=0;i<nodes.length;i++)
      // {
      //   TLnodes.push(nodes[i].value);
      //   if(nodes[i].hasOwnProperty("children"))
      //   {
      //        searchChildNodes(nodes[i].children)
      //   }
      // }
      
     
      setNodes(nodes);
      // handleShow({
      //   SelectedSupervisors: TLnodes,
      //   nodesData: selectedNodes,
      // });
      
      // if(selectAll===true){
       
      //   setChecked(TLnodes);
      // }
      // else{
        // setChecked([getuser().UserID]);
      // }
   
      if(expired)
      {
        axios
        .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
        // .then(function(response){
        //   if(response.data.status===200)
        //   {
        //      return;
        //   }
        // })
      }
  });
  }
  
}
const handleShow=()=>{
    // this.selectedNodes = [];
    getNodesData(nodes, checked);
    let arr = [];
    for (let i = 0; i < selectedNodes.length; i++) {
      arr.push(selectedNodes[i].EmployeeId)
    }
    setEmployeeIds(arr);
    setShowManagerHierarchy(false);
    
    props.handleShow({
      SelectedSupervisors: checked,
      nodesData: selectedNodes
    });
    // this.forceUpdate();
}

const HandleOpenHierarchy=()=>{
    setShowManagerHierarchy(true);
}
const HandleCloseHierarchy=()=>{
    setShowManagerHierarchy(false);
}

const RemoveChecked=(checkeditem)=>{
    let checked1= checked;
    let index = checked1.indexOf(checkeditem);
    if (index > -1) {
      checked1.splice(index, 1);
    }
   setChecked(checked1);
}

return(
    <>
    {
        nodes.length!=0 &&
        <div className="ShowHistoryBTn">
        <Button variant="primary" className="HierarchyBtn" onClick={HandleOpenHierarchy}>
          {/* {Array.isArray(EmployeeIds) && EmployeeIds.length > 0 ? EmployeeIds.slice(0, 2).join() + '...' : 'Filter'} */}
         Filter
        </Button>
        <Offcanvas show={showManagerHierarchy} onHide={HandleCloseHierarchy} backdrop="static" placement="end">

          <Offcanvas.Header closeButton>
            <Offcanvas.Title>
              Select Supervisors
            </Offcanvas.Title>
            <input type="button" className="btn btn-primary " onClick={handleShow} value="Show" /> <i title="Refresh" className="fa fa-refresh refreshIcon" onClick={OnRefreshHierarchy}></i>
          </Offcanvas.Header>
          {IsLoading ?
            <h5 className='hierarchyLoading'>Loading...</h5> :
            <Offcanvas.Body className="hierarchyScrolling">
              <Row>
                <Col>
                  <div className="managers">
                    <CheckboxTree
                      nodes={nodes}
                      checked={checked}
                      expanded={expanded}
                      checkModel="all"
                      name="UserName"
                      showNodeIcon={false}
                      onCheck={(checked)=>setChecked(checked)}
                     onExpand={(expanded)=>setExpanded(expanded)}
                      showExpandAll={true}
                    />
                  </div>
                </Col>
              </Row>
              <br></br>
              <Col md={8}>
                <CardTitle  className= "managerEmployees" tag="h6">{EmployeeIds && Array.isArray(EmployeeIds) && EmployeeIds.join()}</CardTitle>
              </Col>
              </Offcanvas.Body>
          }
        </Offcanvas>
        </div>
    }
    {
        nodes.length==0 && null
    }
    </>
)

}

export default ManagerHierarchy;
