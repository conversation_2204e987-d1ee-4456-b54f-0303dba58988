import React from "react";
import ReactDOM from "react-dom";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
//import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import './customStyling.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";

import { If, Then, Else } from 'react-if';
//import ReactAudioPlayer from 'react-audio-player';
import ReactPlayer from 'react-player'


class CallGallery extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Call Gallery",
      //ProductId: getUrlParameter("productId"),
      CallData:[],
      ReportTime: null,
      IsLoading: true,
      IsPlaying: {},
      IsPaused: false,
      Players: {},
      PlayerInProgress: true,
      CurrentCallDetail: {},
      CurrentCallIndex: 0,
    };
  }

  handleAudio(e, callId, action, index, listenCounts) {
    var userid = getuser().UserID;

    var postData = [];
    //console.log(this.state.Players[index].getDuration(), this.state.Players[index].getCurrentTime(), this.state.Players[index].getSecondsLoaded() );
    if(action == 'Pause' || action == 'End') {
      postData = [{ UserId: userid, CallRecordingId: callId, ListenedDuration: this.state.Players[index].getCurrentTime() }];
    } else {
      postData = [{ UserId: userid, CallRecordingId: callId, ListenCount: 1 }];      
    }

    return this.props.GetCommonspData({
      root: "InsertUpdateCallRecordingLog",
      c: "L",
      params: postData
    }, function(result) {
      //console.log("Resultttttt", result.data.data[0]);
      //toast("Audio Log Saved", { type: 'success' });
      //this.setState({ CallData: result.data.data[0], IsLoading: false });
      this.setState({ PlayerInProgress: true });
      this.handleListingDataUpdate(callId, { ListenCounts : listenCounts + 1 });
    }.bind(this));
  }

  handleLike(e, recordingDetail, key, likes) {
    var userid = getuser().UserID;
    var islike = !recordingDetail.IsLiked ? 1 : 0;
    var postData = { UserId: userid, CallRecordingId: recordingDetail.Id, IsLiked: islike };

    if (recordingDetail.ListenedByCurrentUser && recordingDetail.ListenedByCurrentUser > 0) {
      postData.ListenCount = recordingDetail.CurrentUserListenCounts;
    }

    this.props.GetCommonspData({
      root: "InsertUpdateCallRecordingLog",
      c: "L",
      params: [postData]
    }, function(result) {
      //toast(islike == 0 ? "Audio Disliked" : "Audio Liked", { type: islike == 0 ? 'warning':'success' });
      let element = <i className="fa fa-thumbs-up icons clickable" title="Is this useful?"> { islike == 0 ? (recordingDetail.Likes - 1) : (recordingDetail.Likes + 1) }</i>;
      ReactDOM.render(element, document.getElementById('likeContainer_' + key));
      this.handleListingDataUpdate(recordingDetail.Id, { IsLiked : islike, Likes: islike == 0 ? (recordingDetail.Likes - 1) : (recordingDetail.Likes + 1) });
      //this.setState({ CallData: result.data.data[0], IsLoading: false });
    }.bind(this));
  }

  handleAudioEnd(e) {
    //alert('Audio file is ended');
  }
  
  handlePlayer(player, index) {
    this.state.Players[index] = player;
  }
  
  handleAudioSeeked(recordingDetail, index) {
    //console.log(index);
    //console.log(recordingDetail.StartTime);
    //console.log(this.state.IsPlaying);
    if (!this.state.IsPlaying[index]) {
      this.state.Players[index].seekTo((recordingDetail.StartTime * 60) - 2); // Seeking 2 seconds before actual time 
      this.state.IsPlaying[index] = true;
      //this.setState({ IsPlaying: { index : true } });
    } else {
      //if(progress.playedSeconds > (recordingDetail.StartTime * 60)) {
        //this.state.IsPlaying[index] = false;
        //this.setState({ IsPaused: true });
      //}
    }
  }

  createPlayer(e, item, key) {
    let element = <ReactPlayer
      url={item.Url}
      width="250px"
      height="35px"
      style={{width:"250px", background: "#0065FF", height: "35px"}}
      controls = {true}
      ref={player => this.handlePlayer(player, key)}
      id={ "player_" + key }
      className="react-player"
      //playing={this.state.IsPlaying[key]}
      onPause={(e) => this.handleAudio(e, item.Id, 'Pause', key, item.ListenCounts)}
      onStart={(e) => this.handleAudio(e, item.Id, 'Start', key, item.ListenCounts)}
      onEnded={(e) => this.handleAudio(e, item.Id, 'End', key, item.ListenCounts)}
      //onProgress={(progress) => this.handleAudioSeeked(progress, item, key)}
      onReady={(e) => this.handleAudioSeeked(item, key) }
      //onPlay={() => this.handleAudio(e, item.Id, 'Play')}
      //onSeeked={(e) => this.handleAudioSeeked(e, item.Id)}
    />;
    //let defaultElement = <i class="fa fa-play-circle listen" onClick={e => this.createPlayer(e, this.state.CurrentCallDetail, this.state.CurrentCallIndex)} ></i>;
    //ReactDOM.render(defaultElement, document.querySelector('#playerContainer_' + this.state.CurrentCallIndex));
    ReactDOM.render(element, document.getElementById('playerContainer_' + key));
    this.setState({ CurrentCallIndex: key, CurrentCallDetail: item });
  }

  componentWillReceiveProps(nextProps) {

    /*
    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetUserPODLeads"] || nextProps.CommonData["GetAgentPODBookings"] ) {
        this.setState({ UserPODLeads: CBList, IsLoading: false });
        //this.setState({ ReportTime: new Date() });
      }
    }
    */
  }
  
  componentDidMount() {
    this.fetchCallBackData(0);
  }

  fetchCallBackData(IsPODBooking) {  
    var userid = getuser().UserID;
    this.props.GetCommonspData({
      root: "GetCallRecordings",
      c: "L",
      params: [{ UserId: userid }]
    }, function(result) {
      //console.log("Resultttttt", result.data.data[0]);
      result.data.data[0].map((item, key) => 
        this.state.IsPlaying[key] = false
      );
      this.setState({ CallData: result.data.data[0], IsLoading: false });
    }.bind(this));
  }

  handleListingDataUpdate (Id, data) {
      console.log(data);
      this.setState({
        CallData: this.state.CallData.map(el => (el.Id === Id ? Object.assign({}, el, data) : el))
      });
  }  

  render() {
    const { PageTitle, CallData, showAlert, AlertMsg, AlertVarient, IsPlaying, IsPaused } = this.state;
    //console.log(CallData);
    //console.log(this.state.Players);
    return (
      <>
        <div className="content CallGallery">
          <ToastContainer />
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={9}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                <If condition={this.state.IsLoading}>
                <Then>
                  <i className="fa fa-spinner fa-spin"></i>
                </Then>
                <Else>
                  <Table responsive>
                    <tbody>
                      {
                        CallData && CallData.length > 0 && CallData.map((item, key) =>
                          (<tr>
                            <td>
                              <caption>
                                Agent ID
                              </caption>
                              {item.AgentCode}
                            </td>
                            <td>
                              <caption>
                                Agent Name
                              </caption>
                              {item.AgentName}
                            </td>
                            <td>
                              <caption>
                                Why Should you hear this call
                              </caption>
                              {item.ReasonOfListening}
                            </td>
                            <td>
                              <caption>
                                Calls
                              </caption>
                              <div className="playerContainer" id={"playerContainer_" + key} title="Click To Listen">
                                <i class="fa fa-play-circle listen" 
                                  onClick={e => this.createPlayer(e, item, key)}
                                ></i>
                                {/*<a href={item.CallRecordingUrl} className="button" title="Click To Listen" target="_blank">{item.AgentId == "PW8765" ? "Click To Listen Again" : "Click To Listen"}</a>*/}
                              </div>
                            </td>
                            <td>
                              <caption>
                                &nbsp;
                              </caption>
                              <span className={item.IsLiked ? "likedAudio" : "iconsDefault"} onClick={(e) => this.handleLike(e, item, key)} id={ "likeContainer_" + key }><i className="fa fa-thumbs-up icons clickable" title="Is this useful?"> {item.Likes || 0}</i></span>
                              &emsp;&emsp;
                              <span className={item.ListenCounts && item.ListenCounts > 0 ? "likedAudio" : "iconsDefault"}><i className="fa fa-play icons" title="Listen counts till now"> {item.ListenCounts || 0}</i></span>
                            </td>
                          </tr>)                          
                        )
                      }
                    </tbody>
                  </Table>
                </Else>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(CallGallery);