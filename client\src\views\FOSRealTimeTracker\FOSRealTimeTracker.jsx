import React, { useEffect, useState, useContext, createContext, useRef } from 'react';
import io from 'socket.io-client';
import { GetFOSAgentStatus, OTSData } from 'store/actions/CommonAction';
import { getUserDetails, calculateTimeTaken, OpenNewSalesView, LeadView, gaEventTracker } from '../../utility/utility';
import config from "../../config"
import TableView from "./Components/TableView";
import MapView from "./Components/MapView";
import '../../assets/scss/_tableview.scss';
import '../../assets/scss/_mapview.scss';
import FOSheader from "./Components/FOSheader";
import Drawer from '@mui/material/Drawer';
import { FosContext } from './FosContext';
import ManagerHierarchyMUI from './Components/ManagerHierarchyMUI';
import { GetFosCallingStatus } from 'store/actions/CommonAction';
import FosAppointmentData from 'views/FosAppointmentData';
import { LoadScript } from '@react-google-maps/api';


const FOSRealTimeTracker = () => {

    const [openMap, setOpenMap] = useState(false);

    const [fosData, setFosData] = useState(new Map());


    const [selectedCounter, setSelectedCounter] = useState(0);


    const [fosDataOnly, setFosDataOnly] = useState([]);
    const [callData, setCallData] = useState([]);

    const [timerTrigger, setTimerTrigger] = useState();


    const [socketMsg, setSocketMsg] = useState(null);

    const [currentSelectedNodes, setCurrentSelectedNodes] = useState([]);
    const [currentSelectedNodesEmpId, setCurrentSelectedNodesEmpId] = useState([]);


    const [notify, setNotify] = useState({
        alertMsg: "",
        open: false,
        severity: ""
    });

    const [fullView, setFullView] = useState(false);

    const [triggerComingFrom, setTriggerComingFrom] = useState(null);

    const [mode, setMode] = useState(1);

    const [untrackabilityTime, setUntrackabilityTime] = useState(0);

    const [otsAvgVal, setOtsAvgVal] = useState(null);

    const [hierarchyData, setHierarchyData] = useState(null);

    const [checked, setChecked] = useState([]);


    const employeeId = useRef(null);
    const userID = useRef(null);

    const user = useRef(null);

    const socket = useRef(null);

    const pongEvent = useRef(null);

    let reconnectInterval = 1000;

    const maxReconnectInterval = 30000;

    // socket.current = io(config.api.socket_url, {
    //     reconnectionAttempts: 8,
    //     reconnectionDelay: 1000,
    //     reconnectionDelayMax: 5000,
    //     randomizationFactor: 0.5,
    //     path: "/api/socketio"
    // }
    // );

    const showHideMapView = (condition) => {
        setOpenMap(condition);
    }

    const newUrl = () => {
        let url = document.location.href + "?mode=1"
        window.open(url, "_blank");
    }

    const traverseChildren = (nodesData, arr) => {
        for (let i = 0; i < nodesData.length; i++) {

            arr.push(parseInt(nodesData[i].value));

        }

        return arr;
    }

    const traverseChildrenEmpId = (nodesData, arr) => {
        for (let i = 0; i < nodesData.length; i++) {
            arr.push(nodesData[i].EmployeeId);
        }
        return arr;
    }

    const handleShow = (e) => {

        if (employeeId.current && userID.current && Array.isArray(e.nodesData) && e.nodesData.length) {
            let arr = [];
            for (let i = 0; i < e.nodesData.length; i++) {
                arr.push(e.nodesData[i].EmployeeId)
            }

            let nodesData = e.nodesData || [];

            let TlList = traverseChildren(nodesData, []);
            let TlListEmpId = traverseChildrenEmpId(nodesData, []);
            TlList.push(parseInt(userID.current));
            TlListEmpId.push(employeeId.current);

            setCurrentSelectedNodes(TlList);
            setCurrentSelectedNodesEmpId(TlListEmpId);
            setTriggerComingFrom("Hierarchy");
            setSelectedCounter(0);
  
            setChecked(e.CheckedUsers);

        }
    }

    const GetOTSData = (nodes, childNodes, levelWiseNodes, highestLevel) => {

        const _nodes = nodes != null ? nodes : (hierarchyData && hierarchyData.nodes);
        const _childNodes = childNodes != null ? childNodes : (hierarchyData && hierarchyData.childNodes);
        const _levelWiseNodes = levelWiseNodes != null ? levelWiseNodes : (hierarchyData && hierarchyData.levelWiseNodes);
        const _highestLevel = highestLevel != null ? highestLevel : (hierarchyData && hierarchyData.highestLevel);
        if (!(_nodes != null && _childNodes != null && _levelWiseNodes != null && _highestLevel != null)) {
            return
        }

        OTSData(_nodes, (data) => {

            for (let i = _highestLevel; i >= 1; i--) {

                _levelWiseNodes[i].forEach((val) => {
                    let parentNode = _childNodes.get(val);


                    if (data[val]) {
                        if (data[parentNode]) {
                            data[parentNode].TotalAppointments += data[val].TotalAppointments;
                            data[parentNode].ServingAppointments += data[val].ServingAppointments;
                        }
                        else {
                            data[parentNode] = {
                                TotalAppointments: data[val].TotalAppointments,
                                ServingAppointments: data[val].ServingAppointments
                            }
                        }
                    }
                })
            }

            // let OTSAvgVal = {};

            Object.keys(data).forEach((key) => {

                data[key].val = (data[key].ServingAppointments / data[key].TotalAppointments) * 100;

            })


            setOtsAvgVal(data);

        });


    }

    const FetchOtsData = (nodes, childNodes, levelWiseNodes, highestLevel) => {

        if (!hierarchyData) {
            setHierarchyData({
                nodes: nodes,
                childNodes: childNodes,
                levelWiseNodes: levelWiseNodes,
                highestLevel: highestLevel
            })
            GetOTSData(nodes, childNodes, levelWiseNodes, highestLevel);
        }


    }


    const handleChangeInCounter = (counter) => {
        setTriggerComingFrom("ChangeInCounter");
        setSelectedCounter(counter);

        let action = "";
        switch (counter) {
            case 0:
                action = "AllList"
                break;
            case 1:
                action = "TravellingList"
                break;
            case 2:
                action = "MeetingList"
                break;
            case 3:
                action = "CallingList"
                break;
            case 4:
                action = "IdleList"
                break;
            case 5:
                action = "LogOutList"
                break;
            default:
                break;
        }

        let details = {
            UserId: user.current,
            Time: new Date()
        }

        if (action) {
            gaEventTracker('FOSRealTimeTracker',action, details);
        }
    }

    const switchView = () => {
        let details = {
            UserId: user.current,
            Time: new Date()
        }


        if (openMap) {
            gaEventTracker('FOSRealTimeTracker',"MapViewClick", details);
        }
        else {
            gaEventTracker('FOSRealTimeTracker',"GridViewClick", details);
        }


        setOpenMap(!openMap);
    }


    const FullView = () => {

        setFullView(!fullView);

    }


    const openSalesview = (LeadId) => {

        if (LeadId) {

            let data = fosDataOnly.length > 0 && fosDataOnly.find((data) => data.ParentId == LeadId);
          

            let UserId = getUserDetails('UserId');

            if (data && data.ParentId && data.CustomerId && data.ProductId && UserId) {

                let url = LeadView(data.CustomerId, data.ParentId, data.ProductId, UserId)
                // let url= OpenNewSalesView(data.CustomerId, data.ParentId, data.ProductId);

                if (url.length > 0) {
                    window.open(url, '_blank');
                }

            }
        }

    }

    useEffect(() => {
        const interval = setInterval(() => {

            setTriggerComingFrom("Timer");
            if (currentSelectedNodes.length > 0) {

                GetFOSAgentStatus(currentSelectedNodes, (data) => {
                    setFosDataOnly([]);
                    if (Array.isArray(data) && data.length > 0) {
                        setFosDataOnly(data);
                    }

                });

            }

            if (currentSelectedNodesEmpId.length > 0) {

                GetFosCallingStatus(currentSelectedNodesEmpId, (data) => {
                    if (Array.isArray(data) && data.length > 0) {
                        setCallData(data);
                    }
                })
            }


        }, 10000);

        return () => clearInterval(interval);
    }, [currentSelectedNodesEmpId]);



    useEffect(() => {

        let mapData = new Map();

        let deepCopyFosDataOnly = JSON.parse(JSON.stringify(fosDataOnly));
        deepCopyFosDataOnly.map((dat) => {
            dat['Since'] = { time: 0, den: 'Min' }

            if (!dat.hasOwnProperty('WaitingAtCustomer')) {
                dat['WaitingAtCustomer'] = false;
            }

            if ([4, 5].includes(dat['RealTimeStatusId'])) {
                dat['Since'] = calculateTimeTaken(dat['UpdatedOn']);
                if (dat['Since']['time'] > 15) {
                    if (dat['WaitingAtCustomer'] && dat['distanceFromCustomer'] <= 2) {
                        dat['Since']['time'] = dat['Since']['time'] - 15;
                    }
                    dat['WaitingAtCustomer'] = false;
                }
                if (dat['WaitingAtCustomer'] && dat['distanceFromCustomer'] <= 2) {
                    dat['RealTimeStatusId'] = 6;
                }
            }
            else {
                dat['Since'] = calculateTimeTaken(dat['StatusChangedOn']);
            }

            if (dat['Since']['time'] > 300 && dat['RealTimeStatusId'] == 4) {
                dat['RealTimeStatusId'] = 5;
            }
            mapData.set(dat['EmployeeId'], dat);
        })

        setFosData(mapData);

        if (callData.length > 0) {

            callData.map((dat) => {



                if (['RINGING', 'BUSY'].includes(dat['Status']) && mapData.has(dat['AgentCode'])) {
                    let agentData = mapData.get(dat['AgentCode']);


                    if (parseInt(agentData['ParentId']) == parseInt(dat['LeadId'])) {

                        if ([4, 6].includes(agentData['RealTimeStatusId'])) {

                            agentData['RealTimeStatusId'] = 3;
                            agentData['Since'] = calculateTimeTaken(dat['LastUpdatedOn']);
                        }
                        else {

                            agentData['SecondaryStatus'] = true;
                            agentData['SecondaryLeadId'] = dat['LeadId'];
                        }
                    }
                    else {

                        if ([4, 6].includes(agentData['RealTimeStatusId'])) {

                            agentData["CustomerName"] = "";

                            agentData['RealTimeStatusId'] = 3;
                            agentData['ParentId'] = parseInt(dat['LeadId']);
                            agentData['TTAttempts'] = dat['TotalCalls'];
                            agentData['TTTalkTime'] = dat['TotalTalkTime'];
                            agentData['LastAttempt'] = new Date(dat['LastUpdatedOn']);
                            agentData['Since'] = calculateTimeTaken(dat['LastUpdatedOn']);
                        }
                        else {
                            agentData['SecondaryStatus'] = true;
                            agentData['SecondaryLeadId'] = dat['LeadId'];
                        }

                    }

                    setFosData(prevFosData => {
                        const tempMap = new Map(prevFosData);

                        tempMap.set(dat['AgentCode'], agentData);
                      
                        return tempMap;
                    });

                }
            })
        }

    }, [fosDataOnly, callData])

    useEffect(() => {

        if (currentSelectedNodes.length > 0) {

            GetFOSAgentStatus(currentSelectedNodes, (data, config) => {
                setFosDataOnly([]);
                if (Array.isArray(data) && data.length > 0) {
                    setFosDataOnly(data);
                }

                if (config && parseInt(config.UntrackabilityTime)) {

                    setUntrackabilityTime(parseInt(config.UntrackabilityTime));
                }

            });

        }

    }, [currentSelectedNodes])


    useEffect(() => {

        if (currentSelectedNodesEmpId.length > 0) {
            GetFosCallingStatus(currentSelectedNodesEmpId, (data) => {
                setCallData([]);
                if (Array.isArray(data) && data.length > 0) {
                    setCallData(data);
                }
            })

        }

    }, [currentSelectedNodesEmpId])


    useEffect(() => {
        const updatedMap = new Map(fosData);
        if (updatedMap.size > 0) {

            updatedMap.forEach((value) => {
                if (value?.Since?.time != null) {
                    value['Since']['time'] = value['Since']['time'] + 1;
                } else {
                    value['Since'] = {
                        time: 0,
                        den: 'Min'
                    };
                }
            });


            setFosData(updatedMap);
        }
    }, [timerTrigger])


    useEffect(() => {
        const interval = setInterval(() => {

            setTimerTrigger(prevState => !prevState);

        }, 60000);

        return () => clearInterval(interval);

    }, []);


    useEffect(() => {

        if (socketMsg) {

            if (socketMsg['EmployeeId']) {

                // data['Since']={time:1,den:'Min'};

                socketMsg['Since'] = calculateTimeTaken(socketMsg['StatusChangedOn']);

                // if([4,5].includes(socketMsg['RealTimeStatusId']))
                // {
                //     socketMsg['Since'] = calculateTimeTaken(socketMsg['UpdatedOn']);
                //     if(socketMsg['RealTimeStatusId']==4 && socketMsg['Since']['time']<15)
                //     {

                //         socketMsg['WaitingAtCustomer'] = true;

                //     }
                // }
                // else{
                //     socketMsg['Since'] = calculateTimeTaken(socketMsg['StatusChangedOn']);
                // }

                let fosCopyArray = JSON.parse(JSON.stringify(fosDataOnly));

                for (let i = 0; i < fosCopyArray.length; i++) {
                    if (fosCopyArray[i]['EmployeeId'] == socketMsg['EmployeeId']) {
                        fosCopyArray[i] = socketMsg;
                    }
                }

                setFosDataOnly(fosCopyArray);

                // setFosData(prevFosData => {
                //     const tempMap = new Map(prevFosData);
                //     tempMap.set(data['EmployeeId'], data);
                //     // console.log("The temp map is ", tempMap);
                //     return tempMap;
                // });

            }

        }



    }, [socketMsg])


    // let ConnectSocket=(userId)=>{

    //     socket.current = io(config.api.socket_url, {
    //         reconnectionAttempts: 8,
    //         reconnectionDelay: 1000,
    //         reconnectionDelayMax: 5000,
    //         randomizationFactor: 0.5,
    //         path: "/api/socketio"
    //     }
    //     );

    //     socket.current.on('connect', () => {
    //         console.log(socket.current.id, userId)
    //         socket.current.emit('new_user', userId);

    //         // console.log("Calling from FOS_RTT")
    //         GetFOSAgentStatus([parseInt(userId)], (data) => {
    //             setFosDataOnly([]);
    //             if (Array.isArray(data) && data.length > 0) {
    //                 setFosDataOnly(data);
    //             }
    //         });

    //     });

    //     socket.current.on('message', (data) => {
    //         setSocketMsg(data);
    //     });

    //     socket.current.emit('message', 'Hello from client');

    //     socket.current.on('disconnect', (reason) => {
    //         console.log("The reason is ", reason);
    //         // setTimeout(() => {
    //         //     reconnectInterval = Math.min(reconnectInterval * 2, maxReconnectInterval); // Exponential backoff
    //         //     connect();
    //         // }, reconnectInterval);
    //     })

    //     socket.current.io.on('reconnect_attempt', () => {

    //         // console.log('Attempting to reconnect...');
    //     });

    //     // socket.reconnect(
    //     // );
    //     // socket.on("reconnect",(reconnect)=>{
    //     //           console.log("The reconnect is ", reconnect);
    //     // })

    //     let interval= setInterval(()=>{

    //         socket.current.emit('ping');
    //         pongEvent.current= setTimeout(()=>{
    //             // window.alert("Please refresh the page for live data");
    //             setNotify({

    //                 open: true,
    //                 alertMsg: "Please refresh the page for live data",
    //                 severity: 'error'
    //             });
    //         },30000)

    //     }, 20000)

    //     socket.current.on("pong",()=>{
    //         console.log("Pong");
    //         if(pongEvent.current)
    //         {
    //             clearTimeout(pongEvent.current);
    //             pongEvent.current=null;
    //         }
    //     })

    //     return()  => {
    //         clearInterval(interval);

    //         if(pongEvent.current)
    //         {
    //             clearTimeout(pongEvent.current);
    //         }
    //         socket.current.close();
    //     };
    // }

    useEffect(() => {

        const params = new URLSearchParams(window.location.search);
        const myParam = params.get('mode');

        if (myParam) {
            setMode(0);
        }

        let userId = getUserDetails('UserId');
        let EmployeeId = getUserDetails('EmployeeId');

        user.current = parseInt(userId);

        employeeId.current = EmployeeId;
        userID.current = userId;

        setCurrentSelectedNodes([parseInt(userID.current)]);
        setCurrentSelectedNodesEmpId([employeeId.current]);

        let details = {
            UserId: user.current,
            Time: new Date()
        }

        gaEventTracker('FOSRealTimeTracker',"FOSRTTVisits", details);

        if (parseInt(userId)) {
            // ConnectSocket(parseInt(userId));
        }
    }, []);

    return (


        <FosContext.Provider value={{ fosData: fosData, selectCounter: handleChangeInCounter, selectedCounter: selectedCounter, openMap: openMap, fullView: fullView, triggerComingFrom: triggerComingFrom, mode: mode, newUrl: newUrl, openSalesview: openSalesview, untrackabilityTime: untrackabilityTime, otsAvgVal: otsAvgVal, GetOTSData: GetOTSData, checked:checked }}>
            <LoadScript googleMapsApiKey="AIzaSyC4Kr6Z9WObcn-aOaX3Rth26e9QjziZ_TM">


                <div className={!openMap ? 'fos-advisor-views' : 'fos-advisor-views map-view'}>

                    <ManagerHierarchyMUI module={null} handleShow={handleShow} FullView={FullView} page={openMap ? 'FosRealTimeTracker' : null} value={/UserID/g} noCascade={true} fullview={fullView} FetchOtsData={FetchOtsData} />


                    <FOSheader showHideMapView={showHideMapView} />

                    {fosData.size > 0 ? <div className='switch-view'>Switch to <span onClick={switchView}>{openMap ? 'table view' : 'map view'}</span></div> : null}

                    {openMap ?
                        <MapView /> : <TableView fosData={fosData} />}

                </div>

            </LoadScript>
        </FosContext.Provider>


    );
};

export default FOSRealTimeTracker;
