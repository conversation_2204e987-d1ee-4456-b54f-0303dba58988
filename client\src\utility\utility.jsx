
import React from "react";
import { Form } from 'react-bootstrap';
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'
import Moment from 'react-moment';
import moment from 'moment';
import {

    Col
} from "reactstrap";

import DropDownList from "views/Common/DropDownList.jsx"
import MultiSelectList from "views/Common/MultiSelectList.jsx";
import config from "../config.jsx";
import MultiSelectListMongo from "views/Common/MultiSelectListMongo.jsx";
//import CKTextEditor from "views/Common/CKTextEditor";
import DropDownAutoComplete from "views/Common/DropDownAutoComplete.jsx";
import  QuickSightEmbedding  from 'amazon-quicksight-embedding-sdk';
import * as XLSX from 'xlsx';


export function fnBindRootData(col, props) {
    if (col.type === "dropdown") {
        if(col.config.sp){
            props.GetCommonspData({
                root: col.config.root,
                params: col.config.params,
                con: col.config.con,
                state: true,
                statename: col.config.statename
            });   
        }
        else{
            props.GetCommonData({
                limit: 10,
                skip: 0,
                root: col.config.root,
                cols: col.config.cols,
                con: col.config.con,
                data: col.config.data,
                state: true,
                statename: col.config.statename
            });
        }
    }
    if(col.type === "autodropdown") {
        
        if(col.config.sp){
            props.GetCommonspData({
                root: col.config.root,
                params: col.config.params,
                state: true,
                statename: col.config.statename
            });   
        } 
        else {
            props.GetCommonData({
                limit: 10,
                skip: 0,
                root: col.config.root,
                cols: col.config.cols,
                data: col.config.data,
                state: true,
                statename: col.config.statename
            });
        }
    }
}

///type= bool,dropdown,autodropdown,textarea,multiselect,multiselectMongo,video,editor,int,number,decimal,hidden,datetime,time,
export function fnRenderfrmControl(col, formvalue, handleChange, event, errors, handleEditorChange = 0) {
    
    errors = errors || 0; 
    if (col) {
        // if (col.editable === false && (event === "Edit" || event === "Copy")) {
        //     return <Form.Group as={Col} md={3} controlId={col.name}>
        //         <Form.Label>{col.label}</Form.Label>
        //         <Form.Control disabled="disabled" type="text" placeholder={"Enter " + col.label} value={formvalue[col.name]} />
        //     </Form.Group>
        // }
        var name = col.alias ? col.alias : col.name;
        if(col.hideonmodal === true ){
            return <></>;
        }
        if(col.addable === false && event == 'Add'){
            return <></>;
        }
        if (col.type === "bool") {
            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label} &nbsp;</Form.Label>
                <input type="checkbox"
                    label=""
                    disabled={(col.editable === false && event === "Edit") ? "disabled" : ""}
                    onChange={handleChange}
                    checked={formvalue[name]}
                    name={name} id={name} />
            </Form.Group>
        }
        if (col.type === "dropdown") {
            let filterkey = null;
            let filtervalue = null;
            if (col && col.config && col.config.cols && col.config.cols.length > 2) {
                filterkey = col.config.cols[2];
                filtervalue = formvalue[filterkey];
            }
            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <DropDownList
                    disabled={((col.editable === false && event === "Edit") || (col.disabled)) ? "disabled" : ""}
                    visible={true} col={col}
                    value={formvalue[name]}
                    filterkey={filterkey}
                    filtervalue={filtervalue}
                    onChange={handleChange}
                    required= {(col.required == true)? true : false} 
                    firstoption={(col.config && col.config.firstoption)? col.config.firstoption : ''}
                    firstoptionvalue={(col.config && col.config.firstoptionvalue)? col.config.firstoptionvalue : ''}/>
            </Form.Group>

        }
        if (col.type === "autodropdown") {
            let filterkey = null;
            let filtervalue = null;
            if (col && col.config && col.config.cols && col.config.cols.length > 2) {
                filterkey = col.config.cols[2];
                filtervalue = formvalue[filterkey];
            }
            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <DropDownAutoComplete
                    disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
                    visible={true} col={col}
                    value={formvalue[name]}
                    filterkey={filterkey}
                    filtervalue={filtervalue}
                    onChange={handleChange}
                    required= {(col.required == true)? true : false} />
            </Form.Group>

        }
        if (col.type === "textarea") {
            return <Form.Group as={Col} md={6} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <Form.Control required
                    disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
                    type="text" onChange={handleChange} as="textarea" placeholder={"Enter " + col.label} 
                    value={formvalue[name]} />
            </Form.Group>
        }
        if (col.type === "multiselect") {
            let filterkeymulti = null;
            let filtervaluemulti = null;
            if (col && col.config && col.config.cols && col.config.cols.length > 2) {
                console.log(col.config.cols[0],col.config.cols[1],col.config.cols[2]);
                filterkeymulti = col.config.cols[2];
                filtervaluemulti = formvalue[filterkeymulti];
            }
            return <Form.Group as={Col} md={4} controlId={name} key={name}>
            <Form.Label id={"label-"+name}>{col.label}</Form.Label>
            <MultiSelectList
            disabled={((col.editable === false && event === "Edit") || (col.disabled)) ? "disabled" : ""}
            col={col}
            id={name}
            onChange={handleChange}
            filterkey={filterkeymulti}
            filtervalue={filtervaluemulti}
            value={formvalue[name]}
            className={name}
            />
             </Form.Group>
        }
        if (col.type === "multiselectMongo") {
            let filterkeymulti = null;
            let filtervaluemulti = null;
            if (col && col.config && col.config.cols && col.config.cols.length > 2) {debugger;
                filterkeymulti = col.config.cols[2];
                filtervaluemulti = formvalue[filterkeymulti];
            }
            return <Form.Group as={Col} md={3} controlId={name} key={name}>
            <Form.Label>{col.label}</Form.Label>
            <MultiSelectListMongo
            disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
            col={col}
            id={name}
            onChange={handleChange}
            filterkey={filterkeymulti}
            filtervalue={filtervaluemulti}
            value={formvalue[name]}
            />
                </Form.Group>
        }
        if (col.type === "video") {
            return <Form.Group as={Col} md={2} controlId={name} key={name}>
            <Form.Label>{col.label}</Form.Label>
            <input type="file" style={{width: "184px", height: "40px"}} id = {name} onChange={handleChange} disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""} 
            required={(col.editable === true && (event === "Edit")) ? "" : "required"}/>  
            <input type="hidden" id="MAX_FILE_SIZE" value="8388608" name="MAX_FILE_SIZE"/>
            <span style={{color: "red"}}>{errors.video}</span>
            
            </Form.Group>
        }
        if (col.type === "editor") {
            return <Form.Group as={Col} md={12} controlId={name} key={name}>
            <Form.Label>{col.label}</Form.Label>
            {/* <input type="file" style={{width: "184px", height: "40px"}} id = {name} onChange={handleChange} disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""} 
            required={(col.editable === true && (event === "Edit")) ? "" : "required"}/>  
            <input type="hidden" id="MAX_FILE_SIZE" value="8388608" name="MAX_FILE_SIZE"/>
            <span style={{color: "red"}}>{errors.video}</span> 
            <CKTextEditor onEditorTextChange={handleEditorChange}/>*/}
            <span style={{color: "red"}}>{errors.editor}</span>

            </Form.Group>
        }
        if (col.type === "int" || col.type === "number" || col.type === "decimal") {
            return <Form.Group as={Col} md={2} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <Form.Control required
                    disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
                    type="number" onChange={handleChange} placeholder={"Enter " + col.label} value={formvalue[name]} />
            </Form.Group>
        }
        if (col.type === "hidden" || col.hide) {
            return null;
        }
        if (col.type === "datetime") {
            let datestr = formvalue[name];
            if (datestr === "") {
                datestr = new Date();
            } else {
                datestr = new Date(datestr);
            }

            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <Datetime value={datestr}

                    id={name} dateFormat="YYYY-MM-DD"
                    timeFormat={(col.timeFormat === false)? col.timeFormat: true}
                    onChange={moment => handleChange(moment, name)}
                    utc={(col.utc && col.utc == 'no' && event === "Add")?false : true}
                    inputProps={{
                        id: name,
                        name: name,
                        required: true,
                        disabled: (col.editable === false && (event === "Edit")) ? "disabled" : ""
                    }} />

            </Form.Group>
        }
        if (col.type === "time") {
            let datestr = formvalue[name]
            if (datestr === "") {
                datestr = new Date();
            } else {
                datestr = new Date(datestr);
            }

            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                <Datetime

                    value={datestr} id={name} dateFormat={false} utc={true}
                    onChange={moment => handleChange(moment, name)} timeFormat={true}
                    inputProps={{
                        id: name,
                        name: name,
                        required: true,
                        disabled: (col.editable === false && (event === "Edit")) ? "disabled" : ""
                }} />
            </Form.Group>
        }
        if (col.type === "allocationTime") {

            return <Form.Group as={Col} md={3} controlId={name} key={name}>
                <Form.Label>{col.label}</Form.Label>
                    <Form.Control
                        type="time"
                        id={name}
                        value={formvalue[name] ? (formvalue[name]).substr(11,5) : ''}
                        onChange={(e)=>{handleChange({ target : { id: name, value : (e.target.value ? `1970-01-01T${e.target.value}:00.000Z` : "") }})}}
                        inputProps={{
                            id: name,
                            name: name,
                            required: true,
                            disabled: (col.editable === false && (event === "Edit")) ? "disabled" : ""
                        }}
                    />
            </Form.Group>
        }


        return <Form.Group as={Col} md={3} key={name} controlId={name}>
            <Form.Label>{col.label}</Form.Label>
            <Form.Control
                required={col.required} maxLength={(col.maxLength)?col.maxLength:''}
                disabled={((col.editable === false && event === "Edit") || (col.disabled)) ? "disabled" : ""}
                type="text" onChange={handleChange} placeholder={"Enter " + col.label} value={formvalue[name]} />
                <span style={{color: "red"}}>{(errors.Link && col.name=='Link')?errors.Link:''}</span>
        </Form.Group>
    }
    else return null;
}
export function getMax(arr, prop) {
    var max;
    for (var i = 0; i < arr.length; i++) {
        if (max === null || parseInt(arr[i][prop]) > parseInt(max[prop]))
            max = arr[i];
    }
    return max;
}




export function fnDatatableCol(columnlist) {

    var columns = []
    columnlist.forEach(col => {
        if (!col.hide) {
            if (col.type === "datetime") {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias : col.name,
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    type: col.type? col.type : "",
                    format: col.format? col.format : "",
                    width: col.width ? col.width : "150px",
                    cell: row => row[col.name] ? <Moment format="DD/MM/YYYY HH:mm:ss" utc={true}>{row[col.name]}</Moment> : "N.A",
                    filterType: col.filterType ? col.filterType : "text"
                })
            } else if (col.type === "time") {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias : col.name,
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    // cell: row => <Moment format="HH:mm:ss">{row[col.name]}</Moment>,
                    cell:row => col.cell ? col.cell(row) : (row[col.name] ? <Moment subtract={{hours: 5, minutes: 30}} format="HH:mm:ss">{row[col.name]}</Moment> : "N.A"),
                    filterType: col.filterType ? col.filterType : "text"
                })
            } else if (col.type === "bool") {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias : col.name,
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    cell: row => <span>{row[col.alias ? col.alias : col.name] ? 'Yes' : 'No'}</span>,
                    width: "150px",
                    filterType: col.filterType ? col.filterType : "text"
                });
            } else if (col.type === "dropdown") {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias + "_display" : col.name + "_display",
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    type: col.type === undefined ? false : col.type,
                    config: col.config === undefined ? false : col.config,
                })
            }
            else if (col.type === "autodropdown") {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias + "_display" : col.name + "_display",
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    type: col.type === undefined ? false : col.type,
                    config: col.config === undefined ? false : col.config,
                    filterType: col.filterType ? col.filterType : "text"
                })
            }
            else {
                columns.push({
                    name: col.label,
                    selector: col.alias ? col.alias : col.name,
                    sortable: col.sortable === undefined ? true : col.sortable,
                    searchable: col.searchable === undefined ? false : col.searchable,
                    cell: col.cell === undefined ? null : col.cell,
                    width: col.width ? col.width : "150px",
                    filterType: col.filterType ? col.filterType : "text"
                })
            }
        }
    });

    return columns;
}

export function fnCleanData(columnlist, formvalue, IsUpdate) {

    columnlist.forEach(col => {
        if (col.type === "datetime") {
            let datestr = formvalue[col.name] //? moment(formvalue[col.name]).format("YYYY-DD-MM HH:mm:ss") : "";
            if (datestr === "Invalid date") {
                delete formvalue[col.alias ? col.alias : col.name];
            }
            else {
                formvalue[col.alias ? col.alias : col.name] = datestr;
            }
        }
        if (col.type === "dropdown") {
            delete formvalue[col.alias ? col.alias + "_display" : col.name + "_display"];
        }
        if (col.type === "autodropdown") {
            delete formvalue[col.alias ? col.alias + "_display" : col.name + "_display"];
        }
        if (formvalue[col.alias ? col.alias : col.name] === "" ||
            formvalue[col.alias ? col.alias : col.name] === null ||
            formvalue[col.alias ? col.alias : col.name] === "null" ||
            formvalue[col.alias ? col.alias : col.name] === undefined ||
            formvalue[col.alias ? col.alias : col.name] === "undefined") {
            delete formvalue[col.alias ? col.alias : col.name];
        }
        if (IsUpdate && col.editable === false) {
            delete formvalue[col.alias ? col.alias : col.name];
        }
    });
    return formvalue;
}


export function GetJsonToArray(columnlist, name) {
    let cols = [];
    columnlist.forEach(col => {
        if (col.type === "number" || col.type === "decimal") {
            if (col.alias) {
                cols.push("ISNULL(" + col[name] + ",0) AS " + col.alias);
            }
            else {
                cols.push("ISNULL(" + col[name] + ",0) AS " + col[name]);
            }
        }

        else {
            if (col.alias) {
                cols.push("ISNULL(" + col[name] + ",'') AS " + col.alias);
            } else {
                cols.push("ISNULL(" + col[name] + ",'') AS " + col[name]);
            }
            //cols.push("ISNULL(NULLIF(" + col[name] + ", ''), " + col[name] + ") AS " + col[name] + "");
        }

    });
    return cols;
}

export function joinObject(json1, json2, jcolname) {

    json2.forEach(j => {
        json1.forEach(k => {
            if (j.Id === k[jcolname]) {
                k[jcolname + "_display"] = j.Display;
            }
            else {
                if (!k[jcolname + "_display"]) {
                    if (k[jcolname] != "") {

                        k[jcolname + "_display"] = k[jcolname];
                    }
                    else {
                        k[jcolname + "_display"] = "";
                    }
                }
            }
        });
    });

    return json1;
}

export function CompareJson(obj1, obj2) {
    var flag = true;

    obj1.forEach(k => {
        if (obj1[k] !== obj2[k]) {
            flag = false;
        }
    });


    return flag;
}



export function getUrlParameter(name, decode) {

    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(window.location.search);
    if (decode) {
        return results === null ? '' : results[1];
    }
    else
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

export function OpenSalesView(custid, leadid, productid) {
    let baseurl = config.api.SalesviewUrl;
    try {
        //baseurl = window.parent.location.href   
    }
    catch (e) {

    }

    return baseurl + window.btoa(custid + "/" + productid + "/" + leadid);
}


export function OpenNewSalesView(custid, leadid, productid) {
    //let baseurl = config.api.NewSalesviewUrl;
    let res;
    try {
        //baseurl = window.parent.location.href 
        let domain = getCookie("cdomain");
        let baseurl = document.referrer;
        if(domain){
            res = "https://" + domain + "/pgv/newsv/SalesView/" + window.btoa(custid + "/" + productid + "/" + leadid);
            return res;
        }
        if(baseurl != "" && !baseurl.includes('matrixdashboard')) {
            res = baseurl + "pgv/newsv/SalesView/"+ window.btoa(custid + "/" + productid + "/" + leadid);
        } else {
            res = config.api.newSalesviewUrl + "SalesView/" + window.btoa(custid + "/" + productid + "/" + leadid);
        }
    }
    catch (e) {
        console.error("Unable to open salesview", e)
    }
    return res;
}
export function LeadView(custid, leadid, productid, userId) {
    let res;
    try {
        let domain = getCookie("cdomain");
        let baseurl = document.referrer;
        if(domain){
            res = "https://" + domain + "/pgv/newsv/Leadview/" + window.btoa(custid + "/" + productid + "/" + leadid + "//" + userId);
            return res;
        }
        if(baseurl.match(/matrixdashboard/i)) {
            console.log('matrixdashboard match');
            baseurl = '';
        }
        if (baseurl != "" && !(baseurl.includes("bms"))) {
            res = baseurl + "pgv/newsv/Leadview/" + window.btoa(custid + "/" + productid + "/" + leadid + "//" + userId);
        } else {
            userId = getCookie("AgentId");
            res = config.api.newSalesviewUrl + "Leadview/" + window.btoa(custid + "/" + productid + "/" + leadid + "//" + userId);
        }
    }
    catch (e) {
        console.error("Unable to open salesview", e)
    }

    return res;
}

export function LeadContentView(custid, leadid, productid) {debugger;
    let res;
    try {
        let baseurl = document.referrer;
        if(baseurl.match(/matrixdashboard/i)) {
            console.log('matrixdashboard match');
            baseurl = '';
        }
        if (baseurl != "") {
            res = baseurl + "pgv/newsv/LeadContent/" + window.btoa(custid + "/" + productid + "/" + leadid);
        } else {
            res = config.api.newSalesviewUrl + "LeadContent/" + window.btoa(custid + "/" + productid + "/" + leadid);
        }
    }
    catch (e) {
        console.error("Unable to open salesview", e)
    }

    return res;
}

export function getBaseUrl() {
    let res;
    try {
        let domain = getCookie("cdomain");
        let baseurl = document.referrer + 'pgv/newsv/';
        if(domain){
            res = "https://" + domain + 'pgv/newsv/';
            return res;
        }
        if(baseurl.match(/matrixdashboard/i)) {
            console.log('matrixdashboard match');
            baseurl = '';
        }
        if (baseurl != "") {
            res = baseurl;
        } else {
            res = config.api.newSalesviewUrl;
        }
    }
    catch (e) {
        console.error("unable to get base url", e)
    }

    return res;
}


export function hhmmss(secs) {
    var minutes = Math.floor(secs / 60);
    secs = secs % 60;
    var hours = Math.floor(minutes / 60)
    minutes = minutes % 60;
    return `${("0" + hours).slice(-2)} h, ${("0" + minutes).slice(-2)} m, ${("0" + secs).slice(-2)} s`;
}

export function getuser() {

    try {

        let userid = getUrlParameter("u") === "" ? getCookie("AgentId") : getUrlParameter("u");
        let user = JSON.parse(localStorage.getItem("Users-" + userid))
        if (user && user.length > 0) {
            user[0].EmployeeId = user[0].EmployeeId.trim();
            return user[0];
        }
        else
            return {};
    } catch (error) {
        console.error(error);
        return {};
    }
}

export function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "domain=.policybazaar.com;expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    var expiresae = "domain=.policybazaar.ae;expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expiresae + ";path=/";
}

export function getCookie(cname) {
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) === 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

export function getDifferenceInMinFromNow(date){
    var now = moment().format("YYYY-MM-DD HH:mm:ss");
    var then = moment(new Date(date)).format("YYYY-MM-DD HH:mm:ss");
    var diff = (moment(now,"YYYY-MM-DD HH:mm:ss").diff(moment(then,"YYYY-MM-DD HH:mm:ss")))/ (1000 * 60);
    return diff;
}

export function IsMobile() {
    let check = false;
    (function (a) {
        if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4)))
            check = true;
    })(navigator.userAgent || navigator.vendor || window.opera);
    return check;
};

export function TimeConversion(time) {
    let myArray = time.split(":");
    let t = parseInt(myArray[0]);
    // console.log(t);
    if(t<12){
        return `${t}:${myArray[1]} AM`
    }
    else if(t == 12){
        return `${t}:${myArray[1]} PM`
    }
    else{
        return `${t-12}:${myArray[1]} PM`
    }
}

export async function embedSession (options){    
  const {
      createEmbeddingContext,
  } = QuickSightEmbedding;

  const embeddingContext = await createEmbeddingContext({
      onChange: (changeEvent, metadata) => {
          console.log('Context received a change', changeEvent, metadata);
      },
  });

  const frameOptions = options;

  const contentOptions = {
      onMessage: async (messageEvent, experienceMetadata) => {
          switch (messageEvent.eventName) {
              case 'ERROR_OCCURRED': {
                  console.log("Do something when the embedded experience fails loading.");
                  break;
              }
          }
      }
  };
  const embeddedConsoleExperience = await embeddingContext.embedDashboard(frameOptions, contentOptions);
};

export function getAgentId(){
    let userid = getUrlParameter("u") === "" ? getCookie("AgentId") : getUrlParameter("u");
    return userid;
}

export const getUserDetails=(key)=>{

    let userDetailsEncoded =  getCookie('User') || null;
    
    if(userDetailsEncoded && userDetailsEncoded.length>0)
    {
    let userDetails =  JSON.parse(window.atob(userDetailsEncoded));
    if(userDetails)
    {
      return userDetails[key];
    }
    }
   
    
    return null;
}

export const handleDownload=(data, name)=>{
    
    let sheet_Data= []
   
    sheet_Data = XLSX.utils.json_to_sheet(data) 
   
    
  
    var wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, sheet_Data, 'Sheet')
  
   
    if(name){
        XLSX.writeFile(wb, `${name}.xlsx`);
    }
    else {
        XLSX.writeFile(wb, "Download.xlsx"); 
    }
  
  }

  export  const calculateTimeTaken=(StatusChangedOn)=>{
    if (StatusChangedOn && !isNaN(new Date(StatusChangedOn)))
    {
        let difference = new Date() - new Date(StatusChangedOn);

        let timeToReturn= parseInt(difference/60000);
        let denoter="";
        if(timeToReturn>1)
        {
            denoter="Mins";
        }
        else{
            denoter="Min";
        }
        return {time:timeToReturn, den:denoter}
        
    }

    return {time:1, den:"Min"};
    
}

export  const ConvertToTimeTaken = (timeTaken) => {
    try{
      if (timeTaken) {
          let time = parseInt(timeTaken);
          if (time > 0 && time <= 1) {
              return '1 Minute';
          }
          else if (time < 60) {
              return `${timeTaken} Minutes`;
          }
          else {
              let hr = parseInt(timeTaken / 60);
              let min = timeTaken % 60;
              if (hr <= 1) {
                  if(min==0)
                  {
                      return `${hr} Hr`
                  }
                  else if (min<=1) {
                       
                      return `${hr} Hr ${min} Minute`
                  }
                  else{
                      return `${hr} Hr ${min} Minutes`
                  }
              }
              else {
                  if (min <= 1) {
                      return `${hr} Hrs ${min} Minute`
                  }
                  else {
                      return `${hr} Hrs ${min} Minutes`
                  }
              }
          }
      }
      else {
          return "";
      }
    }
    catch(e)
    {
        return ""
    }
  }

  export const ConvertToMinutes=(timeTaken)=>{
    try{
         let time= parseInt(timeTaken);
         time = parseInt(time/60);
         if(time>1)
         {
            return `${time} Minutes`;
         }
         else{
            return `${timeTaken} Seconds`;
         }
    }
    catch(e)
    {
        return "-";
    }
  }

  export const fetchDistance=(overallDistance)=>{
    if(overallDistance)
    {
        // return `${parseFloat(overallDistance)} KMs`
        return `${Math.round((parseFloat(overallDistance) + Number.EPSILON) * 100) / 100} KMs`;
    }
    if(String(overallDistance)=='0')
    {
        return '0 KM';
    }
   return '-';
    
  }


  export const sortByKey=(List, Key)=>{
    
    for(let i=0;i<List.length;i++)
    {
        for(let j=i+1;j<List.length;j++)
        {
            if(List[i][Key]>List[j][Key])
            {
                let temp=List[i];
                List[i] = List[j];
                List[j]= temp
            }
            else{
                continue;
            }
        }
    }

}


export const gaEventTracker = async (eventCat,eventAction, details) => {
    try {
      let modifiedEventAction = eventAction;
  
    //   if (_isAgent) {
    //     modifiedEventAction = `Agent: ${eventAction}`;
    //   }
      const data = {
        eventCategory: eventCat,
        eventAction: modifiedEventAction,
        eventLabel: JSON.stringify(details),
        eventValue: 1,
      };
  
      // Push the data to the Google Analytics data layer
      window.dataLayer = window.dataLayer || [];
  
      if (!window.gtag) {
        window.gtag = (...args) => { window.dataLayer.push(...args); };
      }
  
      window.gtag('event', 'eventTracking', data);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Google Analytics Error:', error);
    }
  };
  
