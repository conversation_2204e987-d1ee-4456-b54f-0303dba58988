import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, joinObject } from '../utility/utility.jsx';
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DropDown from './Common/DropDown';
import RealTimePanel from './RealTimePanel/RealTimePanel';
import RealTimePanelQueuewise from './RealTimePanel/RealTimePanelQueuewiseTwo';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


// reactstrap components
import {
    <PERSON>,
    <PERSON><PERSON>ead<PERSON>,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class RealTimeDashboard extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            selectedIvrProduct: "",
            ivrType: "0",
            ivrProducts: [],
            ivrQueues: [],
            selectedqueues: [],
            addClass: '',
            UserId: '',
            RealTimeUrl: '',
            selectedValue: [{ label: "Select IVR Queues", value: "0" }],
        };
        this.ivrtypechange = this.ivrtypechange.bind(this);
        this.ivrproductchange = this.ivrproductchange.bind(this);
        this.onSelect = this.onSelect.bind(this);

        this.IvrTypeList = {
            config:
            {
                root: "Ivrtypelist",
                data: [
                    { Id: "Service", Display: "Service" }, 
                    { Id: "Sales", Display: "Sales" }, 
                    { Id: "Claim", Display: "Claim" }, 
                    { Id: "pbpartners", Display: "pbpartners" },
                    { Id: "smeclaim", Display: "SME Claim" },
                ],
            }
        };


    }

    componentDidMount() {
        const user = getuser();
        let userid = user.UserID;
        this.setState({ UserId: userid });
    }

    fnBindStore(col, nextProps) {
        //debugger;
        if (col.type == "dropdown") {
            let items;
            if (nextProps.CommonData[this.state.root] && (nextProps.CommonData[col.config.root] || col.config.data)) {
                items = joinObject(nextProps.CommonData[this.state.root], (nextProps.CommonData[col.config.root] || col.config.data), col.name)
                this.setState({ items: items });
            }
        }
    }


    showRealTimeData() {
        this.setState({ addClass: 'disabled' });
        if (this.state.ivrType == 0) {
            toast("Please enter ivrType", { type: 'error' });
            this.setState({ addClass: '' });
            return;
        }
        if (this.state.selectedIvrProduct == '') {
            toast("Please enter IvrProduct", { type: 'error' });
            this.setState({ addClass: '' });
            return;
        }
        let queues = this.state.selectedValue;
        var queuestring = queues.map(function (val) {
            return val.label;
        });
        var selectedqueues = queuestring.join("','");

        let url = "RealTimePanelQueuewiseTwo?type=" + this.state.ivrType + "&product=" + this.state.selectedIvrProduct + "&queues=" + selectedqueues + "&u=" + this.state.UserId;

        this.setState({ RealTimeUrl: url, addClass: '', selectedqueues: selectedqueues });

        // setTimeout(function () {
        //     var winactive = 1;
        //     debugger
        //     var wn = document.getElementById('myframe').contentWindow;
        //     wn.postMessage({ winactive: 1, type: "checkactive" }, "*");
        //     window.onfocus = function () {
        //         //do something
        //         winactive = 1;
        //         console.log("winactive", winactive);
        //         wn.postMessage({ winactive: winactive, type: "checkactive" }, "*");
        //     }

        //     window.onblur = function () {
        //         //do something            
        //         winactive = 0;
        //         console.log("winactive", winactive);
        //         wn.postMessage({ winactive: winactive, type: "checkactive" }, "*");
        //     }
        // }, 500)
    }

    fetchProductData() {

        if(this.state.ivrType == "0"){
            return;
        }
        this.props.GetMySqlData({
            root: "getProductByIvrType",
            ProductType: this.state.ivrType,
        }, function (result) {
            this.setState({ ivrProducts: result?.data?.data });
        }.bind(this));

    }

    fetchQueueData() {

        //debugger;
        if(this.state.selectedIvrProduct == "0"){
            return;
        }
        this.props.GetMySqlData({
              root: "getQueuesByIvrProduct",
              ProductType: this.state.ivrType,
              ProductId: this.state.selectedIvrProduct,
            }, function (result) {
              this.setState({
                ivrQueues: result && result.data && result.data.data,
              });
            }.bind(this)
        );
    }


    ivrtypechange(e, props) {
        this.setState({
            ivrType: e.target.value,
            selectedValue: [{ label: "Select IVR Queues", value: "0" }],
            ivrProducts: [{ Display: "Select IVR Product", Id: "0" }]
        }, function () {
            this.fetchProductData();
        });

    }

    ivrproductchange(e, props) {
        this.setState({
            selectedIvrProduct: e.target.value,
            ivrQueues: [],
            selectedValue: [{ label: "Select IVR Queues", value: "0" }]
        }, function () {
            this.fetchQueueData();
        });
    }

    onSelect(selectedList, selectedItem) {
        // debugger;
        console.log("selectlist", selectedList);
        const newselectedList = selectedList.filter(task => {
            return (
                task.label !== 'Select IVR Queues'
            )
        });
        this.setState({ selectedValue: newselectedList });
    }

    renderRealTimePanelQueuewise() {



        if (this.state.RealTimeUrl) {
            return <RealTimePanelQueuewise type={this.state.ivrType} product={this.state.selectedIvrProduct} queues={this.state.selectedqueues} u={this.state.UserId}></RealTimePanelQueuewise>
        }
    }

    render() {
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>


                                    <Row>
                                        <Col md={3}>
                                            <Form.Group controlId="ivrtype_dropdown" >
                                                <Form.Label><i>*</i> Ivr Type </Form.Label>
                                                <DropDownListMysql firstoption="Select Ivr Type" value={this.state.ivrType} col={this.IvrTypeList} onChange={this.ivrtypechange}>
                                                </DropDownListMysql>
                                            </Form.Group>
                                        </Col>
                                        <Col md={3}>
                                            <Form.Group controlId="product_dropdown" >
                                                <Form.Label><i>*</i> Ivr Product </Form.Label>
                                                <DropDown firstoption="Select Ivr Product" items={this.state.ivrProducts} onChange={this.ivrproductchange}>
                                                </DropDown>
                                            </Form.Group>

                                        </Col>
                                        <Col md={3}>
                                            <Form.Group controlId="queues_dropdown">
                                                <Form.Label>Ivr Queues (Optional)</Form.Label>
                                                <MultiSelect
                                                    options={this.state.ivrQueues} // Options to display in the dropdown
                                                    value={this.state.selectedValue} // Preselected value to persist in dropdown
                                                    onChange={this.onSelect} // Function will trigger on select event                                                
                                                    //labelledBy={"Select IVR Queues"}
                                                    selectAllLabel={"Select ALL IVR Queues"}
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col md={3}> 
                                        <Form.Label>Show Panel</Form.Label>                                           
                                        <Form.Group controlId="show_panel">
                                            <Button className={this.state.addClass} variant="primary" onClick={() => this.showRealTimeData()}>Show</Button>
                                        </Form.Group>
                                        </Col>

                                    </Row>

                                </CardHeader>
                                {/* <CardBody>
                                
                                </CardBody> */}
                            </Card>
                        </Col>
                    </Row>
                    {this.renderRealTimePanelQueuewise()}
                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData,
    }
)(RealTimeDashboard);