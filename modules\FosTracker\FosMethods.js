const { Kafka } = require('kafkajs');
const cache = require('memory-cache');
const { ReturnSocketList } = require('./MongoSocketMaintenance');

let consumeFosData = async (consumer) => {

    try {
      const topic = "fos-appointmentData-topic";
      await consumer.subscribe({ topic: topic });
      
    //   let SocketList =await ReturnSocketList(msg["TlId"]);
    //   console.log("The socketList is ", SocketList);
    //   if(global.io && SocketList.length>0)
    //   {
    //       global.io.to(SocketList).emit("Message",msg);
    //   }
      await consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          let msg = JSON.parse(message.value.toString());
          // console.log(msg
          // );
          let SocketList = await ReturnSocketList(msg['ManagerId']);
          // console.log("the ScoketList is ", SocketList);

          if(global.io && SocketList.length>0)
            {
                SocketList.forEach((socketId)=>{
                  global.io.to(socketId).emit("message",msg);
                })
            }
        }
      });
    }
    catch (e) {
      console.log("Error in consumeFosData: ", e);
    }
  

}

module.exports = {
  consumeFosData: consumeFosData
}