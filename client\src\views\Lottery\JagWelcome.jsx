import React, { useEffect, useState } from "react";
import './StarsAward.scss';
import _ from 'underscore';
import LotteryJag from "./LotteryJag";
import LotteryEmerging from "./LotteryEmerging";

const JagWelcome = () => {
    const [PageId, setPageId] = useState(1);

    const handleNavigateJagPage = (e) => {
        setPageId(2);
    }

    const handleNavigateEmergingStarPage = (e) => {
        setPageId(3);
    }

    const handleBacktoHome = () => {
        setPageId(1);
    }

    return (
        <>

            <div class="confetti-bg"></div>

            {
                (PageId === 1) && <div className="EmergingStarsLayout">
                    <div className="BoxLayout">
                        <div className="Header">
                            <img src="/lottery/Award/jagLogo.png" />
                            <div className="star">
                                <img src="/lottery/Award/star.png" />
                            </div>
                            <img src="/lottery/Award/pbLogo.svg" width="180px" />
                        </div>
                        <div className="WelcomeLayout">
                            <img src="/lottery/Award/Welcome.png" width="196px" />
                            <h2>The night when our stars shine the brightest</h2>
                            <h1> <img src="/lottery/Award/StarWhite.png" />  2024-25 <img src="/lottery/Award/StarWhite.png" /> </h1>
                            <div className="LottryTicketImage">
                                <img src="/lottery/Award/Light-effect.png" className="lightEffect" />
                                <img src="/lottery/Award/lotteryTicket.png" className="ticketImg" />

                                {/* <div className="BtnDesign">
                                 <button onClick={(e) => handleNavigateRoute(e, '/gaming/LotteryJag')} >JAG LOTTERT</button>
                                 <button onClick={(e) => handleNavigateRoute(e, '/gaming/LotteryEmerging')}>ES LOTTERT</button>
                             </div> */}

                            </div>
                        </div>

                        <img onClick={handleNavigateJagPage} src="/lottery/Award/house-mobile.png" className="House-footerBg" />
                        <img onClick={handleNavigateEmergingStarPage} src="/lottery/Award/car-mobile.png" className="Car-footerBg" />
                    </div>
                </div>

            }
            {(PageId === 2) && <LotteryJag handleBacktoHome={handleBacktoHome} />}
            {(PageId === 3) && <LotteryEmerging handleBacktoHome={handleBacktoHome} />}

        </>


    );
}

export default JagWelcome;

