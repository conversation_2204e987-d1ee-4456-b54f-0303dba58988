const message = {
    status:  [
        {
            status: 500,
            message: "Could Not Initiate the call.",
        },
        {
            status: 502,
            message: "Could Not Initiate the call.",
        },
        {
            status: 403,
            message: "Bad request, Required Parameter missing.",
        },
        {
            status: 404,
            message: "Connect with customer first.",
        },
        {
            status: 405,
            message: "Requested Channel / Agent is not on call.",
        },
        {
            status: 200,
            message: "Success, Call is being transferred",
        },
        {
            status: 101,
            message: "Customer has disconnected the call.",
        },
        {
            status: 102,
            message: "Third Party Agent is not on call, Transfer first.",
        }
    ]
  
  };

  export default message;
  