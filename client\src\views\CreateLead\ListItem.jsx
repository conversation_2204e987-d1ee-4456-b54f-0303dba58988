
import React from "react";
import { Form } from 'react-bootstrap';

class ListItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      items: [],
    }
  }
  render() {
    return (
      <div className="ContainerBox">
        <input
          type="checkbox"
          checked={this.props.selected}
          onChange={this.props.handleOnChange}
          id={this.props.id}
          disabled={this.props.disabled}
        />
        <p>
          <h4> {this.props.text.optionHeading} </h4> {this.props.text.optiondescription}
      </p>
        
        </div >
    );
  }
}


export default ListItem;