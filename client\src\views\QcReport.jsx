
// import React, { Suspense } from 'react';


// import {
//     GetCommonData, GetCommonspData, GetDataDirect, GetExportQuiz, GetQualityReport
// } from "../store/actions/CommonAction";
// import { getUrlParameter, getuser } from '../utility/utility.jsx';
// import {
//     Card,
//     CardHeader,
//     CardBody,
//     CardTitle,
//     Table,
//     Row,
//     Col
// } from "reactstrap";
// import moment from 'moment';
// //import SimpleTreeTable from 'simple-react-treetable';
// import { func } from "prop-types";
// import _ from 'underscore';


// const SimpleTreeTable = React.lazy(() => import('simple-react-treetable'));

// const columns = [
//     {
//         dataField: "UserName",
//         heading: "UserName",
//         fixedWidth: true,
//         percentageWidth: 25
//     },
//     {
//         dataField: "TotalCount",
//         heading: "Total Count",
//     },
//     {
//         dataField: "TotalPending",
//         heading: "Total Pending",
//     },
//     {
//         dataField: "TLPending",
//         heading: "TL Pending",
//     },
//     {
//         dataField: "PendingWithinTAT",
//         heading: "Pending Within TAT",
//     },
//     {
//         dataField: "PendingOutsideTAT",
//         heading: "Pending outside TAT",
//     },
//     {
//         dataField: "TotalClosureP",
//         heading: "Total Closure %",
//     },
//     {
//         dataField: "TotalClosureWithinTATP",
//         heading: "Total Closure% within TAT",
//     },
// ];

// const control = {
//     tableClasses: "table table-striped table-responsive",
//     buttonClasses: "btn btn-default",
//     showButton: false
// };

// class QcReport extends React.Component {
//     constructor(props) {
//         super(props);
//         this.state = {
//             nodes: [],
//             ResponseData: [],
//         };

//     }
//     componentDidMount() {

//         console.log("getuser", getuser());
//         let UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;
//         //let UserId = getuser().RoleId == 2 ? 2640 : getuser().UserID;
//         let RoleId = getuser().RoleId;
//         let EmployeeId = getuser().RoleId == 2 ? 'ET01104' : getuser().EmployeeId;

//         //const encodedString = btoa((getuser().RoleId == 2 ? 'ET01104' : getuser().EmployeeId) + '-' + getuser().RoleId);
//         const encodedString = btoa(EmployeeId + '-' + RoleId);
//         console.log(encodedString);
//         //GetQualityReport("UFcwMTA4OC0xMQ==", function (results) {
//         GetQualityReport(encodedString, function (results) {


//             let aiagentdata = null;
//             if (results.data.status == true) {
//                 aiagentdata = results.data.response.output;
//                 this.setState({ ResponseData: results.data.response.output });
//             }

//             GetDataDirect({
//                 root: "Hierarchy",
//                 ManagerId: UserId,
//                 statename: "Hierarchy-" + UserId,
//                 value: this.props.value,
//                 state: true
//             }, function (result) {
//                 result = this.getTree(result, aiagentdata);                
//                 this.setState({ nodes: result });
//             }.bind(this));
//         }.bind(this));

//     }
//     componentWillReceiveProps(nextProps) {//debugger;

//     }

//     getTree(node, aiagentdata) {

//         for (let index = 0; index < node.length; index++) {
//             let agents = _.where(aiagentdata, { ManagerId: node[index].UserID });

//             node[index]["data"] = {
//                 "RoleId": node[index].RoleId,
//                 "UserID": node[index].UserID,
//                 "EmployeeId": node[index].EmployeeId,
//                 "UserName": node[index].UserName,
//                 "ManagerId": node[index].ManagerId,
//                 "RoleName": node[index].RoleName,
//                 "TotalCount": 0,
//                 "TotalPending": 0,
//                 "TLPending": 0,
//                 "PendingWithinTAT": 0,
//                 "PendingOutsideTAT": 0,
//                 "TotalClosure": 0,
//                 "TotalClosureWithinTAT": 0,
//                 "TotalClosureP": 0,
//                 "TotalClosureWithinTATP": 0,
//             }

//             if (node[index].children && node[index].children.length > 0) {
//                 node[index].children = this.getTree(node[index].children, aiagentdata);
//             }
//             else {
//                 node[index]["children"] = [];
//             }

//             agents.forEach(element => {
//                 var agentinnode = _.filter(node[index]["children"], function (item) { return item.EmployeeId == element.emp_id; });

//                 let WithInTAT = this.WithInTAT(element.submission_date);
//                 if (agentinnode.length > 0) {
//                     for (var i in node[index]["children"]) {
//                         if (node[index]["children"][i].EmployeeId == element.emp_id) {
//                             node[index]["children"][i]["data"].TotalCount = node[index]["children"][i]["data"].TotalCount + 1;
//                             node[index]["children"][i]["data"].TotalPending = node[index]["children"][i]["data"].TotalPending + (element.status == 'verifiedByTl' ? 1 : 0);
//                             node[index]["children"][i]["data"].TLPending = node[index]["children"][i]["data"].TLPending + (element.status == 'submitted' ? 1 : 0);
//                             node[index]["children"][i]["data"].PendingWithinTAT = node[index]["children"][i]["data"].PendingWithinTAT + ((element.status == 'submitted' && WithInTAT) ? 1 : 0);
//                             node[index]["children"][i]["data"].PendingOutsideTAT = node[index]["children"][i]["data"].PendingOutsideTAT + ((element.status == 'submitted' && WithInTAT == false) ? 1 : 0);
//                             node[index]["children"][i]["data"].TotalClosure = node[index]["children"][i]["data"].TotalClosure + ((element.status == 'verified') ? 1 : 0);
//                             node[index]["children"][i]["data"].TotalClosureWithinTAT = node[index]["children"][i]["data"].TotalClosureWithinTAT + ((element.status == 'verified' && WithInTAT == false) ? 1 : 0);
//                             node[index]["children"][i]["data"].TotalClosureP = node[index]["children"][i]["data"].TotalClosureP + ((element.status == 'verified') ? 1 : 0);
//                             node[index]["children"][i]["data"].TotalClosureWithinTATP = node[index]["children"][i]["data"].TotalClosureWithinTATP + ((element.status == 'verified' && WithInTAT == false) ? 1 : 0);

//                             break; //Stop this loop, we found it!
//                         }
//                     }
//                 }
//                 else {
//                     let agent = {
//                         "EmployeeId": element.emp_id,
//                         "UserName": element.emp_name,
//                         "ManagerId": element.ManagerId,
//                         "data": {
//                             "EmployeeId": element.emp_id,
//                             "UserName": element.emp_name,
//                             "ManagerId": element.ManagerId,
//                             "TotalCount": 1,
//                             "TotalPending": element.status == 'verifiedByTl' ? 1 : 0,
//                             "TLPending": element.status == 'submitted' ? 1 : 0,
//                             "PendingWithinTAT": (element.status == 'submitted' && WithInTAT) ? 1 : 0,
//                             "PendingOutsideTAT": (element.status == 'submitted' && WithInTAT == false) ? 1 : 0,
//                             "TotalClosure": (element.status == 'verified') ? 1 : 0,
//                             "TotalClosureWithinTAT": (element.status == 'verified' && WithInTAT == false) ? 1 : 0,
//                             "TotalClosureP": (element.status == 'verified') ? 1 : 0,
//                             "TotalClosureWithinTATP": (element.status == 'verified' && WithInTAT == false) ? 1 : 0,
//                         },
//                         "children": []
//                     }
//                     node[index]["children"].push(agent);
//                 }
//             });

//             node[index]["data"].TotalCount = node[index]["children"].reduce((s, f) => { return s + f.data.TotalCount; }, 0);
//             node[index]["data"].TotalPending = node[index]["children"].reduce((s, f) => { return s + f.data.TotalPending; }, 0);
//             node[index]["data"].TLPending = node[index]["children"].reduce((s, f) => { return s + f.data.TLPending; }, 0);
//             node[index]["data"].PendingWithinTAT = node[index]["children"].reduce((s, f) => { return s + f.data.PendingWithinTAT; }, 0);
//             node[index]["data"].PendingOutsideTAT = node[index]["children"].reduce((s, f) => { return s + f.data.PendingOutsideTAT; }, 0);

//             let TotalClosure = node[index]["children"].reduce((s, f) => { return s + f.data.TotalClosure; }, 0);
//             let TotalClosureWithinTAT = node[index]["children"].reduce((s, f) => { return s + f.data.TotalClosureWithinTAT; }, 0);

//             let TotalClosureP = this.CalculatePercentage(node[index]["data"].TotalCount, TotalClosure);
//             let TotalClosureWithinTATP = this.CalculatePercentage(node[index]["data"].TotalCount, TotalClosureWithinTAT);

//             node[index]["data"].TotalClosure = TotalClosure;
//             node[index]["data"].TotalClosureWithinTAT = TotalClosureWithinTAT;
//             node[index]["data"].TotalClosureP = TotalClosure + ' / ' + TotalClosureP + " %";
//             node[index]["data"].TotalClosureWithinTATP = TotalClosureWithinTAT + ' / ' + TotalClosureWithinTATP + " %";

//         }

//         return node;
//     }

//     descriptionRenderer(dataRow, dataField) {
//         return <span dangerouslySetInnerHTML={{ __html: dataRow.data[dataField] }}></span>;
//     };

//     WithInTAT(date) {
//         let durationTime = ((new Date() - new Date(date)) / 1000) / 60;
//         if ((durationTime / 60) <= 72) {
//             return true;
//         }
//         return false
//     }

//     CalculatePercentage(total, values) {
//         if (total == 0 || values == 0) {
//             return 0;
//         }
//         return ((values / total) * 100).toFixed(2)
//     }

//     render() {

//         return (

//             <div className="content">

//                 <Row>
//                     <Col md="12">
//                         <Card>
//                             <CardHeader>
//                                 <CardTitle tag="h4">QC Report</CardTitle>
//                             </CardHeader>
//                             <CardBody>
//                                 {
//                                     this.state.nodes.length == 0 ? <i class="fa fa-spinner fa-spin"></i> :
//                                         <Suspense fallback={<div>Loading...</div>}>
//                                             <SimpleTreeTable tableData={this.state.nodes} columns={columns} control={control} />
//                                         </Suspense>
//                                 }

//                             </CardBody>
//                         </Card>
//                     </Col>
//                 </Row>

//             </div>

//         )
//     }
// }

// export default QcReport;
