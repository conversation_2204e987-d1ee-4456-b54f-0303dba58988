import React from "react";
import {  Marker} from '@react-google-maps/api';


const CustomMarker = (latLong) => {



  return (

    <>
    {

    latLong.map((coords)=>{

      return(
        <Marker
        key={index}
        position={{ lat: location.lat, lng: location.lng }}
        icon={{
          url: CustomMarkerIcon,
          scaledSize: new this.props.google.maps.Size(40, 40),
        }}
      />
      )

    })

    }
    </>

  )

}


export default CustomMarker;
