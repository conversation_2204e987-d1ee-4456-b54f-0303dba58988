import React from "react";
import {
  MANAGER_ABOVE_MESSAGE_NOTE, AGENT_MESSAGE_NOTE, SUPERVISOR_MESSAGE_NOTE
} from "./Utility";

const Messages = (props) => {
  const { RoleId } = props;

  return (
    <>
    { [13].includes(RoleId) && <p className="pera">{AGENT_MESSAGE_NOTE}</p>}
    { [12].includes(RoleId) && <p className="pera">{SUPERVISOR_MESSAGE_NOTE}</p>}
    { ![12, 13].includes(RoleId) && <p className="pera">{MANAGER_ABOVE_MESSAGE_NOTE}</p>}
    </>
  )
}

export default Messages;