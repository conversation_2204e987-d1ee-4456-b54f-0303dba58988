const e = require("express");
const sqlHelper = require("../../Libs/sqlHelper");
const HadoopHelper = require("../Hadoopdb/Helper");


async function getFactsDataSql(key, datasource) {
  try {

    let response = {}, result=null;
    let query, sqlparams;

    if( datasource === 'mongo') {
      let RuleEngineDB = ruleenginedb;
      let data = await RuleEngineDB.collection('FactValues')
                  .find({ key }).toArray();
      response.key = key;
      if( data !== null && data.length > 0) {
        response.data = data[0].data;
      }
      

    } else {
      
      switch (key) {

        case 'product':
          query = 'select [ID] as [key], ProductName as [value] from dbo.Products where IsActive =1'
          sqlparams = [];
          result = await sqlHelper.sqlquery("R", query, sqlparams);
          if(result !== null) {
            response.key = key;
            response.data = result.recordsets[0];

            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: result.recordsets[0]});
          }


          break;


        case 'supplier':
          // query = `select  DISTINCT SupplierID as [key],SupplierName + '[' + ProductName +']' as [value] from [dbo].[vwSupplierPlanDetailsAllProduct]`;
          query = `select * from incentive.insurermaster`;
          result = await HadoopHelper.queryAthenaData(query);
          
          if(result !== null) {
            response.key = key;
            let supplier = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                    for (let index = 0; index < data.length; index++) {
                      const element = data[index];
                      const { insurerid: key, insurerfullname: value } = element;
                      supplier.push({key, value});
                    }
          }
            response.data = supplier;
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: supplier});
          }
          break;


        case 'plancategory':
          query = `Select * from Incentive.PlanCategoryMaster`;

          result = await HadoopHelper.queryAthenaData(query);
          if(result !== null) {
            response.key = key;
            let planCategory = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                    for (let index = 0; index < data.length; index++) {
                      const element = data[index];
                      if(element !== null && element !== undefined) {
                        planCategory.push(element.plancategory);
                      }              
                    }
            }
            response.data = planCategory;
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: planCategory });
          }
          break;

        case 'insurercategory':
          query = `Select * from Incentive.InsurerCategoryMaster`;

          result = await HadoopHelper.queryAthenaData(query);
          if(result !== null) {
            response.key = key;
            let insurerCategory = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                    for (let index = 0; index < data.length; index++) {
                      const element = data[index];
                      if(element !== null && element !== undefined) {
                        insurerCategory.push(element.insurercategory);
                      }
                    }
                    response.data = insurerCategory;
              }
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: insurerCategory });
          }
          break;

        case 'subprocess':
          query = `Select * from incentive.agentsubprocessgroupmaster`;

          result = await HadoopHelper.queryAthenaData(query);
          if(result !== null) {
            response.key = key;
            let subProcess = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                  for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    if(element !== null && element !== undefined) {
                      subProcess.push(element.agentsubprocessgroup);
                    }
                    
                }
            response.data = subProcess;
            }
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: subProcess });
            
          }
          break;

        case 'process':
          query = `Select * from incentive.agentprocessgroupmaster`;

          result = await HadoopHelper.queryAthenaData(query);
          if(result !== null) {
            response.key = key;
            let process = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                    for (let index = 0; index < data.length; index++) {
                      const element = data[index];
                      if(element !== null && element !== undefined) {
                        process.push(element.agentprocessgroup);
                      }
              
               }
            response.data = process;
            }
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: process });
          }
          break;

        case 'paymentperiodicity':
          query = `Select * from Incentive.PaymentPeriodicityMaster`;

          result = await HadoopHelper.queryAthenaData(query);
          if(result !== null) {
            response.key = key;
            let paymentperiodicity = [];
            let data = [];
            if(result.results !== undefined &&
                result.results !== null && 
                  result.results.Items !== undefined) {

                    data = result.results.Items;
                    for (let index = 0; index < data.length; index++) {
                      const element = data[index];
                      if(element !== null && element !== undefined) {
                        paymentperiodicity.push(element.paymentperiodicity);
                      }
                    }
            response.data = paymentperiodicity;
          }
            // let RuleEngineDB = ruleenginedb;
            // let params = await RuleEngineDB.collection('FactValues')
            //                   .insertOne({ key, data: paymentperiodicity });
          }
          break;


        default:
          break;
      }
    }
 
     
    return response;

  } catch (err) {
    console.log(err);
    return null;
  }
}




async function SaveBookingDetails({
  BookingId,
  incentivemonth,
  BookingStatus,
  salesAgent,
  APE,
  WeightedAPE,
  AmountwithJustification,
  AmountwithoutJustification

}) {
  // console.log('start');
  try {

    query = '[enc].[InsertUpdateBookingDetailsV1]'
    sqlparams = [];
    // @BookingId bigint,
    // @incentivemonth datetime,
    // @BookingStatus varchar(200)='',
    // @salesAgent varchar(100),
    // @APE varchar(100),
    // @AmountwithJustification decimal(10,2)=0,
    // @AmountwithoutJustification decimal(10,2)=0
    // @WeightedAPE varchar(100),
    // @uploadedBy bigint,
    // @status smallint out,
    // @message varchar(100) out 


    sqlparams.push({ key: "BookingId", value: BookingId });
    sqlparams.push({ key: "incentivemonth", value: incentivemonth });
    sqlparams.push({ key: "BookingStatus", value: BookingStatus });
    sqlparams.push({ key: "salesAgent", value: salesAgent });
    sqlparams.push({ key: "APE", value: APE });
    sqlparams.push({ key: "AmountwithJustification", value: AmountwithJustification });
    sqlparams.push({ key: "AmountwithoutJustification", value: AmountwithoutJustification });
    sqlparams.push({ key: "WeightedAPE", value: WeightedAPE });
    sqlparams.push({ key: "uploadedBy", value: 124 });
    sqlparams.push({ key: "status", value: 0, type: "out" });
    sqlparams.push({ key: "message", value: '', type: "out" });

    result = await sqlHelper.sqlProcedure("L", query, sqlparams);
    // console.log('output sql result', result);
    return result;

    //   if(result && result.output){
    //     element['status'] = result.output.status
    //     element['message'] = result.output.message
    // }

  } catch (err) {
    console.log('here catch ', err)
    return null;
  }
}

async function SaveAgentDetails({
  Ecode,
  productId,
  Incentivemonth,
  ProcessName,
  Sourcedbkgs,
  IssuedBkgs,
  WeightedAPE,
  IssuedAPE,
  AmountMade,
  QualityScore,
  FinalIncentive
}) {
  try {

    query = '[ENC].[InsertUpdateAgentDetailsV1]'
    sqlparams = [];
    // @Ecode varchar(100),
    // @productId smallint,
    // @Incentivemonth datetime,
    // @ProcessName varchar(100),
    // @Sourcedbkgs decimal(10,2),
    // @IssuedBkgs decimal(10,2),
    // @WeightedAPE decimal(10,2),
    // @IssuedAPE decimal(10,2),
    // @AmountMade decimal(10,2),
    // @QualityScore decimal(10,3),
    // @FinalIncentive decimal(10,2),
    // @uploadedBy bigint,
    // @status smallint out,
    // @message varchar(100) out 

    sqlparams.push({ key: "Ecode", value: Ecode });
    sqlparams.push({ key: "productId", productId });
    sqlparams.push({ key: "Incentivemonth", Incentivemonth });
    sqlparams.push({ key: "ProcessName", ProcessName });
    sqlparams.push({ key: "Sourcedbkgs", Sourcedbkgs });
    sqlparams.push({ key: "IssuedBkgs", IssuedBkgs });
    sqlparams.push({ key: "WeightedAPE", WeightedAPE });
    sqlparams.push({ key: "IssuedAPE", IssuedAPE });
    sqlparams.push({ key: "AmountMade", AmountMade });
    sqlparams.push({ key: "QualityScore", QualityScore });
    sqlparams.push({ key: "FinalIncentive", FinalIncentive });
    //sqlparams.push({ key: "uploadedBy", 124 });
    sqlparams.push({ key: "status", value: status, type: "out" });
    sqlparams.push({ key: "message", value: message, type: "out" });

    result = await sqlHelper.sqlProcedure("L", query, sqlparams);
    return result;

    //   if(result && result.output){
    //     element['status'] = result.output.status
    //     element['message'] = result.output.message
    // }
  } catch (err) {
    return null;
  }
}







module.exports = {
  getFactsDataSql: getFactsDataSql,
  SaveBookingDetails: SaveBookingDetails,
  SaveAgentDetails: SaveAgentDetails

};
