
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");
const auth = require("../../auth")
const cache = require("memory-cache");
const {AddSupervisorsList, ReturnMongoData, ReturnAgentLatLong, ReturnAgentLatLongByTime, ReturnAgentLatLongByAppointment, FetchOTSData,FetchSocialMediaAgents}=require('../FosTracker/MongoSocketMaintenance');
const { FetchMultipleDocuments } = require("../common/CommonMethods");
const exp = require("constants");
const CommonMethods = require("../common/CommonMethods");



// exports.getAppointmentSlots = function (req, res) {
//     const url = conf.MATRIXCOREAPI + "/fos/api/fos/getAppointmentSlots/539363636"

//     const headers = {
//         "source" : "fosapp",
//         "clientKey": conf.FOSCLIENTKEY,
//         "authKey": conf.FOSAUTHKEY,
//         "agentid": 19031,
//         "processid": 0
//     }

//     console.log(url);
//     console.log(headers);

//     try {
//         axios.get(url, { headers: headers }).then(response => {
//             try {
//                 if (response.status === 200) {
//                     res.send({
//                         status: 200,
//                         data: response.data
//                     });
//                 }
//                 else {
//                     res.send({
//                         status: 500,
//                         data: response
//                     });
//                 }
//             }
//             catch (e) {

//                 res.send({
//                     status: 500,
//                     message: e
//                 });
//             }

//         });
//     }
//     catch (e) {
//         console.log(e);
//         res.send({
//             status: 500,
//             message: e
//         });
//     }
// }



// exports.getAppointmentsBySlotId = function (req, res) {
//     const url = conf.MATRIXCOREAPI + "/fos/api/fos/getAppointmentsBySlotId/539363636"

//     const headers = {
//         "source" : "fosapp",
//         "clientKey": conf.FOSCLIENTKEY,
//         "authKey": conf.FOSAUTHKEY,
//         "agentid": 19031,
//         "processid": 0
//     }

//     console.log(url);
//     console.log(headers);

//     try {
//         axios.get(url, { headers: headers }).then(response => {
//             try {
//                 if (response.status === 200) {
//                     res.send({
//                         status: 200,
//                         data: response.data
//                     });
//                 }
//                 else {
//                     res.send({
//                         status: 500,
//                         data: response
//                     });
//                 }
//             }
//             catch (e) {

//                 res.send({
//                     status: 500,
//                     message: e
//                 });
//             }

//         });
//     }
//     catch (e) {
//         console.log(e);
//         res.send({
//             status: 500,
//             message: e
//         });
//     }
// }

const ReschduleLead = async (req) => {
    const url = conf.MATRIXCOREAPI + "/api/FOS/SetAppointmentDataV2"
    const reqdata = {
        CustomerId: req.CustomerId,
        ParentId: req.ParentId,
        UserId: req.CreatedBy,
        OfflineCityId: req.OfflineCityId,
        AppointmentType: req.AppointmentType,
        AssignmentId: req.AssignmentId,
        ZoneId: req.ZoneId,
        AppointmentDateTime: req.AppointmentDateTime,
        SlotId: req.SlotId,
        Address: req.Address,
        Address1: req.Address1,
        Pincode: req.Pincode,
        Landmark: req.Landmark,
        NearBy: req.NearBy,
        Comments: req.Comments,
        subStatusId: req.subStatusId,
        place_id: req.place_id,
        CancelReasonId: req.CancelReasonId
    }

    const headers = {
        "source": "slotmodel",
        "clientKey": conf.SlOTCLIENTKEY,
        "authKey": conf.SlOTAUTHKEY,
        "agentid": req.UserId,
    }

    // console.log(url);
    // console.log(headers);

    try {
        let response = await axios.post(url, reqdata, { headers: headers });
        return ({data: response, errorStatus: 0 })
    }
    catch(e){
        return ({data: e.toString(), errorStatus: 1 })
    }
    
}

exports.SetAppointmentData = async function (req, res) {
    let data;
    let errorStatus;
    let err = "";
    try{
        ({data, errorStatus} = await ReschduleLead(req.body))

        if (errorStatus == 0 && data.status === 200) {
            return res.send({
                status: 200,
                data: data?.data || []
            });
        }
        else {
            return res.send({
                status: 500,
                data: data
            });
        }
    }
    catch (e) {
        // console.log('SetAppointmentData 1', e);
        err = e;
        return res.send({
            status: 500,
            message: e
        });
    }
    finally{
        // (agentId, Method, Channel, query, ResponseText, err, IP)
        auth.createLog(req.body.ParentId, "SetAppointmentData", "MatrixDashBoard", req.body, data.data, err ,"")
    }
}

exports.SlotLeadAssignment = async function (req, res) {

    let data;
    let errorStatus;
    let err = "";
    // console.log(req.body[1])
    try{
        let sqlparam = [];
        sqlparam.push({ key: "LeadID", value: req.body[1].LeadID });
        sqlparam.push({ key: "EmployeeID", value: req.body[1].EmployeeID });
        sqlparam.push({ key: "AssignedBy", value: req.body[1].AssignedBy });
        sqlparam.push({ key: "JobTrackingId", value: '87' });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[Fos_UploadLeadAllocation]", sqlparam);
        if(result.recordset[0].status==1){
            ({data, errorStatus} = await ReschduleLead(req.body[0]))
            if (errorStatus == 0 && data.status === 200) {
                return res.send({
                    status: 200,
                    data: data?.data || []
                });
            }
            else {
                return res.send({
                    status: 500,
                    data: data
                });
            }
    
        }
        
    }
    catch (e) {
        // console.log('SlotLeadAssignment 1', e);
        err = e;
        return res.send({
            status: 500,
            message: e
        });
    }
    finally{
        // (agentId, Method, Channel, query, ResponseText, err, IP)
        auth.createLog(req.body[1].LeadID, "SlotLeadAssignment", "MatrixDashBoard", req.body, data.data, err ,"")
    }
}

exports.FosRealTimeStatus = async (req, res)=>{
  
    try{
        let userId = req.user?.userId ;
        
        if(userId)
        {
            let TlList = req.body;
            AddSupervisorsList(userId, TlList);
            let data = await ReturnMongoData(userId, TlList);

            let UntrackabilityTime= await CommonMethods.FosRTTUntrackabilityTime();

            if(Array.isArray(data))
            {
                return res.status(200).json({
                    errorStatus: 0,
                    data: data,
                    configVal:  {
                        UntrackabilityTime: UntrackabilityTime
                    }
                })
            }
            // FetchMultipleDocuments()
        } 
        
    }
    catch (e){

        console.log("FosRealTimeStatus error is ", e);
        return res.status(200).json({
            errorStatus : 1,
            error : e  
        })

    }
    return res.status(200).json({
        errorStatus: 0,
        data: []
    })
   
}

exports.FOSCallingStatus = async (req, res)=>{
    
  
    try{
        let userId = req.user?.userId ;
        let TlList = req.body;
        let url=conf.CALLING_STATUS_URL;
        
        if(userId && Array.isArray(TlList) && TlList.length>0 && url.length>0)
        {
            let TlListUrl= TlList.join(',');
            url= url+TlListUrl
            // console.log("TlListUrl", url);
            
            
            
            let result = await axios.get(url);
            if(result.status==200)
            {

                if(Array.isArray(result.data))
                {
                    return res.status(200).json({
                        errorStatus:0,
                        data:result.data
                    })
                }
                
            }   
            
        }

        
    }
    catch (e){

        console.log("FosCallingStatus error ", e);
        return res.status(200).json({
            errorStatus : 1,
            error : "Some error" 
        })

    }

    return res.status(200).json({
        errorStatus: 0,
        data: []
    })
}

exports.FetchAgentLatLong = async(req, res)=>{
    try{
        let AgentList = req.body;
        if(Array.isArray(AgentList) && AgentList.length>0)
        {
            let set = new Set(AgentList);
            let UniqueAgentList = Array.from(set);


            let data = await ReturnAgentLatLong(UniqueAgentList);
            if(Array.isArray(data))
            {
                return res.status(200).json({
                    errorStatus: 0,
                    data: data
                })
            }

        }
    }
    catch(e)
    {
        console.log("FetchAgentLatLong: ",e);
        return res.status(200).json({
            errorStatus : 1,
            error : e  
        })
    }
    return res.status(200).json({
        errorStatus: 0,
        data: []
    })
}

exports.FetchAgentLatLongByTime = async(req, res) =>{
    try{
        let StartTime=req.body.StartTime;
        let EndTime = req.body.EndTime;
        let AgentId = req.body.AgentId;
        let AppointmentId = req.body.AppointmentId;
        let data= await ReturnAgentLatLongByAppointment(AgentId, AppointmentId);
       
        return res.status(200).json({
                errorStatus: 0,
                data: data
        })
        
    }
    catch(e)
    {
        console.log("FetchAgentLatLongByTime: ",e);
        return res.status(200).json({
            errorStatus : 1,
            error : e  
        })
    }
    // return res.status(200).json({
    //     errorStatus: 0,
    //     data: {}
    // })
}

exports.OTSData = async(req, res)=>{
    try{
        let userId = req.user?.userId ;
        
        if(userId)
        {
            let TlList = req.body;
            // console.log("The TlList is ", req.body[0]);
            let data = await FetchOTSData(userId, TlList);
            
            return res.status(200).json({
                errorStatus:0,
                data: data
            })
        }
        
    }
    catch(e)
    {
        console.log("OTSData: ",e);
        return res.status(200).json({
            errorStatus: 1,
            error:e
        })
    }
    return res.status(200).json({
        errorStatus: 0,
        data: {}
    })
}

exports.GetFosUserDetails = (req, res) => {
    try{
        let user = req.user;
        if (user && user.processList) 
        {         
            return res.status(200).json({
                errorStatus:0,
                data: { ProcessList : user?.processList || []}
            })
        }
        return res.status(200).json({
            errorStatus:0,
            data: { ProcessList : []}
        })
    }
    catch(e)
    {
        return res.status(200).json({
            errorStatus: 1,
            error:e
        })
    }
}

exports.IsSocialMediaAgent = async(req,res)=>{
    try{
        let userId = req?.user?.userId ;
        
        if(userId)
        {
            let data = await FetchSocialMediaAgents();
            if(data && Array.isArray(data) && data.length>0)
            {
                // Check if userId exists in the data array
                const isSMAgent = data.includes(parseInt(userId));
                return res.status(200).json({
                    errorMessage: "",
                    data: isSMAgent 
                });
            }
        }
        
    }
    catch(e)
    {
        console.log("IsSocialMediaAgent: ",e);
        return res.status(200).json({
            errorMessage:e,
            data: false
        })
    }
    return res.status(200).json({
        errorMessage: "",
        data: false
    })
}
