
import React from "react";
import { useState, useEffect } from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { connect } from "react-redux";
import { If, Then } from 'react-if';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import {
    Container,
    Tab,
    Tabs,

} from "react-bootstrap";

import DropDown from './Common/DropDownList';

import {
    GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";

import { fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter, getuser } from '../utility/utility.jsx';
import UserStats from "views/UserStats";

import {MultiSelect} from "react-multi-select-component";
import MultiSelectList from './Common/MultiSelectList';
import { ToastContainer, toast } from 'react-toastify';

const AgentSurveyPopUp = (props) => {

    const [activeTab, setActiveTab] = React.useState(0);
    let [showModal, setshowModal] = useState(true);
    const [items, setitems] = useState([])
    const [Productselected, setProductSelected] = useState([]);
    const [Agent, setAgent] = useState([]);

    const [ProductId, setProductId] = React.useState();
    const [Process, setProcess] = React.useState([]);
    const [Processselected, setProcessselected] = React.useState([]);

    const handleTabChange = (newValue) => {
        setActiveTab(newValue);
    };

    const ProductList = {
        config:
        {
            root: "Products",
            cols: ["ID AS Id", "ProductName AS Display", "ID AS value", "ProductName AS label"],
            con: [{ "Isactive": 1 }],
        }
    };

    useEffect(() => {
        if (ProductId) {
            if (!props.CommonData.isError) {
                props.GetCommonspData({
                    root: 'GetAgentProcessOnProduct',
                    c: "R",
                    params: [{ Product: ProductId }],
                }, function (data) {
                    if (data && data.data && data.data.data) {
                        let item = data.data.data[0]
                        setProcess(item)
                    }
                })
            }
        }

    }, [ProductId])

    const handleClose = () => {
        props.handleClose();
    }

    const handleClick = () => {
        console.log(activeTab)
        let ProductIds = ''
        let ProcessList = ''

        if (props.survey.length == 0 || props.survey=='0') {
            toast('Select Survey', { type: 'error' });
            return;
        }

        if (Productselected && Productselected.length>0) {
            let Product = []
            Productselected.map(item => {
                Product.push(item.value)
            })
            ProductIds = Product.toString()

        }

        else if (Processselected.length > 0) {

            let Process = Processselected.map(item => {
                return item.label
            })
            ProcessList = Process.toString()
            console.log(activeTab, Agent, ProductId, ProcessList)
        }
        let product = parseInt(ProductId)
        let User = parseInt(Agent)

        toast('Loading...', { type: 'info' });
        props.GetCommonspData({
            root: 'InsertSurveyAgentsProcess',
            c: "L",
            params: [{ SurveyId: props.survey, ProductIds: ProductIds, RoleId: User, ProductId: product, Process: ProcessList }],
        }, function (result) {
            try {
                if (result.data && result.data.status == 200) {
                    toast('Saved Successfully', { type: 'success' });
                } else {
                    toast('Not Saved', { type: 'error' });
                    return;
                }
            }
            catch (e) {
                toast('Something Wrong', { type: 'error' });
            }
        });
        handleClose();
    }


    // const onSelect = (selectedList, selectedItem) => {
    //     debugger;
    //     var newselectedList = selectedList.filter(task => task.label !== 'Select Values');
    //     let selectAll = false;
    //     if(items.length == selectedList.length){
    //          selectAll = true;
    //     }
    //     setProductSelected(newselectedList);
    // }


    const OnProcessselected = (selectedList, selectedItem) => {
        debugger;
        var newselectedList = selectedList.filter(task => task.label !== 'Select Values');
        let selectAll = false;
        if (items.length == selectedList.length) {
            selectAll = true;
        }
        setProcessselected(newselectedList);
    }
    const ProductChange = (e) => {
        setProcessselected([])
        setProductId(e.target.value);
    }


    const checkUser = (event) => {
        setAgent(event.target.id)
    }

    const handleChange = (event)=>{
        setProductSelected(event.newselectedList)
    }

    return (

        <div >

            <Modal show={showModal} onHide={props.handleClose} size="lg" style={{ width: '1200px' }}>
                <Modal.Header closeButton>
                </Modal.Header>
                <Modal.Body>
                    <Form >
                        <Row>
                            <Col md={11}>


                                <div className='CsatratingPopup'>
                                    <Tabs activeKey={activeTab} onSelect={handleTabChange} >
                                        <Tab eventKey="0" title="Role Wise"
                                        >

                                            {<Container spacing={3}>
                                                <Row>
                                                    <Col sm={4} md={4} xs={4}>
                                                        <div onChange={(e) => { checkUser(e) }}>
                                                            <input type="radio" id="0" name="Agents" />
                                                            <label for="Agents"> &nbsp; All Users</label><br />
                                                            <input type="radio" id="13" name="Agents" />
                                                            <label for="Agents"> &nbsp; Agents</label><br />
                                                            <input type="radio" id="11" name="Agents" />
                                                            <label for="Managers"> &nbsp; Managers</label><br />
                                                            <input type="radio" id="12" name="Agents" />
                                                            <label for="Supervisors"> &nbsp; Supervisors</label><br />
                                                            <input type="radio" id="2" name="Agents" />
                                                            <label for="Admins"> &nbsp; Admins</label><br />
                                                            <input type="radio" id="-1" name="Agents" />
                                                            <label for="JAG"> &nbsp; JAG Agents</label><br />
                                                        </div>
                                                    </Col>

                                                </Row>
                                            </Container>
                                            }

                                        </Tab>
                                        <Tab eventKey="1" title="Product Wise" className="criteriaPoint"
                                        >

                                            <Container spacing={3}>
                                                <Row >
                                                    <Col sm={10} md={10} xs={10}>
                                                        <p style={{ color: "black" }}>Survey Assign To Agents</p>
                                                        
                                                        <MultiSelectList
                                                        className="agentSurveyBox"
                                                            //     options={ProductList}
                                                            //     value={Productselected}
                                                            //     labelledBy="Select"

                                                            firstoption="Select" valueTobefiltered={[2, 115, 7, 117]} col={ProductList} onChange={handleChange}
                                                            // setSelected={setProductSelected}
                                                        />

                                                    </Col>
                                                </Row>
                                            </Container>
                                        </Tab>

                                        <Tab eventKey="2" title="Process Wise" className="criteriaPoint"
                                        >

                                            <Container spacing={3}>
                                                <Row >
                                                    <Col sm={10} md={10} xs={10}>
                                                    <p style={{ color: "black" }}>Product</p>
                                                        <DropDown firstoption="Select" valueTobefiltered={[2, 115, 7, 117, 147]} col={ProductList} onChange={ProductChange} >

                                                        </DropDown><br></br>

                                                        <p style={{ color: "black" }}>Process</p>
                                                        <MultiSelect
                                                        className="agentSurveyBox"
                                                            options={Process}
                                                            value={Processselected}
                                                            onChange={OnProcessselected}
                                                            labelledBy="Select"
                                                        />
                                                    </Col>
                                                </Row>
                                            </Container>
                                        </Tab>
                                    </Tabs>

                                </div>


                            </Col>

                            <Col md={1}>

                            </Col>

                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClick}>
                        Save
                    </Button>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>

                </Modal.Footer>
            </Modal>


        </div>

    );


}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData
    }
)(AgentSurveyPopUp);


