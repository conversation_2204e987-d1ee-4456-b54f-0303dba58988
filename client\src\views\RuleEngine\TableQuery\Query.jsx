import axios from 'axios';
import React, { Component } from 'react';
import { Form , Row , Col , Button } from 'react-bootstrap';
import Beat<PERSON>oader from "react-spinners/BeatLoader";
import config from './../../../config';

class Query extends Component{
    constructor(props){
        super(props);
        this.state = {
            query : "",
            loading : false,
            response : false,
            columns : [],
            data:[],
        }
        this.handleQueryChange = this.handleQueryChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
    }
    
    handleQueryChange(event){
        event.preventDefault();
        this.setState({
            [event.target.name] : event.target.value,
        });
    }
    
    async handleSubmit(event){
        event.preventDefault();

        this.setState({
            loading : true,
        })
        const URL = `${config.api.base_url}/hdb/executeQuery`;
        let body = { queryString : this.state.query };
        let headers = {'Content-Type': 'application/json'};
        let res = await axios.post(URL, body, headers);
        this.setState({
            loading : false,
            response : true,
            data : res.data.data,
        })
    }


    render(){
        return (
            <React.Fragment>
                <h4>Query</h4>
                <div>
                    <form onSubmit={this.handleSubmit}>
                        <textarea 
                            style={{
                                width: "95%",
                                padding: "10px",
                                margin: "10px"
                            }}
                            name="query" 
                            value={this.state.agentId} 
                            onChange = {this.handleQueryChange} 
                            rows="5"
                        />
                        <Button type="submit">
                            Search
                        </Button>
                    </form>
                </div>
                {this.state.loading &&
                    <div style={{textAlign:"center"}}>
                        <BeatLoader size={20}
                            color={"#3498db"}
                            loading={true}
                        />
                    </div>
                }
                {this.state.response && !this.state.loading &&
                    <textarea 
                        style={{
                            width: "95%",
                            padding: "10px",
                            margin: "10px"
                        }}
                        rows="20"
                    >
                        {JSON.stringify(this.state.data,null,4)}
                    </textarea>
                }
            </React.Fragment>
        )
    }

}

export default Query;