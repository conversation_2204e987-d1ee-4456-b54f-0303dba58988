import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
//import { Box, Tab, Tabs, Grid,  Paper, LinearProgress } from "@material-ui/core";
// reactstrap components
import {
    Card,
    Tab, 
    Tabs,
    CardHeader,
    CardBody,
    CardTitle,
    Container,
    Row,
    Col,
    Table
} from "react-bootstrap";
//import { CONFIG } from "../../../appconfig"
//import 'CSATScore.scss';
//import { CALL_API } from "../../../services";
//import User from "../../../services/user.service";
import { getuser } from "../../../utility/utility";
import {
     listsp
  } from "../../../store/actions/CommonAction";


function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && <Card p={3}>{children}</Card>}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
};




export default function AgentCSATScore(props) {

    const [activeTab, setActiveTab] = React.useState(0);
    const [CSATScoreDetails, setCSATScoreDetails] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    let [CsatImg, setCsatImage] = useState('');

    useEffect(() => {
        // if (ParentLeadId > 0) {
        GetCSATRating();
        // }
    }, [])

    const GetCSATRating = () => {
        setIsLoading(true);
        let user = getuser();
        const input = {
            root: "GetAgentCSATScore",
            params: [{ userId: user.UserID }]
        };
       

        listsp(input, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
               // debugger;
             result= result.data.data[0];
                if (result && result[0]) {
                    setCsatImageCall(result[0].YTDCSAT);
                }
                setCSATScoreDetails(result);
                setIsLoading(false);
            }
        });

    }

    const handleTabChange = ( newValue) => {
        setActiveTab(newValue);
    };

    const setCsatImageCall = (csat) => {
        if (csat > 0 && csat < 3.5) {
            setCsatImage("Below Average");
        }
        else if (csat >= 3.5 && csat < 4.25) {
            setCsatImage("Average");
        }
        else if (csat >= 4.25 && csat <= 5) {
            setCsatImage("Good");
        }

    }



    return (

        <div className='CsatratingPopup'>
            <Tabs activeKey={activeTab} onSelect={handleTabChange} >
                <Tab eventKey="0" title="Your Rating"
                >
                     
                {/* {isLoading && <LinearProgress color="secondary" />} */}
                {(Array.isArray(CSATScoreDetails) && CSATScoreDetails.length > 0) && <Container spacing={3}>
                    <Row>
                        <Col sm={4} md={4} xs={4}>
                            {CsatImg && <><img src={"/CSAT/" + CsatImg + ".svg"} alt={CsatImg} />
                                <p className="RatingStatus"> {CsatImg}</p></>}
                            {/* <img src={CONFIG.PUBLIC_URL + "/images/salesview/average.svg"} />
                            <p className="RatingStatus"> Average</p>
                            <img src={CONFIG.PUBLIC_URL+"/images/salesview/bad.svg"}  />
                            <p className="RatingStatus"> Bad</p>
                            <img src={CONFIG.PUBLIC_URL+"/images/salesview/good.svg"}  />
                            <p className="RatingStatus"> Good</p> */}

                        </Col>
                        <Col sm={8} md={8} xs={8}>
                            <hr />
                            <h2>{(CSATScoreDetails[0].CSAT) ? CSATScoreDetails[0].CSAT : 'N.A'}<span>/5</span></h2>
                            <p className="ratingMsg">
                                Advisors with customer feedback rating of <strong>4.25</strong> and above have a <strong>10%</strong> higher conversion rate compared to advisors with lower ratings
                            </p>
                            </Col>
                    </Row>
                    <Row>
                        <Container>
                            <Table aria-label="simple table" className="ratingdata">
                                <thead>
                                    <tr>
                                        <th align="right"></th>
                                        <th align="right">YTD</th>
                                        <th align="right">Last 30 Days</th>

                                    </tr>
                               </thead>
                                <tbody>
                                    <tr >
                                        <td>  Total Responses
                                        </td>
                                        <td >{(CSATScoreDetails[0].YTDTotalReview || CSATScoreDetails[0].YTDTotalReview == 0) ? CSATScoreDetails[0].YTDTotalReview : 'N.A'}</td>
                                        <td >{(CSATScoreDetails[0].TotalReview || CSATScoreDetails[0].TotalReview == 0) ? CSATScoreDetails[0].TotalReview : 'N.A'}</td>
                                    </tr>
                                    <tr >
                                        <td> % Good
                                        </td>
                                        <td >{(CSATScoreDetails[0].YTDGoodReviewPer || CSATScoreDetails[0].YTDGoodReviewPer == 0) ? (CSATScoreDetails[0].YTDGoodReviewPer + '%') : 'N.A'}</td>
                                        <td >{(CSATScoreDetails[0].GoodReviewPer || CSATScoreDetails[0].GoodReviewPer == 0) ? (CSATScoreDetails[0].GoodReviewPer + '%') : 'N.A'}</td>
                                    </tr>
                                    <tr >
                                        <td>  % Bad
                                        </td>
                                        <td>{(CSATScoreDetails[0].YTDBadReviewPer || CSATScoreDetails[0].YTDBadReviewPer == 0) ? (CSATScoreDetails[0].YTDBadReviewPer + '%') : 'N.A'}</td>
                                        <td >{(CSATScoreDetails[0].BadReviewPer || CSATScoreDetails[0].BadReviewPer == 0) ? (CSATScoreDetails[0].BadReviewPer + '%') : 'N.A'}</td>
                                    </tr>
                                    <tr >
                                        <td> Score
                                        </td>
                                        <td>{(CSATScoreDetails[0].YTDScore || CSATScoreDetails[0].YTDScore == 0) ? (CSATScoreDetails[0].YTDScore + '%') : 'N.A'}</td>
                                        <td >{(CSATScoreDetails[0].Score || CSATScoreDetails[0].Score == 0) ? (CSATScoreDetails[0].Score + '%') : 'N.A'}</td>
                                    </tr>
                                    <tr >
                                        <td> CSAT
                                        </td>
                                        <td>{(CSATScoreDetails[0].YTDCSAT || CSATScoreDetails[0].YTDCSAT == 0) ? CSATScoreDetails[0].YTDCSAT : 'N.A'}</td>
                                        <td >{(CSATScoreDetails[0].CSAT || CSATScoreDetails[0].CSAT == 0) ? CSATScoreDetails[0].CSAT : 'N.A'}</td>
                                    </tr>
                                </tbody>
                            </Table>
                        </Container>
                    </Row>
                </Container>
                }
                {(Array.isArray(CSATScoreDetails) && CSATScoreDetails.length == 0 && !isLoading) && 'No Data Found'}
          
                    </Tab>
                <Tab eventKey="1" title="Criterias" className="criteriaPoint"
                >
                    
<Container spacing={3}>
    <Row >
        <Col sm={12} md={12} xs={12}>
        <h4>Star Rating:</h4>
        <ul>
            <li><strong> 0-3.5 = Bad </strong> (Agent with CSAT 3.5 above are having 25% better Conversion Rate w.r.t to agents in bad Category)</li>
            <li><strong> 3.5-4.25 - Average </strong> (Agent with CSAT 4.25 above are having 10% better Conversion Rate w.r.t. to agents in Average category )</li>
            <li><strong> 4.25 Above - Good </strong> ( Keep it up !!! )</li>
        </ul>
        </Col>
    </Row>
</Container>
</Tab>
            </Tabs>

           
           
            {/* </DialogContent>
        </Dialog> */}
        </div>

    );
}

