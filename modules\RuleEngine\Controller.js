const tblList = require("../constants");
let fs = require('fs');
const path = require("path");
const { Rule, Fact, Engine } = require('json-rules-engine');
const { ObjectID, ObjectId } = require('mongodb');
let engine = new Engine();
const { expressionParser } = require('./expressionParser');
const { evaluate } = require('mathjs');
const sqlHelper = require("../../Libs/sqlHelper");
const SQLMethods = require("./SQLMethods");
var _ = require('underscore');


async function getRules(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let data = await RuleEngineDB.collection("IncentiveRules")
      .find({})
      .toArray();
    res.send({
      status: 200,
      data: data,
      message: "Success"
    });

  } catch (err) {
    //console.log(err);
    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    //await RuleEngineDB.close();
  }
}

async function getRulebyId(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let data = await RuleEngineDB.collection("IncentiveRules")
      .find({ _id: ObjectID(req.query._id) })
      .toArray();

    res.send({
      status: 200,
      data: data,
      message: "Success"
    });

  } catch (err) {
    //console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {

  }
}


async function SaveCondition(req, res) {
  try {

    var newvalues = { $set: { conditions: req.body.conditions } };
    let RuleEngineDB = ruleenginedb;
    var collection = RuleEngineDB.collection("IncentiveRules");

    collection.updateOne({ _id: ObjectID(req.body._id) }, newvalues, function (err, results) {

      res.send({
        status: 200,
        data: results.result,
        message: "Success"
      });
    });
  } catch (err) {

    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    // await collection.close();
  }
}

async function SaveRule(req, res) {
  try {

    let RuleEngineDB = ruleenginedb;
    var type = req.body.type;
    let rule = {
      ruleName: req.body.ruleName,
      fact: req.body.fact,
      isActive: true,
      conditions: {
        all: [{
          fact: req.body.fact,
          operator: req.body.operator,
          value: req.body.value,
        }]
      },
      event: [{
        type: req.body.eventType,
        eventName: req.body.eventMessage
      }],
      // AIAgentId: "HealthChatbot"
    }
    let id = req.body._id;
    // console.log("id");
    // console.log(id);
    //let ro = new Rule(rule);
    //let jsonString = ro.toJSON();
    //var bodyJson = JSON.parse(jsonString);
    var collection = RuleEngineDB.collection("IncentiveRules");
    if (id) {
      collection.updateOne({ _id: ObjectID(id) }, { $set: rule }, { upsert: true });
    }
    else {
      collection.insertOne(rule);
    }


    res.redirect("/list.html");
  } catch (err) {

    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    // await collection.close();
  }
}

async function factCheck(req, res) {
  try {
    let option = req.body.factCheck;
    let value = req.body.value;

    engine.addRule({
      conditions: {
        any: [{
          all: [{
            fact: "sampleFact",
            operator: "equal",
            value: true
          }]
        }]
      },
      event: {  // define the event to fire when the conditions evaluate truthy
        type: 'Display Message',
        params: {
          message: 'Print Message'
        }
      }
    });
    //let fax = new Fact("any", req.body.factCheck, req.body.value);
    let fac = {
      option: value
    }

    engine
      .run(fac)
      .then(results => {
        results.events.map(event => console.log(event.params.message))
      });

    res.send({
      status: 200,
      message: "Success"
    });

  } catch (err) {

    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    // await collection.close();
  }
}



async function resolve(body) {

  let errorStatus = 0;
  try {
    //collection = client.db("RuleEngine").collection("Rules");
    let RuleEngineDB = ruleenginedb;
    let collection = RuleEngineDB.collection("IncentiveRules");

    if (collection == null) {
      // console.log("mongo not connected");

    } else {
      let { facts, searchKeys, criteria, operationType } = body;

      // creating engine instance with allowing undefined facts
      let engine = new Engine([], { allowUndefinedFacts: true });

      // custom operators
      engine.addOperator("DateGreaterThan", (factValue, jsonValue) => {
        if (!factValue) return false;

        let factDate = Date.parse(factValue);
        let jsonDate = Date.parse(jsonValue);

        if (factDate > jsonDate) {
          return true;
        } else {
          return false;
        }
      });

      engine.addOperator("DateLessThan", (factValue, jsonValue) => {
        if (!factValue) return false;

        let factDate = Date.parse(factValue);
        let jsonDate = Date.parse(jsonValue);

        if (factDate < jsonDate) {
          return true;
        } else {
          return false;
        }
      });

      engine.addOperator("DateLessThanInclusive", (factValue, jsonValue) => {
        if (!factValue) return false;

        let factDate = Date.parse(factValue);
        let jsonDate = Date.parse(jsonValue);

        if (factDate <= jsonDate) {
          return true;
        } else {
          return false;
        }
      });

      engine.addOperator("DateGreaterThanInclusive", (factValue, jsonValue) => {
        if (!factValue) return false;

        let factDate = Date.parse(factValue);
        let jsonDate = Date.parse(jsonValue);

        if (factDate >= jsonDate) {
          return true;
        } else {
          return false;
        }
      });

      // searching rules based on keys { product, incentive month } and isActive flag
      let rules = [];
      if (searchKeys !== undefined) {
        rules = await RuleEngineDB.collection("IncentiveRules")
          .find({
            searchKeys: searchKeys,
            isActive: true,
            criteria: criteria,
            operationType: operationType
          })
          .toArray();
  
      } else {
        rules = await RuleEngineDB.collection("IncentiveRules")
          .find({
            isActive: true,
            criteria: criteria,
            operationType: operationType
          })
          .toArray();
        
      }

      // adding rules to the engine
      rules.forEach(rule => {
        let ruleName = rule.name;
        let priority = rule.priority;
        rule.decisions.forEach(value => {
          engine.addRule({
            conditions: value.conditions,
            event: value.event,
            priority: priority,
            name: ruleName
          });
        });
      });

      // console.log('Number of Rules Processing: ', rules.length);

      // running engine instance based on facts 
      let result = engine
        .run(facts)
        .then(async (results) => {
          // console.log('events', results.events);

          let data = null;
          if (results.events.length === 0) {
            data = null;
          } else {

            let finalIncentive = 0;
            let initialApe = facts.ape;

            for (let index = 0; index < results.events.length; index++) {
              let event = results.events[index];
              let { EXPRESSION } = event.params;
              let params = event.params;
              let flag = 0, operationType='';
              if(params !== null && params.operationType !== undefined) {
                operationType = params.operationType
              }
              if(params.amount!== undefined) {
                flag =1;
              }
              let obj = { ...params, ...facts };
              let parsedExpression = expressionParser(EXPRESSION, obj);
              let expression = parsedExpression.result;

                let result = 0;

                if(parsedExpression.errorStatus === 0) {
                  result = evaluate(expression).toFixed(2);
                  facts.ape = result;

                  if(operationType === 'deduction' || operationType === 'addon') {
                    facts.incentive = result;
                  }                

                  if(flag == 1){
                    finalIncentive= parseFloat(finalIncentive) + parseFloat(result);
                  } else {
                    finalIncentive = result;
                  }
                }
                event.params.value = expression + " = " + result;    
              
            }

            facts.ape = initialApe;
            // console.log('Incentive', finalIncentive);

            // res.status(200).json({
            //   status: 200,
            //   message: 'resolve success',
            //   data: {
            //     rules: results,
            //     events: results.events,
            //     result: finalIncentive,
            //     facts: facts
            //   }
            // });
            data = {
              rules: results,
              events: results.events,
              result: finalIncentive,
              facts: facts
            }

          }
          return data;
        });
      return result;
    }
    // engine.addOperator("arrayobjectmatch", (factValue, jsonValue) => {
    //   if (!factValue) return false;
    //   var ar = jsonValue.split(":");
    //   var lpcount = 0;
    //   factValue.forEach(fact => {
    //     if (fact[ar[0]] == ar[1]) {
    //       lpcount = 1;
    //     }
    //   });
    //   if (lpcount == 1) {
    //     return true;
    //   }
    // });
    // engine.addOperator("stringcontains", (factValue, jsonValue) => {
    //   if (!factValue.length) return false;
    //   if (factValue.indexOf(jsonValue) != -1) return true;
    // });
    // engine.addOperator("DateCheckGreaterThan", (factValue, jsonValue) => {
    //   if (!factValue) return false;
    //   var a = jsonValue.split("-");
    //   var start = a[1];
    //   if (a[0].toLowerCase() == "year") {
    //     var year = new Date();
    //     var year1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (year - year1) / (1000 * 60 * 60 * 24 * 365)
    //     );
    //     if (diff > start) return true;
    //   } else if (a[0].toLowerCase() == "month") {
    //     var month = new Date();
    //     var month1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (month - month1) / (1000 * 60 * 60 * 24 * 30)
    //     );
    //     if (diff > start) return true;
    //   } else if (a[0].toLowerCase() == "date") {
    //     var date = new Date();
    //     var date1 = new Date();
    //     let diff = Math.ceil(
    //       (date - date1) / (1000 * 60 * 60 * 24)
    //     );
    //     if (diff > start) return true;
    //   } else return false;
    // });
    // engine.addOperator("DateCheckLessThan", (factValue, jsonValue) => {
    //   if (!factValue) return false;
    //   var a = jsonValue.split("-");
    //   var start = a[1];
    //   if (a[0].toLowerCase() == "year") {
    //     var year = new Date();
    //     let year1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (year - year1) / (1000 * 60 * 60 * 24 * 365)
    //     );
    //     if (diff < start) return true;
    //   } else if (a[0].toLowerCase() == "month") {
    //     var month = new Date();
    //     let month1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (month - month1) / (1000 * 60 * 60 * 24 * 30)
    //     );
    //     if (diff < start) return true;
    //   } else if (a[0].toLowerCase() == "date") {
    //     var date = new Date();
    //     let date1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (date - date1) / (1000 * 60 * 60 * 24)
    //     );
    //     if (diff < start) return true;
    //   } else return false;
    // });
    // engine.addOperator("DateCheckBetween", (factValue, jsonValue) => {
    //   if (!factValue) return false;
    //   var a = jsonValue.split("-");
    //   var start = a[1];
    //   var end = a[2];
    //   if (a[0].toLowerCase() == "year") {
    //     var year = new Date();
    //     let year1 = new Date(factValue)
    //     let diff = Math.ceil(
    //       (year - year1) / (1000 * 60 * 60 * 24 * 365)
    //     );
    //     if (diff > start && diff < end) return true;
    //   } else if (a[0].toLowerCase() == "month") {
    //     var month = new Date();
    //     let month1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (month - month1) / (1000 * 60 * 60 * 24 * 30)
    //     );
    //     if (diff > start && diff < end) return true;
    //   } else if (a[0].toLowerCase() == "date") {
    //     var date = new Date();
    //     let date1 = new Date(factValue);
    //     let diff = Math.ceil(
    //       (date - date1) / (1000 * 60 * 60 * 24)
    //     );
    //     if (diff > start && diff < end) return true;
    //   } else return false;
    // });
    // engine.addOperator("paramsMatch", (factValue, jsonValue) => {
    //   if (!factValue) return false;
    //   var arr = jsonValue.split(".");
    //   var j = req.body.requestData;
    //   for (var i = 0; i < arr.length; i++) {
    //     if (j != undefined && j != null) {
    //       j = j[arr[i]];
    //     } else {
    //       break;
    //     }
    //   }
    //   if (factValue == j) {
    //     return true;
    //   }
    //   return false;
    // });


    // var cursor = collection
    //   .find({
    //isActive: true,
    //action: { $in: [currentIntent, "Common"] },
    //AIAgentId: req.body.requestData.AIAgentId
    // })
    // .sort({ priority: -1 });

    // var cursorCount = cursor.count(function (err, count) {
    //   cursor.forEach(doc => {
    //     if (doc !== null) {
    //       var event = doc.event;
    //       engine.addRule({
    //         conditions: doc.conditions,
    //         event: event[0]
    //       });
    //       // var docFact = doc.fact;
    //       // engine.addFact(docFact, function (params, almanac) {
    //       //   return almanac.factValue("accountId").then(accountId => {
    //       //     return requestdataValue[accountId];
    //       //   });
    //       // });
    //       const facts = { accountActive: true }
    //       engine
    //         .run(facts)
    //         .then((events) => {
    //           events = events.events

    //           if (!events.length) {
    //             counter = counter + 1;
    //             if (counter == count) {
    //               res.json({message: 'rule failed'});
    //             }
    //           } else {
    //             events.map(event => event.params);
    //             count = 1;
    //             res.json(event);
    //           }
    //         })
    //         .catch(e => {
    //           console.log(e);
    //           counter = counter + 1;
    //           if (counter == count) {
    //             res.json(null);
    //           }
    //         });
    //     }
    //     });
    //   });


  } catch (err) {

    // res.status(500).send({
    //   status: 500,
    //   error: err
    // });
    //console.log('here');
    errorStatus = 1;
    return { errorStatus, err };
  }
  finally {
    // await collection.close();
  }
}



async function resolveRules(req, res) {
  try {

    let result = await resolve(req.body);
    // console.log('result', result)

    if (result && result.errorStatus === undefined) {
      res.status(200).json({
        status: 200,
        message: 'resolve success',
        data: result
      });
    } else if (result && result.errorStatus !== undefined) {
      res.status(500).send({
        status: 500,
        error: result.err
      });
    } else {
      res.status(404).send({
        status: 404,
        error: "No Rules applied"
      });
    }

  } catch (err) {
    res.status(500).send({
      status: 500,
      error: err
    });
  }

}



async function getFacts(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let facts = await RuleEngineDB.collection('Facts')
      .find({})
      .toArray();

    res.status(200).json({
      status: 200,
      message: 'success',
      data: facts
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}


async function addRule(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let rule = req.body;
    rule.priority = 1;
    let result = await RuleEngineDB.collection('IncentiveRules')
      .insertOne(rule);

    res.status(200).json({
      status: 200,
      message: 'success',
      data: result
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}


async function editRule(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let body = req.body;
    let ruleId = req.params.id;
    let result = await RuleEngineDB.collection('IncentiveRules')
      .updateOne(
        { _id: ObjectId(ruleId) },
        {
          $set: {
            decisions: body,
            isActive: true
          }
        }
      );

    res.status(200).json({
      status: 200,
      message: 'success',
      data: result
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}


async function editRuleDetails(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let body = req.body;
    let ruleId = req.params.id;

    let result = await RuleEngineDB.collection('IncentiveRules')
      .updateOne(
        { _id: ObjectId(ruleId) },
        {
          $set: {
            "name": body.name,
            "description": body.description,
            "searchKeys.1.value": body.incentiveMonth,
            "isActive": body.isActive,
            "priority": body.priority
          }
        }
      );

    res.status(200).json({
      status: 200,
      message: 'success',
      data: result
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}

async function getRuleParams(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let params = await RuleEngineDB.collection('RuleParams')
      .find({})
      .toArray();

    res.status(200).json({
      status: 200,
      message: 'success',
      data: params
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}

async function getFactsData(req, res) {
  try {
    let { key, datasource } = req.query;
    let result = await SQLMethods.getFactsDataSql(key, datasource);

    res.status(200).json({
      status: 200,
      message: 'success',
      data: result
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}

async function toggleFactIsActive(req, res) {
  try {
    let RuleEngineDB = ruleenginedb;
    let body = req.body;
    // console.log('body', body)
    let factId = body.id;
    let result = await RuleEngineDB.collection('Facts')
      .updateOne(
        { _id: ObjectId(factId) },
        {
          $set: {
            "isActive": body.newActiveState
          }
        }
      );
    res.status(200).json({
      status: 200,
      message: 'success',
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}

async function evaluateRule(req, res) {
  try {
    let { decisions, facts }= req.body;
    // console.log(JSON.stringify(req.body, null, 4));
    let engine = new Engine([], { allowUndefinedFacts: true });
    // custom operators

    engine.addOperator("DateGreaterThan", (factValue, jsonValue) => {
      if (!factValue) return false;

      let factDate = Date.parse(factValue);
      let jsonDate = Date.parse(jsonValue);
      if (factDate > jsonDate) {
        return true;
      } else {
        return false;
      }
    });


    engine.addOperator("DateLessThan", (factValue, jsonValue) => {
      if (!factValue) return false;
      let factDate = Date.parse(factValue);
      let jsonDate = Date.parse(jsonValue);
      if (factDate < jsonDate) {
        return true;
      } else {
        return false;
      }
    });


    engine.addOperator("DateLessThanInclusive", (factValue, jsonValue) => {
      if (!factValue) return false;

      let factDate = Date.parse(factValue);
      let jsonDate = Date.parse(jsonValue);
      if (factDate <= jsonDate) {
        return true;
      } else {
        return false;
      }
    });


    engine.addOperator("DateGreaterThanInclusive", (factValue, jsonValue) => {
      if (!factValue) return false;
      let factDate = Date.parse(factValue);
      let jsonDate = Date.parse(jsonValue);

      if (factDate >= jsonDate) {
        return true;
      } else {
        return false;
      }

    });

    

    decisions.forEach(value => {
      engine.addRule({
        conditions: value.conditions,
        event: value.event
      });
    });


    // running engine instance based on facts 

    engine
      .run(facts)
      .then(async (results) => {

        let data = null;
        if (results.events.length === 0) {
          data = null;
        } else {

          let finalIncentive = 0;
          let initialApe = facts.ape;

          for (let index = 0; index < results.events.length; index++) {
            let event = results.events[index];
            let { EXPRESSION } = event.params;
            let params = event.params;
            let flag = 0, operationType='';

            if(params !== null && params.operationType !== undefined) {
              operationType = params.operationType
            }

            if(params.amount!== undefined) {
              flag =1;
            }


            let obj = { ...params, ...facts };
            let parsedExpression = expressionParser(EXPRESSION, obj);           

            let expression = parsedExpression.result;
            let result = 0;
            if(parsedExpression.errorStatus === 0) {
              result = evaluate(expression).toFixed(2);
              facts.ape = result;
              // if(operationType === 'deduction' || operationType === 'addon') {

              //   facts.incentive = result;

              // }             

              if(flag == 1){
                finalIncentive= parseFloat(finalIncentive) + parseFloat(result);
              } else {
                finalIncentive = result;
              }
            }
            event.params.value = expression + " = " + result;   

            }
          facts.ape = initialApe;

          data = {
            events: results.events,
            result: finalIncentive,
          }
        }

        // console.log(data);
        res.status(200).json({
          status: 200,
          message: 'success',
          data: data
        });
     });

  } catch (err) {


    res.status(500).json({
      status: 500,
      message: err,
    })
  }
}



module.exports = {
  getRules: getRules,
  getRulebyId: getRulebyId,
  SaveCondition: SaveCondition,
  SaveRule: SaveRule,
  factCheck: factCheck,
  resolveRules: resolveRules,
  getFacts: getFacts,
  addRule: addRule,
  editRule: editRule,
  getRuleParams: getRuleParams,
  getFactsData: getFactsData,
  editRuleDetails: editRuleDetails,
  resolve: resolve,
  toggleFactIsActive : toggleFactIsActive,
  evaluateRule: evaluateRule
};
