
import React from "react";
import { Alert } from 'react-bootstrap';


class Loading extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            show: this.props.show
        }
    }
    componentDidUpdate() {

    }
    componentWillReceiveProps(nextProps) {
        this.setState({ show: nextProps.show });        
    }
    render() {
        if (this.state.show) {
            return (
                <div className="PageLoading">
                    <span>
                        Loading...
                    </span>
                </div>
            );
        }
        else {
            return null;
        }
    }
}

export default Loading;
