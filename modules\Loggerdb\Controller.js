const AWS = require('aws-sdk');
const {Kafka}= require('kafkajs');
const config = require('../../env_config');
const {LogKafka}= require('./Methods');

async function LogData(req, res) {

  try {
    let body = req.body;
    let data = await <PERSON>g<PERSON>(body);

    if (data.errorStatus !== undefined && data.errorStatus === 0) {
      res.status(200).json({
        status: 200,
        message: 'success',
        data: data.result
      })
    } else {
      res.status(500).json({
        status: 500,
        data: data.message
      })
    }

  } catch (err) {
    console.log("Error in LogData: ",err);
    res.status(500).json({
      status: 500,
      data: err.message
    })
  }

}

async function Logger(data) {
  try {

    let errorStatus = 0;
    // let LoggerDB = loggerdb;
    let result = ""//await LoggerDB.collection('Log_Collection').insertOne(data);
    return { errorStatus, result };

  } catch (err) {

    let errorStatus = 1;
    console.log(err);
    return { errorStatus, message: err.message };

  }
}

async function LogConsole(data) {
  try {

    let errorStatus = 0;
    // let LoggerDB = loggerdb;
    let result = ""//await LoggerDB.collection('MatrixDashboard_logs').insertOne(data);
    return { errorStatus, result };

  } catch (err) {

    let errorStatus = 1;
    console.log('LogConsole: ', err);
    return { errorStatus, message: err.message };

  }
}


async function LogSqs(data) {
  try {

    const QueueUrl = config.AWS_SQS_LOGGING;

    if(QueueUrl) {
      AWS.config.update({ region: 'ap-south-1' });
      const sqs = new AWS.SQS({ apiVersion: '2022-11-05' });

      const params = {
        MessageBody: JSON.stringify(data),
        QueueUrl: QueueUrl
      };
      let sendSqsMessage = sqs.sendMessage(params).promise();
  
      sendSqsMessage.then((data) => {
       // console.log('success', data);
       // console.log(`MessageId | SUCCESS: ${data.MessageId}`);
      }).catch((err) => {
        console.log(`SQS LOG | ERROR: ${err}`);
      });
    }
     
    return;
  } catch (err) {
    console.log('INSIDE LOG_SQS: ', err);
    return;
  }
}

module.exports = {
  LogData: LogData,
  Logger: Logger,
  LogConsole,
  LogSqs: LogSqs
}

