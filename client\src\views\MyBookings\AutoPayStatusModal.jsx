import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import { PostCommonApiData } from "../../store/actions/CommonAction";
import config from "../../config";

const AutoPayStatusModal = (props) => {
  const { current, show } = props || {};
  const [IsLoading, setIsLoading] = useState(true);
  const [AutoPayStatus, setAutoPayStatus] = useState("");

  const Url = config.api.base_url + '/common/PostCommonApi/AutoPayStatusModal';

  useEffect(() => {
    const handleAutoPayStatus = async () => {
      if (current && current.BookingID && show) {
        let { errorStatus, data } = await PostCommonApiData({
          Url,
          Data: { Type: 'AUTO_PAY_STATUS', Data: { BookingID: current.BookingID, IsSI: current.IsSI } },
          headers: {}
        });
  
        if (errorStatus === 0) {
          const status = data && data.data && data.data.autoPayStatus || "";
          props.handleUpdateAutoPayData({
            BookingID: current.BookingID,
            PrevStatus: current.IsSI,
            CurrentStatus: status
          })
          setAutoPayStatus(status);
          setIsLoading(false);
        } else {
          setAutoPayStatus("Could Not Fetch!!");
          setIsLoading(false);
        }
      }
  
    }
    handleAutoPayStatus();
  }
    , [current]);

  return (
    <>
      <Modal
        {...props}
        //size="lg"
        //aria-labelledby="contained-modal-title-vcenter"
        centered
        onHide={props.onAutoPayStatusCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            Auto Pay Status
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>

          {
            IsLoading ? <>Loading...</> :
              <>
                <p>BookingID: {current?.BookingID}</p>
                <p>Status: {AutoPayStatus}</p>
              </>
          }
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={props.onAutoPayStatusCancel}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default AutoPayStatusModal;