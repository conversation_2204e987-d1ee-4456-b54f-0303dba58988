<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="125.999" height="96.385" viewBox="0 0 125.999 96.385"><defs><style>.a{opacity:0.12;fill:url(#a);}.b{fill:#263238;}.c{fill:#ffd1bd;}.d{fill:#7c3b29;}.e{fill:#db9987;}.f{fill:#e8505b;}.g,.l{opacity:0.3;}.h{fill:#455a64;}.i{fill:#a1373f;}.j{fill:#519d9b;}.k{fill:#509c9a;}.l{fill:#86d6d0;}.m{fill:#fafafa;}.n{fill:#6fc8c2;}.o{fill:#70c9c3;}.p{opacity:0.2;}.q{fill:#cc8f75;}.r{opacity:0.78;}</style><linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#6fc8c2"/></linearGradient></defs><g transform="translate(-266 -129.168)"><path class="a" d="M77.009,173.107a63.9,63.9,0,0,1-63-53.108h126a63.339,63.339,0,0,1-7.563,21.018,63.817,63.817,0,0,1-55.436,32.089Z" transform="translate(251.99 52.446)"/><g transform="translate(264.509 75.994)"><g transform="translate(22.491 124.723)"><path class="b" d="M104.491,461.531c0,.026-18.358.048-41,.048s-41-.021-41-.048,18.354-.048,41-.048S104.491,461.5,104.491,461.531Z" transform="translate(-22.491 -461.483)"/></g><g transform="translate(56.185 73.814)"><g transform="translate(0 0)"><g transform="translate(33.981 7.292)"><path class="c" d="M400.873,227.292c-.144-.623.591-2.774.116-2.822-.6-.061-.707,2.385-.728,2.6s-.339.188-.309-.009c.043-.283.446-3.051-.164-3.058-.338,0-.352.612-.352.612s-.228,2.728-.516,2.715-.082-1.565-.053-2.143c.028-.554-.249-.871-.454-.632a6.3,6.3,0,0,0-.336,2.31c-.037.554-.186,2.145-.757,1.349a2.565,2.565,0,0,0-1.574-1.171c-.27.028-.321.417-.058.569a3.654,3.654,0,0,1,.715.682,3.492,3.492,0,0,1,.412.676,3.054,3.054,0,0,1,.3.964,1.9,1.9,0,0,0,.841,1.31l-.032,4.37-.136,2.162,3.4,1.18-.447-7.711a28.756,28.756,0,0,1,1.058-4.225c.38-1.385.2-1.445.033-1.453C401.458,225.558,401.013,227.9,400.873,227.292Z" transform="translate(-395.515 -224.008)"/><g transform="translate(2.514 5.149)"><path class="d" d="M411.611,252.057a3.15,3.15,0,0,0-.417.07,1.583,1.583,0,0,0-1,.837l-.022.045-.033-.034a2.392,2.392,0,0,0-.645-.471c-.18-.09-.3-.126-.293-.136a.964.964,0,0,1,.312.094,2.148,2.148,0,0,1,.675.462l-.055.011a1.589,1.589,0,0,1,.171-.284,1.623,1.623,0,0,1,.437-.393,1.545,1.545,0,0,1,.439-.181,1.3,1.3,0,0,1,.314-.035A.336.336,0,0,1,411.611,252.057Z" transform="translate(-409.204 -252.042)"/></g><g transform="translate(3.276 7.207)"><path class="d" d="M414.828,263.251a2.538,2.538,0,0,1-1.478.048c0-.021.333.031.74.016S414.823,263.23,414.828,263.251Z" transform="translate(-413.35 -263.247)"/></g></g><g transform="translate(12.085)"><g transform="translate(6.081)"><path class="b" d="M309.413,187.408a2.557,2.557,0,0,1,2.093-2.3,3.324,3.324,0,0,0,.863-.16,4.976,4.976,0,0,0,.6-.4,1.708,1.708,0,0,1,2.112.285c.488.538.613-.269,1.711.282a.871.871,0,0,1,.506,1.211,1.96,1.96,0,0,1,2.05,1.408c.144.556.078,1.249.544,1.585.226.163.526.191.758.345a1.456,1.456,0,0,1,.433,1.407,2.836,2.836,0,0,0,.047,1.528c.365.765,1.508,1,1.707,1.821.094.389-.038.873.257,1.144a1.363,1.363,0,0,0,.43.212,1.788,1.788,0,0,1,.294,3.162c-.153.1-.337.192-.386.367-.054.2.086.384.19.559a1.719,1.719,0,0,1-2.279,2.4,2.689,2.689,0,0,1-1.486,1.364,1.632,1.632,0,0,1-.934.092,1.868,1.868,0,0,1-.672-.351c-.969-.729-2.335-1.853-2.506-3.054" transform="translate(-309.413 -184.305)"/></g><g transform="translate(6.016 2.95)"><path class="c" d="M309.1,202.387l-.044-.86,5.135-1.143a3.438,3.438,0,0,1,3.51,2.92,28.478,28.478,0,0,1,.131,5.274,3.32,3.32,0,0,1-2.372,3.08l.071,2.645-5.89.257Z" transform="translate(-309.057 -200.364)"/></g><g transform="translate(13.012 7.528)"><path class="b" d="M347.831,225.588a.342.342,0,0,1-.319.355.327.327,0,0,1-.362-.3.342.342,0,0,1,.319-.355A.327.327,0,0,1,347.831,225.588Z" transform="translate(-347.149 -225.289)"/></g><g transform="translate(12.781 6.701)"><path class="b" d="M347.24,221.126c-.041.046-.306-.138-.673-.125s-.631.2-.673.161c-.02-.019.02-.1.135-.19a.936.936,0,0,1,.537-.184.9.9,0,0,1,.538.155C347.219,221.026,347.26,221.106,347.24,221.126Z" transform="translate(-345.889 -220.787)"/></g><g transform="translate(9.58 7.7)"><path class="b" d="M329.143,226.526a.342.342,0,0,1-.319.355.327.327,0,0,1-.362-.3.342.342,0,0,1,.319-.355A.327.327,0,0,1,329.143,226.526Z" transform="translate(-328.462 -226.227)"/></g><g transform="translate(9.169 6.881)"><path class="b" d="M327.573,222.11c-.041.046-.306-.138-.674-.125s-.631.2-.673.161c-.02-.019.02-.1.135-.19a.936.936,0,0,1,.537-.184.9.9,0,0,1,.538.155C327.552,222.01,327.593,222.09,327.573,222.11Z" transform="translate(-326.222 -221.771)"/></g><g transform="translate(11.672 6.847)"><path class="b" d="M340.119,224.643a2.438,2.438,0,0,1,.594-.126c.094-.013.182-.034.2-.1a.477.477,0,0,0-.071-.276l-.3-.706a12.255,12.255,0,0,1-.682-1.851,12.328,12.328,0,0,1,.829,1.792l.288.711a.544.544,0,0,1,.06.367.235.235,0,0,1-.15.142.639.639,0,0,1-.159.027A2.391,2.391,0,0,1,340.119,224.643Z" transform="translate(-339.852 -221.585)"/></g><g transform="translate(8.73 13.408)"><path class="e" d="M327.524,258.148a7.057,7.057,0,0,1-3.689-.844,3.531,3.531,0,0,0,3.708,1.508Z" transform="translate(-323.835 -257.304)"/></g><g transform="translate(10.721 10.563)"><path class="d" d="M335.764,242.048a.663.663,0,0,0-.6-.228.605.605,0,0,0-.411.237.384.384,0,0,0-.016.439.445.445,0,0,0,.486.107,1.393,1.393,0,0,0,.461-.3.406.406,0,0,0,.1-.112.123.123,0,0,0,0-.137" transform="translate(-334.674 -241.814)"/></g><g transform="translate(10.811 10.194)"><path class="b" d="M335.217,239.8c.06-.005.073.394.423.666s.774.216.779.271c.006.025-.092.08-.272.091a1,1,0,0,1-.649-.2.865.865,0,0,1-.329-.56C335.148,239.9,335.189,239.8,335.217,239.8Z" transform="translate(-335.163 -239.805)"/></g><g transform="translate(8.897 5.522)"><path class="b" d="M326.417,214.624c-.033.1-.4.065-.831.13s-.775.194-.835.106c-.027-.042.031-.138.168-.239a1.462,1.462,0,0,1,1.27-.18C326.349,214.5,326.431,214.576,326.417,214.624Z" transform="translate(-324.744 -214.37)"/></g><g transform="translate(12.71 5.618)"><path class="b" d="M346.755,215.254c-.062.086-.318.007-.623.012s-.561.073-.623-.012c-.027-.042.011-.127.121-.209a.9.9,0,0,1,1,0C346.745,215.128,346.783,215.213,346.755,215.254Z" transform="translate(-345.501 -214.893)"/></g><g transform="translate(5.707 2.37)"><path class="b" d="M316.781,197.46c-.361.96-1.22,2.412-2.231,2.585a3.012,3.012,0,0,1-2.76-1.012c-.117.766-.939,1.21-1.7,1.372a3.046,3.046,0,0,1-1.623-.03,1.613,1.613,0,0,1-1.083-1.137,2.51,2.51,0,0,1,1.129-2.03" transform="translate(-307.376 -197.207)"/></g><g transform="translate(0 2.428)"><path class="b" d="M284.1,211.889a2.1,2.1,0,0,1-1.4,1.968,1.487,1.487,0,0,1-2.831.783,1.783,1.783,0,0,0-.249-.488c-.284-.311-.786-.209-1.2-.31a1.722,1.722,0,0,1-1.084-1.8,1.757,1.757,0,0,0-.011-.447,1.987,1.987,0,0,0-.513-.749,2.974,2.974,0,0,1,.131-3.507c.244-.341.558-.7.5-1.118a2.245,2.245,0,0,0-.274-.643,1.9,1.9,0,0,1,.363-2.107.6.6,0,0,0,.2-.28.578.578,0,0,0-.017-.22,2.224,2.224,0,0,1,1.706-2.5.555.555,0,0,0,.193-.058c.164-.1.153-.337.162-.53a3.553,3.553,0,0,1,.709-1.839,1.786,1.786,0,0,1,2.495,0,26.956,26.956,0,0,1,.9,4.274,9.681,9.681,0,0,0-.218,3.167,6.25,6.25,0,0,1,.428,3.017A14.357,14.357,0,0,0,284.1,211.889Z" transform="translate(-276.303 -197.523)"/></g></g><g transform="translate(0 35.472)"><g transform="translate(24.549 10.782)"><g transform="translate(0 0.416)"><path class="f" d="M345.958,438.4s7.777,2.152,8.089,2.262c.469.165.39,1.881-.339,1.855-.345-.013-5.973.21-6.684.165a17.152,17.152,0,0,1-2.857-.89Z" transform="translate(-344.166 -438.399)"/></g><g transform="translate(5.016 1.829)"><path class="b" d="M371.49,447.65a3.409,3.409,0,0,1,.893-1.551,1.728,1.728,0,0,0-.893,1.551Z" transform="translate(-371.474 -446.096)"/></g><g transform="translate(4.208 1.529)"><path class="b" d="M367.09,445.947c.038.007.117-.361.324-.767s.452-.691.424-.717a1.95,1.95,0,0,0-.747,1.485Z" transform="translate(-367.078 -444.461)"/></g><g transform="translate(3.409 1.26)"><path class="b" d="M362.775,444.549a2.728,2.728,0,0,1,.605-1.549,1.46,1.46,0,0,0-.605,1.549Z" transform="translate(-362.724 -442.998)"/></g><g transform="translate(2.981)"><path class="b" d="M361.014,437.943a1.376,1.376,0,0,0,.083-.579,2.717,2.717,0,0,0-.079-.634,2.676,2.676,0,0,0-.116-.361.433.433,0,0,0-.125-.19.207.207,0,0,0-.267.013.343.343,0,0,0-.1.22,1.51,1.51,0,0,0-.01.205,1.716,1.716,0,0,0,.048.381,2.32,2.32,0,0,0,.239.595,1.569,1.569,0,0,0,.349.468c.018-.012-.112-.193-.268-.511a2.547,2.547,0,0,1-.2-.578,1.718,1.718,0,0,1-.037-.355c0-.12,0-.269.072-.326a.075.075,0,0,1,.1-.009.327.327,0,0,1,.085.136,2.845,2.845,0,0,1,.118.343,3.126,3.126,0,0,1,.1.607C361.028,437.72,360.993,437.94,361.014,437.943Z" transform="translate(-360.397 -436.136)"/></g><g transform="translate(2.032 1.647)"><path class="b" d="M356.786,445.184a1.089,1.089,0,0,0-.5-.081,2.263,2.263,0,0,0-.552.068,1.214,1.214,0,0,0-.318.117.328.328,0,0,0-.176.355.389.389,0,0,0,.28.278.713.713,0,0,0,.349,0,1.341,1.341,0,0,0,.505-.248,1.371,1.371,0,0,0,.362-.356,3.43,3.43,0,0,0-.414.281,1.379,1.379,0,0,1-.477.206.614.614,0,0,1-.288,0,.263.263,0,0,1-.189-.185.2.2,0,0,1,.116-.217,1.183,1.183,0,0,1,.282-.11,2.606,2.606,0,0,1,.524-.091C356.592,445.174,356.783,445.2,356.786,445.184Z" transform="translate(-355.23 -445.103)"/></g><g transform="translate(6.166 2.152)"><path class="b" d="M377.8,449.169a2.1,2.1,0,0,1,.534-1.31,1.116,1.116,0,0,0-.534,1.31Z" transform="translate(-377.736 -447.855)"/></g><g transform="translate(0.8 3.823)"><path class="b" d="M348.521,456.953a1.634,1.634,0,0,0,.3.142,6.669,6.669,0,0,0,.865.268,12.35,12.35,0,0,0,2.969.281c1.165-.013,2.216-.066,2.977-.1l.9-.048a1.653,1.653,0,0,0,.33-.035,1.616,1.616,0,0,0-.332-.015l-.9.006-2.975.058a13.9,13.9,0,0,1-2.948-.241C348.964,457.12,348.53,456.929,348.521,456.953Z" transform="translate(-348.521 -456.951)"/></g><g transform="translate(0.57 3.341)"><path class="b" d="M347.268,454.989c.011.018.223-.106.583-.264a2.71,2.71,0,0,1,1.472-.245,2.332,2.332,0,0,1,1.272.757,4.544,4.544,0,0,0,.448.458,2.425,2.425,0,0,0-.381-.52,3.082,3.082,0,0,0-.516-.482,1.846,1.846,0,0,0-.8-.34,2.571,2.571,0,0,0-1.531.29A2.067,2.067,0,0,0,347.268,454.989Z" transform="translate(-347.268 -454.327)"/></g><g class="g" transform="translate(0 0.416)"><path d="M345.958,438.4s7.777,2.152,8.089,2.262c.469.165.39,1.881-.339,1.855-.345-.013-5.973.21-6.684.165a17.152,17.152,0,0,1-2.857-.89Z" transform="translate(-344.166 -438.399)"/></g></g><g transform="translate(0 2.265)"><path class="b" d="M228.207,389.767l-1.679,3.45s-8.976-2.73-11.238-2.6-4.114,1.032-4.554,2.868-.5,4.371,2.139,6.131,15.752,2.731,15.752,2.731h5.189L242.3,391.16l-1.366-1.108Z" transform="translate(-210.506 -389.767)"/></g><g transform="translate(4.836 4.587)"><path class="h" d="M252.522,407.654a.968.968,0,0,1-.17,0c-.111-.009-.277-.008-.487-.038a10.117,10.117,0,0,1-1.759-.363,16.908,16.908,0,0,1-2.481-.97c-.907-.428-1.875-.955-2.893-1.5a29.938,29.938,0,0,0-2.93-1.405,13.125,13.125,0,0,0-2.535-.758,8.947,8.947,0,0,0-1.777-.155c-.211,0-.375.018-.486.019a.941.941,0,0,1-.171,0,.977.977,0,0,1,.168-.025c.111-.008.275-.038.487-.04a8.321,8.321,0,0,1,1.792.12,12.751,12.751,0,0,1,2.56.74,28.528,28.528,0,0,1,2.945,1.4c1.018.547,1.983,1.075,2.884,1.507a17.649,17.649,0,0,0,2.459.988,11.114,11.114,0,0,0,1.742.4c.208.036.373.042.483.059A.987.987,0,0,1,252.522,407.654Z" transform="translate(-236.833 -402.407)"/></g><g transform="translate(19.729)"><path class="b" d="M324.85,391.363c1.667-.655,10.835-3.572,14.081-7.323a3.589,3.589,0,0,0,.889-3.113c-.453-2.5-2.753-4.169-5.14-3.224-.339.134-1.119.437-1.445.613-.89.478-15.313,9.625-15.313,9.625s2.513,4.9,2.45,5.089Z" transform="translate(-317.922 -377.435)"/></g><g transform="translate(19.807 3.054)"><path class="h" d="M329.711,394.061s-.012.012-.039.031l-.121.082-.47.307-1.742,1.11-5.768,3.625-3.133,1.954.02-.064,1.643,3.953.462,1.134.119.3a.056.056,0,1,1-.016.007l-.134-.3-.489-1.123-1.681-3.937-.017-.041.038-.024,3.129-1.961,5.785-3.6,1.76-1.079.482-.289.127-.073A.17.17,0,0,1,329.711,394.061Z" transform="translate(-318.345 -394.061)"/></g><g transform="translate(16.033 5.569)"><path class="h" d="M300.271,409.566a1.5,1.5,0,0,1-.426-.16,5.963,5.963,0,0,1-1.764-1.292,1.5,1.5,0,0,1-.281-.357c.026-.024.485.5,1.17.994S300.286,409.534,300.271,409.566Z" transform="translate(-297.799 -407.756)"/></g><g transform="translate(23.432 3.313)"><path class="h" d="M338.148,395.475a3.479,3.479,0,0,1,.022.574c0,.354.019.842.067,1.378s.123,1.019.181,1.368a3.465,3.465,0,0,1,.082.569,2.508,2.508,0,0,1-.16-.553,10.671,10.671,0,0,1-.25-2.763A2.519,2.519,0,0,1,338.148,395.475Z" transform="translate(-338.082 -395.475)"/></g><g transform="translate(24.003 3.191)"><path class="h" d="M341.76,394.81c0-.005.043-.01.113.015a.507.507,0,0,1,.239.2.551.551,0,0,1,.068.462.5.5,0,0,1-.465.327.489.489,0,0,1-.491-.287.511.511,0,0,1,.064-.463.447.447,0,0,1,.251-.186c.073-.018.115-.005.114,0a.534.534,0,0,0-.3.229.443.443,0,0,0-.035.381.391.391,0,0,0,.39.213.4.4,0,0,0,.373-.249.486.486,0,0,0-.039-.387A.678.678,0,0,0,341.76,394.81Z" transform="translate(-341.192 -394.806)"/></g><g transform="translate(17.31 2.832)"><path class="h" d="M307.575,392.854a.492.492,0,0,1,.079.17,1.546,1.546,0,0,1,.075.507,1.825,1.825,0,0,1-.167.744,1.668,1.668,0,0,1-1.491,1,1.826,1.826,0,0,1-.752-.126,1.546,1.546,0,0,1-.441-.261c-.091-.08-.132-.133-.128-.137a2.763,2.763,0,0,0,.6.324,1.855,1.855,0,0,0,.719.1,1.613,1.613,0,0,0,1.4-.943,1.855,1.855,0,0,0,.181-.7A2.757,2.757,0,0,0,307.575,392.854Z" transform="translate(-304.749 -392.854)"/></g><g transform="translate(27.485 2.844)"><path class="h" d="M361.355,394.613a.594.594,0,0,1-.34-.048,1.289,1.289,0,0,1-.86-1.317.593.593,0,0,1,.093-.331c.018,0-.016.13-.013.333a1.355,1.355,0,0,0,.81,1.241C361.229,394.576,361.358,394.595,361.355,394.613Z" transform="translate(-360.15 -392.917)"/></g><g transform="translate(32.052 0.045)"><path class="h" d="M386.137,389.031a.408.408,0,0,1,.055-.03l.165-.08.636-.306c.551-.268,1.349-.654,2.312-1.175a17.7,17.7,0,0,0,3.192-2.124,9.439,9.439,0,0,0,1.527-1.663,3.691,3.691,0,0,0,.553-2.306,3.624,3.624,0,0,0-.3-1.181,3.165,3.165,0,0,0-.3-.52,4.188,4.188,0,0,0-.378-.454,10.026,10.026,0,0,0-.858-.774,2.585,2.585,0,0,0-.936-.559,3.49,3.49,0,0,0-1.038-.115,5.338,5.338,0,0,0-.985.109,6.015,6.015,0,0,0-1.662.628l-2.269,1.257-.618.34-.162.087a.375.375,0,0,1-.056.027.391.391,0,0,1,.053-.034l.158-.093.611-.352c.533-.3,1.3-.747,2.258-1.279a6.033,6.033,0,0,1,1.675-.642,5.391,5.391,0,0,1,1-.114,3.552,3.552,0,0,1,1.058.115,2.649,2.649,0,0,1,.961.57,10.047,10.047,0,0,1,.866.778,4.279,4.279,0,0,1,.385.462,3.232,3.232,0,0,1,.3.532,3.694,3.694,0,0,1,.31,1.205,3.757,3.757,0,0,1-.566,2.353,9.451,9.451,0,0,1-1.543,1.674,17.484,17.484,0,0,1-3.212,2.118c-.968.516-1.77.894-2.325,1.155l-.642.294-.168.074A.378.378,0,0,1,386.137,389.031Z" transform="translate(-385.015 -377.679)"/></g><g transform="translate(11.916 10.812)"><g transform="translate(0 0.433)"><path class="i" d="M283.8,438.663s-7.8,2.056-8.116,2.162c-.471.159-.413,1.876.316,1.859.345-.008,5.97.283,6.682.248a14.7,14.7,0,0,0,2.635-.768Z" transform="translate(-275.381 -438.663)"/></g><g transform="translate(4.268 1.796)"><path class="b" d="M299.494,447.647a7.278,7.278,0,0,0-.332-.839,7.376,7.376,0,0,0-.541-.723,1.728,1.728,0,0,1,.873,1.562Z" transform="translate(-298.62 -446.083)"/></g><g transform="translate(5.227 1.508)"><path class="b" d="M304.57,446.007c-.038.006-.112-.363-.314-.771s-.443-.7-.415-.722a1.95,1.95,0,0,1,.729,1.494Z" transform="translate(-303.839 -444.512)"/></g><g transform="translate(6.133 1.25)"><path class="b" d="M309.361,444.668a2.73,2.73,0,0,0-.586-1.556,1.46,1.46,0,0,1,.586,1.556Z" transform="translate(-308.773 -443.109)"/></g><g transform="translate(6.519)"><path class="b" d="M310.952,438.106a1.378,1.378,0,0,1-.076-.58,2.726,2.726,0,0,1,.086-.633,2.691,2.691,0,0,1,.12-.359.434.434,0,0,1,.127-.189.207.207,0,0,1,.267.016.344.344,0,0,1,.1.221,1.5,1.5,0,0,1,.008.206,1.713,1.713,0,0,1-.053.38,2.308,2.308,0,0,1-.247.591,1.57,1.57,0,0,1-.355.464c-.018-.013.115-.192.274-.508a2.551,2.551,0,0,0,.211-.576,1.71,1.71,0,0,0,.042-.355c0-.12,0-.269-.068-.327a.075.075,0,0,0-.1-.01.33.33,0,0,0-.087.135,2.866,2.866,0,0,0-.122.342,3.131,3.131,0,0,0-.11.606C310.94,437.883,310.973,438.1,310.952,438.106Z" transform="translate(-310.875 -436.304)"/></g><g transform="translate(6.606 1.649)"><path class="b" d="M311.349,445.355a1.09,1.09,0,0,1,.5-.075,2.266,2.266,0,0,1,.551.074,1.217,1.217,0,0,1,.316.121.328.328,0,0,1,.171.358.388.388,0,0,1-.283.274.712.712,0,0,1-.349-.005,1.341,1.341,0,0,1-.5-.255,1.37,1.37,0,0,1-.357-.36,3.416,3.416,0,0,1,.411.286,1.38,1.38,0,0,0,.475.212.612.612,0,0,0,.288,0,.263.263,0,0,0,.192-.183.2.2,0,0,0-.113-.218,1.186,1.186,0,0,0-.281-.114,2.607,2.607,0,0,0-.523-.1C311.543,445.347,311.352,445.376,311.349,445.355Z" transform="translate(-311.349 -445.28)"/></g><g transform="translate(3.427 2.109)"><path class="b" d="M294.561,449.106a2.1,2.1,0,0,0-.518-1.317,1.115,1.115,0,0,1,.518,1.317Z" transform="translate(-294.042 -447.785)"/></g><g transform="translate(1.019 3.853)"><path class="b" d="M289.279,457.282a1.593,1.593,0,0,1-.3.138,6.631,6.631,0,0,1-.869.258,12.334,12.334,0,0,1-2.972.245c-1.165-.027-2.215-.093-2.976-.133l-.9-.059a.166.166,0,1,1,0-.051l.9.018,2.974.095a13.916,13.916,0,0,0,2.951-.2C288.834,457.444,289.271,457.258,289.279,457.282Z" transform="translate(-280.93 -457.28)"/></g><g transform="translate(5.813 3.352)"><path class="b" d="M310.812,455.237c-.011.018-.222-.109-.579-.271a2.707,2.707,0,0,0-1.468-.263,2.331,2.331,0,0,0-1.281.741,4.545,4.545,0,0,1-.454.452,2.419,2.419,0,0,1,.387-.515,3.087,3.087,0,0,1,.522-.476,1.844,1.844,0,0,1,.808-.331,2.572,2.572,0,0,1,1.527.309A2.061,2.061,0,0,1,310.812,455.237Z" transform="translate(-307.028 -454.554)"/></g><g transform="translate(1.46 2.348)"><path class="b" d="M283.66,451.016c-.014-.01.054-.112.117-.293a1.424,1.424,0,0,0-.24-1.4c-.12-.149-.218-.223-.208-.237s.122.042.264.187a1.327,1.327,0,0,1,.253,1.474C283.761,450.935,283.669,451.023,283.66,451.016Z" transform="translate(-283.328 -449.089)"/></g></g></g><g transform="translate(13.05 16.116)"><g transform="translate(2.616)"><g transform="translate(0.297)"><path class="j" d="M301.432,272.348l-4.012,1.124,1.3,12.481s-.75,4.456-.75,5.627a2.327,2.327,0,0,0,1.184,2.093l12.815.387.655-.567a1.849,1.849,0,0,0,.428-1.43l-2.381-19.044-4.461-.968Z" transform="translate(-297.42 -272.051)"/></g><g transform="translate(0 4.469)"><path class="b" d="M297.417,305.8a.481.481,0,0,1-.024-.094l-.054-.273-.185-1.007-.582-3.327-.56-3.331-.162-1.011-.04-.276a.5.5,0,0,1-.009-.1.466.466,0,0,1,.024.094l.054.273.185,1.007.582,3.328.56,3.331.161,1.011.04.276A.463.463,0,0,1,297.417,305.8Z" transform="translate(-295.801 -296.383)"/></g><g transform="translate(3.914 0.227)"><path class="b" d="M322.853,273.287a1.432,1.432,0,0,1-.185.146c-.062.044-.142.09-.234.148s-.2.113-.328.163a4.328,4.328,0,0,1-.91.284,6.736,6.736,0,0,1-1.16.121,11.525,11.525,0,0,1-1.165-.036,6.732,6.732,0,0,1-.944-.124,2.829,2.829,0,0,1-.61-.2,1.652,1.652,0,0,1-.154-.084c-.034-.021-.051-.033-.05-.036s.076.035.215.093a3.223,3.223,0,0,0,.609.179,7.407,7.407,0,0,0,.938.107c.356.023.748.04,1.158.03a7.116,7.116,0,0,0,1.149-.114,4.565,4.565,0,0,0,.9-.265A5.446,5.446,0,0,0,322.853,273.287Z" transform="translate(-317.113 -273.286)"/></g><g transform="translate(5.212 18.448)"><path class="b" d="M332.765,373.517a.388.388,0,0,1-.087.018l-.252.036c-.109.016-.244.033-.4.046s-.335.035-.532.044c-.395.028-.867.041-1.39.033s-1.1-.046-1.7-.117-1.167-.176-1.679-.287-.966-.24-1.343-.36c-.19-.056-.358-.118-.507-.169s-.275-.1-.378-.14l-.237-.095a.4.4,0,0,1-.08-.038.407.407,0,0,1,.086.024l.241.082c.1.037.232.081.382.127s.318.105.509.158c.378.114.833.231,1.343.344s1.076.207,1.673.28,1.171.106,1.692.122.992,0,1.386-.017c.2-.006.375-.023.532-.033s.291-.023.4-.034l.254-.022A.4.4,0,0,1,332.765,373.517Z" transform="translate(-324.179 -372.49)"/></g><g transform="translate(14.27 4.223)"><path class="b" d="M374.507,299.594a.875.875,0,0,1-.061-.173c-.035-.113-.082-.277-.137-.481-.111-.408-.254-.973-.394-1.6s-.251-1.2-.323-1.619c-.036-.208-.063-.377-.08-.494a.855.855,0,0,1-.018-.183.868.868,0,0,1,.047.178l.1.489c.085.413.2.984.344,1.612s.274,1.2.372,1.606l.114.487A.88.88,0,0,1,374.507,299.594Z" transform="translate(-373.492 -295.042)"/></g></g><g transform="translate(0 0.864)"><g class="g" transform="translate(7.43)"><path d="M322.9,276.753a14.893,14.893,0,0,1,.09,1.87,26.645,26.645,0,0,1,.321,6.048.809.809,0,0,0,.021.328.988.988,0,0,0,.163.253,2.657,2.657,0,0,1,.429,1.922,7.9,7.9,0,0,1-.581,1.927c-.014-.539.014-1.345.057-1.882.009-.107.063-.038.066-.146a2.205,2.205,0,0,0-.544-1.706,1.4,1.4,0,0,1-.257-.289.962.962,0,0,1-.087-.345q-.417-3.384-.57-6.795a.543.543,0,0,1,.074-.371.99.99,0,0,1,.25-.171c.174-.115.282-.317.47-.406" transform="translate(-322.009 -276.753)"/></g><g transform="translate(0 0.557)"><path class="j" d="M284.472,279.786s-3.663.863-2.774,6.353a33.946,33.946,0,0,0,2.185,7.795,3.383,3.383,0,0,0,3.481,2.185,5.065,5.065,0,0,0,1.769-.25c.676-.408.97-1.781,1.1-3.263.087-1.009.05-2.061.184-2.922.218-1.407-.427-2.069-1.253-2.234l-3.237-.645-.186,1.713Z" transform="translate(-281.559 -279.786)"/></g><g transform="translate(5.492 8.415)"><path class="b" d="M311.463,325.635c.009-.007.061.066.187.16a1.292,1.292,0,0,0,.6.24,2.019,2.019,0,0,0,.958-.137,3.059,3.059,0,0,0,1.035-.6,1.827,1.827,0,0,0,.538-1.035,1.787,1.787,0,0,0-.115-.955,2.505,2.505,0,0,0-.515-.744.636.636,0,0,1,.2.149,1.774,1.774,0,0,1,.4.559,1.821,1.821,0,0,1,.149,1.009,1.906,1.906,0,0,1-.566,1.112,3.05,3.05,0,0,1-1.084.615,2.03,2.03,0,0,1-1.013.115,1.245,1.245,0,0,1-.622-.292A.49.49,0,0,1,311.463,325.635Z" transform="translate(-311.462 -322.566)"/></g><g transform="translate(2.127 10.417)"><path class="b" d="M293.139,337.274c.012,0,.069.173.219.469a4.194,4.194,0,0,0,.834,1.121,3.951,3.951,0,0,0,1.793.991,4.011,4.011,0,0,0,2.48-.174c.415-.168.564-.629.723-1.013a6.7,6.7,0,0,0,.309-1.188c.132-.775.162-1.488.207-2.084s.078-1.06.1-1.413l.035-.384a.068.068,0,1,1,.026,0c0,.1-.006.23-.01.385-.016.353-.037.832-.063,1.415a18.971,18.971,0,0,1-.181,2.1,6.667,6.667,0,0,1-.307,1.212,4.418,4.418,0,0,1-.273.608,1.094,1.094,0,0,1-.514.478,4.081,4.081,0,0,1-2.564.172,4.026,4.026,0,0,1-2.643-2.205,2.91,2.91,0,0,1-.141-.36A.533.533,0,0,1,293.139,337.274Z" transform="translate(-293.138 -333.467)"/></g><g transform="translate(3.337 7.755)"><path class="b" d="M299.737,323.063a3.353,3.353,0,0,1,.008-.66c.022-.406.06-.966.111-1.585.027-.31.048-.6.08-.873a1.371,1.371,0,0,1,.249-.689.735.735,0,0,1,.415-.271c.118-.024.183,0,.181.006s-.065.006-.168.043a.744.744,0,0,0-.355.278,3.465,3.465,0,0,0-.274,1.518c-.051.618-.1,1.177-.148,1.582A3.334,3.334,0,0,1,299.737,323.063Z" transform="translate(-299.726 -318.975)"/></g><g transform="translate(2.842 0.557)"><path class="b" d="M298.683,285.241c-.033-.174-.084-.425-.15-.734-.132-.619-.324-1.472-.543-2.412a13.5,13.5,0,0,0-.348-1.321,3.067,3.067,0,0,0-.537-.987l-.071.058a3.14,3.14,0,0,1,.495.968c.131.392.223.842.335,1.312.219.94.425,1.79.58,2.4.078.307.143.554.19.725a1.139,1.139,0,0,0,.087.262A1.123,1.123,0,0,0,298.683,285.241Z" transform="translate(-297.034 -279.786)"/></g><g class="g" transform="translate(2.505 7.161)"><path d="M297.216,315.774a1.432,1.432,0,0,0-.824.278,2.739,2.739,0,0,0-1.09,1.543,3.2,3.2,0,0,0,.085,1.9c.067.189.2.4.4.4.236-.009.331-.3.363-.537l.271-1.941a1.8,1.8,0,0,1,.172-.64.612.612,0,0,1,.533-.339q.03-.345.06-.691" transform="translate(-295.195 -315.743)"/></g></g><g transform="translate(16.169 0.968)"><g transform="translate(6.637 3.501)"><path class="j" d="M409.244,302.923a5.428,5.428,0,0,0,.756-2.541,8.489,8.489,0,0,0-.359-2.793l-3.891-1.206-.025,1.3Z" transform="translate(-405.725 -296.383)"/></g><path class="k" d="M376.226,282.12l-6.637-4.8v.332l.717,7.473s5.487,2.458,6.94,2.82a2.9,2.9,0,0,0,2.5-.585Z" transform="translate(-369.589 -277.32)"/><g transform="translate(6.561 4.777)"><path class="b" d="M407.356,306.983a.523.523,0,0,1-.15-.079,2.636,2.636,0,0,1-.365-.285,4.088,4.088,0,0,1-.887-1.238,8.082,8.082,0,0,1-.506-1.435c-.049-.188-.085-.341-.108-.447a.59.59,0,0,1-.026-.168,4.531,4.531,0,0,1,.2.6,10.292,10.292,0,0,0,.527,1.412,4.517,4.517,0,0,0,.847,1.231C407.159,306.843,407.366,306.969,407.356,306.983Z" transform="translate(-405.313 -303.331)"/></g><g transform="translate(6.674 3.855)"><path class="b" d="M409.785,300.1a.59.59,0,0,1-.172,0,3.964,3.964,0,0,1-.465-.051,5.683,5.683,0,0,1-2.774-1.289,4.006,4.006,0,0,1-.339-.323.6.6,0,0,1-.109-.133c.012-.012.184.16.491.4a6.466,6.466,0,0,0,2.743,1.275C409.543,300.062,409.786,300.083,409.785,300.1Z" transform="translate(-405.926 -298.306)"/></g><g transform="translate(6.486 4.741)"><path class="b" d="M405.348,306.2a1.381,1.381,0,0,1-.206-.411,3.961,3.961,0,0,1-.221-1.089,7.826,7.826,0,0,1,.009-1.109,2.056,2.056,0,0,1,.057-.455,2.326,2.326,0,0,1,.01.458c0,.282-.011.672.019,1.1a4.886,4.886,0,0,0,.19,1.074C405.288,306.034,405.363,306.192,405.348,306.2Z" transform="translate(-404.905 -303.135)"/></g></g><g transform="translate(16.067 3.838)"><path class="b" d="M369.038,292.945a.93.93,0,0,1,.069.2c.038.132.09.325.151.564.122.477.278,1.14.433,1.876s.276,1.406.356,1.892c.04.243.07.44.087.577a.936.936,0,0,1,.018.214.924.924,0,0,1-.055-.208l-.117-.571-.383-1.885-.4-1.88-.122-.57A.943.943,0,0,1,369.038,292.945Z" transform="translate(-369.036 -292.945)"/></g></g><g transform="translate(16.89 8.352)"><g transform="translate(0.485 7.881)"><path class="c" d="M305.105,281.687l.813-9,2.7,1.064.347,9.153a2.006,2.006,0,0,1-2.319.77A2.364,2.364,0,0,1,305.105,281.687Z" transform="translate(-305.105 -272.688)"/></g><g transform="translate(0.366)"><path class="b" d="M305.1,229.777l5.039,4.432-.912,3.31-.366.108-4.406-4.509.279-3.233Z" transform="translate(-304.453 -229.777)"/></g><g transform="translate(0 0.108)"><path class="h" d="M303.106,230.368l5.039,4.432-.912,3.31-4.771-4.4Z" transform="translate(-302.462 -230.368)"/></g><g transform="translate(0.487 1.205)"><path class="c" d="M308.734,244.368a1.964,1.964,0,0,0,.506-1.239c-.012-.678-.2-2.311-.2-2.311a14.935,14.935,0,0,0,.853-2.088c-.022-.245-.062-.241-.377-.143s-1.29,1.779-1.29,1.779l-.319-1.246s.812-1.308.876-1.49.172-.333-.032-.35a11.811,11.811,0,0,0-1.727,1.461l.123,1.365s-.03.109-.25-.083-.164-1.82-.164-1.82a7.011,7.011,0,0,0,.507-1.1c-.012-.14.1-.291-.007-.529s-.4.036-.4.036l-1.012,1.326-.361-1.312s.015-.28-.349-.282l.141,4.256.922,3.275Z" transform="translate(-305.113 -236.339)"/></g><g transform="translate(0.462 1.154)"><path class="c" d="M308.855,253s-.008-.054-.014-.16-.014-.268-.025-.469c-.019-.418-.045-1.02-.08-1.787l-.256-6.439v-.026l.022-.019a1.761,1.761,0,0,0,.487-.689,2.013,2.013,0,0,0,.1-.886c-.032-.623-.109-1.272-.2-1.934l0-.018.008-.016c.165-.351.33-.712.49-1.082.08-.185.159-.373.232-.563.036-.1.071-.192.1-.288l.018-.071a.551.551,0,0,0,.006-.059.433.433,0,0,0-.025-.128c.01-.03-.129,0-.218.032a.281.281,0,0,0-.119.061.86.86,0,0,0-.107.1,4.946,4.946,0,0,0-.384.5c-.242.352-.471.723-.694,1.1l-.079.135-.039-.151-.319-1.246-.007-.026.015-.024.412-.673c.157-.265.329-.538.456-.8.026-.073.063-.148.08-.21.01-.034,0-.048,0-.045a.1.1,0,0,0-.061-.012.418.418,0,0,0-.073.037c-.03.017-.061.041-.091.061-.06.044-.12.089-.179.137-.467.384-.9.8-1.33,1.2l.019-.051q.062.693.122,1.365v.013l0,.01a.113.113,0,0,1-.121.076.244.244,0,0,1-.089-.028.673.673,0,0,1-.124-.086.5.5,0,0,1-.132-.278,3.756,3.756,0,0,1-.061-.545c-.019-.36-.018-.712-.008-1.061v-.013l.007-.013a9.13,9.13,0,0,0,.488-1.015.3.3,0,0,0,.012-.051c0-.015,0-.053,0-.076.006-.052.017-.1.025-.141a.486.486,0,0,0-.009-.245c-.028-.089-.055-.126-.124-.113a.5.5,0,0,0-.205.115l.007-.007-1.015,1.324-.066.086-.028-.1-.357-1.313,0-.007v-.009a.217.217,0,0,0-.092-.177.374.374,0,0,0-.207-.054l.049-.05.123,4.257v-.007l.466,1.8.116.467c.025.1.034.16.034.16s-.02-.052-.05-.155l-.131-.462-.494-1.8v-.007c-.04-1.089-.094-2.525-.158-4.256l0-.051h.051a.467.467,0,0,1,.262.07.314.314,0,0,1,.136.264l0-.016.364,1.311-.094-.018,1.009-1.328,0,0,0,0a.6.6,0,0,1,.262-.144.2.2,0,0,1,.176.048.331.331,0,0,1,.078.145.605.605,0,0,1,.013.3c-.008.047-.019.092-.023.132,0,.023,0,.032,0,.063a.325.325,0,0,1-.017.086A9.063,9.063,0,0,1,306.7,238l.007-.026c-.009.344-.009.7.01,1.05a3.665,3.665,0,0,0,.06.526.4.4,0,0,0,.09.208.559.559,0,0,0,.1.07.134.134,0,0,0,.041.015h0a.064.064,0,0,0,0,.013l0,0h0l0,0,.008,0h0l-.032-.011v0l0,.023-.124-1.365,0-.03.022-.021c.428-.41.864-.824,1.338-1.214.06-.048.121-.1.185-.142.034-.022.063-.046.1-.066a.342.342,0,0,1,.136-.055.2.2,0,0,1,.154.048.169.169,0,0,1,.032.171c-.026.087-.058.15-.084.222-.142.3-.306.548-.466.82l-.413.674.008-.05.319,1.246-.118-.016c.225-.381.454-.752.7-1.109a5.016,5.016,0,0,1,.4-.515.97.97,0,0,1,.122-.117.425.425,0,0,1,.16-.082,1.561,1.561,0,0,1,.16-.042.294.294,0,0,1,.1,0,.145.145,0,0,1,.107.076.506.506,0,0,1,.039.176.562.562,0,0,1-.008.1l-.02.08c-.03.1-.066.2-.1.3-.074.194-.155.382-.235.568-.162.372-.328.733-.494,1.085l.005-.034c.085.666.161,1.314.191,1.945a2.121,2.121,0,0,1-.113.931,1.865,1.865,0,0,1-.525.734l.021-.044c.087,2.678.159,4.89.21,6.441.021.767.038,1.369.05,1.788,0,.2.007.357.009.469S308.855,253,308.855,253Z" transform="translate(-304.976 -236.059)"/></g><g transform="translate(1.203 2.771)"><path class="d" d="M309.217,247.2a10.66,10.66,0,0,1-.184-2.337,10.662,10.662,0,0,1,.184,2.337Z" transform="translate(-309.012 -244.865)"/></g></g></g></g><g transform="translate(24.85 53.131)"><g transform="translate(0 0)"><g transform="translate(0.042 0.043)"><path class="l" d="M79.174,105.45,76.3,99.632a21.379,21.379,0,1,0-4.4,6.268l-.006.01Z" transform="translate(-35.562 -69.37)"/></g></g><g transform="translate(10.409 39.712)"><path class="h" d="M92.01,285.386l22-.036a20.842,20.842,0,0,1-11.575,3.074A28.693,28.693,0,0,1,92.01,285.386Z" transform="translate(-92.01 -285.35)"/></g><g transform="translate(6.908 5.352)"><g transform="translate(9.441 18.041)"><path class="m" d="M128.038,198.1l1.255,4.589,4.583-6.188L132.8,212.724h-6.543l-1.908.092s.283-2.557.4-4a16.147,16.147,0,0,1,.581-3.4C126.3,202.23,128.038,198.1,128.038,198.1Z" transform="translate(-124.346 -196.499)"/><g transform="translate(1.809 8.779)"><path class="b" d="M137.557,244.294c.016.021-.723.617-1.65,1.33s-1.693,1.276-1.709,1.255.723-.616,1.651-1.331S137.541,244.274,137.557,244.294Z" transform="translate(-134.198 -244.294)"/></g></g><g transform="translate(9.7 14.027)"><g transform="translate(4.12)"><path class="n" d="M160.37,178.021l-3.563-1.714-1.4-1.661-1.977,2.055-5.241,18.14h8.988l1.153-3.112,2.038-12.02Z" transform="translate(-148.191 -174.646)"/></g><g transform="translate(0.023 3.376)"><path class="o" d="M126.127,210.11l-.241-3.85,10.858-1.839s1.855-8.96,3-10.428a2.247,2.247,0,0,1,2.43-.885s1.239.849.943,4.1c-.155,1.7-.777,7.6-1,9.733a3.4,3.4,0,0,1-.494,1.438h0a3.4,3.4,0,0,1-2.89,1.606Z" transform="translate(-125.886 -193.029)"/></g><g transform="translate(10.796 14.799)"><path class="b" d="M186.4,257.425c-.016,0-.031-.171-.11-.434a2.576,2.576,0,0,0-1.339-1.588c-.246-.122-.414-.166-.41-.182a1.162,1.162,0,0,1,.438.12,2.376,2.376,0,0,1,1.376,1.632A1.156,1.156,0,0,1,186.4,257.425Z" transform="translate(-184.537 -255.219)"/></g><g class="p" transform="translate(6.478 1.923)"><path d="M167.006,185.113a16.981,16.981,0,0,1-.647,3.345l-.817-.24a2.126,2.126,0,0,1,.244,1.688,6.306,6.306,0,0,1-.706,1.6l-4.053,7.28,4.085-9.275-.106-1.715.786-.168Z" transform="translate(-161.028 -185.113)"/></g><g class="p" transform="translate(10.743 14.657)"><path d="M184.352,254.452a2.166,2.166,0,0,1,2.221,1.607.558.558,0,0,1,.006.349.231.231,0,0,1-.283.145.367.367,0,0,1-.159-.21,3.293,3.293,0,0,0-1.888-1.759" transform="translate(-184.249 -254.448)"/></g><g transform="translate(6.508 1.713)"><path class="b" d="M161.191,197.769a.959.959,0,0,1,.055-.149l.175-.419.661-1.535,2.214-5.049.909-2.053,0,.021c-.036-.609-.07-1.184-.1-1.715l0-.035.035-.007.787-.165-.026.02.979-1.981.274-.542.074-.141a.205.205,0,0,1,.029-.046.194.194,0,0,1-.019.051l-.064.145-.256.551-.95,2-.008.016-.018,0-.786.171.032-.043.111,1.714v.011l0,.01-.9,2.055-2.239,5.038-.689,1.523-.192.412A1.017,1.017,0,0,1,161.191,197.769Z" transform="translate(-161.19 -183.973)"/></g><g transform="translate(0 6.322)"><path class="b" d="M126.015,223.075a.328.328,0,0,1-.009-.064c0-.048-.01-.11-.016-.188-.012-.172-.03-.414-.053-.724-.042-.635-.1-1.544-.176-2.663l0-.031.03-.006,7.855-1.443,2.9-.525-.036.034,1.585-6.019.47-1.751.13-.468.035-.12a.16.16,0,0,1,.015-.04.18.18,0,0,1-.007.042l-.028.123-.115.472-.444,1.758-1.552,6.028-.007.028-.029.005-2.9.529-7.859,1.417.028-.036c.061,1.12.111,2.029.145,2.665.015.31.026.553.035.725,0,.079,0,.141.006.189A.294.294,0,0,1,126.015,223.075Z" transform="translate(-125.759 -209.066)"/></g></g><g transform="translate(23.173 17.098)"><path class="b" d="M201.389,191.369c0,.017-.182.045-.459.148a2.939,2.939,0,0,0-1.64,1.536c-.121.27-.16.452-.177.448s0-.048.014-.131a1.861,1.861,0,0,1,.1-.344,2.7,2.7,0,0,1,1.68-1.574,1.849,1.849,0,0,1,.35-.078A.355.355,0,0,1,201.389,191.369Z" transform="translate(-199.11 -191.365)"/></g><g transform="translate(3.264 9.977)"><path class="c" d="M92.377,155.641c.322-.593.179-2.988.675-2.9.632.108.048,2.625.008,2.855s.292.286.316.077c.036-.3.4-3.229,1.027-3.064.345.091.186.721.186.721s-.535,2.839-.239,2.907.524-1.569.656-2.165c.127-.571.5-.817.64-.516A6.652,6.652,0,0,1,95.34,156c-.118.574-.414,2.235.391,1.586a2.708,2.708,0,0,1,1.931-.749c.267.1.209.514-.1.6a3.865,3.865,0,0,0-.919.493,3.687,3.687,0,0,0-.609.572,3.218,3.218,0,0,0-.576.9,2,2,0,0,1-1.224,1.1h0l-.47,2.237-3.045-.652.679-2.367a30.373,30.373,0,0,0,.113-4.6c0-1.516.2-1.527.375-1.487C92.27,153.712,92.063,156.219,92.377,155.641Z" transform="translate(-90.717 -152.598)"/><g transform="translate(1.575 5.076)"><path class="q" d="M99.291,180.234a3.346,3.346,0,0,1,.4.189,1.672,1.672,0,0,1,.779,1.132l.01.052.043-.026a2.523,2.523,0,0,1,.789-.3c.208-.041.336-.045.336-.056a1.012,1.012,0,0,0-.344.008,2.267,2.267,0,0,0-.817.281l.053.026a1.7,1.7,0,0,0-.094-.337,1.715,1.715,0,0,0-.335-.523,1.623,1.623,0,0,0-.4-.307,1.378,1.378,0,0,0-.31-.124A.365.365,0,0,0,99.291,180.234Z" transform="translate(-99.291 -180.233)"/></g><g transform="translate(1.17 7.206)"><path class="q" d="M97.085,191.831a2.679,2.679,0,0,0,1.49.464c0-.022-.347-.062-.758-.192S97.1,191.811,97.085,191.831Z" transform="translate(-97.085 -191.829)"/></g></g><g transform="translate(13.133 14.581)"><path class="c" d="M146.773,178.062l-2.326,4.658,1.255,4.589,3.341-3.53,3.293-6.117Z" transform="translate(-144.446 -177.661)"/></g><g transform="translate(0 14.18)"><path class="o" d="M86.214,176.72a21.346,21.346,0,0,0-3.307,1.394h0c-.584.458-2.529,5.161-3.148,6.05l-1.769,2.606h0l1.484-5.791-3.264-.96s-2.787,6.736-3.126,8.235a5.507,5.507,0,0,0,.053,2.687l.018-.015a3.1,3.1,0,0,0,1.223,1.645,3.21,3.21,0,0,0,4,.078c.75-.554,1.605-1.221,2.39-1.846l.205,4.863h1.8c0-.273-.055-3.605-.055-3.605a18.81,18.81,0,0,0,.546-2.4c.164-1.147,2.849-8.674,2.849-8.674l2.085-4.014.048-1.486Z" transform="translate(-72.945 -175.482)"/><g transform="translate(7.62 3.823)"><path class="b" d="M114.8,212.615a.948.948,0,0,1-.016-.152l-.027-.436c-.02-.388-.048-.936-.083-1.613-.062-1.374-.147-3.269-.243-5.391v-.012l.122-.631,1.121-5.721.35-1.731.1-.47.028-.122a.143.143,0,0,1,.013-.041.14.14,0,0,1,0,.043l-.019.124-.082.473-.321,1.738-1.093,5.726-.122.631v-.012l.213,5.392.053,1.614q.006.275.01.437A.946.946,0,0,1,114.8,212.615Z" transform="translate(-114.434 -196.296)"/></g><g transform="translate(9.837 1.238)"><path class="b" d="M126.5,196.808a.944.944,0,0,1,.017-.152l.066-.434.26-1.593.892-5.251.639-3.666v-.005l0,0,1.115-2.528.319-.706.087-.185a.294.294,0,0,1,.034-.062.282.282,0,0,1-.022.067l-.076.19-.3.716-1.083,2.542,0-.009-.627,3.668-.92,5.246-.29,1.588-.084.431A.914.914,0,0,1,126.5,196.808Z" transform="translate(-126.501 -182.22)"/></g><g transform="translate(3.501 11.213)"><path class="b" d="M92.007,240.6c-.013-.009.129-.212.327-.557a10.134,10.134,0,0,0,.663-1.4,11.86,11.86,0,0,0,.455-1.485c.092-.387.134-.631.149-.629a.611.611,0,0,1-.009.174c-.011.111-.036.272-.074.469a9.123,9.123,0,0,1-.432,1.5,8.057,8.057,0,0,1-.695,1.4,4.7,4.7,0,0,1-.27.39C92.052,240.561,92.012,240.608,92.007,240.6Z" transform="translate(-92.006 -236.531)"/></g><g class="p" transform="translate(3.593 11.533)"><path d="M92.665,241.756a.2.2,0,0,0-.14.284.3.3,0,0,0,.316.145.628.628,0,0,0,.321-.175,2.2,2.2,0,0,0,.562-.981,9.5,9.5,0,0,0,.3-2.757,11.3,11.3,0,0,1-1.355,3.431" transform="translate(-92.507 -238.272)"/></g><g class="p" transform="translate(7.085 8.445)"><path d="M112.2,228.466a4.879,4.879,0,0,1-.667-2.977,14.206,14.206,0,0,1,1.3-4.027c-.3,1.389-.463,2.768-.731,4.1,0,0,.143,1.985.146,2.866" transform="translate(-111.52 -221.463)"/></g><g class="p" transform="translate(10.048 2.137)"><path d="M127.651,199.544a58.008,58.008,0,0,1,.662-6.322,20.178,20.178,0,0,1,2.13-6.1c-.266.838-1.087,2.6-1.087,2.6s-1.186,6.248-1.7,9.4" transform="translate(-127.651 -187.118)"/></g></g><g transform="translate(3.887 30.131)"><g transform="translate(0 0)"><path class="c" d="M100,263.368l.3,3.28-1.549-.006-1.371-.022a9.611,9.611,0,0,1-1.572.042c-.044,0-.07-.01-.073-.017a1.244,1.244,0,0,1-.239-.058h-.007v0c-.14-.051-.284-.142-.262-.3.068-.47.347-.508.347-.508l.14-.01.011-.041c-.213,0-.392-.005-.486-.015-.044,0-.07-.01-.073-.016s-.549-.065-.507-.356c.068-.47.347-.508.347-.508l.452-.03c-.219,0-.405,0-.5-.015-.044,0-.07-.01-.073-.016s-.549-.065-.507-.356c.068-.47.348-.508.348-.508l.374-.025c-.178,0-.325-.006-.408-.015-.044,0-.07-.01-.073-.016s-.549-.065-.507-.356c.068-.47.348-.508.348-.508s.864-.062,1.457-.086c0,0,.673-.607.938-.579.4.042,2.7.892,2.7.892Z" transform="translate(-94.106 -262.323)"/></g><g transform="translate(0.728 1.463)"><g transform="translate(0.732 1.869)"><path class="q" d="M103.586,280.493a3.1,3.1,0,0,1-.768.074,2.951,2.951,0,0,1-.76-.024,3.1,3.1,0,0,1,.768-.074A2.927,2.927,0,0,1,103.586,280.493Z" transform="translate(-102.059 -280.463)"/></g><g transform="translate(0.078 0.945)"><path class="q" d="M100.684,275.457a12.115,12.115,0,0,1-2.187.078,12.072,12.072,0,0,1,2.187-.078Z" transform="translate(-98.497 -275.433)"/></g><g class="r"><path class="q" d="M100.258,270.312a12.093,12.093,0,0,1-2.187.078,12.087,12.087,0,0,1,2.187-.078Z" transform="translate(-98.071 -270.288)"/></g></g><g transform="translate(1.56 0.564)"><path class="q" d="M102.6,265.429a1.587,1.587,0,0,0,.348.027l.839.027.839.03a1.571,1.571,0,0,0,.349,0,1.439,1.439,0,0,0-.345-.053c-.214-.021-.511-.043-.84-.054s-.626-.011-.842,0A1.432,1.432,0,0,0,102.6,265.429Z" transform="translate(-102.597 -265.394)"/></g></g><g transform="translate(12.066)"><g transform="translate(10.092 2.863)"><path class="b" d="M193.973,115.01a2.08,2.08,0,0,1,2.582-1.025,2.837,2.837,0,0,1,1.718,2.355,2.117,2.117,0,0,1-.293,1.447,1.954,1.954,0,0,1-1.345.757,2.68,2.68,0,0,1-2.834-3.716" transform="translate(-193.589 -113.862)"/></g><g transform="translate(11.201 4.135)"><path class="h" d="M202.809,120.791c.005,0-.018.049-.069.125a2.272,2.272,0,0,1-.247.3,2.742,2.742,0,0,1-2.358.809,2.256,2.256,0,0,1-.38-.086.383.383,0,0,1-.132-.057c0-.016.2.046.52.076a2.929,2.929,0,0,0,2.3-.791C202.681,120.949,202.8,120.781,202.809,120.791Z" transform="translate(-199.623 -120.791)"/></g><g transform="translate(11.38 5.214)"><path class="h" d="M204.015,126.667c0,.005-.033.04-.105.1a3.358,3.358,0,0,1-.319.23,3.632,3.632,0,0,1-1.2.5,2.977,2.977,0,0,1-.718.057,2.653,2.653,0,0,1-.58-.1,2.206,2.206,0,0,1-.37-.138c-.084-.041-.128-.066-.126-.072s.194.07.512.145a2.973,2.973,0,0,0,.567.073,3.162,3.162,0,0,0,.7-.062,4.117,4.117,0,0,0,1.182-.467C203.839,126.774,204.005,126.654,204.015,126.667Z" transform="translate(-200.599 -126.666)"/></g><path class="b" d="M139.184,105.113c-2.273-5,3.135-7.513,6.086-6.68,2.773.783,3.956,2.164,4.413,4.019a9.158,9.158,0,0,1-.292,5.633,2.719,2.719,0,0,1-1.94,1.866" transform="translate(-138.641 -98.276)"/><g transform="translate(0.574 1.794)"><path class="c" d="M144.453,125.446c-.039-2.389-.067-5.671-.068-5.658a3.337,3.337,0,0,1-2.479-3.021,28.623,28.623,0,0,1-.032-5.3c.1-1.607,1.167-3.58,2.768-3.41l5,1.264a.942.942,0,0,1,.843.956h0l-.326,10.345" transform="translate(-141.768 -108.043)"/></g><g transform="translate(1.907 6.814)"><path class="b" d="M149.021,135.679a.342.342,0,0,0,.325.348.327.327,0,0,0,.356-.3.342.342,0,0,0-.325-.349A.327.327,0,0,0,149.021,135.679Z" transform="translate(-149.021 -135.374)"/></g><g transform="translate(5.613 6.814)"><path class="b" d="M169.2,135.679a.342.342,0,0,0,.325.348.327.327,0,0,0,.356-.3.342.342,0,0,0-.325-.349A.327.327,0,0,0,169.2,135.679Z" transform="translate(-169.199 -135.375)"/></g><g transform="translate(2.453 5.935)"><path class="b" d="M152.938,133.668a2.459,2.459,0,0,0-.6-.108c-.094-.01-.184-.028-.2-.093a.481.481,0,0,1,.063-.28l.279-.719a12.364,12.364,0,0,0,.627-1.881,12.379,12.379,0,0,0-.777,1.826l-.267.723a.547.547,0,0,0-.049.371.237.237,0,0,0,.155.138.642.642,0,0,0,.16.022A2.392,2.392,0,0,0,152.938,133.668Z" transform="translate(-151.996 -130.587)"/></g><g transform="translate(3.187 12.592)"><path class="q" d="M155.992,167.791a6.816,6.816,0,0,0,3.589-.957,3.472,3.472,0,0,1-3.589,1.614Z" transform="translate(-155.992 -166.834)"/></g><g transform="translate(8.631 7.273)"><path class="c" d="M185.784,138c.039-.019,1.434-.636,1.451.968a1.206,1.206,0,0,1-1.6,1.244C185.63,140.171,185.784,138,185.784,138Z" transform="translate(-185.634 -137.872)"/></g><g transform="translate(9.114 7.732)"><path class="q" d="M188.262,141.645c.007-.005.028.019.075.04a.28.28,0,0,0,.209,0,.692.692,0,0,0,.314-.624.923.923,0,0,0-.082-.4.323.323,0,0,0-.212-.212.142.142,0,0,0-.164.078c-.021.045-.01.077-.019.08s-.035-.026-.023-.093a.18.18,0,0,1,.062-.1.211.211,0,0,1,.154-.042.393.393,0,0,1,.294.246.947.947,0,0,1,.1.446.726.726,0,0,1-.4.7.3.3,0,0,1-.256-.04C188.265,141.684,188.257,141.648,188.262,141.645Z" transform="translate(-188.261 -140.373)"/></g><g transform="translate(3.317 9.588)"><g transform="translate(0.14 0.073)"><path class="q" d="M157.509,151.259a1.548,1.548,0,0,0,.2.391.519.519,0,0,0,.369.214.466.466,0,0,0,.394-.215.743.743,0,0,0,.11-.448c-.006-.141-.071-.31-.211-.326a.351.351,0,0,0-.17.038l-.738.3" transform="translate(-157.461 -150.873)"/></g><path class="b" d="M157.181,151.545a1.068,1.068,0,0,0,.393-.049.562.562,0,0,0,.3-.27.592.592,0,0,0,.01-.483.53.53,0,0,0-.064-.11c-.029-.031-.024-.029-.056-.025s-.078.031-.118.046l-.121.049c-.158.064-.3.12-.422.165a1.343,1.343,0,0,1-.4.121,1.344,1.344,0,0,1,.365-.207c.116-.056.255-.121.411-.191l.119-.053a1.341,1.341,0,0,1,.135-.055.173.173,0,0,1,.109,0,.2.2,0,0,1,.081.064.632.632,0,0,1,.083.14.692.692,0,0,1-.023.59.631.631,0,0,1-.374.3.6.6,0,0,1-.32.012C157.212,151.578,157.178,151.553,157.181,151.545Z" transform="translate(-156.699 -150.477)"/></g><g transform="translate(9.19 6.306)"><path class="h" d="M190.066,132.612c-.164-.012-1.489.16-1.385,2.623.1,2.3,1.711,1.794,2.226.928C191.4,135.342,191.677,132.725,190.066,132.612Z" transform="translate(-188.675 -132.611)"/></g><g transform="translate(9.679 3.197)"><path class="h" d="M193.057,119.084a.306.306,0,0,1-.006-.065c0-.048,0-.11-.008-.188l-.02-.7c-.012-.605-.029-1.445-.048-2.416l.03.029h-.683l.031-.031c0,.183.01.377.016.573.023.924.044,1.785.062,2.505v.021l-.021,0a1.244,1.244,0,0,0-.7.453,1.662,1.662,0,0,0-.281.594,2.464,2.464,0,0,0-.072.421c0,.049-.007.087-.008.112a.158.158,0,0,1,0,.039.125.125,0,0,1,0-.039c0-.026,0-.064,0-.113a2.164,2.164,0,0,1,.059-.427,1.655,1.655,0,0,1,.276-.612,1.28,1.28,0,0,1,.725-.478l-.02.026c-.021-.72-.045-1.581-.072-2.505-.005-.2-.01-.39-.015-.573v-.032h.744v.029c.011.971.02,1.811.026,2.416,0,.295,0,.532,0,.7l0,.188A.323.323,0,0,1,193.057,119.084Z" transform="translate(-191.336 -115.683)"/></g><g transform="translate(6.258 0.036)"><path class="h" d="M177.036,101.666a5.285,5.285,0,0,0-2.344-2.422,9.811,9.811,0,0,0-1.978-.733,4.317,4.317,0,0,1,2.522.465,4.776,4.776,0,0,1,1.894,1.341,4.113,4.113,0,0,1,.673,1.35Z" transform="translate(-172.714 -98.474)"/></g><g transform="translate(9.654 6.399)"><path class="b" d="M191.951,137.261a.142.142,0,0,1-.046-.014.723.723,0,0,1-.123-.064,1.08,1.08,0,0,1-.347-.37,1.78,1.78,0,0,1-.213-.722,5.259,5.259,0,0,1,0-.92,5.6,5.6,0,0,1,.152-.908,2.605,2.605,0,0,1,.265-.7,1.088,1.088,0,0,1,.349-.368.735.735,0,0,1,.124-.062.134.134,0,0,1,.047-.013c0,.005-.061.028-.157.1a1.132,1.132,0,0,0-.325.37,2.7,2.7,0,0,0-.247.7,5.925,5.925,0,0,0-.146.9,5.54,5.54,0,0,0,0,.91,1.791,1.791,0,0,0,.194.706,1.123,1.123,0,0,0,.323.372C191.89,137.233,191.954,137.256,191.951,137.261Z" transform="translate(-191.2 -133.115)"/></g><g transform="translate(3.518 8.581)"><g transform="translate(0 1.531)"><g transform="translate(0 0)"><path class="h" d="M.361,0H1.556a.361.361,0,0,1,.361.361v0a.361.361,0,0,1-.361.361H.361A.361.361,0,0,1,0,.361v0A.361.361,0,0,1,.361,0Z" transform="translate(0 0.369) rotate(-11.103)"/></g></g><g transform="translate(1.782)"><path class="h" d="M167.5,146.777l5.63-1.766a.266.266,0,0,1,.332.172h0a.266.266,0,0,1-.184.337l-5.757,1.555Z" transform="translate(-167.496 -144.998)"/></g><g transform="translate(4.925 0.005)"><path class="b" d="M185.627,146.022a1.222,1.222,0,0,1,.186-.066l.515-.157c.216-.067.481-.137.762-.229a.51.51,0,0,0,.184-.114.282.282,0,0,0,.079-.2.171.171,0,0,0-.116-.161.434.434,0,0,0-.235.007l-1.688.543-.514.159a1.218,1.218,0,0,1-.191.05,1.19,1.19,0,0,1,.184-.073l.508-.178c.431-.146,1.022-.349,1.687-.561a.488.488,0,0,1,.271-.005.232.232,0,0,1,.156.218.341.341,0,0,1-.1.239.567.567,0,0,1-.206.125c-.291.092-.551.156-.77.218l-.52.138A1.216,1.216,0,0,1,185.627,146.022Z" transform="translate(-184.609 -145.024)"/></g></g><g transform="translate(4.753 5.976)"><path class="q" d="M166.274,131.167c-.043.068-.429-.142-.907-.056s-.772.407-.836.358c-.031-.021,0-.14.129-.285a1.173,1.173,0,0,1,1.4-.242C166.225,131.036,166.3,131.137,166.274,131.167Z" transform="translate(-164.52 -130.811)"/></g><g transform="translate(4.772 6.198)"><path class="b" d="M166.3,132.216a1.931,1.931,0,0,1-.264-.066,1.414,1.414,0,0,0-1.206.271c-.13.1-.2.182-.21.173s.04-.1.166-.224a1.273,1.273,0,0,1,1.268-.285C166.221,132.14,166.307,132.207,166.3,132.216Z" transform="translate(-164.621 -132.023)"/></g><g transform="translate(4.816 4.978)"><path class="b" d="M166.745,125.714c-.039.129-.448.119-.936.142s-.9.052-.945-.074c-.021-.061.063-.155.23-.24a1.746,1.746,0,0,1,.7-.165,1.72,1.72,0,0,1,.708.112C166.672,125.563,166.762,125.652,166.745,125.714Z" transform="translate(-164.861 -125.377)"/></g><g transform="translate(1.147 5.994)"><path class="q" d="M146.4,131.53c-.037.021-.109-.048-.225-.128a1.17,1.17,0,0,0-.488-.2c-.415-.069-.748.124-.8.056-.022-.029.034-.124.181-.216a1,1,0,0,1,.659-.124.905.905,0,0,1,.587.337C146.422,131.4,146.432,131.513,146.4,131.53Z" transform="translate(-144.886 -130.909)"/></g><g transform="translate(0.98 6.192)"><path class="b" d="M145.7,132.611c-.013.008-.075-.084-.2-.2a1.289,1.289,0,0,0-1.253-.272c-.166.05-.26.108-.269.095s.076-.088.246-.159a1.2,1.2,0,0,1,1.323.287C145.67,132.5,145.711,132.606,145.7,132.611Z" transform="translate(-143.975 -131.989)"/></g><g transform="translate(1.111 5.172)"><path class="b" d="M146.452,126.737c-.024.132-.415.169-.879.175s-.856-.03-.882-.161.363-.314.878-.318S146.477,126.609,146.452,126.737Z" transform="translate(-144.69 -126.433)"/></g><g transform="translate(6.37 1.632)"><path class="b" d="M174.151,107.206a5.323,5.323,0,0,0-.734,1.382,1.8,1.8,0,0,0,.157,1.511,3.207,3.207,0,0,0,1.206.872,1.8,1.8,0,0,1,1,1.06c.034.153-.031.688.084.795a.391.391,0,0,0,.555-.123,2.774,2.774,0,0,0,.2-.982l.028-2a3.688,3.688,0,0,0-.244-1.661c-.343-.713-1.053-.941-2.089-.89" transform="translate(-173.323 -107.161)"/></g><g transform="translate(0.554 0.962)"><path class="b" d="M141.723,108.245a3.778,3.778,0,0,1,.76-1.871,2.771,2.771,0,0,1,1.714-1.026,9.975,9.975,0,0,1,2.041.125,1.945,1.945,0,0,0,1.816-.712.516.516,0,0,0,.022-.6.6.6,0,0,0-.2-.132,5.139,5.139,0,0,0-2.921-.472,4.033,4.033,0,0,0-2.559,1.431,3.325,3.325,0,0,0-.649,2.824" transform="translate(-141.658 -103.513)"/></g><g transform="translate(0.108 1.385)"><path class="h" d="M139.331,109.118a.342.342,0,0,1-.032-.087,1.556,1.556,0,0,1-.054-.261,2.255,2.255,0,0,1,.092-.973,2.588,2.588,0,0,1,.805-1.2,3.354,3.354,0,0,1,1.616-.727,4.567,4.567,0,0,1,1.774.08,6.285,6.285,0,0,1,.743.224c.229.084.433.177.629.229a2.689,2.689,0,0,0,.953.085.7.7,0,0,0,.311-.153.241.241,0,0,1-.062.07.569.569,0,0,1-.241.119,2.55,2.55,0,0,1-.978-.056c-.2-.05-.412-.141-.639-.22a6.459,6.459,0,0,0-.736-.213,4.575,4.575,0,0,0-1.738-.071,3.316,3.316,0,0,0-1.575.7,2.562,2.562,0,0,0-.8,1.155,2.348,2.348,0,0,0-.119.948C139.3,108.993,139.34,109.116,139.331,109.118Z" transform="translate(-139.227 -105.814)"/></g><g transform="translate(3.176 0.43)"><path class="h" d="M155.93,100.66a.519.519,0,0,1,.169-.033,2.337,2.337,0,0,1,.469,0,2.729,2.729,0,0,1,1.425.57c.238.189.436.394.646.537a1.7,1.7,0,0,0,.619.249,1.406,1.406,0,0,0,.452.031c.107-.013.16-.042.165-.034a.3.3,0,0,1-.158.071,1.25,1.25,0,0,1-.471,0,1.671,1.671,0,0,1-.656-.243c-.223-.145-.426-.351-.656-.534a2.851,2.851,0,0,0-1.374-.578C156.174,100.642,155.931,100.678,155.93,100.66Z" transform="translate(-155.93 -100.615)"/></g><g transform="translate(6.499 4.365)"><path class="h" d="M178.812,123.069c-.009,0-.037-.064-.115-.167a1.149,1.149,0,0,0-.426-.327,1.816,1.816,0,0,0-.783-.157,6.067,6.067,0,0,0-.99.137,3.639,3.639,0,0,1-1.015.077,2.788,2.788,0,0,1-.809-.2,2.525,2.525,0,0,1-.493-.265.674.674,0,0,1-.158-.131,5.268,5.268,0,0,0,.675.333,2.917,2.917,0,0,0,.792.17,3.741,3.741,0,0,0,.99-.083,5.614,5.614,0,0,1,1.007-.131,1.812,1.812,0,0,1,.813.184,1.1,1.1,0,0,1,.426.367.752.752,0,0,1,.07.136C178.81,123.05,178.815,123.067,178.812,123.069Z" transform="translate(-174.023 -122.04)"/></g><g transform="translate(6.659 2.65)"><path class="h" d="M179.235,114.162c-.008,0-.036-.063-.114-.161a1.07,1.07,0,0,0-.428-.3,1.6,1.6,0,0,0-.774-.082c-.3.029-.618.119-.965.184a2.069,2.069,0,0,1-1-.016,1.628,1.628,0,0,1-.694-.43,1.5,1.5,0,0,1-.305-.457.917.917,0,0,1-.049-.141c-.009-.033-.012-.051-.008-.052a2.1,2.1,0,0,0,.41.6,1.618,1.618,0,0,0,.671.393,2.045,2.045,0,0,0,.956.006c.341-.063.666-.152.974-.178a1.612,1.612,0,0,1,.809.108,1.027,1.027,0,0,1,.431.342A.431.431,0,0,1,179.235,114.162Z" transform="translate(-174.897 -112.704)"/></g><g transform="translate(4.113 10.573)"><path class="b" d="M161.032,167.744a.26.26,0,0,1,.034-.029c.023-.019.058-.044.1-.081a4.557,4.557,0,0,0,.374-.34,6.424,6.424,0,0,0,.527-.612c.193-.251.4-.55.625-.885.452-.67.981-1.484,1.542-2.424.281-.47.57-.971.863-1.5l.22-.4a2.505,2.505,0,0,1,.251-.4.879.879,0,0,1,.39-.283.472.472,0,0,1,.248-.027.24.24,0,0,1,.118.065.276.276,0,0,1,.063.12l0,.009,0,.009a.437.437,0,0,1-.087.151.246.246,0,0,1-.161.077.517.517,0,0,1-.318-.11.538.538,0,0,1-.2-.626.478.478,0,0,1,.234-.266.53.53,0,0,1,.348-.049.37.37,0,0,1,.283.219.188.188,0,0,1-.041.195.345.345,0,0,1-.165.09.487.487,0,0,1-.539-.246.439.439,0,0,1-.032-.315.531.531,0,0,1,.164-.266.425.425,0,0,1,.286-.111.339.339,0,0,1,.271.143.224.224,0,0,1,.034.162.213.213,0,0,1-.089.138.33.33,0,0,1-.3.037.37.37,0,0,1-.2-.222.806.806,0,0,1-.045-.291,1.024,1.024,0,0,1,.145-.553.42.42,0,0,1,.23-.174.368.368,0,0,1,.278.05.273.273,0,0,1,.139.248.252.252,0,0,1-.183.214.3.3,0,0,1-.27-.044.38.38,0,0,1-.136-.236.546.546,0,0,1,.023-.266,1.231,1.231,0,0,1,.091-.242.464.464,0,0,1,.174-.192.569.569,0,0,1,.238-.08.24.24,0,0,1,.129.017.283.283,0,0,1,.1.086.329.329,0,0,1,.053.117.183.183,0,0,1-.016.134.213.213,0,0,1-.227.089.387.387,0,0,1-.209-.108.625.625,0,0,1-.18-.414.663.663,0,0,1,.112-.422.5.5,0,0,1,.156-.146.437.437,0,0,1,.2-.055.3.3,0,0,1,.2.058.219.219,0,0,1,.083.187.3.3,0,0,1-.264.257.322.322,0,0,1-.3-.17.543.543,0,0,1-.075-.326,1.978,1.978,0,0,1,.2-.56l.2-.455.279-.629.074-.163c.017-.037.027-.055.027-.055s-.006.02-.021.057l-.068.165-.269.634-.2.457a1.985,1.985,0,0,0-.192.551.508.508,0,0,0,.072.3.286.286,0,0,0,.266.15.264.264,0,0,0,.227-.219.175.175,0,0,0-.067-.149.262.262,0,0,0-.168-.048.393.393,0,0,0-.179.051.457.457,0,0,0-.141.133.615.615,0,0,0-.1.391.575.575,0,0,0,.166.38.335.335,0,0,0,.183.095.163.163,0,0,0,.175-.063.192.192,0,0,0-.035-.193.231.231,0,0,0-.078-.07.19.19,0,0,0-.1-.012.452.452,0,0,0-.368.245,1.155,1.155,0,0,0-.086.231.484.484,0,0,0-.021.24.322.322,0,0,0,.115.2.241.241,0,0,0,.219.034.192.192,0,0,0,.143-.163.215.215,0,0,0-.113-.192.311.311,0,0,0-.233-.042.364.364,0,0,0-.2.15.969.969,0,0,0-.133.518.743.743,0,0,0,.042.268.31.31,0,0,0,.166.187.271.271,0,0,0,.243-.03.155.155,0,0,0,.04-.215.28.28,0,0,0-.222-.114.365.365,0,0,0-.244.1.408.408,0,0,0,.352.718.286.286,0,0,0,.135-.072.122.122,0,0,0,.027-.13.311.311,0,0,0-.237-.179.446.446,0,0,0-.511.274.475.475,0,0,0,.181.554.462.462,0,0,0,.28.1.238.238,0,0,0,.2-.187v.018a.178.178,0,0,0-.136-.14.419.419,0,0,0-.216.025.82.82,0,0,0-.364.264,2.476,2.476,0,0,0-.246.39l-.221.4c-.294.529-.585,1.029-.868,1.5-.565.939-1.1,1.752-1.555,2.419-.228.334-.437.632-.633.882a6.16,6.16,0,0,1-.536.608A3.227,3.227,0,0,1,161.032,167.744Z" transform="translate(-161.032 -155.841)"/></g></g></g></g></g></g></svg>