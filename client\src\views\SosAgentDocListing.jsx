import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
//import Moment from 'react-moment';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import './customStyling.css';
//import moment from "moment";
import { If, Then, Else } from 'react-if';
import 'react-datetime/css/react-datetime.css'

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col,
  FormGroup,
  Input,
} from "reactstrap";


class SosAgentDocListing extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "SOS Agent Doc Listing",
      SosAgentDocs: [],
      RedStatuses: ['Docs Pending', 'QC Pending', 'QC Rejected', 'Medical Pending', 'PIVC Pending'],
      IsLoading: true,
    };

    this.columnlist = [
      {
        name: "Lead ID",
        selector: "leadid", 
        searchable: true
      },
      //{ name: "CRT Agent ID", selector: "CRTAgentID" },
      //{ name: "CRT Agent Name", selector: "CRTAgentName" },
      //{ name: "Supplier Shortname", selector: "suppliershortname", width:"150px", },
      //{ name: "Plan Name", selector: "planname", width:"150px", },
      //{ name: "Doc Received Date", selector: "DocReceived_DT", width:"150px", cell: row => (!row.DocReceived_DT ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.DocReceived_DT}</Moment>)) },
      //{ name: "Doc QCPending Date", selector: "DocQCPendingDate", width:"150px", cell: row => (!row.DocQCPendingDate ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.DocQCPendingDate}</Moment>)) },
      //{ name: "Doc QCApproved Date", selector: "DocQCApprovedDate", width:"150px", cell: row => (!row.DocQCApprovedDate ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.DocQCApprovedDate}</Moment>)) },
      //{ name: "Doc QCReject Date", selector: "DocQCRejectDate", width:"150px", cell: row => (!row.DocQCRejectDate ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.DocQCRejectDate}</Moment>)) },
      //{ name: "Medical Done Date", selector: "MedicalDone_DT", width:"150px", cell: row => (!row.MedicalDone_DT ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.MedicalDone_DT}</Moment>)) },
      //{ name: "Med QCApproved Date", selector: "MedQCApprovedDate", width:"150px", cell: row => (!row.MedQCApprovedDate ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.MedQCApprovedDate}</Moment>)) },
      //{ name: "Med QCReject Date", selector: "MedQCRejectDate", width:"150px", cell: row => (!row.MedQCRejectDate ? 'N/A' : (<Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.MedQCRejectDate}</Moment>)) },
      { 
        name: "PF Filled", 
        selector: "PF_Fill_Date", 
        width:"80px",
        cell: row => <div className={row.PF_Fill_Date.toLowerCase() == 'no' ? 'RED' : 'WHITE'}>
          { row.PF_Fill_Date}
        </div> 
      },
      { 
        name: "Docs", 
        selector: "Docs_Status", 
        cell: row => <div className={this.state.RedStatuses.indexOf(row.Docs_Status) !== -1 ? 'RED' : 'WHITE'}>
          { row.Docs_Status == 'NA' ? 'N/A' : row.Docs_Status}
        </div> 
      },
      { 
        name: "Tele Medical", 
        selector: "TM Status", 
        cell: row => <div className={this.state.RedStatuses.indexOf(row['TM Status']) !== -1 ? 'RED' : 'WHITE'}>
          { row['TM Status'] == 'NA' ? 'N/A' : row['TM Status'] }
        </div> 
      },
      { 
        name: "Video Medical", 
        selector: "VM Status", 
        cell: row => <div className={this.state.RedStatuses.indexOf(row['VM Status']) !== -1 ? 'RED' : 'WHITE'}>
          { row['VM Status'] == 'NA' ? 'N/A' : row['VM Status'] }
        </div> 
      },
      /*{ 
        name: "PM Status", 
        selector: "PM Status", 
        cell: row => <div className={this.state.RedStatuses.indexOf(row['PM Status']) !== -1 ? 'RED' : 'WHITE'}>
          { row['PM Status'] == 'NA' ? 'N/A' : row['PM Status'] }
        </div> 
      },*/
      { 
        name: "PIVC", 
        selector: "PIVCStatus", 
        width:"150px",
        cell: row => <div className={this.state.RedStatuses.indexOf(row.PIVCStatus) !== -1 ? 'RED' : 'WHITE'}>
          { row.PIVCStatus || 'N/A' }
        </div> 
      },
    ];
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetSosAgentDocList"]) {
        let CBList = nextProps.CommonData["GetSosAgentDocList"];
        const ModifiedList = [];
        for (let index = 0; index < CBList[0].length; index++) {
          const element = CBList[0][index];

          //To Show only those rows which have atleast once red colored column 
          if(element['Docs_Status'].toLowerCase() == 'no'
           || this.state.RedStatuses.indexOf(element['Docs_Status']) !== -1
           || this.state.RedStatuses.indexOf(element['TM Status']) !== -1
           || this.state.RedStatuses.indexOf(element['VM Status']) !== -1
           || this.state.RedStatuses.indexOf(element['PIVCStatus']) !== -1) {
            ModifiedList.push(element);
          }
        }

        this.setState({ SosAgentDocs: ModifiedList, IsLoading: false });
      }

    }
  }

  componentDidMount() {
    this.fetchCallBackData();
  }

  fetchCallBackData() {

    if(!getUrlParameter("EmpId") || getUrlParameter("EmpId") == "") {
      setInterval(() => {
        toast("EmployeeID is missing", { type: 'error' });        
      }, 15000);
      toast("EmployeeID is missing", { type: 'error' });        
      this.setState({ IsLoading: false });
      return false;
    }

    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetSosAgentDocList",
      c: "A",
      params: [{
        //UserId:getuser().UserID,
        AgentID: getUrlParameter("EmpId"),
      }]
    });
  }

  render() {
    const columns = this.columnlist; 
    const { items, PageTitle, SosAgentDocs, showAlert, AlertMsg, AlertVarient } = this.state;

    return (
      <>
        <div className="content noExportButton">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={4}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                <If condition={this.state.IsLoading}>
                <Then>
                  <i class="fa fa-spinner fa-spin"></i>
                </Then>
                <Else>
                <DataTable
                    columns={columns}
                    data={(SosAgentDocs && SosAgentDocs.length > 0) ? SosAgentDocs : []}
                    //defaultSortField="RejectedOn"
                    defaultSortAsc={false}
                    export={false}
                    //selectableRows={true}
                    //ref={this.dtRef}
                    //onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </Else>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(SosAgentDocListing);
