
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import { fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject } from '../utility/utility.jsx';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";


class INVProduct extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "INVProduct",
      PageTitle: "INV Product",
      FormTitle: "",
      formvalue: {},
      event: "",
      ProductId: 115
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.selectedrow = { "ID": 0, "Description": "", "InvTypeID": "", "GroupID": "", "InsurerID": "", "GradeRuleID": "", "NonSelectionAllocation": "", "CreatedOn": new Date() }
    this.columnlist = [
      {
        name: "ID",
        label: "ID",
        type: "hidden",
        hide: true,
      },
      {
        name: "Description",
        label: "Description",
        searchable: true,
        type: "string"
      },
      {
        name: "InvTypeID",
        label: "Inv Type",
        type: "dropdown",
        searchable: true,
        config: {
          root: "Investers",
          data: [{ Id: 1, Display: "Growth" }, { Id: 2, Display: "Retairment" }, { Id: 3, Display: "Child" }],
        }
      },
      {
        name: "GroupID",
        label: "Group",
        type: "dropdown",
        searchable: true,
        config: {
          root: "vwUserGroup",
          cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
          con: [{ "PGM.ProductId": this.state.ProductId }],
          statename: "vwUserGroup-" + this.state.ProductId,
        }
      },
      {
        name: "InsurerID",
        label: "Insurer",
        type: "dropdown",
        searchable: true,
        config: {
          root: "Suppliers",
          cols: ["DISTINCT OldSupplierId AS Id", "SupplierName AS Display"],
          con: [{ "ProductID": this.state.ProductId }],
          statename: "Suppliers-" + this.state.ProductId,
          state: true
        }
      },
      {
        name: "GradeRuleID",
        label: "Grade Rule",
        type: "dropdown",
        searchable: true,
        config: {
          root: "AgentRules",
          cols: ["RuleID AS Id", "RuleName AS Display"],
          con: [{ "ProductID" : this.state.ProductId }],
          statename: "AgentRules-" + this.state.ProductId,
          state: true
        }
      },
      {
        name: "CreatedOn",
        label: "CreatedOn",
        type: "datetime",
        editable: false
      }, {
        name: "NonSelectionAllocation",
        label: "NSA",
        type: "bool"
      }
    ];
    let count = 0;
  }



  componentDidMount() {
    this.columnlist.map(col => (
      fnBindRootData(col,this.props)
    ));

    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      c: "L",
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name")
    });

  }
  fnBindStore(col,nextProps){
    if(col.type == "dropdown"){
      let items;      
      
      if(nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]){
        
        items = joinObject(nextProps.CommonData[this.state.root],nextProps.CommonData[col.config.root],col.name)
        this.setState({ items: items });
      }
    }
  }
  componentWillReceiveProps(nextProps) {
    

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      
      //setTimeout(function(){
        this.columnlist.map(col => (
          this.fnBindStore(col,nextProps)
        ));
      //}.bind(this),2000);
      
    }

    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200)
        alert(nextProps.CommonData.InsertSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        alert(nextProps.CommonData.UpdateSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "InvType",
    //   selector: "InvTypeID_display",
    //   sortable: true,
    // });

    // columns.push({
    //   name: "Group Name",
    //   selector: "GroupID_display",
    //   sortable: true,
    // });

    // columns.push({
    //   name: "GradeRule",
    //   selector: "GradeRuleID_display",
    //   sortable: true,
    // });

    // columns.push({
    //   name: "Insurer Name",
    //   selector: "InsurerID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }



  handleCopy(row) {
    this.setState({ formvalue: row, event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ formvalue: row, event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }

  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {


    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {

    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    
    this.fnCleanData(formvalue, true)
    if (this.state.event == "Edit") {
      let id = formvalue["ID"];
      delete formvalue["ID"]
      this.fnCleanData(formvalue, true);
      this.props.UpdateData({
        root: this.state.root,
        body: formvalue,
        querydata: { "ID": id }
      });
    } else if (this.state.event == "Copy") {

      formvalue["CreatedOn"] = new Date();
      this.fnCleanData(formvalue, false);
      this.props.InsertData({
        root: this.state.root,
        body: formvalue
      });
      setTimeout(function () {
        this.props.GetCommonData({
          limit: 10,
          skip: 0,
          root: this.state.root,
          cols: GetJsonToArray(this.columnlist, "name")
        });
      }.bind(this), 2000);

    } else {
      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      formvalue["CreatedOn"] = new Date();
      this.fnCleanData(formvalue, false);
      this.props.InsertData({
        root: this.state.root,
        body: formvalue
      });
    }
    this.setState({ showModal: false });
  }
  handleChange = (e,props) => {    
    let formvalue = this.state.formvalue;
    
    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if(e._isAMomentObject){
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }

    this.setState({ formvalue: formvalue });
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue,event } = this.state;
    
    return (
      <>
        <div className="content">
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>

                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form>
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange,event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <Button variant="primary" onClick={this.handleSave}>
                Save Changes
          </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData
  }
)(INVProduct);