import React from "react";
import {
    GetCJUrl,
    GetCommonData, GetCommonspData, GetCommonspDataV2, GetPendingDocumentLeadList,
} from "../../store/actions/CommonAction";
import {
    addRecord
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from '../Common/DataTableWithFilter';
import { fnDatatableCol, getuser, OpenNewSalesView } from '../../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDown from "views/Common/DropDown";
import { Button, Form } from "react-bootstrap";
import { MultiSelect } from "react-multi-select-component";
import moment from "moment";
import Datetime from 'react-datetime';
import * as XLSX from 'xlsx';
import ManagerHierarchy from "views/Common/ManagerHierarchy";
import Loader from '../Common/Loader';

class PfPending extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isLoaded: false,
            showModal: false,
            items: [],
            activePage: 1,
            root: "GetPfPending",
            PageTitle: "PF/Doc Pending",
            FormTitle: "",
            formvalue: {},
            event: "",
            ModalValueChanged: false,
            ReportTypes: [{ Id: 2, Display: "Document Pending" }],
            dropdownValues: [{ label: "Tele-Medical", value: 1 }, { label: "Physical", value: 2 },
            { label: "Non-Medical", value: 3 }],
            medicalSelectValue: [],
            enableMedicalType: false,
            Products: [{ Id: 115, Display: "Investments" }],
            SupplierMultiList: [],
            supplierSelectValue: [],
            ManagerMultiList: [],
            managerSelectValue: [],
            StartDate: moment().subtract(30, 'days').format("YYYY-MM-DD"),
            EndDate: moment().format("YYYY-MM-DD"),
            Ageing: [{ Id: 1, Display: "0-5" }, { Id: 2, Display: "6-10" }, { Id: 3, Display: "10+" }],
            ageing: 0,
            PFPendingItems: [],
            ProductId: 7,
            clickSearch: false,
            ReportType: 1,
            UserList: [],
            UserData: []
        };
        this.HandleSearchClick = this.HandleSearchClick.bind(this)
        this.getPFPendingLeads = this.getPFPendingLeads.bind(this)
        this.handleShow = this.handleShow.bind(this)
        this.tableRef = React.createRef();
        const Cell = ({ v }) => (
            <span title={v}>{(v) ? v.substring(0, 25)
                .toLowerCase()
                .split(' ')
                .map(word => {
                    return word.charAt(0).toUpperCase() + word.slice(1);
                })
                .join(' ') : ''}{(v && v.length > 25) ? '...' : ''}</span>
        );
        this.ADMIN_AND_MANAGER_NOTE = '* Please use Filters to search the Data.';

        this.postColumnList = [
            {
                name: "ProposalFillDate",
                label: "ProposalFillDate",
                type: "datetime",
                cell: row => row.ProposalFillDate ? row.ProposalFillDate : "N.A",
                sortable: true,
            }
        ]

        this.columnlist = [
            {
                name: 'Sno',
                label: 'S.No.',
                cell: (row, index) => index + 1,
                width: "60px",
            },
            {
                name: "LeadID",
                label: "LeadID",
                cell: row => <div>
                    <a href={OpenNewSalesView(row.CustomerID, row.LeadID, row.ProductID)} target="_blank">{row.LeadID} </a>
                </div>,
                sortable: true,
                width: "120px"
                //searchable: true
            },
            {
                name: "Name",
                label: "Name",
                cell: row => row.Name ? <Cell v={row.Name} /> : "N.A",
                sortable: true,
                searchable: false
            },
            {
                name: "OfferCreateON",
                label: "Booking Date",
                cell: row => row.OfferCreateON ? row.OfferCreateON : "N.A",
                type: "datetime",
                sortable: true,
                //searchable: true
            },
            {
                name: "StatusName",
                label: "LeadStatus",
                cell: row => row.StatusName ? <Cell v={row.StatusName} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "SupplierName",
                label: "InsurerName",
                cell: row => row.SupplierName ? <Cell v={row.SupplierName} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "UserName",
                label: "SalesAgent",
                cell: row => row.UserName ? <Cell v={row.UserName + ' (' + row.EmployeeId + ')'} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "UserGroupName",
                label: "GroupName",
                cell: row => row.UserGroupName ? <Cell v={row.UserGroupName} /> : "N.A",
                sortable: true,
                width: 150
                //searchable: true
            }, {
                name: "ReporterEmpID",
                label: "Reporting Manager",
                cell: row => row.ReporterEmpID ? <Cell v={row.ReporterName + ' (' + row.ReporterEmpID + ')'} /> : "N.A",
                sortable: true,
                //searchable: true  
            },
            {
                name: "Ageing",
                label: "Ageing",
                cell: row => row.Ageing || row.Ageing == 0 ? row.Ageing : "N.A",
                sortable: true,
                width: "70px"
                //searchable: true  
            },
            {
                label: "CJ Url",
                cell: row => <div>
                    <a href="#" onClick={(e) => this.OpenCJUrl(e, row.SupplierId, row.ProductID, row.LeadID, 0)} target="_blank">Continue </a>
                </div>,
                sortable: true,
                width: "80px"

            },
            {
                label: "Copy URL",
                cell: row => <div className="PfPendingLink">
                    <Button className="PfLinkBtn" id={"PfLinkBtn_" + row.LeadID} onClick={(e) => this.OpenCJUrl(e, row.SupplierId, row.ProductID, row.LeadID, 1)} variant="primary" >Copy URL</Button>
                </div>,
                sortable: true,
                width: "100px"

            },
        ];

    }

    handleExport = () => {

        const newArray = Array.isArray(this.state.PFPendingItems) && this.state.PFPendingItems.map(obj => {
            const { LeadID, Name, StatusName, OfferCreateON: BookingDate, Ageing, SupplierName, UserName: SalesAgentName,
                EmployeeId: SalesAgentEmpId, UserGroupName, ReporterName, ReporterEmpID, ProposalFillDate } = obj; // Use destructuring to rename the "name" key to "fullName"
            if (this.state.ReportType == 1)
                return {
                    LeadID, Name, StatusName, BookingDate, Ageing, SupplierName, SalesAgentName,
                    SalesAgentEmpId, UserGroupName, ReporterName, ReporterEmpID
                };
            else
                return {
                    LeadID, Name, StatusName, BookingDate, Ageing, SupplierName, SalesAgentName,
                    SalesAgentEmpId, UserGroupName, ReporterName, ReporterEmpID, ProposalFillDate
                };
        });
        if (Array.isArray(newArray) && newArray.length > 0) {
            const sheet = XLSX.utils.json_to_sheet(newArray);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
            XLSX.writeFile(workbook, 'PFPendingReport.xlsx');
        }
    };

    async OpenCJUrl(e, SupplierId, ProductId, LeadId, copyCJUrl) {
        e.preventDefault();

        let data = {
            "SupplierId": SupplierId,
            "ProductID": ProductId,
            "LeadId": LeadId
        }
        let response = await GetCJUrl(data);

        if (response && copyCJUrl == 1) {
            const buttons = document.getElementsByClassName('PfLinkBtn');
            for (let i = 0; i < buttons.length; i++) {
                if (buttons[i]) {
                buttons[i].innerText = 'Copy URL';
                }
            }
            const button = document.getElementById('PfLinkBtn_' + LeadId);
            button.innerText = 'Copied';
            //navigator.clipboard.writeText(response);
            var textField = document.createElement('textarea')
            textField.innerText = response
            document.body.appendChild(textField)
            textField.select()
            document.execCommand('copy')
            textField.remove()
        }
        else if (response && copyCJUrl == 0)
            window.open(response);

        setInterval(function () {
            const button = document.getElementById('PfLinkBtn_' + LeadId);
            button.innerText = 'Copy URL';
        }.bind(this), 3000)

    }

    componentDidMount() {
        let user = getuser();
        this.getUserListFromSupervisor(user.UserID);
        this.setState({ clickSearch: true, RoleId: user.RoleId })
        this.setSuppliers("TermLife", 7);
        //this.setSupervisors(12, 7);
        // setTimeout(function () {
        //     this.getPFPendingLeads();
        //   }.bind(this), 2000);       
    }

    componentWillReceiveProps(nextProps) {

        if (!nextProps.CommonData.isError) {
            this.setState({ items: nextProps.CommonData[this.state.root] });
        }

    }

    componentDidUpdate(prevProps, prevState) {
        if (prevState.UserList != this.state.UserList && this.state.clickSearch) {
            this.getPFPendingLeads();
        }
    }

    setMedicalTypeSelected(list) {
        this.setState({ medicalSelectValue: list });
    }

    ReportTypechange(e) {
        this.setState({ ReportType: (e.target.value) ? e.target.value : 1, PFPendingItems: [] })
        if (e.target.value == 2) {
            this.setState({ enableMedicalType: true })
        }
        else {
            this.setState({ enableMedicalType: false, medicalSelectValue: [] })
        }

    }

    Ageingchange(e) {
        this.setState({ ageing: (e.target.value) ? e.target.value : 0 })
    }

    Productschange(e) {
        let user = getuser();
        this.setState({ medicalSelectValue: [], managerSelectValue: [], supplierSelectValue: [] })
        let productId = e.target.value
        this.setState({ ProductId: productId }, () => {
            this.getUserListFromSupervisor(user.UserID);
        });
        var index = e.nativeEvent.target.selectedIndex;
        var productname = e.nativeEvent.target[index].text
        this.setSuppliers(productname, productId);
    }

    setSuppliers(productname, productId) {
        this.props.GetCommonspDataV2({
            root: 'GetSuppliersAndPlans',
            c: "R",
            params: [{ ProductName: productname, ProductId: productId }],

        }, function (data) {
            if (data?.data?.data && data.data.data.length > 0 && data.data.data[0].length > 0) {
                let dropdownValuesMulti = [];
                let dropdownValues = data.data.data[0];
                for (let index = 0; index < data.data.data[0].length; index++) {
                    const element = dropdownValues[index];
                    let obj = { label: element.SupplierName, value: element.ID };
                    dropdownValuesMulti.push(obj);
                }
                this.setState({ SupplierMultiList: dropdownValuesMulti })
            }
        }.bind(this));

    }

    setSupplierSelected(list) {
        this.setState({ supplierSelectValue: list });
    }


    fnDatatableCol() {
        let _columns = this.columnlist;
        let ReportType = this.state.ReportType;
        if (ReportType == 2) {
            _columns = [...this.columnlist, ...this.postColumnList]
        } else {
            _columns = this.columnlist;
        }

        var columns = fnDatatableCol(_columns);
        return columns;
    }

    handleStartDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.setState({ StartDate: e.format("YYYY-MM-DD") }, function () {
            });
        }
    }

    handleEndDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.setState({ EndDate: e.format("YYYY-MM-DD") }, function () {
            });
        }
    }

    validation = (currentDate) => {
        return currentDate.isBefore(moment());

    };

    validationEndDate = (currentDate) => {


        if (!currentDate.isBefore(moment().add(30, "days").format("YYYY-MM-DD"))) {
            return false;
        }

        if (currentDate.isBefore(moment(this.state.StartDate))) {
            return false;
        }

        return true;

    };

    HandleSearchClick() {
        this.setState({ clickSearch: true })
        this.getPFPendingLeads()
    }

    handleShow(e) {
        this.setState({ SelectedSupervisors: e.SelectedSupervisors, clickSearch: true });
        this.getUserListFromSupervisor(e.SelectedSupervisors);
    }

    async getUserListFromSupervisor(ManagerList) {
        let ManagerListString = '';
        if (ManagerList && Array.isArray(ManagerList) && ManagerList.length > 0) {
            ManagerListString = ManagerList.join();
        } else {
            ManagerListString = ManagerList;
        }
        await this.props.GetCommonspDataV2({
            root: 'GetAgentFromSupervisor',
            c: "R",
            params: [{ ProductID: this.state.ProductId, SupervisorId: ManagerListString }],

        }, function (data) {
            if (data?.data?.data && data.data.data.length > 0 && data.data.data[0].length > 0) {
                let UserList = data.data.data[0];
                this.setState({ UserData: UserList });
                var userIdArray = UserList.map(obj => parseInt(obj.UserID));
                this.setState({ UserList: userIdArray })
            } else {
                this.setState({ UserList: [ManagerListString] })
            }
        }.bind(this));

    }

    async getPFPendingLeads() {
        let supplierSelectValue = this.state.supplierSelectValue
        let medicalSelectValue = this.state.medicalSelectValue
        let SupplierList = ''; let MedicalList = ''; let ReportType = this.state.ReportType; let response = []

        if (supplierSelectValue.length > 0) {

            let suppliers = supplierSelectValue.map(item => {
                return item.value
            })
            SupplierList = suppliers.toString()
        }
        if (medicalSelectValue.length > 0) {

            let medical = medicalSelectValue.map(item => {
                return item.value
            })
            MedicalList = medical.toString()
        }

        let data = {
            "SupplierId": 0,
            "ProductID": this.state.ProductId,
            "FromDate": moment(this.state.StartDate).format("YYYY-MM-DD"),
            "ToDate": moment(this.state.EndDate).format("YYYY-MM-DD"),
            "GroupId": "0",
            //"SupervisorId": ManagerList,
            //"Age": this.state.ageing,
            "InsurerIds": SupplierList,
            "MedicalType": MedicalList,
            "UserList": this.state.UserList,
            "RequestType": (ReportType == 1) ? 1 : 2,
            "MinAgeing": (this.state.ageing == 1) ? 0 : (this.state.ageing == 2) ? 6 : (this.state.ageing == 3) ? 10 : 0, //@MinAgeing = CASE WHEN @age = 1 THEN 0 WHEN @age = 2 THEN 6  ELSE 10 END
            "MaxAgeing": (this.state.ageing == 1) ? 6 : (this.state.ageing == 2) ? 10 : (this.state.ageing == 3) ? 180 : 0,//@MaxAgeing = CASE  WHEN @age = 1 THEN 6  WHEN @age = 2 THEN 10  ELSE 180  END
        }
        response = await GetPendingDocumentLeadList(data)
        this.setState({ clickSearch: false })
        if (response && response.PendingDocLeadList) {
            let PFPendingItems = response.PendingDocLeadList
            let FilteredPFPendingItems = PFPendingItems.filter((item) => item.EmployeeId !== '');
            this.mergeUserDetailsInDocpending(FilteredPFPendingItems);
        }
        else if (response && response.PendingDocumentLeadList) {
            let DocPendingItems = response.PendingDocumentLeadList
            let FilteredDocPendingItems = DocPendingItems.filter((item) => item.EmployeeId !== '');
            this.mergeUserDetailsInDocpending(FilteredDocPendingItems);
        }
        else {
            this.setState({ PFPendingItems: [] })
        }
    }

    mergeUserDetailsInDocpending(FilteredData) {
        // Create a map for efficient lookup based on the common key
        let UserArrayMap = {};
        this.state.UserData.forEach(obj => {
            UserArrayMap[obj.EmpId] = {
                "UserGroupName": obj.UserGroupName ? obj.UserGroupName : 'N.A',
                "ManagerId": obj.ManagerId ? obj.ManagerId : 'N.A',
                "ManagerName": obj.ManagerName ? obj.ManagerName : 'N.A'
            };
        });


        // Concatenate ManagerId, ManagerName, and UserGroupName from the second array into the first array
        FilteredData.forEach(obj => {
            let empId = obj.EmployeeId;
            if (UserArrayMap[empId]) {
                let userData = UserArrayMap[empId];
                obj.UserGroupName = userData.UserGroupName;
                obj.ReporterEmpID = userData.ManagerId;
                obj.ReporterName = userData.ManagerName;
            }
        });

        //console.log(FilteredData);
        this.setState({ PFPendingItems: FilteredData });
    }

    render() {
        const columns = this.fnDatatableCol();
        const { PFPendingItems, PageTitle, clickSearch } = this.state;

        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>
                                    <Row>
                                        <Col md={11}>
                                            <CardTitle tag="h4">{PageTitle}</CardTitle>
                                        </Col>
                                        <Col md={1}>

                                        </Col>
                                    </Row>
                                    {<ManagerHierarchy handleShow={this.handleShow} value={/UserID/g} ></ManagerHierarchy>}
                                </CardHeader>
                                <CardBody>
                                    <Row>
                                        <Col md={2}>
                                            <Form.Group>
                                                <Form.Label>{"Report Type"}</Form.Label>
                                                <DropDown firstoption="PF Pending" firstoptionValue={1} items={this.state.ReportTypes} onChange={this.ReportTypechange.bind(this)}>
                                                </DropDown>
                                            </Form.Group>
                                        </Col>
                                        {
                                            this.state.enableMedicalType &&

                                            <Col md={2}>
                                                <Form.Group>
                                                    <Form.Label>{"Medical Type"}</Form.Label>
                                                    <MultiSelect
                                                        options={this.state.dropdownValues}
                                                        value={this.state.medicalSelectValue}
                                                        onChange={this.setMedicalTypeSelected.bind(this)}
                                                    />
                                                </Form.Group>
                                            </Col>

                                        }
                                    </Row>
                                    <Row>
                                        <Col md={2}>
                                            <Form.Group>
                                                <Form.Label>Products</Form.Label>
                                                <DropDown firstoption="TermLife" firstoptionValue={7} items={this.state.Products} onChange={this.Productschange.bind(this)}>
                                                </DropDown>
                                            </Form.Group>
                                        </Col>
                                        <Col md={2}>
                                            <Form.Group>
                                                <Form.Label>Insurer</Form.Label>
                                                <MultiSelect
                                                    options={this.state.SupplierMultiList}
                                                    value={this.state.supplierSelectValue}
                                                    onChange={this.setSupplierSelected.bind(this)}
                                                />
                                            </Form.Group>
                                        </Col>
                                        {/* <Col md={3}>
                                            <Form.Group>
                                                <Form.Label>Reporting Manager</Form.Label>
                                                <MultiSelect
                                                    options={this.state.ManagerMultiList}
                                                    value={this.state.managerSelectValue}
                                                    onChange={this.setManagerSelected.bind(this)}
                                                />
                                            </Form.Group>
                                        </Col> */}
                                    </Row>
                                    <Row>
                                        <Col md={2}>
                                            <Form.Label>Booking From Date</Form.Label>

                                            <Datetime
                                                // value={new Date()}
                                                dateFormat="YYYY-MM-DD"
                                                value={this.state.StartDate}
                                                isValidDate={this.validation}
                                                onChange={moment => this.handleStartDateChange(moment)}
                                                utc={true}
                                                timeFormat={false}
                                                closeOnSelect={true}
                                            />
                                        </Col>
                                        <Col md={2}>
                                            <Form.Label>Booking To Date</Form.Label>

                                            <Datetime
                                                // value={new Date()}
                                                dateFormat="YYYY-MM-DD"
                                                value={this.state.EndDate}
                                                isValidDate={this.validationEndDate.bind(this)}
                                                onChange={moment => this.handleEndDateChange(moment)}
                                                utc={true}
                                                timeFormat={false}
                                                closeOnSelect={true}
                                            />
                                        </Col>
                                        <Col md={2}>
                                            <Form.Group>
                                                <Form.Label>Ageing</Form.Label>
                                                <DropDown firstoption="---All---" firstoptionValue={0} items={this.state.Ageing} onChange={this.Ageingchange.bind(this)}>
                                                </DropDown>
                                            </Form.Group>
                                        </Col>
                                        <Col md={2}>
                                            <button id='searchbtn' className="SearchBTN" onClick={this.HandleSearchClick}> Search <i class={this.state.clickSearch ? "fa fa-spinner fa-spin" : ""}></i></button>

                                        </Col>
                                    </Row>
                                    <Col md={12} className="mt-2">
                                        Lead Count: {PFPendingItems.length}
                                    </Col>
                                    {<strong style={{ color: "red" }}>{this.ADMIN_AND_MANAGER_NOTE}</strong>}
                                    {Array.isArray(PFPendingItems) && PFPendingItems.length > 0 ? <span onClick={this.handleExport} class="downloadExcel"></span> : ''}
                                    {clickSearch && <center><Loader /></center>}
                                    {!clickSearch && <DataTable
                                        columns={columns}
                                        defaultSortField="IsActive"
                                        defaultSortAsc={false}
                                        data={Array.isArray(PFPendingItems) && PFPendingItems.length > 0 ? PFPendingItems : []}
                                        printexcel={false}
                                        ref={this.tableRef}
                                    />}
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>

                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        addRecord,
        GetCommonspData,
        GetCommonspDataV2,
    }
)(PfPending);