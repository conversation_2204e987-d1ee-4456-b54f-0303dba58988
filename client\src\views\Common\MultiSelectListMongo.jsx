import React from "react";
import { connect } from "react-redux";
// reactstrap components

import { GetCommonData } from "../../store/actions/CommonMongoAction";

import _ from "underscore";
import {MultiSelect} from "react-multi-select-component";

class MultiSelectList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      selectedValue: this.props.value
        ? this.props.value
        : [{ label: "Select Values", value: "0" }],
      name: "",
    };
  }
  componentDidMount() {
    if (this.state.items && this.state.items.length === 0) {
      this.props.GetCommonData({
        root: this.props.col.config.root,
        cols: this.props.col.config.cols,
        con: this.props.col.config.con,
        data: this.props.col.config.data,
        statename: this.props.col.config.statename,
        state: this.props.col.config.state == false ? false : true,
      });
    }
  }
  componentWillReceiveProps(nextProps) {
    let multiItems = [];
    if(nextProps.filtervalue != this.props.filtervalue){
        this.setState({ selectedValue :  [{ label: "Select Values", value: "0" }]})
    }
    if (!nextProps.CommonData.isError) {
      let items =
        nextProps.CommonData[
          this.props.col.config.statename ?? this.props.col.config.root
        ]; 

      this.setState({ name: this.props.col.name });
      if (items && nextProps.valueTobefiltered) {
        items = items.filter(function (n) {
          return nextProps.valueTobefiltered.indexOf(n.Id) > -1;
        });
      }
      // console.log("here::::",this.state);

      if (items && items.length > 0) {
        if (this.props.col.config.root == "roles") {
          multiItems.push({
              
            value: "user",
            label: "user",
            
          })
          const reqItems=[{_id:"TL", value:"TL"},
          {_id:"livechat-agent",name:"livechat-agent"},
          {_id:"livechat-guest",name:"livechat-guest"},
          {_id:"livechat-manager",name:"livechat-manager"},
          // {_id:"Manager",name:"Manager"},
          ]
          items = reqItems. filter(item1 => items. some(item2 => item1._id === item2._id))
          items.map((item) =>item.name?
          
            multiItems.push({
              
              value: item._id,
              label: item.name,
              
            })
          :''
          );
        
        } else if(this.props.col.config.root == "livechat_department") {
          const itemMap = {};
          items.forEach((i) => {
            const key = i['_id'];
            itemMap[key] = i;
          });
          
          if(this.state.selectedValue && this.state.selectedValue.length) {
            this.state.selectedValue.forEach((value, key) => {
            if(itemMap[value['value']]) {
              this.state.selectedValue[key] = {
                'label' : itemMap[value['value']]['name'],
                'value' : itemMap[value['value']]['_id'],
              } 
            }
            });
          }
          items.map((item) =>
            multiItems.push({
              value: item._id,
              label: item.name || '',
            })
          );
        } 
        else {
          items.map((item) =>
            multiItems.push({
              value: item._id,
              label: item.name || '',
            })
          );
        }
      }
      this.setState({ items: multiItems });
    }
  }

  displayoption(item) {
    return (
      <option key={item.Id} value={item.Id}>
        {item.Display}
      </option>
    );
  }

  onSelect(selectedList, selectedItem) {
    // console.log("onselect");
    var newselectedList = selectedList.filter(
      (task) => task.label !== "Select Values"
    );
    let selectAll = false;
    if (this.state.items.length == selectedList.length) {
      selectAll = true;
    }
    this.setState({ selectedValue: newselectedList });
    this.props.onChange({
      id: this.props.id,
      newselectedList,
      selectAll: selectAll,
      type: "multiselect",
    });
  }

  setItemMultiselect(items) {
    debugger;
    let multiItems = [];
    let filter = [];
    let filtervalue = parseInt(this.props.filtervalue);
    if (this.props.filterkey && this.props.filtervalue) {
      items.map(
        function (item, i) {
          if (this.props.filtervalue == 7) {
            if (item.ProductID == 7 || item.ProductID == 1000) {
              filter = multiItems.filter((task) => task.value == item.value);
              if (filter.length == 0) {
                multiItems.push({
                  value: item.value,
                  label: item.label,
                });
              }
            }
          } else if (item.ProductID == filtervalue) {
            multiItems.push({
              value: item.value,
              label: item.label,
            });
          }
        }.bind(this)
      );
    } else {
      multiItems = items;
    }
    let selectedValue = this.state.selectedValue;
    if (
      multiItems.length > 0 &&
      selectedValue &&
      selectedValue.length > 0 &&
      selectedValue[0].value == "*"
    ) {
      this.setState({ selectedValue: multiItems });
      // console.log("...............................", this.state.selectedValue);
    }
    return multiItems;
  }

  render() {
    let { items } = this.state;
    const { value, onChange, visible, name } = this.props;
    if (!items) {
      items = [];
    }
    if (visible == false) {
      return null;
    }
    let multiitem = this.setItemMultiselect(items);
    return (
      <div>
        <MultiSelect
          options={multiitem} // Options to display in the dropdown
          value={this.state.selectedValue} // Preselected value to persist in dropdown
          onChange={this.onSelect.bind(this)} // Function will trigger on select event
          disabled={this.props.disabled}

          // labelledBy={"Select IVR Queues"}
          // selectAllLabel={"Select ALL IVR Queues"}
        />
      </div>
    );
  }
}
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}

export default connect(mapStateToProps, {
  GetCommonData,
})(MultiSelectList);
