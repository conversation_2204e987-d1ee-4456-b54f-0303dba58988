const tblList = require("../constants");
const methods = require("./GMCApiMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");




async function getUserInfo(req, res) {
    try {
       await methods.getUserInfo(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


module.exports = {
    getUserInfo: getUserInfo
};