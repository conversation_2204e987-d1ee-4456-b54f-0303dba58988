import React, { useCallback, useContext, useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { GetCommonspDataV2 } from "store/actions/CommonAction";
import { Accordion, AccordionSummary, AccordionDetails, Typography } from '@mui/material';

import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

import WarningIcon from '@mui/icons-material/Warning';

import '../../../assets/scss/_fosappointmentjourney.scss';

import advisorIdle from '../../../assets/icons/advisor.svg';
import advisorLoggedOut from '../../../assets/icons/advisorLogout.svg'


import moment from 'moment';
import { fetchDistance } from "utility/utility";
import { calculateTimeTaken } from "utility/utility";
// import { Loader } from "./Loader";
import { FosContext } from "../FosContext";
import {AppointmentsContext} from "../AppointmentsContext";
import AppointmentList from "./AppointmentList";


const FosAppointmentJourney = (props) => {


    const { data, appointmentCard}=props;

    const [appointmentConfirmation, setappointmentConfirmation] = useState(null);

    const [appointmentTime, setAppointmentTime] = useState({

        appConfirmed: null,
        startJourney: null,
        endJourney: null,
        meetingStart: null,
        meetingEnd: null
    });

    const [startJourneyTime, setStartJourneyTime] = useState(null);

    const [appConfirmTime, setAppConfirmTime] = useState(null);


    const [expanded, setExpanded] = useState(true);

    const [distanceTravelled, setDistanceTravelled] = useState(null);

    const { openSalesview } = useContext(FosContext);

    // const [appointmentCard, setAppointmentCard]= useState(appointmentDetails);


    function formatDate(date) {
        return moment(date).format('Do MMM, YYYY | hh:mm A');
      }

    const onhandleAccordianToggle = () => {
        setExpanded(!expanded);
    }


    useEffect(() => {
        // console.log("The expandes is ", expanded)
    }, [expanded])

    //Appointment data
    useEffect(() => {
        GetData();
    }, [props.data.ParentId])

    const GetData = () => {

        if (props.data.ParentId) {
            setAppConfirmTime(null);
            setappointmentConfirmation(null);
            setAppointmentTime({
                appConfirmed: null,
                startJourney: null,
                endJourney: null,
                meetingStart: null,
                meetingEnd: null
            })

            props.GetCommonspDataV2({
                root: 'GetAppointmentJourney',
                c: "R",
                params: [{ LeadId: parseInt(props.data.ParentId) }],
            }, (data) => {

                if (Array.isArray(data?.data?.data) && data.data.data.length > 0) {
                    // console.log("GETAPPointment Journey is ", data.data.data);
                    setExpanded(true);
                    let appointments = data.data.data[0];

                    let now = moment();



                    let timings = {

                        appConfirmed: null,
                        startJourney: null,
                        endJourney: null,
                        meetingStart: null,
                        meetingEnd: null
                    };
                    for (let i = 0; i < appointments.length; i++) {
                        // console.log("The appointmentsis ", appointments[i])

                        appointments[i].CreatedOn = new Date(appointments[i].CreatedOn);
                        appointments[i].CreatedOn.setHours(appointments[i].CreatedOn.getHours() - 5);
                        appointments[i].CreatedOn.setMinutes(appointments[i].CreatedOn.getMinutes() - 30);
                        appointments[i].CreatedOn.toString();
                        // appointments[i].CreatedOn = appointments[i].CreatedOn.slice(0, -1) + '-05:30';

                        if (appointments[i].SubStatusId === 2193) {

                            // setStartJourneyTime(appointments[i].CreatedOn - GetDateTime ());
                            let AppCreatedOn = new Date(appointments[i].CreatedOn).getTime();
                            let currDateTime = new Date().getTime();

                            let diffInMin = (AppCreatedOn - currDateTime) / (1000 * 60);



                            timings['startJourney'] = moment(appointments[i].CreatedOn).format('HH:mm');

                            setStartJourneyTime(parseInt(diffInMin));
                        }
                        else if (appointments[i].SubStatusId === 2194) {

                            timings['endJourney'] = moment(appointments[i].CreatedOn).format('HH:mm');

                        }
                        else if (appointments[i].SubStatusId === 2003) {

                            // setappointmentConfirmation(appointments[i]); 
                            timings['meetingEnd'] = moment(appointments[i].CreatedOn).format('HH:mm');
                        }
                        else if (appointments[i].SubStatusId === 2124) {

                            timings['meetingStart'] = moment(appointments[i].CreatedOn).format('HH:mm');
                            setDistanceTravelled(appointments[i].EndJourneyDistance);

                        }
                        else if (appointments[i].SubStatusId === 2088) {

                            setAppConfirmTime(moment(appointments[i].CreatedOn).format('DD/MM/YY HH:mm'))
                            timings['appConfirmed'] = moment(appointments[i].CreatedOn).format('HH:mm');
                        }

                    }


                    if (appointments.length > 0) {
                        setappointmentConfirmation(appointments[appointments.length - 1]);
                    }

                    setAppointmentTime(timings)
                }
            })
        }

    }

    const appointmentStatusId = Object.freeze({
        AppointmentConfirmed: [2088, 2193, 2194, 2124, 2003],
        StartJourney: [2193, 2194, 2124, 2003],
        StopJourney: [2194, 2124, 2003],
        StartAppointment: [2124, 2003],
        OTPVerification: [2124, 2003],
        EndAppointment: [2003]
    });

    // useEffect(()=>{

    //     console.log("Appointment card is ", appointmentCard.length);

    // },[appointmentCard])

    return (

       <AppointmentsContext.Provider value={{appointmentCard:appointmentCard, currentStatus : parseInt(props.data.RealTimeStatusId) || null, contextUsedFrom: "AppointmentList"}}> 
        <div className='innerDetails-box'>

            {/* {!props.loader ?

            <> */}

            {/* Travelling */}

            {props.data.RealTimeStatusId === 1 &&
                <Accordion className="travelling" expanded={expanded} >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='left-content'>
                            {/* <div>gfg</div> */}

                           
{/* <><h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5><h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '9px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3></> */}
<h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5>

                                {/* {expanded ?

                                    <h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '10px' }}>{'(' + props.data.EmployeeId + ')'}</span> &nbsp; <span>{props.data.CustomerName} &nbsp;<strong style={{ fontWeight: '10px' }} onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?  '(' +  props.data.ParentId + ')' : null}</strong></span></h3> :
                                    <><h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5><h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '9px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3></>

                                } */}
                             
                            </div>
                            {/* {console.log("AppointmentList ",appointmentCard.length)} */}
                           {appointmentCard.length>0 && <AppointmentList totalAppointments={appointmentCard.length}/>}
                            <div className='ryt-content'>
                              
                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ? <AddIcon /> : <RemoveIcon />}
                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">
                        {appointmentConfirmation &&

                            <>
                                <div className="progress-bar-container pt-3">
                                    <div className="progress-bar">

                                        <div className={`progress-step ${appointmentStatusId.StartJourney.includes(appointmentConfirmation?.SubStatusId) ? 'completed' : ''}`}>
                                            {appointmentTime.startJourney && <span className="time">{appointmentTime.startJourney}</span>}
                                            <div className="step-label underline" onClick={() => props.changeCenterBaseOn(2193)}>Journey Start<img src='/Images/start-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip start' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.endJourney && <span className="time">{appointmentTime.endJourney}</span>} */}
                                            <div className="step-label">Journey End <img src='/Images/stop-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip end' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.meetingStart && <span className="time">{appointmentTime.meetingStarts}</span>} */}
                                            <div className="step-label">Appt Start <img src='/Images/meeting-appointment.png' style={{ width: '15px', height: '15px' }} alt='Meet Start' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.meetingEnd && <span className="time">{appointmentTime.meetingEnd}</span>} */}
                                            <div className="step-label">Appt End</div>
                                            {/* <div className="step-label">App End <img src='/Images/start-appointment.png' style={{width:'15px', height:'15px'}} alt='Meet End' /></div> */}
                                            <div className='progress-line '></div>
                                        </div>
                                    </div>
                                </div>
                                <div className="appointment-details">
                                    <ul className="d-flex">



                                        <li>
                                            <label>Last Location Captured On</label>

                                            <span style={{ color: '#8B0000' }}>

                                                {props.data?.insertedAt ? moment.utc(props.data?.insertedAt).format('DD/MM/YY HH:mm') : '-'}
                                            </span>
                                        </li>


                                        <li>
                                            <label>Action on appointment</label>
                                            {/* {console.log(appointmentConfirmation?.CreatedOn)} */}
                                            {moment(appointmentConfirmation?.CreatedOn).format('DD/MM/YY HH:mm')}

                                        </li>


                                        <li>
                                            <label>Travelled distance</label>
                                            <span>{props?.data?.overallDistance ? `${Math.round((parseFloat(props.data.overallDistance) + Number.EPSILON) * 100) / 100} KMs` : '-'}</span>
                                        </li>

                                        <li>
                                            <label>Away from CX</label>
                                            <span>{props?.data?.distanceFromCustomer != null ? `${fetchDistance(props?.data?.distanceFromCustomer)}` : '-'}</span>

                                        </li>

                                        <li>
                                            <label>Trip started since</label>
                                            <span>{props?.data?.Since && props.data['Since']['time'] + ' ' + props.data['Since']['den']}</span>
                                            {/* 2193 - currenttime */}
                                        </li>
                                    </ul>
                                    {/* {props?.data?.MethodIdentifier && <><WarningIcon/> <span> {props.data.MethodIdentifier}</span></>} */}
                                </div>
                               
                            </>
                        }
                    </AccordionDetails>
                </Accordion>
            }

            {/* Meeting */}
            {props.data.RealTimeStatusId === 2 &&
                // <Accordion className="meeting" onChange={onhandleAccordianToggle} defaultExpanded >
                <Accordion className="meeting" expanded={expanded}  >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='left-content'>

                            {/* <><h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5><h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '9px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3></> */}
                            <h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5>
                                {/* {expanded ?

                                    <h3 style={{ fontSize: '12px' }}>
                                        {props.data.UserName}
                                        &nbsp;
                                        <span style={{ fontSize: '10px' }}>
                                            {'(' + props.data.EmployeeId + ')'}</span> &nbsp;&nbsp;
                                        <span>
                                            {props.data.CustomerName} &nbsp;
                                            <strong style={{ fontSize: '10px' }} onClick={() => { openSalesview(props.data.ParentId) }}>
                                                {parseInt(props.data.ParentId) ? '(' + props.data.ParentId  + ')':null}
                                            </strong> 
                                        </span>
                                    </h3>
                                    :
                                    <>
                                        <h5>
                                            {props.data.CustomerName}
                                            &nbsp;
                                            <strong onClick={() => {openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId) ?  '(' +props.data.ParentId + ')':null}
                                            </strong>
                                        </h5>
                                        <h3 style={{ fontSize: '12px' }}>
                                            {props.data.UserName}
                                            &nbsp;
                                            <span style={{ fontSize: '9px' }}>
                                                {'(' + props.data.EmployeeId + ')'}
                                            </span>
                                        </h3>
                                    </>

                                } */}
                                    {/* <AppointmentList totalAppointments={appointmentCard.length}/> */}
                            </div>
                           {appointmentCard.length>0 && <AppointmentList totalAppointments={appointmentCard.length}/>}
                            <div className='ryt-content'>

                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ?
                                        <AddIcon /> : <RemoveIcon />}

                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">
                        {appointmentConfirmation && <>
                            <div className="progress-bar-container pt-3">
                                <div className="progress-bar">

                                    <div className={`progress-step ${appointmentStatusId.StartJourney.includes(appointmentConfirmation?.SubStatusId) ? 'completed' : ''}`}>
                                        {appointmentTime.startJourney && <span className="time">{appointmentTime.startJourney}</span>}
                                        <div className="step-label underline" onClick={() => props.changeCenterBaseOn(2193)}>Journey Start<img src='/Images/start-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip start' /></div>
                                        <div className='progress-line'></div>
                                    </div>
                                    <div className={`progress-step ${appointmentStatusId.StopJourney.includes(appointmentConfirmation?.SubStatusId) ? 'completed' : ''}`}>
                                        {appointmentTime.endJourney && <span className="time">{appointmentTime.endJourney}</span>}
                                        <div className="step-label underline" onClick={() => props.changeCenterBaseOn(2194)}>Journey End<img src='/Images/stop-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip end' /></div>
                                        <div className='progress-line'></div>
                                    </div>
                                    <div className={`progress-step ${appointmentStatusId.StartAppointment.includes(appointmentConfirmation?.SubStatusId) ? 'completed' : ''}`}>
                                        {appointmentTime.meetingStart && <span className="time">{appointmentTime.meetingStart}</span>}
                                        <div className="step-label underline" onClick={() => props.changeCenterBaseOn(2124)}>Appt Start<img src='/Images/meeting-appointment.png' style={{ width: '15px', height: '15px' }} alt='Meet Start' /></div>
                                        <div className='progress-line'></div>
                                    </div>
                                    <div className={`progress-step`}>

                                        <div className="step-label">Appt End</div>
                                        <div className='progress-line'></div>
                                    </div>
                                </div>
                            </div>
                            <div className="appointment-details">
                                <ul className="d-flex">

                                    <li>
                                        <label>Last Location Captured On</label>

                                        <span style={{ color: '#8B0000' }}>

                                            {props.data?.insertedAt ? moment.utc(props.data?.insertedAt).format('DD/MM/YY HH:mm') : '-'}
                                        </span>
                                    </li>
                                    <li>
                                        <label>Action on appointment</label>
                                        {moment(appointmentConfirmation?.CreatedOn).format('DD/MM/YY HH:mm')}

                                    </li>
                                    <li>
                                        <label>Travelled distance</label>
                                        <span>{distanceTravelled != null ? `${Math.round((parseFloat(distanceTravelled) + Number.EPSILON) * 100) / 100} KMs` : '-'}</span>
                                    </li>
                                    <li>
                                        <label>Meeting Duration</label>
                                        <span>{props?.data?.Since && props.data['Since']['time'] + ' ' + props.data['Since']['den']}</span>

                                    </li>
                                </ul>
                            </div>
                        </>
                        }
                    </AccordionDetails>
                </Accordion>
            }

            {/* Calling */}
            {props.data.RealTimeStatusId === 3 &&
                <Accordion className="calling" expanded={expanded}  >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='left-content'>
                                <h5> <span>{props.data.CustomerName} <strong onClick={() => {openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?props.data.ParentId:null}</strong> </span></h5>
                                <h3>{props.data.UserName}&nbsp;<span style={{ fontSize: '12px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3>
                                {/* <AppointmentList totalAppointments={appointmentCard.length}/> */}
                            </div>
                            {appointmentCard.length>0 && <AppointmentList totalAppointments={appointmentCard.length}/>}
                            <div className='ryt-content'>

                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ? <AddIcon /> : <RemoveIcon />}
                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">

                        <div className="appointment-details">
                            <ul className="d-flex">

                                <li>
                                    <label>Last Location Captured On</label>

                                    <span style={{ color: '#8B0000' }}>

                                        {props.data?.insertedAt ? moment.utc(props.data?.insertedAt).format('DD/MM/YY HH:mm') : '-'}
                                    </span>
                                </li>
                                {appointmentConfirmation &&
                                    <li>
                                        <label>Action on appointment</label>
                                        {moment(appointmentConfirmation?.CreatedOn).format('DD/MM/YY HH:mm')}

                                    </li>
                                }
                                {/* <li>
                                    <label>Travelled distance</label>
                                    <span>{props.data.overallDistance}KMs</span>
                                </li> */}
                                <li>
                                    <label>In Call Since</label>

                                    <span>{props?.data?.Since && props.data['Since']['time'] + ' ' + props.data['Since']['den']}</span>
                                </li>
                            </ul>
                        </div>

                    </AccordionDetails>
                </Accordion>
            }

            {/* Idle */}

            {props.data.RealTimeStatusId === 4 &&
                <Accordion className="idle" expanded={expanded} >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='left-content'>
                            {/* <h3 style={{ fontSize: '11px' }}>{props.data.UserName}  <span style={{ fontSize: '10px' }}>{'(' + props.data.EmployeeId + ')'}</span> &nbsp; <span style={{ fontWeight: 400 }}>{props.data.CustomerName} &nbsp;<strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt( props.data.ParentId ) ?  '(' + props.data.ParentId + ')': null}</strong></span></h3>  */}
                            <h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5>
                                {/* {expanded ?

                                    <h3 style={{ fontSize: '11px' }}>{props.data.UserName}  <span style={{ fontSize: '10px' }}>{'(' + props.data.EmployeeId + ')'}</span> &nbsp; <span style={{ fontWeight: 400 }}>{props.data.CustomerName} &nbsp;<strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt( props.data.ParentId ) ?  '(' + props.data.ParentId + ')': null}</strong></span></h3> :
                                    <><h5><span style={{ fontSize: '11px', fontWeight: 500 }}>{props.data.CustomerName}&nbsp;<strong onClick={() => {openSalesview(props.data.ParentId) }}>{ parseInt(props.data.ParentId) ? '(' + props.data.ParentId  + ')':null}</strong></span></h5><h3 style={{ fontSize: '10px' }}>{props.data.UserName}<span style={{ fontSize: '9px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3></>

                                } */}
                                      {/* <AppointmentList totalAppointments={appointmentCard.length}/> */}
                            </div>
                            {appointmentCard.length>0 && <AppointmentList totalAppointments={appointmentCard.length}/>}
                            <div className='ryt-content'>

                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ? <AddIcon /> : <RemoveIcon />}
                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">
                        <div className="advisor-idle d-flex align-items-center">
                            <figure className="m-0">
                                <img src={advisorIdle} alt="Advisor Idle" />
                            </figure>
                            <div className="advisor-details">
                                <h3>Advisor is idle</h3>
                                <p>Advisor has been idle for the past {props?.data?.Since && props.data['Since']['time']} minutes</p>
                                <p>
                                    Last Location Captured On :
                                    &nbsp;
                                    <span style={{ color: '#8B0000' }}>

                                        {props.data?.insertedAt ? moment.utc(props.data?.insertedAt).format('DD/MM/YY HH:mm') : '-'}
                                    </span>

                                </p>
                            </div>
                        </div>
                    </AccordionDetails>
                </Accordion>
            }

            {/* Logged Out */}
            {props.data.RealTimeStatusId === 5 &&
                <Accordion className="idle loggedout-out" expanded={expanded} >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='ryt-content'>
                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ? <AddIcon /> : <RemoveIcon />}
                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">
                        <div className="advisor-idle d-flex align-items-center">
                            <figure className="m-0">
                                <img src={advisorLoggedOut} alt="Advisor Logged Out" />
                            </figure>
                            <div className="advisor-details">
                                <h3>Advisor has logged out</h3>
                                <p>Advisor has been Logged Out for the past {props?.data?.Since && props.data['Since']['time']} minutes</p>
                            </div>
                        </div>
                    </AccordionDetails>
                </Accordion>
            }
            {props.data.RealTimeStatusId === 6 &&
                <Accordion className="travelling" expanded={expanded} >
                    <AccordionSummary
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        <Typography>
                            <div className='left-content'>
                            <h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{parseInt(props.data.ParentId)?'(' + props.data.ParentId + ')':null}</strong></h5>
                                {/* {expanded ?

                                    <h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '10px' }}>{'(' + props.data.EmployeeId + ')'}</span> &nbsp; <span>{props.data.CustomerName} &nbsp;<strong style={{ fontWeight: '10px' }} onClick={() => { openSalesview(props.data.ParentId) }}>{'(' + props.data.ParentId + ')'}</strong></span></h3> :
                                    <><h5>{props.data.CustomerName}&nbsp; <strong onClick={() => { openSalesview(props.data.ParentId) }}>{'(' + props.data.ParentId + ')'}</strong></h5><h3 style={{ fontSize: '12px' }}>{props.data.UserName} <span style={{ fontSize: '9px' }}>{'(' + props.data.EmployeeId + ')'}</span></h3></>

                                } */}
                            </div>
                            {appointmentCard.length>0 && <AppointmentList totalAppointments={appointmentCard.length}/>}
                            <div className='ryt-content'>

                                <button onClick={() => onhandleAccordianToggle()} className='add-remove-icon'>
                                    {!expanded ? <AddIcon /> : <RemoveIcon />}
                                    <span>{!expanded ? 'Expand' : 'Minimize'}</span>
                                </button>
                                <button className='live-icon'>Live</button>
                            </div>
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails className="p-0">
                        {appointmentConfirmation &&

                            <>
                                <div className="progress-bar-container pt-3">
                                    <div className="progress-bar">

                                        <div className={`progress-step ${appointmentStatusId.StartJourney.includes(appointmentConfirmation?.SubStatusId) ? 'completed' : ''}`}>
                                            {appointmentTime.startJourney && <span className="time">{appointmentTime.startJourney}</span>}
                                            <div className="step-label underline" onClick={() => props.changeCenterBaseOn(2193)}>Journey Start<img src='/Images/start-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip start' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.endJourney && <span className="time">{appointmentTime.endJourney}</span>} */}
                                            {appointmentTime.endJourney && <span className="time">{appointmentTime.endJourney}</span>}
                                            <div className="step-label">Journey End <img src='/Images/stop-travelling.png' style={{ width: '15px', height: '15px' }} alt='trip end' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.meetingStart && <span className="time">{appointmentTime.meetingStarts}</span>} */}
                                            <div className="step-label">Appt Start <img src='/Images/meeting-appointment.png' style={{ width: '15px', height: '15px' }} alt='Meet Start' /></div>
                                            <div className='progress-line'></div>
                                        </div>
                                        <div className={'progress-step'}>
                                            {/* {appointmentTime.meetingEnd && <span className="time">{appointmentTime.meetingEnd}</span>} */}
                                            <div className="step-label">Appt End</div>
                                            {/* <div className="step-label">App End <img src='/Images/start-appointment.png' style={{width:'15px', height:'15px'}} alt='Meet End' /></div> */}
                                            <div className='progress-line '></div>
                                        </div>
                                    </div>
                                </div>
                                <div className="appointment-details">
                                    <ul className="d-flex">
                                        <li>
                                            <label>Last Location Captured On</label>

                                            <span style={{ color: '#8B0000' }}>

                                                {props.data?.insertedAt ? moment.utc(props.data?.insertedAt).format('DD/MM/YY HH:mm') : '-'}
                                            </span>
                                        </li>


                                        <li>
                                            <label>Action on appointment</label>
                                            {/* {console.log(appointmentConfirmation?.CreatedOn)} */}
                                            {moment(appointmentConfirmation?.CreatedOn).format('DD/MM/YY HH:mm')}

                                        </li>


                                        <li>
                                            <label>Travelled distance</label>
                                            <span>{props?.data?.overallDistance ? `${Math.round((parseFloat(props.data.overallDistance) + Number.EPSILON) * 100) / 100} KMs` : '-'}</span>
                                        </li>

                                        <li>
                                            <label>Away from CX</label>
                                            <span>{props?.data?.distanceFromCustomer != null ? `${fetchDistance(props?.data?.distanceFromCustomer)}` : '-'}</span>

                                        </li>

                                        <li>
                                            <label>Trip started since</label>
                                            <span>{props?.data?.Since && props.data['Since']['time'] + ' ' + props.data['Since']['den']}</span>
                                            {/* 2193 - currenttime */}
                                        </li>
                                    </ul>
                                </div>
                            </>
                        }
                    </AccordionDetails>
                </Accordion>
            }
            {/* </> 
             :
            <Loader/>
        }  */}

        </div>
        </AppointmentsContext.Provider>
    )

}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}
export default connect(
    mapStateToProps,
    {

        GetCommonspDataV2,

    }
)(FosAppointmentJourney);