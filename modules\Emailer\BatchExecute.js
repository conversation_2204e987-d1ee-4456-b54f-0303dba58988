const Utility = require("../../Libs/Utility");
const conf = require('../../env_config');
const { POST_JagEmailer } = require('./joi');
const { ObjectID } = require('mongodb');

function getHeaders() {
  return {
    "REQUESTINGSYSTEM": "Matrix",
    "TOKEN": conf.PB_SERVICE_TOKEN
  };
}

async function JagEmailerBatch(req, res) {

  const { error } = POST_JagEmailer.validate(req.body);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {
    let promises = [];
    let succeeded= [];
    let failed = [];
    let db = matrixdashboarddb;
    const { TriggerName, Collection } = req.body || {};
    let agents = await db.collection(Collection)
      .find({ IsActive: true})
      .toArray();

    for (let i = 0; i < agents.length; i++) {

      let agentDetails = agents[i];
      let id = agentDetails._id;
      const { Email } = agents[i];

      delete agentDetails.Email;

      const body = {
        TriggerName: TriggerName,
        LeadId: 0,
        CommunicationType: 1,
        ProductId: 2,
        To: [
          Email
        ],
        InputData: agentDetails
      };

      const url = conf.BMS_SEND_COMMUNICATION_URL;
      const headers = getHeaders();

      //let response = await Utility.API_POST(url, body, headers);
      promises.push(Utility.API_POST(url, body, headers));

    }

    let totalPromises = promises.length || 0;
    let  index = 0;
    let batchSize = 20;
    let currentBatch = 0;
    while(index < totalPromises) {
      currentBatch += batchSize
      let curr = promises.slice(index, currentBatch);
      let responses= await Promise.allSettled(curr);
      for (let i = 0; i < responses.length; i++) {
        const element = responses[i];
        if(element.status === 'fulfilled' && element?.value?.IsSuccess) {
            succeeded.push(element?.value?.UUID || "NA");
        } else {
          failed.push(element?.value?.UUID || "NA");
        }
      }
      index+= batchSize;
    }

    return res.status(200).json({
      status: 200,
      message: 'success',
      failed,
      succeeded

    });
    
  }
  catch (e) {
    return res.status(400).json({
      status: 400,
      data: e.toString()
    })
  }
  finally {
    // return;
  }
}

module.exports = {
  JagEmailerBatch: JagEmailerBatch
};



