import { useEffect, useState } from "react";
import React from "react";
import { But<PERSON> } from 'react-bootstrap';

import {
    GetCommonData, GetCommonspData, GetCommonspDataV2,
} from "../../store/actions/CommonAction";

import { connect } from "react-redux";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DataTable from '../Common/DataTableWithFilter';
import UsersGroupPopUp from '../Agents/UsersGroupPopUp';


import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import 'react-bootstrap-typeahead/css/Typeahead.css';
import ShimmerLoader from "views/Common/ShimmerLoader";
import DropDownList from '../Common/DropDownList';

const UsersGroup = (props) => {
    const PageTitle = "All Groups"
    const [ModalOpen, setModalOpen] = useState(false);
    const [UsersGroup, setUsersGroup] = useState([]);
    const [IsLoading, setIsLoading] = useState(false);
    const [ProductId, setProductId] = React.useState();

    const Cell = ({ v }) => (
        <span title={v}>{(v) ? v.substring(0, 60)
            .toLowerCase()
            .split(' ')
            .map(word => {
                return word.charAt(0).toUpperCase() + word.slice(1);
            })
            .join(' ') : ''}{(v && v.length > 60) ? '...' : ''}</span>
    );
    
    const columnlist = [
        {
            name: 'Sno',
            label: 'S.No.',
            cell: (row, index) => index + 1,
            width: "60px!important",
            type: "number"
        },
        {
            name: 'GroupName',
            selector: 'UserGroupName',
            cell: row => row.UserGroupName ? <Cell v={row.UserGroupName} /> : "N.A",
            type: "string",
            editable: false,
            width: "150px!important",
            searchable: true,
            sortable: true
        },
        {
            name: 'Products',
            selector: 'Products',
            cell: row => row.Products ? <Cell v={row.Products} /> : "N.A",
            type: "string",
            editable: false,
            width: "300px!important"
        },
        {
            name: 'Sub Product Type',
            selector: 'SubProductType',
            cell: row => row.SubProductType ? <Cell v={row.SubProductType} /> : "N.A",
            type: "string",
            editable: false,
            width: "220px!important"
        },
        {
            name: 'Dialer',
            selector: 'IsAsterick',
            type: "string",
            editable: false,
            width: "100px!important"
        },
        {
            name: 'Group Process',
            selector: 'GroupProcess',
            cell: row => row.GroupProcess ? <Cell v={row.GroupProcess} /> : "N.A",
            type: "string",
            editable: false,
            width: "100px!important"
        }
    ];

    const ProductList = {
        config:
        {
            root: "Products",
            cols: ["ID AS Id", "ProductName AS Display", "ID AS value", "ProductName AS label"],
            con: [{ "Isactive": 1 }],
            order: 1,
            direction: 'asc'
        }
    };

    useEffect(() => {   
        if(ProductId){   
        GetGroupList();
        }
    }, [ProductId])

    const GetGroupList = () => {
        setIsLoading(true);
        props.GetCommonspDataV2({
            root: 'GetAllUserGroups',
            c: "R",
            params: [{ ProductId: ProductId === '7' ? '1000' : ProductId }]
          }, (groupData) => {
            if (groupData && Array.isArray(groupData?.data?.data) && groupData.data.data.length > 0 ) {
              setUsersGroup(groupData.data.data[0]);
              setIsLoading(false);
            }else {
                setIsLoading(false);
            }
          }
          )       
    }

    const handleCreateGroup = (event, LeadId) => {
        setModalOpen(true)
    };

    const ProductChange = (e) => {
        setProductId(e.target.value);
    }

    return (
        <>
            <ToastContainer />
            <div className="content UsersGroupPanel">

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={2}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                            <Row className="d-flex  justify-content-between">
                                <Col xs="auto">
                                    <p style={{ color: "black", marginBottom: "5px" }}>Product</p>
                                    <DropDownList firstoption="Select Product" col={ProductList} onChange={ProductChange} value={ProductId}/>
                                </Col>

                                <Col xs="auto">
                                    <Button variant="primary" onClick={handleCreateGroup}>Create Group</Button>
                                </Col>
                            </Row>
                                {ModalOpen && <UsersGroupPopUp
                                    handleClose={(ProductId) => {
                                        setModalOpen(false);
                                        setProductId(ProductId);
                                    }}
                                />
                                }

                                {(UsersGroup && UsersGroup.length > 0) && <DataTable
                                    columns={columnlist}
                                    data = {UsersGroup}
                                    printexcel={true}
                                    paginationPerPage={50}
                                />}
                                {IsLoading &&
                                    <>
                                        <ShimmerLoader />
                                        <ShimmerLoader />
                                    </>
                                }

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2,
    }
)(UsersGroup);