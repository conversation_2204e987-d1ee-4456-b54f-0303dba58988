
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
import {MultiSelect} from "react-multi-select-component";
import moment from 'moment';
import _, { bind } from 'underscore';


// reactstrap components

import {
    GetMoodleCourse
} from "../../store/actions/CommonAction";

import { Row, Col } from 'react-bootstrap';

import DropDown from '../Common/DropDown';

class CourseList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            CourseData: [],
            selecteddata: "",
            selectedValue: [{ label: "Select Course", value: "0" }],

        }
        //this.coursechange = this.coursechange.bind(this);
        this.onSelect = this.onSelect.bind(this);


    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {
        
        if ((nextProps.ProductId && nextProps.ProductId != this.props.ProductId) || (nextProps.StartDate != this.props.StartDate) || (nextProps.EndDate != this.props.EndDate)) {
            this.fetchCourseData(nextProps.ProductId, nextProps.StartDate, nextProps.EndDate);
            this.setState({ selectedValue: [{ label: "Select Course", value: "0" }] });
        }

    }
    fetchCourseData(ProductId, StartDate, EndDate) {
        GetMoodleCourse(ProductId, function (results) {
            var data = results.data.data;
            let AllDates = this.enumerateDaysBetweenDates(StartDate, EndDate);
            const resultdata = data.filter(val => _.contains( AllDates, val.date));
            this.setState({ CourseData: resultdata });
        }.bind(this));
    }

    enumerateDaysBetweenDates(startDate, endDate) {

        var dates = [];
        dates.push(startDate);

        var currDate = moment(startDate).startOf('day');
        var lastDate = moment(endDate).startOf('day');
        while (currDate.add(1, 'days').diff(lastDate) < 0) {
            dates.push(currDate.format('YYYY-MM-DD'));
        }

        dates.push(endDate);

        return dates;
    }

    
    onSelect(selectedList, selectedItem) {

        console.log(selectedList);
        const newselectedList = selectedList.filter(task => task.label !== 'Select Course');
        this.setState({ selectedValue: newselectedList });
        this.props.onSelectCourse(newselectedList);
        console.log(this.props);
    }

    // coursechange(e, props) {
        
    //     this.setState({ selecteddata: e.target.value });
    //     var SelectedCourseId = e.target.value;
    //     this.props.onSelectCourse(SelectedCourseId);

    // }


    render() {
        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <div>

                <Form.Group controlId="course_dropdown">
                    {/* <DropDown
                        firstoption="Select Course"
                        disabled={this.props.disabled}
                        value={this.props.disabled ? "" : this.state.selecteddata}
                        items={this.state.CourseData}
                        onChange={this.coursechange}>
                    </DropDown> */}

                    <MultiSelect
                        options={this.state.CourseData} // Options to display in the dropdown
                        value={this.props.disabled ? [] : this.state.selectedValue} // Preselected value to persist in dropdown
                        onChange={this.onSelect} // Function will trigger on select event                                                
                        // labelledBy={"Select Course"}
                        disabled={this.props.disabled}
                        selectAllLabel={"Select ALL Course List"}
                        className={this.props.disabled ? "courselist disable" : "courselist"}
                    />
                </Form.Group>
            </div>

        );
    }
}


export default CourseList;

