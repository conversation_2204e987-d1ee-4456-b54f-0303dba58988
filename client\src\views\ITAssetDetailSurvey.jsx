import React from "react";
import { GetCommonData, InsertData, UpdateData, GetCommonspData } from "../store/actions/CommonAction";
import { connect } from "react-redux";
import RadioButton from './Common/RadioOptions';
import { getUrlParameter } from '../utility/utility.jsx';
import { If, Then, Else } from 'react-if';
import moment from 'moment';
import UserDetails from './ITAssetSurvey/UserDetails';
import TabletDetails from './ITAssetSurvey/TabletDetails';
import LaptopDetails from './ITAssetSurvey/LaptopDetails';
import Success from './ITAssetSurvey/Success';

// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";
import { createThis } from "typescript";

class ITAssetDetailSurvey extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            uid: '',
            errors: {},
            fields: {},
            hover: false,
            hoverImg: '/IT-Asset-Details-Submission.png',
            step: 1,
            stepOnePart: 1,
            email: '',
            empname: '',
            question1: '',
            serialno: '',
            question2: '',
            sid: '',
            noasset: '',
        };
        this.handleValidation = this.handleValidation.bind(this);

    }


    componentDidMount() {
        let uid = getUrlParameter("u");
        let sid = getUrlParameter("sid");
        if (uid) {
            this.props.GetCommonspData({
                root: 'CheckSurveyAgent',
                c: "L",
                params: [{ "UserId": uid , "SurveyId" : sid}],
            }, function (result) {
                if (result.data && result.data.data[0].length > 0 ) {console.log(result.data.data[0][0].Eligible,result.data.data[0][0].IsComplete)
                    if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == false) {
                    this.setState({ uid: uid, sid: sid, stepOnePart: 4 });
                } else if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == true){
                    this.setState({ stepOnePart: 3 });
                } else{
                    this.setState({ stepOnePart: 2 });
                }
            }
            }.bind(this));

        } else {
            this.setState({ stepOnePart: 2 });
        }

    }

    handleValidation = (part, values) => {
        debugger;

        let fields = this.state.fields;
        let errors = {};
        let formIsValid = true;
        if(!values.noasset){
       if (part == 'part1') { 
        if(!values.email){
           formIsValid = false;
           errors["email"] = "This field is required";
        }else if (
            !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
          ) {
            formIsValid = false;
            errors["email"] = 'Invalid email address';
          }

        if(!values.empname){
            formIsValid = false;
            errors["empname"] = "This field is required";
         }

        if(!values.question1){
        formIsValid = false;
        errors["question1"] = "This field is required";
        }
    }

    if(part == 'part2'){
            if(!values.serialno){
            formIsValid = false;
            errors["serialno"] = "This field is required";
            }
            if(!values.question2){
            formIsValid = false;
            errors["question2"] = "This field is required";
            }
    }
    } 
       this.setState({errors: errors});
       return formIsValid;
   }

    showdetails() {
        debugger;
        this.setState({ hover: true });
    }

    nextStep = (values) => {debugger;
        const { step } = this.state
        if(values.noasset){
            this.setState({step : step + 2})
        }else{
            this.setState({step : step + 1})
        }
    }

    prevStep = () => {
        const { step } = this.state
        this.setState({
            step: step - 1
        })
    }

    handleChange = input => event => {
        if (event.target && event.target.type == "checkbox") {
            this.setState({ [input] : event.target.checked })
        }else{
        this.setState({ [input] : event.target.value })
        }
    }

    SaveAssetDetails = (values) => {
            alert('Form submitted successfully');

           if(values.noasset){
            var json = { 'AnswerText': values.noasset, 
            'CreatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
            'SurveyId': this.state.sid,
            'UserId': this.state.uid,
            'QuestionId': 22,
            'Description': '',
            'AnswerId': 1,
            'IsActive':1,
        }
        this.props.InsertData({
            root: "SurveyResponse",
            body: json,
            c: "L",
          });
        }else{
        Object.entries(values).map(([key, value], index) => {
            index = index + 5;
            if (values['question1'].includes('Tablet') && index == 9) {
                index = index + 1;
            }
            console.log('key', key, 'value', value.toString(), index + 1)
            var json = {
                'AnswerText': value.toString(),
                'CreatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
                'SurveyId': this.state.sid,
                'UserId': this.state.uid,
                'QuestionId': index + 1,
                'Description': '',
                'AnswerId': 1,
                'IsActive': 1,
            }
            if(key != "noasset"){
            this.props.InsertData({
                root: "SurveyResponse",
                body: json,
                c: "L",
            });
        }

        })
        }
        // Update survey reponse completed
        let res = this.props.UpdateData({
            root: "SurveyAgentMapping",
            body: {
                'IsCompleted': 1,
                'UpdatedOn': moment().format("YYYY-MM-DD HH:mm:ss")
            },
            querydata: { "UserId": this.state.uid, "SurveyId": this.state.sid },
            c: "L",
        });
    }

    renderSwitch(step, values, errors, stepOnePart) {
        debugger;
        switch (step) {
            case 1:
                if (stepOnePart == 1)
                    return <i class="fa fa-spinner fa-spin"></i>

                if (stepOnePart == 2)
                    return <p class="survey-response info">Invalid action</p>

                if (stepOnePart == 3)
                    return <p class="survey-response info">Your survey response is already recorded</p>

                if (stepOnePart == 4)
                    return <UserDetails
                        nextStep={this.nextStep}
                        handleChange={this.handleChange}
                        onValidation={this.handleValidation.bind(this)}
                        SaveAssetDetails = {this.SaveAssetDetails.bind(this)}
                        values={values}
                        errors={errors}
                    />
            case 2:
                if (values.question1.includes('Tablet'))
                    return <TabletDetails
                        nextStep={this.nextStep}
                        prevStep={this.prevStep}
                        handleChange={this.handleChange}
                        onValidation={this.handleValidation.bind(this)}
                        showdetails={this.showdetails.bind(this)}
                        SaveAssetDetails={this.SaveAssetDetails.bind(this)}
                        values={values}
                        errors={errors}
                    />
                if (values.question1.includes('Laptop'))
                    return <LaptopDetails
                        nextStep={this.nextStep}
                        prevStep={this.prevStep}
                        handleChange={this.handleChange}
                        onValidation={this.handleValidation.bind(this)}
                        showdetails={this.showdetails.bind(this)}
                        SaveAssetDetails={this.SaveAssetDetails.bind(this)}
                        values={values}
                        errors={errors}
                    />

            case 3:
                return <Success />
        }
    }


    render() {
        const { Error, UserInfo } = this.state;
        const { step } = this.state;
    	const { email, empname, question1, serialno, question2, noasset, modelno, errors, uid, stepOnePart } = this.state;
        const values = { email, empname, question1, serialno, question2, noasset };
           return (
            <div className="content">
                <Row>
                    <Col>
                        <Card className="surveyform col-md-6 col-xs-12">
                            <CardHeader>
                                <Col md={6} className="col-md-12 text-center">
                                    <h3>Information || Company Assets</h3>
                                </Col>
                            </CardHeader>
                            <CardBody>
                                {this.renderSwitch(step, values, errors, stepOnePart)}
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
                <Modal show={this.state.hover} onHide={() => this.setState({ hover: false })} dialogClassName="modal-50w">
                    <Modal.Header closeButton>
                        <Modal.Title></Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <div className="modalmoreinfodata">
                            <img src={this.state.hoverImg} />
                        </div>
                    </Modal.Body>
                    <Modal.Footer>

                    </Modal.Footer>
                </Modal>
            </div>
        )

    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        InsertData,
        UpdateData,
        GetCommonspData
    }
)(ITAssetDetailSurvey);