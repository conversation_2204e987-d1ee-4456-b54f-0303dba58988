import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
//import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import './customStyling.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func } from "prop-types";
import moment from "moment";
import DropDownListMysql from './Common/DropDownListMysql';
import { If, Then, Else } from 'react-if';


class UserPODLeads extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "My POD Leads",
      UserPODLeads: [],
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      podType: 0,
      IsLoading: true,

    };
    this.dtRef = React.createRef();
    this.AgentType = null;
    this.PodTypeList = {
      config:
      {
          root: "Podtypelist",
          data: [ { Id: 1, Display: "Booked leads" }],
      }
    };

    const Cell = ({v}) => (
      <span title={v}>{(v)?v.substring(0,25):''}{(v && v.length>25)?'...':''}</span>
    );
    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadID", 
        searchable: true,       
      },
      {
        name: "Customer Name",
        selector: "Name",        
      },
      /*{
        name: "ReassignedAgent",
        sortable: false,
        //width: "400px",
        //cell: row => (row.Type == 1) ? <Cell v={row.ReAssignedTo} /> : <Cell v={row.ReAssignedBy} />
        cell: row => row.ReAssignedTo + ' / ' + row.ReAssignedBy
      },*/
      {
        name: "Assigned To",
        sortable: false,
        width: "250px",
        cell: row => row.ReAssignedTo.includes(getuser().EmployeeId) ? 'You' : row.ReAssignedTo
      },
      {
        name: "Assigned By",
        sortable: false,
        width: "250px",
        cell: row => row.ReAssignedBy.includes(getuser().EmployeeId) ? 'You' : row.ReAssignedBy
      },
      {
        name: "Status",
        selector: "StatusName",
        sortable: false,  
      },
      {
        name: "Transfer Date",
        selector: "ReAssignedon",
        sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.ReAssignedon}</Moment>
      },
      {
        name: "CallBack Date",
        selector: "EventDate",
        sortable: true,
        cell: row => (!row.EventDate || row.EventDate == '1900-01-01T00:00:00.000Z') ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.EventDate}</Moment>
      },
      {
        name: "Last Called Date",
        selector: "LastCalled",
        sortable: true,
        cell: row => (!row.LastCalled || row.LastCalled == '1900-01-01T00:00:00.000Z') ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.LastCalled}</Moment>
      }

    ];

    this.columnlistBookedLeads = [
      {
        name: "Booked Lead ID",
        selector: "BookedLeadId",
        searchable: true,        
      },
      {
        name: "Parent Lead ID",
        selector: "ParentID",        
      },
      {
        name: "Assigned To",
        sortable: false,
        width: "250px",
        cell: row => row.TransferTo.includes(getuser().EmployeeId) ? 'You' : row.TransferTo
      },
      {
        name: "Assigned By",
        sortable: false,
        width: "250px",
        cell: row => row.TransferBy.includes(getuser().EmployeeId) ? 'You' : row.TransferBy
      },
      {
        name: "Transferred Date",
        selector: "TransferredDate",        
        sortable: true,
        cell: row => (!row.TransferredDate || row.TransferredDate == '1900-01-01T00:00:00.000Z') ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.TransferredDate}</Moment>
      },
      {
        name: "Booking Date",
        selector: "BookingDate",
        sortable: true,
        cell: row => (!row.BookingDate || row.BookingDate == '1900-01-01T00:00:00.000Z') ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.BookingDate}</Moment>
      },
      {
        name: "Status",
        selector: "StatusName",
        sortable: false,
      }

    ];



  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetUserPODLeads"] || nextProps.CommonData["GetAgentPODBookings"] ) {

        let CBList = (this.state.podType == 0)?nextProps.CommonData["GetUserPODLeads"]: nextProps.CommonData["GetAgentPODBookings"];

         //for (let index = 0; index < CBList[0].length; index++) {
           const element = CBList[0][0];

        //   var compareTo = element.EventDate;
        //   //var compareTo = "2021-05-07T15:00:00.000Z";
        //   var date = new Date().toISOString();
        //   var now = moment.utc(date);
        //   now.add(5, 'hour')
        //   now.add(30, 'minute')
        //   //var then = moment.utc(compareTo);


        //   var isAfter = moment(now).isAfter(compareTo);

        //   if (isAfter) {
        //     element['IsBooked'] = 0;
        //   }
        //   else {
        //     element['IsBooked'] = 1;
        //   }
        if(element){
          this.AgentType = (this.state.podType == 0)?element.Type: element.AgentType;
        }
        //}
        this.setState({ UserPODLeads: CBList, IsLoading: false });
        //this.setState({ ReportTime: new Date() });
      }



    }
  }

  componentDidMount() {
    this.fetchCallBackData(0);
  }

  fetchCallBackData(IsPODBooking) {debugger;
    var userid = getuser().UserID;
    console.log('userrrrrrrrrrrid', getuser(),userid);
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: (IsPODBooking == 0)?"GetUserPODLeads" : "GetAgentPODBookings",
      params: [{ UserId:  userid}]
    });

    /*
    if (this.state.SelectedRows.length > 0) {
      this.dtRef.current.handleClearRows();
    }*/

  }


/*  onSelectedAgent(e) {
    this.setState({ SelectedAgentAssigTo: e.target.value });
  }

  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }
*/
podtypechange(e, props){
  this.setState({podType: e.target.value, IsLoading: true, UserPODLeads: []})
  this.fetchCallBackData(e.target.value);

}
  render() {
    const columns = (this.state.podType == 0)?this.columnlist : this.columnlistBookedLeads; 
    const { items, PageTitle, UserPODLeads, showAlert, AlertMsg, AlertVarient, SelectedRows } = this.state;

    /*let columnNames = [];
    columns.forEach((element,index) => {
      if(index == 2) {
        element.name = parseInt(this.AgentType) === 1 ? "Assigned To" : "Assigned By";
        element.selector = parseInt(this.AgentType) === 1 ? "TransferTo" : "TransferBy";
      }
      columnNames.push(element);
    });*/

    return (
      <>
        <div className="content UserPODLeadsContainer">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={9}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                    <Form.Label><i>*</i> POD Type </Form.Label>
                    <Form.Group controlId="podType_dropdown" >
                        <DropDownListMysql firstoption="Transfer leads" value={this.state.podType} col={this.PodTypeList} onChange={this.podtypechange.bind(this)}>
                        </DropDownListMysql>
                    </Form.Group>
                  </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                <If condition={this.state.IsLoading}>
                <Then>
                  <i class="fa fa-spinner fa-spin"></i>
                </Then>
                <Else>
                <DataTable
                    columns={columns}
                    data={(UserPODLeads && UserPODLeads.length > 0) ? UserPODLeads[0] : []}
                    defaultSortField="ReAssignedon"
                    defaultSortAsc={false}
                    export={false}
                    //selectableRows={true}
                    //ref={this.dtRef}
                    //onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </Else>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(UserPODLeads);