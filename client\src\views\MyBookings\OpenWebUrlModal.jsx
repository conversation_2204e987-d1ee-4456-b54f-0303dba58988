import React, { useEffect, useState } from 'react';
import { Modal } from 'react-bootstrap';

const OpenWebUrlModal = (props) => {
  const { webUrl, WebUrlTitle } = props;
  const [IsLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if(webUrl) {
      setIsLoading(true);
      setTimeout(() => {
        setIsLoading(false);
      }, 5000)
    }
  }, [webUrl])

  return (
    <>
      <Modal
        {...props}
        fullscreen={true}
        onHide={props.onWebUrlModalCancel}
      >
        <Modal.Header closeButton {...(props.sme && { className: "iframe-modal-header" })}>
        <Modal.Title>{WebUrlTitle}</Modal.Title> &nbsp;
          {IsLoading && <span className={'spinner-border spinner-border-sm'}></span>}
        </Modal.Header>
        <Modal.Body>
          {webUrl && <iframe title={WebUrlTitle} src={webUrl} height="100%" width="100%" frameborder={0}></iframe>}
        </Modal.Body>
      </Modal>
    </>
  );
}

export default OpenWebUrlModal;