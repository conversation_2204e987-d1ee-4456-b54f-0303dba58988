import React,{useContext} from "react";
import { AppointmentsContext } from "../AppointmentsContext";
import {
    Box,
    Typography,
    Paper,
    Divider,
    Popper,
    ClickAwayListener,
  } from "@mui/material";
import { Grid} from "@mui/material";


const AppointmentCard=()=>{

    const AppointmentContext = useContext(AppointmentsContext);


    const DisplayCurrentStatus=(currentStatus)=>{

        switch (currentStatus){
            case 1:
                return <h5 className="Travelling">Advisor is Travelling</h5>
            case 2:
                return <h5 className="Meeting">Appointment in Progress (in meeting)</h5>
            case 3:
                return null
            case 4:
                return null
            case 5:
                return null
            case 6:
                return <h5 className="WaitingCustomer">Waiting at customer's place</h5>
            default:
                return null
        }

    }

    const DisplaySubStatus=(subStatus)=>{
      switch(subStatus){
        case 2193:
          return 'Journey Started'
        case 2194:
          return 'Journey Completed'
        case 2003:
          return 'Appointment Completed'
        case 2124:
          return 'Appointment Started'
        case 2005:
          return 'Appointment Rescheduled'
        case 2004:
          return 'Appointment Cancelled'
        case 2002:
          return 'Appointment Booked'
        
      }
    }

    return(
    <>
    <div
        className="Arrow"       
      />
      
      <Paper className="AppoitmentCardTooltip">
        {/* <div className="Header">         
          <Typography
            variant="subtitle1"
          >
            Keysang Yonthan
          </Typography>
          <Typography
            variant="body2"
          >
            Base location: Gurgaon | <strong>PW37892</strong>
          </Typography>
        </div> */}
        
        {
        AppointmentContext.currentStatus &&

        <>
        {
        DisplayCurrentStatus(AppointmentContext.currentStatus)
        }
        </>
        }
        
        
        {/* Details */}
        <div className="AppointmentDetails">
        {
        AppointmentContext.appointmentCard.map((appointment)=>{
        if(!appointment) return null;

        return(
        <>
          <Grid container spacing={1}>
            {/* {console.log("The appointment is ",appointment)} */}
            {
            appointment.LeadId &&
            <Grid item xs={12} sm={6} md={5}>
              <Typography variant="body2">Lead ID</Typography>
              <Typography variant="h6">
                {appointment.LeadId}
              </Typography>
            </Grid>
            }
            {
            appointment.CustomerName &&
            <Grid item xs={12} sm={6} md={7}>
            
              <Typography variant="body2">Customer Name</Typography>
              <Typography variant="h6">{appointment.CustomerName}</Typography>
            </Grid>
            }
            {
            appointment.Status &&
            <Grid item xs={12} sm={12} md={12}>
              {/* <Typography variant="body2">Date & Time</Typography>
              <Typography variant="h6">
                {appointment.DateTime}
              </Typography> */}
              <Typography variant="body2">Current Status</Typography>
              <Typography variant="h6">
                {DisplaySubStatus(appointment.Status)}
              </Typography>
            </Grid>
            }
          </Grid>
          <Divider />
          </>
            )
        })   
        
        }
        </div>
      </Paper>
      </>
    )
}

export default AppointmentCard;