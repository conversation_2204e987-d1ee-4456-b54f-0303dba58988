const sqlHelper = require("../../LibsPBIncentive/sqlHelper");
const conf = require('../../env_config');

const CheckContestAgent = async (req, res) => {
  try {
    const { EmployeeId, UserId, ApplicationId } = req.body || {};

    let sqlParams = [], contests = [];
    sqlParams.push({ key: 'ApplicationId', value: parseInt(ApplicationId) });
    sqlParams.push({ key: 'EmployeeId', value: EmployeeId });
    sqlParams.push({ key: 'UserId', value: UserId });

    let response = await sqlHelper.sqlProcedure("R", "[ENC].[CheckContestAgent]", sqlParams);
    let data = response?.recordset || [];

    for (let i = 0; i < data.length; i++) {
      let element = data[i];
      if(element && element.ContestId) {
        element = { ...element, ContestUrl: `${conf.MATRIX_DASHBOARD_BASEURL}/gaming/Spinwheel?cid=${element.ContestId}` }
      }
      contests.push(element);
    }
   
    return res.status(200).json({
      status: 200,
      message: 'success',
      data: contests
    })
  } catch (err) {
    console.log('Inside CheckContestAgent:: ', err);
    return res.status(500).json({
      status: 500,
      message: err.toString()
    })
    
  }
}

module.exports = {
  CheckContestAgent: CheckContestAgent
}