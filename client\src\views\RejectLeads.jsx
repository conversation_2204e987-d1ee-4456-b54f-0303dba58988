import React from "react";
import {
    PostLeadRejectionData
} from "../store/actions/CommonAction";
import {
    GetMySqlData
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, joinObject } from '../utility/utility.jsx';
import DropDown from './Common/DropDownList';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import DataTable from './Common/DataTableWithFilter';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class RejectLeads extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            addClass: '',
            UserId: '',
            selectedFile: null,
            ManagerId: '',
            DownloadedFile: '/SampleExcelfiles/LeadRejectionData.xlsx',
            ResponseData:'',
            UploadStatusColumns: [],
            prodId: '',
        };

     	/*this.ProductList = {
            config:
            {
              root: "Products",
              cols: ["ID AS Id", "ProductName AS Display"],
              con: [{ "Isactive": 1 }],
            }
  		 };*/
    }

    componentDidMount() {
        this.UserList();
    }

    fnDatatableCol(columnlist) {

        var columns = fnDatatableCol(columnlist);
        return columns;
    }

    UserList() {debugger;
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ManagerId : managerid});
    }

    // On file select (from the pop up) 
    onFileChange(event) { debugger;
        console.log(event.target.files[0]);
        // Update the state 
        this.setState({ selectedFile: event.target.files[0] }); 
       
    }; 

    // On file upload (click the upload button) 
    onFileUpload(e)
    {   
        e.preventDefault();
         debugger;

        /*if (this.state.prodId == '') {
        toast("Please choose Product", { type: 'error' });
        return;
        }*/

        if (this.state.selectedFile == null) {
        toast("Please choose Excel File", { type: 'error' });
        return;
        }
        // Create an object of formData 
        const formData = new FormData(); 
        console.log(formData);
        // Update the formData object 
        formData.append( 
          "myFile", 
          this.state.selectedFile, 
          this.state.selectedFile.name
        ); 
        formData.append('UserId', this.state.ManagerId);
        //formData.append('ProductId', this.state.prodId);  
               
        // Details of the uploaded file 
        console.log(this.state.selectedFile);      
        document.getElementById('uploadbutton').innerHTML = 'Upload! <i class="fa fa-spinner fa-spin"></i>';
        // Request made to the backend api 
        // Send formData object 
        try {
            PostLeadRejectionData(formData, function (results) {
                console.log(results);
                document.getElementById('uploadbutton').innerHTML = 'Upload!';
                this.setState({ ResponseData: results.data.data });
                alert('File uploaded');
            //     if(results.data.status == 200){
            //     toast(results.data.data, { type: 'success' });
            //     }else{
            //    // toast(results.data.data, { type: 'error' });    
            //     }
            }.bind(this));
        } catch(e) {
            console.log(e);
        }
    }

    fileData() 
    {      debugger;
        if (this.state.selectedFile) { 
            
          return ( 
            <div> 
              {/* <span>File Details:</span>  */}
              <span>File Name: {this.state.selectedFile.name}</span> 
              <p>File Type: {this.state.selectedFile.type}</p> 
              {/* <p> 
                Last Modified:{" "} 
                {this.state.selectedFile.lastModifiedDate.toDateString()} 
              </p>  */}
            </div> 
          ); 
        } else { 
          return ( 
            <div> 
              <br /> 
              <h4>Choose before Pressing the Upload button</h4> 
            </div> 
          ); 
        } 
    } 

    renderDownloadFile(){
        return <Link to={this.state.DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
    }

    renderUploadStatus() {debugger;

        if (this.state.ResponseData && this.state.ResponseData.length > 0) {
            let UploadStatusColumns = [];
            let ResponseData = this.state.ResponseData;
      
            Object.entries(ResponseData[0]).map(([key,value])=>{
                    if(key == 'Status'){
                        UploadStatusColumns.push({ label: "Status",
                            name: "Status",
                            cell: row => <div className="Status">{row.Status ? row.Status : "N.A"}</div>,
                        })
                    }else{
                        UploadStatusColumns.push({
                            "name": key.toString(),
                            "label": key.toString(),
                            searchable: true,                    
                        })
                    }
              })

            const columns = this.fnDatatableCol(UploadStatusColumns);

            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={12}>
                                    <CardTitle tag="h6">Upload Data Status</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={columns} data={this.state.ResponseData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    /*productchange(e, props) {
        this.setState({ prodId: e.target.value });
    }*/
    render() {
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>                                    
                                    <Row>
                                        <Col md={6}>
                                            {/*
                                            <Form.Group controlId="product_dropdown">
                                                <DropDown firstoption="Select Product" valueTobefiltered={[2,115,7]} col={this.ProductList} onChange={this.productchange.bind(this)}>
                                                </DropDown>
                                            </Form.Group> */}
                                            <form ref="form" onSubmit={this.onFileUpload.bind(this)}>
                                                <input type="file" onChange={this.onFileChange.bind(this)} /> 
                                                <button type="submit" id="uploadbutton" className="btn btn-primary">Upload!</button>
                                            </form>
                                        </Col>
                                    </Row>
                                    {this.renderDownloadFile()}

                                </CardHeader>
                                <CardBody>
                                {this.fileData()}
                                {this.renderUploadStatus()}
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>
                 </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData,
    }
)(RejectLeads);