import React, { Component } from 'react';
import {GetCommonData,InsertData, GetCommonspData, UpdateData, GetAgentCallLogs } from "../store/actions/CommonAction";
import { ButtonGroup, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
  } from "reactstrap";
  import { getUrlParameter, getuser, getDifferenceInMinFromNow } from '../utility/utility.jsx';
  import { connect } from "react-redux";
  import moment from 'moment';
import { getCookie, setCookie } from 'utility/utility';
import { Redirect } from 'react-router';
import SmsAdvisorDetails from './Common/SmsInfo/SmsAdvisorDetails';

class KyaInfoVc extends Component {
  constructor(props) {
    super(props);
    this.state = {
        customerId : null,
        agentId : null,   
        advisorData: [],
        advisorDataRating: null,
        StarRating : 0,
        rating:'',
        currentRating: "0",
        kycinfo: null,
        step: null,
        smsId : null,
        IsLoading: true,
        leadId : null
    };
  }

  async componentDidMount() {
    let leadId = atob(getUrlParameter('leadId'));
    let Empcode = atob(getUrlParameter('Empcode'));
    
    await this.props.GetCommonspData({
        root: 'GetDetailsForKYA',
        c: "L",
        params: [{"LeadId": leadId , "EmployeeId" : Empcode }],
          }, function (result) {
          if (result.data.status == 200) {
              if(result.data && result.data.data && result.data.data[0][0]){
                if(result.data.data[0][0].CustomerID && result.data.data[0][0].UserID){
                console.log(result.data.data[0][0].CustomerID);
                this.setState({ step :1, customerId: result.data.data[0][0].CustomerID, 
                    agentId: result.data.data[0][0].UserID, leadId : leadId }, () =>
                    this.getadvisorData());
                }else{
                  this.setState({ step : 2})    
                }
              }else{
                  this.setState({ step : 2})
              }
              
            }else{
              this.setState({ step : 2})
            }
         }.bind(this));  
    
  }

  setKyaVCInfo(){
    this.props.GetCommonspData({
      root: 'InsertKYAVisitLog',
      c: "L",
      params: [{"LeadId": this.state.leadId , "CustomerId" : this.state.customerId, "AgentId" : this.state.agentId,
      "Source" : "VC"}],
        }, function (result) {
          console.log(result);
       }.bind(this));  
  }

  async getadvisorData(){

    this.setKyaVCInfo();
    await this.props.GetCommonData({
        limit: 10,
        skip: 0,
        root: 'AdvisorInfoStarRating',
        con: [{ "UserID": this.state.agentId , "CustomerId" : this.state.customerId , "IsActive" : 1}]

      }, function (results) {
        if (results.data.status == 200 && results.data.data && results.data.data[0]) {
            console.log('advisorstarrating',results.data.data[0]);
        this.setState({ advisorDataRating: results.data.data[0], StarRating : results.data.data[0][0].StarRating });
        }
    }.bind(this));
    // }.bind(this), 10000);

     await this.props.GetCommonData({
        limit: 10,
        skip: 0,
        root: 'AdvisorInfo',
        con: [{ "UserID": this.state.agentId, "IsActive" : 1 }]

      }, function (results) {
        if (results.data.status == 200 && results.data.data && results.data.data[0]) {
            console.log('advisorinfo',results.data.data[0] );
        this.setState({ advisorData: results.data.data[0], IsLoading: false });
        }
    }.bind(this));
    
  }

    renderSwitch(step) {
        switch (step) {
        case 1:
              
          return <SmsAdvisorDetails
          agentid= {this.state.agentId}
          advisorData= {this.state.advisorData}
          StarRatingValue={this.state.StarRating}
          customerId={this.state.customerId}
          kycinfo={this.state.kycinfo}
          IsLoading={this.state.IsLoading}
          type= {'VC'}
          />   
        case 2:
        return <p className="survey-response invalid text-center"><b>Invalid User</b></p>  

        default:
        return <></>  
        }
    }

  render() {
    const { step } = this.state;

    return (
      this.renderSwitch(step)
    );
  }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      UpdateData,
      InsertData
    }
  )(KyaInfoVc);