diff a/client/src/views/HealthRenewal/PaymentLinkCreation.jsx b/client/src/views/HealthRenewal/PaymentLinkCreation.jsx	(rejected hunks)
@@ -449,3 +449,11 @@
                                                 type="text"
-                                                value={data.PaymentReason ? data.PaymentReason : "N/A"}
+                                                value={data.PaymentReason ? (
+                                                    data.PaymentReason.length > 50 ? (
+                                                        <Tooltip title={data.PaymentReason} arrow>
+                                                                {data.PaymentReason.substring(0, 50)}
+                                                        </Tooltip>
+                                                    ) : (
+                                                        data.PaymentReason
+                                                    )
+                                                ) : "N/A"}
                                                 readOnly
