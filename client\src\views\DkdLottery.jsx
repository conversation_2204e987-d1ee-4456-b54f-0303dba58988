import { useEffect, useState } from "react";
import React from "react";


import {
    GetCommonspData, GetCommonData as GetCommonDataSQ, GetLotteryTickets
} from "../store/actions/CommonAction";

import {
    GetCommonData, addRecord, UpdateData, DeleteData
} from "../store/actions/CommonMongoAction";

import { connect } from "react-redux";

// reactstrap components

import { getuser } from '../utility/utility.jsx';
import 'react-toastify/dist/ReactToastify.css';

const DkdLottery = (props) => {

    const [items, setitems] = useState();

    // const [timeLeft, setTimeLeft] = useState(() => calculateTimeLeft);
    const [timeLeft, setTimeLeft] = useState("");


    useEffect(() => {
        AgentDetails();
        //LotteryDetails();
        // setTimeLeft(calculateTimeLeft());
    }, [])

    const AgentDetails = () => {
        const user = getuser();
        if (!props.CommonData.isError) {

            // 
            GetLotteryTickets(user.UserID, function (result) {
                if (result.status == 200) {
                    console.log(result.data)
                    debugger;
                    if (result.data && result.data.data) {
                        setitems(result.data.data);
                    }
                }

            })

            // props.GetCommonData({
            //     root: "ContestAgents",
            //     c: 'M',
            //     con: { Userid: user.UserID },
            //     cols: []
            // }, function (data) {
            //     debugger;
            //     if (data && data.data && data.data.data) {
            //         let item = data.data.data[0]
            //         if (item) {
            //             LotteryContestDetails(item.QuizID)
            //             setitems(item)
            //         }
            //     }
            // })
        }
    }

    // const LotteryContestDetails = (QuizID) => {

    //     props.GetCommonDataSQ({
    //         root: "QuizMaster",
    //         con: [{ "id": QuizID }, { "IsActive": 1 }],
    //     }, function (data) {
    //         debugger;
    //         if (data && data.data && data.data.data) {
    //             let item = data.data.data[0][0]
    //             if (item) {
    //                 setLotteryInfo(item);
    //             }
    //         }
    //     })
    // }


    useEffect(() => {
        setTimeLeft(calculateTimeLeft())
        const time = setInterval(function () {
            setTimeLeft(calculateTimeLeft());
        }, 30000)

        return ()=>clearInterval(time)
    }, [items]);


    // const LotteryDetails = () => {
    //     const user = getuser();
    //     if (!props.CommonData.isError) {
    //         if (user && user.UserID) {
    //             props.GetCommonspData({
    //                 root: "Dkd_LotteryTickets",
    //                 params: [{ UserId: user.UserID }],
    //             }, function (data) {
    //                 if (data && data.data && data.data.data) {
    //                     let item = data.data.data[0]
    //                     if (Array.isArray(item)) { setLottery(item) }
    //                 }
    //             })
    //         }
    //     }

    // }

    const displayLotteryTickets = (lottery) => {

        let res = []

        if (lottery.length > 0) {
            for (let index = 0; index < lottery.length; index++) {
                const element = lottery[index].TicketNumber
                const ele = element

                if (lottery[index].IsWinner == 1) {
                    res.push(
                        <li style={{ color: "green", fontWeight: "bold" }}>{ele}     </li>
                    )
                }

                else {
                    res.push(
                        <li>{ele}   </li>
                    )
                }
            }

        }
        return res
    }

    const calculateTimeLeft = () => {

        // let difference = +new Date(`06/17/2023 19:00:00`) - +new Date();
        if(items && items.length>0 && items[0]?.QuizMaster?.EventDate){
            let difference = +new Date(items[0].QuizMaster.EventDate) - +new Date() - 19800000;
            let timeLeft = "";
            // let timeLeft = {};
            // if (difference > 0) {
            //     timeLeft = {
            //         DAYS: Math.floor(difference / (1000 * 60 * 60 * 24)),
            //         HOURS: Math.floor((difference / (1000 * 60 * 60)) % 24),
            //         MINS: Math.floor((difference / 1000 / 60) % 60),
            //     };

            if (difference > 0) {
                timeLeft = Math.floor(difference / (1000 * 60 * 60 * 24)) + ' DAYS: ' +
                        Math.floor((difference / (1000 * 60 * 60)) % 24) + ' HOURS: ' +
                        Math.floor((difference / 1000 / 60) % 60) + ' MINUTES'
            }

            return timeLeft;
        }

    }

    const BindLotteryAgentData = (item) => {
        let result = [];
        // result.push(<tr><th>1</th><td>2</td></tr>)
        // result.push(<tr><th>3</th><td>4</td></tr>)


        //return result;

        for (const key in item) {
            
            const value = item[key];
            let ignorekeys = ['Userid', 'QuizID', '_id', 'QuizMaster', 'TicketNumbers', 'IsActive']
            if (ignorekeys.indexOf(key) > -1) {

            }
            else {
                //console.log(key)
                console.log(value)
                result.push(<tr key={Math.random()}><th>{key}</th><td>{value}</td></tr>);
                //result.push(<tr key={Math.random()}><th>{key}</th><td>{Math.random()}</td></tr>);
            }

        }
        debugger
        console.log(result);
        return result;
    }

    return (
        <>
            {items && items.length > 0 &&
                <div>

                    {items.map(item => (
                        <div className="dkd" key={item._id}>
                            <h3>{item.QuizMaster.QuizName}</h3>
                            <table>
                                <tbody>
                                   
                                    {items && items.length>0 && item?.QuizMaster && item?.QuizMaster?.EventDate && timeLeft && timeLeft.length>0 && <tr >
                                        <th colSpan={2} className="timer"> {item.QuizMaster.DESCRIPTION} <br />{timeLeft}</th>
                                    </tr>}
                                    
                                    {
                                        BindLotteryAgentData(item)
                                    }
                                    
                                    {/* {
                                        item.map(element => (
                                             <>{element}</>
                                        ))
                                    } */}

                                    {item && item.TicketNumbers && item.TicketNumbers.length > 0 &&
                                        <>
                                           
                                            <tr>
                                                <th>Ticket Number</th>
                                                <td><ul className="tickets">{displayLotteryTickets(item.TicketNumbers)}</ul></td>
                                            </tr>
                                        </>

                                    }

                                </tbody>
                            </table>

                        </div>

                    ))}
                </div>
            }
        </>

    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,

    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData,
        GetCommonDataSQ
    }
)(DkdLottery);