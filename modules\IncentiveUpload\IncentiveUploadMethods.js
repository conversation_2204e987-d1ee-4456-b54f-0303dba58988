
const sqlHelper = require("../../Libs/sqlHelper");


const UploadIncentiveData = async (req,awsFilePath) => {

    try {
        let sqlparam = [];
        let userId = req.user?.userId ? req.user.userId : null;  
        let body = req.body;
        sqlparam.push({ key: "ProductId", value: body.ProductId });
        sqlparam.push({ key: "CriteriaHtml", value: awsFilePath });
        sqlparam.push({ key: "CriteriaMonth", value: body.StartDate });        
        sqlparam.push({ key: "TabName", value: body.TabName });
        sqlparam.push({ key: "CreatedBy", value: userId });
        //sqlparam.push({ key: "IsRequired", value: body.IsRequired });
        sqlparam.push({ key: "IsActive", value: 1 });
        sqlparam.push({ key: "status", value: "", type: "out" });
        // console.log('params',sqlparam);
        let result = await sqlHelper.sqlProcedure("L", "[enc].[InsertSuperGroupCriteria]", sqlparam);
        // console.log(result);
        return result;

    }
    catch (e) {
        console.log("Error in UploadIncentiveData: ",e);
        return e;

    }
    finally {

    }


}


module.exports = {
  UploadIncentiveData,
}
