import React  from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from "react-bootstrap";
import {
  GetCommonData,
  addR<PERSON>ord,
  UpdateData,
  GetCommonDataResponse,
} from "../../store/actions/CommonMongoAction";
import AlertBox from "./../Common/AlertBox";
import { connect } from "react-redux";
import {
  fnRenderfrmControl,
  getuser,
  fnCleanData,

} from "../../utility/utility.jsx";
import DataTable from "react-data-table-component";
import {
  <PERSON>,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col,
} from "reactstrap";
import { toast } from "react-toastify";


class ChatUsers extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      useraction: "",
      showUpdateModal: false,
      items: [],
      activePage: 1,
      root: "users",
      PageTitle: " Users",
      FormTitle: "New Users",
      formvalue: {},
      event: "",
      depId:[],
      search: "",
      showAlert: false,
      AlertMsg: "",
      AlertVarient: "",
      assignedDepartments: [],
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.searchAgent = this.searchAgent.bind(this);
    this.getUsers = this.getUsers.bind(this);
    this.selectedrow = {
      name: "",
      username: "",
      domainusername:"",
      emails: "",
      manager: "",
      employeeId: "",
      department: [],
      password: "",
      verified: true,
      requirePasswordChange: false,
      role: [],
    };
    this.list = [
      {
        name: "Names",
        label: "Names",
        selector: "name",
      },
      {
        name: "Username",
        label: "Usernames",
        selector: "username",
      },
      {
        name: "Emails",
        label: "Emails",
        selector: "emails",
      },
      {
        name: "Manager",
        label: "ManagerId",
        selector: "manager",
      },
      
    ];
    this.columnlist = [
      {
        name: "_id",
        label: "id",
        type: "hidden",
        hide: true,
      },
      {
        name: "name",
        label: "Name",
      },
      {
        name: "username",
        label: "Username",
        editable: false
      },
      {
        name: "domainusername",
        label: "Domain Username",
        editable: false
      },
      {
        name: "emails",
        label: "Emails",
      },
      {
        name: "manager",
        label: "Manager(EmpId)",
      },
      {
        name: "employeeId",
        label: "EmployeeId",
      },
      {
        name: "department",
        label: "Department",
        selector:"department",
        type: "multiselectMongo",
        config: {
          root: "livechat_department",
          cols: JSON.stringify(["_id", "name"]),
          con: {},
          state: true,
        },
      },
      
      {
        name: "password",
        label: "Password",
        editable: false
      },
      {
        name: "role",
        label: "Role",
        type: "multiselectMongo",
        config: {
          root: "roles",
          cols: JSON.stringify(["_id", "name"]),
          con: {},
        },
      },
      {
        name: "verified",
        label: "Verified",
        type: "hidden",
        hide: true,
      },
      {
        name: "requirePasswordChange",
        label: "Require Password Changes",
        type: "hidden",
        hide: true,
      },
      
    ];
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
      var department=nextProps.CommonData["livechat_department_agents"];
      const dep=department && department.map((e)=>{
        return e.departmentId
      })
      var depAdded=nextProps.CommonData[this.state.root];
     depAdded && depAdded.map((e=>{
        return e["department"]=dep
      }))
      // console.log("depAAdded",depAdded);
      var raw_items = depAdded
        ? depAdded.map((e) => {
            return {
              _id: e._id,
              name: e.name || "--",
              verified: e.emails?e.emails[0].verified:'',
              employeeId:e.employeeId,
              requirePasswordChange:e.requirePasswordChange,
              domainusername :e.domainusername || "--",
              department: e.department ? e.department.map(e=>({"label": e, "value": e})) : null,
              role: e.roles ? e.roles.map(e => ({"label": e, "value": e})) : null,
              username: e.username || "--",
              manager: e.manager || "--",  
              emails:
                e.emails && e.emails[0] && e.emails[0].address
                  ? e.emails[0].address
                  : "--",
            };
          })
        : [];

      this.setState({ items: raw_items});
      this.setState({ showAlert: false})
    }
    if (
      nextProps.CommonData && Object.values(nextProps.CommonData.InsertSuccessData).length > 0
    ) {
      const motor_response = nextProps.CommonData?.InsertSuccessData?.motor_response?.success;
      const common_response = nextProps.CommonData?.InsertSuccessData?.common_response?.success;
      const health_response = nextProps.CommonData?.InsertSuccessData?.health_response?.sucess;

      if (motor_response == false || common_response == false || health_response == false || nextProps.CommonData?.InsertSuccessData?.status== 400 || nextProps.CommonData?.InsertSuccessData?.status== 500   ) {
        const healthMsg=nextProps.CommonData?.InsertSuccessData?.health_response?.message;
        const commonMsg=nextProps.CommonData?.InsertSuccessData?.common_response?.message;
        const motorMsg =nextProps.CommonData?.InsertSuccessData?.motor_response?.message; 
        alert(healthMsg || commonMsg || motorMsg || nextProps.CommonData?.InsertSuccessData?.message || nextProps.CommonData?.InsertSuccessData?.error);

        nextProps.CommonData.InsertSuccessData = [];
      } else {
        this.setState({ showModal: false });
        this.setState({
          showAlert: true,
          AlertMsg: "Record Add Successfully.",
          AlertVarient: "success",
        });
        nextProps.CommonData.InsertSuccessData = [];
      }
    }
    if (
      nextProps.CommonData && Object.values(nextProps.CommonData.UpdateSuccessData).length > 0
      
    ) {
      const motorUpdate_response = nextProps.CommonData?.UpdateSuccessData?.motorUpdate_response?.success;
      const commonUpdate_response = nextProps.CommonData?.UpdateSuccessData?.commonUpdate_response?.success;
      const healthUpdate_response = nextProps.CommonData?.UpdateSuccessData?.healthUpdate_response?.sucess;
      
    if(motorUpdate_response ==false || commonUpdate_response == false || healthUpdate_response == false || nextProps.CommonData?.UpdateSuccessData?.status== 400 || nextProps.CommonData?.UpdateSuccessData?.status== 500 ){
          const healthMsg=nextProps.CommonData?.UpdateSuccessData?.healthUpdate_response?.message;
          const commonMsg=nextProps.CommonData?.UpdateSuccessData?.commonUpdate_response?.message
          const motorMsg =nextProps.CommonData?.UpdateSuccessData?.motorUpdate_response?.message
          alert(healthMsg || commonMsg || motorMsg || nextProps.CommonData?.UpdateSuccessData?.message || nextProps.CommonData?.UpdateSuccessData?.error
         );
        nextProps.CommonData.UpdateSuccessData=[];
        this.setState({ showModal: true});
      }
      else {
        this.setState({ showModal: false });
        this.setState({
          showAlert: true,
          AlertMsg: "Record Updated Successfully.",
          AlertVarient: "success",
        });
        nextProps.CommonData.UpdateSuccessData=[];
      }
    }
  }


  handleShow() {
    this.setState({
      showAlert: false,
      useraction: "SAVE",
      formvalue: { ...this.selectedrow },
      event: "Add",
      showModal: true,
      FormTitle: "New User"
    });
  }

  handleEdit(row) {
    this.setState({
      formvalue: row,
      useraction: "UPDATE",
      event: "Edit",
      showModal: true,
      FormTitle: "Edit Record",
    });
  }

  fnDatatableCol() {
    this.list.push({
      name: "Action",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button variant="secondary" onClick={() => this.handleEdit(row)}>
            <i className="fa fa-pencil-square-o" aria-hidden="true"></i>
          </Button>
        </ButtonGroup>
      ),
    });
    return this.list;
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }

  handleSave = () => {
    if (
      document.getElementsByName("frmChatUsers").length > 0 &&
      document.getElementsByName("frmChatUsers")[0].reportValidity()
    ) {
      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      var id = formvalue["_id"];
      // delete formvalue["_id"];
     

      if (this.state.event == "Edit") {
        delete formvalue["password"]
        this.fnCleanData(formvalue);
        let res = this.props.UpdateData(
          {
            root: this.state.root,
            body: formvalue,
            querydata: { "_id": id },
            c: "R",
          },
          function (data) {
            toast("Record Saved Successfully!", { type: "success" });
          }
        );

        // this.props.addRecord(
        //   {
        //     root: "History",
        //     body: {
        //       module: "ChatUsers",
        //       od: this.state.od,
        //       nd: formvalue,
        //       ts: new Date(),
        //       by: getuser().UserId,
        //     },
        //   },
        //   function (data) {
        //     toast("History Maintained!", { type: "success" });
        //   }
        // );
      } else {
        this.props.addRecord({
          root: "users",
          body: {
            module: "",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserID,
          },
        });
      }
    }
  };

  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    } else if (e._isAMomentObject) {
      formvalue[props] = e.format();
    } else {
      if (e.type == "multiselect") {
        formvalue[e.id] =
          e.newselectedList && e.newselectedList.length
            ? e.newselectedList.map((e) => e.value)
            : [];
      } else {
        formvalue[e.target.id] =
          e.target.type === "number"
            ? parseInt(e.target.value)
            : e.target.value;
      }
    }
    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  };
  searchAgent = (e) => {
    this.setState({ search: e.target.value });
  };

  getUsers = () => {
    let condition = [];
    const inputValue = this.inputElement.value;
    if (!!inputValue) {
      condition.push({ username: inputValue });
    }
    this.props.GetCommonData({
      limit: 20,
      skip: 0,
      root: this.state.root,
      cols: JSON.stringify(["name", "username", "emails", "verified",
      "manager","requirePasswordChange","employeeId","domainusername","roles"]),
      con: condition,
      c: "R",
    });
    this.props.GetCommonData({
      limit: 20,
      skip: 0,
      root: "livechat_department_agents",
      cols: JSON.stringify(["departmentId"]),
      con: condition,
      c: "R",
    });

  };
  handleClose = (nextProps) => {
    this.setState({ showModal: false });
  };

  render() {
    const columns = this.fnDatatableCol();
    const {
      items,
      search,
      useraction,
      PageTitle,
      showModal,
      showUpdateModal,
      FormTitle,
      formvalue,
      showAlert,
      AlertVarient,
      AlertMsg,
    } = this.state;
    // console.log("formvalue",formvalue);
    return (
      <>
        <div className="content">
          <AlertBox
            show={showAlert}
            variant={AlertVarient}
            body={AlertMsg}
          ></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>

                    <Col md="4">
                      <Form.Control
                        type="text"
                        ref={(input) => {
                          this.inputElement = input;
                        }}
                        // onChange={this.searchAgent}
                        placeholder={"Enter Username"}
                      />
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.getUsers}>
                        Fetch
                      </Button>
                    </Col>

                    <Col md={2}>
                      <Button variant="primary" onClick={this.handleShow}>
                       ADD USER
                      </Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                    defaultSortFieldId={1}
                    pagination
                    striped={true}
                    highlightOnHover
                  />
                  {/* <DataTable columns={this.columnlist} data={items} /> */}
                </CardBody>
              </Card>
            </Col>
          </Row>
          <Modal
            show={showModal}
            onHide={this.handleClose}
            dialogClassName="modal-90w"
          >
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmChatUsers">
                <Row>
                  {this.columnlist.map((col) =>
                    fnRenderfrmControl(
                      col,
                      formvalue,
                      this.handleChange,
                      this.state.event
                    )
                  )}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <Button variant="primary" onClick={this.handleSave}>
              {useraction}
              </Button>
            </Modal.Footer>
          </Modal>

          
        </div>
      </>
    );
  }
}
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}
export default connect(mapStateToProps, {
  GetCommonData,
  addRecord,
  UpdateData,
  addRecord,
})(ChatUsers);
