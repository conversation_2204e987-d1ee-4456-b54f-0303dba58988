import React, { Component } from 'react';
import {  Button, Form } from 'react-bootstrap';
import {
    Row,
    Col
} from "reactstrap";

import OtpInput from "react-otp-input";
import { If, Then, Else } from 'react-if';
import moment from 'moment';


class Verification extends Component{

    constructor(props) {
        super(props);
        this.state = {
            userIcon:'/userid.svg',
            dateIcon:'/date.svg',
            timeIcon:'/time.svg',
            productIcon:'/product.svg',
            callingIcon:'/calling.svg',
            verifyImg:'/verify-ad.svg',
            bgImg: '/Graphic.svg' ,    
    }
    }

    back  = (e) => {
        //window.location.reload();
        e.preventDefault();
        this.props.prevStep(1);
    }

    render(){

        const { verifyImg, UserInfo,bgImg } = this.props
        return(
            <Row> 
            <Col md={6} xs={12}> 
            <Button className="backCallLog" onClick={this.back}><i className="fa fa-arrow-left"></i></Button> 
            <div className="verifybgImg2"><img src={this.state.bgImg}/>    </div></Col>
            <Col md={6} xs={12}>                          
               <Row>
                   <div className="AdvisorHeader">
                   <div className="bgBlue row">
                   <Col  md={3} xs={12} className="text-center">  <img src={this.state.verifyImg}/> </Col>
                   <Col  md={9} xs={12}>
                   <h2>{UserInfo.UserName}</h2>
                   <p>You are speaking with the genuine <br/> Policybazaar advisor</p>
                   </Col>
                  </div>                                         
                   </div>
                   <div  className="advisorDetails bgWhite">
                   <Row>
                   <Col lg={4} md={6} xs={6}>
                  <img src={this.state.productIcon}/>
                  <span>Product</span>
                  <p>{UserInfo.ProductName}</p>
                  </Col>
                  <Col lg={4} md={6} xs={6}>
                  <img src={this.state.userIcon} />
                  <span>Advisor Id</span>
                  <p>{UserInfo.UserId}</p>
                  </Col>
                  <Col lg={4} md={6} xs={6}>
                  <img src={this.state.timeIcon} />
                  <span>Time</span>
                  <p>{moment(new Date(UserInfo.CallDate)).format("hh:mm A")}</p>
                  </Col>
                  {/* <Col lg={4} md={6} xs={6}>
                  <img src={this.state.callingIcon} />
                  <span>Calling Number</span>
                  <p>0124-823627</p>
                  </Col> */}
                  <Col lg={4} md={6} xs={6}>
                  <img src={this.state.dateIcon} />
                  <span>Date</span>
                  <p>{moment(new Date(UserInfo.CallDate)).format("DD MMM, YYYY")}</p>
                  </Col>                  
                  </Row>
                   </div>
               </Row>
            </Col> 
               </Row>      

                                                   
        )
    }
}


export default Verification;