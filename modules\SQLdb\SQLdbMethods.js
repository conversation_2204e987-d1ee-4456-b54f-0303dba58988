const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
const UserStatsToManager = require("../SQLdb/UserStatsToManager");
const xlsxFile = require('read-excel-file/node');
var fs = require('fs');
var jsonxml = require('jsontoxml');
const bodyParser = require("body-parser");
const crypto = require('crypto');
const encryptionType = 'aes-256-cbc';
const encryptionEncoding = 'base64';
const bufferEncryption = 'utf-8';
const moment = require("moment");
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const AWS = require('aws-sdk');
const { Base64Encoding } = require('../../auth');

//AWS.config.update(conf.s3);

const CommonMethods = require('../common/CommonMethods');
const { AesEncryption } = require("../common/CommonMethods");


const { ExcelFileColumns } = require("../ExcelFileColumns");
const { GetIncentiveColumn, typeConvert, unWantedCharacters, dateCheck } = require("../GetIncentiveColumn");
var cache = require('memory-cache');
var _ = require('underscore');
const { bulkInsertBooking, bulkInsertIncentiveData, InsertFileLog, InsertFileUploadLog } = require("../Mongodb/Methods");
const conf = require("../../env_config");
const { InsertDataMongo, IterateChildren } = require("../common/CommonMethods");
const date = require("joi/lib/types/date");
const constants = require("../constants");
const Admin = require("mongodb/lib/admin");

// Function to mask MobileNo
const maskMobileNo = (mobileNo) => {
// Replace all but the last 4 digits with asterisks
return mobileNo.slice(0, -2).replace(/\d/g, '*') + mobileNo.slice(-2);
};

exports.FindRoot = function (req, res) {
    let result = false;
    switch (req.query.root) {
        case "Hierarchy":
            CallHierarchy(req,res);
            result = true;
            break;
        case "UserDetails":
            GetUserDetails(req, res);
            result = true;
            break;
        case "IVRActivityReport":
            IVRActivityReport(req, res);
            result = true;
            break;
        case "UserStatsToManager":
            UserStatsToManager.UserStatsToManager(req, res);
            result = true;
            break;
        case "unregistered":
            unregistered(req, res);
            result = true;
            break;
        // case "AgentLeadsNotCalled":
        //     AgentLeadsNotCalled(req, res);
        //     result = true;
        //     break;
        case "InboundCallData":
            InboundCallData(req, res);
            result = true;
            break;
        // case "AesDecryption":
        //     AesDecryption(req, res);
        //     result = true;
        //     break;
        case "GetAgentsUnderManagers":
            GetAgentsUnderManagers(req, res);
            result = true;
            break;
        case "SurveyQuestionMasterJoin":
            SurveyQuestionMasterJoin(req, res);
            result = true;
            break;
        case "FetchMenuMaster":
            FetchMenuMaster(req, res);
            result = true;
            break;
        case "FetchAsteriskUrl":
            FetchAsteriskUrl(req, res);
            result = true;
            break;
        case "FetchCallingCompany":
            FetchCallingCompany(req, res);
            result = true;
            break;
        case "SetSlotDistribution":
            SetSlotDistribution(req, res);
            result = true;
            break;
        case "GetNearestAgentToLead":
            GetNearestAgentToLead(req, res);
            result = true;
            break;
        case "Hierarchy2":
            FetchManagerHierarchy2(req, res);
            result = true;
            break;
    }

    return result;
}

async function FetchAsteriskUrl(req, res) {
    try {
        let result = await Utility.API_GET(conf.dialerApiUrlV2 + "dialer/getDialerData");

        let Apiparams = [];

        let json = result.data.servers
        for (let index = 0; index < json.length; index++) {
            const element = json[index];
            Apiparams.push({ Id: element.AsteriskIp, Display: element.AsteriskUrl, ProductID: element.productIds });
        }
        //console.log('apiparams',Apiparams);

        res.send({
            status: 200,
            data: [Apiparams],
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function FetchCallingCompany(req, res) {
    try {
        // console.log(conf.dialerApiUrlV2 + "dialer/getDialerData")

        let result = await Utility.API_GET(conf.dialerApiUrlV2 + "dialer/getDialerData");
        // console.log('asteriskurl', result)
        let Apiparams = [];
        let json = result.data.callingCompanies
        // console.log("callingcom", json)
        for (let index = 0; index < json.length; index++) {
            Apiparams.push({ Id: json[index], Display: json[index] });
        }

        // console.log('apiparams', Apiparams);
        res.send({
            status: 200,
            data: [Apiparams],
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}
async function GetUserDetails(req, res) {
    try {
        const { userId} = req.user || {};
        if (isNaN(userId)) {
            return res.send({
                status: 200,
                data: []
            });
        }

        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: userId });
        let result = await sqlHelper.sqlProcedure("R", constants['GetUserInfoMtxDashboard'], sqlparam);
        return res.status(200).json({
            status: 200,
            data: result.recordset
        });
    }
    catch (e) {
        console.log('GetUserDetails', e);
        return res.status(500).json({
            status: 500,
            message: e
        });
    }
}

async function FetchManagerHierarchy(req, res) {
    let result = {};
    // req.query.ManagerId = req.user.roleId==2?75:req.user.userId;
    if (req.query.ManagerId == 75) {
        result = cache.get('jerry');
        if (result === null) {
            result = await GetTree(req.query.ManagerId);
            cache.put('jerry', result, (60 * 60 * 1000), function (key, value) {
                // console.log(key + ' did ' + "EMPTY");
            });
        }
    }
    else {
        result = await GetTree(req.query.ManagerId);
    }


    res.send({
        status: 200,
        data: result
    });
}

async function GetTree(ManagerId) {
    let result = await GetUnderManagers(ManagerId);
    if (result) {
        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            let childrens = await GetTree(element.UserID);
            if (childrens && childrens.length > 0) {
                element["children"] = childrens;
            }

        }
    }

    return result;
}

exports.GetTreeUtility = async function (ManagerId) {
    let result = await GetUnderManagers(ManagerId);
    if (result) {
        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            let childrens = await GetTree(element.UserID);
            if (childrens && childrens.length > 0) {
                element["children"] = childrens;
            }

        }
    }

    return result;
}



async function GetUnderManagers(ManagerId) {
    try {

        let query = `SELECT distinct UD.UserID, 12 AS RoleId, UD.EmployeeId, UD.UserName, UD.ManagerId, 'Supervisor' AS RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  UD.IsActive=1 AND RSM.RoleId NOT IN(13) and UD.ManagerId = @ManagerId`
        // console.log(query);
        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        let res = result.recordset;
        return res;
    }
    catch (e) {
        console.log(e);
    }
    finally {

    }
}


async function IVRActivityReport(req, res) {
    try {

        let query = `SELECT
            DISTINCT n.callid AS utcallid,
            CONVERT(varchar, n.calltime,120) As calltime,
            C.ProductName,
            n.leadid,
            ISNULL(t.isAlreadyWhatsAppOptIn,'NULL') AS isAlreadyWhatsAppOptIn,
            ISNULL(t.isWhatsAppOptIn,'NULL') AS isWhatsAppOpted,            
            t.isAlreadyHaveOpenTicket AS OpenTicketExist,
            t.isTatBusted AS TicketTatBusted,
            t.ticketStatusCustInput,
            t.bookingGreaterThenThirtyDay AS BookingGreaterThen30Days,
            t.isPolicyIssued,
            t.ivrOption,
            t.softCopyOption,
            t.isSatisifyWithIvrOption,
            t.playnstp,
            t.playnstpid,
            t.playnstpOption,
            C.AssignedToAgent AS AssignedAgent,
            n.CallStatus AS AssignedAgentCallStatus,
            n.newcallid,
            n.language,
            n.QName,
            --n.agentname AS GroupAgent,
            CONVERT(varchar, n.Qentertime,120) As Qentertime,
            CONVERT(varchar, n.answertime,120) As answertime,
            CONVERT(varchar, n.hanguptime,120) As hanguptime,
            n.callstatus AS GroupCallStatus,
            --n.callendnode,
            n.callendreason,
            --n.ivrcalltype,
            --CASE WHEN n.duration='switchToService' THEN 'Yes' ELSE 'NO' END AS isSalesToService,
            C.BookingNo, C.PolicyNo, C.SupplierName, C.policyType, 
            CONVERT(varchar, C.bookingCreatedDate,120) As bookingCreatedDate,
            CONVERT(varchar, C.bookingStampingDate,120) As bookingStampingDate,
            C.registrationNumber,
            CASE WHEN C.isWhatsAppOptIn=1 THEN 'YES' ELSE 'NO' END AS isWhatsAppOptIn,
            CASE WHEN C.isStp=1 THEN 'STP' ELSE 'NSTP' END AS isStp,
            CASE WHEN C.isSoftCopyAvailable=1 THEN 'YES' ELSE 'NO' END AS isSoftCopyAvailable
        FROM MTX.IBCallData n
            INNER JOIN MTX.CustomerINFO C ON n.callid=C.UniqueueID AND n.leadid=C.LeadID AND C.IsBooking=1 AND C.productID IN (117,114)
            LEFT JOIN MTX.inboundCallTracking t ON n.callid=t.unique_id
        WHERE       n.calltime BETWEEN @startdate  AND @enddate AND CAST(n.calltime AS TIME) BETWEEN '10:00'  AND '19:00'
            AND ISNULL(n.QName,'') <> 'unregistered'`;

        if (req.query.ProductId > 0) {
            query = query + `AND C.productID = @productID`
        }

        let sqlparam = [];
        sqlparam.push({ key: "startdate", value: req.query.startdate });
        sqlparam.push({ key: "enddate", value: req.query.enddate });
        sqlparam.push({ key: "productID", value: req.query.productID });

        // console.log(query);
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function unregistered(req, res) {
    try {

        let query = `SELECT DATEPART(HOUR,n.calltime) AS hour, 
        CONVERT(varchar, n.calltime,120) As calltime,        
        ISNULL(n.CallID,0) callid, 
        ISNULL(n.AgentID,'') agentname, 
        ISNULL(CONVERT(varchar, n.Qentertime,120),'') AS entertime, 
        ISNULL(CONVERT(varchar, n.answertime,120),'') AS answertime, 
        ISNULL(CONVERT(varchar, n.hanguptime,120),'') AS hanguptime, 
        ISNULL(n.callstatus,'') AS callstatus, 
        ISNULL(t.callid,'') AS transferedCallid, 
        ISNULL(PD.ProductName,'') AS product, 
        CAST(ISNULL(t.leadid,'') AS CHAR( 50 ) ) AS leadid, 
        ISNULL(t.QName,'') AS mainQueue, 
        ISNULL(t.AgentID,'') AS mainQueueAgent, 
        ISNULL(CONVERT(varchar, t.Qentertime,120),'') AS mainQueueEnterTime, 
        ISNULL(CONVERT(varchar, t.answertime,120),'') AS mainQueueAnswerTime, 
        ISNULL(CONVERT(varchar, t.hanguptime,120),'') AS mainQueueHangupTime, 
        ISNULL(t.callstatus,'') AS mainQueueCallStatus 
        FROM		MTX.IBCallData n 
        LEFT JOIN	MTX.IBCallData t 
        LEFT JOIN   CRM.ProductDetails PD (NOLOCK) ON t.LeadID=PD.LeadID
        ON			n.callid=t.orig_callid                 
        WHERE       n.calltime BETWEEN @startdate  AND @enddate 
        AND			DATEPART(HOUR,n.CallTime) BETWEEN 10 AND 19 
        AND			n.QName = 'unregistered'`;

        let sqlparam = [];
        sqlparam.push({ key: "startdate", value: req.query.startdate });
        sqlparam.push({ key: "enddate", value: req.query.enddate });

        // console.log(query);
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function InboundCallData(req, res) {
    try {
        let sqlparam = [];
        sqlparam.push({ key: "FromDate", value: req.query.startdate });
        sqlparam.push({ key: "ToDate", value: req.query.enddate });
        sqlparam.push({ key: "QName", value: req.query.queues });
        // sqlparam.push({ key: "ProductID", value: req.query.ProductID });
        // sqlparam.push({ key: "ivrType", value: req.query.ivrType });
        // console.log('inboundcalldata,sqlparam', sqlparam);

        let result = await sqlHelper.sqlProcedure("R", "[MTX].[GetInboundCallData]", sqlparam);
        // console.log(result);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

// async function AesDecryption(req, res) {
//     try {
//         console.log(req.query);
//         let AesKey = '';
//         let AesIV = '';
//         if (req.query.Source && req.query.Source == 'Fos') {
//             AesKey = '';
//             AesIV = '';
//         }
//         const buff = Buffer.from(req.query.uid, encryptionEncoding);
//         const key = Buffer.from(AesKey, bufferEncryption);
//         const iv = Buffer.from(AesIV, bufferEncryption);
//         const decipher = crypto.createDecipheriv(encryptionType, key, iv);
//         const deciphered = decipher.update(buff) + decipher.final();
//         //return JSON.parse(deciphered);
//         return res.send({
//             status: 200,
//             data: JSON.parse(deciphered)
//         });
//     }
//     catch (e) {
//         console.log(e);
//         res.send({
//             status: 500,
//             message: e
//         });
//     }
//     finally {

//     }
// }

async function UploadIncentiveData(xml, typeId, userId, validfrom, productId, awsFileName, awsFilePath) {
    try {
        //console.log(xml,typeId, userId, validfrom, productId, awsFileName, awsFilePath);
        let sqlparam = [];
        sqlparam.push({ key: "ProductID", value: productId });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "Type", value: typeId });
        sqlparam.push({ key: "filename", value: awsFileName });
        sqlparam.push({ key: "filePath", value: awsFilePath });
        if (typeId == 15 || typeId == 16) {
            sqlparam.push({ key: "xml", value: '' });
        } else {
            sqlparam.push({ key: "xml", value: xml });
        }
        sqlparam.push({ key: "message", value: '' });
        if (!validfrom == '') {
            sqlparam.push({ key: "ValidFrom", value: validfrom });
        }
        return await sqlHelper.sqlProcedure("L", "[Enc].[UploadIncentiveData]", sqlparam);
    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}

exports.UploadMonthlyIncentiveFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);

        //console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" });
        }
        const myfile = req.files.myFile;
        var myFileName = myfile.name.split('.').join('-' + Date.now() + '.');
        const uploadDate = req.body.UploadDate;
        var dir =
            appDir +
            "\\client\\public\\SampleExcelfiles\\MonthlyIncentive\\" +
            myFileName + "-" + uploadDate;
        var filePath = await Utility.uploadFile(req.files, dir);
        [columnList, collection] = GetIncentiveColumn(req.body.FileType, req.body.ProductID);
        let fileType = req.body.FileType
        let productID = req.body.ProductID
        let excelData = await Utility.readXlsxFileNew(filePath.filePath);
       
        //console.log(excelData.length, "====");
        //console.log("excelData", excelData);
        if (excelData.length > 0) {
            let awsUploadResponse = uploadAWSfiles(dir, myFileName, 'asterisk-log');
            if (awsUploadResponse) {
                awsUploadResponse.then(async function (resultaws) {
                    if (resultaws.key != undefined) {
                        //  console.log('resultedkey', resultaws.key);
                        var awsFileName = myFileName;
                        var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultaws.key + '&bucket=asterisk-log';
                        fileLog = { "awsFileName": awsFileName, "awsFilePath": awsFilePath, "FileType": fileType, "ProductID": req.body.ProductID, "uploadDate": uploadDate }
                        let status = await InsertFileLog(fileLog)
                    }
                })
            }
            var _result = [];
            var rows = excelData[0].content;
            rows= rows.map(row => {
                for (const key in row) {
                  if (key.includes('__EMPTY')) {
                    delete row[key];
                  }
                }
                return row;
              });
            //console.log(rows);
            let fileColumns = Object.keys(excelData[0].content[0]);
            var excelColumns = Object.keys(columnList).map(function (k) {
                return columnList[k].name;
            });
            let check = excelColumns.filter(function (value) {
                return fileColumns.indexOf(value) == -1;
            });

            //console.log("OutPut", excelData[0].content[0].IncentiveMonth)
            if (check.length == 0) {
                if (
                    dateCheck(excelData[0]?.content[0]?.IncentiveMonth, req.body.UploadDate)
                ) {
                    for (var i = 0; i in rows; i++) {
                        var obj = {};
                      //  console.log("rows", i);
                        for (j in rows[i]) {
                            //console.log(req.body.UploadDate, rows[0]["IncentiveMonth"]);

                            if (unWantedCharacters(rows[i][j], j) == false) {
                               // console.log(columnList.find((item) => item.name == j))
                            //    console.log("hi ",columnList.find((item) => item.name == j) != undefined);
                                if (columnList.find((item) => item.name == j) != undefined) {
                                    //console.log(columnList.find((item) => item.name == j).type)
                                    let datatype = columnList.find((item) => item.name == j).type;
                                    obj[j] = typeConvert(rows[i][j], datatype); //data converstion
                                }
                                else {
                                    return res.send({
                                        status: 501,
                                        message: "File Error"
                                    })
                                }
                            } else {
                                //console.log(i, j, rows[i][j]);
                                return res.send({
                                    status: 40,
                                    row: i + 2,
                                    column: j,
                                    message: "Column Error",
                                });
                            }
                        }
                        obj['productID'] = productID;
                        obj['createOn'] = new Date();
                        obj['read'] = false;
                        _result.push(obj);
                    }

                   // console.log(_result);

                    let databaseselection = ''
                    if (req.body.FileType == '6' || req.body.ProductID == '217' || req.body.ProductID === '117' && req.body.FileType === '5') {
                        databaseselection = 'MD';
                    }
                    var result = await bulkInsertIncentiveData(_result, collection, databaseselection);
                    if (result.status == 200) {
                        return res.send({
                            status: 200,
                            message: "File Uploaded!!",
                        });
                    }
                } else {
                    return res.send({
                        status: 41,
                        message: "Date Error"
                    });
                }
            } else {
                return res.send({
                    status: 403,
                    data: check,
                    message: "Incorrect Columns",
                });
            }
        }

        // console.log("Endtime", new Date());
        // console.log("Data", excelData)
    } catch (e) {
        console.log("error", e);
        return res.send({
            status: 500,
            message: "Failed to upload",
        });
    }
};

exports.UploadIncentiveFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        var TypeIsXml = JSON.parse(req.body.TypeIsXml);
        // console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        // console.log("timestampfilename", myFileName);
        // console.log("cwd", process.cwd());
        // console.log("tmpfile", myFile.tempFilePath);
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, function (err) {
            // console.log(err);
            if (err) {
                //throw err
                console.log(err)
            } else {
                // console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                // console.log('filepath', filePath, "filename", myFileName);
                let timestamp = Date.now();
                let filename = "Incentive/" + today + "/" + myFileName;

                if (req.body.TypeId && (req.body.TypeId == 17 || req.body.TypeId == 18 || req.body.TypeId == 20 || req.body.TypeId==22)) {
                    var batchId = uuid.v1().toUpperCase();

                    xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                        for (var col = 0; col < rows[0].length; col++) {
                            if (ExcelFileColumns.UploadIncentiveData[req.body.TypeId].indexOf(rows[0][col]) === -1) {
                                return res.send({
                                    status: 403,
                                    message: "Invalid column(s) used in sheet, please refer sample file"
                                });
                            }
                        }

                        var _result = [];
                        //var JsonExcel = {};
                        if (req.body.TypeId == 20) {
                            let sqlparams = [];
                            sqlparams.push({ key: "ProductId", value: req.body.ProductId });
                            sqlHelper.sqlProcedure("L", "mtx.ResetAgentProcessDetails", sqlparams);
                        }
                        for (var i = 1; i in rows; i++) {
                            var obj = {};
                            for (j in rows[i]) {
                                obj[rows[0][j]] = rows[i][j];
                            }

                            //var innerObj = {};
                        
                            let data = await uploadFileReadData(obj, req.body, batchId);
                            const {errorStatus,message}= data;
                            
                            if(errorStatus)
                            {
                               
                                if(!obj.LeadID)
                                {
                                    obj.LeadID="Please enter LeadID";
                                }
                                if(!obj.LeadSource)
                                {
                                    obj.LeadSource="Please enter LeadSource"
                                }
                                // console.log("the object is",obj)
                        
                                obj['Status']=message;

                            }
                            if(!obj['Status'])
                            {
                            obj['Status'] = data;
                            }
                            // console.log("the object is",obj);
                            _result.push(obj);
                        }
                        res.send({
                            status: 200,
                            data: _result
                        });
                    });
                } else {
                    let awsUploadResponse = uploadAWSfiles(filePath, filename, 'asterisk-log');
                    // console.log("awsresp", awsUploadResponse);
                    awsUploadResponse.then(function (resultaws) {
                        // console.log('resultedkey', resultaws.key);
                        var awsFileName = myFileName;
                        var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultaws.key + '&bucket=asterisk-log';

                        if (req.body.TypeId == 15) {
                            xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                                for (var col = 0; col < rows[0].length; col++) {
                                    if (ExcelFileColumns.UploadIncentiveData[req.body.TypeId].indexOf(rows[0][col]) === -1) {
                                        return res.send({
                                            status: 403,
                                            message: "Invalid column(s) used in sheet, please refer sample file"
                                        });
                                    }
                                }

                                var _result = [];
                                for (var i = 1; i in rows; i++) {
                                    var obj = {};
                                    for (j in rows[i]) {
                                        obj[rows[0][j]] = rows[i][j];
                                    }
                                    _result.push(obj);
                                }
                                await bulkInsertBooking(_result);
                            });
                        }

                        // Read Excel file to xml
                        let xml = ExcelToXml(myFile, TypeIsXml);
                        // console.log("xml",xml);
                        xml.then(function (resultedxml) {
                            // console.log('xmlrsult', resultedxml);
                            let response = UploadIncentiveData(resultedxml, req.body.TypeId, req.body.UserId, req.body.ValidFrom, req.body.ProductId, awsFileName, awsFilePath);
                            response.then(function (result) {
                                // console.log("recordset", result.recordset) // "Some User token"
                                res.send({
                                    status: 200,
                                    data: result.recordset
                                });
                            })
                        })

                    })
                }


            }
        })


        // xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {console.log(rows);
        //     var _result = [];
        //     //let response = [];
        //     var JsonExcel = {};
        //     for (var i = 1; i in rows; i++) {
        //         var obj = {};
        //         for (j in rows[i]) {
        //             obj[rows[0][j]] = rows[i][j];
        //         }
        //         var innerObj = {};
        //         innerObj['Data'] = obj;
        //         _result.push(innerObj);
        //     }
        //     JsonExcel['xml'] = _result;
        //     console.log("result", JsonExcel);

        //     var xml = jsonxml(JsonExcel);
        //     console.log(xml);
        // let response = UploadIncentiveData(xml, req.body.TypeId, req.body.UserId, req.body.ValidFrom, req.body.ProductId, awsFileName, awsFilePath);
        // response.then(function (result) {
        //     console.log(result.recordset) // "Some User token"
        //     res.send({
        //         status: 200,
        //         data: result.recordset
        //     });
        // })

        // })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
    // console.log('here');
}

/**
 * Call SP and set parameters
 * @param {*} xlsData 
 * @param {*} fixedParams 
 * @param {*} batchId 
 * @returns 
 */
async function uploadFileReadData(xlsData, fixedParams, batchId) {
    var response = '';
    try {
        let sqlparam = [];
        let spName = '';
        if (fixedParams.TypeId == 17 || fixedParams.TypeId == 18) {
            sqlparam.push({ key: "ProductID", value: fixedParams.ProductId });
            sqlparam.push({ key: "UploadedBy", value: fixedParams.UserId });
            sqlparam.push({ key: "BatchId", value: batchId });
            //sqlparam.push({ key: "message", value: '' });

            if (!fixedParams.ValidFrom == '') {
                sqlparam.push({ key: "ValidFrom", value: fixedParams.ValidFrom });
            }
            sqlparam.push({ key: "SuperGroupId", value: xlsData.SuperGroupId });
        }

        if (fixedParams.TypeId == 17) {
            sqlparam.push({ key: "SuperGroupName", value: xlsData.SuperGroupName ? (xlsData.SuperGroupName.trim()).substring(0, 100) : '' });
            sqlparam.push({ key: "GroupType", value: xlsData.Type });
            sqlparam.push({ key: "MinRange", value: xlsData.LowerRange });
            sqlparam.push({ key: "MaxRange", value: xlsData.UpperRange });
            sqlparam.push({ key: "slabWeightage", value: xlsData.Weightage });
            sqlparam.push({ key: "SubCategoryID", value: xlsData.SubCategoryID });
            spName = "[Enc].[UploadAgentSlabs]";
        } else if (fixedParams.TypeId == 18) {
            sqlparam.push({ key: "FloorProcess", value: xlsData.FloorProcess ? (xlsData.FloorProcess.trim()).substring(0, 100) : '' });
            spName = "[Enc].[UploadFloorProcessMapping]";
        } else if (fixedParams.TypeId == 20) {//20 for AgentProcessDetails
            // console.log(xlsData);
            if (fixedParams.ProductId == xlsData.ProductId) {
                sqlparam.push({ key: "EmployeeId", value: xlsData.EmployeeId });
                //sqlparam.push({key: "Tenure", value : '2020-10-01'});
                sqlparam.push({ key: "FloorProcess", value: xlsData.FloorProcess });
                sqlparam.push({ key: "TLEmployeeId", value: xlsData.TLEmployeeId });
                sqlparam.push({ key: "AMEmployeeId", value: xlsData.AMEmployeeId });
                sqlparam.push({ key: "ManagerEmployeeId", value: xlsData.ManagerEmployeeId });
                sqlparam.push({ key: "TargetAPE", value: xlsData.TargetAPE });
                sqlparam.push({ key: "TargetBookings", value: xlsData.TargetBookings });
                sqlparam.push({ key: "ProductId", value: fixedParams.ProductId });
                sqlparam.push({ key: "Location", value: xlsData.Location });
                sqlparam.push({ key: "CreatedBy", value: fixedParams.UserId });
                sqlparam.push({ key: "RoleType", value: xlsData.RoleType });
                spName = "mtx.InsertAgentProcessDetails"
            }
        }
        else if(fixedParams.TypeId==22){
            if(xlsData.LeadID && xlsData.LeadSource)
            {
            sqlparam.push({ key: "LeadID", value: xlsData.LeadID });
            sqlparam.push({ key: "Utm_Source", value: xlsData.Utm_Source });
            sqlparam.push({ key: "Utm_Medium", value: xlsData.Utm_Medium });
            sqlparam.push({ key: "Utm_Term", value: xlsData.Utm_Term });
            sqlparam.push({ key: "Utm_Campaign", value: xlsData.Utm_Campaign });
            sqlparam.push({ key: "LeadSource", value: xlsData.LeadSource });
            sqlparam.push({ key: "ProductID", value: xlsData.ProductID });
            sqlparam.push({ key: "ProductName", value: xlsData.ProductName});
            sqlparam.push({ key: "GroupID", value: xlsData.GroupID });
            sqlparam.push({ key: "LeadRank", value: xlsData.LeadRank });
            spName="mtx.InsertReopenLeads";
            }
            
            else{
                // console.log("WE ARE IN");
                return { errorStatus: 1, message: 'please enter leadid ...'}
            }
        }
        // console.log(sqlparam)
        result = await sqlHelper.sqlProcedure("L", spName, sqlparam);
        // console.log(result)
        let status = result && result.recordset && result.recordset[0];
        response = status['message'];
        return response;
    }
    catch (e) {
        response = e.toString();
        return response;
    }
//     finally {
//         return response;
//     }
 }

async function UploadVideoFileData(body, awsFilePath, pageId) {
    try {
        // console.log(body.userId, awsFilePath, pageId);
        let sqlparam = [];
        if (pageId) {
            sqlparam.push({ key: "pageId", value: pageId });
        } else {
            sqlparam.push({ key: "ProductId", value: body.ProductId });
            sqlparam.push({ key: "UserRoleID", value: body.roleId });
            sqlparam.push({ key: "UserGroupID", value: body.groupId });
        }
        sqlparam.push({ key: "userId", value: body.userId });
        sqlparam.push({ key: "IsRequired", value: body.IsRequired });
        sqlparam.push({ key: "IsActive", value: body.IsActive });
        sqlparam.push({ key: "PageUrl", value: awsFilePath });
        sqlparam.push({ key: "Link", value: body.Link });
        sqlparam.push({ key: "SurveyName", value: body.SurveyName });
        sqlparam.push({ key: "StartDate", value: body.StartDate });
        sqlparam.push({ key: "Endtime", value: body.Endtime });
        sqlparam.push({ key: "ContentType", value: body.ContentType });
        sqlparam.push({ key: "MultiLoginCount", value: body.MultiLoginCount });
        sqlparam.push({ key: "SurveyLocation", value: body.SurveyLocation });

        let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertPreLoginPageSurveyByPanelTest]", sqlparam);
        // console.log(result);
        return result;

    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}


async function AllowedMultipleSeenOnPrelogin(ProductID) {
    try {
        let sqlparam = [];
        sqlparam.push({ key: "ProductId", value: ProductID });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[GetMultiLoginByProduct]", sqlparam);
        // console.log(result);
        return result;

    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}


exports.UploadVideoFile = async function (req, res) {
    try {
        // console.log(req.body);   
        if(req.body.MultiLoginCount != 1 && req.body.ProductId) {
            let response = AllowedMultipleSeenOnPrelogin(req.body.ProductId);
            response.then(function (result) {
                // console.log("recordset", result.recordset[0].status) // "Some User token"
                if (result.recordset && result.recordset.length > 0) {
                    let status = result.recordset[0].status
                    if( status == 0){
                        return res.send({
                            status: 200,
                            data: result.recordset
                        });
                        
                    }else{
                        PreLoginSurvey(req, res);
                    }
                }
               
            })
        }else{
            PreLoginSurvey(req, res);
        }
      

        //}
        //})

    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function PreLoginSurvey (req, res) {
    var response = '';
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        var pageId = '';
        if (req.body.pageId) {
            pageId = req.body.pageId;
        }
        if (req.files) {
            // console.log('appdir', appDir, req.body, req.files);
            if (!req.files) {
                return res.status(500).send({ msg: "file is not found" })
            }

            // accessing the file
            const myFile = req.files.myFile;
            const today = moment(Date.now()).format('YYYY-MM-DD');
            var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');//myFile.name;
            // console.log("timestampfilename", myFileName);
            //let CopyFileTodayRes = CopyFileToday(myFile);
            //CopyFileTodayRes.then(function (resultedRes) {
            //console.log(resultedRes);
            //if (resultedRes) {           
            // Upload File to AWS
            var filePath = appDir + "\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
            // console.log('filepath', filePath, "filename", myFileName);
            let timestamp = Date.now();
            let filename = "PreLogin/" + today + "/" + myFileName;
            //let awsUploadResponse = uploadAWSfiles(filePath, filename, 'policystatic.policybazaar.com');
            let awsUploadResponse = uploadAWSfiles(`${myFile.tempFilePath}`, filename, 'policystatic.policybazaar.com');

            // console.log("awsresp", awsUploadResponse);
            awsUploadResponse.then(function (resultaws) {

                // console.log('resultedkey', resultaws.key);
                var awsFileName = myFileName;
                var awsFilePath = 'https://' + resultaws.Bucket + '/' + resultaws.Key;

                let response = UploadVideoFileData(req.body, awsFilePath, pageId);
                response.then(function (result) {

                    // console.log("recordset", result.recordset) // "Some User token"
                    res.send({
                        status: 200,
                        data: result.recordset
                    });
                })


            })
        } else {

            let response = UploadVideoFileData(req.body, awsFilePath, pageId);
            response.then(function (result) {
                // console.log("recordset", result.recordset) // "Some User token"
                res.send({
                    status: 200,
                    data: result.recordset
                });
            })

        }

    }
    catch (e) {

        console.log(e.getMessage());
        response = e.getMessage();
    }
    finally {
        return response;
    }
}


exports.FileUpload = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        // console.log("Inside FileUpload")
        //console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" });
        }
        const myfile = req.files.myFile;
        var myFileName = myfile.name.split('.').join('-' + Date.now() + '.');
        const uploadDate = req.body.UploadDate;
        var dir =
            appDir +
            "\\client\\public\\FileUpload\\" +
            myFileName + "-" + uploadDate;

        var filePath = await Utility.uploadFile(req.files, dir);

        let fileType = req.body.FileType

        let awsUploadResponse = uploadAWSfiles(dir, myFileName, 'matrixbtk');
        if (awsUploadResponse) {
            awsUploadResponse.then(async function (resultaws) {
                if (resultaws.key != undefined) {
                    // console.log('resultedkey', resultaws.key);
                    var awsFileName = myFileName;
                    var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultaws.key + '&bucket=matrixbtk';
                    fileLog = {
                        "awsFileName": awsFileName,
                        "awsFilePath": awsFilePath,
                        "FileType": fileType,
                        "uploadDate": uploadDate
                    }
                    let status = await InsertFileUploadLog(fileLog)
                }
            });

        }
        res.send({
            status: 200,
            message: "File Uploaded!!",
        });


        // console.log("Endtime", new Date());
        // console.log("Data", excelData)
    } catch (e) {
        console.log("error", e);
    }
};

async function CopyFileToday(myFile) {
    try {
        var appDir = path.dirname(require.main.filename);
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        let result = await fs.promises.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + `${myFile.name}`)
            .then(async () => {
                return true;
            })
            .catch(function (err) {
                return false;
            });
        // console.log(result);
        return result;
    } catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function ExcelToXml(myFile, TypeIsXml) {
    try {
        // console.log("TypeIsXml", TypeIsXml);
        if (TypeIsXml == true) {
            let result = await xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {
                var _result = [];
                //let response = [];
                var JsonExcel = {};
                for (var i = 1; i in rows; i++) {
                    var obj = {};
                    for (j in rows[i]) {
                        obj[rows[0][j]] = rows[i][j];
                    }
                    var innerObj = {};
                    innerObj['Data'] = obj;
                    _result.push(innerObj);
                }
                JsonExcel['xml'] = _result;
                // console.log("result", JsonExcel);

                var xml = jsonxml(JsonExcel);
                // console.log(xml);
                return xml;

            })
            return result;
        } else {
            return '<xml></xml>';
        }
    } catch (e) {
        console.log(e);
        return e;
    }
    finally {

    }
}

async function uploadAWSfiles(filepath, filename, bucketname) {
    // Load the AWS SDK for Node.js
    var AWS = require('aws-sdk');
    // Set the region 
    AWS.config.update({ region: 'ap-south-1' });

    // Create S3 service object
    const s3 = new AWS.S3();

    // call S3 to retrieve upload file to specified bucket
    var uploadParams = { Bucket: bucketname, Key: '', Body: '' };
    var file = filepath;
    // console.log(filepath);
    // Configure the file stream and obtain the upload parameters
    var fs = require('fs');
    var fileStream = fs.createReadStream(file);
    fileStream.on('error', function (err) {
        console.log('File Error', err);
    });
    uploadParams.Body = fileStream;
    var path = require('path');
    uploadParams.Key = filename;

    // call S3 to retrieve upload file to specified bucket
    var s3upload = s3.upload(uploadParams).promise();
    // return the `Promise`
    var result = await s3upload
        .then(function (data) {
            // console.log(data);
            return data;
        })
        .catch(function (err) {
            console.log("uploadAWSfiles", err)
            return err.message;
        });
    // var result = await s3.upload (uploadParams, function (err, data) {
    //   if (err) {
    //     console.log("Error", err);
    //     return err.message;
    //   } if (data) {
    //     console.log("Upload Success", data);
    //     return data.key;
    //   }
    // });
    return result;

}


exports.HealthCheck = function (req, res) {
    try {
        res.send({
            status: 200,
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}
async function GetAgentsUnderManagers(req, res) {
    try {

        if (isNaN(req.query.ManagerId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }


        let query = `SELECT distinct RSM.RoleId,UD.UserID, UD.EmployeeId, UD.UserName, UD.ManagerId, RM.RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  UD.IsActive=1 AND RSM.RoleId IN (13) and UD.ManagerId IN (SELECT item from dbo.fnSplit(@ManagerId, ','))`;


        // console.log(query);

        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: req.query.ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);


        res.send({
            status: 200,
            data: result.recordset
        });
        //let res = result.recordset;
        //return res;
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function SurveyQuestionMasterJoin(req, res) {
    try {

        // console.log(req.body)
        // console.log(req.query)

        let query = `SELECT  ISNULL(QuestionID,0) AS QuestionID,ISNULL(QuestionText,'') AS QuestionText,ISNULL(SurveyID,0) AS SurveyID,
        ISNULL(FieldType,'') AS FieldType,ISNULL(FieldOptions,'') AS FieldOptions,ISNULL(IsRequired,'') AS IsRequired,
        ISNULL(SNQM.SelectedOption,'') AS SelectedOption,ISNULL(SNQM.NestedQuestionId,0) AS NestedQuestionId 
        , CASE WHEN (SNQM2.NestedQuestionId IS NULL) THEN 0 ELSE 1 END AS IsNestedQuestion
        FROM MTX.SurveyQuestionMaster SQM (NOLOCK) 
        LEFT JOIN  MTX.SurveyNestedQuestionMaster SNQM (NOLOCK) ON SNQM.ParentQuestionId = SQM.QuestionID 
        LEFT JOIN  MTX.SurveyNestedQuestionMaster SNQM2 (NOLOCK) ON SNQM2.NestedQuestionId = SQM.QuestionID 
        WHERE SurveyID = @SurveyID ORDER BY 1 ASC`

        let sqlparam = [];
        sqlparam.push({ key: "SurveyID", value: req.query.SurveyID });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function rejectLeads(xlsData, userId) {
    var response = '';
    try {
        let sqlparam = [];
        sqlparam.push({ key: "LeadId", value: xlsData.LeadId });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "RejectionSet", value: xlsData.RejectionSet });
        sqlparam.push({ key: "Reason", value: xlsData.Reason ? (xlsData.Reason.trim()).substring(0, 100) : '' });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[RejectLeadByUploadProcess]", sqlparam);
        let status = Object.values(result.recordset[0]);
        response = status[0];
    }
    catch (e) {
        console.log(e.getMessage());
        response = e.getMessage();
    }
    finally {
        return response;
    }
}

exports.uploadLeadRejectionFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        // console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, async function (err) {
            if (err) {
                //throw err
                console.log(err)
            } else {
                // console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                let timestamp = Date.now();
                let filename = "LeadRejection/" + today + "/" + myFileName;

                xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                    var _result = [];
                    //var JsonExcel = {};
                    for (var i = 1; i in rows; i++) {
                        var obj = {};
                        for (j in rows[i]) {
                            obj[rows[0][j]] = rows[i][j];
                        }

                        //var innerObj = {};
                        obj['Status'] = await rejectLeads(obj, req.body.UserId);
                        // console.log(obj);
                        _result.push(obj);
                    }
                    res.send({
                        status: 200,
                        data: _result
                    });
                })


                /*let awsUploadResponse = uploadAWSfiles(filePath, filename, 'asterisk-log');
                console.log("awsresp",awsUploadResponse);
                awsUploadResponse.then(function (resultedkey) {
                    console.log('resultedkey',resultedkey);
                    var awsFileName = myFileName;
                    var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultedkey + '&bucket=asterisk-log';

                    xlsxFile(fs.createReadStream(`${myFile}`)).then((rows) => {
                        var _result = [];
                        //var JsonExcel = {};
                        for (var i = 1; i in rows; i++) {
                            var obj = {};
                            for (j in rows[i]) {
                                obj[rows[0][j]] = rows[i][j];
                            }
                            obj['Status'] = rejectLeads(obj, req.body.UserId);
                            //var innerObj = {};
                            _result.push(obj);
                        }
                        res.send({
                            status: 200,
                            data: _result
                        });
                    });
                });*/

            }
        })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UpdateUserGrades(xlsData, userId) {
    var response = '';
    try {
        let sqlparam = [];
        sqlparam.push({ key: "Grade", value: xlsData.Grade });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "EmployeeId", value: xlsData.EmployeeId });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[UpdateUserGradesByUpload]", sqlparam);
        let status = Object.values(result.recordset[0]);
        response = status[0];
    }
    catch (e) {
        console.log(e.getMessage());
        response = e.getMessage();
    }
    finally {
        return response;
    }
}

exports.uploadUserGradesFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        //console.log('appdir',appDir,req.body,req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, async function (err) {
            if (err) {
                //throw err
                console.log(err)
            } else {
                // console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                let timestamp = Date.now();
                let filename = "UploadUserGrade/" + today + "/" + myFileName;

                xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                    var _result = [];
                    //var JsonExcel = {};
                    for (var i = 1; i in rows; i++) {
                        var obj = {};
                        for (j in rows[i]) {
                            obj[rows[0][j]] = rows[i][j];
                        }

                        //var innerObj = {};
                        obj['Status'] = await UpdateUserGrades(obj, req.body.UserId);
                        // console.log(obj);
                        _result.push(obj);
                    }
                    res.send({
                        status: 200,
                        data: _result
                    });
                })
            }
        })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function FetchMenuMaster(req, res) {
    try {

        // console.log(req.body)
        // console.log(req.query)

        if (isNaN(req.query.UserId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }

        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: req.query.UserId });

        let query = `SELECT FM.* FROM [CRM].[UserMenuMap] UMM  JOIN CRM.FunctionsMaster FM ON FM.MenuId = UMM.MenuId  where UserId = @UserId and URL LIKE '%matrixdashboard%'`
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

exports.openSalesView = async function (req, res) {
    try {

        let sqlparam = [];
        sqlparam.push({ key: "LeadId", value: req.query.LeadId });

        let query = `SELECT CustomerId, ProductId FROM [MATRIX].[CRM].[LEADDETAILS150] where LeadId = @LeadId`
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

async function SetSlotDistribution(req, res) {
    try {

        let sqlparam = [];
        sqlparam.push({ key: "ManagerIds", value: req.query.ManagerIds });
        sqlparam.push({ key: "slotdate", value: new Date(req.query.slotdate) });
      
        let result = await sqlHelper.sqlProcedure("R", "[FOS].[GetFosSlotDistributionData]", sqlparam);

        // let query = `
        // DROP TABLE IF EXISTS #USERS  

	
        // DROP TABLE IF EXISTS #APPDATA 
    
        // CREATE TABLE #APPDATA 
        // (
        //     AppointmentID BIGINT, 
        //     LeadId BIGINT, 
        //     assignedTO BIGINT,
        //     EmployeeId VARCHAR(20), 
        //     UserName VARCHAR(100), 
        //     AppDateTime DATETIME,
        //     CreatedOn DATETIME,
        //     Source VARCHAR(100),
        //     SubstatusId SMALLINT,
        //     SubStatusName VARCHAR(100),
        //     CustomerId BIGINT,
        //     SlotId INT,
        //     ProductId INT,
        //     Address VARCHAR(500),
        //     Address1 VARCHAR(500),
        //     Pincode BIGINT,
        //     AppointmentType SMALLINT,
        //     AssignmentId SMALLINT,
        //     OfflineCityId SMALLINT,
        //     Comments VARCHAR(500),
        //     ZoneId INT,
        //     CreatedBy BIGINT, 
        //     NearBy VARCHAR(500),
        //     Landmark VARCHAR(500),
        //     PlaceId VARCHAR(500),
        //     CallDate DATETIME,
		// 	CallDateTTGT0 BIT,
        //     LastVisitedAgentId BIGINT,
        //     LastVisitedEmoloyeeId Varchar(20),
        //     LastVisitedEmoloyeeName Varchar(100),
        //     LastVisitDate DATETIME,
        //     LastVisitid Bigint
        // )
    
        // DECLARE @TBMANAGERIDS AS TABLE(MANAGERID BIGINT) 
     
        // IF @MANAGERIDS != '' 
        //     INSERT INTO @TBMANAGERIDS(MANAGERID) 
        //     SELECT ITEM FROM [DBO].[FNSPLIT](@MANAGERIDS,',') 
        
        //   CREATE TABLE #USERS 
        // ( 
        //     USERID BIGINT, 
        //     EMPLOYEEID VARCHAR(50), 
        //     USERNAME VARCHAR(100),
        //     MANAGERID BIGINT
        // ) 
    
        //  INSERT INTO #USERS 
        //         SELECT UD.USERID,UD.EMPLOYEEID, UD.USERNAME, UD.ManagerId 
        //         FROM CRM.USERDETAILS UD          (NOLOCK) 
        //         WHERE UD.ISACTIVE = 1  AND ManagerId IN (SELECT ITEM FROM [DBO].[FNSPLIT](@MANAGERIDS,',') )
    
        //  INSERT INTO #USERS
        //  (USERID,EMPLOYEEID,USERNAME,MANAGERID)
        //  VALUES
        //  (0,NULL,NULL,NULL)
    
        // INSERT INTO #APPDATA
        //     SELECT ASST.AppointmentID, 
        //     ASST.LeadId, 
        //     LD.assignedTO,
        //     UD.EmployeeId, 
        //     UD.UserName, 
        //     ASST.AppDateTime,
        //     ASST.CreatedOn,
        //     ASST.Source,
        //     ASST.SubstatusId,
        //     SSM.SubStatusName,
        //     AD.CustomerId,
        //     AD.SlotId,
        //     LD.ProductId,
        //     AD.Address,
        //     AD.Address1,
        //     AD.Pincode,
        //     AD.AppointmentType,
        //     AD.AssignmentId,
        //     AD.OfflineCityId,
        //     AD.Comments,
        //     AD.ZoneId,
        //     AD.CreatedBy,
        //     AD.NearBy,
        //     AD.Landmark,
        //     AD.PlaceId,
        //     --CDH.CallDate,
        //     --CallDateTTGT0 = case when cast(CDH.CALLDATE as date) BETWEEN CAST(@slotdate AS DATE) AND CAST(@slotdate+1 AS DATE) AND CDH.talktime > 0 then 1 ELSE 0 END,
        //     NULL,
        //     NULL,
        //     NULL,
        //     NULL,
        //     NULL,
        //     NULL,
        //     NULL
        // FROM MTX.AppointmentSubStatus ASST
        // INNER JOIN MTX.AppointmentData AD(NOLOCK) ON AD.ID = ASST.AppointmentID
        // INNER JOIN crm.SubstatusMaster SSM(NOLOCK) ON SSM.SubStatusID = ASST.SubstatusId  
        // INNER JOIN crm.leaddetails150 LD(NOLOCK) ON LD.LeadID = ASST.LeadID
        // LEFT JOIN crm.UserDetails UD(NOLOCK) ON UD.UserID = ld.assignedTO
        // --INNER JOIN MTX.CallDataHistory CDH (NOLOCK) ON LD.LeadID = CDH.LeadID AND CDH.CallDate  BETWEEN CAST(@slotdate AS DATE) AND CAST(@slotdate+1 AS DATE) 
        // WHERE ASST.AppDateTime BETWEEN CAST(@slotdate AS DATE) AND CAST(@slotdate+1 AS DATE)  AND ASST.isLastStatus=1 AND AD.AssignmentId <> 1
        // AND ISNULL(LD.assignedTO,0) IN  (SELECT UserId FROM #USERS) ORDER BY UD.UserName, SLOTID 


        
        // UPDATE 	APD
        //     SET APD.LastVisitid = A.id--,  APD.LastVisitedAgentId = A.Userid, APD.LastVisitedEmoloyeeId = A.EmployeeId, APD.LastVisitedEmoloyeeName = A.UserName, LastVisitDate = A.AppDateTime
        // FROM        #APPDATA APD 
        // INNER JOIN (
        //     Select MaX(ASST.Id) AS id --, ASST.Userid, UD.EmployeeId,UD.UserName,  ASST.AppDateTime
        //     , ASST.LeadID
        //     from
        //         MTX.AppointmentSubStatus ASST 
        //     Where ASST.isLastStatus = 1 AND ASST.SubstatusId = 2003 
        //     GROUP BY ASST.LeadId--, ASST.Userid,  UD.EmployeeId,UD.UserName, ASST.AppDateTime
        // )A ON A.LeadID = APD.LEADID 
            
        
        // UPDATE APD 
        // SET  APD.LastVisitedAgentId = ASST.Userid, APD.LastVisitedEmoloyeeId = UD.EmployeeId, APD.LastVisitedEmoloyeeName = UD.UserName, LastVisitDate = ASST.AppDateTime
        // FROM   #APPDATA APD 
        //     INNER JOIN  MTX.AppointmentSubStatus ASST (NOLOCK) ON ASST.LeadID = APD.LeadID
        //     INNER JOIN  CRM.UserDetails UD (NOLOCK) ON UD.UserID = ASST.UserId 
        //     WHERE ASST.Id = APD.LastVisitid

        // UPDATE APD 
        // SET  APD.CallDate = CDH.CallDate, APD.CallDateTTGT0 = case when cast(CDH.CALLDATE as date) BETWEEN CAST(@slotdate AS DATE) AND CAST(@slotdate+1 AS DATE) AND CDH.talktime > 0 then 1 ELSE 0 END
        // FROM   #APPDATA APD 
        //     INNER JOIN MTX.CallDataHistory CDH (NOLOCK) ON APD.LeadID = CDH.LeadID 
        //     WHERE CDH.CallDate  BETWEEN CAST(@slotdate AS DATE) AND CAST(@slotdate+1 AS DATE) 
            

        // --UPDATE APD
        // --	SET	APD.Mid = MaX(ASST.Id), APD.LastVisitedAgentId = ASST.Userid, APD.LastVisitedEmoloyeeId = UD.EmployeeId, APD.LastVisitedEmoloyeeName = UD.UserName, LastVisitDate = ASST.AppDateTime
        // --FROM        #APPDATA APD
        // --INNER JOIN  MTX.AppointmentSubStatus ASST (NOLOCK) ON ASST.LeadID = APD.LeadId
        // --INNER JOIN  CRM.UserDetails UD (NOLOCK) ON UD.UserID = ASST.UserId
        // --Where ASST.isLastStatus = 1 AND ASST.SubstatusId = 2003 
        // --GROUP BY APD.LeadId
        // --order by  ASST.AppDateTime desc

        // SELECT * FROM #APPDATA ORDER BY UserName, SlotId `


        // let result = await sqlHelper.sqlquery("R", query, sqlparam);
        return res.send({
            status: 200,
            data: result?.recordsets || []
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


async function GetNearestAgentToLead(req, res) {
    try {

        let sqlparam = [];
        // console.log('query', req.query)
        sqlparam.push({ key: "LeadId", value: req.query.LeadId });
        sqlparam.push({ key: "SlotId", value: req.query.SlotId });
        sqlparam.push({ key: "AppointmentDate", value: new Date(req.query.AppointmentDate) });

// console.log('sqlParam', sqlparam)
        let query = `  DECLARE 
		@FOSRegionName VARCHAR(100)

    SELECT      TOP 1 
			@FOSRegionName=FCGA.FOSRegionName                        
    FROM            MATRIX.CRM.LeadAssignDetails LAD (NOLOCK) 
    INNER JOIN      Matrix.CRM.Leaddetails150 LD (NOLOCK) ON LAD.leadID=LD.LeadID
    INNER JOIN      MTX.AppointmentData AD (NOLOCK) ON LAD.LeadID=AD.LeadID AND AD.IsActive=1 
    INNER JOIN      Master.FOS_CItyGroupMapping_Allocation FCGA (NOLOCK) ON AD.OfflineCityId=FCGA.CityID 
    AND             FCGA.ProductID =LD.ProductID AND FCGA.IsFOSAllocation=1     
    WHERE           LAD.LeadID =@LeadID 
    AND             LAD.IsLastAssigned=1
    

    DECLARE @AgentTable AS TABLE
    (
         [AgentID] [BIGINT] NULL,[AgentCode] [varchar](30) NULL,[UserName] [varchar](100) NULL,[GroupId] [int] NULL,[DailyLimit] [int] NULL,[BucketSize] [smallint] NULL
        ,[AssignedLeadsCount] [smallint] NULL,[OpenLeadCount] [int] NULL,[Grade] SMALLINT,[batchallocatedlead] TINYINT,PinCode BIGINT
        , ProcessID TINYINT,FOSRegionName VARCHAR(100),SlotCount INT
    )
        
    
    INSERT INTO @AgentTable 
    (
         [AgentID],[AgentCode],[UserName],[GroupId],[DailyLimit],[BucketSize],OpenLeadCount,Grade,[batchallocatedlead],PinCode,ProcessID,FOSRegionName,SlotCount
    )
    SELECT      DISTINCT ugrn.UserId AS UserID,UD.EmployeeId,UD.UserName,UGRN.GroupId,ud.limit,ud.BucketSize,ud.OpenLeadCount,ud.Grade,0,ud.PinCode,PGMA.ProcessID,FCGA.FOSRegionName,0
    FROM        CRM.UserGroupRoleMapNew AS UGRN WITH(NOLOCK) 
    INNER JOIN  CRM.UserDetails ud WITH(NOLOCK) ON ud.UserID=UGRN.UserId AND ud.IsActive=1 
    INNER JOIN  Master.FOS_CItyGroupMapping_Allocation FCGA (NOLOCK) ON UGRN.GroupId=FCGA.GroupID AND FCGA.IsFOSAllocation=1 AND FCGA.FOSRegionName=@FOSRegionName
    INNER JOIN  CRM.PermissionDetails PD (NOLOCK) ON UGRN.UserId=PD.UserId  AND PD.ProcessId IN (1,2) AND UGRN.RoleSuperId=11 --AND UGRN.AutoAllocation=1   
    INNER JOIN  MTX.ProductGrpMapping_Allocation PGMA (NOLOCK) ON UGRN.GroupID=PGMA.GroupID AND PGMA.ProcessID IN (6,8)
    WHERE       UD.EmployeeId NOT IN ('PH00727','PH00726','PI00720','PT00001','PT00002')

    /*Fetch Agent Slot Assignment */
    UPDATE  AT
    SET     AT.SlotCount=SlotAssignedCount
    FROM    @AgentTable AT
    INNER JOIN
    (
        SELECT       DISTINCT AT.AgentID AS UserID
                    ,SUM(CASE WHEN SlotId > 0 THEN 1 ELSE 0 END) SlotAssignedCount
        FROM         @AgentTable AT
        INNER  JOIN  MATRIX.CRM.LeadAssignDetails LAD (NOLOCK) ON AT.AgentID=LAD.AssignedToUserID AND LAD.IsLastAssigned=1
        INNER  JOIN  MTX.AppointmentData AD (NOLOCK) ON AD.LeadId=LAD.LeadID 
		INNER  JOIN  MTX.AppointmentSubStatus ASST (NOLOCK) ON AD.LeadId=ASST.LeadID 
		WHERE ASST.isLastStatus=1 
        AND          AD.AppointmentDateTime BETWEEN @AppointmentDate AND DATEADD(DAY,1,@AppointmentDate)
        AND          AD.SlotId=@SlotID
		AND			 ASST.SubstatusId NOT IN (2003,2004)
        GROUP BY     AT.AgentID
    ) A ON AT.AgentID=A.UserID


    SELECT     TOP(5)     AT.AgentID,AT.AgentCode,AT.UserName,AT.GroupId,AT.Grade,ISNULL(AT.SlotCount,0) SlotLeadCount,ACD.Distance AS Distance,  AT.DailyLimit                      
    FROM            @AgentTable AT  
    INNER JOIN      [FOS].[Agent_Customer_Distance] ACD ON AT.AgentID=ACD.UserID AND ACD.LeadID =@LeadId                                            
    LEFT JOIN       MTX.AgentRosterDetails ARD (NOLOCK) ON AT.AgentID=ARD.AgentID AND @AppointmentDate=ARD.OffDay AND ARD.IsActive=1    
    WHERE           AT.FOSRegionName=@FOSRegionName                 
    AND             ARD.AgentID IS NULL AND ISNULL(AT.SlotCount,0) < 2 and ACD.Distance < 20  ORDER BY ACD.Distance`


        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        // console.log('result', result?.recordsets);

        return res.status(200).json({
            status: 200,
            data: 'test' || []
        });
    }
    catch (e) {
        console.log('getnrearest', e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


async function createMongoHierarchy() {
    let AdminHierarchy = await GetTree(75);
    let Data = {
        UserID: "75",
        RoleName: "Admin",
        UserName: "Admin",
        children: AdminHierarchy,
        inserttime: new Date()
    }
    let obj75 = { Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: Data };
    CommonMethods.InsertDataMongo(obj75);
}

async function searchAdminHierarchy(AdminHierarchy, UserId) {
    let result = AdminHierarchy[0].children;
    console.log("AdminHiERARCHY IS ",AdminHierarchy[0].ProcessList);
    for (let i = 0; i < result.length; i++) {
        if (result[i].UserID == UserId) {
            result[i].inserttime = AdminHierarchy[0].inserttime;
            let obj = { Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: result[i] };
            await CommonMethods.InsertDataMongo(obj);
            return result[i];
        }
        if (result[i].hasOwnProperty('children')) {
            let ans = CommonMethods.IterateChildren(result[i].children, UserId);
            if (ans[0] == 1) {
                continue;
            }
            result = ans;
            result.inserttime = AdminHierarchy[0].inserttime;
            console.log("The result is ", result);
            let obj = { Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: result };
            await CommonMethods.InsertDataMongo(obj);
            return result;
        }
    }
}

async function searchAdminHierarchy2(AdminHierarchy, UserId) {
    let result = AdminHierarchy[0].children || [];
    console.log("AdminHiERARCHY IS ",AdminHierarchy[0].ProcessList);
    for (let i = 0; i < result.length; i++) {
        if (result[i].UserID == UserId) {
            result[i].inserttime = AdminHierarchy[0].inserttime;
            return result[i];
        }
        if (result[i].hasOwnProperty('children')) {
            let ans = CommonMethods.IterateChildren(result[i].children, UserId);
            if (ans[0] == 1) {
                continue;
            }
            result = ans;
            return result;
        }
    }
}


async function FetchManagerHierarchy2(req, res) {
    let UserId = null;
    if(parseInt(req.query?.Hierarchy)===1)
    {
        UserId = parseInt(req.query.ManagerId);
        // const {userId, roleId} = req.user || {};
        // // console.log("The role Id, UserId is ", roleId, userId);
        // UserId = roleId==2?75: userId;


    }
    else{
        const { userId } = req.user || {};
        UserId = userId;
    }
    if(UserId)
    {

    let UserParams = {
        Database: "MATRIX_DASHBOARD_DB",
        Collection: "ManagerHierarchyUserWise",
        Params: UserId
    }

    let AdminParams = {
        Database: "MATRIX_DASHBOARD_DB",
        Collection: "ManagerHierarchyUserWise",
        Params: 75
    };

    let AdminHierarchy = await CommonMethods.GetMongoData(AdminParams);
    let InsertedTime = Array.isArray(AdminHierarchy) && AdminHierarchy.length > 0 && AdminHierarchy[0].inserttime || null;
    if(UserId==75 && Array.isArray(AdminHierarchy) && AdminHierarchy.length > 0)
    {
       let data=AdminHierarchy[0].children;
       if(new Date()- InsertedTime>8*60*60*1000)
       {
        data=[{...data[data.length-1],expired:true}];
       }
       return res.status(200).json({
        status:200,
        data:data
       })
    }
    let UserHierarchy = await CommonMethods.GetMongoData(UserParams);
    if (Array.isArray(UserHierarchy) && UserHierarchy.length > 0) {
        if (new Date() - InsertedTime > 8 * 60 * 60 * 1000) {
            UserHierarchy = [{ ...UserHierarchy[0], expired: true }];

        }
        return res.status(200).json({
            status: 200,
            data: UserHierarchy
        })
    }

    if (Array.isArray(AdminHierarchy) && AdminHierarchy.length > 0) {
        let result = await searchAdminHierarchy(AdminHierarchy, UserId);
        let data=[];
        data.push(result);
        if (new Date() - InsertedTime > 8 * 60 * 60 * 1000) {
            data = [{ ...data[0], expired: true }];
        }
        return res.status(200).json({
            status: 200,
            data: data
        })
    }

    if(!InsertedTime) {
       
        await createMongoHierarchyV2();
        
        AdminHierarchy = await CommonMethods.GetMongoData(AdminParams);
        if(UserId==75 && Array.isArray(AdminHierarchy) && AdminHierarchy.length>0)
        {
            let data= AdminHierarchy[0].children;
            return res.status(200).json({
                status:200,
                data:data
            })
        }
        let result =await searchAdminHierarchy(AdminHierarchy, UserId);
        let data=[];
        data.push(result);
        return res.status(200).json({
            status: 200,
            data: data
        })
    }
    }
    else{
        return res.status(200).json({
            status:200,
            data:[]
        })
    }
}


async function CallHierarchy(req, res){
        let User = parseInt(req.user?.userId) || 0;
        
        let Role = (req.user?.roleId==2?75:User) || 0;
        let result =await CommonMethods.FindHierarchyToUse();
        if(result==1)
        {
            CommonMethods.InsertMongoLogData(User, Role);
            FetchManagerHierarchy2(req, res);
        }
        else{
            FetchManagerHierarchy(req,res);
        }
}

exports.getUserlistbyManagerId = async function (req, res) {
    try{
        let sqlparam = [];
        let Role = req.user?.roleId? req.user.roleId : 0;

        sqlparam.push({ key: "productId", value: req.body.productId });
        if(Role && Role !== 0 && [13,12].includes(Role)){
        sqlparam.push({ key: "managerId", value: req.user.userId });
        }else{
        sqlparam.push({ key: "managerId", value: req.body.SupervisorIds }); 
        }
        let result = await sqlHelper.sqlProcedure("L", "[mtx].[GetUserlistbyManagerIdTest]", sqlparam);
        const AWSSecretConfig = global.SecretConfig;
        let Aes_Encryption= AWSSecretConfig.Aes_Encryption;

        if(result?.recordset?.length > 0){
            //if(Aes_Encryption){
            // let Key = Aes_Encryption.MatrixSecretKey;
            // let Iv = Aes_Encryption.MatrixSecretIV;
            let Key = conf.MatrixSecretKey;
            let Iv = conf.MatrixSecretIV;  
            const modifiedData =  result.recordset.map(obj => {    
                const encryptedUserId = AesEncryption({ data: obj?.UserId+'&'+req.user?.userId, key: Key, iv: Iv });
                return {...obj, _tid: encodeURIComponent(encryptedUserId)};
            });
            result.recordsets = modifiedData;  
        //}
        }    
        return res.send({
            status: 200,
            data: result?.recordsets || [],
            info: {},
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}

exports.getDuplicateLeadsData = async function (req, res) {
    try{
        const { userId, token } = req.user || {};
        let sqlparam = [] , info = "";
        const UserInfo = Base64Encoding(JSON.stringify({ userId, token }));
        info = UserInfo;

        sqlparam.push({ key: "UserInput", value: req.body.UserInput });
        sqlparam.push({ key: "SearchType", value: req.body.SearchType });
        sqlparam.push({ key: "UserId", value: req.user.userId });
        let result = await sqlHelper.sqlProcedure("L", "[mtx].[GetDuplicateLeads]", sqlparam);  
        let hasInvalidMobile = false;
        if(result && result.recordsets && result.recordsets.length > 0)  {
             
            result.recordsets[0] = result.recordsets[0].map(lead => {
                if (lead.InvalidMobile) {
                    hasInvalidMobile = true; // Flag as invalid if `InvalidMobile` exists
                }
        
                return {
                    ...lead,
                    MobileNo: lead.MobileNo ? maskMobileNo(lead.MobileNo) : '', // Morph MobileNo if present
                    //InvalidMobile: lead.InvalidMobile ? maskMobileNo(lead.InvalidMobile) : '' // Morph InvalidMobile if present
                };
            });
        }
          // Check if there is any InvalidMobile and return error if true
            if (hasInvalidMobile) {
                return res.send({
                    errorCode: 400, // Example error code
                    message: "Invalid mobile number detected.",
                    data: result?.recordsets || [], // You can include the data for debugging purposes
                });
            }
        return res.send({
            status: 200,
            data: result?.recordsets || [],
            info: info,
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}

exports.getIsAllowedDuplicateSearch = async function (req, res) {
    try{
        const { userId } = req.user || {};
        let sqlparam = [] , info = "";
        let IsSearchAllowed = 0 , CustomerID = 0, IsNRICust = 0; 
        let Response = [];
        sqlparam.push({ key: "SearchText", value: req.body.SearchText });
        sqlparam.push({ key: "SearchType", value: req.body.SearchType });
        sqlparam.push({ key: "UserID", value: userId});
        sqlparam.push({ key: "RoleId", value: req.body.RoleId});
        sqlparam.push({ key: 'IsSearchAllowed', value: "", type: "out" });
        sqlparam.push({ key: 'CustomerIdOut', value: "", type: "out" });
        sqlparam.push({ key: 'IsNRICust', value: "", type: "out" });

        let result = await sqlHelper.sqlProcedure("L", "[mtx].[AllowedDuplicateLeadSearch]", sqlparam);  

        if (result && result.output) {
            IsSearchAllowed = result.output.IsSearchAllowed;
            CustomerID =  result.output.CustomerIdOut;
            IsNRICust = result.output.IsNRICust;
          }
        Response = [{IsSearchAllowed : IsSearchAllowed , CustomerID : CustomerID, IsNRICust : IsNRICust}]
        return res.send({
            status: 200,
            data: Response? Response : [],
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.IsUserAttributesAuthorized = async function (req, res) {
    try{
        const { userId } = req.user || {};
        let sqlparam = [];
        let Authorized = true; 
        let Response = {};
        sqlparam.push({ key: "UserId", value: userId});
        sqlparam.push({ key: "AttributeId", value: req.body.AttributeId});
        sqlparam.push({ key: 'Authorized', value: "", type: "out" });

        let result = await sqlHelper.sqlProcedure("L", "[mtx].[GetUserAuthorizedAttributesValue]", sqlparam);  

        if (result && result.output) {
            if(result.output.Authorized == '1'){
                Authorized = true;
            }
            else{
                Authorized = false;
            }
        }
        Response = {Authorized : Authorized}
        return res.send({
            status: 200,
            data: Response? Response : [],
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}

exports.getDuplicateLeadsDatabyProductId = async function (req, res) {
    try{
        const { userId, token } = req.user || {};
        let sqlparam = [] , info = "";
        const UserInfo = Base64Encoding(JSON.stringify({ userId, token }));
        info = UserInfo;

        sqlparam.push({ key: "UserInput", value: req.body.UserInput });
        sqlparam.push({ key: "SearchType", value: req.body.SearchType });
        sqlparam.push({ key: "UserId", value: req.user.userId });
        sqlparam.push({ key: "ProductId", value: req.body.ProductId });
        let result = await sqlHelper.sqlProcedure("L", "[mtx].[GetDuplicateLeadsByProductId]", sqlparam);  
        let hasInvalidMobile = false;
        if(result && result.recordsets && result.recordsets.length > 0)  {
             
            result.recordsets[0] = result.recordsets[0].map(lead => {
                if (lead.InvalidMobile) {
                    hasInvalidMobile = true; // Flag as invalid if `InvalidMobile` exists
                }
        
                return {
                    ...lead,
                    MobileNo: lead.MobileNo ? maskMobileNo(lead.MobileNo) : '', // Morph MobileNo if present
                    //InvalidMobile: lead.InvalidMobile ? maskMobileNo(lead.InvalidMobile) : '' // Morph InvalidMobile if present
                };
            });
        }
          // Check if there is any InvalidMobile and return error if true
            if (hasInvalidMobile) {
                return res.send({
                    errorCode: 400, // Example error code
                    message: "Invalid mobile number detected.",
                    data: result?.recordsets || [], // You can include the data for debugging purposes
                });
            }
        return res.send({
            status: 200,
            data: result?.recordsets || [],
            info: info,
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}



exports.getPaymentOverDueCountByManagerId = async function (req, res) {
    try{
        let sqlparam = [];
        let Role = req.user?.roleId? req.user.roleId : 0;

        sqlparam.push({ key: "ProductID", value: req.body.ProductId });
        if(Role && Role !== 0 && [13,12].includes(Role)){
        sqlparam.push({ key: "ManagerId", value: req.user.userId });
        }else{
        sqlparam.push({ key: "ManagerId", value: req.body.SupervisorIds }); 
        }
        let result = await sqlHelper.sqlProcedure("R", "[mtx].[GetPaymentOverDueCountByManagerIdTest]", sqlparam);  
        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}


exports.GetAdditionalLDBulkUploadData = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "ProductId", value: req.body.ProductId });
        sqlparam.push({ key: "ProcessName", value: req.body.ProcessName });

        let result = await sqlHelper.sqlProcedure("L", "[mtx].[GetAdditionalLDBulkUploadData]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.GetUniqueGroups = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "ProductId", value: req.body.ProductId });
        sqlparam.push({ key: "AgentId", value: req.user.userId });

        let result = await sqlHelper.sqlProcedure("L", "[CRM].[GetUniqueGroups]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.FetchHealthRenewalPaymentLink = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "Type", value: req.body.Type });
        sqlparam.push({ key: "ID", value: req.body.ID });
        sqlparam.push({ key: "FromDate", value: req.body.FromDate });
        sqlparam.push({ key: "ToDate", value: req.body.ToDate });

        let result = await sqlHelper.sqlProcedure("L", "[mtx].[FetchHealthRenewalPaymentLink]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.SetHealthRenewalPaymentLink = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "Type", value: req.body.Type });
        sqlparam.push({ key: "ID", value: req.body.ID });
        sqlparam.push({ key: "L2Reason", value: req.body.L2Reason });
        sqlparam.push({ key: "L2ReasonId", value: req.body.L2ReasonId });
        sqlparam.push({ key: "CancellationReason", value: req.body.CancellationReason });

        let result = await sqlHelper.sqlProcedure("L", "[mtx].[SetHealthRenewalPaymentLink]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.UpdateRenewalFileStatus = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "UniqueId", value: req.body.UniqueId });
        sqlparam.push({ key: "Status", value: req.body.Status });
        sqlparam.push({ key: "Uploadedby", value: req.body.UploadedBy });


        let result = await sqlHelper.sqlProcedure("L", "[mtx].[UpdateStatusRenewalBulkUploadFile]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}
exports.GetStagedFileData = async function (req, res) {
    try{
        let sqlparam = [] ;

        sqlparam.push({ key: "UploadedBy", value: req.body.UploadedBy });
        sqlparam.push({ key: "Source", value: '' });
        sqlparam.push({ key: "TrackingId", value: req.body.UniqueId });


        let result = await sqlHelper.sqlProcedure("L", "[CRM].[GetRenewalUploadsData]", sqlparam);  

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            message: "Success"
        });
    }   
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }

}

exports.GetProcessList = async function (userId) {
    try {
            if (!userId) {
                throw new Error("Invalid User ID");
            }
           
            let AdminParams = {
                Database: "MATRIX_DASHBOARD_DB",
                Collection: "ManagerHierarchyUserWise",
                Params: 75
            };
            
            let AdminHierarchy = await CommonMethods.GetMongoData(AdminParams);
            let UserHierarchy=null;
            
            if (Array.isArray(AdminHierarchy) && AdminHierarchy.length > 0) {
              console.log("Process us ",AdminHierarchy[0].ProcessList)
              UserHierarchy = await searchAdminHierarchy2(AdminHierarchy, userId.toString());
              console.log("UserHierarchy", UserHierarchy);
            }        

            // Assuming processList are part of the UserHierarchy data
            const processList = UserHierarchy?.ProcessList || [];

            return {  processList: processList };
    } catch (e) {
            console.error("Error in GetProductAndProcessList:", e);
            return { processList: [] };
    }
};



const LoopThroughTheTreeToGetMetaData = (Tree, TLNodes,childNodes, nodesAtEachLevel, level, parentNode)=>{
    
    for(let i=0;i<Tree.length;i++)
    {

        let childNodes1  = childNodes.get(parentNode) || [];
        childNodes1.push(parseInt(Tree[i].UserID));

        childNodes.set(parentNode, childNodes1);

        let nodesAtThisLevel = nodesAtEachLevel.get(level) || [];
        nodesAtThisLevel.push(parseInt(Tree[i].UserID));
        nodesAtEachLevel.set(level,nodesAtThisLevel);


         if(!Tree[i].children)
         {
            TLNodes.push(parseInt(Tree[i].UserID));
         }
         else{
            LoopThroughTheTreeToGetMetaData(Tree[i].children, TLNodes, childNodes, nodesAtEachLevel, level+1, parseInt(Tree[i].UserID))  
         }
    }
}

const AddProcessListToTree=(Tree, ProcessIdMapping)=>{
    for(let i=0;i<Tree.length;i++)
    {
        
        Tree[i]['ProcessList']= ProcessIdMapping[Tree[i].UserID] ?ProcessIdMapping[Tree[i].UserID] : [];
        if(Tree[i].children)
        {
            AddProcessListToTree(Tree[i].children, ProcessIdMapping);
        }
        
    }
}


exports.createMongoHierarchyV2= async function() {

    try{
    let HierarchyId = 75;
    // console.log("The time1 is ", new Date());
    let AdminHierarchy = await GetTree(HierarchyId);

    // console.log("The time2 is ", new Date());
    //first I need to find all TLNodes and Fetch Their ProcessIds;
    //I also need to keep track of nodes at each level;
    //Then for all these nodes I also need to keep track of their child nodes
    //And from the highest level I need to go up and update the processIds

    let TLNodes =  [];
    let childNodes = new Map();
    let nodesAtEachLevel = new Map();

    let level = 1;

    nodesAtEachLevel.set(0,[HierarchyId]);
    LoopThroughTheTreeToGetMetaData(AdminHierarchy, TLNodes, childNodes, nodesAtEachLevel, level, HierarchyId);

    let ProcessIdMapping = {};

    // console.log("The new Date3 is ", new Date(), TLNodes.length);

    TLNodes.length> 0 &&

    await Promise.all(TLNodes.map(async (node) => {
    let sqlparam = [];
    sqlparam.push({ key: "ManagerId", value: node });

    let result = await sqlHelper.sqlProcedure("R", "[MTX].[GetTLProcessId]", sqlparam);  

    // console.log("The result is ",result.recordset,node);
    let ProcessIds = [];

    Array.isArray(result?.recordset) && result.recordset.length>0 &&
    result.recordset.map((record)=>{
        if(record['ProcessID'])
        {
        ProcessIds.push(record['ProcessID']);
        }
    })
    ProcessIdMapping[node] = ProcessIds;
    }));   

    // console.log("The new Date 4 is ", new Date());
    let highestLevel = 1;
    nodesAtEachLevel.forEach((val, key)=>{
        if(key>highestLevel)
        {
            highestLevel = key;
        }
    })
    
    while(highestLevel>=0)
    {
        let nodes= nodesAtEachLevel.get(highestLevel);
        nodes.map((node)=>{
            
            let processIds = ProcessIdMapping[node] || [];
            let children = childNodes.get(node) || [];
            children.forEach((child)=>{
                processIds = [...processIds, ...ProcessIdMapping[child]];
            });

            ProcessIdMapping[node] = [...new Set(processIds)]
            
        })
        highestLevel =highestLevel- 1;
        
    }

    // console.log("The procesidMapping is ", ProcessIdMapping);

    // console.log("The new Date5 is ",new Date());

    AddProcessListToTree(AdminHierarchy,ProcessIdMapping);

    // console.log("The new DATE6 IS ", new Date());

    let Data = {
        UserID: String(HierarchyId),
        RoleName: "Admin",
        UserName: "Admin",
        children: AdminHierarchy,
        ProcessList: ProcessIdMapping[String(HierarchyId)] || [],
        inserttime: new Date()
    }
    let obj75 = { Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: Data };
    await CommonMethods.InsertDataMongo(obj75);
    return;

    // console.log("The AdminHierarchy is ",Data);
    // return Data;
    // let Data = {
    //     UserID: "75",
    //     RoleName: "Admin",
    //     UserName: "Admin",
    //     children: AdminHierarchy,
    //     inserttime: new Date()
    // }
   
    // let obj75 = { Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: Data };
    // CommonMethods.InsertDataMongo(obj75);
}
catch(e)
{
    console.log("The error in createMongoHierarchyV2 method is ", e);
    return;
    
}
}
