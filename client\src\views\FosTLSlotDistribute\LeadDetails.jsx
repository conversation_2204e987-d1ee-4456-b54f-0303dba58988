import TogglePopup from './TogglePopup'
import moment from 'moment';
import { Col, Dropdown, Form, Modal, Row, Button } from 'react-bootstrap';
import { TimeConversion, getuser } from '../../utility/utility.jsx';

const LeadDetails = (props) => {

    const { Rescheduled, AppointmentSlots, Details, slotidChange, activeTab, AppointmentDate,
    dateObj, AppDate, hours } = props

    // class="fas fa-phone-volume"
    return <>
        <ul>
            <li>Lead ID </li><li><b>{Details.LeadId}</b></li>
            <li>Current Status </li><li><b>{Details.SubStatusName}</b></li>
            <li>Current Advisor </li><li><b>{Details.UserName ? Details.UserName : "-"}</b></li>
            {/* <li>Last Advisor Visited</li><li><b>{Details.LastVisitedEmoloyeeName?Details.LastVisitedEmoloyeeName : "-" }</b></li>
            <li>Last Advisor Visit Date</li><li><b>{Details.LastVisitDate?moment(Details.LastVisitDate).format("YYYY-MM-DD HH:mm:ss") : "-" }</b></li> */}
            {Details.LastVisitedEmoloyeeName && <p>{Details.LastVisitedEmoloyeeName} has last visited on {moment(Details.LastVisitDate).format("DD MMMM")}</p>}
            <li>Appointment Slot</li><li> <Row className="justify-content-center">
                <Col xs={5} sm={5} className="pd-0">
                    <Form.Select aria-label="Default select example" onChange={AppointmentDate}>
                        {/* <option>8 Mar 2023</option>
                            <option value="1">8 Mar 2023</option>
                            <option value="2">8 Mar 2023</option> */}
                        {/* <option value="0">SELECT</option>  */}
                        {dateObj && dateObj.length > 0 && dateObj.map((date) => {
                            return (
                                <option value={date} selected={moment.utc(Details.AppDateTime).local(true).format("YYYY-MM-DD")==date}>{moment(date).format("DD MMM YYYY")}</option>
                            )
                        })
                        }

                        {/* {activeTab == 2 && <option value="1">{moment().format("DD MMM YYYY")}</option>}
                        <option value="2">{moment().add(1, 'days').format("DD MMM YYYY")}</option> */}

                    </Form.Select>
                </Col>
                <Col xs={7} sm={7}>
                    <Form.Select aria-label="Default select example" onChange={slotidChange}>

                        {/* <option value="1">8:00 AM - 10:00 AM</option>
                            <option value="2">10:00 PM - 12:00 PM</option>
                            <option value="3">12:00 PM - 2:00 PM</option>
                            <option value="4">2:00 PM - 4:00 PM</option>
                            <option value="5">4:00 PM - 6:00 PM</option>
                            <option value="6">6:00 PM - 8:00 PM</option>
                            <option value="7">8:00 PM - 10:00 PM</option> */}

                        {
                            AppointmentSlots && Array.isArray(AppointmentSlots) && AppointmentSlots.length > 0 && AppointmentSlots.map((slots, index) => {

                                // if (activeTab <= 2) {
                                    // If ActiveTab Today And AppointmentDate selected for Today
                                    if (AppDate == moment().format("YYYY-MM-DD") || AppDate == moment().subtract(1, 'days').format("YYYY-MM-DD")) {

                                        if (hours < slots.StartTime) {
                                            return (
                                                <option value={index} selected={Details.SlotId == index + 1}>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</option>
                                            )
                                        }
                                        else if (AppointmentSlots[AppointmentSlots.length - 1].StartTime < hours) {
                                            if (Details.SlotId == index + 1) {
                                                return (
                                                    <option value={index} selected={Details.SlotId == index + 1}>{TimeConversion(AppointmentSlots[Details.SlotId - 1].StartTime)} - {TimeConversion(AppointmentSlots[Details.SlotId - 1].EndTime)}</option>
                                                )
                                            }
                                        }
                                    }
                                    // If ActiveTab Today And AppointmentDate selected for Tommorrow
                                    else {
                                        if(activeTab<=2){
                                            return (
                                                <option value={index}>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</option>
                                            )
                                        }
                                        else{
                                            return (
                                                        <option value={index} selected={Details.SlotId == index + 1}>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</option>
                                                    )
                                        }
                                        
                                    }
                                // }
                                // If ActiveTab Tommorrow And AppointmentDate selected for Tommorrow or any FutureDate
                                // else if (activeTab == 3) {
                                //     return (
                                //         <option value={index} selected={Details.SlotId == index + 1}>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</option>
                                //     )
                                // }
                            })
                        }

                    </Form.Select>
                </Col>
                <Col xs={1} sm={1}> </Col>
            </Row></li>
        </ul>
        
    </>
}


export default LeadDetails;