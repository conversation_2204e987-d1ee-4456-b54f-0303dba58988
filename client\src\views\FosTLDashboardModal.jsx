
import React from "react";
import { useState, useEffect } from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { connect } from "react-redux";
import { If, Then } from 'react-if';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";

import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { hhmmss } from "utility/utility";
import moment from 'moment';
import DateRange from "./Common/DateRange"
import PrintExcelColumn from "./Common/PrintExcelColumn";
import { getuser } from '../utility/utility.jsx';

const FosTLDashboardModal = (props) => {
  let item;
  console.log(props)
  const { columns, SelectedSupervisors } = props;
  let dateRangeRef = React.createRef();
  const [showModal, setshowModal] = useState(true);
  const [download, setDownload] = useState(false);
  const [dowloadFlag, setDowloadFlag] = useState(false);
  const [HistoryData, setHistoryData] = useState([]);
  const [nextDayData, setNextDayData] = useState([]);

  const [StartDate, setStartDate] = useState(moment().subtract(30, 'days').format("YYYY-MM-DD"));
  const [EndDate, setEndDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));

  useEffect(() => {

  }, [])


  const downloadExcel = () => {
    setDownload(false)
  }

  const GetHistoryData = () => {
    try {
      let that = this;
      setHistoryData([])
      setNextDayData([])
      setDowloadFlag(false)
      var SelectedSupervisor = SelectedSupervisors;
      const user = getuser();

      props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "GetFosTLDashboardData",
        params: [{ ManagerIds: SelectedSupervisor.join() }, { StartDate: StartDate },
        { EndDate: EndDate }]//90022 }]
      }, function (result) {

        if (result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0) {
          setHistoryData(result.data.data[0]);
          setDowloadFlag(true)
        }
      });
    }
    catch (ex) {
      console.log(ex)
    }
  }


  const GetNextDayData = () => {
    try {
      let that = this;
      setHistoryData([])
      setNextDayData([])
      setDowloadFlag(false)
      var SelectedSupervisor = SelectedSupervisors;
      const user = getuser();

      props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "GetAppointmentsTrackingNextDay",
        params: [{ ManagerIds: SelectedSupervisor.join() }]//90022 }]
      }, function (result) {

        if (result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0) {
          setNextDayData(result.data.data[0]);
          setDowloadFlag(true)
          
        }
      });
    }
    catch (ex) {
      console.log(ex)
    }
  }


  const handleStartDate = (StartDateValue) => {
    setStartDate(StartDateValue);
  }

  const handleEndDate = (EndDateValue) => {
    setEndDate(EndDateValue);
  }

  const DownloadData = () => {
    setDownload(true)
    GetHistoryData();
  }

  const DownloadNextDayData = () =>{
    setDownload(true)
    GetNextDayData();
  }


  const handleClose = () => {
    props.handleClose();
  }


  return (

    <div>

      <Modal show={showModal} size="lg" onHide={props.handleClose} >
        <Modal.Header closeButton>
          <Modal.Title>You can download past 30-day data of FOS RealTime Panel</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form >
            <Row>

              {/* <Form.Label><i>*</i>Select Agent To Assign</Form.Label> */}

              <DateRange days={30} FromDate={"Start Date "} ToDate={"End Date "}
                startDate={StartDate} endDate={EndDate} ref={dateRangeRef} onStartDate={handleStartDate} onEndDate={handleEndDate}
                EndDateFixed={true} LimitStartdate={30}>
              </DateRange>

              <Row>
                <Col>
                  <Button onClick={DownloadData} >Download</Button>
                  {dowloadFlag && <PrintExcelColumn columnNames={columns} download={download} downloadExcel={downloadExcel} downloadIcon={false}
                    data={(nextDayData && nextDayData.length > 0) ? nextDayData : []} />}

                </Col>

                
              </Row>

            </Row>
          </Form>
        </Modal.Body>
        <Modal.Body>
        <Form>
          <Row>
              <Form.Label><i>*</i>You can <span style={{fontWeight: "bold"}}>Download Next Day Data</span> <span style={{fontStyle: "Italic"}}>(This data may change as appointments gets Re-Scheduled in real time and new appointments may get added)</span></Form.Label>
                <Col>
                  <Button onClick={DownloadNextDayData} >Download NextDay Data</Button>
                  {dowloadFlag && <PrintExcelColumn columnNames={columns} download={download} downloadExcel={downloadExcel} downloadIcon={false}
                    data={(HistoryData && HistoryData.length > 0) ? HistoryData : []} />}

                </Col>
              </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>

          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>

        </Modal.Footer>
      </Modal>


    </div>

  );


}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonspData
  }
)(FosTLDashboardModal);


