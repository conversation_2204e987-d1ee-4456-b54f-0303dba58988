import React from "react";
// import Dropdown from "react-bootstrap/Dropdown";
import DropDown from "./DropDown";

import { Row, Col } from "reactstrap";

const TimePicker = (props) => {


  let hours = [];

  for (let i = 0; i < 24; i++) {
    hours.push({Display: i, id: i});
  }

  let minutes = [];

  for (let i = 0; i < 60; i++) {
    minutes.push({Display: i, id: i});
  }

  return (
    <>
      <div className="col-md-2 mt-2">
        <Row className="container" style={{ textAlign: "center" }}>
          <Col>{props.heading}</Col>
        </Row>
        <div style={{ display: "flex" }}>
          <Col>
            <DropDown firstoption="Hours" firstoptionValue="-1" items={hours} value={props.hours} onChange={props.hoursChange}></DropDown>
          </Col>
          <Col>
            <DropDown firstoption="Minutes" firstoptionValue="-1" items={minutes} value={props.minutes} onChange={props.minutesChange}></DropDown>
          </Col>
        </div>
      </div>
    </>
  );
};

export default TimePicker;
