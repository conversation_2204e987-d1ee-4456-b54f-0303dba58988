import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetCommonData, InsertData, UpdateData, GetCommonspData, AllocationFormData
} from "../store/actions/CommonAction";
import {
    addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from './Common/DataTableWithFilter';
import { OpenSalesView, OpenNewSalesView, GetJsonToArray, getUrlParameter, getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDownAgent from './Common/DropDownAgent';
import DropDownGroup from './Common/DropDownGroup';
import DropDown from './Common/DropDown';
import ModalFosAllocationPanel from './Common/ModalFosAllocationPanel';
import { Link } from 'react-router-dom'
import DateRange from "./Common/DateRange"
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'
import moment from 'moment';


const FosAllocationPanel = (props) => {
    
    
    const [root] = useState(["GetFosAllocationPanel"])
    const PageTitle = "FOS Assignment Panel"
    
    //const [StartDate,setStartDate] = useState('moment().format("YYYY-MM-DD")')
    let [rows, setRows] = useState([]);
    const [unassign, setunassign] = useState('0');
    //let [item, setitem] = useState([]);
    const [SelectedValue, setSelectedValue] = useState();
    const [Group, setGroup] = React.useState();
    const [Product, setProduct] = React.useState();
    const [startDate, setstartDate] = useState(moment().subtract(7, 'days').format("YYYY-MM-DD"));
    let [AssignLeads, setAssignLeads] = useState([])
    const [ModalOpen, setModalOpen] = useState(false)
    let dtRef = React.createRef();
    const [selectedFile,setselectedFile] = useState(null)
    const [addClass,setaddClass] = useState('btn btn-primary')
    const DownloadedFile ='/SampleExcelfiles/FosLeadAllocation.xlsx'
    let item = [];
    const [items,setitems] = useState()
    const [unassignleads,setunassignleads] = useState()
    const [endDate,setendDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));
    let dateRangeRef = React.createRef();

    const columnlist = [
        {
            name: 'LeadID',
            selector: 'LeadID',
            type: "string",
            width: '100px',
            cell: row => <div>
                <a href={OpenNewSalesView(row.CustomerId, row.LeadID, row.productID)} target="_blank">{row.LeadID} </a>

            </div>,
            editable: false,
            sortable: true,
        },
        {
            name: 'Customer Name',
            selector: 'Name',
            width: '150px',
            type: "string",
            editable: false,
            sortable: true,
        },
        {
            name: 'Select City',
            selector: 'City',
            width: '100px',
            type: "string",
            editable: false,
            sortable: true,
            width: '100px',
            searchable: true
        },
        {
            name: 'Pincode',
            selector: 'Pincode',
            type: "string",
            width: '100px',
            //sortable: true,
            searchable: true
        },

        {
            name: 'AssignmentType',
            selector: 'AssignmentType',
            type: "string",
            width: '120px',
            //sortable: true,
            searchable: true
        },
        {
            name: 'AppointmentType',
            selector: 'AppointmentType',
            type: "string",
            width: '150px',
            //sortable: true,
            searchable: true
        },
        {
            name: 'Appointment Date Time',
            selector: 'AppointmentDateTime',
            type: "datetime",
            cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            format:'YYYY-MM-DD HH:mm:ss',
            //width: '200px',
            searchable: true
        },
        {
            name: 'Created On',
            selector: 'Createdon',
            type: "datetime",
            cell: row => <div>{row.Createdon ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.Createdon}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            //width: '200px',
            format:'YYYY-MM-DD HH:mm:ss',
            //exportCell: ((row) => {row.Createdon ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.Createdon}</Moment> : "-"}),
            searchable: true
        },

        {
            name: 'Product Name',
            selector: 'ProductName',
            type: "string",
            sortable: false,
            width: '100px',
            //width: '200px',
            //searchable: true,
        },

        {
            name: 'AssignedTo',
            selector: 'EmployeeId',
            cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: 'Call Center Agent',
            selector: 'Call Center Agent',
            //cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: '1st Reporting',
            selector: '1st Reporting',
            //cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: '2nd Reporting',
            selector: '2nd Reporting',
            //cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        
    ];


    // useEffect(() => {
    //     const user = getuser();
    //     props.GetCommonspData({
    //         root:'GetFosAllocationPanel',
    //         c: "R",
    //         //8324{AppointmentDateTime: startDate}
    //         params: [{ ProductId: Product }, {AppointmentDateTime: startDate} ],
    //         //params: [{ Type: unassign }, { GroupId: Group }],
    //         sort: { ts: -1 },
    //     })

    //     if (!props.CommonData.isError) {

    //         GetData();
    //     }

    // }, [Product, startDate])


    useEffect(() => {
        const user = getuser();
        props.GetCommonspData({
            root: "Fos_FosAssignmentProducts",
            //cols: ["ProductID AS Id", "Product AS Display"],
        }, function (data) {
            if (data && data.data && data.data.data) {
                let item = data.data.data[0]
                setitems(item);
            }
        })

        if (!props.CommonData.isError) {

           GetData();
        }

    }, [Product, startDate, endDate])



    const GetData = () => {
        // if (AssignLeads.length > 0) {
        //     if (dtRef.current != null) {
        //         dtRef.current.handleClearRows();
        //     }

        //     setAssignLeads([])
        // }
        
        if(Product) {
            props.GetCommonspData({
                root:'GetFosAllocationPanel',
                c: "R",
                //8324{AppointmentDateTime: startDate}
                params: [{ ProductId: Product }, {AppointmentDateTime: startDate},{AppointmentEndDate: endDate}],
                //params: [{ Type: unassign }, { GroupId: Group }],
            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data[0]
                    setunassignleads(item)
                }
            })
        }

    }


    // const handleChange = (SelectedRows) => {
    //     AssignLeads = [];

    //     SelectedRows.map((r) => {
    //         AssignLeads.push(r);
    //     })
    //     setAssignLeads(AssignLeads);
    // };


    // const success = () => {
    //     toast("Saved Successfully", { type: 'success' })

    // }


    //Insert Data
    // const handleClick = async () => {
        
    //     const updatedrows = [...AssignLeads]

    //     if (AssignLeads.length > 0) {
    //         dtRef.current.handleClearRows();
    //         setAssignLeads([])
    //     }
        
    //     for (let i = 0; i < updatedrows.length; i++) {
    //         await props.GetCommonspData({
    //             root: "SetFosUnassignLeads",
    //             c: "R",

    //             params: [{ LeadID: updatedrows[i].LeadId }, { AssignedToUserID: SelectedValue }],
    //             cols: GetJsonToArray(columnlist, "name"),
    //             sort: { ts: -1 },
    //         })
    //     }

    //     // updatedrows.map(async(r) => {
            
    //     //     await props.GetCommonspData({
    //     //         root: "SetFosUnassignLeads",
    //     //         c: "R",
    //     //         //8324
    //     //         params: [{ LeadID: r.LeadId }, { AssignedToUserID: SelectedValue }],
    //     //         cols: GetJsonToArray(columnlist, "name"),
    //     //         sort: { ts: -1 },
    //     //     })
            
    //     // })



    //     setTimeout(()=> success() 
    //             , 200);

    //     setTimeout(()=> GetData() 
    //             , 2000);

    // };




    // const handleAssign = (event, LeadId) => {
    //     setModalOpen(true)

    // };



    // const AgentList = {
    //     config:
    //     {
    //         root: "GetFosAllocationAgent",
    //         //cols: ["UserId"],
    //         con: [{ "Isactive": 1 }],
    //         //state: false
    //     }

    // }



    // const GroupList = {
    //     config:
    //     {
    //         root: "GetFosGroups",
    //         //cols: ["UserId"],
    //         con: [{ "Isactive": 1 }],
    //         //state: false
    //     }

    // }


    const ProductList = {
        config:
        {
            root: "GetProductsDropDown",
            cols: ["ProductID AS Id", "Product AS Display"],
            //con: [{ "Id": 2 }],
        }

    }


    // const radioChange = (e) => {
    //     setunassign(e.currentTarget.value)

    //     props.GetCommonspData({
    //         root: "GetFosAllocationPanel",
    //         c: "R",
    //         //8324
    //         params: [{ Type: e.currentTarget.value }, { GroupId: Group }],
    //         cols: GetJsonToArray(columnlist, "name"),
    //         sort: { ts: -1 },
    //     })

    //     if (AssignLeads.length > 0) {
    //         dtRef.current.handleClearRows();
    //         setAssignLeads([])
    //     }

    // }


    // const GroupChange = (e) => {
    //     setGroup(e.target.value);
    // }

    const ProductChange = (e) => {
        setProduct(e.target.value);
    }

    
    const onFileChange = (event) => {
        // Update the state 
        setselectedFile(event.target.files[0])
    };

    const onFileUpload = (e) => {
      
        e.preventDefault();
        setaddClass('btn btn-primary fa fa-spinner');

        if (selectedFile == null) {
            toast("Please choose Excel File", { type: 'error' });
            setaddClass('btn btn-primary');
            return;
        }
        // Create an object of formData
        const User = getuser(); 
        var type =  getType();
        const formData = new FormData(); 
        // Update the formData object 
        formData.append( 
            "myFile",
            selectedFile,
            selectedFile.name,
        ); 
        formData.append('type', type);
         

        formData.append('UserId', User.UserID); 

        
        // Details of the uploaded file 
        //console.log(this.state.selectedFile);      
        // Request made to the backend api 
        // Send formData object 
        AllocationFormData(formData, function (results) {
            
            // document.getElementById('uploadbutton').innerHTML = 'Upload!';
            document.getElementById('files-upload').value = null;
            if (results.data.status == 200) {
                alert('File uploaded');
                setaddClass('btn btn-primary');
                setselectedFile(null);
                GetData() 
            } else {
                setselectedFile(null);
                toast(results.data.message, { type: 'error' });
                return;
            }
        });
    }


    const getType =() => {
        var loc = window.location.href;
        let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
        
        lastUrl = lastUrl.split('?');
        //console.log('lasturl',lastUrl[0]);
        return lastUrl[0];    
    }

    const renderDownloadFile = () => {
        if (DownloadedFile) {
            return  <Link style={{ fontSize: '14px' }} to={DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }
    
    const handleStartDate = (StartDateValue) => {
        setstartDate(StartDateValue);
        setendDate(moment(StartDateValue).add(8, 'days').format("YYYY-MM-DD"))
    }
    
    const handleEndDate = (EndDateValue) => {
        setendDate (EndDateValue);
    }

    //GetProductsDropDown
    // if(props.CommonData.GetFosAllocationPanel){
    //     item = props.CommonData.GetFosAllocationPanel[0];
    // }
    //ProductID
    //Product
    // if(props.CommonData.GetProductsDropDown){
    //     item = props.CommonData.GetProductsDropDown[0];
    // }

    return (
        <>

            <div className="content fosAgent" >
                <ToastContainer />

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                            <Row>
                                <Col md={5}>
                                    {/* <Row>
                                    <label>Please use the excel upload below to upload the assignment list or select leads from the below list and click on assign to manage the assignment.</label>
                                    </Row> */}
                                    <Form.Label><i>*</i> Product </Form.Label>

                                    {/* <DropDownGroup firstoption="Select" col={ProductList} onChange={ProductChange} >

                                    </DropDownGroup><br></br> */}

                                    <DropDown firstoption="Select" items={items} onChange={ProductChange} >

                                    </DropDown><br></br>

                                    {/* <DateRange 
                  startDate={StartDate} >
                  </DateRange> */}



                                    {/* <DropDownGroup firstoption="Select" col={GroupList} onChange={GroupChange} >

                                    </DropDownGroup> */}

                                    {/* <Form.Group >
                                        <input style={{ margin: '3px' }} type="radio" value="0" name="leads" checked={unassign === "0"} onChange={radioChange} /> All
                                        <input style={{ margin: '3px' }} type="radio" value="1" name="leads" checked={unassign === "1"} onChange={radioChange} /> Assigned
                                        <input style={{ margin: '3px' }} type="radio" value="2" name="leads" checked={unassign === "2"} onChange={radioChange} /> UnAssigned
                                    </Form.Group> */}

                                </Col>
                                

                                <Row>
                                
                                <DateRange days={7} FromDate= {"Appointment From"} ToDate= {"Appointment To"}
                                startDate={startDate} endDate={endDate} ref={dateRangeRef} onStartDate={handleStartDate} onEndDate={handleEndDate}>
                                </DateRange>
                  
                                </Row>
                                
                            </Row>
                                

                                
                                <Row>
                                    <Col md = {10}>
                                        <form onSubmit={onFileUpload}>
                                            
                                                <label for="files-upload">Upload Excel To Assign Leads</label>&ensp;
                                                <input type="file" id="files-upload" onChange={onFileChange} />
                                            
                                                <button type="submit" id="uploadbutton"  className={addClass}>Upload!</button>                    
                                            
                                        </form>
                                    </Col>
                                    
                                    {/* <Col md={1}>
                                        {Array.isArray(AssignLeads) && AssignLeads.length > 0 && <Button variant="primary" onClick={() => handleAssign()} >Assign</Button>}
                                    </Col> */}
                                </Row>
                                        
                               
                                {renderDownloadFile()} 
                                {/* {ModalOpen && <ModalFosAllocationPanel Group={Group} handleClick={handleClick} setSelectedValue={setSelectedValue} GetData={GetData}
                                    handleClose={() => {
                                        setModalOpen(false);
                                    }}
                                />

                                } */}

                                <DataTable
                                    columns={columnlist}
                                    //data={item}
                                    data = {unassignleads}
                                    //selectableRowsNoSelectAll={false}
                                    //selectableRows={true}
                                    //onSelectedRows={handleChange}
                                    printexcel={true}
                                    //ref={dtRef}
                                />

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(FosAllocationPanel);