import React, { useContext } from "react";
import { AppointmentsContext } from "../AppointmentsContext";
import {
    Box,
    Typography,
    Paper,
    Divider,
    Popper,
    ClickAwayListener,
    Button
} from "@mui/material";
import { Grid } from "@mui/material";


const InfoWindowCard = (props) => {

    const { handleClose, serving } = props;

    const AppointmentContext = useContext(AppointmentsContext);


    const DisplayCurrentStatus = (currentStatus) => {

        switch (currentStatus) {
            case 1:
                return <h5 className="Travelling">Advisor is Travelling</h5>
            case 2:
                return <h5 className="Meeting">Appointment in Progress (in meeting)</h5>
            case 3:
                return null
            case 4:
                return null
            case 5:
                return null
            case 6:
                return <h5 className="WaitingCustomer">Waiting at customer's place</h5>
            default:
                return null
        }

    }

    return (

        <div className="InfoWindowData">
            {/* <Paper elevation={3}> */}
            <Button
                onClick={handleClose}
                className="CloseButton"
            >
                &times;
            </Button>
            {serving &&
             AppointmentContext.currentStatus &&
             <>
             {
                 DisplayCurrentStatus(AppointmentContext.currentStatus)
             }
             </>   
            }
            {/* <h5 className="Meeting">Appointment in Progress (in meeting)</h5> */}
            {
                AppointmentContext.appointmentCard.map((appointment) => {
                    if (!appointment) return null;
                    return (
                        <Grid container spacing={0}>
                            <Grid item xs={12}>
                                <Typography>
                                    <span className="Key" >Lead ID: </span>
                                    <span
                                        className="Value"
                                    >
                                    {appointment.LeadId}
                                    </span>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <Typography>
                                    <span className="Key">Customer Name: </span>
                                    <span
                                        className="Value"
                                    >
                                        {appointment.CustomerName}
                                    </span>
                                </Typography>
                            </Grid>
                        </Grid>
                    )
                })
            }
            {/* </Paper> */}
        </div>
    )
}

export default InfoWindowCard;