
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import {
  GetCommonData, addRecord, UpdateData, DeleteData
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownListMongo';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { fnRenderfrmControl, fnDatatableCol, fnCleanData } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import moment from 'moment';


class JagStoryBoard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "JagStoryBoard",
      departmentId: "0",

      PageTitle: "Jag Story Board",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {}
    };
    this.handleRemove = this.handleRemove.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.selectedrow = {"empId": "", "story": "" }
    this.columnlist = [
      {
        name: "_id",
        label: "id",
        type: "hidden",
        hide: true,
      },
      {
        name: "empId",
        label: "Employee Id",
        type: "string",
        searchable: true,
      },
      {
        name: "empName",
        label: "Employee Name",
        type: "string",
        searchable: true,
      },
      {
        name: "designation",
        label: "Designation",
        type: "string",
        searchable: true,
      },
      {
        name: "story",
        label: "story",
        type: "textarea",
        width: "450px",
        editable: true,
      },
      {
        name: "CreatedOn",
        label: "CreatedOn",
        type: "datetime",
        hide: true
      },
      {
        name: "UpdatedOn",
        label: "UpdatedOn",
        type: "datetime",
        hide: true
      }
      
    ];
    let count = 0;
  }



  componentDidMount() {

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

    this.props.GetCommonData({
        limit: 10,
        skip: 0,
        root: this.state.root,
        cols: {},
        con: {},
        c: "M",
      });
  }



  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {

      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.setState({ store: nextProps.CommonData });
    }

  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => { if (window.confirm('Are you sure you wish to delete this record?')) this.handleRemove(row)}}><i className="fa fa-trash" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
  handleRemove(row){
    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    let id = formvalue["_id"];

    this.props.DeleteData({
      root: this.state.root,
      body: {_id: id},
      c: "M"
    }, function (data) {
      if(data.data.status === 200) 
        toast("Record deleted!", { type: 'success' });
      else 
        toast.error("Record could not be deleted"); 
    })

    setTimeout(function () {
      this.props.GetCommonData({
        root: this.state.root,
        cols: {},
        con: {},
        c: "M",
        });
    }.bind(this), 1000);

  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: {...this.selectedrow}, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {
    if (document.getElementsByName("frmJagStoryBoard").length > 0 &&
      document.getElementsByName("frmJagStoryBoard")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      console.log("formvalue", formvalue);
      this.fnCleanData(formvalue);
      let id = formvalue["_id"];
      delete formvalue["_id"]

      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);
        formvalue["UpdatedOn"] = moment().format("YYYY-MM-DD HH:mm:ss");

        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { _id: id },
          c: "M",

        }, function (data) {
          toast("Record Updated Successfully!", { type: 'success' });
        });



        // this.props.addRecord({
        //   root: "History",
        //   body: {
        //     module: "JagStoryBoardConfigure",
        //     od: this.state.od,
        //     nd: formvalue,
        //     ts: new Date(),
        //     by: getuser().UserId
        //   }
        // }, function (data) {
        //   toast("History Maintained!", { type: 'success' });
        // });
      } else{
        formvalue["CreatedOn"] = moment().format("YYYY-MM-DD HH:mm:ss");
        this.props.addRecord({
          root: this.state.root,
          body: formvalue
        }, function (data) {
          if(data.data.status === 400){
            toast.error(data.data.message) ; 
          } else
            toast("User Added Successfully!", { type: 'success' });
        });
      }

      setTimeout(function () {
        this.props.GetCommonData({
          root: this.state.root,
          cols: {},
          con: {},
          c: "M",
        });
      }.bind(this), 1000);

      this.setState({ showModal: false });
    }
    return false;
  }
  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.type === 'number' ? parseInt(e.target.value) : e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }


  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, event } = this.state;
    console.log("renderitems", items);
    // let departments = [];
    // departments.push(items);


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>

                    <Col md={2}>
                      <Button variant="primary" onClick={this.handleShow}>Add Story</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                    extention={true}
                    export={false}
                    print={false}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmJagStoryBoard">
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    addRecord,
    UpdateData,
    addRecord,
    DeleteData
  }
)(JagStoryBoard);