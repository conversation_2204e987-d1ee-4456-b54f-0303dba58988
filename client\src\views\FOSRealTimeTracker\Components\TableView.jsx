import React,{useContext, useEffect, useState} from 'react';

import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow} from '@mui/material';

import '../../../assets/scss/_tableview.scss';

import { ConvertToMinutes,  fetchDistance, sortByKey } from '../../../utility/utility'

import travellingStatusIcon from '../../../assets/icons/travellingStatus.svg';
import meetingStatusIcon from '../../../assets/icons/meetingStatus.svg';
import callingStatusIcon from '../../../assets/icons/callingStatus.svg';
import logoutStatusIcon from '../../../assets/icons/loggedoutStatus.svg';
import lunchStatusIcon from '../../../assets/icons/lunchStatus.svg';
import leadCallIcon from '../../../assets/icons/lead-call.svg';
import idleIcon from '../../../assets/icons/idle-icon.svg';
import waitingAtCX from  '../../../assets/icons/waitingAtCX.svg';
import { FosContext } from '../FosContext';
import moment from 'moment';
import StatusComponent from './StatusComponent';

  const TableView = () =>{

  const {fosData, selectedCounter}= useContext(FosContext);


  const [tableData, setTableData]= useState([]);

  useEffect(()=>{
   

    setTableData([]);
    
    if(fosData.size>0)
    {    
       
        let dataToSet=[];
        fosData.forEach((data, key)=>{
           if(!key || !data)
           {return null};
           if(selectedCounter!=0 && data['RealTimeStatusId']!=selectedCounter)
           {
             return null;
           }
          
           let objToAdd={};
           objToAdd['name']= <><em>{data.UserName? (data.UserName.length>27? data.UserName.substring(0,24)+"...":data.UserName) : "-"}</em><small className='ecode'>{data.EmployeeId? data.EmployeeId: null}</small><span></span></>;
           objToAdd['UserName'] = data.UserName
           // objToAdd['name']= <><em>{data.UserName? data.UserName : "-"}</em><span>{data.Location? "Base Location: "+data.Location : null }<small className='ecode'>{data.EmployeeId? data.EmployeeId: null}</small></span></>;
           if(String(data['RealTimeStatusId'])=='1')
           {
            objToAdd['status']=<><span className='icon'><img src={travellingStatusIcon} alt='Travelling' /> Travelling</span> {data['Since']  && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} <small>Distance travelled: <small className='text-right'>{fetchDistance(data['overallDistance'])}</small></small> <small>Away from CX: <small className='text-right'>{fetchDistance(data['distanceFromCustomer'])}</small></small>{data['SecondaryStatus']?(<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['SecondaryLeadId']!=null ?data['SecondaryLeadId']:"-"}</small></small>):null} </>;
            objToAdd['className']=''
           }
           else if(String(data['RealTimeStatusId'])=='2')
            {
                objToAdd['status']=<><span className='icon meeting'><img src={meetingStatusIcon} alt='Meeting' /> Meeting</span> {data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} {data['SecondaryStatus']?(<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['SecondaryLeadId']!=null ?data['SecondaryLeadId']:"-"}</small></small>):null}  </>;
                objToAdd['className']='meeting';
            }
            else if(String(data['RealTimeStatusId'])=='3')
            {
                objToAdd['status']=<><span className='icon calling'><img src={callingStatusIcon} alt='Calling'/>Calling</span>{data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} {<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['ParentId']!=null ?data['ParentId']:"-"}</small></small>}  </>;
                objToAdd['className']='calling';
            }
            else if(String(data['RealTimeStatusId'])=='4')
            {
                objToAdd['status'] =
                <>
                <span className='icon idle'><img src={idleIcon} alt='Idle'/>Idle</span>{data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>}
                 {/* {data.WaitingAtCustomer?<small className='green-text'>Advisor Waiting at Customer Location</small>:null}  */}
                 {data['SecondaryStatus']?(<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['SecondaryLeadId']!=null ?data['SecondaryLeadId']:"-"}</small></small>):null} 
                 </>;
                objToAdd['className']='idle';
            }
            else if(String(data['RealTimeStatusId'])=='5')
            {
                
                if(data['LogoutType'] == 4){
                    objToAdd['status']=<><span className='icon lunch'><img src={lunchStatusIcon} alt='LoggedOut'/>Lunch</span></>;
                    objToAdd['className']='loggedoff';

                }else{
                objToAdd['status']=<><span className='icon loggedout'><img src={logoutStatusIcon} alt='LoggedOut'/>Logged Off</span>{data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} {data['SecondaryStatus']?(<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['SecondaryLeadId']!=null ?data['SecondaryLeadId']:"-"}</small></small>):null}</>;
                objToAdd['className']='loggedoff';
                }
            }
            else if(String(data['RealTimeStatusId'])=='6')
            {
                objToAdd['status']=<><span className='icon'><img src={waitingAtCX} alt='Waiting at CX'/>Waiting at CX</span>{data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} {data['SecondaryStatus']?(<small className='green-text'><img src={leadCallIcon} /> Calling on lead ID <small className='text-right'> {data['SecondaryLeadId']!=null ?data['SecondaryLeadId']:"-"}</small></small>):null}</>;
                objToAdd['className']='';
            }
            else{
                objToAdd['status']=<><span className='icon idle'><img src={idleIcon} alt='Unknown'/>Unknown Status</span>{data['Since'] && <small>Since: <small className='text-right'>{data['Since']['time']+' '+data['Since']['den']}</small></small>} </>;
                objToAdd['className']='idle';
                
            }
          


           objToAdd['leadId']=[1,2,3,4,6].includes(data['RealTimeStatusId'])? <><em>{data.CustomerName? (data.CustomerName.length>27?data.CustomerName.substring(0,24)+"...":data.CustomerName):"-"}</em><strong>{data.ParentId? data.ParentId : null}</strong></>:null;
           objToAdd['idleTime']= ConvertToMinutes(data.idleTime);


           if(new Date(data.UpdatedOn).getUTCDate()== new Date().getUTCDate())
           {
           objToAdd['totalAttempts']= (data.hasOwnProperty('TTAttempts') && data.TTAttempts!=null)? String(data.TTAttempts):"-" ;
           objToAdd['OTS'] =data.OTSValue!=null && !isNaN(data.OTSValue) ? parseInt(data.OTSValue) + "%" : '-';
           objToAdd['talktime']= ConvertToMinutes(data.TTTalkTime) ;
           objToAdd['lastAttempt'] = data.LastAttempt? (new Date().getUTCFullYear()==new Date(data.LastAttempt).getUTCFullYear() && new Date().getUTCDate()==new Date(data.LastAttempt).getUTCDate()? moment(data.LastAttempt).format('hh:mm:ss A'):"-"):"-";
           objToAdd['verifiedVisits']= data.OTPVerified!==null?data.OTPVerified:"-";
           }
           else{
            objToAdd['OTS'] =  '-';
            objToAdd['totalAttempts']= "-";
           objToAdd['talktime']= "-" ;
           objToAdd['lastAttempt'] = "-"
           objToAdd['verifiedVisits']= "-";

           }
           objToAdd['StatusChangedOn'] = data.StatusChangedOn;
           
           dataToSet.push(objToAdd);

        })

        // console.log("The dataToSer ", dataToSet)
        sortByKey(dataToSet, 'UserName')

        // sortByKey(dataToSet, 'StatusChangedOn')
        
       
        setTableData(dataToSet);
    }

  },[fosData, selectedCounter]) 

return(
    <div className='tableview'>
        <div className='advisor-table-list'>
            <TableContainer>
                <Table stickyHeader aria-label="sticky table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Advisor name</TableCell>
                            <TableCell>Working leadID</TableCell>
                            {/* <TableCell>Total idle time</TableCell> */}
                            <TableCell className='green-bg work-done' colSpan={4}>
                                FOS Vital Metrics
                                <TableCell>Total attempts</TableCell>
                                <TableCell>Total talktime</TableCell>
                                {/* <TableCell>Last attempt</TableCell> */}
                                <TableCell>OTS</TableCell>
                                <TableCell>Verified visits</TableCell>
                            </TableCell>
                            </TableRow>
                    </TableHead>
                    <TableBody>

                    {
                        tableData.length>0 && tableData.map((row, leadId)=>{
                            // console.log("The data ", row)
                            return(
                                <>
                            <TableRow key={leadId}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.leadId}</TableCell>
                                {/* <TableCell>{row.idleTime}</TableCell> */}
                                <TableCell className='inner-details' >
                                    <TableCell>{row.totalAttempts}</TableCell>
                                    <TableCell>{row.talktime}</TableCell>
                                    {/* <TableCell>{row.lastAttempt}</TableCell> */}
                                    <TableCell>{row.OTS}</TableCell>
                                    <TableCell>{row.verifiedVisits}</TableCell>
                                </TableCell>
                            </TableRow>
                            <StatusComponent Status={row.status} ClassName={row.className?row.className:''}/>
                        </>
                            )
                        })
                    }
                    </TableBody>
                </Table>
            </TableContainer>
        </div>
    </div>
)
}

export default TableView;