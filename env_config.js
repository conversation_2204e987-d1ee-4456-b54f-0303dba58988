const qa = {
    PORT: 80,
    AGENT_CTC_BASE_URL: "https://hrmsqa.policybazaar.com/HRMS-Emp/api/EmpExt/GetAgentCTCDetails",
    AGENT_CTC_TOKEN: "g4cjZpWJEf1BntHoldTXkA==",
    CTC_INCENTIVE_URL: "https://incentiveapiqa.policybazaar.com/api/Incentive/DecryptData",
    AWS_REGION: "ap-south-1",
    AWS_CREDENTIALS_URL: 'http://169.254.169.254/latest/meta-data/iam/security-credentials/matrixlive-api-role',
    dialerApiUrlV2: "https://dialerqaapi.policybazaar.com/api/v2/",
    BMS_SEND_COMMUNICATION_URL: 'https://pbqaserviceapi.policybazaar.com/sendcommunication',
    PB_SERVICE_TOKEN: "B5BED94D-9197-45CF-83D5-2534A1A8FEFD",
    BMSTicket: "https://ticketservice.policybazaar.com/",
    BMSToken: "630496192d0c283e0ed93838",
    BMSApp: "MATRIX",
    BMSContent: "application/json",
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    VERIFY_TOKEN_MTX: 'https://internalcustomernotificationqa.policybazaar.com/customer/verifytoken/',
    VERIFY_TOKEN_BMS: 'https://bmsv2testservice.policybazaar.com/api/Utility/CheckUserLogin',
    VERIFY_TOKEN_DIALER: 'https://dialerapi.policybazaar.com/api/v2/dialer/validateToken',
    VERIFY_TOKEN_CLAIM: 'https://claimqaapi.policybazaar.com/Login/IsValidAgent',
    UUID_MTX: 'MatrixToken',
    UUID_BMS: 'bmsv2TokenQA',
    UUID_DIALER: 'd7f080c7-e134-459b-967d-389b71a9396f',
    UUID_CLAIM: 'ValidationKey',
    AGENTID_DIALER: 'c7fc71cf-2f0e-4da7-b0a3-72d285b0c2a6',
    AGENTID_CLAIM: 'ClaimEmployeeId',
    SOURCE_KEY_CLAIM: 'Dialer',
    VALIDATION_KEY_CLAIM: 'db3d8188-b687-49d3-a713-f33493ef4459',
    MATRIXCOREAPI: 'https://qainternalmatrixapi.policybazaar.com',
    REGISTER_CUSTOMER: 'https://apiqa.policybazaar.com/cs/customer/registerCustomer',
    REGISTER_CUST_AUTHKEY: 'fw03VZNVHPln4VS94QljBt31il7LbrhmsBCGTkeI',
    REGISTER_CUST_CLIENTKEY: 'rYAhR07qvs',
    Dialer_clientKey: '3ac2d66b',
    Dialer_source: 'matrix',
    Dialer_authKey: '70ab0476-0a6b-4b49-9ffe-a75618de4116',
    AUTO_PAY_SECRET_KEY: '********************************************',
    AUTO_PAY_SECRET_IV: 'S0d4bUhSaXZqeXNvSG1aUQ==',
    AUTO_PAY_AUTH: 'gMbsh35hgfhfhHGf122mFBcy2341XIw',
    AUTO_PAY_MID: 'HA4K3',
    AUTO_PAY_API: 'https://pgqa.policybazaar.com/pgi/si/autopayInfo',
    BMS_APP_SERVICE_V2_ID: 'Matrix1.0',
    BMS_APP_SERVICE_V2_KEY: '35E7A413-05D8-45EB-9A86-1074E973ECC4',
    BMS_DOC_POINT_AUTH_HEADER: 'UG9saWN5 YmF6YWFy',
    BMS_DOC_POINT_API: 'https://docpointqa.policybazaar.com/coreapi/api/RedirectToDocpoint',
    BMS_VERIFICATION_STATUS_URL: 'https://bmsappservcice2qa.policybazaar.com/api/Partner/InsertUpdateVerificationStatus',
    AWS_SQS_LOGGING: '',
    ADD_LEAD_TO_PRIORITYQUEUE: 'https://progressiveqa.policybazaar.com/communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
    AWS_ENV_CONFIG_SECRET: "arn:aws:secretsmanager:ap-south-1:721537467949:secret:QA_MatrixDashboard-Cf0Tf8",
    Comm_Api : "https://progressiveqa.policybazaar.com/Communication/",
    INTERNAL_DOMAIN_COOKIE: 'cdomain',
    INTERNAL_DOMAIN_VALUE: 'progressiveqa.policybazaar.com',
    MATRIX_DASHBOARD_BASEURL: 'https://matrixdashboardqa.policybazaar.com',
    BMS_SERVICE_URL: 'https://bmsappservcice2qa.policybazaar.com/',
    MATRIX_CORE_URL: 'https://qamatrixcoreapi.policybazaar.com',

    /* HEADER, CREDS AND TPTOKENS*/
    PB_SERVICE_TOKEN : "B5BED94D-9197-45CF-83D5-2534A1A8FEFD",
    GET_VERIFICATION_CAMPAIGN: 'https://qamatrixcoreapi.policybazaar.com/api/Bms/GetLeadCallTransferInfo',
    INTERNAL_MATRIX_API_CLIENTKEY: 'L6YWNav',
    INTERNAL_MATRIX_API_AUTHKEY: 'LGsaWLYmF6YWNav',
    CORESERVICES_ENCRYPTIONKEY  :"ctTqALVPKufStTGOiZ5sdtBBldLxK3XI",
    CORESERVICES_ENCRYPTIONSALT :"hB6ZaONtUrpJNDJN",
    CORESERVICES_OPTFORWA_URL: "https://apiqa.policybazaar.com/cs/customer/getPrefComm/v2?",
    CORESERVICES_CLIENTKEY:"Gqc7die92q69",
    CORESERVICES_AUTHKEY: "5pcBUK9PUbBlsOte7i4C099yfU0iN5KmSpsPdwp1",
    BMS_CROMA_URL: "https://bmscromaqaapi.policybazaar.com/",
    BMSCROMA_AUTHKEY: "a63phurQkK7P2QZmTZS",
    BMSCROMA_CLIENTKEY: "qJHjrnvOSI",
    CALLING_STATUS_URL:"https://internalagenttrackerqa.policybazaar.com/agentstatus/RealTimeAgentStatus?managercode=",
    IncentiveCriteriaUrl: 'matrixinternal/QA/IncentiveCriteria/'
};

const prod = {
    PORT: 80,
    AGENT_CTC_BASE_URL: "https://hrmsqa.policybazaar.com/HRMS-Emp/api/EmpExt/GetAgentCTCDetails",
    AGENT_CTC_TOKEN: "g4cjZpWJEf1BntHoldTXkA==",
    CTC_INCENTIVE_URL: "https://incentiveapi.policybazaar.com/api/Incentive/DecryptData",
    AWS_REGION: "ap-south-1",
    AWS_CREDENTIALS_URL: 'http://169.254.169.254/latest/meta-data/iam/security-credentials/matrixlive-api-role',
    dialerApiUrlV1: "http://dialerapi.policybazaar.com/api/dialer/",
    dialerApiUrlV2: "https://dialerapi.policybazaar.com/api/v2/",
    BMSTicket: "https://ticketservice.policybazaar.com/",
    BMSContent: "application/json",
    BMSToken: "cRfUjXn2r5u8xADGKaPdSgVkYp3s",
    BMSApp: "Matrix",
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    BMSToken: "YmU1OGZmMjAtMDExOC00NDc0LTk4NTgtNGIyZTQyODcwMzhlfjI3NDE=",
    BMSApp: "MATRIX",
    VERIFY_TOKEN_MTX: 'https://internalcustomernotification.policybazaar.com/customer/verifytoken/',
    VERIFY_TOKEN_BMS: 'https://bmsappservice.policybazaar.com/api/Utility/CheckUserLogin',
    VERIFY_TOKEN_DIALER: 'https://dialerapi.policybazaar.com/api/v2/dialer/validateToken',
    VERIFY_TOKEN_CLAIM: 'https://claimapi.policybazaar.com/Login/IsValidAgent',
    UUID_MTX: 'MatrixToken',
    UUID_BMS: 'bmsv2Token',
    UUID_DIALER: 'd7f080c7-e134-459b-967d-389b71a9396f',
    UUID_CLAIM: 'ValidationKey',
    SOURCE_KEY_CLAIM: 'Dialer',
    VALIDATION_KEY_CLAIM: 'db3d8188-b687-49d3-a713-f33493ef4459',
    AGENTID_DIALER: 'c7fc71cf-2f0e-4da7-b0a3-72d285b0c2a6',
    AGENTID_CLAIM: 'ClaimEmployeeId',
    MATRIXCOREAPI: 'https://internalmatrixapi.policybazaar.com',
    REGISTER_CUSTOMER: 'https://api.policybazaar.com/cs/customer/registerCustomer',
    REGISTER_CUST_AUTHKEY: 'nlGpH1UxWGzIJfRXXFlyYPtf814HETXWX09tahQJ',
    REGISTER_CUST_CLIENTKEY: 'OlWfN8v7wF',
    Dialer_clientKey: '3ac2d66b',
    Dialer_source: 'matrix',
    Dialer_authKey: '70ab0476-0a6b-4b49-9ffe-a75618de4116',
    AUTO_PAY_SECRET_KEY: '********************************************',
    AUTO_PAY_SECRET_IV: 'MDAzdUpoak9PQmh1cjAwMw==',
    AUTO_PAY_AUTH: 'gFH8gPsdfg4Fh3555FJGfgfdf67ehhHfg',
    AUTO_PAY_MID: 'MTX35dgXhJ',
    AUTO_PAY_API: 'https://pg.policybazaar.com/pgi/si/autopayInfo',
    BMS_APP_SERVICE_V2_ID: 'Matrix1.0',
    BMS_APP_SERVICE_V2_KEY: '35E7A413-05D8-45EB-9A86-1074E973ECC4',
    BMS_DOC_POINT_AUTH_HEADER: 'TWF0cml4Xy9Qb2xpY3ktL0JhemFhci0t',
    BMS_DOC_POINT_API: 'https://docpoint.policybazaar.com/coreapi/api/RedirectToDocpoint',
    AWS_SQS_LOGGING: 'https://sqs.ap-south-1.amazonaws.com/721537467949/LoggingQueue',
    ADD_LEAD_TO_PRIORITYQUEUE: 'https://matrixliveapi.policybazaar.com/communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
    AWS_ENV_CONFIG_SECRET: "arn:aws:secretsmanager:ap-south-1:721537467949:secret:Prod_MatrixDashboard-DYjpH3",
    Comm_Api : "https://matrixliveapi.policybazaar.com/Communication/",
    INTERNAL_DOMAIN_COOKIE: 'cdomain',
    INTERNAL_DOMAIN_VALUE: 'mobilematrix.policybazaar.com',
    MATRIX_DASHBOARD_BASEURL: 'https://matrixdashboard.policybazaar.com',
    SlOTAUTHKEY: "wSAyuE8Di",
    SlOTCLIENTKEY: "LAyuivekgFbsh9bI",       
    BMS_SERVICE_URL: 'https://bmsappservice2.policybazaar.com/',
    MATRIX_CORE_URL: 'https://matrixcoreapi.policybazaar.com',
    BMS_SEND_COMMUNICATION_URL: 'https://pbserviceapi.policybazaar.com/sendcommunication',

     /* HEADER, CREDS AND TPTOKENS*/
     PB_SERVICE_TOKEN : "B5BED94D-9197-45CF-83D5-2534A1A8FEFD",
    GET_VERIFICATION_CAMPAIGN: 'https://matrixcoreapi.policybazaar.com/api/Bms/GetLeadCallTransferInfo',
    INTERNAL_MATRIX_API_CLIENTKEY: 'L6YWNav',
    INTERNAL_MATRIX_API_AUTHKEY: 'LGsaWLYmF6YWNav',
    CORESERVICES_ENCRYPTIONKEY  :"ykCWNg3bAF4gclxnqg488MMUXq1sNLC1",
    CORESERVICES_ENCRYPTIONSALT :"cDQuHoWerG95oH3o",
    CORESERVICES_OPTFORWA_URL: "https://api.policybazaar.com/cs/customer/getPrefComm/v2?",
    CORESERVICES_CLIENTKEY:"3fVx7MxzRRXl",
    CORESERVICES_AUTHKEY: "4zJPamJbuh5IUMh7aB7OzEA8ij9q6nFXZ586YpKT",
    BMS_CROMA_URL: "https://bmscromaapi.policybazaar.com/",
    BMSCROMA_AUTHKEY: "b63phurQkK7P2LImTZS",
    BMSCROMA_CLIENTKEY: "qLIjrnvOSI",
    MatrixSecretKey: "********************************************",
    MatrixSecretIV: "Yk80dWpLMWVlcGEwQlQ4Nw==",
    CALLING_STATUS_URL:"https://internalagenttracker.policybazaar.com/agentstatus/RealTimeAgentStatus?managercode=",
    IncentiveCriteriaUrl: 'matrixinternal/Dashboard/IncentiveCriteria/'
};

const config = prod;

module.exports = {
    ...config
};