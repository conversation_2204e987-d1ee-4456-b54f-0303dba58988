import { useEffect, useState } from "react";
import React from "react";
import axios from "axios";
import {
    GetCommonspData
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";
import {
    Row,
    Col
} from "reactstrap";
import config from "../../config";
import { ToastContainer } from 'react-toastify';
const GMCGPACardView = (props) => {

    const [GMCData, setGMCData] = useState([]);
    const [GPAData, SetGPAData] = useState([]);
    const [UserDetails, setUserDetails] = useState([]);
    const [BusinessUnitDetails, setBusinessUnitDetails] = useState([]);
    const [GroupNameDetails, setGroupNameDetails] = useState([]);
    const [ShowGMCMessage,setShowGMCMessage]=useState("");
    const [ShowGPAMessage,setShowGPAMessage]=useState("");

    useEffect(() => {
        getData();
    }, [])

    const getData = () => {
        let url = config.api.base_url + "/GMC/getUserInfo";
         
        const headers = {
            'Content-Type': 'application/json',
        }
        axios
            .get(url
                , {
                    headers: headers
                })
            .then(data => {
                console.log("GMC Data", data)
                if (data) {
                    let _GetGMCData = data.data && data.data.data && data.data.data.GMCData;
                   Array.isArray(_GetGMCData) && _GetGMCData.length===0? setShowGMCMessage("We couldn't find the Group Medical Cover information for you, please connect with HR/TL."):setShowGMCMessage("");
                    let _GetGPAData = data.data && data.data.data && data.data.data.GPAData;
                    Array.isArray(_GetGPAData) && _GetGPAData.length===0? setShowGPAMessage(" We couldn't find the Group Personal Accident information for you, please connect with HR/TL."):setShowGPAMessage();
                    let _UserDetailsData = data.data && data.data.data && data.data.data.UserProfile && data.data.data.UserProfile.recordsets;
                    console.log("GMC Data is", _GetGMCData);
                    console.log("GPA Data is", _GetGPAData);
                    console.log("User details", _UserDetailsData[0])
                    console.log("Business Unit Details", _UserDetailsData[1])
                    console.log("Group Name details", _UserDetailsData[2])
                    let _userdetails = _UserDetailsData[0]
                    let _userName = _userdetails[0] && _userdetails[0].UserName;
                    let EmployeeId = _userdetails[0] && _userdetails[0].EmployeeId;
                    let _BusinessDetailsData = _UserDetailsData && _UserDetailsData[1];
                    let _GroupNameDetailsData = _UserDetailsData && _UserDetailsData[2];
                    let _BusinessDetails = '';

                    Array.isArray(_BusinessDetailsData) && _BusinessDetailsData.length > 0 && _BusinessDetailsData.map((BU, index) => {
                        _BusinessDetails = _BusinessDetails + (index == _BusinessDetailsData.length - 1 ? BU.ProductName : BU.ProductName + ', ')
                    })

                    let _GroupNameDetails = '';
                    Array.isArray(_GroupNameDetailsData) && _GroupNameDetailsData.length > 0 && _GroupNameDetailsData.map((GN, index) => {

                        _GroupNameDetails = _GroupNameDetails + (index == _GroupNameDetailsData.length - 1 ? GN.GroupName : GN.GroupName + ',')
                    }
                    )
                    console.log("Business Data", _BusinessDetails)
                    console.log("User Name is", _userName);
                    console.log("Employee ID is", EmployeeId);
                    console.log(_GetGMCData);

                    setGMCData(_GetGMCData);
                    SetGPAData(_GetGPAData);
                    setUserDetails(_UserDetailsData[0]);
                    setBusinessUnitDetails(_BusinessDetails);
                    setGroupNameDetails(_GroupNameDetails);


                }
            })
            .catch(error => {
                setGMCData([]);
                SetGPAData([]);
                setShowGMCMessage("We couldn't fetch your GMC details.Please try again!!")
                setShowGPAMessage("We couldn't fetch your GPA details.Please try again!!")
            });
    }

    return (
        <>

            <div className="content GMCGPACardSection">
                <ToastContainer />
                <Row className="justify-content-md-center">
                    <Col sm={8} md={4} xs={12}>
                        <div className="profile">

                            <img src="/GMC/profile_img.png" />
                            {/* <h4>Rishabh Mehrotra</h4> */}
                            {UserDetails && UserDetails[0] && UserDetails[0].UserName && <h4>{UserDetails[0].UserName}</h4>}
                            {/* <p>PW22205</p> */}
                            {UserDetails && UserDetails[0] && UserDetails[0].EmployeeId && <p>{UserDetails[0].EmployeeId}</p>}
                            <ul>
                                <li>Business Unit
                                    <p title={BusinessUnitDetails}>{BusinessUnitDetails.length > 30 ? BusinessUnitDetails.substring(0, 20) + "..." : BusinessUnitDetails}</p>
                                </li>
                                <li>Group Name
                                    <p title={GroupNameDetails}>{GroupNameDetails.length > 30 ? GroupNameDetails.substring(0, 20) + "..." : GroupNameDetails}</p>
                                </li>
                            </ul>
                        </div>
                        <div className="GMCcardView">
                            <span>You are covered with</span>
                            <p>Personal Accident Insurance</p>

                            {Array.isArray(GPAData) && GPAData.length === 0 && <div className="AccidentInsurCard">
                                <div className="errorMessage">
                                    <p> {ShowGPAMessage}</p>

                                </div>
                            </div>}
                            {Array.isArray(GPAData) && GPAData.map((record, index) => {
                                return (
                                    <>
                                        <div className="AccidentInsurCard ">
                                            <img src="/GMC/digitLogo.png" className="brandlogo" />
                                            <div className="logoProfileName">
                                                <h4>₹{parseInt(record['MEMBER SI']) / 100000} Lakh Cover
                                                    <p>{record.INSURED_NAME + '(' + record.EMPLOYEE_ID + ')'}</p>
                                                </h4>
                                                <p className="groupname">
                                                    Corporate name
                                                    {/* <h4> CALL Support Services Private Limited  </h4> */}
                                                    <h4>{record['PROPOSER NAME']}</h4>
                                                </p>
                                            </div>
                                            <ul>
                                                <li>Group Policy number
                                                    <h4>{record.Group_Policy_Number}</h4>
                                                </li>
                                                <li>Policy number
                                                    <h4>{record.POLICY_NUMBER}</h4>
                                                </li>
                                                <li>Member ID
                                                    <h4>{record.MEMBER_ID}</h4>
                                                </li>
                                                <li>Policy end date
                                                    <h4>{record.RISK_EXPIRY_DATE}</h4>
                                                </li>
                                                <li>Date of Birth
                                                    <h4>{record['DATE OF BIRTH']}</h4>
                                                </li>


                                            </ul>
                                            <div className="FooterCard">
                                                <ul>
                                                    <li>
                                                        Insurer's name
                                                        <h4>{record['TPA NAME']}</h4>
                                                    </li>
                                                    <li>
                                                        Toll free number
                                                        <h4>1800-258-4242</h4>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </>
                                )
                            })}

                        </div>



                        <div className="GMCcardView">
                            <span>You and your family members(if any) are covered under Group Medical Insurance for a sum assured of upto</span>
                            <p>₹ {Array.isArray(GMCData) && GMCData.length > 0 ?parseInt(GMCData[0]['MEMBER SI']/100000):"___"} Lakh cumulatively</p>
                           

                            {Array.isArray(GMCData) && GMCData.length === 0 && <div className="AccidentInsurCard GroupHealthCard GMCCard">
                                <div className="errorMessage">
                                    <p>{ShowGMCMessage}</p>
                                </div>
                            </div>}

                            {Array.isArray(GMCData) && GMCData.map((record, index) => {
                                return (<>
                                    <div className="AccidentInsurCard GroupHealthCard GMCCard">
                                        <img src="/GMC/Niva_Bupa_Logo.jpg" className="brandlogo" width="75px"/>
                                        <div className="logoProfileName">
                                            <h4>₹{parseInt(record['MEMBER SI']) / 100000} Lakh Health Cover
                                                <p>{record.INSURED_NAME + '(' + record.EMPLOYEE_ID + ')'}</p>
                                            </h4>
                                            {/* <span>Includes: <b>Spouse, Child & Parents</b></span> */}
                                            <p className="groupname">
                                                Group Name
                                                {/* <h4> CALL Support Services Private Limited  </h4> */}
                                                <h4>{record['PROPOSER NAME']}</h4>
                                            </p>
                                        </div>

                                        <ul>
                                            <li>Group Policy number
                                                <h4>{record.Group_Policy_Number}</h4>
                                            </li>
                                            <li>Policy number
                                                <h4>{record.POLICY_NUMBER}</h4>
                                            </li>
                                            <li>Member ID
                                                <h4>{record.MEMBER_ID}</h4>
                                            </li>

                                            <li>Policy end date
                                                <h4>{record.RISK_EXPIRY_DATE}</h4>
                                            </li>

                                            <li>Date of Birth
                                                <h4>{record['DATE OF BIRTH']}</h4>
                                            </li>
                                            <li>Relation Ship
                                                <h4>{record.RELATIONSHIP}</h4>
                                            </li>

                                        </ul>
                                        <div className="FooterCard">
                                            <ul>
                                                <li>
                                                    TPA Name
                                                    <h4>{record['TPA NAME']}</h4>
                                                </li>
                                                <li>
                                                     Contact Number
                                                    <h4>8448590822</h4>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                </>)

                            })

                            }
                            {/* <div className="downloadPolicy">
                            <div className="downloadPolicybtn">
                                <img src="/GMC/download-pdf.SVG" /> <p onClick={() => {
                                    window.open()
                                }}>Download Policy</p>
                            </div>
                            </div> */}

                        </div>







                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(GMCGPACardView);