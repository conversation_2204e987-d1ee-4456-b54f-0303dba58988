import React, {useState, useContext, useEffect} from 'react';

import Modals from './Modals';
import Badge from '@mui/material/Badge';

// import '../../../assets/scss/_tableview.scss';
// import '../../../assets/scss/_mapview.scss';

import arrowIcon from '../../../assets/icons/arrow.svg';
import travellingIcon from '../../../assets/icons/travelling-icon.svg';
import meetingIcon from '../../../assets/icons/meeting-icon.svg';
import callingIcon from '../../../assets/icons/calling-icon.svg';
import idleIcon from '../../../assets/icons/idle-icon.svg';
import logoutIcon from '../../../assets/icons/logout-icon.svg';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import waitingIcon from '../../../assets/icons/waiting-icon.svg';
// import MapView from './MapView';
// import TableView from './TableView';

import { FosContext } from '../FosContext';


const FOSheader = (props) => {


  const {fosData, selectCounter, selectedCounter, openMap, mode, newUrl}= useContext(FosContext);


  // console.log("fos data us ", fosData);

    // const [openMap, setOpenMap] = useState(false); //modal show/hide
    const [cityModal, setcityModal]= useState(false); //city-modal
    const [appointmentModal, setappointmentModal] = useState(false);//appointment-modal

    const [statusCounter, setStatusCounter]=useState({
      Travelling: 0,
      Meeting: 0,
      Calling: 0,
      Idle:0,
      LoggedOut:0,
      Waiting: 0
    });

    const handleClick=(e)=>{
      selectCounter(e);
    }


    
   useEffect(()=>{

    // console.log("fosData is ",fosData);
    // console.log("jsj ", String(fosData.size));

      let Counter = {
        Travelling: 0,
        Meeting: 0,
        Calling: 0,
        Idle:0,
        LoggedOut:0,
        Waiting:0
      };
    
  
      fosData.forEach((data)=>{
            if(data['RealTimeStatusId'])
            {
               switch(data['RealTimeStatusId']){
                case(1):
                  Counter['Travelling']= Counter['Travelling']+1;
                  break;
                case(2):
                  Counter['Meeting']= Counter['Meeting']+1;
                  break;
                case(3):
                  Counter['Calling']= Counter['Calling']+1;
                  break;
                case(4):
                  Counter['Idle']= Counter['Idle']+1;
                  break;
                case(5):
                  Counter['LoggedOut']= Counter['LoggedOut']+1;
                  break;
                case(6):
                  Counter['Waiting']= Counter['Waiting']+1;
                  break;
                default:
                  break;
               }
            }
      });
     
      // console.log("The counter is ", Counter);
      setStatusCounter(Counter);
    
   },[fosData])


 
    


   

  return (
    <div className='fos-header'>
      <h3>FOS Tracker {mode == 1 ? <img onClick={newUrl} style={{ width: '15px' }} src="/fosTlDashboard/Maximize.jpeg" /> : null}</h3> 
      
      <div className='switch-view'>
    
     
      </div>  

      {/* Modals */}
      <div className='header-list d-flex align-items-center'>

        {/* { !openMap && <ul className='d-flex'>
          {cityModal && <Modals modalNum={1} closeModal={handleCloseModal} />}
              <li onClick={cityhandleShow}>
                  Gurugram 
                  <img src={arrowIcon} alt='arrow' />
              </li>
          {appointmentModal && <Modals modalNum={2} closeModal={handleCloseModal}/>}    
              <li onClick={appointmenthandleShow}>
                  10:00 – 12:00
                  <img src={arrowIcon} alt='arrow' />
              </li>
          </ul>
          } */}

          
          <ul className = {!openMap ? 'main-listing d-flex' : 'main-listing d-flex map-listing'} >
              <li className={selectedCounter==0?'active':''} value={0} onClick={()=>handleClick(0)}>
                  <span>All</span>
                  <Badge badgeContent={String(fosData.size)} max={9999} color="primary"></Badge>
              </li>
              <li className={selectedCounter==1?'active':''} value={1} onClick={()=>handleClick(1)}>
                  <img src={travellingIcon} alt='Travelling' />
                  <span>Travelling</span>
                  <Badge badgeContent={String(statusCounter.Travelling)} max={999} color="primary"></Badge>
              </li>
              <li className={selectedCounter==4?'active':''} value={4} onClick={()=>handleClick(4)}>
                  <img src={idleIcon} alt='Idle' />
                  <span>Idle</span>
                  <Badge badgeContent={String(statusCounter.Idle)} max={999} color="primary"></Badge>
              </li>
              <li className={selectedCounter==2?'active':''} value={2} onClick={()=>handleClick(2)}>
                  <img src={meetingIcon} alt='Meeting' />
                  <span>Meeting</span>
                  <Badge badgeContent={String(statusCounter.Meeting)} max={999} color="primary"></Badge>
              </li>
              <li className={selectedCounter==3?'active':''} value={3} onClick={()=>handleClick(3)}>
                  <img src={callingIcon} alt='Calling' />
                  <span>Calling</span>
                  <Badge badgeContent={String(statusCounter.Calling)} max={999} color="primary"></Badge>
              </li>
              <li className={selectedCounter==6?'active':''} value={6} onClick={()=>handleClick(6)}>
                  <img src={waitingIcon} alt='Waiting at CX' />
                  
                  <span>Waiting at CX</span>
                  <Badge badgeContent={String(statusCounter.Waiting)} color="primary"></Badge>
              </li>

              <li className={selectedCounter==5?'active':''} value={5} onClick={()=>handleClick(5)}>
                  <img src={logoutIcon} alt='Logged out' />
                  <span>Logged out</span>
                  <Badge badgeContent={String(statusCounter.LoggedOut)} max={999} color="primary"></Badge>
              </li>
          </ul>

      </div>
    </div>
  )
}

export default FOSheader;