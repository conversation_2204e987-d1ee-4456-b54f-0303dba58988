import moment from 'moment';
import config from "../../config";
import { LeadContentView, LeadView, setCookie } from "../../utility/utility";
export const ALL = 1000;
export const MOBILE_SEARCHED_TEXT = 'Search result by MobileNo';
export const LEADID_SEARCHED_TEXT = 'Search result by LeadId';
export const EMAILID_SEARCHED_TEXT = 'Search result by EmailId';
export const SECONDARY_BOOKINGS_TEXT = 'All your bookings from FOS and other assisted sales channels';
export const SECONDARY = 2000;
export const ISADMIN = false;


export const GetFirstDateOfMonth = () => {
  try {
    const Today = new Date();
    const d = new Date(Today.getFullYear(), Today.getMonth(), 1);
    const MonthNumber = parseInt(d.getMonth() + 1);
    const Year = d.getFullYear();
    let DateValue = "";
    if (MonthNumber < 10) {
      DateValue = `${Year}-0${MonthNumber}-01`;
    } else {
      DateValue = `${Year}-${MonthNumber}-01`;
    }
    return DateValue;
  } catch (err) {
    return null;
  }
}

export const DateRangeFilter = () => {
  const Result = [];
  const MonthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"];
  const Today = new Date();

  for (let i = 6; i > 0; i--) {
    const d = new Date(Today.getFullYear(), Today.getMonth() - i + 1, 1);
    const Month = MonthNames[d.getMonth()];
    const MonthNumber = d.getMonth() + 1;
    const Year = d.getFullYear();
    let DateValue = "";
    if (MonthNumber < 10) {
      DateValue = `${Year}-0${MonthNumber}-01`;
    } else {
      DateValue = `${Year}-${MonthNumber}-01`;
    }
    Result.push({ month: Month, year: Year, value: DateValue });
  }
  Result.reverse();
  return Result;
}

export const ModifyType = (data, type) => {
  let result = data;
  if (type === 'date') {
    result  = data ? moment(data).utcOffset(0).format('DD-MM-YYYY') : "";
  } else if (type === 'datetime') {
    result  = data ? moment(data).utcOffset(0).format('DD-MM-YYYY HH:mm:ss') : "";
  } else if (type === 'int') {
    result = data && data.toFixed(2);
  } else if (type === '') {
    result = data ? data : 'N.A';
  }

  if (!result) {
    result = "-"
  }
  return result;
}

// export const FilterSearchedDuplicateLeads = ({ DuplicateLeads, TextToSearch }) => {
//   try {
//     const searchTerm = TextToSearch.trim();
//     let rows = DuplicateLeads;

//     if (!searchTerm) return rows;

//     if (rows.length > 0) {
//       const attributes = Object.keys(rows[0]);

//       const list = [];

//       for (const current of rows) {
//         for (const attribute of attributes) {
//           if (attribute === "Id") {
//             continue;
//           }
//           const value = current[attribute];
//           if ( value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())) {
//             const found = list && list.find((row) => row.Id === current.Id);
//             if (!found) {              
//               list.push(current);
//             }
//           }
//         }
//       }
//       return list;
//     }
//   } catch (err) {
//     console.log(err)
//     return [];
//   }
// }

export const FilterSearchedTextDuplicateLeads = ({ DuplicateLeads, TextToSearch }) => {
  try {
    const text = TextToSearch.trim();
    const FilteredLeads = DuplicateLeads.filter(leads => {
      const CustomerName = (leads?.Name && leads.Name.toLowerCase()) || "";
      const LeadID = (leads?.LeadID) || "";
      const ParentID = (leads?.ParentID) || "";
      const EmailId = (leads?.EmailId && leads.EmailId.toLowerCase()) || "";
      const City = (leads?.City && leads.City.toLowerCase()) || "";
      const Product = (leads?.Product && leads.Product.toLowerCase()) || "";
      const LeadSource = (leads?.LeadSource && leads.LeadSource.toLowerCase()) || "";
      const StatusSubstatus = (leads?.StatusSubstatus && leads.StatusSubstatus.toLowerCase()) || "";
      //FOR SME SUBPRODUCT NAME TO BE INCLUDED
      const SubProductName = (leads?.SubProductName && leads.SubProductName.toLowerCase()) || "";   
      return (
        CustomerName.includes(text) || (LeadID.toString()).includes(text) ||
        EmailId.includes(text) || Product.includes(text) || City.includes(text) || ParentID.includes(text) ||
        LeadSource.includes(text) || StatusSubstatus.includes(text) || SubProductName.includes(text)
      );
    });
    return FilteredLeads;

  } catch (err) {
    return [];
  }
}

export const fnFilteredItems= async({items, filterName, filterValue}) => {

  if (filterValue == "") {
    return items;
  }

  //if (typeof (items) != 'undefined') {
    const filteredItems = items.filter(item => String(item[filterName]).toString().toUpperCase().indexOf(filterValue.toUpperCase()) > -1);
    return filteredItems;
  //}
  //else {
  //  return items;
  //}

}


export const GetSalesViewurl = async ({ data, AgentInfo }) => {
  try {

    let FinalUrl = "", url= "";
    let { userId, token } = JSON.parse(window.atob(AgentInfo || {})) || {};
    let UserDetails = window.btoa(JSON.stringify({"UserId": userId,"AsteriskToken": token }))
    
      url = await LeadContentView(data?.CustID, data?.LeadID, data?.ProductID);
      let TargetUrl = window.btoa(url);
      FinalUrl = `${config.api.newSalesviewUrl}auth?u=${UserDetails}&t=${TargetUrl}`
    
    return FinalUrl;
  } catch (err) {
    return ""
  }
}

export const PerformActionsOnLinkClick = async ({ data, col, FilterProductId }) => {
  let ReturnUrl = 0, Data = "", ErrorStatus = 0, Type = "";
  try {
    let url = '';
    switch (col?.name) {
      case 'BookingID':
        url = LeadContentView(data.CustomerID, data.BookingID, FilterProductId);
        ReturnUrl = 1;
        break;

      case 'ContinueJourney':
        url = data[col.accessor];
        ReturnUrl = 1;
        break;

      default:
        break;
    }

    if (ReturnUrl && url) {
      window.open(url);
    }
    return { Type, ErrorStatus, Data }

  } catch (err) {
    return { Type: "Error", ErrorStatus: 1, Data: null }

  }
}



    export const TableColumnsDuplicateLeads = [
      { name: "S. No", accessor: "", type: "" },
      { name: "Lead Created On", accessor: "CreatedOn", type: "datetime", dependents: [], sortable: true },
      { name: "LeadID", accessor: "LeadID",  type: "iframe", show: true, dependents: []},
      { name: "Product", accessor: "Product", type: "", sortable : true },
      { name: "Name", accessor: "Name", type: "" },
      { name: "Company Name", accessor: "CompanyName", type: "" },     
      { name: "Email Id", accessor: "EmailId", type: "" },
      { name: "Customer Id", accessor: "CustID", type: "" },
      { name: "SubProductName", accessor: "SubProductName", type: "" },
      { name: "Customer Status", accessor: "CustomerStatus", type: "" },
      { name: "Contact No", accessor: "MobileNo", type: "mobileno" },
      { name: "City", accessor: "City", type: "" },
      { name: "Status", accessor: "StatusSubstatus", type: "", sortable : true },
      { name: "Assigned To Group", accessor: "GroupName", type: "" },
      { name: "Assigned To User", accessor: "AgentName", type: "", sortable : true },
      { name: "Sales Agent", accessor: "SalesAgentName", type: "", sortable : true },
      { name: "Is AVCertified", accessor: "IsCertified", type: "" },
      { name: "AVCertification Date", accessor: "DateofCertification", type: "datetime" },
      { name: "Is BQPCertified", accessor: "IsBQPCertified", type: "" },
      { name: "BQPCertified Date", accessor: "BQPCertificationDate", type: "datetime" },
      { name: "WorkLocation", accessor: "WorkLocation", type: "" },
      { name: "LeadSource", accessor: "LeadSource",type: "" },
      { name: "Utm_source", accessor: "Utm_source", type: "" },
      { name: "ParentLeadID", accessor: "ParentID", type: "" },
      { name: "Proposal Form Filled", accessor: "PropFormFilled", type: "" },
      { name: "IsPaymentCall", accessor: "IsPaymentCall", type: "" },

    ];

    export const TableColumnsSearchLeads = [
      { name: "S. No", accessor: "", type: "" },
      { name: "Lead Created On", accessor: "CreatedOn", type: "datetime", dependents: [], sortable: true },
      { name: "LeadID", accessor: "LeadID",  type: "iframe", show: true, dependents: []},
      { name: "Product", accessor: "Product", type: "", sortable : true },
      { name: "Name", accessor: "Name", type: "" },
      { name: "Company Name", accessor: "CompanyName", type: "" },     
      { name: "Email Id", accessor: "EmailId", type: "" },
      { name: "SubProductName", accessor: "SubProductName", type: "" },
      { name: "Customer Status", accessor: "CustomerStatus", type: "" },
      { name: "Contact No", accessor: "MobileNo", type: "mobileno" },
      { name: "City", accessor: "City", type: "" },
      { name: "Status", accessor: "StatusSubstatus", type: "", sortable : true },
      { name: "Assigned To Group", accessor: "GroupName", type: "" },
      { name: "Assigned To User", accessor: "AgentName", type: "", sortable : true },
      { name: "Sales Agent", accessor: "SalesAgentName", type: "", sortable : true },
      { name: "Is AVCertified", accessor: "IsCertified", type: "" },
      { name: "AVCertification Date", accessor: "DateofCertification", type: "datetime" },
      { name: "Is BQPCertified", accessor: "IsBQPCertified", type: "" },
      { name: "BQPCertified Date", accessor: "BQPCertificationDate", type: "datetime" },
      { name: "WorkLocation", accessor: "WorkLocation", type: "" },
      { name: "LeadSource", accessor: "LeadSource", type: ""},
      { name: "Utm_source", accessor: "Utm_source", type: "" },
      { name: "ParentLeadID", accessor: "ParentID", type: "" },
      { name: "Proposal Form Filled", accessor: "PropFormFilled", type: "" },
      { name: "IsPaymentCall", accessor: "IsPaymentCall", type: ""},

    ];