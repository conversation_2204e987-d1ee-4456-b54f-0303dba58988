import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetCommonData, GetCommonspData, AgentSurvey
} from "../store/actions/CommonAction";

import { connect } from "react-redux";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DataTable from './Common/DataTableWithFilter';
import AgentSurveyPopUp from './AgentSurveyPopUp';


import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import DropDown from './Common/DropDown';
import 'react-bootstrap-typeahead/css/Typeahead.css';

const AgentSurveyMapping = (props) => {
    const [items, setitems] = useState([])
    const PageTitle = "Agent Survey Mapping"
    const [selected, setSelected] = useState([]);
    const [employeeId, setEmployeeId] = useState();
    const [SuccessData, setSuccessData] = useState([]);
    const [showbutton, setshowbutton] = useState(false);
    //const [addClass, setaddClass] = useState('btn btn-primary');
    const [LoadClass, setLoadClass] = useState("")
    const [LoadClassAll, setLoadClassAll] = useState("")
    const [disClass, setdisClass] = useState(false);
    const [ModalOpen, setModalOpen] = useState(false);
    let dataArray = []
    let text=""

    const columnlist = [
        {
            name: 'Employee Id',
            selector: 'EmployeeId',
            type: "string",
            editable: false,
        },
        {
            name: 'Status',
            selector: 'status',
            cell: row => <div>{row.status==0 ? "Failure" : row.status=="Pending" ? "Pending"  : ""}</div>,
            type: "string",
            editable: false,
        },

    ];

    useEffect(() => {
        if (!props.CommonData.isError) {
            props.GetCommonData({
                limit: 10,
                skip: 0,
                root: 'SurveyMaster',
                cols: ["SurveyId AS Id", "SurveyName+' ('+cast(SurveyId as varchar)+')' AS Display"],
                con: [{ "Isactive": 1 }],
                c: "R",
            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data[0]
                    item.sort(function (a, b) {
                        return b.Id - a.Id;
                      });
                    setitems(item);
                }
            });
        }
    }, [])


    const handleAssign = (event, LeadId) => {
        setModalOpen(true)
    };

    //For AutoDropDown
    // const SurveyChange = (select) => {
    //     if (select.length > 0) {
    //         setSelected(select[0].Id)
    //     } else {
    //         setSelected([])
    //     }
    // }

    
    const SurveyChange = (e) => {
        setSelected(e.target.value);
    }

    const Getdata = (e) => {
        e.preventDefault();
        if (!employeeId || selected == 0) {
            toast.error("Please fill all the required fields!")
        }

        if (employeeId && selected > 0) {
            let match = employeeId.split(',')
            for (let i = 0; i < match.length; i++) {
                let emid = match[i].trim();
                if (emid == "") {
                    continue
                }

                let arr = { EmployeeId :emid , status : "Pending"} 

                dataArray.push(arr)
                // props.GetCommonspData({
                //     root: "InsertSurveyAgentMapping",
                //     params: [{ EmployeeId: emid }, { SurveyId: selected }],
                //     c: "L",
                // }, function (data) {
                //     if (data && data.data && data.data.data) {
                //         let item = data.data.data[0]
                //         dataArray.push(item)
                //         setSuccessData(dataArray)
                //     }
                // })
                
                setSuccessData(dataArray)
                setshowbutton(true);
            }
            //toast("Saved Successfully", { type: 'success' })
        }
    }


    const handleSubmit = () => {
        //setaddClass('btn btn-primary fa fa-spinner');
        setLoadClass('spinner-border spinner-border-sm');
        setdisClass(true)
        //setLoadClass(true);
        let updateArray = [ ...SuccessData ]

        for(let i=0; i<updateArray.length; i++){
            updateArray[i]={...updateArray[i], SurveyId: selected }
        }
        AgentSurvey(updateArray, function (results) {
            
            if (results.data.status == 200) {
                let faileddata = []
                alert('Saved Successfully');
                for(let k = 0 ; k < results.data.data.length; k++){
                    if(results.data.data[k].status===0){
                        faileddata.push(results.data.data[k])
                    }
                }
                
                setSuccessData(faileddata)
                setshowbutton(false);
                //setaddClass('btn btn-primary');
                setLoadClass("");
                setdisClass(false)
                //setLoadClass(false);
                setEmployeeId("")
            } else {
                toast(results.data.message, { type: 'error' });
                setLoadClass("");
                //setLoadClass(false);
                //setaddClass('btn btn-primary');
                setshowbutton(false);
                setdisClass(false)
                return;
            }
        });
    }

    const handleSubmitAll = () => {
        setLoadClassAll('spinner-border spinner-border-sm');
        setdisClass(true)
        var SurveyId = selected;

        props.GetCommonspData({
            root: 'InsertSurveyAgentMappingAll',
            c: "L",
            params: [{ "SurveyId": SurveyId }],
        }, function (result) {
            if (result.data && result.data.status == 200) {
                toast('Saved Successfully', { type: 'success' });
                setLoadClassAll("");
                setdisClass(false)
            }else {
            toast('Not saved', { type: 'error' });
            setLoadClassAll("");
            setdisClass(false)
            return;
        }
        }.bind(this));

    }

    const employeeChange = (e) => {
        setEmployeeId(e.target.value)
    }
    

    return (
        <>
            <ToastContainer />
            <div className="content">

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Col md={12}>
                                        <Form >
                                            <Form.Label>Survey</Form.Label>
                                            {/* <DropDown AutoList={true} firstoption="Select" items={items} onChange={SurveyChange} ></DropDown> */}
                                            <DropDown firstoption="Select" items={items} onChange={SurveyChange} ></DropDown>
                                        </Form>
                                    </Col>
                                </Row>
                                <br />
                                    <Col md={1}>
                                        <Button variant="primary" onClick={() => handleAssign()} >Assign</Button>
                                    </Col>

                                {ModalOpen && <AgentSurveyPopUp survey = {selected}
                                    handleClose={() => {
                                        setModalOpen(false);
                                    }}
                                />

                                }

                                <div><br />
                                    <Form.Label>Enter Employee Id (Employee id with , Seperated)</Form.Label>
                                    <form>
                                        <textarea placeholder="PW26XXX, PW00XXX, PW52XXX"
                                            style={{
                                                width: "100%",
                                                padding: "10px",
                                            }}
                                            name="query"
                                            onChange={employeeChange}
                                            value={employeeId}
                                            rows="10"
                                        />
                                        <br />
                                        <Row>
                                            <Col md={1}>
                                                <Button onClick={Getdata}>
                                                    Upload
                                                </Button>
                                            </Col>

                                            {showbutton && <Col md={2}>
                                                <Button style = {{marginLeft:"10px"}} className="btn btn-primary" disabled = {disClass} onClick={handleSubmit}>
                                                <span className={LoadClass}></span>
                                                 {/* { LoadClass && <Spinner animation="border" size="sm" /> } */}
                                                    Publish
                                                </Button>
                                            </Col>}
                                            {/* <Col md={2}>
                                                <Button disabled = {disClass} onClick={handleSubmitAll}>
                                                <span className={LoadClassAll}></span>
                                                  For All Agents
                                                </Button>
                                            </Col> */}
                                        </Row>
                                    </form>
                                </div>
                                <br></br>

                                <DataTable
                                    columns={columnlist}
                                    //data={dataArray}
                                    data = {SuccessData}
                                    printexcel={true}
                                />

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData
    }
)(AgentSurveyMapping);