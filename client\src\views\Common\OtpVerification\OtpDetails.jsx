import React, { Component } from 'react';
import { Button, Form } from 'react-bootstrap';
import {
    Row,
    Col
} from "reactstrap";

import OtpInput from "react-otp-input";
import { If, Then } from 'react-if';


class OtpDetails extends Component{

    constructor(props) {
        super(props);
        this.state = {
            bgImg: '/Graphic.svg' ,    
            advImg: '/Logo.svg' , 
            enterSmallImg:'/enterSmall.svg', 
            verifybtniconImg:'/verifybtnicon.svg',
    }
    }

    renderButtonCallLog(isCookieExists, loginCookie){
        if(isCookieExists){
            return  <a onClick={(e) => this.props.checkCallHistory(loginCookie)}><div className="seperateBox"><Col md={3} xs={3} lg={2} className="text-center"><span><img src={this.state.enterSmallImg}/> </span> </Col>
            <Col md={9} xs={9} lg={10}><Button variant="primary" className="clHistoryBtn">Check Call History</Button>
            <p>Find if you spoke to a genuine advisor from Policybazaar</p></Col></div></a>
        } else {
            return <div className="seperateBox"><Col md={3} xs={3} lg={2} className="text-center"><span><img src={this.state.enterSmallImg}/> </span> </Col>
            <Col md={9} xs={9} lg={10}><Button variant="primary" id="central-login-module-trigger" onClick={(e) => this.props.eventOnClick()} className="clHistoryBtn">Check Call History</Button>
            <p>Check details of the conversations that you had with our advisors in past</p></Col></div>

        }
    }

    render(){

        const { bgImg, advImg, enterImg, isCookieExists, loginCookie } = this.props
        return(
            <Row> 
            <Col md={6} xs={12}> <div className="verifybgImg"><img src={this.state.bgImg}/>  </div>  </Col>
            <Col md={6} xs={12}>                          
            <Row>                                                                       
        <Col md={12} xs={12} lg={9} className=" mt-2">
        <Form.Text className="text-muted">
        <h2 class="otp-title">Know your advisor</h2>  
 		<p class="otp-caption">Policybazaar is India's largest insurance web aggregator with over 100 million registered customers. But some fraudsters have started misusing Policybazaar's name to mislead customers.<br/><br/>
        Through this service, you can instantly check your call history with us at anytime and ensure you have been speaking with a genuine Policybazaar advisor.
        </p>
        <p><a onClick={this.props.howitworks.bind(this)} className="knowMore">Know More </a></p>  
        </Form.Text> 
        </Col> </Row>  
        {/* <Row className="bgBlue">
            <Col md={3} xs={3} lg={2}><img src={this.state.advImg}/> </Col>
            <Col md={4} xs={4} lg={3}><span className="wantTo">Want to</span>
            <p><a onClick={this.props.knowmore.bind(this)} className="knowMore">Know more <i class="nc-icon nc-minimal-right"></i> </a></p>
            </Col>
            <Col md={5} xs={5} lg={3}>
            <span className="wantTo">Still not sure</span>
            <p><a onClick={this.props.howitworks.bind(this)} className="knowMore">See how it works <i class="nc-icon nc-minimal-right"></i> </a></p>
            </Col>

        </Row> */}
         <Row >
         <Col  md={12} xs={12} lg={9}>
        <div className="clHistoryCheck mt-4">
        <Row>
        
         
            {this.renderButtonCallLog(isCookieExists, loginCookie)}
            
                 
            </Row> 
            {/* <h4 className="seperateLine"><span>Or</span></h4>

            <Row>
         <div  className="seperateBox">
         <Col md={3} xs={3} lg={2} className="text-center"><span className="bgyellow"><img src={this.state.verifybtniconImg}/></span>  </Col>
        <Col md={9} xs={9} lg={10}>
        <Button variant="primary" className="clHistoryBtn" onClick={(e) => this.props.openOtpVerify()}>Verify Advisor through OTP</Button>
            <p>Enter the 6 digit OTP shared by your advisor to verify instantly</p></Col>
        </div>
            </Row> */}
            </div>  
                     
        </Col>  
                                 
            </Row> 
            </Col> 
            </Row>                                                       

                                 
        )
    }
}


export default OtpDetails;