import React, { useEffect, useState } from "react";
import 'react-toastify/dist/ReactToastify.css';

import './StarsAward.scss';
import _ from 'underscore';
import JagCountDownImage from"./JagCountDown.json"
import JagCountDown from "lottie-react"


const JagCounting = () => {
      return (
        <div className="EmergingStarsLayout">            
          <h2>dssd</h2>
          <JagCountDown animationData={JagCountDownImage} className="lottieAnimation" loop={true} />
        

        </div>
    );

}

export default JagCounting;



