import { useEffect, useState } from 'react';
import { Col, Dropdown, Form, Modal, Row, Button } from 'react-bootstrap';
import moment from 'moment';
import { connect } from "react-redux";
import {
  GetCommonData, GetCommonspData, SalesView, SetAppointmentData, SlotLeadAssignment
} from "../../store/actions/CommonAction";
import { TimeConversion, getuser } from '../../utility/utility.jsx';
import LeadDetails from './LeadDetails';


const AssignedLeadPopup = (props) => {
  const { Details, AppointmentSlots, activeTab, Rescheduled, IsSuccess, onHide, AssignagentLead } = props;
  const [Toggle, setToggle] = useState(false);
  const [AppDate, setAppDate] = useState(moment.utc(Details.AppDateTime).local(true).format("YYYY-MM-DD"));
  const [agentsList, setAgentsList] = useState([]);
  const [dateObj, setDateObj] = useState([]);
  const [getSlot, setgetSlot] = useState(parseInt(Details.SlotId));
  const [assignAgent, setAssignAgent] = useState(Details.assignedTO);
  const [confirmPopup, setconfirmPopup] = useState(false);
  const [AssignLeadPopup, setAssignLeadPopup] = useState(true);
  const [toggleChecked, settoggleChecked] = useState('-1');
  // const [hours, sethours] = useState();

  let hours;
  if (Details && Details.AppDateTime) {
    let diff = parseInt(AppointmentSlots[0].EndTime.slice(0, 2)) - parseInt(AppointmentSlots[0].StartTime.slice(0, 2))
    hours = moment(new Date()).subtract(diff, 'hours').format("HH:mm")
    // date = moment(new Date()).format("DD")
  }

  useEffect(() => {
    let dateObj = []
    // if (activeTab <= 2) {
      // setAppDate(moment().format("YYYY-MM-DD"))
      for (let i = 0; i < 2; i++) {
        dateObj.push(moment().add(i, 'days').format("YYYY-MM-DD"))
      }
      setDateObj(dateObj)
    // } 
    // else {
    //   setAppDate(moment().add(1, 'days').format("YYYY-MM-DD"))
    //   dateObj.push(moment().add(1, 'days').format("YYYY-MM-DD"))
    //   setDateObj(dateObj)
    // }
  }, [])

  useEffect(() => {
    let dropslotid

    // if (activeTab <= 2) {
      // If ActiveTab Today And AppointmentDate selected for Today
      if (AppDate) {
        if (AppDate == moment().format("YYYY-MM-DD") || AppDate == moment().subtract(1, 'days').format("YYYY-MM-DD")) {
          for (let i = 0; i < AppointmentSlots.length; i++) {
            if (hours < AppointmentSlots[i].StartTime) {
              dropslotid = AppointmentSlots[i].Id;
              break;
            }
          }
          if (getSlot < dropslotid) {
            setgetSlot(dropslotid);
            OnChangeSlot(dropslotid)
          }
          else {
            setgetSlot(getSlot);
            OnChangeSlot(getSlot)
          }
        }
        else {
          if(activeTab <=2){
            setgetSlot(1);
            OnChangeSlot(1)
          }
          else{
            setgetSlot(Details.SlotId);
            OnChangeSlot(Details.SlotId)
          }
          
        }
      }

    // }
    // else {
    //   OnChangeSlot(getSlot)
    // }
  }, [AppDate])


  // useEffect(() => {
  //   let SlotId ; 
  //   // let dropslotid 
  //   if(getSlot>0){
  //     SlotId = getSlot;
  //   }
  //   // else{
  //   //   if(activeTab == 2){
  //   //     // If ActiveTab Today And AppointmentDate selected for Today
  //   //     for(let i = 0; i <AppointmentSlots.length;i++){
  //   //       if(AppDate == moment().format("YYYY-MM-DD")){
  //   //         if(hours < AppointmentSlots[i].StartTime){
  //   //           dropslotid = AppointmentSlots[i].slotId;
  //   //           return;
  //   //         }
  //   //       }
  //   //     }
  //   //   }
  //   // }

  //   // if(slotid < dropslotid){
  //   //   setgetSlot(dropslotid)
  //   // }
  //   // else{
  //   //     SlotId = Details.SlotId;
  //   //     setgetSlot(parseInt(Details.SlotId))
  //   // }

  //     props.GetCommonspData({
  //       root: 'GetNearestAgentToLead',
  //       params: [{ LeadId: Details.LeadId }, { slotId: SlotId }],
  //       c: "R",
  //     }, function (data) {
  //       if (data && data.data && data.data.data) {
  //         let item = data.data.data[0]

  //         setAgentsList(item);
  //       }
  //     });

  // }, [getSlot])

  const GetNearestAgent = (SlotId) => {
    try {
      // props.GetCommonData({
      //   limit: 10,
      //   skip: 0,
      //   root: 'GetNearestAgentToLead',
      //   LeadId: Details.LeadId , SlotId: SlotId , AppointmentDate: AppDate,
      //   c: "R"
      props.GetCommonspData({
        root: 'GetNearestAgentToLead',
        params: [{ LeadId: Details.LeadId }, { slotId: SlotId }, { AppointmentDate: AppDate }],
        c: "R",
      }, function (data) {
        if (data && data.data && data.data.data) {
          let item = data.data.data[0]

          for(let i=0; i<item.length; i++){
            item[i]={...item[i], idx: i }
          }
          setAgentsList(item);
        }
      });
    }
    catch (ex) {
      console.log(ex)
    }

  }

  const OnChangeSlot = (SlotId) => {
    if (SlotId) {
      if (AssignagentLead && AppDate && AppDate == moment.utc().local(true).format("YYYY-MM-DD") && AppointmentSlots && AppointmentSlots[AppointmentSlots.length - 1].StartTime > hours) {
        GetNearestAgent(SlotId)
      }
      else if (AssignagentLead  && AppDate && AppDate != moment().format("YYYY-MM-DD")) {
        GetNearestAgent(SlotId)
      }
    }
  }

  const AppointmentDate = (e) => {
    console.log(e.target.value)
    setAppDate(e.target.value)
    setToggle(!Toggle)
    setAssignAgent(Details.assignedTO)
    settoggleChecked('-1')
  }
  const slotidChange = (e) => {
    setgetSlot(parseInt(e.target.value) + 1)
    OnChangeSlot(parseInt(e.target.value) + 1)
    setAssignAgent(Details.assignedTO)
    settoggleChecked('-1')
  }

  const getAgent = (e) => {
    setAssignAgent(e.target.value)

    for(let i=0;i<agentsList.length;i++){
      if(agentsList[i].AgentID == e.target.value){
        settoggleChecked(agentsList[i].idx)
      }
    }
  }



  const confirmation = () => {
    console.log(getSlot, AppDate, assignAgent)
    if((assignAgent > 0) && (getSlot!=Details.SlotId || AppDate!= moment.utc(Details.AppDateTime).local(true).format("YYYY-MM-DD") ||  assignAgent!=Details.assignedTO)){
      if(AppDate && AppDate == moment().format("YYYY-MM-DD") && hours>AppointmentSlots[AppointmentSlots.length - 1].EndTime){
        setconfirmPopup(false)
      }
      else{
        setconfirmPopup(true)
      }
      
     }
  }

  const RescheduleLeadData = () =>{
    console.log(getSlot, AppDate, assignAgent)
    //setAssignLeadPopup(false);

    // let AssignAgent = assignAgent;
    let RescheduleTime;
    let AppStatus;

    for (let i = 0; i < AppointmentSlots.length; i++) {
      if (getSlot == AppointmentSlots[i].Id) {
        RescheduleTime = AppDate + " " + AppointmentSlots[i].StartTime + ':00'
        break;
      }
    }

    if (AppDate != moment(Details.AppDateTime).format("YYYY-MM-DD")) {
      // AppStatus = Details.SubstatusId
      AppStatus = 2005
    }
    else if (getSlot != Details.SlotId) {
      // AppStatus = Details.SubstatusId
      AppStatus = 2005
    }
    else {
      AppStatus = Details.SubstatusId
    }

    // if(AppDate != moment(Details.AppDateTime).format("YYYY-MM-DD")){
    //   AppStatus = 2005
    // }
    // else{
    //   AppStatus = Details.SubstatusId
    // }

    // if(Rescheduled){
    //   AssignAgent = Details.assignedTO
    // }else{
    //   AssignAgent = assignAgent
    // }

      let obj = {
        CustomerId: Details.CustomerId,
        ParentId: Details.LeadId,
        UserId: getuser().UserID,
        CreatedBy: getuser().UserID,
        OfflineCityId: Details.OfflineCityId,
        AppointmentType: Details.AppointmentType,
        AssignmentId: Details.AssignmentId,
        ZoneId: Details.ZoneId,
        AppointmentDateTime: RescheduleTime,
        SlotId: getSlot,
        Address: Details.Address,
        Address1: Details.Address1,
        Pincode: Details.Pincode,
        Landmark: Details.Landmark,
        NearBy: Details.NearBy,
        Comments: Details.Comments,
        // subStatusId: Details.SubstatusId,
        subStatusId: AppStatus,
        place_id: Details.PlaceId,
        CancelReasonId: 0
      }

      return obj;
  }

  const RescheduleLead = () => {
    let AssignAgent = assignAgent;
    try{
      let obj = RescheduleLeadData()
      onHide();
      if (AssignAgent > 0) {
        SetAppointmentData(obj, function (results) {
          if (results.data.status == 200) {
            setTimeout(function () {
              IsSuccess();
              setconfirmPopup(false)
            }, 500)
          }
        });
      }


    }
    catch (ex) {
      console.log(ex)
      setconfirmPopup(false)
      onHide();
    }
  }

  const Assignment = () => {
    let EmployeeID = Details.EmployeeId

    for(let i = 0; i<agentsList.length; i++){
      if(assignAgent == agentsList[i].AgentID){
        EmployeeID = agentsList[i].AgentCode;
        break;
      } 
    }

    console.log(EmployeeID)

    let obj = RescheduleLeadData()
    let data = {
      LeadID: Details.LeadId, 
      EmployeeID: EmployeeID, 
      AssignedBy:getuser().UserID
    }

    let AssignData = [obj, data]
    try{
      SlotLeadAssignment(AssignData, function (results) {
        onHide();
        if (results.data.status == 200) {
          setTimeout(function () {
            IsSuccess();
            setconfirmPopup(false)
          }, 500)
        }
      });
    }
    catch(ex){
      console.log(ex)
      setconfirmPopup(false)
      onHide();
    }
  }


  const saveData = () => {
    debugger;
    let EmployeeID 
    console.log(getSlot, AppDate, assignAgent)
    if(Rescheduled){
      RescheduleLead();
    }
    else if(AssignagentLead){
      Assignment();
      
      // props.GetCommonspData({
      //   root: "Fos_UploadLeadAllocation",
      //   params: [ {LeadID: Details.LeadId}, {EmployeeID: EmployeeID}, {AssignedBy:getuser().UserID }], 
      //   c: "L",
        
      // }, function (result) {
      //   if(result.data && result.data.data[0].length > 0){debugger;
      //     RescheduleLead()
      //   }
      // })
    }
    
  }


  return (
    <>
      {Details && agentsList && <Modal
        {...props}
        size="md"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        className="AssignedLeadPopup"
      >
        <Modal.Header closeButton>
          {Rescheduled ? <h3>Re-Schedule Lead</h3> : <h3>Assign Lead</h3>}

          {/* {!confirmPopup && <LeadDetails Rescheduled={Rescheduled} AppointmentSlots={AppointmentSlots} Details={Details} slotidChange={slotidChange} activeTab={activeTab} AppointmentDate={AppointmentDate} 
          dateObj={dateObj} AppDate={AppDate} hours={hours}/>} */}

          {!confirmPopup && <LeadDetails Rescheduled={Rescheduled} AppointmentSlots={AppointmentSlots} Details={Details} slotidChange={slotidChange} activeTab={activeTab} AppointmentDate={AppointmentDate} 
          dateObj={dateObj} AppDate={AppDate} hours={hours}/>}

          {confirmPopup &&
            <>
              {/* {Rescheduled ? <p>Please save to rescheduled the appointment</p> : <p>Please click on save to reassigned the advisor</p>} */}
              <p>Please click on save</p>
              <Button color="primary" onClick={saveData}>Save</Button>{' '}
            </>
          }

        </Modal.Header>

        {!confirmPopup && !Rescheduled && <Modal.Body>
          <Row className="padding15">

            <Col xs={4} sm={4} className="text-center"> <label className="heading">Assign Lead to</label></Col>
            {agentsList.length > 0 ? <Col xs={8} sm={8}>
              {/* <div class="form-check">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="optradio" />Rahul Kr
                <p><img src="/fosTlDashboard/map.svg" /><b> 3 Km away </b> | Currently 2-lead in same slot</p>
              </label>
            </div>
            <hr />
            <div class="form-check">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="optradio" />Vishal Mehta
                <p><img src="/fosTlDashboard/map.svg" /><b> 3.25 Km away </b> | Currently 1-lead in same slot</p>
              </label>
            </div>
            <hr />
            <div class="form-check">
              <label class="form-check-label">
                <input type="radio" class="form-check-input" name="optradio" />Gunjan sharma
                <p><img src="/fosTlDashboard/map.svg" /><b> 5 Km away</b> | | No lead in same slot</p>
              </label>
            </div> */}

              {agentsList.map((agent) =>
                <div class="form-check">
                  <label class="form-check-label">
                    <input type="radio" class="form-check-input" name="optradio" onChange={getAgent} value={agent.AgentID} checked={toggleChecked == agent.idx}/>{agent.UserName} {agent.ProcessID == 7 && <b>(Store)</b>}
                    <p><img src="/fosTlDashboard/map.svg" /><b> {agent.Distance.toFixed(2)} Km away</b> | | {agent.SlotLeadCount > 0 ? agent.SlotLeadCount + " lead in same slot" : "No lead in same slot"}</p>
                  </label>
                </div>
              )}

            </Col> : "No advisor near to this Customer"}
          </Row>

        </Modal.Body>}

        {!confirmPopup && <button className="confrimBtn" onClick={confirmation}>Confirm</button>}

        {/* {!confirmPopup && !Rescheduled && agentsList.length>0  && <button className="confrimBtn" onClick={confirmation} >Confirm1</button>} */}

      </Modal>}

      {/* {confirmPopup &&  <Modal
    {...props}
    size="md"
    aria-labelledby="contained-modal-title-vcenter"
    centered
    className="AssignedLeadPopup">

      <Modal.Header closeButton>
        <h3>Are you Sure You Want To Save The Data</h3>
      </Modal.Header>
      <Modal.Footer>
            <Button color="primary" onClick = {saveData}>Save</Button>{' '}
            <Button color="secondary" >Cancel</Button>
          </Modal.Footer>
      </Modal>} */}

    </>
  )

}

// export default AssignedLeadPopup;

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(AssignedLeadPopup);