const axios = require("axios");
const cache = require('memory-cache');
const CryptoJS = require("crypto-js");
const conf = require("./env_config");
const LoggerController = require("./modules/Loggerdb/Controller");
const LoggerMethods= require("./modules/Loggerdb/Methods");
const { GetParsedConfigFromCache, GetParsedConfigLocal, GetAgentDetails, APIListCollection } = require('./modules/common/CommonMethods');

const CACHE_DURATION = 30 * 60 * 1000;


function parseCookies(request) {
  var cookies = {};
  request.headers && request.headers.cookie && request.headers.cookie.split(';').forEach(function (cookie) {
    var parts = cookie.match(/(.*?)=(.*)$/);
    cookies[parts[1].trim()] = (parts[2] || '').trim();
  });
  return cookies;
}

function IsInternalRequest(req) {
  try {
    const IsInternalDomain = parseCookies(req)[conf.INTERNAL_DOMAIN_COOKIE];
    let result = false;
    if (IsInternalDomain && (IsInternalDomain !== conf.INTERNAL_DOMAIN_VALUE)) {
      result =  true;
    }
    // console.log("result: IsInternalDomain: ", result)
    return result;
  } catch (err) {
    console.log('Inside IsInternalRequest: ', err);
  }
}

async function createLog(agentId, Method, Channel, query, ResponseText, err, IP) {
  try {
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = agentId
    logData.Exception = err.toString();
    logData.Method = Method;
    logData.Application = "MatrixDashboard";
    logData.Channel = Channel;
    logData.RequestText = JSON.stringify(query);
    logData.ResponseText = JSON.stringify(ResponseText);
    logData.Requesttime = responseTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixDashboard";
    logData.IP = IP;


    LoggerMethods.LogKafka(logData);
    //loggerdb.collection('Log_Collection').insertOne(logData);
  }
  catch (e) {
    // console.log(e);
  }
}

function Base64Decoding(value) {
  const decoded = CryptoJS.enc.Base64.parse(value);
  const details = decoded.toString(CryptoJS.enc.Utf8);
  return details;
}

function Base64Encoding(value) {
  const encoded = CryptoJS.enc.Utf8.parse(value);
  const details = CryptoJS.enc.Base64.stringify(encoded)
  return details;
}

async function ValidateToken({ url, body, name }) {
  try {
    const { userId, employeeId, token } = body;
    if (!employeeId || !token) {
      // console.log("ValidateToken", 1);
      return false;
    }

    let cachedToken = cache.get(employeeId);
    let result = false, response = null;

    if (name === 'mtx') {
      let data = {
        userid: userId,
        token: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if (response && response.data) {
          result = true;
          cache.put(employeeId, data, CACHE_DURATION);
        }
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          result = false;
        } else if (body.token !== cachedToken.token) {
          response = await axios.post(url, data);
          if (response && response.data) {
            result = true;
            cache.put(employeeId, data, CACHE_DURATION);
          }
        } else {
          result = true;
        }
      }

    } else if (name === 'bms') {
      try{
      let data = {
        UserId: userId,
        Token: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if ((response && response.IsLoggedIn) || (response?.data?.IsLoggedIn)) {
          result = true;
          cache.put(employeeId, data, CACHE_DURATION);
        }
        createLog(userId, "BMSAuthentication","",body,response,"","")
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          createLog(userId,"CheckCachedBMSTokenFalse","",body,cachedToken,"","");
          result = false;
        } else if (body.token !== cachedToken.Token) {
          response = await axios.post(url, data);
          if ((response && response.IsLoggedIn) || (response?.data?.IsLoggedIn)) {
            result = true;
            cache.put(employeeId, data, CACHE_DURATION);
          }
          createLog(userId, "BMSAuthentication","",body,response,"","")
        } else {
          result = true;
        }
      }
    }
    catch(e)
    {
      createLog(userId,"ValidateTokenBMS","",body,result, e.message,"")
    }
    } else if (name === 'dialer') {
      let data = {
        jwt: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if (response && response.data) {
          result = true;
          cache.put(employeeId, data, CACHE_DURATION);
        }
      } else {
        if (!(body.token) || !(cachedToken.jwt)) {
          result = false;
        } else if (body.token !== cachedToken.jwt) {
          response = await axios.post(url, data);
          if (response && response.data) {
            result = true;
            cache.put(employeeId, data, CACHE_DURATION);
          }
        } else {
          result = true;
        }
      }
    } else if (name === 'claim') {
      let claimUrl = `${url}/${employeeId}/${token}`;
      let dataToken = {
        token: token
      }

      let claimHeaders = {
        SourceKey: conf.SOURCE_KEY_CLAIM,
        ValidationKey: conf.VALIDATION_KEY_CLAIM
      }

      // console.log("ValidateToken claim", 2);
      // console.log("ValidateToken claim", claimUrl);
      if (!cachedToken) {
        response = await axios.get(claimUrl, { headers: claimHeaders });
        const { status, headers, data } = response;
        const logResponse = { status, headers, data };
        // console.log('claim response ', response);
       // createLog(employeeId, "ClaimResponse", "Authentication", claimUrl, logResponse, "", "")
        if (response && response.data && response.data.Data) {
          result = true;
          cache.put(employeeId, dataToken, CACHE_DURATION);
        }
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          result = false;
        } else if (body.token !== cachedToken.token) {
          response = await axios.get(claimUrl, { headers: claimHeaders });
          const { status, headers, data } = response;
          const logResponse = { status, headers, data };
          // console.log('claim response', response);
          //createLog(employeeId, "ClaimResponse", "Authentication", claimUrl, logResponse, "", "")
          if (response && response.data && response.data.Data) {
            result = true;
            cache.put(employeeId, dataToken, CACHE_DURATION);
          }
        } else {
          result = true;
        }
      }
    }
    createLog(userId, "ValidateToken","Auth","Url: "+url +" Body: "+JSON.stringify(body),result,"","")
    return result;

  } catch (err) {

    createLog(body? body.userId:null,"ValidateToken","","Url: "+url +" Body: "+JSON.stringify(body),"",err.message,"")
    console.log('Inside ValidateToken', err);
    return false;
  }

}

function getMtxPayload(cookie) {
  try {
    let details = "{}", body = {};
    if (cookie) {
      details = Base64Decoding(cookie);
    }

    // console.log("getMtxPayload", cookie, details)

    const { UserId, AsteriskToken, EmployeeId, RoleId } = JSON.parse(details);
    return {
      userId: UserId,
      employeeId: EmployeeId,
      token: AsteriskToken,
      roleId: RoleId || ""
    }
  } catch (err) {
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: "",
      roleId: ""
    }
  }
}

function getBmsPayload(cookie, userId) {
  try {
    // console.log("getBmsPayload", cookie, userId)
    return {
      userId: userId,
      employeeId: "BMS_" + userId,
      token: cookie
    }

  } catch (err) {
    createLog(userId,"getBmsPayload","",cookie,"",err.message,"")
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: ""
    }
  }

}

function getDialerPayload({ payload, userId }) {
  // console.log("getDialerPayload", payload, userId)
  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

function getClaimPayload({ payload, userId }) {

  //console.log("getClaimPayload", payload, userId)

  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

async function ValidateUser(req, AgentId) {
  let err;
  let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;
  let TrackingId = 0, Url = "", Body = "";
  try {
    let isValid = false;
    

    const mtxCookie = parseCookies(req)[conf.UUID_MTX];
    const bmsCookie = parseCookies(req)[conf.UUID_BMS];
    const dialerCookie = parseCookies(req)[conf.UUID_DIALER];
    const claimCookie = parseCookies(req)[conf.UUID_CLAIM];

    const mtxUrl = conf.VERIFY_TOKEN_MTX;
    const bmsUrl = conf.VERIFY_TOKEN_BMS;
    const dialerUrl = conf.VERIFY_TOKEN_DIALER;
    const claimUrl = conf.VERIFY_TOKEN_CLAIM;

    const userIdDialer = parseCookies(req)[conf.AGENTID_DIALER];
    const userIdClaim = parseCookies(req)[conf.AGENTID_CLAIM];

    const validationArr = [
      { url: mtxUrl, cookie: mtxCookie, name: 'mtx' },
      { url: dialerUrl, cookie: dialerCookie, name: 'dialer' },
      { url: claimUrl, cookie: claimCookie, name: 'claim' },
      { url: bmsUrl, cookie: bmsCookie, name: 'bms' },
    ]

    for (let i = 0; i < validationArr.length; i++) {
      let { url, cookie, name } = validationArr[i];
      let userId = '', token = '', employeeId = '', roleId=null;
      if (name === 'mtx') {

        userId = getMtxPayload(cookie).userId;
        employeeId = getMtxPayload(cookie).employeeId;
        token = getMtxPayload(cookie).token;
      } else if (name === 'bms') {

        userId = getBmsPayload(cookie, AgentId).userId;
        employeeId = getBmsPayload(cookie, AgentId).employeeId;
        token = getBmsPayload(cookie, AgentId).token;

      } else if (name === 'dialer') {

        userId = getDialerPayload({ payload: cookie, userId: userIdDialer }).userId;
        employeeId = getDialerPayload({ payload: cookie, userId: userIdDialer }).employeeId;
        token = getDialerPayload({ payload: cookie, userId: userIdDialer }).token;

      } else if (name === 'claim') {

        userId = getClaimPayload({ payload: cookie, userId: userIdClaim }).userId;
        employeeId = getClaimPayload({ payload: cookie, userId: userIdClaim }).employeeId;
        token = getClaimPayload({ payload: cookie, userId: userIdClaim }).token;
      }
      let body = { userId, employeeId, token };
      TrackingId = userId,
      Body = body;
      Url = url;
      isValid = await ValidateToken({ url, body, name });

      if(bmsCookie)
      {
        isValid=true;
        createLog(AgentId,"ValidateUser","Auth","Url: "+url +" Body: "+JSON.stringify(body), isValid,"","")
      }
     
      if (isValid) {
        if(name === 'mtx') {
          let details = await GetAgentDetails({ userId, cacheTime: CACHE_DURATION });
          body = { 
            userId, 
            token, 
            employeeId: details?.EmployeeId, 
            roleId: getMtxPayload(cookie).roleId.length > 0 ? parseInt(getMtxPayload(cookie).roleId) : null,
            processList: details?.processList || []
          };
        }
        if(name === 'dialer') {
          body = { userId: AgentId}
        }
        let value = IsInternalRequest(req);
        req['user'] = { ...body, IsInternalDomain: value};
        req['user']={userId:90549}
      break;
      }
    }

    return isValid;
  } catch (e) {
    console.log("INSIDE ValidateUser::", e);
    err = e;
    createLog(AgentId, "ValidateUserMethod", "Auth", JSON.stringify(req.headers, null, 2), "", e.message, IP);
    return false;
  }

}

async function Auth(req, res, next) {
  let TrackingId = 0;
  let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;
  try {

    if (process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      const SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      // console.log('Inside Auth ENV: ', process.env.ENVIRONMENT_MTX_DASH)
    } else {
      const SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
    }

    if (req.originalUrl.includes('/health.html')) {
      res.status(200).send("health page");
      return;
    }

    var isAllowURL = false;

    let isValid = false;
    let AgentId = null;
    AgentId = parseCookies(req)['AgentId'];
    TrackingId = AgentId;

   
        isValid = true;
      

    //console.log('env', process.env.ENVIRONMENT_MTX_DASH);

    if (isValid || process.env.ENVIRONMENT_MTX_DASH === "DEV_MTX_DASH") {
      next();
    }
    else {
      res.status(401).send("Please login in Matrix Or BMS to see this page or contact your administrator.")
    }

  } catch (err) {
    console.log('Inside Auth', err);
    createLog(TrackingId, "Auth", "", "", "", err, IP);
    res.status(500).send("Auth Error");
  }

}
module.exports = {
  Auth: Auth,
  Base64Decoding: Base64Decoding,
  Base64Encoding: Base64Encoding,
  ValidateUser: ValidateUser,
  ValidateToken: ValidateToken,
  createLog: createLog,
  parseCookies: parseCookies,
}