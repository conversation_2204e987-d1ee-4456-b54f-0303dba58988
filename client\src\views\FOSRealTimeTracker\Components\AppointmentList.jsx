import React, { useState } from "react";
import {
  Box,
  Popper,
  ClickAwayListener,  
 
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import AppointmentCard from "./AppointmentCard";

const AppointmentList = ({ totalAppointments }) => {

  // const { totalAppointments } = props;

  const [anchorEl, setAnchorEl] = useState(null);


  const handleClick = (event) => {
    // console.log("The event is ", event.currentTarget);
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };


  const handleClickAway = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
 

  return (

    totalAppointments ?
      <Box

      >
       
        <ClickAwayListener onClickAway={handleClickAway}>
          <div>
            <button
              className="AppoitmentsButton"
              onClick={handleClick}
            >
              {
                totalAppointments > 1 ? `${totalAppointments} Appointments` : `${totalAppointments} Appointment`
              } <InfoIcon />
            </button>

            {/* Popper <PERSON>ltip */}
            <Popper
              open={open}
              anchorEl={anchorEl}
              placement="top"
              sx={{ zIndex: 1200 }}
              modifiers={[
                {
                  name: "offset",
                  options: {
                    offset: [0, 10], // Adjust the Popper's position relative to the button
                  },
                },
              ]}
            >
              <AppointmentCard />
            </Popper>
          </div>
        </ClickAwayListener>

      </Box>
      :
      null

  );
}

export default AppointmentList;