const apiReferenceModule = "items";
const moment = require("moment");

// livechat/healthservices/getRooms/:userId
const get_room_by_userid = async function(req, res) {
  const apiReference = {
    module: apiReferenceModule,
    api: "get_room_by_userid"
  };
  try {
    let userId = req.params.userId;
    const query = {
      leadid: parseInt(userId)
    };
    let rooms = await db
      .collection("rocketchat_room")
      .find(query, {
        fields: {
          _id: 1,
          ts: 1,
          "servedBy.username": 1,
          "servedBy._id": 1,
          open: 1,
          msgs: 1,
          agentmsgs: 1,
          botmsgcount: 1,
          allagents: 1
        },
        sort: {
          ts: -1
        }
      })
      .toArray();
    let updated_rooms = [];
    let obj = {};
    for (const element of rooms) {
      if (element.servedBy && element.servedBy._id) {
        let user = await db.collection("users").findOne({
          _id: element.servedBy._id
        });
        obj = {
          rid: element._id,
          ts: element.ts,
          user: user.employeeId,
          managerId: user.manager,
          open: element.hasOwnProperty("open") ? true : false,
          msgs: element.msgs,
          botmsgcount: element.botmsgcount,
          agentmsgs: element.agentmsgs,
          allagents: element.allagents ? element.allagents : []
        };
      } else {
        obj = {
          rid: element._id,
          ts: element.ts,
          open: element.hasOwnProperty("open") ? true : false,
          msgs: element.msgs,
          botmsgcount: element.botmsgcount,
          agentmsgs: element.agentmsgs,
          allagents: element.allagents ? element.allagents : []
        };
      }
      updated_rooms.push(obj);
    }

    res.send({
      status: 200,
      data: updated_rooms,
      message: "Success"
    });
  } catch (err) {
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};
// "livechat/healthservices/getRoom/:rid
const get_room_by_rid = async function(req, res) {
  const apiReference = {
    module: apiReferenceModule,
    api: "get_room_by_rid"
  };
  try {
    let rid = req.params.rid;
    var room = await db.collection("rocketchat_room").findOne({
      _id: rid
    });
    res.send({
      status: 200,
      data: room,
      message: "Success"
    });
  } catch (err) {
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

//livechat/healthservices/UpdateChatCounter/:type
const update_chat_counter = async function(req, res) {
  const apiReference = {
    module: apiReferenceModule,
    api: "update_chat_counter"
  };
  try {
    // console.log("UpdateChatCounter Api call");
    const dptName = req.params;
    // console.log("departmentName", dptName);
    let startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
    const query = {
      departmentname: dptName,
      ts: {
        $gt: startDate
      },
      unassigned: {
        $ne: true
      },
      "servedBy._id": {
        $exists: 1
      }
    };
    let room = await db
      .collection("rocketchat_room")
      .find(query)
      .toArray();

    // console.log("room");
    // console.log(room.length);

    var dpt = await db.collection("rocketchat_livechat_department").findOne({
      name: dptName
    });

    const result = _.groupBy(room, function(currentObject) {
      return currentObject.servedBy._id;
    });
    if (result && result != null) {
      const agentList = Object.keys(result);
      agentList.forEach(agent => {
        // console.log("agent");
        // console.log(agent);
        let chats = result[agent];
        let openChats = chats.filter(item => item.open == true);
        let openChatCount = openChats.length;
        let query = {
          departmentId: dpt._id,
          agentId: agent
        };
        let update = {
          $set: {
            assigned: chats.length,
            count: openChatCount
          }
        };
        // await db.collection('rocketchat_livechat_department_agents').update(query, update);
      });
    }
    res.send({
      status: 200,
      data: {},
      message: "Success"
    });
  } catch (e) {
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

const CheckInternalIP = async function(req, res) {
  const apiReference = {
    module: apiReferenceModule,
    api: "CheckInternalIP"
  };
  try {
    // console.log("CheckInternalIP Api call");
    // console.log(req.params.ip);
    if (req.params && req.params.ip) {
      let position = req.params.ip.lastIndexOf(".");
      let ipvalue = req.params.ip.substring(0, position);
      // console.log(ipvalue);
      let chatInternalIPs = await db
        .collection("Chat_InternalIP")
        .find({})
        .toArray();
      if (
        chatInternalIPs &&
        chatInternalIPs[0].IpList.indexOf(ipvalue.toString()) > -1
      ) {
        return true;
      } else {
        return false;
      }
    }
    res.send({
      status: 200,
      data: result,
      message: "Success"
    });
  } catch (e) {
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

const closeChats = async function(req, res) {
  const apiReference = {
    module: apiReferenceModule,
    api: "closeChats"
  };
  try {
    let subQuery = {
      _updatedAt: {
        $gte: moment()
          .subtract(3, "days")
          .startOf("day")
          .toDate(),
        $lte: moment()
          .subtract(1, "days")
          .endOf("day")
          .toDate()
      },
      $or: [
        {
          open: true
        },
        {
          alert: true
        }
      ]
    };
    await db
      .collection("rocketchat_subscription")
      .updateMany(subQuery, { $set: { alert: false, open: false } });
    let query = {
      _updatedAt: {
        $gte: moment()
          .subtract(2, "days")
          .startOf("day")
          .toDate(),
        $lte: moment()
          .subtract(1, "days")
          .endOf("day")
          .toDate()
      },
      status: "open"
    };
    let inquiries = await db
      .collection("rocketchat_livechat_inquiry")
      .find(query)
      .project({
        rid: 1,
        _id: 0
      })
      .toArray();
    //console.log(inquiries);
    let rooms = inquiries.map(a => a.rid);
    // console.log(rooms);
    db.collection("rocketchat_livechat_inquiry").updateMany(
      {
        rid: {
          $in: rooms
        }
      },
      {
        $set: {
          status: "taken",
          takenBy: "admin"
        }
      },
      function(err, res) {
        if (err) {
          console.log(err);
          //throw err;
        }
      }
    );
    res.send({
      status: 200,
      data: inquiries,
      message: "Success"
    });
  } catch (e) {
    console.log(e);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

module.exports = {
  update_chat_counter: update_chat_counter,
  get_room_by_rid: get_room_by_rid,
  get_room_by_userid: get_room_by_userid,
  CheckInternalIP: CheckInternalIP,
  closeChats: closeChats
};
