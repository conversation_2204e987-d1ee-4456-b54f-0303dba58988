const tblList = require("../constants");
const methods = require("./FosMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../Libs/sqlHelper");


// async function getAppointmentSlots(req, res) {
//     try {
//         methods.getAppointmentSlots(req, res);
//     } catch (err) {
//         res.send({
//             status: 500,
//             error: err
//         });
//     }
//     finally {
//         // res.send({
//         //     status: 200,
//         //     message: 'Records are in process.'
//         // });
//         //await sql.close();
//     }
// }

// async function getAppointmentsBySlotId(req, res) {
//     try {
//         methods.getAppointmentsBySlotId(req, res);
//     } catch (err) {
//         res.send({
//             status: 500,
//             error: err
//         });
//     }
//     finally {
//         // res.send({
//         //     status: 200,
//         //     message: 'Records are in process.'
//         // });
//         //await sql.close();
//     }
// }

async function SetAppointmentData(req, res) {
        methods.SetAppointmentData(req, res);
}

async function SlotLeadAssignment(req, res) {
    methods.SlotLeadAssignment(req, res);
}

async function FosRealTimeStatus(req, res){
    methods.FosRealTimeStatus(req, res);
}

async function FOSCallingStatus(req, res){
    methods.FOSCallingStatus(req, res);
}

async function FetchAgentLatLong(req, res){
    methods.FetchAgentLatLong(req, res);
}

async function FetchAgentLatLongByTime(req, res){
    methods.FetchAgentLatLongByTime(req,res);
}

async function OTSData(req,res){
    methods.OTSData(req, res);
}
async function IsSocialMediaAgent(req, res){
    methods.IsSocialMediaAgent(req, res);
}

async function GetFosUserDetails(req,res){
    methods.GetFosUserDetails(req, res);
}


module.exports = {
    // getAppointmentSlots: getAppointmentSlots,
    // getAppointmentsBySlotId: getAppointmentsBySlotId,
    SetAppointmentData : SetAppointmentData,
    SlotLeadAssignment : SlotLeadAssignment,
    FosRealTimeStatus : FosRealTimeStatus,
    FOSCallingStatus : FOSCallingStatus,
    FetchAgentLatLong: FetchAgentLatLong,
    FetchAgentLatLongByTime: FetchAgentLatLongByTime,
    OTSData: OTSData,
    GetFosUserDetails: GetFosUserDetails,
    IsSocialMediaAgent:IsSocialMediaAgent
};