
import React from "react";
import {
  GetCommonData,GetCommonspData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from './Common/DataTableWithFilter';
import { fnDatatableCol, getUrlParameter } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


class FosAppointmentData extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      activePage: 1,
      root: "GetFosAppointmentData",
      PageTitle: "Appointment History ",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
    };
  const getvalue=(type,Id)=>
{
  switch(type)
  {
  case 'Income':
       if (Id==1)
          return '2 to 4 lacs';
      else if(Id==2)
          return '4 to 6 lacs';
      else if(Id==3)
          return '6 to 10 lacs';
      else 
         return '10 lacs+';
      break;
  case 'IncomeDocs':
    if (Id==1)
          return 'Yes';
      else if(Id==2)
          return 'No';
      else
          return 'Not available';
      break;
  case 'Education':
     if (Id==1)
          return 'Post graduate';
      else if(Id==2)
          return 'Graduate';
      else if(Id==3)
          return 'Diploma';
      else if(Id==4)
          return '12th pass';
      else if(Id==5)
          return '10th pass';
      else
         return 'below 10th pass';
      break;
    default: break;
 



  }

}
    this.columnlist = [
      {
        name: "AppointmentType",
        label: "Appointment Type",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true
      },
      {
        name: "AssignmentType",
        label: "Assigned to",
        type: "string",
        
        sortable: false,
        searchable: false
      },
      {
        name: "City",
        label: "Select City",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true
      },
      {
        name: "Address",
        label: "Address1",
        type: "string",
        sortable: true,
        //searchable: true
      },
      {
        name: "Address1",
        label: "Address2",
        type: "string",
        sortable: true,
        //searchable: true
      },
      {
        name: "Landmark",
        label: "Landmark/Street/Area",
        type: "string",
        sortable: true,
        //searchable: true
      },
      {
        name: "Pincode",
        label: "Pincode",
        type: "string",
        sortable: true,
        //searchable: true
      }, {
        name: "AppointmentDateTime",
        label: "Appointment Date Time",
        type: "datetime",
        sortable: true,
        //searchable: true  
      }, 
      {
        name: "Income",
        label: "Income",
        cell: row => 
                <div>{row.IncomeId ? getvalue('Income',row.IncomeId): 'N/A'}</div>,
        type: "string",
        sortable: true,
        //searchable: true  
      }, 
      {
        name: "Income Docs",
        label: "Income Docs",
        cell: row => 
                <div>{row.IncomeDocsId ? getvalue('IncomeDocs',row.IncomeDocsId): 'N/A'}</div>,
        type: "string",
        sortable: true,
        //searchable: true  
      }, 
      {
        name: "Education",
        label: "Education",
        cell: row => 
                <div>{row.EducationId ? getvalue('Education',row.EducationId): 'N/A'}</div>,
        type: "string",
        sortable: true,
        //searchable: true  
      }, 
      {
        name: "UpdatedOn",
        label: "UpdatedOn",
        type: "datetime",
        sortable: true,
        //searchable: true  
      }, {
        name: "Comments",
        label: "Comments",
        type: "string",
        sortable: true,
      }, {
        name: "SupplierName",
        label: "Supplier",
        type: "string",
        sortable: true,
        //searchable: true  
      }, {
        name: "PlanName",
        label: "Plan",
        type: "string",
        sortable: true,
        //searchable: true  
      },{
        id: "IsActive",
        name: "IsActive",
        label: "IsActive",
        type: "bool",
        sortable: true,
        //searchable: true  
      }
    ];

  }



  componentDidMount() {
    let leadid = getUrlParameter("leadid");
    this.props.GetCommonspData({
      root: "GetFosAppointmentData",
      c: "L",
      params: [{ LeadId: leadid }],
    },
    
    );
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
    }

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }

  
  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>

                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    
                    defaultSortField="IsActive"
                    defaultSortAsc={false}
                    data={Array.isArray(items)&& items.length>0?items[0]:[]}
                    printexcel={false}

                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    addRecord,
    GetCommonspData
  }
)(FosAppointmentData);