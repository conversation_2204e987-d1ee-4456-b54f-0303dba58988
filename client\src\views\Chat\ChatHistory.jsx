import React, { useState, useEffect } from "react";
import { Button, Form } from "react-bootstrap";
import { GetCommonData } from "../../store/actions/CommonAction";
import { connect } from "react-redux";
import DataTable from "react-data-table-component";
import moment from "moment";
import Datetime from "react-datetime";
import "react-datetime/css/react-datetime.css";
import "react-toastify/dist/ReactToastify.css";
import { Card, CardHeader, CardBody, CardTitle, Row, Col } from "reactstrap";
import { GetJsonToArray } from "../../utility/utility.jsx";


const columnlist = [
  {
    name: "LeadId",
    label: "LeadId",
    selector: "LeadId",
  },
  {
    label: "RoomID",
    name: "RoomID",
    selector: "RID",
  },
  {
    name: "ProductName",
    label: "ProductName",
    selector: "ProductName",
  },
  {
    name: "ReceivedOn",
    label: "ReceivedOn",
    selector: "ReceivedOn",
  },
];
const PrintExcelColumn = React.lazy(() => import("../Common/PrintExcelColumn"));
const ChatHistory = (props) => {
  const [items, setItems] = useState([]);
  const [root] = useState("Rooms");
  const [roomserachvalue, setRoomserachvalue] = useState("");
  const [leadserachvalue, setLeadserachvalue] = useState("");
  const [dateserachvalue, setDateserachvalue] = useState(
    moment().format("YYYY-MM-DD")
  );
  const [bool,setBool]=useState(false)
  const [pageTitle] = useState("ChatHistory");
  const getChatHistory = () => {
    let condition = [];
    if (roomserachvalue != "") {
      condition.push({ RID: roomserachvalue });
    }
    if (leadserachvalue != "") {
      condition.push({ LeadId: leadserachvalue });
    }
    if (dateserachvalue != "") {
      condition.push({ ReceivedOn: dateserachvalue });
    }

    if (
      roomserachvalue != "" ||
      leadserachvalue != "" ||
      dateserachvalue != ""
    ) 
    {
      props.GetCommonData({
        limit: 10,
        skip: 0,
        root: root,
        cols: GetJsonToArray(columnlist, "selector"),
        c: "R",
        con: condition,
        sort: { ts: -1 },
      });
    }
   
  };

  
  useEffect(() => {
    console.log("props.CommonData::",props.CommonData)
    if (!props.CommonData.isError) {
      setItems(props.CommonData[root]);
    }
  }, [props]);
 
  const searchRoom = (e) => {
    setRoomserachvalue(e.target.value);
  };
  const searchLead = (e) => {
    setLeadserachvalue(e.target.value);
  };
  const CallDateChange = (moment) => {
    if (moment._isAMomentObject) {
      setDateserachvalue(moment.format("YYYY-MM-DD"));
    } else {
      setDateserachvalue("");
    }
  };

  const handler = (row, e) => {
    const url = `https://matrixdashboard.policybazaar.com/client/chatroomhistory?rid=${row.RID}&dep=${row.ProductName}`;
    window.open(url, "_blank");
  };

  const customStyles = {
    rows: {
      style: {
        cursor: "pointer",
      },
    },
  };
  const isTableEmpty = items && items.length ? true : false
 
  return (
    <>
       <div className="content">
      <Row>
        <Col md="12">
          <Card>
            <CardHeader>
              <Row style={{"marginLeft":"30px"}} > 
                <Col md={11}>
                  <CardTitle tag="h4">{pageTitle}</CardTitle>
                </Col>
                
                <Col md={3}>
                  <Form.Control
                    type="text"
                    onChange={searchRoom}
                    placeholder={"Enter RoomId"}
                  />
                </Col>
                <Col md={3}>
                  <Form.Control
                    type="text"
                    onChange={searchLead}
                    placeholder={"Enter LeadId"}
                  />
                </Col>

                <Col md={3}>
                  <Datetime
                    value={new Date()}
                    dateFormat="YYYY-MM-DD"
                    value={dateserachvalue}
                    onChange={CallDateChange}
                    utc={true}
                    timeFormat={false}
                  />
                </Col>
                <Col md={1}>
                  <Button variant="primary" onClick={getChatHistory}>
                    Fetch
                  </Button>
                </Col>
                <Col md={1}>
                 { isTableEmpty? <PrintExcelColumn
                data={items}
                columnNames={columnlist}
                fileName={new Date().getTime().toString()}
              />:''}
                </Col>
             
            
              </Row>
            </CardHeader>
            <CardBody>
              
             
              <DataTable
                columns={columnlist}
                data={items}
                defaultSortFieldId={1}
                pagination
                striped={true}
                onRowClicked={handler}
                highlightOnHover
                customStyles={customStyles}
              />
            </CardBody>
          </Card>
        </Col>
      </Row>
      </div>
    </>
  );
};
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}
export default connect(mapStateToProps, {
  GetCommonData,
})(ChatHistory);