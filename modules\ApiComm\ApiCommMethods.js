const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");

exports.AddLeadValidation = function (req, res) {
    const url = conf.Comm_Api + "LeadPrioritization.svc/AddLeadValidation"
    // const url = "http://localhost:63818/LeadPrioritization.svc/AddLeadValidation"
    const body = [req.query.LeadId]
    
    const headers = {
        "content-type": "application/json" 
    }

    try {
        axios.post(url, body, { headers: headers }).then(response => {
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data[0]
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {

                res.send({
                    status: 500,
                    message: e
                });
            }

        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
   
}
exports.ValidateAddLeadToPriorityQueue = function(priorityLead,res)
{   
    const url = conf.MATRIXCOREAPI + "/onelead/api/LeadPrioritization/ValidateAddLeadToPriorityQueue"
    const body = priorityLead;
    const headers = {
        "AgentId": priorityLead.UserId,
        "Source": "dashboard",
        "Content-Type": "application/json",
        "authkey": conf.FOSAUTHKEY,
        "clientkey": conf.FOSCLIENTKEY

    }

    try {
        axios.post(url, body, { headers: headers }).then(response => {
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {

                res.send({
                    status: 500,
                    message: e
                });
            }

        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

exports.GetInvalidLeads = function(request, res){
    const url = conf.MATRIXCOREAPI + "/onelead/api/LeadPrioritization/GetInvalidLeadsFromNext5Leads"
    let body = request.body;
    const headers = {
        "Content-Type": "application/json",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY
    }
    try {
        axios.post(url, body, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: e
                });
            }

        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

exports.RemoveInvalidLeads = function(request, res){
    const url = conf.MATRIXCOREAPI + "/onelead/api/LeadPrioritization/RemoveInvalidLeadsFromNext5Leads"
    let body = request.body;
    body['RemoveBy'] = request.user?.userId || 0;
    const headers = {
        "Content-Type": "application/json",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY
    }
    try {
        axios.post(url, body, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: e
                });
            }

        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}