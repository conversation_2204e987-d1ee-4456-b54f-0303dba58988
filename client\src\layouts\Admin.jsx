
import React from 'react';
// javascript plugin used to create scrollbars on windows
import PerfectScrollbar from "perfect-scrollbar";
import { Route, Routes } from "react-router-dom";

import DemoNavbar from "components/Navbars/DemoNavbar.jsx";
import Footer from "components/Footer/Footer.jsx";
import Sidebar from "components/Sidebar/Sidebar.jsx";

import NotAccessable from "views/NotAccessable.jsx";

import routes from "routes.js";
import {
  GetDataDirect
} from "../store/actions/CommonAction";
import { getUrlParameter, getuser, getCookie, setCookie } from '../utility/utility.jsx';
import config from "../config";


import UnAuthenticated from "views/UnAuthenticated.jsx";
import ITAssetDetailSurvey from "views/ITAssetDetailSurvey.jsx";
import TCSSMEFeedback from "views/TCSSMEFeedback.jsx";
import AgentsFeedback from "views/AgentsFeedback.jsx";
import SurveyForm from "views/SurveyForm.jsx";
import { If, Then, Else } from 'react-if';
var ps;

class Dashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      backgroundColor: "black",
      activeColor: "info",
      mainclass: "main-panel full",
      inMatrix: true,
      user: {},
      location: '',

    };
    this.mainPanel = React.createRef();
  }

  componentWillMount() {
    var userid = null;
    if (getUrlParameter("u") != "") {
      // var userid = getCookie("AgentId");
      // if(getUrlParameter("u") != userid){
      //   this.props.history.push('/client/UnAuthenticated')
      // }

      userid = getUrlParameter("u");      
    }
    else {
      var userid = getCookie("AgentId");      
    }



    if (userid != "") {
      GetDataDirect({
        root: "UserDetails",
        UserID: userid,
        statename: "Users-" + userid,
        state: true
      }, function (result) {
        this.setState({ user: result[0] });
      }.bind(this));

      GetDataDirect({
        root: "FetchMenuMaster",
        UserId: userid,
        statename: "Menu-" + userid,
        state: false
      }, function (result) {
        this.setState({ Menu: result[0] });
      }.bind(this));



    }
    else {
      this.Logout();
    }
  }


  Logout() {
    setCookie("userid", "", -1);
    localStorage.clear();
    window.location.href = config.site_base_url;
  }

  componentDidMount() {
    if (navigator.platform.indexOf("Win") > -1) {
      ps = new PerfectScrollbar(this.mainPanel.current);
      document.body.classList.toggle("perfect-scrollbar-on");
    }
  }
  componentWillUnmount() {
    if (navigator.platform.indexOf("Win") > -1) {
      try{
        document.body.classList.toggle("perfect-scrollbar-on");
        ps.destroy();

      }
      catch(e){
        console.log(e);
      }
    }
  }
  componentDidUpdate(e) {
    if (e.history.action === "PUSH") {
      this.mainPanel.current.scrollTop = 0;
      document.scrollingElement.scrollTop = 0;
    }

  }
  handleActiveClick = color => {
    this.setState({ activeColor: color });
  };
  handleBgClick = color => {
    this.setState({ backgroundColor: color });
  };

  getLocation() {
    var loc = window.location.href;
    let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
    console.log(lastUrl);
    if (lastUrl == "OtpVerification") {
      return false;
    }
    if (lastUrl == "SmsInfo") {
      return false;
    }
    if (lastUrl == "KyaInfoVc") {
      return false;
    }
    return true;
  }

  checkAuthentication(prop, key) {
    const RoleIdCheck = prop?.RoleIdCheck || false;
    const RoleIds = prop?.RoleId || [];
    let IsRoleIdAuthorized = true;
    const { RoleId } = getuser() || {};

    if(RoleIdCheck && !RoleIds.includes(RoleId)) {
      IsRoleIdAuthorized = false;
    }

    if (config.byMenuSecurity) {
      return (<Route
        path={prop.layout + prop.path}
        component={prop.component}
        key={key} />)
    }
    if (!this.state.Menu) {
      return;
    }
    let hasMenu = false;
    for (let index = 0; index < this.state.Menu.length; index++) {
      const element = this.state.Menu[index];
      let path = prop.path.replace("/", "");
      if (element.URL.includes(path)) {
        hasMenu = true
        break;
      }
    }

    if (IsRoleIdAuthorized  && (hasMenu || !prop.authCheck)) {
      return (<Route
        path={prop.layout + prop.path}
        component={prop.component}
        key={key} />)
    }
    else {
      return (<Route
        path={prop.layout + prop.path}
        component={UnAuthenticated}
        key={key} />)

    }
  }



  render() {
    let RoleId = 0;
    if (this.state.user && this.state.user.RoleId) {
      RoleId = this.state.user.RoleId;
    }
    const location = this.getLocation();
    return (
      <div className="wrapper">
        <If condition={location}>
          <Sidebar
            {...this.props}
            routes={routes}
            bgColor={this.state.backgroundColor}
            activeColor={this.state.activeColor}
            inMatrix={this.state.inMatrix}
          />
        </If>
        <div className={(location) ? this.state.mainclass : ""} ref={this.mainPanel}>
          <If condition={location}>
            <DemoNavbar {...this.props} inMatrix={this.state.inMatrix} Logout={this.Logout} />
          </If>
          <Routes>
            {routes.map((prop, key) => {


              return this.checkAuthentication(prop, key)

            }
            )}
          </Routes>
          <Footer fluid />
        </div>
        {/* <FixedPlugin
          bgColor={this.state.backgroundColor}
          activeColor={this.state.activeColor}
          handleActiveClick={this.handleActiveClick}
          handleBgClick={this.handleBgClick}
        /> */}
      </div>
    );
  }
}

export default Dashboard;
