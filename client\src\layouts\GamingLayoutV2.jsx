import React, { createRef, useEffect } from 'react';
import routes from "../routes";

import {
  Routes,
  Route
} from "react-router-dom";
import Footer from "components/Footer/Footer.jsx";

const GamingLayoutV2 = () => {

  const mainPanel = createRef();

  useEffect(() => {
    if (navigator.platform.indexOf("Win") > -1) {
      document.body.classList.toggle("perfect-scrollbar-on");
    }
  }, []);

  return (
    <div className="wrapper">

      <div className='main-panel full'
        ref={mainPanel}
       >

        <Routes>
          {routes.map((prop, key) => {
            return (
              <Route
                path={prop.path}
                element={prop.component}
                key={key} />
            );
          })
          }
        </Routes>
        <Footer fluid />
      </div>

    </div>
  );
}

export default GamingLayoutV2;
