import React, { useState, useEffect } from "react";
import { GetCommonData as GetMongoData, UpdateData } from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import DeleteIcon from "@mui/icons-material/Delete";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import { Snackbar, Alert } from "@mui/material";

const QuickSightAccess = (props) => {
  const [employeeId, setEmployeeId] = useState("");
  const [employeeName, setEmployeeName] = useState("");
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quickSightData, setQuickSightData] = useState({});
  const [products, setProducts] = useState([]);
  const [productData, setProductData] = useState({});

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
  });
  
  const showSnackbar = (message, severity = "info") => {
    setSnackbar({ open: true, message, severity });
  };

  useEffect(() => {
    try {
      props.GetMongoData(
        {
          root: "Settings",
          con: { application: "matrixdashboard_1", key: "quicksightvisualization" },
          c: "M",
        },
        function (result) {
          if (result?.data?.data?.[0]?.value) {
            let mongoData = result.data.data[0].value;
            setQuickSightData(mongoData);
            setProducts(Object.keys(mongoData));
          }
        }
      );
    } catch (ex) {
      console.log(ex);
    }
  }, []);

  const handleProductChange = (e) => {
    let product = e.target.value;
    setSelectedProduct(product);
    setProductData(quickSightData[product]?.users || {});
  };

  const addUser = async () => {
    if (employeeId.trim() === "" || employeeName.trim() === "" || !selectedProduct) return;
  
    // Check if employeeId already exists
    if (productData[employeeId]) {
      showSnackbar("User ID already exists!", "error");
      return;
    } 

    const updatedUsers = { ...productData, [employeeId]: employeeName };
  
    // Update MongoDB using GetMongoData
    try {
      await props.UpdateData(
        {
          root: "Settings",
          querydata: { application: "matrixdashboard_1", key: "quicksightvisualization" },
          c: "M", // U = Update operation
        body: {
            [`value.${selectedProduct}.users.${employeeId}`]: employeeName, // Update specific user in the selected product
          },
        },
        function (result) {
          if (result?.status === 200) {
            console.log("User added successfully to MongoDB");
            setProductData(updatedUsers);
            // setQuickSightData((prev) => ({
            //   ...prev,
            //   [selectedProduct]: { ...prev[selectedProduct], users: updatedUsers },
            // }));
          } else {
            console.error("Error adding user:", result);
          }
        }
      );
    } catch (error) {
      console.error("Error adding user to MongoDB:", error);
    }
  
    setEmployeeId(""); // Clear input fields
    setEmployeeName("");
  };
  
  const deleteUser = async (employeeId) =>{
    try {
      await props.UpdateData(
        {
          root: "Settings",
          c: "M",
          querydata: {
            application: "matrixdashboard_1",
            key: "quicksightvisualization"
          },
          body: {
            [`value.${selectedProduct}.users.${employeeId}`]: "" // Remove the user
          },
          unset: "unset"
        },
        function (result) {
          if (result?.status === 200) {
            console.log("User deleted successfully from MongoDB");
  
            // Update state after deletion
            const updatedUsers = { ...productData };
            delete updatedUsers[employeeId];

            console.log(updatedUsers);
            setProductData(updatedUsers);
            
          } else {
            console.error("Error deleting user:", result);
          }
        }
      );
    } catch (error) {
      console.error("Error deleting user from MongoDB:", error);
    }
  }

  return (
    <div style={{ padding: "20px", textAlign: "center" }}>
      <h2 style={{ marginBottom: "20px" }}>QuickSight Access Panel</h2>

      {/* Product Dropdown */}
      <select onChange={handleProductChange} style={styles.dropdown}>
        <option value="">Select Product</option>
        {products.map((product) => (
          <option key={product} value={product}>
            {product}
          </option>
        ))}
      </select>

      <br />
      <br />

      {/* Add User Input Fields & Button */}
      {selectedProduct && (
        <div style={{ marginBottom: "20px", display: "flex", justifyContent: "center", gap: "10px" }}>
          <TextField
            label="Employee ID"
            variant="outlined"
            size="small"
            value={employeeId}
            onChange={(e) => setEmployeeId(e.target.value)}
          />
          <TextField
            label="Employee Name"
            variant="outlined"
            size="small"
            value={employeeName}
            onChange={(e) => setEmployeeName(e.target.value)}
          />
          <Button variant="contained" color="primary" onClick={addUser}>
            ➕ Add User
          </Button>
        </div>
      )}

      {/* User Table */}
      {productData && Object.keys(productData).length > 0 && (
        <table style={styles.table}>
          <thead>
            <tr>
              <th style={styles.th}>#</th>
              <th style={styles.th}>Employee ID</th>
              <th style={styles.th}>Name</th>
              <th style={styles.th}>Action</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(productData).map(([id, name], index) => (
              <tr key={id} style={index % 2 === 0 ? styles.stripedRow : {}}>
                <td style={styles.td}>{index + 1}</td>
                <td style={styles.td}>{id}</td>
                <td style={styles.td}>{name}</td>
                <td style={styles.td}>
                  <IconButton color="error" size="small" onClick={() => deleteUser(id)}>
                    <DeleteIcon />
                  </IconButton>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      <Snackbar
  open={snackbar.open}
  autoHideDuration={3000}
  onClose={() => setSnackbar({ ...snackbar, open: false })}
>
  <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
    {snackbar.message}
  </Alert>
</Snackbar>
    </div>
  );
};

// CSS Styles
const styles = {
  dropdown: {
    padding: "10px",
    fontSize: "16px",
    borderRadius: "5px",
    border: "1px solid #ccc",
  },
  table: {
    width: "80%",
    margin: "20px auto",
    borderCollapse: "collapse",
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
  },
  th: {
    border: "1px solid #ddd",
    padding: "12px",
    backgroundColor: "#007bff",
    color: "white",
    textAlign: "center",
  },
  td: {
    border: "1px solid #ddd",
    padding: "12px",
    textAlign: "center",
  },
  stripedRow: {
    backgroundColor: "#f9f9f9",
  },
};

// Redux Connection
function mapStateToProps(state) {
  return { CommonData: state.CommonData };
}

export default connect(mapStateToProps, { GetMongoData, UpdateData })(QuickSightAccess);
