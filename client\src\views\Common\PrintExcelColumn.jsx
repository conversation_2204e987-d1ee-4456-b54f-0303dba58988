
import React from "react";
import { Alert } from 'react-bootstrap';
import moment from 'moment';
import DataTable from "react-data-table-component";
import exportFromJSON from 'export-from-json';
import { useEffect } from "react";


const PrintExcelColumn = (props) =>{
    const { fileName, columnNames, data, downloadIcon=true } = props;
    // console.log(props)
    // console.log(columnNames);
    // console.log(data)

    // let mapColumnDataType = {} ;
    // let mapActualColumnName = {} ;
    
    useEffect(()=>{
        if(data && data.length!=0 && props.download){
            handleDownload()
            //props.downloadExcel();
        }
        if(props.download){
            props.downloadExcel();
        }
    },[props.download])

    const UpdatedDatakey = (data) => {
        let result = data && data.map((item) => {
            let obj = {};

            for (let index = 0; index < columnNames.length; index++) {
                const element = columnNames[index];
             
        
                if(element.selector=="CommentByEmpId")
                {
                    let temp1= item.CommentByEmpId? item.CommentByEmpId:"";
                    let temp2= item.CommentCreatedOn?  moment(item?.CommentCreatedOn).format("Do MMM, h:mm"):"";
                    let temp3= item.LastComment ? item.LastComment:"";
                    let temp4 = temp1+ " "+temp2+" "+ temp3;
                    // temp=temp+ item.CommentCreatedOn ? moment(item.CommentCreatedOn).format("Do MMM, h:mm") :"";
                    // temp=temp+ item.LastComment ? item.LastComment:"";
                    obj[element.selector]= temp4;
                    
                }
                if(element.type === 'datetime') {
                    if(item[element.selector]){
                        //console.log(item[element.selector])
                        obj[element.name] = moment.utc(item[element.selector]).local(true).format("YYYY-MM-DD HH:mm:ss");
                    }
                    else{
                        if(item[element.selector]==null){
                            obj[element.name] = "";
                        }
                        else{
                            obj[element.name] = item[element.selector];
                        }
                    }
                } else {
                   if(item[element.selector]==null){
                        obj[element.name] = "";
                    }
                    else{
                        obj[element.name] = item[element.selector];
                    }
                }
                if(element.excelValue && element.excelValue === "FosSelfieStatus")
                {
                    let temp1= item.Status === 1 ? 'Pass' : item.Status === 0 ? "Fail" : "NA";                   
                    obj[element.name]= temp1;                    
                }
                
                if(element.excelValue && element.excelValue === "FosApptCompletedOn")
                {
                    let temp1= item.ApptCompletedOn === '1990-01-01 00:00:00' ? 'N.A' : item.ApptCompletedOn;                   
                    obj[element.name]= temp1;                    
                }                   
            }
        
            return obj;
        });
        return result;
    }


    const handleDownload = ()=>{
        let UpdatedData= UpdatedDatakey(data) || [];
        //console.log(UpdatedData)
        const exportType =  exportFromJSON.types.xls;
        exportFromJSON({ data: UpdatedData, fileName, exportType });
    }
    
    return (
        <>
        {data && data.length!=0 && downloadIcon?<span className="downloadExcel" onClick={handleDownload}></span>:""}
        </>
    )

}

// class PrintExcelColumn extends React.Component {
//     constructor(props) {
//         super(props);
//         this.state = {
//             columns: this.props.columnNames
//         }
//     }
//     componentDidUpdate() {

//     }
//     componentDidMount() {
//     }
//     componentWillReceiveProps(nextProps) {
//         this.setState({ columns: nextProps.columnNames });        
//     }

//     renderValue(columns){
//         console.log("columns")
//             console.log(columns)
//         if(columns.type == 'datetime'){
//             return (col) => moment.utc(col[columns.selector]).local(true).format(columns.format)    
//         }
//         else
//         return columns.selector;
//     }

//     renderExcelColumnData(columns){
//         let ExcelColumns = [];

//         let index = 0;
//             if(columns.length) {
//                 columns.forEach(element => {
//                     ExcelColumns.push(
//                         <ExcelColumn key={index} 
//                         label={element.name} 
//                         value={this.renderValue(element)}/>      
//                     )
//                     });
//                 }
//             return ExcelColumns;
//     }
//     render() {

//         if (this.props.columnNames && this.props.columnNames.length > 0) {
//            return(
//             <ExcelFile filename={this.props.fileName} element={<span className="downloadExcel"></span>}>
//             <ExcelSheet data={this.props.data} name="Conference">
//             {this.renderExcelColumnData(this.props.columnNames)}
//             </ExcelSheet>
//             </ExcelFile>
//            );   
           
//         }
//         else {
//             return null;
//         }
//     }
// }

export default PrintExcelColumn;
