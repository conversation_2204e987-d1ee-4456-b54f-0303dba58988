import React, { useState, useContext, useEffect, useRef } from 'react';
import { GoogleMap, LoadScript, Marker, DirectionsRenderer, InfoWindow, Polyline } from '@react-google-maps/api';
import moment from 'moment';

const center = {
    lat: 28.5355,
    lng: 77.3910,
};

const options = {
    strokeColor: '#FF0000',
    strokeOpacity: 0,
    icons: [
      {
        icon: {
          path: 'M 0,-1 0,1',
          strokeOpacity: 1,
          scale: 2,
        },
        offset: '0',
        repeat: '10px',
      },
    ],
  };

const GoogleMapView= React.memo(({initialCenterLatLong,mapData, singleAgentlatLongBYTime,latLong, showSingleAgentData, singleAgentlatLongBYTimeMarkers})=>{


   
    const [initialCenterLatLong1, setInitialCenterLatLong] = useState(initialCenterLatLong);
    const [directions, setDirections] = useState(null);
    const [skippedSingleAgentLatLongBYTime, setSkippedSingleAgentLatLongBYTime] = useState([]);
    const [selectedMarker, setSelectedMarker] = useState(null);
    const [map, setMap]= useState(null);
 
    const [polylineCurrent,setPolylineCurrent] = useState({
        current: null,
        destination: null
    })

    const infoWindow= useRef(null);

    const handleMarkerClick = (positionItem) => {
        setSelectedMarker(positionItem);
      };

      const GenerateInfoText=(data)=>{
        let text='';
        

        switch (data['subStatusId']){
            case 2193:
                text='Started Journey at '+ moment(data.insertedAt).format('hh:mm:ss A');
            case 2194:
                text='Reached Customer Location at '+ moment(data.insertedAt).format('hh:mm:ss A') ;
            case 2124:
                text='Started Appointment on at '+moment(data.insertedAt).format('hh:mm:ss A');
            case 2003:
                text='Meeting Over at '+moment(data.insertedAt).format('hh:mm:ss A');
        }
        return <>{data.LeadId}<h6>{text}</h6></>

      }

      const FindInitials=(username)=>{


        const words = username.split(' ');
       

        if (words.length === 1) {
            return words[0]; // Return the entire first word
          } else {

        
            let initials = '';
      
        words.forEach(word => {
          if (word.length > 0) {
            initials += word[0]; 
          }
        });
        return initials.toUpperCase();
    }
      
      }


      useEffect(()=>{

        // console.log("The p ", polylineCurrent)

      },[polylineCurrent])

      useEffect(()=>{

        // console.log("000 ",singleAgentlatLongBYTimeMarkers);
        if(singleAgentlatLongBYTimeMarkers && singleAgentlatLongBYTimeMarkers.length>0)
        {

        // console.log("111 ",singleAgentlatLongBYTimeMarkers);
        let c, d;
        for(let i=0;i<singleAgentlatLongBYTimeMarkers.length;i++)
            {
                
                if(singleAgentlatLongBYTimeMarkers[i].subStatusId==2124)
                {
                    // console.log('2124', singleAgentlatLongBYTimeMarkers[i])
                    d = {
                        lat:singleAgentlatLongBYTimeMarkers[i].Lat,
                        lng:singleAgentlatLongBYTimeMarkers[i].Long
                    }
                    continue;
    
                }
                if(singleAgentlatLongBYTimeMarkers[i].isLastLatLong)
                {
                   
                    c = {
                        lat:singleAgentlatLongBYTimeMarkers[i].Lat,
                        lng:singleAgentlatLongBYTimeMarkers[i].Long
                    }
                    continue;
                   
                }
               
            }
            setPolylineCurrent({
                current:c,
                destination: d
            })
        }
   
      },[singleAgentlatLongBYTimeMarkers])
   

    useEffect(() => {

        

        if (singleAgentlatLongBYTime?.length > 0) {
            const directionsService = new window.google.maps.DirectionsService();

            const origin = { lat: singleAgentlatLongBYTime[0]?.Lat, lng: singleAgentlatLongBYTime[0]?.Long };
            const destination = { lat: singleAgentlatLongBYTime[singleAgentlatLongBYTime.length - 1]?.Lat, lng: singleAgentlatLongBYTime[singleAgentlatLongBYTime.length - 1]?.Long };
            const deltaLat = (destination.lat - origin.lat) / 2;
            const deltaLng = (destination.lng - origin.lng) / 2;
            let filteredArr = [];
            let numElements = Math.round(singleAgentlatLongBYTime.length / 10) + 1;
            for (let i = numElements; i < singleAgentlatLongBYTime.length - 1; i += numElements) {
                filteredArr.push(singleAgentlatLongBYTime[i]);
            }
            filteredArr.push(singleAgentlatLongBYTime[singleAgentlatLongBYTime.length - 1]);
           
            setSkippedSingleAgentLatLongBYTime(filteredArr);
            const waypoints = filteredArr.map(point => ({
                location: new window.google.maps.LatLng(point.Lat + deltaLat, point.Long + deltaLng),
            }));

            setInitialCenterLatLong(origin);


              directionsService.route(
                {
                  origin: origin,
                  destination: destination,
                  waypoints: waypoints.map(waypoint => ({
                    location: waypoint.location,
                    stopover: true
                  })),
                  travelMode: window.google.maps.TravelMode.DRIVING,
                },
                (result, status) => {
                  if (status === window.google.maps.DirectionsStatus.OK) {
                    setDirections(result);
                  } else {
                    console.error('Error fetching directions:', status);
                  }
                }
              );
        }
    }, [singleAgentlatLongBYTime, singleAgentlatLongBYTimeMarkers]);

   

    const mapContainerStyle = {
        width: '910px',
        height: '100vh',
    };

    const directionsOptions = {
        polylineOptions: {
            strokeColor: 'blue', // customize the color of the route line
            strokeWeight: 4, // customize the thickness of the route line
        },
        markerOptions: {
            icon: {
                url: '/lottery/whitCircle.png', // URL to your marker icon image
                scaledSize: { width: 7, height: 7 }, // specify the size of the icon
                anchor: { x: 10, y: 10 } // adjust the anchor to add margin top (e.g., y: 30 for 10px margin top)
    
            },
        }
    };

    // const handleMarkerHover=(marker, item)=>{
    //     let elementToRender= GenerateInfoText(item);
    //     // console.log("The marker is ", marker);
    //     console.log("mouse hover captured");

    //     if(infoWindow.current)
    //     {
    //         infoWindow.current.setOptions({
    //             content:elementToRender
    //         })
    //     }

    //     else{
    //      infoWindow.current = new window.google.maps.InfoWindow({
    //         content: elementToRender,
    //       });
    //     }
    //     infoWindow.current.open({
    //         // anchor: marker,
    //         map:map,
    //         shouldFocus: false,
    //       });
      
    //     //   marker.addListener('mouseout', () => {
    //     //     infoWindow.current.close();
    //     //   });
    // }

    const CustomMarker = ({positionItem, text, position, isLast }) => (
        <>
        {/* {console.log("posss ",positionItem)}; */}
        <Marker 
          
            onClick={() => handleMarkerClick(positionItem)}
            // onMouseOver={(e) => handleMarkerHover(e.marker, positionItem)}
        position={position} icon={{
          url: positionItem?.subStatusId == 2193 ?'/Images/start-travelling.png': 
               positionItem?.subStatusId == 2194 ? '/Images/stop-travelling.png' : 
               positionItem?.subStatusId == 2124 ? '/Images/meeting-appointment.png' : 
               positionItem?.subStatusId == 2003 ? '/Images/end-appointment.png' : 
    
               positionItem?.isLastLatLong ? '/Images/current-travelling.png' : null,
          scaledSize: new window.google.maps.Size(30, 30),
            labelOrigin: new window.google.maps.Point(15, 10),
        }}>
        
        </Marker>
          {/* {selectedMarker && (
            <InfoWindow
              position={{lat:selectedMarker.Lat,lng: selectedMarker.Long}}
              onCloseClick={() => setSelectedMarker(null)}
            >
              <div>
                <h3>{GenerateInfoText(selectedMarker)}</h3>
              </div>
            </InfoWindow>
          )} */}
        </>
    );


    const CustomMarkerAgent = ({positionItem, key, position }) => {
        const foundObject = mapData.find(obj => obj.UserId == positionItem.AgentId);
        return(
          <>
          <Marker 
              onClick={() => handleMarkerClick(positionItem)}
              label={{
                text: FindInitials(foundObject?.UserName || ''),
                color: '#0066ff', 
                fontWeight:'800'
              }}
              MarkerLabel={{
                color:'#fff',
                text:'rrrrr',
                fontWeight:1800
              }}
              position={position} 
              icon={{
                url: foundObject?.RealTimeStatusId == 1 ? '/Images/current-travelling.png': 
                foundObject?.RealTimeStatusId == 2 ? '/Images/meeting-appointment.png' : 
                foundObject?.RealTimeStatusId == 3 ? '/Images/calling-appointment.png' :
                foundObject?.RealTimeStatusId == 4 ? '/Images/idle-agent.png' :
                foundObject?.RealTimeStatusId == 5 ? '/Images/loggedoff-appointment.png' : null,
                scaledSize: new window.google.maps.Size(40, 40),
                labelOrigin: new window.google.maps.Point(18, -10),
              }}>
          </Marker>
          </>
        )
        };


    const GoogleMapOptions = {
        mapTypeControlOptions: {
          mapTypeIds: [] // This removes all map types (including Map and Satellite)
        },
        zoomControl: true, // If you want to keep zoom control
        fullscreenControl: true, // If you want to keep fullscreen control
      };

    

    return(
        <>
       
          <></>
          {/* {console.log("The map is triggered")} */}
             <GoogleMap
                 mapContainerStyle={mapContainerStyle}
                 zoom={12}
                 center={initialCenterLatLong1}
                 options={GoogleMapOptions}
                //  onLoad={onLoad}
                 >
             {
        polylineCurrent.current && polylineCurrent.destination &&
        <>
        {console.log("polyline ",polylineCurrent)}
                <Polyline
                path={[polylineCurrent.current, polylineCurrent.destination]}
                options={options}
                />
                </>
    }
             {
             // singleAgentlatLongBYTime?.length > 0 &&
              showSingleAgentData ? 
              singleAgentlatLongBYTime?.length > 0 ?  
              <>
             {
             <>
             <DirectionsRenderer  options={directionsOptions} directions={directions} />
             {singleAgentlatLongBYTimeMarkers.map((position, index) => (
                <>
            
                 <CustomMarker
                 positionItem={position}
                 key={position.insertedAt}
                 position={{ lat: position.Lat, lng: position.Long }}
              
                 />

                 </>
                 ))}
             </>
             }
             </>
             :null
             : latLong?.length > 0 && 
             <>
             {latLong.map((position, index) => (
                 <CustomMarkerAgent
                 positionItem={position}
                 key={position.AgentId}
                 position={{ lat: position.Lat, lng: position.Long }}
                 />
                 ))}

                 </>
             }
         </GoogleMap>
         

     
     </>
    )

})

export default GoogleMapView;
