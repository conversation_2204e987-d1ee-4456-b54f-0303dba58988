
import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists, GetAwsRecordingUrl
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser, fnDatatableCol, hhmmss, joinObject } from '../utility/utility.jsx';
//import { Multiselect } from 'multiselect-react-dropdown';
//import { MDBSelect } from "mdbreact";
//import Select from 'react-select'; 
//import makeAnimated from 'react-select/animated'; 
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDown';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import config from "../config.jsx";



import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import DateRange from "./Common/DateRange"
import 'react-datetime/css/react-datetime.css'



// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class MobileCallingReport extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "queueAbandonData",
            PageTitle: "Mobile Calling Report",
            ProductData: [],
            ProductId: 0,
            ReportDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
            ReportTime: null,
            startdate: moment().subtract(6, 'days').format("YYYY-MM-DD 00:00:00"),
            enddate: moment().format("YYYY-MM-DD hh:mm:ss"),
            maxdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
            showMoreInfoModal: false,
            MoreInfoData: [],
            addClass: "fa fa-play-circle",
            selectedIvrProduct: "",
            ivrType: "0",
            ivrProducts: [],
            ivrQueues: [],
            SelectedQueues: [],
            queueServerIp: "",
            selectedValue: [ { label: "Select IVR Queues*", value: "0" }],
            ConfType: '',
            addClassDisabled: '',
            confTypes: [],
            MobileAbandonReport: [],
            showDataTable: false,

        };
        this.ivrtypechange = this.ivrtypechange.bind(this);
        this.ivrproductchange = this.ivrproductchange.bind(this);
        this.conftypechange = this.conftypechange.bind(this);
        this.onSelect = this.onSelect.bind(this);

        this.columnlist = [
            {
                name: "CallId",
                selector: "callid",
                width: "150px",
                type: "string",
                cell: row => <div className="callid">{row.callid ? row.callid : "N.A"}</div>,
            },
            {
                name: "LeadId",
                selector: "leadid",
                searchable: true
            },
            {
                name: "dstchannel",
                selector: "dstchannel",
                hidden: true,
            },  
            {
                name: "userfield",
                selector: "userfield",
                hidden: true
            },
            {
                name: "queuename",
                selector: "queuename",
                searchable: true

            },
            {
                name: "ConfType",
                selector: "conference_type",
                cell: row => <div className="conference_type">{row.conference_type ? row.conference_type : "N.A"}</div>,
            },
            {
                name: "Calldate",
                selector: "calldate",
                cell: row => <div className="calldate">{row.calldate ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss"
                >{row.calldate}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "agentid",
                selector: "agentid",
                searchable: true,
            },
            {
                name: "agentno",
                selector: "agentno",
                searchable: true
            },
            {
                name: "duration",
                selector: "ringtime",
                searchable: true,
            },
            {
                name: "disposition",
                selector: "custom_disposition",
                type: "dropdown",
                searchable: true,
                config: {
                    root: "custom_disposition",
                    state: true,
                    statename: "custom_disposition",
                    data: [{ Id: "ANSWERED", Display: "ANSWERED" }, { Id: "NO ANSWER", Display: "NO ANSWER" }, { Id: "BUSY", Display: "BUSY" }, { Id: "FAILED", Display: "FAILED" }],
                }
            },
            {
                name: "Listen", cell: row =>(['Playback','Hangup'].indexOf(row.custom_disposition) > -1)?'No file found':
                <div className="listen">
                    {this.getHtmlListenMoreInfo(row)}
                </div>,
                width: "150px",  
            },
            // {
            //     name: "Listen", cell: row =>
            //       <div className="listen">
            //         {this.CreateRecordingURL(row)}
            //       </div>,
            //     width: "150px",  
            // }

        ];



        this.IvrTypeList = {
            config:
            {
                root: "Ivrtypelist",
                data: [{ Id: "Service", Display: "Service" }, { Id: "Sales", Display: "Sales" }, { Id: "Claim", Display: "Claim" }],
            }
        };

        this.ConfTypeList = {
            config:
            {
                root: "Conftypelist",
                data: [{ Id: "sales_to_service", Display: "Life Hot Call Transfer" }, { Id: "telimedical", Display: "Teli-Medical [BMS Transfer]" }, { Id: "telemax", Display: "Tele-Max" }, { Id: "tltransfer", Display: "TLTransfer" }
                    , { Id: "0", Display: "Other" }],
            }
        };


    }

    componentDidMount() {

        this.props.GetMySqlData({
          root: "getConferenceType",
        }, function (result) {
            this.setState({ confTypes: result?.data?.data });
        }.bind(this));
    
      }

    componentWillMount() {
        // this.props.GetMySqlData({
        //   root: this.state.root,
        //   startdate: this.state.startdate,
        //   enddate: this.state.enddate,
        //   conftype: 'sales_to_service',
        // }, function (result) {
        //   this.setState({ MobileCallingReport: result.data.data[0] });
        // }.bind(this));

    }

    fnDatatableCol() {

        var columns = fnDatatableCol(this.columnlist);
        return columns;
    }


    fetchAbandonData() {
        //debugger;
        if (this.state.ivrType == 0) {
        toast("Please enter ivrType", { type: 'error' });
        return;
          }
        if (this.state.selectedIvrProduct == '' || this.state.selectedIvrProduct == '0') {
        toast("Please enter ivrProducts", { type: 'error' });
        return;
        }
        if (this.state.selectedValue.length==1 && this.state.selectedValue[0].label == "Select IVR Queues*") {
        toast("Please enter ivrQueues", { type: 'error' });
        return;
        }  
          
        this.setState({addClassDisabled:'disabled'});

        let queues = this.state.selectedValue;

        var serverip = queues.map(function (val) {
            return val.serverip;
        });

        var queuestring = queues.map(function (val) {
            return val.label;
        });
        var selectedqueues = queuestring.join("','");

        this.props.GetMySqlData({
            root: this.state.root,
            startdate: this.state.startdate,
            enddate: this.state.enddate,
            queues: selectedqueues,
            conftype: this.state.ConfType,
            queueServerIp: serverip[0],
        }, function (result) {
            if(result?.data?.data?.length == 0){
                this.setState({ MobileAbandonReport: [] ,addClassDisabled:'', showDataTable: 'true'});
            } else{
            this.setState({ MobileAbandonReport: result?.data?.data ,addClassDisabled:'', showDataTable: 'true'});
            }
        }.bind(this));

    }

    fetchProductData() {

        if(this.state.ivrType == "0"){
            return;
        }

        this.props.GetMySqlData({
            // root: "getProduct",
            // con: [{ "ivrtype": this.state.ivrType }],
            root: "getProductByIvrType",
            ProductType: this.state.ivrType,
        }, function (result) {
            this.setState({ ivrProducts: result?.data?.data });
        }.bind(this));

    }

    fetchQueueData() {

        //debugger;
        if(this.state.selectedIvrProduct == "0"){
            return;
        }
        this.props.GetMySqlData({
            // root: "getIvrQueue",
            // con: [{ "ivrtype": this.state.ivrType, "product": this.state.selectedIvrProduct }],
            root: "getQueuesByIvrProduct",
            ProductType: this.state.ivrType,
            ProductId: this.state.selectedIvrProduct,
     
        }, function (result) {
            this.setState({ 
                 ivrQueues: result && result.data && result.data.data,    
            });   
            }.bind(this)
        );
    }


    play(number) {
        var audio = document.getElementById('audio_' + number);
        var icon = document.getElementById("play" + number);
        if (audio.paused) {
            audio.play();
            icon.classList.remove("fa-play-circle");
            icon.classList.add("fa-stop-circle");
        } else {
            audio.pause();
            audio.currentTime = 0
            icon.classList.remove("fa-stop-circle");
            icon.classList.add("fa-play-circle");
        }
    }

    // CreateRecordingURL(row) {

    //     let userfield = row.userfield;
    //     let dstchannel = row.dstchannel;
    //     let date = moment(new Date(userfield)).format("DD-MM-YYYY");
    //     let hour = moment(new Date(userfield)).format("H");
    //     let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
    //     let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
    //     let callid = row.callid;
    //     console.log(date, hour, datetime, phoneNo, callid);
    //     let url = config.api.s3recordingurl + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
    //     console.log(url);
    //     if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
    //         return <span class='NotFound'>File not found</span>
    //     }

    //     GetFileExists(url, function (params) {
    //         if (params && params.status && params.status != 404) {
    //         }
    //         else {   
    //              //debugger;
    //             var listenelement = document.getElementById("span_" + row.row_num);
    //             if(typeof(listenelement) != 'undefined' && listenelement != null){ 
    //                 document.getElementById("span_" + row.row_num).innerHTML = "<span class='NotFound'>File not found</span>";
    //             }
    //         }
    //     });

    //     //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/12-05-2020/10/1589258668.10883-20200512101428-07509883158.wav";
    //     return <span id={"span_" + row.row_num}><audio src={url} id={"audio" + "_" + row.row_num}></audio>
    //         <i className={this.state.addClass} id={"play" + row.row_num} onClick={(e) => this.play(row.row_num, e)}></i>
    //     </span>;
    // }


    getHtmlListenMoreInfo(row){
        return  (<span id={"span_" + row.row_num} onClick={(e) => this.CreateMoreInfoRecordingURL(e, row)}>
          <i class="fa fa-play-circle listen"></i>
          </span>)
      }
    
      CreateMoreInfoRecordingURL(e, row){
        var audio = document.getElementById('audio2');
        var number = row.row_num;
        if (audio.paused) {
          document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
          let userfield = row.userfield;
          let dstchannel = row.dstchannel;
          let date = moment(new Date(userfield)).format("DD-MM-YYYY");
          let hour = moment(new Date(userfield)).format("H");
          let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
          let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
          let callid = row.callid;
          console.log(date, hour, datetime, phoneNo, callid);
          let recfilename = "recording/"+ date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
          //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
      
      
          if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
            document.getElementById('span_'+ number).innerHTML = 'No File Found';
            return;
          }
         
          //debugger;
              GetAwsRecordingUrl(recfilename, 'asterisk-log' ,function (results) {debugger;
                //debugger;
    
                console.log("results", results);
                if (results.data.status == 200) {
                  let url = results.data.data;
                  audio.src = results.data.data;
                  document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                  GetFileExists(url, function (params) {
                    //debugger;
                    if (params && params.status && params.status != 404) {
                      audio.onloadedmetadata = function() {
                        console.log(audio.duration)
                        //setTimeout(function () {
                          audio.play();
                          console.log(audio.duration);
                          if(audio.paused == false && audio.duration > 0 && audio.duration != 'Infinity' && audio.duration != 'NaN'){
                          document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-stop-circle listen"></i>';
                         
                          audio.onended = function() {
                            document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
                          };
                         
                          }else{
                          document.getElementById('span_'+ number).innerHTML = 'No File Found'; 
                          }
                       // }.bind(this), 500);
                      };
                    } else {
                      try {
                        document.getElementById("span_" + row.row_num).innerHTML = "File not found";
                      } catch (e) {
                        //console.log('error', e);        
                      }
                    }
                  });
                
                  
                } else {
                  document.getElementById('span_'+ number).innerHTML = 'No File Found';
                }
              }.bind(this));
           
    
    
        } else {
          //debugger;
          audio.pause();
          audio.currentTime = 0;
          document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
    
        }
    
      }
    ivrtypechange(e, props) {
        this.setState({
            ivrType: e.target.value,
            selectedValue: [{ label: "Select IVR Queues*", value: "0" }],
            ivrProducts: [{ Display: "Select Ivr Product*", Id: "0" }]
        }, function () {
            this.fetchProductData();
        });

    }

    ivrproductchange(e, props) {
        this.setState({
            selectedIvrProduct: e.target.value,
            ivrQueues : [],
            selectedValue : [{ label: "Select IVR Queues*", value: "0" }]
        }, function () {
            this.fetchQueueData();
        });
    }

    conftypechange(e, props) {
        this.setState({
            ConfType: e.target.value
        }, function () {
            //this.fetchConferenceData();
        });

    }

    handleChange = (e, props) => {

        if (e._isAMomentObject) {
            this.setState({ ReportDate: e.format("YYYY-MM-DD") }, function () {
                this.fetchCallBackData();
            });
        }


    }

    handleStartDateChange = (StartDateValue) => {
        const date = new Date();
        const startDate = new Date(StartDateValue);
    
        startDate.setDate(startDate.getDate()+6);
    
        if(date-startDate<=0){
            this.setState({
                startdate: StartDateValue,
                enddate: moment().format("YYYY-MM-DD 23:59:59")
            })
        } else {
            this.setState({
                startdate: StartDateValue,
                enddate: moment(StartDateValue).add(6, 'days').format("YYYY-MM-DD 23:59:59")
            })
        }
      }
    
      handleEndDateChange = (currentDate) => {
        this.setState({ enddate: currentDate});
      }
    

    onSelect(selectedList, selectedItem) {
        debugger;
        console.log("selectlist",selectedList);
        const newselectedList = selectedList.filter(task => task.label !== 'Select IVR Queues*');
        this.setState({ selectedValue: newselectedList });
    }

    render() {
        const columns = this.columnlist;
        //const columns = this.fnDatatableCol();
        const moreinfocolumns = this.moreinfolist;
        const { PageTitle, MobileAbandonReport, showAlert, AlertMsg, AlertVarient, ReportTime, MoreInfoData } = this.state;
        console.log("MobileAbandonReport", MobileAbandonReport);
        console.log("columns", columns);
        let selectedLeads = [];

        return (
            <>
                {/* <div className="content">
                    <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>
                                    <Row>
                                        <Col md={12}>
                                            <CardTitle tag="h5">{PageTitle}</CardTitle>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col md={2}>
                                            <Form.Group controlId="ivrtype_dropdown">
                                                <DropDownListMysql firstoption="Select Ivr Type*" value={this.state.ivrType} col={this.IvrTypeList} onChange={this.ivrtypechange}>
                                                </DropDownListMysql>
                                            </Form.Group>
                                        </Col>
                                        <Col md={2}>
                                            <Form.Group controlId="product_dropdown">
                                                <DropDown firstoption="Select Ivr Product*" items={this.state.ivrProducts} onChange={this.ivrproductchange}>
                                                </DropDown>
                                            </Form.Group>

                                        </Col>
                                        <Col md={3}>
                                            <MultiSelect
                                                options={this.state.ivrQueues} // Options to display in the dropdown
                                                value={this.state.selectedValue} // Preselected value to persist in dropdown
                                                onChange={this.onSelect} // Function will trigger on select event                                                
                                                //labelledBy={"Select IVR Queues"}
                                                selectAllLabel={"Select ALL IVR Queues"}
                                            />
                                        </Col>
                                        <Col md={3}>
                                            <Form.Group as={Col} md={12} controlId="conftype_dropdown">
                                                <DropDown firstoption="Select Conference Type" items={this.state.confTypes} onChange={this.conftypechange}>
                                                </DropDown>
                                            </Form.Group>
                                        </Col>

                    
                                        <Col md={4} style={{display: 'flex', gap: '1rem'}}>
                                            <DateRange 
                                                days={6}
                                                FromDate= {" "} 
                                                ToDate= {" "}
                                                startDate={this.state.startdate}
                                                endDate={this.state.enddate}
                                                EndDateFixed={true}
                                                onStartDate={this.handleStartDateChange}
                                                onEndDate={this.handleEndDateChange}
                                                >
                                            </DateRange>
                                        </Col>
                                        <Col md={1} style={{marginTop: '29px'}}>
                                            <Button className={this.state.addClassDisabled} variant="primary" onClick={() => this.fetchAbandonData()}>Fetch</Button>
                                        </Col>

                                    </Row>

                                </CardHeader>
                                <CardBody>
                                    {this.state.showDataTable && <DataTable
                                        columns={columns}
                                        data={(MobileAbandonReport && MobileAbandonReport.length > 0) ? MobileAbandonReport : []}
                                    />}
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>



                </div>
                <audio src="" id={"audio2"}></audio> */}
                <div className="container mt-5">
                    <h2 style={{textAlign: 'center'}}>
                        ( This report is closed. If you still need to use this report, please connect with the dialer team once. )
                    </h2>
                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetMySqlData,
        GetDataDirect,

    }
)(MobileCallingReport);