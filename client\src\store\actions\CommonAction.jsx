import axios from "axios";
import config from "../../config.jsx";
import constants from "../../Constants/Constants.jsx";
import { getuser } from "utility/utility";
import { getAgentId } from "utility/utility.jsx";


axios.interceptors.response.use(response => {
  return response;
}, error => {
  return error;
});

const GetCommonData_fetched = Todos => {

  if (Todos.dataToSend.state && Todos.dataToSend.statename) {
    localStorage.setItem(Todos.dataToSend.statename, JSON.stringify(Todos.data));
  }

  return {
    type: constants.GET_COMMON_SUCCESS,
    payload: Todos.data,
    root: Todos.dataToSend.statename ?? Todos.dataToSend.root
  };
};

const GetCommonData_fetch_error = error => {
  return {
    type: constants.GET_COMMON_FAIL,
    payload: error
  };
};

export const GetCommonData = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }
  try {
    if ((dataToSend.state && localStorage.getItem(dataToSend.statename)) || localStorage.getItem(dataToSend.statename)) {

      return function (dispatch, getState) {
        let data = JSON.parse(localStorage.getItem(dataToSend.statename))
        dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
      }
    }
  }
  catch (e) {

  }


  return function (dispatch, getState) {
    axios
      .get(config.api.base_url + "/db/list/", {
        params: dataToSend
      })
      .then(data => {
        if (dataToSend.state && dataToSend.statename) {
          localStorage.setItem(dataToSend.statename, JSON.stringify(data.data.data));
        }
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetCommonspData = (dataToSend, cb) => {

  let endpoint = dataToSend?.root || 'GetCommonspData';
  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    axios
      .get(config.api.base_url + "/db/listsp/" + endpoint, {
        params: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetCommonspDataV2 = (dataToSend, cb) => {
  try {
    let endpoint = dataToSend.root || '';
    if (dataToSend.data != null || dataToSend.data != undefined) {
      return function (dispatch, getState) {
        dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
      };
    }

    return function (dispatch, getState) {
      axios
        .get(config.api.base_url + "/db/listspV2/" + endpoint, {
          params: dataToSend
        })
        .then(data => {
          if (cb) {
            cb(data);
          }
          dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
        })
        .catch(error => {
          dispatch(GetCommonData_fetch_error(error, dataToSend));
        });
    };
  } catch (err) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetch_error(err, dataToSend));
    };
  }
};



const InsertData_fetched = Todos => {

  return {
    type: constants.INSERT_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const InsertData_fetch_error = error => {
  return {
    type: constants.INSERT_COMMON_FAIL,
    payload: error
  };
};

export const InsertData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/insert/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(InsertData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(InsertData_fetch_error(error));
      });
  };
};


const UpdateData_fetched = Todos => {

  return {
    type: constants.UPDATE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const UpdateData_fetch_error = error => {
  return {
    type: constants.UPDATE_COMMON_FAIL,
    payload: error
  };
};

export const UpdateData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/update/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(UpdateData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(UpdateData_fetch_error(error));
      });
  };
};

const DeleteData_fetched = Todos => {

  return {
    type: constants.DELETE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const DeleteData_fetch_error = error => {
  return {
    type: constants.DELETE_COMMON_FAIL,
    payload: error
  };
};

export const DeleteData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/delete", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(DeleteData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(DeleteData_fetch_error(error));
      });
  };
};


export const GetDataDirect = (dataToSend, cb) => {

  try {
    if (!dataToSend.ClearLocalStorage && ((dataToSend.state && localStorage.getItem(dataToSend.statename)) || localStorage.getItem(dataToSend.statename))) {

      let data = JSON.parse(localStorage.getItem(dataToSend.statename));
      cb(data);
      return;
    }
  }
  catch (e) {

  }
  //return function (dispatch, getState) {
  axios
    .get(config.api.base_url + "/db/list/", {
      params: dataToSend
    })
    .then(data => {
      if (dataToSend.state && dataToSend.statename) {
        localStorage.setItem(dataToSend.statename, JSON.stringify(data?.data?.data));
      }
      cb(data?.data?.data);

      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeAgentData = (manager, context, cb) => {

  let url = config.api.realtimeurl + "RealTimeAgentStatus?managercode=" + (context == "" ? manager : "") + "&context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      cb(error);
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeSingleAgentData = (assignedagent, cb) => {

  let url = config.api.realtimeurl + "getagentrealtime/" + assignedagent + "/";

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetQualityReport = (encodedId, cb) => {

  let url = config.api.aiUrl + "getQualityReport?id=" + encodedId;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};
export const PostIncentiveFormData = (formdata, cb) => {
  try {
    debugger;
    let url = config.api.base_url + "/db/UploadIncentiveFile";
    const headers = {
      'Content-Type': 'multipart/form-data',
    }
    axios
      .post(url, formdata
        , {
          headers: headers
        })
      .then(data => {
        if (cb) {
          cb(data);
        }
        console.log(data);
      })
      .catch(error => {
      });

  } catch (err) {
    console.log(err);
  }

};
export const PostMonthlyIncentiveFormData = (formdata, cb) => {
  debugger;

  let url = config.api.base_url + "/db/UploadMonthlyIncentiveFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};
export const PostFileUploadData = (formdata, cb) => {
  debugger;

  let url = config.api.base_url + "/db/FileUpload";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};


export const PostUploadStoryFormData = async (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadStoryFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  return await axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      cb(data);
      console.log(data);
    })
    .catch(error => {
    });

};

export const PostUploadVideoFormData = async (formdata) => {
  let url = config.api.base_url + "/db/UploadVideoFile";

  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  return await axios
    .post(url, formdata
      , {
        headers: headers
      })
  // .then(data => {
  //   cb(data);
  //   console.log(data);
  // })
  // .catch(error => {
  // });

};
export const PostAgentChatFileData = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadChatAgentFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};
export const UploadFileToS3BucketPvtService = (formdata, cb) => {
  let url = config.api.MatrixCoreURL + "api/UploadFile/UploadFileToS3BucketPvt";
  const headers = {
    "Content-Type": "multipart/form-data",
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*"
  }
  axios
    .post(url, formdata
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const ProcessUploadIncentiveFile = (row, cb) => {
  let url = config.api.base_url + "/db/ProcessUploadIncentivefile?rid=" + row.Id + "&ProductId=" + row.ProductId
    + "&IncentiveMonth=" + row.IncentiveMonth;

  axios
    .get(url)
    .then(data => {

      cb(data);
    })
    .catch(error => {
    });

};
export const GetRealTimeQueueData = (context, cb) => {

  let url = config.api.realtimeurl + "RealTimeData?context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetWaitingAssignedCallData = (queuename, cb) => {

  let url = config.api.waitingAssignedQueue + "?queue=" + queuename;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRecordingName = (calldataid, cb) => {

  let url = config.api.recordingnameurl + calldataid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetAwsRecordingUrl = (key, bucket, cb) => {

  let myBucket = bucket;
  if (bucket === undefined) {
    myBucket = "newcctecbuckt";
  }
  let url = config.api.awsrecordingurl + "?key=" + key + "&bucket=" + myBucket;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeTotalData = (context, cb) => {

  let url = config.api.realtimeurl + "RealTimeData?context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const ResetTokenDidUpdate = (userid, cb) => {

  let url = config.api.realtimeurl + "resettoken/" + userid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const SetAgentStatusBlocked = (managerid, agentCode, bookingid, grade, cb) => {

  //let url = config.api.base_url + "setAgentStatus.php?emp_id=" + agentCode + "&requested_by=" + managerid + "&leadid=" + bookingid + "&grade=" + grade + "&status=blocked";
  // .get(config.api.base_url + "/dialerapi/setAgentStatus", {
  //   params: dataToSend
  // })
  let dataToSend = {
    agentCode,
    managerid,
    bookingid,
    grade
  }

  axios
    .get(config.api.base_url + "/dialerapi/setAgentStatus", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const TransferCallThirdParty = (agent, thirdpartynumber, campaign, bookingid, transfer_type, dtmf_no, insurer,
  application_number, transfer_agents, grade, blockedAgentid, claimid, claim_callid, productid, salesagent, isenc, countrycode, dialercode, isCreateLead, pushrecording, cb) => {

  let dataToSend = {
    agent,
    thirdpartynumber,
    campaign,
    bookingid,
    transfer_type,
    dtmf_no,
    insurer,
    application_number,
    transfer_agents,
    grade,
    blockedAgentid,
    productid,

    salesagent,
    isenc,
    countrycode,
    dialercode,
    action: "transfer",
    pushrecording


  }
  if (!!isCreateLead) {
    dataToSend['isCreateLead'] = isCreateLead;
  }

  if (transfer_type == 'claim_transfer') {
    dataToSend['claimid'] = claimid;
    dataToSend['claim_callid'] = claim_callid;
  }


  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const MergeCallThirdParty = (agent, cb) => {

  //let url = config.api.dialerApiUrl + "multi_conference.php?agent=" + agent + "&action=merge";
  let dataToSend = {
    agent,
    action: "merge"
  }
  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const UnHoldCallThirdParty = (agent, cb) => {

  // let url = config.api.dialerApiUrl + "multi_conference.php?agent=" + agent + "&action=unhold";

  let dataToSend = {
    agent,
    action: "unhold"
  }
  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const AgentToAgentCall = (leadid, serviceagent, salesagent, sericeagentphn, salesagentphn,
  uid, serviceserverip, salesserverip, cb) => {

  // let url = config.api.dialerApiUrl + "mob2mobcall.php?leadid=" + leadid + "&campaign=agentToagent" + "&emp=" + serviceagent +
  //   "&agentphone=" + sericeagentphn
  //   + "&emp2=" + salesagent + "&agent2phone=" + salesagentphn
  //   + "&uid=" + uid + "&server_ip=" + serviceserverip + "&serverip2=" + salesserverip + "&islocal=1"
  //   ;

  let dataToSend = {
    leadid,
    campaign: "agentToagent",
    emp: serviceagent,
    agentphone: sericeagentphn,
    emp2: salesagent,
    agent2phone: salesagentphn,
    uid: uid,
    server_ip: serviceserverip,
    serverip2: salesserverip,
    islocal: 1
  }
  axios
    .get(config.api.base_url + "/dialerapi/mob2mobcall", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetMoodleCourse = (productid, cb) => {

  let url = config.api.moodleurl + "course.php?productid=" + productid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetMoodleQuiz = (productid, cb) => {

  let url = config.api.moodleurl + "courseModule.php?productid=" + productid + "&type=quiz";

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetExportQuiz = (dataToSend, cb) => {
  let url = config.api.moodleurl + "quiz.php";
  axios
    .post(url, dataToSend
      //{"quizId":"10,13","courseId":"7,10","userId":"pw07725,pw05523","productId":"2","format":"export"}
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLoginLogoutHistory = (dataToSend, cb) => {
  let url = config.api.chatAgentUrl + "loginhistory/getloginhistory";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLoginLogoutDetails = (dataToSend, cb) => {
  let url = config.api.chatAgentUrl + "loginhistory/getlogindetailsperdep";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};
export const CustAgentVerifyOTP = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "CustAgentVerifyOTP";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetAgentCallDetails = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "GetAgentCallDetails";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetAgentCallLogs = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "GetAgentCallDetailsV2";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryQuiz = (dataToSend, cb) => {
  let url = config.api.moodleurl + "quiz.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const UpdateAgentChatParams = (dataToSend, cb) => {
  debugger;
  let url = config.api.chatAgentUrl + "departmentagent/updateagentchatparams";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      //cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryCourse = (dataToSend, cb) => {
  let url = config.api.moodleurl + "courseAttendance.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryLMS = (dataToSend, cb) => {
  let url = config.api.moodleurl + "lmsVisit.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const SendOTP = (dataToSend, cb) => {
  let url = config.api.SendOTP;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': 'cG9saWN5 YmF6YWFy'
  }
  axios
    .post(url,
      dataToSend
      , {
        headers: headers
      })
    .then(data => {

      cb(data);
    })
    .catch(error => {
    });
};

export const ValidateOTP = (dataToSend, cb) => {
  let url = config.api.ValidateOTP;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': 'cG9saWN5 YmF6YWFy'
  }
  axios
    .post(url,
      dataToSend
      , {
        headers: headers
      })
    .then(data => {

      cb(data);
    })
    .catch(error => {
    });
};

export const GetComunicationData = (dataToSend, cb) => {

  return function (dispatch, getState) {
    axios
      .get(config.api.ServiceAPI + dataToSend.root, {
        params: dataToSend.data
      })
      .then(data => {
        cb(data);
        //dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};
export const PostCommunicationData = (dataToSend, cb) => {

  return function (dispatch, getState) {
    axios
      .post(config.api.ServiceAPI + dataToSend.root,
        dataToSend.data,
        {
          withCredentials: true
        }
      )
      .then(data => {
        cb(data);
        //dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetFileExists = (url, cb) => {

  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
      if (cb) {
        cb(error);
      }
    });
};

export const GetChatMessages = (rid, dep, cb) => {
  let url = config.api.chatAgentUrl + "chat/getChatMessagesUsingRoom/" + rid + "?dep=" + dep;
  axios
    .get(url
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetDashboardUrl = (dataToSend, cb) => {
  let url = "https://cdp.policybazaar.com/quicksight/getdashboardurl";
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'userid': dataToSend.userid,
  }
  delete dataToSend['userid'];

  axios
    .post(url,
      dataToSend
      , {
        headers: headers
      })
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLeadRejectionData = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/rejectLeads";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const PostUserGrades = (formdata, cb) => {
  let url = config.api.base_url + "/db/uploadUserGrades";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });
};

export const SendReassignNotification = (params, cb) => {

  let url = config.api.realtimeurl + "setnotification";
  axios
    .post(
      url,
      params
    )
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const ReAssignedPODLead = (params, cb) => {
  let url = config.api.progressiveUrl + "ReAssignedPODLead/" + params.leadId + "/" + params.userId + "/" + params.assignedUserId;
  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};


export const resolveRedirectURL = (dataToSend, cb) => {
  axios
    .post(config.api.base_url + "/db/resolveRedirectURL",
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetDashboardLink = (dataToSend, cb) => {
  let url = config.api.DashboardApiUrl + "linkGenerate/genrateLinkLead";
  const headers = {
    'x-access-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
  }
  axios
    .post(url, dataToSend, {
      headers: headers,
      withCredentials: true
    }
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const LotteryData = (params, cb, method = 'get') => {
  let url = config.api.base_url + "/comm/" + params.root;
  if (method === 'post') {
    axios
      .post(url, params)
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  } else {
    axios
      .get(url, params)
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  }

};


export const AllocationFormData = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadAllocationFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const HniLogicMaster = (formdata, cb) => {
  let url = config.api.base_url + "/db/HniLogicMaster";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const UploadMatrixFiles = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadMatrixFiles";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const listsp = (dataToSend, cb, method = 'get') => {
  let url = config.api.base_url + "/db/listsp/";
  if (method === 'post') {
    axios
      .post(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  } else {
    axios
      .get(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  }

};


export const AgentSurvey = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/AgentSurvey";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const getqueuetwo = (type, proid, queues, cb) => {
  debugger;
  queues = queues.replaceAll("'", "");
  let url = `${config.api.dialerApiUrlInbound}?type=${type.toLowerCase()}&product=${proid}&queuename=${queues}`;
  //let url = "https://dialerqaapi.policybazaar.com/api/v2/dialer/getInboundQueueMappingData?type=service&product=4&queuename=teletata,testtermweb"
  console.log(url);
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const Ticketing = (startdate, enddate, UserName, EmployeeID, cb) => {
  let url = `${config.api.MatrixCoreURL}api/Ticket/GetMyTicketList`
  const request = {
    StartDate: startdate,
    EndDate: enddate,
    Type: 1,
    UserName: UserName,
    EmployeeID: EmployeeID
  }
  const headers = { "AgentId": getAgentId() }

  axios
    .post(url, request, { headers, withCredentials: true })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const RegisterCustomer = (dataToSend, cb) => {
  let url = config.api.base_url + "/registerCustomer";
  axios
    .post(
      url,
      dataToSend
    )
    .then(data => {
      let errorCodes = [400, 401, 402, 403, 404, 500, 501, 502, 503, 504];
      let errorData = {};
      if (data && data.response && data.response.status && errorCodes.includes(data.response.status)) {
        errorData = {
          error: data.response.data || "",
          status: data.response.status || 0
        }
        cb(errorData);
      } else if (cb) {
        cb(data.data);
      }
      //console.log(data);
    })
    .catch(error => {
      if (cb) {
        cb(error);
      }
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};


export const GetLotteryTickets = (userid, cb) => {
  debugger;
  let url = `${config.api.base_url}/comm/GetLottery?userid=${userid}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const GetCommonApiData = async ({ Url, Data, headers }) => {
  try {
    let response = await axios.get(Url, {
      params: Data
    },
      { headers: headers }
    );
    if (response.status === 200) {
      return {
        errorStatus: 0,
        data: response && response.data && response.data.data || ""
      }
    } else {
      return {
        errorStatus: 1,
        data: 'Error executing'
      }
    }
  } catch (err) {
    return {
      errorStatus: 1,
      data: err
    }
  }
};


export const PostCommonApiData = async ({ Url, Data, headers }) => {
  try {
    let response = await axios.post(Url, Data, { headers: headers });
    if (response?.data?.status === 201) {
      return {
        errorStatus: 0,
        data: response?.data?.data || "",
        info: response?.data?.info || "{}"
      }
    } else {
      return {
        errorStatus: 1,
        data: 'Error executing'
      }
    }
  } catch (err) {
    return {
      errorStatus: 1,
      data: err.toString()
    }
  }
};


export const SalesView = async (LeadId, cb) => {
  console.log(LeadId)
  let url = `${config.api.base_url}/db/openSalesView?LeadId=${LeadId}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const AddLeadValidation = async (LeadId, cb) => {
  console.log(LeadId)
  let url = `${config.api.base_url}/apicomm/AddLeadValidation?LeadId=${LeadId}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}
export const ValidateAddLeadToPriorityQueue = async (priorityLead, cb) => {
  //let url = `${config.api.base_url}/apicomm/ValidateAddLeadToPriorityQueue?LeadId=${LeadId}`;
  let url = `${config.api.base_url}/apicomm/ValidateAddLeadToPriorityQueue`;
  const headers = {
    'Content-Type': 'application/json',
  }
  return axios
    .post(url, priorityLead)
    .then(data => {
      if (cb) {
        return cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}

export const SetAppointmentData = async (obj, cb) => {
  console.log(obj)
  let url = `${config.api.base_url}/Fos/SetAppointmentData`;
  const headers = {
    'Content-Type': 'application/json',
  }
  // axios.get()
  axios
    .post(url, obj)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}

export const SlotLeadAssignment = async (obj, cb) => {
  debugger
  console.log(obj)
  let url = `${config.api.base_url}/Fos/SlotLeadAssignment`;
  const headers = {
    'Content-Type': 'application/json',
  }
  // axios.get()
  axios
    .post(url, obj)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}


export const GetAgentDetailsByManager = (obj, cb) => {
  let url = config.api.base_url + "/db/getUserlistbyManagerId";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const GetPaymentOverDueCountByManagerId = (obj, cb) => {
  let url = config.api.base_url + "/db/getPaymentOverDueCountByManagerId";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });
};

export const GetDuplicateLeadsData = (obj, cb) => {
  let url = config.api.base_url + "/db/getDuplicateLeadsData";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const GetAdditionalLDBulkUploadData = (obj, cb) => {
  let url = config.api.base_url + "/db/GetAdditionalLDBulkUploadData";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};
export const GetUniqueGroups = (obj, cb) => {
  let url = config.api.base_url + "/db/GetUniqueGroups";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const UpdateRenewalFileStatusService = (obj, cb) => {
  let url = config.api.base_url + "/db/UpdateRenewalFileStatus";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};
export const GetStagedFileDataService = (obj, cb) => {
  let url = config.api.base_url + "/db/GetStagedFileData";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const SendDuplicateOtp = async (request, cb) => {
  let url = `${config.api.base_url}/MatrixCoreApi/SendDuplicateOtp`;
  axios
    .post(url, request)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
}
export const VerifyDuplicateOtp = async (request, cb) => {
  let url = `${config.api.base_url}/MatrixCoreApi/VerifyDuplicateOtp`;
  axios
    .post(url, request)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
}

export const GetDuplicateLeadsDataByProductId = (obj, cb) => {
  let url = config.api.base_url + "/db/getDuplicateLeadsDatabyProductId";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const GetIsAllowedDuplicateSearch = (obj) => {
  let url = config.api.base_url + "/db/getIsAllowedDuplicateSearch";
  const headers = {
    'Content-Type': 'application/json',
  }
  return axios
    .post(url, obj
      , {
        headers: headers
      })

};

export const IsUserAttributesAuthorized = (obj) => {
  let url = config.api.base_url + "/db/IsUserAttributesAuthorized";
  const headers = {
    'Content-Type': 'application/json',
  }
  return axios
    .post(url, obj
      , {
        headers: headers
      })

};


export const GetPendingDocumentLeadList = async (DataToSend) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/GetPendingDocumentLeadList';
    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'PENDING_DOCUMENT_LEADLIST', Data: DataToSend },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    console.log('error is', err)
    return err
  }
}

export const GetPendingPFLead = async (DataToSend) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/GetPendingPFLead';
    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'PENDING_PF_LEAD', Data: DataToSend },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    console.log('error is', err)
    return err
  }
}

export const GetPendingDocLead = async (DataToSend) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/GetPendingDocLead';
    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'PENDING_DOC_LEAD', Data: DataToSend },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    console.log('error is', err)
    return err
  }
}

export const GetCJUrl = async (DataToSend) => {
  try {
    const Url = config.api.base_url + '/common/GetCommonApi/GetCJUrl';
    let { errorStatus, data } = await GetCommonApiData({
      Url,
      Data: { Type: 'GET_CJ_URL', Data: DataToSend },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    console.log('error is', err)
    return err
  }
}

export const GetFOSAgentStatus = async (AgentList, cb) => {
  try {
    let url = config.api.base_url + "/Fos/FOSRealTimeStatus";
    const headers = {
      'Content-Type': 'application/json',
    }
    let result = await axios
      .post(url, AgentList
        , {
          headers: headers
        });
    if (result.status == 200 && result.data.data) {

      cb(result.data.data, result.data.configVal ? result.data.configVal : null);
    }
  }
  catch (e) {
    console.log("The error is ", e);
  }

}

export const GetFosCallingStatus = async (AgentList, cb) => {
  try {
    let url = config.api.base_url + "/Fos/FOSCallingStatus";
    const headers = {
      'Content-Type': 'application/json',
    }
    let result = await axios
      .post(url, AgentList
        , {
          headers: headers
        });
    if (result.status == 200 && result.data.data) {
      cb(result.data.data);
    }
  }
  catch (e) {
    console.log("The error is ", e);
  }

}

export const RejectLead = async (requestData, cb) => {
  let url = config.api.base_url + '/MatrixCoreApi/RejectLead';
  let headers = {};
  return axios.post(url, requestData, headers);
}

export const CreateLead = async (requestData, cb) => {
  let url = config.api.base_url + '/MatrixCoreApi/CreateLead';
  let headers = {};
  return axios.post(url, requestData, headers);
}

export const FetchAgentLatLong = async (AgentList, cb) => {
  try {
    let url = config.api.base_url + "/Fos/FetchAgentLatLong";
    const headers = {
      'Content-Type': 'application/json',
    }

    let uniqueAgentSet = new Set(AgentList);
    let uniqueAgentList = Array.from(uniqueAgentSet);
    // console.log("unique ", uniqueAgentList)
    let result = await axios
      .post(url, uniqueAgentList
        , {
          headers: headers
        });
    if (result.status == 200 && result.data.data) {
      cb(null, result.data.data);
    }
    else {
      cb(result.status);
    }
  }
  catch (e) {
    cb(e)
    console.log("The error is ", e);
  }

}

export const FetchAgentLatLongByTime = async (body, cb) => {
  try {
    let url = config.api.base_url + "/Fos/FetchAgentLatLongByTime";
    const headers = {
      'Content-Type': 'application/json',
    }
    let result = await axios
      .post(url, body
        , {
          headers: headers
        });
    if (result.status == 200 && result.data.data) {
      cb(null, result.data.data);
    }
    else {
      cb(result.status);
    }
  }
  catch (e) {
    cb(e);
    console.log("The error is ", e);
  }

}


export const GetInvalidLeadsResponse = async (request, cb) => {
  let url = `${config.api.base_url}/apicomm/GetInvalidLeads`;
  axios
    .post(url, request)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
}

export const RemoveInvalidLeadsResponse = async (request, cb) => {
  let url = `${config.api.base_url}/apicomm/RemoveInvalidLeads`;
  axios
    .post(url, request)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
}

export const GetCouponRaiseRequest = async (cb) => {
  var UserId = getuser().UserID;
  let url = config.api.base_url + '/MatrixCoreApi/GetCouponRaiseRequest?UserID=' + UserId;
  let headers = {};
  axios
    .get(url, headers)
    .then(data => {
      cb(data);
    })
    .catch(error => {
      cb(error);
    });
}
export const FetchAgentPredefinedUrl = async (RequestId, cb) => {
  let url = config.api.base_url + '/MatrixCoreApi/FetchAgentPredefinedUrl?RequestId=' + RequestId;
  let headers = {};
  axios
    .get(url, headers)
    .then(data => {
      cb(data);
    })
    .catch(error => {
      cb(error);
    });
}

export const UpdateCouponRaiseRequest = async (request, cb) => {
  let url = `${config.api.base_url}/MatrixCoreApi/UpdateCouponRaiseRequest`;
  axios
    .post(url, request)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
}

export const AdultChildScoreFormData = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadAdultChildScoreFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const CityScoreFormData = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadCityScoreFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const GetCouponDataByLeadId = async (LeadId, cb) => {
  let url = config.api.base_url + '/MatrixCoreApi/GetCouponDataByLeadId?LeadId=' + LeadId;
  let headers = {};
  axios
    .get(url, headers)
    .then(data => {
      cb(data);
    })
    .catch(error => {
      cb(error);
    });
}

export const PayUScoreRankFormData = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadPayUScoreRankFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const FetchProcTableWithMenu = async (cb) => {
  try {
    let url = config.api.base_url + "/Common/FetchProcTableWithMenu";
    // const headers = {
    //   'Content-Type': 'application/json',
    // }
    let result = await axios
      .get(url);

    if (result.status == 200 && result.data.data) {
      cb(null, result.data.data);
    }
    else {
      cb(1);
    }
  }
  catch (e) {
    cb(1);
    console.log("The error is ", e);
  }

}

export const EditMenuEntityMapping = async (body, cb) => {
  try {
    let url = config.api.base_url + "/Common/EditMenuEntityMapping";
    const headers = {
      'Content-Type': 'application/json',
    }
    let result = await axios
      .post(url, body
        , {
          headers: headers
        });

    if (result.status == 200 && result.data.data) {
      cb(null, result.data.data);
    }
    else {
      cb(1);
    }
  }
  catch (e) {
    cb(1);
    console.log("The error is ", e);
  }

}

export const PostIncentiveData = async (formdata) => {
  let url = config.api.base_url + "/UploadIncentive/gethtmlFromExcel";

  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  return await axios
    .post(url, formdata
      , {
        headers: headers
      })
  // .then(data => {
  //   cb(data);
  //   console.log(data);
  // })
  // .catch(error => {
  // });

};

export const LeadOnlyView = async (leadid, cb) => {
  let res;
  try {
    let url = `${config.api.base_url}/MatrixCoreApi/GetLeadOnlyURL?Leadid=${leadid}`;
    const headers = {
      'Content-Type': 'application/json',
    }
    return await axios
      .get(url);

  }
  catch (e) {
    console.error("Unable to Open LeadOnly", e)
    return null
  }
}

export const GetUserDetailsBytoken = async (requestData) => {
  const url = `${config.api.MatrixTicket}service/SalesTicket.svc/GetUserDetailsBytoken`

  return await axios.post(url, requestData, { withCredentials: true })
};


export const GenieCreateTicket = async (requestData) => {
  const url = `${config.api.MatrixTicket}service/SalesTicket.svc/CreateTicket`

  return await axios.post(url, requestData, { withCredentials: true })
};


export const OTSData = async (TLlist, cb) => {
  try {
    let url = config.api.base_url + "/Fos/OTSData";
    const headers = {
      'Content-Type': 'application/json',
    }

    let result = await axios
      .post(url, TLlist
        , {
          headers: headers
        });
    if (result.status == 200 && result.data.data) {

      cb(result.data.data);
    }
  }
  catch (e) {
    console.log("The error is ", e);
  }

}

export const GetFosUserDetails = async (cb) => {
  try {
      let url = config.api.base_url + "/Fos/GetFosUserDetails";
      const headers = {
          'Content-Type': 'application/json',
      }
      let result = await axios.get(url, {
          headers: headers
      });
      if (result.status === 200) {
          cb(result.data);
      }
  }
  catch (e) {
      console.log("GetFosUserDetails error: ", e);
      cb(null, e);
  }
}

export const IsSocialMediaAgent = async (UserInfo,cb) =>{
  try{
  let url = config.api.base_url + "/Fos/IsSocialMediaAgent";
  const headers = {
    'Content-Type': 'application/json',
  }
  
  let result=await axios
  .post(url, UserInfo
      , {
        headers: headers
      });
  if(result.status==200 && result.data.data)
  {
        cb(result.data.data);
  }
  }
  catch(e){
    console.log("The error is ", e);
  }
  
}

export const UpdateMISPaymentRejection = (formdata, UserId, cb) => {
  let url = config.api.MatrixCoreURL + "coremrs/api/ProposalService/UpdateMISPaymentRejection";
  const headers = {
    "Content-Type": "multipart/form-data",
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*",
    "AgentId": UserId
  }
  axios
    .post(url, formdata
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
};

export const FetchHealthRenewalNeedAnalysis = (LeadID, UserId, cb) => {
  let url = config.api.MatrixCoreURL + "coremrs/api/HealthRenewal/FetchHealthRenewalNeedAnalysis/"+LeadID;
  const headers = {
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*",
    "AgentId": UserId
  }
  axios
    .get(url
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const SetHealthRenewalNeedAnalysis = (requestData, cb) => {
  let url = config.api.MatrixCoreURL + "coremrs/api/HealthRenewal/SetHealthRenewalNeedAnalysis";
  const headers = {
    "Content-Type": "application/json",
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*",
    "AgentId": getuser().UserID
  }
  axios
    .post(url, requestData
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });
};


export const FetchHealthRenewalPaymentLinkService = (obj, cb) => {
  let url = config.api.base_url + "/db/FetchHealthRenewalPaymentLink";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const CreateRenewalManualPaymentLinkService = async (Request, cb) => {
  let url = config.api.MatrixCoreURL + "api/HealthRenewal/CreateRenewalManualPaymentLink";
  const headers = {
    "Content-Type": "application/json",
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*"
  }
  axios
    .post(url, Request
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });
};
export const SetHealthRenewalPaymentLinkService = (obj, cb) => {
  let url = config.api.base_url + "/db/SetHealthRenewalPaymentLink";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, obj
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};

export const GetProductPlans = (ProductId, SupplierId, Source, UserId, cb) => {
  let url = config.api.MatrixCoreURL + "api/master/GetProductPlans?ProductId=" + ProductId + "&supplierId=" + SupplierId + "&Source=" + Source;
  const headers = {
    "source": "dashboard",
    'Access-Control-Allow-Credentials': true,
    "Access-Control-Allow-Origin": "*",
    "AgentId": UserId
  }
  axios
    .get(url
      , {
        headers: headers,
        withCredentials: true
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
    });

};