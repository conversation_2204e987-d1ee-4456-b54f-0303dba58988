
import React from 'react';

import {
  GetDataDirect
} from "../../store/actions/CommonAction";
import { getuser } from '../../utility/utility.jsx';
import Offcanvas from 'react-bootstrap/Offcanvas';
import { Row, Col, Button } from 'react-bootstrap';
import {
  CardTitle,
} from "reactstrap";
import 'react-checkbox-tree/lib/react-checkbox-tree.css';
import CheckboxTree from 'react-checkbox-tree';


class ManagerHierarchyMyBookings extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      nodes: [],
      checked: [],
      expanded: [],
      selectedNodes: [],
      arr: [],
      showManagerHierarchy: false,
      EmployeeIds: [],
      IsLoading: false
    }
    this.HandleOpenHierarchy = this.HandleOpenHierarchy.bind(this);
    this.HandleCloseHierarchy = this.HandleCloseHierarchy.bind(this);
    this.HandleShowClick = this.HandleShowClick.bind(this);
    this.OnRefreshHierarchy = this.OnRefreshHierarchy.bind(this);
    this.SetCheckedValues = this.SetCheckedValues.bind(this);
    this.selectedNodes = [];
  }

  componentDidMount() {
    let UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;

    GetDataDirect({
      root: "Hierarchy",
      ManagerId: UserId,
      statename: "Hierarchy-" + UserId,
      value: this.props.value,
      state: true
    }, function (result) {

      let str = JSON.stringify(result);
      var res = str.replace(/UserName/g, "label");
      res = res.replace(this.props.value, "value");
      this.setState({ nodes: JSON.parse(res) });
    }.bind(this));
  }

  OnRefreshHierarchy() {
    let UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;
    this.setState({ IsLoading: true })
    
    GetDataDirect({
      root: "Hierarchy",
      ManagerId: UserId,
      statename: "Hierarchy-" + UserId,
      value: this.props.value,
      state: true,
      ClearLocalStorage: true
    }, function (result) {

      let str = JSON.stringify(result);
      var res = str.replace(/UserName/g, "label");
      res = res.replace(this.props.value, "value");
      this.setState({ nodes: JSON.parse(res) });
      this.setState({ IsLoading: false })
    }.bind(this));
  }

  getNodesData(nodesData, SelectedSupervisors) {
    for (let i = 0; i < nodesData.length; i++) {
      let element = nodesData[i];
      if (SelectedSupervisors.indexOf(element.value) > -1) {
        this.selectedNodes.push(element);
      }
      if (element.children) {
        this.getNodesData(element.children, SelectedSupervisors)
      }
    }
  }

  HandleShowClick() {
    this.selectedNodes = [];
    this.getNodesData(this.state.nodes, this.state.checked);
    let arr = [];
    for (let i = 0; i < this.selectedNodes.length; i++) {
      arr.push(this.selectedNodes[i].EmployeeId)
    }
    this.setState({ EmployeeIds: arr, showManagerHierarchy: false });

    this.props.handleShow({
      SelectedSupervisors: this.state.checked,
      nodesData: this.selectedNodes
    });
    this.forceUpdate();
  }

  HandleOpenHierarchy() {
    this.setState({ showManagerHierarchy: true })
  }

  HandleCloseHierarchy() {
    this.setState({ showManagerHierarchy: false })
  }

  RemoveChecked(checkeditem) {
    const { checked } = this.state;
    let index = checked.indexOf(checkeditem);
    if (index > -1) {
      checked.splice(index, 1);
    }
    this.setState(checked);
  }

  SetCheckedValues(checkedOptions) {
    debugger
    if(!Array.isArray(checkedOptions)){
      return
    }

    if(checkedOptions.length > 2) {
      // this.setState({ checked: [checkedOptions[0]]})
      window.alert("Please select single checkboxes down the arrow.")
    } else if(checkedOptions.length === 2){
      const { checked } = this.state;
      const options = checkedOptions?.filter(opt => opt !== checked[0]);
      this.setState({ checked: options })
    } else {
      this.setState({ checked: checkedOptions})
    }

  }

  render() {
    const { nodes, showManagerHierarchy, EmployeeIds, IsLoading } = this.state;
    if (nodes.length == 0) {
      return null;
    }
    return (
      <>
        {/* <label>Filter</label> */}
        <Button variant="primary" className="HierarchyBtn" onClick={this.HandleOpenHierarchy}>
          {Array.isArray(EmployeeIds) && EmployeeIds.length > 0 ? EmployeeIds.slice(0, 2).join() + '...' : 'Show Hierarchy'}
        </Button>

        <Offcanvas show={showManagerHierarchy} onHide={this.HandleCloseHierarchy} backdrop="static" placement="end">
          <Offcanvas.Header closeButton>
            <Offcanvas.Title>
              Select Supervisors
            </Offcanvas.Title>
            <input type="button" className="btn btn-primary " onClick={this.HandleShowClick} value="Show" /> <i title="Refresh" className="fa fa-refresh refreshIcon" onClick={this.OnRefreshHierarchy}></i>
          </Offcanvas.Header>
          {IsLoading ?
            <h5 className='hierarchyLoading'>Loading...</h5> :
            <Offcanvas.Body className="hierarchyScrolling">
              <Row>
                <Col>
                  <div className="managers">
                    <CheckboxTree
                      nodes={nodes}
                      checked={this.state.checked}
                      expanded={this.state.expanded}
                      checkModel="all"
                      name="UserName"
                      showNodeIcon={false}
                      onCheck={checked => this.SetCheckedValues(checked)}
                      onExpand={expanded => this.setState({ expanded })}
                      showExpandAll={true}
                    />
                  </div>
                </Col>
              </Row>
              <br></br>
              <Col md={8}>
                <CardTitle  className= "managerEmployees" tag="h6">{this.state.EmployeeIds && Array.isArray(this.state.EmployeeIds) && this.state.EmployeeIds.join()}</CardTitle>
              </Col>
              </Offcanvas.Body>
          }
        </Offcanvas>
      </>
    )
  }
}

export default ManagerHierarchyMyBookings;
