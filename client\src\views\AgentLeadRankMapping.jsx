
import React from "react";

import { useState } from "react";

import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import DropDown from './Common/DropDown';
import DropDownList from './Common/DropDownList';

import { useEffect } from "react";
import {
    GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";

import { connect } from "react-redux";
import DropDownGroup from "./Common/DropDownGroup";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

const AgentLeadRankMapping = (props) => {
    const [productid, setProductid] = useState();
    const PageTitle = "Agent Lead Rank Mapping";
    const [groupid, setGroupid] = useState();
    const [group, setGroup] = useState([]);
    const [name, setName] = useState("");
    const [leadranks, setleadranks] = useState('');



    const ProductChange = (e) => {
        setGroup([]);
        setProductid(e.target.value);
        // console.log(e.target.value);
    }
    const onProdChange = (e) => {
        setGroupid(e.target.value);
        //console.log(e.target.value);
    }


    const handleNameChange = (e) => {
        setName(e.target.value);
        //console.log(e.target.value);
    }

    // useEffect(()=>{
    // if(!props.CommonData.isError)
    // {
    //     props.GetCommonData({
    //         root: 'Products',
    //         cols: ["ID AS Id", "ProductName AS Display"],
    //         con: [{ "Isactive": 1 }],
    //         c: "R",
    //     }, function (data){
    //         if (data && data.data && data.data.data) {
    //             let products = data.data.data[0];
    //           //  console.log(data.data.data[0][1]);
    //            // console.log(products);
    //             setProducts(products);
    //         }
    //     });
    // }

    // },[]);

    let productList = {
        config: {
            root: 'Products',
            cols: ["ID AS Id", "ProductName AS Display"],
            con: [{ "Isactive": 1 }],
            c: "R",
        }
    }


    useEffect(() => {
        if (productid) {
            if (!props.CommonData.isError) {
                props.GetCommonspData({
                    root: 'GetGroupByProductId',
                    c: "R",
                    params: [{ "ProductId": productid }],
                    con: [{ "Isactive": 1 }],
                },
                    function (result) {
                        if (result && result.data && result.data.data) {
                            let group = result.data.data[0];

                            let group1 = group.map(({ GroupId, Groupname }) => ({
                                Id: GroupId,
                                Display: Groupname
                            }));
                            //console.log(group1);
                            setGroup(group1);
                        }
                    }
                );
            }
        }

    }, [productid]
    );

    const handleLeadRanks = (e) => {
        const re = /^[0-9,\b]+$/;
        if (e.target.value === '' || re.test(e.target.value)) {
            setleadranks(e.target.value);
        }
    }

    const InsertData = (e) => {
        console.log("Name is " + name + " Lead Ranks are =" + leadranks + " Product ID = " + productid + " Group ID is: " + groupid);
        e.preventDefault();
        if (!leadranks || !name || productid == 0 || groupid == 0) {
            alert("select all required fields");
        }
        else {
            props.GetCommonspData({
                root: "Agent_LeadRankMapping",
                c: "L",
                params: [{ "LeadRanks": leadranks }, { "ProductID": productid }, { "RuleName": name }, { "GroupID": groupid }]

            }, function (result) {
                if (result.data && result.data.status == 200) {
                    alert("Successfully Saved");
                }
                else {
                    alert("Not Saved");
                    return;
                }
            }
            )
        }
    }



    return (
        <>
            <div className="content">
                {/* <form> */}
                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Form>
                                    <Row>
                                        <Col md={12}>
                                            <Form.Label>Product</Form.Label>
                                           
                                            <DropDownList firstoption="Select" valueTobefiltered={[2, 115, 7, 117]} col={productList} onChange={ProductChange}></DropDownList>

                                        </Col>
                                    </Row>
                                    <br />
                                    <Row><Col md={12}>
                                        <Form.Label>Group Name</Form.Label>
                                      
                                        <DropDown firstoption="Select" items={group} onChange={onProdChange}></DropDown>

                                    </Col></Row>
                                    <Row>
                                        <Col md={12}>
                                            <br />
                                            <Form.Label>RuleName</Form.Label>
                                            <Form.Control
                                                required={true}
                                                type="text"
                                                onChange={handleNameChange}
                                                placeholder={"Enter RuleName"}
                                                value={name} />


                                        </Col>
                                    </Row>


                                    <Row>
                                        <Col md={12}>
                                            <br />
                                            <Form.Label>LeadRanks</Form.Label>
                                            <Form.Control
                                                required={true}
                                                type="text"
                                                onChange={handleLeadRanks}
                                                placeholder={"123,456,789"}
                                                value={leadranks} />

                                        </Col>
                                    </Row>





                                    <div><br />
                                        <Row>
                                            <Col md={12}>
                                                <Button onClick={InsertData}>
                                                    Save
                                                </Button>
                                            </Col>
                                        </Row>
                                    </div>

                                </Form>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
                {/* </form> */}
            </div>
        </>



    );
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData
    }
)(AgentLeadRankMapping);