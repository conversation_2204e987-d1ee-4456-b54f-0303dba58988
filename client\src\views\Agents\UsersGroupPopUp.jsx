
import React from "react";
import { useState, useEffect } from "react";
import {  Button, Modal, Form } from 'react-bootstrap';
import { connect } from "react-redux";
// reactstrap components
import {
    Row,
    Col
} from "reactstrap";

import DropDown from '../Common/DropDownList';

import {
    GetCommonData, GetCommonspData, GetCommonspDataV2
} from "../../store/actions/CommonAction";

import { MultiSelect } from "react-multi-select-component";
import { toast } from 'react-toastify';

const UsersGroupPopUp = (props) => {

    let [showModal, setshowModal] = useState(true);
    const [items, setitems] = useState([])
    const [ProductId, setProductId] = React.useState();
    const [SubProductselected, setSubProductselected] = React.useState([]);
    const [Groupname, setGroupName] = useState();
    const [Language, setLanguage] = useState(null);
    const [Location, setLocation] = useState(null);
    const [AgentProcess, setAgentProcess] = useState(null);
    const [GroupProcessType, setGroupProcessType] = useState(1);
    const [SubProductTypeList, setSubProductTypeList] = useState([]);
    const [CallingType, setCallingType] = useState(1);
    const [IsAsterisk, setIsAsterisk] = useState(true);
    const [IsOneLead, setIsOneLead] = useState(true);
    const [IsLoader, setIsLoader] = useState(false);

    const ProductList = {
        config:
        {
            root: "Products",
            cols: ["ID AS Id", "ProductName AS Display", "ID AS value", "ProductName AS label"],
            con: [{ "Isactive": 1 }],
            order: 1,
            direction: 'asc'
        }
    };

    const GroupProcessTypeList = {
        config:
        {
            root: "GroupProcessMaster",
            cols: ["ID AS Id", "GroupProcess AS Display"],
            con: [{ "Isactive": 1 }],
            order: 1,
            direction: 'asc'
        }
    };

    const CallingTypeList = {
        config:
        {
            root: "CallingTypeList",
            data: [{ Id: 1, Display: "Normal Calling" }, { Id: 2, Display: "Special Calling" }],
        }
    };

    const LocationList = {
        config:
        {
            root: "LocationMaster",
            cols: ["ID AS Id", "LocationName AS Display"],
            con: [{ "Isactive": 1 }],
            order: 1,
            direction: 'asc'
        }
    };

    const AgentProcessList = {
        config:
        {
            root: "AgentProcessMaster",
            cols: ["ID AS Id", "AgentProcess AS Display"],
            con: [{ "Isactive": 1 }],
            order: 1,
            direction: 'asc'
        }
    };

    const LanguageList = {
        config:
        {
            root: "LanguageMaster",
            cols: ["ID AS Id", "Language AS Display"],
            order: 1,
            direction: 'asc'
        }
    };

    useEffect(() => {
        if (ProductId) {            
            if (!props.CommonData.isError) {
                props.GetCommonspData({
                    root: 'GetSubProductType',
                    c: "R",
                    params: [{ ProductId: ProductId }],
                }, function (result) {
                    let List = [];
                    if (result && result.data && result.data.data && result.data.data[0] && result.data.data[0].length > 0) {
                        result.data.data[0].forEach((element) => {
                            List.push({
                                label: element.Name,
                                value: parseInt(element.SubProductTypeId, 10)
                            })
                        });
                        setSubProductTypeList(List)
                    } else {
                        setSubProductTypeList([])
                    }
                })
            }
        }

    }, [ProductId])

    const handleClose = () => {
        props.handleClose(ProductId);
    }

    const handleClick = () => {
        setIsLoader(true);
        let SubProductList = '0'

        if (!ProductId) {
            toast('Enter Product Name', { type: 'error' });
            setIsLoader(false);
            return;
        }

        if (!Groupname) {
            toast('Enter Group Name', { type: 'error' });
            setIsLoader(false);
            return;
        }

        if(SubProductTypeList?.length > 0 && !(SubProductselected?.length > 0)){
            toast('Please select SubProducts', { type: 'error' });
            setIsLoader(false);
            return;
        }

        if (SubProductselected.length > 0) {

            let SubProducts = SubProductselected.map(item => {
                return item.value
            })
            SubProductList = SubProducts.toString()
        }

        props.GetCommonspDataV2({
            root: 'InsertUserGroup',
            c: "L",
            params: [{
                UserGroupName: Groupname, ProductId: (parseInt(ProductId,10) === 7)? 1000 : parseInt(ProductId,10), StrSubProductTypeId: SubProductList, IsAsterick: IsAsterisk,
                CallingType: parseInt(CallingType, 10), IsOneLead: IsOneLead, ProcessID: parseInt(GroupProcessType, 10),
                ...(parseInt(ProductId, 10) === 2 ? { LanguageID: Language, Location: Location, AgentProcess: AgentProcess } : {})
            }],
        }, function (result) {
            setIsLoader(false);
            try {
                if (result.data && result.data.status == 200) {
                    if(parseInt(CallingType, 10) === 1){
                        toast('Group Created Successfully', { type: 'success' });
                    }else if(parseInt(CallingType, 10) === 2){
                        toast('Group Created Successfully. Please Contact the Matrix Team to Enable Calling', { type: 'success' });
                    }
                    handleClose();
                } else {
                    if(result.response.data.IsError && result.response.data.message){
                        toast(result.response.data.message, { type: 'error' });
                        return;
                    }
                    toast('Not Saved', { type: 'error' });
                    return;
                }
            }
            catch (e) {
                toast('Something Wrong', { type: 'error' });
                return;
            }
        });
        
    }

    const OnSubProductselected = (selectedList, selectedItem) => {
        var newselectedList = selectedList.filter(task => task.label !== 'Select Values');
        let selectAll = false;
        if (items.length == selectedList.length) {
            selectAll = true;
        }
        setSubProductselected(newselectedList);
    }

    const handleChange = (name, e) => {
        switch (name) {
            case 'Groupname':
                setGroupName(e.target.value);
                break;
            case 'GroupProcessType':
                setGroupProcessType(e.target.value);
                break;
            case 'Product':
                setSubProductselected([]);
                setProductId(e.target.value);
                break;
            case 'IsAsterisk':
                setIsAsterisk(e.target.value === "1");
                break;
            case 'CallingType':
                setCallingType(e.target.value);
                break;
            case 'IsOneLead':
                setIsOneLead(e.target.checked);
                break;
            case 'Language':
                setLanguage(e.target.value);
                break;
            case 'Location':
                setLocation(e.target.options[e.target.selectedIndex]?.text || "");
                break;
            case 'AgentProcess':
                setAgentProcess(e.target.options[e.target.selectedIndex]?.text || "");
                break;    
            default:
                break;
        }
    }

    const handleKeyDown = (e) => {
        const code = e.keyCode || e.which || e.charCode;
        const allowedKeys = [
          32, 8, 9, 37, 39, 46, 16, 17, 18, 86, 13, 40, 41, 45, 34,189
        ]; // Space, Backspace, Tab, Arrow keys, Delete, Shift, Ctrl, Alt, Enter, etc.
      
        if (
          (code >= 65 && code <= 90) || // Uppercase letters
          (code >= 97 && code <= 122) || // Lowercase letters
          allowedKeys.includes(code)
        ) {
          return true;
        } else {
          alert("Only Alphabets Allowed!");
          e.preventDefault();
        }
      };

    return (

        <div >

            <Modal show={showModal} onHide={props.handleClose} size="xl" >
                <div style={{ width: '108%' }} className="modal-content">
                <Modal.Header closeButton>
                </Modal.Header>
                <Modal.Body>
                    <Form >
                        <Row>
                            <Col md={12}>


                                <div className='CsatratingPopup'>
                                    <Row>
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Group Name</label>
                                            <input type="text" value={Groupname} placeholder="Groupname" onChange={(event) => handleChange('Groupname', event)} 
                                            onKeyDown={handleKeyDown} className="form-control" />
                                        </Col>
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Group Type:</label>
                                            <DropDown firstoption={false} col={GroupProcessTypeList} onChange={(event) => handleChange('GroupProcessType', event)} >
                                            </DropDown>
                                        </Col>
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Product</label>
                                            <DropDown col={ProductList} onChange={(event) => handleChange('Product', event)} ></DropDown>
                                        </Col>
                                        {(parseInt(ProductId,10) === 2) && <><Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Language</label>
                                            <DropDown col={LanguageList} onChange={(event) => handleChange('Language', event)} ></DropDown>
                                        </Col>
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Location</label>
                                            <DropDown col={LocationList} onChange={(event) => handleChange('Location', event)} ></DropDown>
                                        </Col>
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Agent Process</label>
                                            <DropDown col={AgentProcessList} onChange={(event) => handleChange('AgentProcess', event)} ></DropDown>
                                        </Col></>}
                                   
                                     
                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Enable Calling</label>
                                            <DropDown firstoption={false} col={CallingTypeList} onChange={(event) => handleChange('CallingType', event)} ></DropDown>
                                        </Col>

                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Is OneLead &nbsp;</label>
                                            <input type="checkbox" onChange={(event) => handleChange('IsOneLead', event)} checked={IsOneLead} />
                                        </Col>

                                        {SubProductTypeList && SubProductTypeList.length > 0 && (
                                            <Col sm={6} md={3} xs={12}>
                                                <label className="mb-2">SubProductType</label>
                                                <MultiSelect
                                                    className="agentSurveyBox"
                                                    options={SubProductTypeList}
                                                    value={SubProductselected}
                                                    onChange={OnSubProductselected}
                                                    labelledBy="Select"
                                                />
                                        </Col>)}

                                        <Col sm={6} md={3} xs={12}>
                                            <label className="mb-2">Dialer</label>

                                            <div onChange={(event) => handleChange('IsAsterisk', event)} style={{ display: "flex" }}>
                                                <input type="radio" value="0" checked={!IsAsterisk} name="IsAsterisk" />
                                                <label for="IsAsterisk"> &nbsp; Altitude &nbsp; </label>
                                                <input type="radio" value="1" checked={IsAsterisk} name="IsAsterisk" />
                                                <label for="IsAsterisk"> &nbsp; Easydialer</label>
                                            </div>
                                        </Col>

                                      
                                    </Row>
                                </div>


                            </Col>

                            <Col md={1}>

                            </Col>

                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClick}>
                        Save {IsLoader && <i class="fa fa-spinner fa-spin"></i>}
                    </Button>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>

                </Modal.Footer>
                </div>
            </Modal>


        </div>

    );


}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData,
        GetCommonspDataV2
    }
)(UsersGroupPopUp);


