const expressionParser = (str, obj) => {

  let expression = str;
  let arr = expression.split(' ');

  let modified_expression ="";
  let errorStatus = 0;

  arr.forEach(function(part, index, res) {
    let reg = /^[A-Za-z]+$/
    let value = res[index];

    if(reg.test(value)){

      if(obj[value] === undefined || obj[value] === null) {
        errorStatus = 1;
      }

      modified_expression+= "${" + value + "}";
      modified_expression+= " ";
    } else {
      modified_expression+= value;
      modified_expression+=" ";
    }
  });

  const interpolate = (str, obj) => str.replace(
    /\${([^}]+)}/g,
    (_, prop) => obj[prop]
  );

  let result = interpolate(modified_expression, obj);
  return {errorStatus, result};

}

module.exports = {
  expressionParser: expressionParser
}