import React,{useEffect, useState} from "react";
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import { toast, ToastContainer } from 'react-toastify';
import { SendAutoDebitPrompt } from "./Utility";

const AutoDebitPrompt=(props)=>{

   const {onAutoDebitPromptClose,booking}=props;

   const [msg, setMsg]= useState({
    display: false,
    message:"",
    type:null
   });


   useEffect(()=>{

    if(msg.display && msg.type)
    {
        if(msg.type=='success')
        {
          toast.success(msg.message);
        }
        else if(msg.type=='error')
        {
          toast.error(msg.message);
        }
    }

   },[msg])

   const handleProceed=async()=>{
        setMsg({
          display: false,
          message:"",
          type:null
        })
        if(await SendAutoDebitPrompt(booking))
        {
          
            // toast.success("Successfully sent the E-Mandate Whatsapp link to the customer");
            setMsg({
              display:true,
              message: "Successfully sent the E-Mandate Whatsapp link to the customer",
              type:"success"
            });
            onAutoDebitPromptClose();
            // setTimeout(()=>{
            //     onAutoDebitPromptClose();
            // },2000)   
        }
        else{
  
            // toast.error("Unable to trigger the E-Mandate Whatsapp link");
            setMsg({
              display:true,
              message: "Unable to trigger the E-Mandate Whatsapp link",
              type:"error"
            });
            onAutoDebitPromptClose();
            // setTimeout(()=>{
            //     onAutoDebitPromptClose();
            // },2000)
        }
   }

   return(
    <>
      <Modal
        {...props}
        //size="lg"
        //aria-labelledby="contained-modal-title-vcenter"
        centered
        onHide={onAutoDebitPromptClose}
      >
        {/* <Modal.Header closeButton> */}
          {/* <Modal.Title id="contained-modal-title-vcenter">
           
          </Modal.Title> */}
        {/* </Modal.Header> */}
        <Modal.Body>
        <p>This will trigger a WhatsApp message to the customer to opt for AutoPay.</p>
        <p>Do you want to proceed?</p> 
        </Modal.Body>
        <Modal.Footer>
        <Button variant="secondary" onClick={onAutoDebitPromptClose}>
            No, Take me back
          </Button>
          <Button variant="primary" onClick={handleProceed}>
            Yes, Proceed
          </Button>
          {/* <Button variant="secondary" onClick={onAutoDebitPromptClose}>Close</Button> */}
        </Modal.Footer>
      </Modal>
    </>
   )
}

export default AutoDebitPrompt;