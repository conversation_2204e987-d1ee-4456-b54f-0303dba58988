
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTableV1 from './Common/DataTableWithFilterV1';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import { CompareJson, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject, getAgentId } from '../utility/utility.jsx';
// reactstrap components
import {
  <PERSON>,
  Card<PERSON>eader,
  <PERSON><PERSON>ody,
  CardTitle,
  Table,
  <PERSON>,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class AgentGradeRules_Allocation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "AgentGradeRules_Allocation",
      PageTitle: "Agent Grade Rules Allocation",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      setProductText: '',
      setProcessText: '',
      setResetPaginationToggle: '',
      resetPaginationToggle: '',
      filterText: '',
      ProductId:'',
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);
    this.selectedrow = { "RuleID": "", "ISActive": false, "CreatedOn": new Date(), "ProductID": null, "RuleName": null }
    this.columnlist = [
     
  
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        config: {
          root: "UserProducts",
          cols: ["DISTINCT PDT.ID AS Id", "PDT.ProductName AS Display"],
          con: [{ "PDT.Isactive": 1 },{ "UBM.IsActive" : 1 },{ "UBM.UserId": getAgentId() }]
        },
        searchable: true,
        editable: false
      },
      {
        name: "RuleID",
        label: "RuleID",
        type: "string",
        hide: true
      },
      {
        name: "RuleName",
        label: "RuleName",
        type: "dropdown",
        config: {
          root: "AgentRulesName",
          cols: ["DISTINCT RuleName AS Id", "RuleName AS Display"],
          //con: [{ "Isactive": 1 }]
          //statename: "AgentRules-"+ this.state.ProductId,
          //state: true,
        },
        searchable: true        
      },
      {
        name: "CreatedOn",
        label: "CreatedOn",
        type: "datetime",
        hide: true,
      },
      {
        name: "ISActive",
        label: "ISActive",
        type: "bool"
      }

    ];
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name"),
      c: "L",
    });
    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ filteredItems: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status && nextProps.CommonData.InsertSuccessData != this.props.CommonData.InsertSuccessData) {
      if (nextProps.CommonData.InsertSuccessData.status != 200) {
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      }
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status && nextProps.CommonData.UpdateSuccessData != this.props.CommonData.UpdateSuccessData) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
      }
    }
    setTimeout(function () {
      this.setState({ showAlert: false, AlertMsg: "", AlertVarient: "" });
    }.bind(this), 2000);
  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
  handleCopy(row) {
    this.setState({ formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {
    if (document.getElementsByName("frmAgentGradeRules_Allocation").length > 0 &&
      document.getElementsByName("frmAgentGradeRules_Allocation")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      let id = formvalue["RuleID"];
      delete formvalue["RuleID"]
      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);
        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { "RuleID": id },
          c: "L",
        });
        
        this.props.addRecord({
          root: "History",
          body: {
            module: "AgentGradeRules_Allocation",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserID
          }
        });
      } else if (this.state.event == "Copy") {        
        formvalue["CreatedOn"] = new Date();
        this.fnCleanData(formvalue);
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
      } else {
        
        formvalue["CreatedOn"] = new Date();
        
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
      }

      let productid = formvalue["ProductID"];

      setTimeout(function () {

        if (productid) {
          this.props.GetCommonData({
            root: this.state.root,
            cols: GetJsonToArray(this.columnlist, "name"),
            c: "L",
            con: [{ "ProductID": productid }],
          });
        }
        else {
          this.props.GetCommonData({
            limit: 10,
            skip: 0,
            root: this.state.root,
            cols: GetJsonToArray(this.columnlist, "name"),
            c: "L",
          });
        }
      }.bind(this), 2000);
      this.setState({ showModal: false });
    }
    return false;
  }
  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }

  handleProductClear = () => {
    this.setState({ setProductText: '' });
  };

  handleProcessClear = () => {
    this.setState({ setProcessText: '' });
    setTimeout(function(){
      this.fnFilteredItems(this.state.items);
    }.bind(this),1000);
  };


  onProcessFilter(e) {
    this.setState({ setProcessText: e.target.value });
    setTimeout(function(){
      this.fnFilteredItems(this.state.items);
    }.bind(this),1000);
    
  }

  fnFilteredItems(items) {
    console.log("funcitems", items);
    console.log("producttext", this.state.setProductText);
    console.log("processtext", this.state.setProcessText);
    let productname = this.state.setProductText;
    let processname = this.state.setProcessText;
    const filteredItems = '';
    console.log("processname", processname);
    if (typeof (items) !== 'undefined') {
      const filteredItems = items.filter(item => item.ProcessName.toUpperCase().indexOf(processname.toUpperCase()) > -1 );
      console.log("filtereditems", filteredItems);
      this.setState({ filteredItems: filteredItems });
      return filteredItems;
    }
    else {
      this.setState({ filteredItems: items });
      return items;
    }

  }

  productchange(e, props) {

    this.setState({ ProductId: e.target.value });

    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name"),
      c: "L",
      con: [{ "ProductID": e.target.value }],
    });

  }
  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event } = this.state;

    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                 
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTableV1
                    columns={columns}
                    data={items}
                    getDataOnProdSearch={true}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmAgentGradeRules_Allocation">
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
  }
)(AgentGradeRules_Allocation);