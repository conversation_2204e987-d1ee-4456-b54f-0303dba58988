import React from "react";
import DropDown from '../Common/DropDownList';
import { connect } from "react-redux";
import Date from "../Common/Date"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'
import Datetime from 'react-datetime';
import { JsonToTable } from "react-json-to-table";
import {
    PostFileUploadData
} from "../../store/actions/CommonAction";
import {
    addRecord, GetCommonData
} from "../../store/actions/CommonMongoAction";
//import {PostMonthlyIncentiveData} from '../../store/actions/CommonMongoAction'
import {
    CardHeader,
    Row,
    Col
} from "reactstrap";
import { Button, Form } from 'react-bootstrap';


class FileUpload extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "FileUploadCollection",
            uploadFile: "",
            UserId: "",
            isUploaded: false,
            SampleFile: "",
            currDate: moment().format("MM-YYYY"),
            IncentiveMonth: moment().format("MM-YYYY"),
            selectedFile: "",
            xlsFile: [],
            columnValidation: null,
            incorrectColumns: [],
            columnList: [],
            incorrectRowNumber: "",
            incorrectColumnsName: "",
            uploadStatus: false,
            DataCount: 0,
            ErrorDataCount: 0,
            errorRecords: "",
            jsonError: {},
            c: "M"
        };
        this.UploadList = {
            config:
            {
                data: [
                    { Id: "GMC_GPA", Display: "GMC_GPA" }
                ]
            }
        };


    }

    onFileChange(e) {
        let file = e.target.files[0]
        this.file = file

    }
    getColumnData(keys, columnList) {
        this.setState({ columns: keys })

        var dataArray = Object.keys(columnList).map(function (k) { return columnList[k].name });

        let check1 = keys.filter(function (value) {
            return dataArray.indexOf(value) == -1;

        });
        let check2 = dataArray.filter(function (value) {
            return keys.indexOf(value) == -1;

        });

        let output = check1.concat(check2);
        if (output.length == 0) {
            this.setState({ columnValidation: true })
        }
        if (output.length > 0) {
            this.setState({ columnValidation: false, incorrectColumns: output })
        }

    }


    uploadFileChange(e) {
        this.setState({
            uploadFile: e.target.value
        })


    }



    GetRecordCount(e) {
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: {},
            type: "count"
        }, function (data) {
            this.setState({ DataCount: data.data.data })
        }.bind(this))
        return;
    }
    GetErrorCount(e) {
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: { error: { $exists: true } },
            type: "count"
        }, function (data) {
            this.setState({ ErrorDataCount: data.data.data })
        }.bind(this))
        return;

    }

    GetErrorRecords(e) {
        e.preventDefault()
        this.props.GetCommonData({
            limit: 20,
            skip: 0,
            root: this.state.root,
            c: this.state.c,
            con: { error: { $exists: true } },
        }, function (data) {
            console.log("Ins", data)
            this.setState({ errorRecords: JSON.stringify(data.data.data), jsonError: data.data.data })

        }.bind(this))
    }

    onFileUpload(e) {
        e.preventDefault()

        if (this.state.uploadFile == "") {
            toast("Please Upload File", { type: 'error' });
            return;
        }

        if (this.state.root == "") {
            toast("Invalid Operation", { type: 'error' });
        }
        console.log(this.file, this.file.name, this.state.uploadFile)

        const formData = new FormData();
        console.log(formData)
        formData.append("myFile", this.file, this.file.name);
        formData.append('FileType', this.state.uploadFile);
        formData.append('UploadDate', this.state.currDate);
        PostFileUploadData(formData, function (results) {
            //alert("File Uploaded")
            console.log(results.data)
           
            
            if (results.data.status == 200) {
                this.setState({ uploadStatus: true })
                alert(results.data.message)

            }
            if (results.data.status == 501) {
                alert(results.data.message)
            }

        }.bind(this))


    }



    render() {

        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <CardHeader>
                                <Row>

                                    <Col md={3}>
                                        <Form.Group controlId="upload_file_dropdown">
                                            <DropDown firstoption="Select Upload File" value={this.state.uploadFile} col={this.UploadList} onChange={this.uploadFileChange.bind(this)}>
                                            </DropDown>
                                        </Form.Group>
                                    </Col>
                                    <form ref="form" onSubmit={this.onFileUpload.bind(this)}>
                                        <input type="file" id="files-upload" onChange={this.onFileChange.bind(this)} />
                                        <button type="submit" id="uploadbutton" className="btn btn-primary">Upload!</button>
                                    </form>


                                </Row>

                            </CardHeader>
                        </Col>
                    </Row>
                    {this.state.incorrectColumns.length > 0 && <h4>Error while processing file. Please Check columns {this.state.incorrectColumns}</h4>}
                    {this.state.incorrectColumnsName != "" && this.state.incorrectRowNumber != "" && <h4>Error while processing file. Please Check columns {this.state.incorrectColumnsName} at line {this.state.incorrectRowNumber}</h4>}
                    <Row>
                        <Col md={3}>
                            {this.state.uploadStatus && <Button variant="secondary" onClick={this.GetRecordCount.bind(this)}>Total Records</Button>}
                            {this.state.uploadStatus && <h6>Records in DB : {this.state.DataCount}</h6>}
                        </Col>
                        <Col md={3}>
                            {this.state.uploadStatus && <Button variant="secondary" onClick={this.GetErrorCount.bind(this)}>Error Records</Button>}
                            {this.state.uploadStatus && <h6>Error Records in DB : {this.state.ErrorDataCount}</h6>}
                        </Col>
                        <Col md={3}>
                            {this.state.uploadStatus && this.state.ErrorDataCount != 0 && <Button variant="secondary" onClick={this.GetErrorRecords.bind(this)}>Get Error Records</Button>}


                        </Col>
                    </Row>
                    {this.state.ErrorDataCount != 0 && this.state.errorRecords != "" && <JsonToTable json={this.state.jsonError} />}



                </div>
            </>
        )
    }

}
function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps, { addRecord, GetCommonData }
)(FileUpload)



