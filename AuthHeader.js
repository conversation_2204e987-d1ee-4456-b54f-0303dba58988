const axios = require("axios");
const cache = require('memory-cache');
const CryptoJS = require("crypto-js");
const conf = require("./env_config");
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');

function parseCookies(request) {
  var cookies = {};
  request.headers && request.headers.cookie && request.headers.cookie.split(';').forEach(function (cookie) {
    var parts = cookie.match(/(.*?)=(.*)$/);
    cookies[parts[1].trim()] = (parts[2] || '').trim();
  });
  return cookies;
}

function IsInternalRequest(req) {
  try {
    const IsInternalDomain = parseCookies(req)[conf.INTERNAL_DOMAIN_COOKIE];
    let result = false;
    if (IsInternalDomain && (IsInternalDomain !== conf.INTERNAL_DOMAIN_VALUE)) {
      result =  true;
    }
    // console.log("result: IsInternalDomain: ", result)
    return result;
  } catch (err) {
    console.log('Inside IsInternalRequest: ', err);
  }
}


function Base64Decoding(value) {
  const decoded = CryptoJS.enc.Base64.parse(value);
  const details = decoded.toString(CryptoJS.enc.Utf8);
  return details;
}

async function ValidateToken({ Source, Token, UserId }) {
  try {
    switch(Source) {
      case 'MatrixGo':
        if (!UserId || !Token) {
          return {IsValid: false, UserId: 0 };
        }

        let cachedToken = cache.get(UserId);
        let response = null;

        let data = {
          userid: UserId,
          token: Token
        };

        if (!cachedToken) {
          response = await axios.post(conf.VERIFY_TOKEN_MTX, data);
          if (response && response.data) {
            cache.put(UserId, data, (8 * 60 * 60 * 1000));
            return { IsValid: true, UserId: UserId} ;
          }
        } else {
          if (!(data.token) || !(cachedToken.token)) {
            return { IsValid: false, UserId: 0 }
          } else if (data.token !== cachedToken.token) {
            response = await axios.post(conf.VERIFY_TOKEN_MTX, data);
            if (response && response.data) {
              cache.put(UserId, data, (8 * 60 * 60 * 1000));
              return { IsValid: true, UserId };
            } else {
              return { IsValid:false, UserId};
            }
          } else {
            return { IsValid: true, UserId };
          }
        }
    }
  } catch (err) {
    console.log('Inside AuthHeader ValidateToken', err);
    return false;
  }
}

async function ValidateUser(req) {
  const { source, token, userid } = req.headers || {};
  try {
    const { IsValid, UserId } = await ValidateToken({ Source: source, Token: token, UserId: userid });
    
    if (IsValid) {
      let value = IsInternalRequest(req);
      req['user'] = { userId: UserId, IsInternalDomain: value};
    }

    return IsValid;
  } catch (err) {
    console.log('Inside Authheader ValidateUser', err);
    return false;
  }

}

async function AuthHeader(req, res, next) {
  try {
    if (process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      const SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      // console.log('Inside Auth ENV: ', process.env.ENVIRONMENT_MTX_DASH)
    } else {
      const SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
    }
   
    let IsValid = await ValidateUser(req);

    if (IsValid) {
      next();
    }
    else {
      res.status(401).json({
        status: 401,
        message: "Unauthorized Source"
      })
    }
  } catch (err) {
    console.log('Inside Auth', err);
    res.status(500).send("Auth Error");
  }
}

module.exports = {
  AuthHeader: AuthHeader,
  Base64Decoding: Base64Decoding,
  ValidateUser: ValidateUser,
  ValidateToken: ValidateToken
}