import React, { Component } from 'react';
import RadioButton from '../Common/RadioOptions';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";

class FeedbackQuestion extends Component {
    constructor(props) {
        super(props);
    }
    saveAndContinue = (e) => {
        e.preventDefault()
        const { values } = this.props;
        if (this.props.onValidation("part1", values)) {
            this.props.SaveFeedbackDetails(values);
            this.props.nextStep()
        }
    }

    render() {
        const { values, errors, activequestion1, activequestion2, activequestion3 } = this.props;
        const items1 = []
        const items2 = []
        const items3 = []

        for (let index = 1; index <= 10; index++) {
            items1.push(<li onClick={this.props.handleChange("question1",index)} className={ activequestion1 === `question1_${index}` ? 'active-rating':"" }>{index}</li>)
        }
        for (let index = 1; index <= 10; index++) {
            items2.push(<li onClick={this.props.handleChange("question2",index)} className={ activequestion2 === `question2_${index}` ? 'active-rating':"" }>{index}</li>)
        }
        for (let index = 1; index <= 10; index++) {
            items3.push(<li onClick={this.props.handleChange("question3",index)} className={ activequestion3 === `question3_${index}` ? 'active-rating':"" }>{index}</li>)
        }
        return (

            <Form>
                <Col md={12} className="pull-left mt-4 mb-4">
                    <Form.Label>1. My Team Coach provides regular feedback & coaching. <span>*</span></Form.Label>
                    <ul className='survey-rating'>
                        {items1}
                    </ul>
                    <span style={{ color: "red" }}>{errors.question1}</span>
                </Col>
                <Col md={12} className="pull-left mt-4">
                    <Form.Label>2. How happy are you with your Team Coach? <span>*</span></Form.Label>
                    <ul className='survey-rating'>
                        {items2}
                    </ul>
                    <span style={{ color: "red" }}>{errors.question2}</span>
                </Col>
                <Col md={12} className="pull-left mt-4">
                    <Form.Text className="questions" id="question1">
                        <p class="survey-text mb-3">3. How likely is it that you recommend a friend or relative to work at policybazaar?  <span>*</span></p>
                    </Form.Text>
                    <ul className='survey-rating'>
                        {items3}
                    </ul>
                    <span style={{ color: "red" }}>{errors.question3}</span>
                </Col>
                <Col md={12} className="pull-left mt-4">
                    <Button onClick={this.saveAndContinue.bind(this)}>Submit Details </Button>
                </Col>
            </Form>

        )
    }
}

export default FeedbackQuestion;