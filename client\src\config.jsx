const qa = {
  env: "QA",
  api: {
    base_url: "/api/v1",
    socket_url:"https://matrixdashboardqa.policybazaar.com",
    realtimeurl: "https://internalagenttrackerqa.policybazaar.com/agentstatus/",
    awsrecordingurl: "https://matrixdashboardqa.policybazaar.com/api/v1/db/awsfile",
    recordingnameurl: "https://matrixlab.policybazaar.com/matrix/getrecordingname/",
    SendOTP: "https://myaccountapiqa.policybazaar.com/Login/GenerateOTP",
    ValidateOTP: "https://myaccountapiqa.policybazaar.com/Login/ValidateOTP",
    ServiceAPI: "https://progressiveqa.policybazaar.com/",
    s3recordingurl: "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/",
    moodleurl: "https://moodle.policybazaar.com/core/api/",
    dialerApiUrl: "https://dialerqaapi.policybazaar.com/api/dialer/",
    chatAgentUrl : "https://chatapiv2qa.policybazaar.com/api/v1/",
    progressiveUrl : "https://progressiveqa.policybazaar.com/communication/LeadPrioritization.svc/",
    aiUrl : "http://************/workflow/",
    SalesviewUrl : "https://progressiveqa.policybazaar.com/pgv/SV/LeadContent.htm#/Sales/",
    DashboardUrl : "https://agentdashboardapi.policybazaar.com/apiMatrixProduct/v1/",
    waitingAssignedQueue : "https://dialerqaapi.policybazaar.com/api/v2/inbound/waitingAssignedCalls" ,
    MatrixCoreURL: "https://qamatrixcoreapi.policybazaar.com/",
    MRSURL: "https://matrixliveapi.policybazaar.com/MRS/",
    MatrixCore: "https://qamatrixcoreapi.policybazaar.com/",
    newSalesviewUrl: "https://progressiveqa.policybazaar.com/pgv/newsv/",
    dialerApiUrlInbound: "https://dialerqaapi.policybazaar.com/api/v2/dialer/getInboundQueueMappingData",
    BlockAgent: 'https://matrixdashboardqa.policybazaar.com/admin/BlockAgent',
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    MatrixInternal: 'https://qainternalmatrixapi.policybazaar.com/',
    MatrixTicket: 'https://matrtixfeedbackqa.policybazaar.com/'
  },
  site_base_url: "https://matrixdashboardqa.policybazaar.com",
  byPassSecurity: true,
  byPassPages: ['BlockAgent'],
  byMenuSecurity: true,
  FOSImageUploadBucket: 'matrixbktqa'
};

const prod = {
  env: "PROD",
  api: {
    base_url: "https://matrixdashboard.policybazaar.com/api/v1",
    socket_url:"https://matrixdashboard.policybazaar.com",
    realtimeurl: "https://internalagenttracker.policybazaar.com/agentstatus/",
    awsrecordingurl: "https://matrixdashboard.policybazaar.com/api/v1/db/awsfile",
    recordingnameurl: "https://matrixlab.policybazaar.com/matrix/getrecordingname/",
    SendOTP: "https://myaccountapi.policybazaar.com/Login/GenerateOTP",
    ValidateOTP: "https://myaccountapiqa.policybazaar.com/Login/ValidateOTP",
    ServiceAPI: "https://mobilematrixapi.policybazaar.com/",
    s3recordingurl: "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/",
    moodleurl: "https://moodle.policybazaar.com/core/api/",
    dialerApiUrl: "https://dialerapi.policybazaar.com/api/dialer/",
    chatAgentUrl: "https://chatapiv2.policybazaar.com/api/v1/",
    progressiveUrl: "https://mobilematrixapi.policybazaar.com/communication/LeadPrioritization.svc/",
    aiUrl: "https://ai.policybazaar.com/workflow/",
    SalesviewUrl : "https://matrix.policybazaar.com/pgv/SV/LeadContent.htm#/Sales/",
    DashboardApiUrl : "https://agentdashboardapi.policybazaar.com/apiMatrixProduct/v1/",  
    DashboardUrl : "https://agentdashboard.policybazaar.com/",
    waitingAssignedQueue : "https://dialerapi.policybazaar.com/api/v2/inbound/waitingAssignedCalls",
    MatrixCoreURL: "https://matrixcoreapi.policybazaar.com/",
    MRSURL: "https://matrixliveapi.policybazaar.com/MRS/",
    dialerApiUrlInbound: "https://dialerapi.policybazaar.com/api/v2/dialer/getInboundQueueMappingData",
    MatrixCore: "https://matrixcoreapi.policybazaar.com/",
    newSalesviewUrl : "https://mobilematrix.policybazaar.com/pgv/newsv/",
    BlockAgent: 'https://matrixdashboard.policybazaar.com/admin/BlockAgent',
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    MatrixInternal: 'https://internalmatrixapi.policybazaar.com/',
    MatrixTicket: 'https://matrixticket.policybazaar.com/'
  },
  site_base_url: "https://matrixdashboard.policybazaar.com",
  byPassSecurity: false,
  byPassPages: ['BlockAgent'],
  byMenuSecurity: false,
  FOSImageUploadBucket: 'matrixbkt'
};

const incentiveprod = {
  env: "PROD",
  api: {
    base_url: "https://incentivedashboardqa.policybazaar.com/api/v1",
    realtimeurl: "https://agenttracker.policybazaar.com/agentstatus/",
    awsrecordingurl: "https://incentivedashboardqa.policybazaar.com/api/v1/db/awsfile",
    recordingnameurl: "https://matrixlab.policybazaar.com/matrix/getrecordingname/",
    SendOTP: "https://myaccountapi.policybazaar.com/Login/GenerateOTP",
    ValidateOTP: "https://myaccountapiqa.policybazaar.com/Login/ValidateOTP",
    ServiceAPI: "https://mobilematrixapi.policybazaar.com/",
    s3recordingurl: "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/",
    moodleurl: "https://moodle.policybazaar.com/core/api/",
    dialerApiUrl: "https://dialerapi.policybazaar.com/api/dialer/",
    chatAgentUrl: "https://chatapiv2.policybazaar.com/api/v1/",
    progressiveUrl: "https://mobilematrixapi.policybazaar.com/communication/LeadPrioritization.svc/",
    aiUrl: "https://ai.policybazaar.com/workflow/",
    SalesviewUrl : "https://matrix.policybazaar.com/pgv/SV/LeadContent.htm#/Sales/",
    DashboardUrl : "https://agentdashboardapi.policybazaar.com/apiMatrixProduct/v1/",  
    DashboardApiUrl : "https://agentdashboardapi.policybazaar.com/apiMatrixProduct/v1/",  
    waitingAssignedQueue : "https://dialerqaapi.policybazaar.com/api/v2/inbound/waitingAssignedCalls",
    MatrixCoreURL: "https://matrixcoreapi.policybazaar.com/",
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    MatrixInternal: 'https://internalmatrixapi.policybazaar.com/',
    MatrixTicket: 'https://matrixticket.policybazaar.com/'
  },
  site_base_url: "https://incentivedashboardqa.policybazaar.com",
  byPassSecurity: true,
  byPassPages: ['BlockAgent'],
  byMenuSecurity: true

};

const local = {
  env: "LOCAL",
  api: {
    base_url: "http://localhost:80/api/v1",
    socket_url:"http://localhost:80",
    realtimeurl: "https://internalagenttrackerqa.policybazaar.com/agentstatus/",
    awsrecordingurl: "http://***********:80/api/v1/db/awsfile",
    recordingnameurl: "https://matrixlab.policybazaar.com/matrix/getrecordingname/",
    SendOTP: "https://myaccountapi.policybazaar.com/Login/GenerateOTP",
    ValidateOTP: "https://myaccountapiqa.policybazaar.com/Login/ValidateOTP",
    ServiceAPI: "https://progressiveqa.policybazaar.com/",
    s3recordingurl: "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/",
    moodleurl: "https://moodle.policybazaar.com/core/api/",
    dialerApiUrl: "https://dialerapi.policybazaar.com/api/dialer/",
    chatAgentUrl : "https://chatapiv2.policybazaar.com/api/v1/",
    progressiveUrl : "https://progressiveqa.policybazaar.com/communication/LeadPrioritization.svc/",
    aiUrl : "http://************/workflow/",
    SalesviewUrl : "https://progressiveqa.policybazaar.com/pgv/SV/LeadContent.htm#/Sales/",
    DashboardApiUrl : "https://agentdashboardapi.policybazaar.com/apiMatrixProduct/v1/",  
    DashboardUrl : "https://agentdashboard.policybazaar.com/",
    waitingAssignedQueue : "https://dialerqaapi.policybazaar.com/api/v2/inbound/waitingAssignedCalls" ,
    MatrixCoreURL: "https://localhost:44318/",
    MRSURL: "http://localhost:5039/",
    MatrixCore: "https://qamatrixcoreapi.policybazaar.com/",
    newSalesviewUrl : "https://progressiveqa.policybazaar.com/pgv/newsv/",
    dialerApiUrlInbound: "https://dialerqaapi.policybazaar.com/api/v2/dialer/getInboundQueueMappingData",
    BlockAgent: 'https://matrixdashboardqa.policybazaar.com/admin/BlockAgent',
    FOSAUTHKEY: "LGsaWLYmF6YWNav",
    FOSCLIENTKEY: "L6YWNav",
    MatrixInternal: 'https://qainternalmatrixapi.policybazaar.com/',
    MatrixTicket: 'https://matrtixfeedbackqa.policybazaar.com/'
  },
  site_base_url: "http://localhost:3000",
  byPassSecurity: true,
  byPassPages: ['BlockAgent'],
  byMenuSecurity: true,
  FOSImageUploadBucket: 'matrixbktqa'
};


const config = local;

export default {
  ...config
};
