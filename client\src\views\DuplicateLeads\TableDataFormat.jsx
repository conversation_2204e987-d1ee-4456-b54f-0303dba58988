import React from 'react';
import { getuser } from 'utility/utility';

const TableDataFormat = (props) => {
  let { item, ModifiedColumnData, data, index, Index, RoleId, ProductsList, SearchType } = props;

  const checkUserConfig = (data) => {
  const productIds = ProductsList.map(product => product.ProductId);
  const LeadOnlyProductIds = [2,117,139,114];

  if(getuser() && getuser().RoleId && [12,13].includes(parseInt(getuser().RoleId)) && productIds.includes(data.ProductID)){   
      if(SearchType == "SearchLeads" && LeadOnlyProductIds.includes(data.ProductID)){
        return true;
      }   
      else if(SearchType == "DuplicateLeads"){
        return true;
      }
      else{
        return false;
      }
      
    }
    else if(getuser() && getuser().RoleId && ![12,13].includes(parseInt(getuser().RoleId))){
      return true;
    }else{
      return false;
    }   
  }
  
  const dependents = item.dependents || [];
  let showContent = true;
  for (let i = 0; i < dependents.length; i++) {
    const dependent = dependents[i];
    const key = dependent.key || "";
    const value = dependent.value || "";
    if (data[key] != value) {
      showContent = false;
    }
  }

  if (index == 0) {
    return <td data-title={item.name}>{Index}</td>
  }

  if (RoleId === 13 && item.name === 'SalesAgent') {
    return null
  } else {
    let value = '-', text = '-';
    switch (item.type) {
      case 'link':
        value = data[item.accessor];
        text = item.text || '-';
        return (
          <td data-title={item.name}>
            <a href='javascript:void(0)' onClick={(e) => { props.handleLinkClick(e, data, item) }}>
              {item.show === true ? value : ''}
              {item.show === false && value && text}
            </a>
          </td>
        )

      case 'iframe':

        if (item.subtype === 'upload') {
          return <td data-title={item.name}><a href="javascript:void(0)" className="uploadDoc" onClick={(e) => { props.handlePopUp(e, data, item) }}><i className="fa fa-cloud-upload" aria-hidden="true"></i> {item.name}</a></td>
        } else {
          if(showContent) {
            if(item.name == 'LeadID'){
            const showHyperlink = checkUserConfig(data);            
            return <td data-title={item.name}>
              {showHyperlink ? (
              <a href='javascript:void(0)' onClick={(e) => { props.handlePopUp(e, data, item) }}>
                {item.show ? data[item.accessor] : item.text}
              </a>
            ) : (
              // Show simple text if the function returns false
              <span>{item.show ? data[item.accessor] : item.text}</span>
            )}</td>
            }else{
            return <td data-title={item.name}><a href='javascript:void(0)' onClick={(e) => { props.handlePopUp(e, data, item) }}>
              {item.show ? data[item.accessor] : item.text}
            </a>
          </td>
          }} else if(item.disable) {
            return <td data-title={item.name}>{item.show ? data[item.accessor] : item.text}</td>
          } else {
            return <td data-title={item.name}>
            <a href='javascript:void(0)' onClick={(e) => { props.handlePopUp(e, data, item) }}>
              {item.show ? data[item.accessor] : item.text}
            </a>
          </td>
          }
        }

      case 'call':
        return <td data-title={item.name}><a href="javascript:void(0)" onClick={(e) => { props.handleLinkClick(e, data, item) }}><i className="fa fa-phone" aria-hidden="true"></i>{" " + data[item.accessor]} </a></td>

      case 'boolean':
        return <td data-title={item.name}>{data[item.accessor] ? "Yes" : "No"}</td>

      case 'popup':
        text = data[item.accessor] || item.text;
        const overflow = item.overflow || false;
        if (overflow && text.length > 0) {
          text = text.substring(0, 20) + "...";
        }

        if (text === 'Active') {
          return <td data-title={item.name}> {text}</td>
        } else if (item.show && text) {
          return <td data-title={item.name}><a href="javascript:void(0)" onClick={(e) => { props.handlePopUp(e, data, item) }}> {text}</a></td>
        } else if (showContent && text) {
          return <td data-title={item.name}><a href="javascript:void(0)" onClick={(e) => { props.handlePopUp(e, data, item) }}> {text}</a></td>
        } else {
          return <td data-title={item.name}> {'-'}</td>
        }

      case 'mobileno':
      value = data[item.accessor];
      text = item.text || '-';
      return (
        <td data-title={item.name}>
            {value.replace(/.(?=..)/g, 'X')}
        </td>
      )

      default:
        return <td data-title={item.name}>
          <p className="text-ellipsis"  title={(ModifiedColumnData && ModifiedColumnData.length > 14)? ModifiedColumnData : ''}>
          {(ModifiedColumnData)? ModifiedColumnData : 'N.A' }
          </p>
          </td>
    }
  }
}

export default TableDataFormat;