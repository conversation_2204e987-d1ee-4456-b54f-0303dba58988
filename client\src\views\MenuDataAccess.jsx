import React, { useEffect, useState } from 'react';
import {
  List,
  ListItem,
  Radio,
  RadioGroup,
  FormControlLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  InputLabel,
  FormControl,
  Chip,
  Box,
  Typography,
  Divider,
  Grid,
  Button
} from '@mui/material';
import { FetchProcTableWithMenu } from 'store/actions/CommonAction';
import { EditMenuEntityMapping } from 'store/actions/CommonAction';

// const items = [
//   { id: 1, name: 'Item 1' },
//   { id: 2, name: 'Item 2' },
//   { id: 3, name: 'Item 3' },
// ];

// const options = [
//   { id: 1, name: 'Option A' },
//   { id: 2, name: 'Option B' },
//   { id: 3, name: 'Option C' },
//   { id: 4, name: 'Option D' },
// ];

function MenuDataAccess() {

  const [menuList, setMenuList] = useState([]);
  const [entityList, setEntityList] = useState([]);
  const [menuEntityMapping, setMenuEntityMapping]= useState([]);

  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedOptions, setSelectedOptions] = useState([]);

  const [saveButton, setSaveButton] = useState(false);


    useEffect(()=>{

    FetchProcTableWithMenu((errorStatus, data)=>{
        if(!errorStatus)
        {
            if(data)
            {
                if(Array.isArray(data['MenuList']))
                {
                    setMenuList(data['MenuList']);
                }
                if(Array.isArray(data['EntityList']))
                {
                    setEntityList(data['EntityList']);
                }
                if(Array.isArray(data['MenuEntityMapping']))
                {
                   
                    setMenuEntityMapping(data['MenuEntityMapping']);
                }
            }
            // if(Array.isArray(data) && )
            // console.log("The data is ", data);
        }
    })
  },[])


  useEffect(()=>{

    // console.log("Selected item is ", selectedItem)
    if(selectedItem)
    {

        setSaveButton(false);
        if(menuEntityMapping.length>0)
        {
           
            let options=[];
            menuEntityMapping.map((obj)=>{
                if(obj.EntityId== selectedItem)
                {
                    options.push(obj.MenuId);
                }   
            }
            );
            setSelectedOptions(options);
            return;
        }
        setSelectedOptions([]);
    }

  },[selectedItem])

  const handleRadioChange = (event) => {
    setSelectedItem(event.target.value);
    // setSelectedOptions([]); 
  };

  
  const handleDropdownChange = (event) => {

    setSelectedOptions(event.target.value);

    setSaveButton(true)

  };

  const handleSave=()=>{
    
    if(selectedItem)
    {
        // console.log("Selected item is ", selectedItem, selectedOptions);

        // setSaveButton(false);
        setSaveButton(false);

        EditMenuEntityMapping({
            MenuId: selectedOptions,
            EntityId: selectedItem
        },(errorStatus, data)=>{
           
            setSelectedItem(null);
            setSelectedOptions([]);
            if(!errorStatus)
            {
                // console.log("Tjhe data is ", data);
                if(data)
                {
                    if(Array.isArray(data['MenuEntityMapping']))
                        {
                           
                            setMenuEntityMapping(data['MenuEntityMapping']);
                        }
                }

            }
        })

    }

  }

  useEffect(()=>{
    // console.log("The selected options are ", selectedOptions);
  },[selectedOptions])

  return (
    <Box sx={{ maxWidth: 1000, margin: 'auto', padding: 3 }}>
     {saveButton && selectedItem && <Button onClick={handleSave}> Save</Button> }

      <Typography variant="h5" sx={{ marginBottom: 3, textAlign: 'center' }}>
      {selectedItem && 'Select Menu for the Table/Procedure'}
      </Typography>
      {selectedItem && (
        <FormControl fullWidth sx={{ marginBottom: 3 }}>
          <InputLabel>Menus</InputLabel>
          <Select
            multiple
            value={selectedOptions}
            onChange={handleDropdownChange}
            label="Options"
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                {selected.map((value) => {
                  const option = menuList && menuList.length>0 && menuList.find((opt) => opt.MenuId === value);
                  return (
                    <Chip
                      key={value}
                      label={option.MenuName}
                      sx={{ margin: '2px', backgroundColor: '#1976d2', color: '#fff' }}
                    />
                  );
                })}
              </Box>
            )}
          >
            { menuList && menuList.length>0 && menuList.map((option) => (
              <MenuItem key={option.MenuId} value={option.MenuId}>
                <Checkbox checked={selectedOptions.includes(option.MenuId)} />
                <ListItemText primary={option.MenuName} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}

 
      {selectedItem && <Divider sx={{ marginBottom: 3 }} />}

      <RadioGroup value={selectedItem} onChange={handleRadioChange} row>
        <Grid container spacing={2}>
          {entityList.length>0 && entityList.map((option, index) => (
            <Grid item xs={6} key={index}>
              <FormControlLabel
                value={option.EntityId}
                control={<Radio />}
                label={option.EntityName}
              />
            </Grid>
          ))}
        </Grid>
      </RadioGroup>

    
      {/* <RadioGroup value={selectedItem} fullWidth onChange={handleRadioChange}>
        <List sx={{ paddingLeft: 0 }}>
          {entityList.length>0 &&  entityList.map((item) => (
            <ListItem key={item.EntityId} sx={{ paddingLeft: 0, marginBottom: 1 }}>
              <FormControlLabel
                value={item.EntityId}
                control={<Radio color="primary" />}
                label={item.EntityName}
                sx={{ fontSize: '1rem' }}
              />
            </ListItem>
          ))}
        </List>
      </RadioGroup> */}

     
      <Box sx={{ textAlign: 'center', marginTop: 4 }}>
        <Typography variant="caption" color="text.secondary">
        Select a table/proc to display the menus
        </Typography>
      </Box>
    </Box>
  );
}

export default MenuDataAccess;
