
import React from "react";
// reactstrap components

class DropDown extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            items: [],
        }
    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {
        this.setState({ items: nextProps.items });

    }

    displayoption(item) {
        return <option key={item.Id} value={item.Id}>{item.Display}</option>
    }

    render() {


        let { visible, items } = this.props;
        if (!items) {
            items = [];
        }
        if (visible == false) {
            return null;
        }
        return (

            <div className="RadioButton">
                {
                items.map(([text, value], i) => (
                <div key={value+"_"+i }>
                <input id={value+"_"+i} name={this.props.name} onChange={this.props.changed} value={value} type="radio" checked={this.props.value ? this.props.value === value : this.props.isSelected === value} 
                disabled={(this.props.disabled)? this.props.disabled: false}/>
                <label htmlFor={value+"_"+i}>{text}</label>
                </div>
                ))
                }
            </div>
        );
    }
}


export default DropDown;

