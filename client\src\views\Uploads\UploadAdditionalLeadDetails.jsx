import React, { useEffect, useState } from "react";
import {
    <PERSON>,
    <PERSON>H<PERSON>er,
    CardBody,
    CardT<PERSON>le,
    Row,
    Col
  } from "reactstrap";
  import { Button, ButtonGroup, Form, Modal, Table } from 'react-bootstrap';
import DropDown from "views/Common/DropDown";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { FilterColumnList, ProductList, FeatureList, ErrorDataColumns} from "./AdditionalLeadUtility";
import { IsUserAttributesAuthorized, GetAdditionalLDBulkUploadData, UploadFileToS3BucketPvtService, UpdateRenewalFileStatusService, GetStagedFileDataService, GetUniqueGroups } from "../../store/actions/CommonAction";
import ExcelJS from 'exceljs';
import {saveAs} from 'file-saver';
import {v4 as uuid} from 'uuid';
import { getuser } from "utility/utility";
import { Table<PERSON>ontainer, TableHead, TableCell, TableRow, TableBody } from "@mui/material";
import { GetMongoData } from "../../store/actions/CommonMongoAction";

const UploadAdditionalLeadDetails = () => {
    const [IsAuthorized,setIsAuthorized] = useState(false);
    const [FilteredFeature, setFilteredFeature] = useState([]);
    const [Product, setProduct] = useState(0);
    const [Feature, setFeature] = useState(0);
    const [FeatureName, setFeatureName] = useState('');
    const [FilteredColumns, setFilteredColumns] = useState([]);
    const [SelectedFile, setSelectedFile] = useState();
    const [ConsolidatedList, setConsolidatedList] = useState([]);
    const [IsData, setIsData] = useState(false);
    const [ShowStagedData, setShowStagedData] = useState(false);
    const [ShowErrorData, setShowErrorData] = useState(false);
    const [StagedData, setStagedData] = useState([]);
    const [ErrorData, setErrorData] = useState([]);
    const [StagedDataColumns, setStagedDataColumns] = useState([]);
    const [GroupList, setGroupList] = useState([]);
    const [GroupId, setGroupId] = useState(0);

    const ProductChange = (event) => {
        setProduct(event.target.value);
        let value = parseInt(event.target.value);
        let features = FeatureList.filter((item) => (item.ProductId.includes(value)));
        setFilteredFeature(features);
        setIsAuthorized(false);
        setIsData(false);
        setFeature(0);
        setFeatureName('');
    }

    const FeatureChange = async (event) => {
        setIsData(false);
        setIsAuthorized(false);
        setStagedDataColumns([]);
        setGroupId(0);
        if(event.target.value != "0"){
            setFeature(event.target.value);  
            let Auth = false;
            if(event.target.value == 1){
                Auth = await IsUserAttributesAuthorizedMethod(13);
            }
            else if(event.target.value == 2){
                Auth = await IsUserAttributesAuthorizedMethod(14);
            }
            else if(event.target.value == 3){
                Auth = await IsUserAttributesAuthorizedMethod(15);
                GetUniqueGroups({ ProductId: Product}, function(resultData){
                    if(resultData && resultData.data.data && Array.isArray(resultData.data.data[0]) && resultData.data.data[0].length > 0){
                        const groups = resultData.data.data[0].map(({ groupId, GroupName }) => ({ Id: groupId, Display: GroupName }));
                        setGroupList(groups);
                    }
                });
            }
            else if(event.target.value != "0"){
                Auth = true;
            }   
            setIsAuthorized(Auth);
            
            
            if(Auth == true){
                let columns = FilterColumnList(event.target.value);
                setFilteredColumns(columns);

                let feature = FeatureList.filter((item) => (item.Id == event.target.value))
                setFeatureName(feature[0].Name);

                if([1,2,3].includes(parseInt(event.target.value))){
                    GetAdditionalLDBulkUploadData({ ProductId: Product, ProcessName : feature[0].Name }, function(resultData){
                        if(resultData && resultData.data.data && Array.isArray(resultData.data.data[0]) && resultData.data.data[0].length > 0){
                            setConsolidatedList(resultData.data.data[0]);
                            setIsData(true);
                        }
                    })
                }
            }
        }
    }

    const GroupChange = (event) => {
        setGroupId(event.target.value);
    }

    const IsUserAttributesAuthorizedMethod = async (AttributeId) =>{
        return IsUserAttributesAuthorized({AttributeId: AttributeId}).then(function (data){
            if(data.data.data.Authorized){
                setIsAuthorized(true);
                return true;
            }
            else{
                setIsAuthorized(false);
                return false
            }
        }).catch((result) => {
            setIsAuthorized(false);
            return false;
        })
    }

    const handleDownloadSample = async () => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Sheet1');

        let columns = [];
        FilteredColumns.Columns.forEach((item, idx) => {
            columns.push({header : item.Column});
        })
        
        worksheet.columns = columns;

        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Sample Sheet for ${FeatureName}.xlsx`);
    }

    const handleFileChange = (event) => {
        setSelectedFile(event.target.files[0]);
    }

    const handleFileUpload = () => {
        if(SelectedFile && isExcel(SelectedFile.name))
        {
            var uuidNo = uuid();
            var Uploadedby = getuser().UserID;

            let formData = new FormData();        

            var data = {
              Processtype: 2,
              UniqueId: uuidNo.trim(),
              productId: Product,
              Status: "Saved",
              GroupId: GroupId > 0 ? GroupId : 0,
              AssignedUser: "",
              SMEProcessType: "",
              folderName: "AdditionalLeadDetails",
              Uploadedby: Uploadedby.trim(),
              FileName : SelectedFile.name,
              UploadedFile : SelectedFile,
              ProcessName : FeatureName
            };

            Object.keys(data).map((key)=>{
                formData.append(key, data[key]);
            })

            UploadFileToS3BucketPvtService(formData, function (requestData){
                if(requestData){
                    setSelectedFile();
                    GetAdditionalLDBulkUploadData({ ProductId: Product, ProcessName : FeatureName }, function(resultData){
                        if(resultData && resultData.data.data && Array.isArray(resultData.data.data[0])){
                            setConsolidatedList(resultData.data.data[0]);
                            setIsData(true);
                        }
                    })
                }
            })
        }
    }

    const UpdateRenewalFileStatus = (UniqueId) => {
        UpdateRenewalFileStatusService({UploadedBy : getuser().UserID, Status : "DataUnderProcessing", UniqueId : UniqueId}, function(resultData){
            GetAdditionalLDBulkUploadData({ ProductId: Product, ProcessName : FeatureName }, function(resultData){
                if(resultData && resultData.data.data && Array.isArray(resultData.data.data[0])){
                    setConsolidatedList(resultData.data.data[0]);
                    setIsData(true);
                }
            })
        })
    }

    const HandleStagedOpen = (UniqueId) => {
        GetStagedFileDataService({UploadedBy : getuser().UserID, UniqueId : UniqueId}, function(resultData){
            if(resultData && resultData.data.data && Array.isArray(resultData.data.data[0])){
                let columns = []
                FilteredColumns.Columns.map((row) => {
                    columns.push({Column: row.Column, ProductId : 117, key : row.key });
                })
                columns.push({Column: "Error Message", ProductId : 117, key : "ErrorMessage" });

                setStagedDataColumns(columns);
                
                let data = resultData.data.data[0].map((item) => 
                    Object.fromEntries(columns.map((col) => [col.key, item[col.key] || "N/A"]))
                )

                setStagedData(data);
            }
            
        });
        setShowStagedData(true);
    }

    const HandleErrorOpen = (TrackingId) => {
        setShowErrorData(true);
        GetMongoData({
            limit: 10,
            skip: 0,
            root: "FileProcessingErrorLogs",
            con: { "TrackingId" : TrackingId },
            c: "L"},

            function(resultData){
                if(resultData && resultData.data.data && resultData.data.data[0])
                {
                    setErrorData(resultData.data.data);
                }
            }
        )
        
    }

    const HandleStagedClose = () => {
        setStagedData([]);
        setStagedDataColumns([]);
        setShowStagedData(false);
    }

    const HandleErrorClose = () => {
        setErrorData([]);
        setShowErrorData(false);
    }

    const getExtension = (filename) => {
        var parts = filename.split(".");
        return parts[parts.length - 1];
      }
    
    const isExcel = (filename) => {
        var ext = getExtension(filename);
        switch (ext.toLowerCase()) {
          case "xls":
          case "xlsx":
            return true;
          default:
            return true;
        }
    }

    return(
        <div>
            <Row>
                <Col md="12">
                  <Card>
                    <CardHeader>
                      <Row>
                        <Col md={11}>
                          <CardTitle tag="h4">Upload Data</CardTitle>
                        </Col>
                      </Row>
                    </CardHeader>
                        <CardBody>
                            <Row>
                                <Col>
                                    <label htmlFor="criteria" className="form-label">
                                      *Product
                                    </label>
                                    <DropDown firstoptionvalue={true} items={ProductList} onChange={ProductChange}>Product</DropDown>
                                </Col>
                                <Col>
                                    <label htmlFor="criteria" className="form-label">
                                      *Feature
                                    </label>
                                    <DropDown firstoptionvalue={true} items={FilteredFeature} value={Feature} onChange={FeatureChange}>Feature</DropDown>
                                </Col>
                            </Row>
                            {IsAuthorized && IsAuthorized == true &&
                            <>
                                {Feature && Feature == 3 && GroupList && Array.isArray(GroupList) && GroupList.length > 0 &&
                                <Row>
                                    <Col>
                                        <br/>
                                        <label htmlFor="criteria" className="form-label">
                                          *GroupId
                                        </label>
                                        <DropDown firstoptionvalue={true} items={GroupList} onChange={GroupChange}>Product</DropDown>
                                    </Col>
                                </Row>}
                                <Row>
                                    <Col>
                                        <br/>
                                        <Button onClick={handleDownloadSample} >
                                            Sample File Download
                                        </Button>
                                    </Col>
                                    <Col>
                                        <br/>
                                        <Button onChange={handleFileChange} className="btn btn-primary">
                                            <input type="file"  className="fileUpload" />
                                        </Button>
                                    </Col>
                                    <Col>
                                        <br/>
                                        <span className="form-label">
                                          Upload here - &nbsp;
                                        </span>
                                        <Button onClick = {handleFileUpload}>
                                            Save
                                        </Button>
                                    </Col>
                                </Row>
                            </>}
                            {
                                IsData && IsData == true &&    
                                <>
                                <br/>                      
                                    <Row> 
                                        <TableContainer>
                                            <Table>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>File Name</TableCell>
                                                        <TableCell align="right">Status</TableCell>
                                                        <TableCell align="right">Stage Data</TableCell>
                                                        <TableCell align="right">View Staged Data</TableCell>
                                                        <TableCell align="right">View Error Console</TableCell>
                                                        <TableCell align="right">UpdatedOn</TableCell>
                                                        <TableCell align="right">Upload By</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {ConsolidatedList.map((row) => (
                                                        <TableRow>
                                                            <TableCell>
                                                                {row.Filename}
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                {row.Status}
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                <ButtonGroup>
                                                                <Button
                                                                    id="btnstaging"
                                                                    className="btn btn-success btnSmall"
                                                                    disabled={row.Status == "DataStaged" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured"|| row.Status == "DateoutofLimit"}
                                                                    onClick={() => UpdateRenewalFileStatus(row.UniqueId)}
                                                                >
                                                                  Start Staging
                                                                </Button>
                                                                </ButtonGroup>
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                <ButtonGroup aria-label="Basic example">
                                                                    <Button
                                                                        className="btn btn-primary btnSmall"
                                                                        disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured"|| row.Status == "DateoutofLimit"}
                                                                        onClick={() => HandleStagedOpen(row.UniqueId)}
                                                                    >
                                                                        View Staged Data
                                                                    </Button>
                                                                </ButtonGroup>
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                <ButtonGroup aria-label="Basic example">
                                                                    <Button
                                                                        className="btn btn-danger btnSmall"
                                                                        disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "DataStaged"|| row.Status == "DateoutofLimit"}
                                                                        onClick={() => HandleErrorOpen(row.UniqueId)}
                                                                    >
                                                                        Error Console
                                                                    </Button>
                                                                </ButtonGroup>
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                {row.createdon}
                                                            </TableCell>
                                                            <TableCell align="right">
                                                                {row.UploadedBy}
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>

                                            </Table>
                                        </TableContainer>
                                    </Row>
                                </> 
                            }
                            
                        </CardBody>
                  </Card>
                </Col>
            </Row> 
            <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={ShowStagedData}
                onHide={HandleStagedClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Uploaded Leads</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                        {StagedData && Array.isArray(StagedData) && StagedData.length > 0 ?
                        <>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        {StagedDataColumns.map((item) => (
                                            <TableCell>
                                                {item.Column}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {StagedData.map((row, index) => (
                                        <TableRow key={index}>
                                            {StagedDataColumns.map((col) => (
                                                <TableCell key={col.key}>
                                                    {row[col.key]}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                        </> 
                        :
                        <>
                            <h6>No Data Found!</h6>
                        </>
                        }
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={HandleStagedClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
          <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={ShowErrorData}
                onHide={HandleErrorClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Error Console</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                    {ErrorData && Array.isArray(ErrorData) && ErrorData.length > 0 ?
                        <>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        {ErrorDataColumns.map((item) => (
                                            <TableCell>
                                                {item.Column}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {ErrorData.map((row) => (
                                        <TableRow>
                                            <TableCell>
                                                {row.TrackingId !== undefined ? row.TrackingId : ''}
                                            </TableCell>
                                            <TableCell>
                                                {row.Createon !== undefined ? row.Createon : ''}
                                            </TableCell>
                                            <TableCell>
                                                {row.Exception !== undefined ? row.Exception : ''}
                                            </TableCell>
                                            <TableCell>
                                                {row.PrimaryColumn !== undefined ? row.PrimaryColumn : ''}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                        </> 
                        :
                        <>
                            <h6>No Data Found!</h6>
                        </>
                        }
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={HandleErrorClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
        </div>
    )
}

export default UploadAdditionalLeadDetails;