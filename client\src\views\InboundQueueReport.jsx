import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser, fnDatatableCol, joinObject } from '../utility/utility.jsx';
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
import moment from 'moment';
import AlertBox from './Common/AlertBox';
import Loader from './Common/Loader';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datetime/css/react-datetime.css'
import DateRange from "./Common/DateRange"

// reactstrap components
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import {Button, Form } from 'react-bootstrap';
class InboundQueueReport extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "InboundCallData",
            PageTitle: "Inbound Queue Report",
            ProductData: [],
            ProductId: 0,
            ReportDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
            ReportTime: null,
            startdate: moment().subtract(9, 'days').format("YYYY-MM-DD 00:00:00"),
            enddate: moment().format("YYYY-MM-DD hh:mm:ss"),
            maxdate: moment().subtract(60, 'days').format("YYYY-MM-DD hh:mm:ss"),
            showMoreInfoModal: false,
            MoreInfoData: [],
            addClass: "fa fa-play-circle",
            selectedIvrProduct: "",
            ivrType: "0",
            ivrProducts: [],
            ivrQueues: [],
            selectedValue: [{ label: "Select IVR Queues", value: "0" }],
            selectedProdValue: [{label: "Select IVR Products", value: "0" }],
            queueServerIp: "",
            ConfType: '',
            addClass: '',
            IBLoader: false,
        };
        this.ivrtypechange = this.ivrtypechange.bind(this);
        this.ivrproductchange = this.ivrproductchange.bind(this);
        this.onSelect = this.onSelect.bind(this);

        this.columnlist = [
            {
                name: "CallId",
                selector: "CallId",
                width: "150px",
                type: "string",
                cell: row => <div className="CallId">{row.CallId ? row.CallId : "N.A"}</div>,
            },
            {
                name: "MainCallId",
                selector: "MainCallId",
                type: "string",
                cell: row => <div className="MainCallId">{row.MainCallId ? row.MainCallId : "N.A"}</div>,
            },
            {
                name: "Product",
                selector: "Product",
                searchable: true
            },
            {
                name: "IvrType",
                selector: "IvrType",
                cell: row => <div className="IvrType">{row.IvrType ? row.IvrType : "N.A"}</div>,

            },
            {
                name: "IvrSubType",
                selector: "IvrSubType",
                cell: row => <div className="IvrSubType">{row.IvrSubType ? row.IvrSubType : "N.A"}</div>,

            },
            {
                name: "Ivr1",
                selector: "Ivr1",
                cell: row => <div className="Ivr1">{row.Ivr1 ? row.Ivr1 : "N.A"}</div>,

            },
            {
                name: "Ivr2",
                selector: "Ivr2",
                cell: row => <div className="Ivr2">{row.Ivr2 ? row.Ivr2 : "N.A"}</div>,

            },
            {
                name: "TollFreeNumber",
                selector: "TollFreeNumber",
                type: "number",
                searchable: true,
                cell: row => <div className="TollFreeNumber">{row.TollFreeNumber ? row.TollFreeNumber : "N.A"}</div>,

            },
            // {
            //     name: "TollFreeType",
            //     selector: "TollFreeType",
            //     cell: row => <div className="LeadID">{row.TollFreeType ? row.TollFreeType : "N.A"}</div>,
            // },
            // {
            //     name: "Queueselector",
            //     selector: "Queueselector",
            //     type: "string",
            //     searchable: true,
            //     cell: row => <div className="LeadID">{row.Queueselector ? row.Queueselector : "N.A"}</div>,
            // },
            {
                name: "QueueName",
                selector: "QueueName",
                type: "string",
                searchable: true,
                cell: row => <div className="QueueName">{row.QueueName ? row.QueueName : "N.A"}</div>,
            },
            {
                name: "LeadID",
                selector: "LeadID",
                type: "string",
                searchable: true,
                cell: row => <div className="LeadID">{row.LeadID ? row.LeadID : "N.A"}</div>,
            },
            {
                name: "CustomerUID",
                selector: "CustomerID",
                type: "string",
                searchable: true,
                cell: row => <div className="CustomerID">{row.CustomerID ? row.CustomerID : "N.A"}</div>,
            },
            {
                name: "BookingStatus",
                selector: "BookingStatus",
                width: "150px",
                type: "string",
                cell: row => <div className="BookingStatus">{row.BookingStatus ? row.BookingStatus : "N.A"}</div>,
            },
            {
                name: "AsignedAgentType",
                selector: "AsignedAgentType",
                width: "150px",
                type: "string",
                cell: row => <div className="AsignedAgentType">{row.AsignedAgentType ? row.AsignedAgentType : "N.A"}</div>,
            },
            {
                name: "AssignedAgent",
                selector: "AssignedAgent",
                type: "string",
                cell: row => <div className="AssignedAgent">{row.AssignedAgent ? row.AssignedAgent : "N.A"}</div>,
            },
            {
                name: "AssignAgentStatus",
                selector: "AssignAgentStatus",
                type: "string",
                cell: row => <div className="AssignAgentStatus">{row.AssignAgentStatus ? row.AssignAgentStatus : "N.A"}</div>,
            },
            {
                name: "IsCallPickedByAssignedAgent",
                selector: "IsCallPickedByAssignedAgent",
                type: "bool",
                //cell: row => <div className="IsCallPickedByAssignedAgent">{row.IsCallPickedByAssignedAgent ? "true" : "false"}</div>,
            },
            {
                name: "AgentId",
                selector: "AgentId",
                width: "150px",
                type: "string",
                searchable: true,
                cell: row => <div className="AgentId">{row.AgentId ? row.AgentId : "N.A"}</div>,
            },
            {
                name: "CallTime",
                selector: "CallTime",
                cell: row => <div className="CallTime">{row.CallTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
                >{row.CallTime}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "QueueEnterTime",
                selector: "QueueEnterTime",
                cell: row => <div className="QueueEnterTime">{row.QueueEnterTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
                >{row.QueueEnterTime}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "MainQueueEnterTime",
                selector: "MainQueueEnterTime",
                cell: row => <div className="MainQueueEnterTime">{row.MainQueueEnterTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
                >{row.MainQueueEnterTime}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "AnswerTime",
                selector: "AnswerTime",
                cell: row => <div className="AnswerTime">{row.AnswerTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
                >{row.AnswerTime}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "CallEndTime",
                selector: "CallEndTime",
                cell: row => <div className="CallEndTime">{row.CallEndTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
                >{row.CallEndTime}</Moment> : "N.A"}</div>,
                type: "datetime",
                sortable: true,
                width: "150px",
            },
            {
                name: "QueueWaitTime",
                selector: "QueueWaitTime",
                cell: row => <div className="QueueWaitTime">{row.QueueWaitTime ? row.QueueWaitTime : "N.A"}</div>,

            },
            {
                name: "IvrTime",
                selector: "IvrTime",
                cell: row => <div className="IvrTime">{row.IvrTime ? row.IvrTime : "N.A"}</div>,
            },
            {
                name: "IsQueueCall",
                selector: "IsQueueCall",
                cell: row => <div className="IsQueueCall">{row.IsQueueCall ? row.IsQueueCall : "N.A"}</div>,
            },
            // {
            //     name: "IsAgentCall",
            //     selector: "IsAgentCall",
            //     cell: row => <div className="IsAgentCall">{row.IsAgentCall ? row.IsAgentCall : "N.A"}</div>,
            // },
            {
                name: "CallStatus",
                selector: "CallStatus",
                searchable: true,
                cell: row => <div className="CallStatus">{row.CallStatus ? row.CallStatus : "N.A"}</div>,

            },
            {
                name: "talktime",
                selector: "talktime",
                cell: row => <div className="talktime">{row.talktime ? row.talktime : "N.A"}</div>,
            },
            {
                name: "duration",
                selector: "duration",
                cell: row => <div className="duration">{row.duration ? row.duration : "N.A"}</div>,
            },
            {
                name: "IsTransferCall",
                selector: "IsTransferCall",
                cell: row => <div className="IsTransferCall">{row.IsTransferCall ? row.IsTransferCall : "N.A"}</div>,
            },
            {
                name: "MainCall",
                selector: "MainCall",
                cell: row => <div className="MainCall">{row.MainCall ? row.MainCall : "N.A"}</div>,
            },
            {
                name: "TransferTo",
                selector: "TransferTo",
                cell: row => <div className="TransferTo">{row.TransferTo ? row.TransferTo : "N.A"}</div>,
            },
            {
                name: "TransferFrom",
                selector: "TransferFrom",
                cell: row => <div className="TransferFrom">{row.TransferFrom ? row.TransferFrom : "N.A"}</div>,
            },
            {
                name: "isRegNumCall",
                selector: "isRegNumCall",
                cell: row => <div className="isRegNumCall">{row.isRegNumCall ? row.isRegNumCall : "N.A"}</div>,
            },
            {
                name: "IssueName",
                selector: "IssueName",
                cell: row => <div className="IssueName">{row.IssueName ? row.IssueName : "N.A"}</div>,

            },
            {
                name: "SubIssueName",
                selector: "SubIssueName",
                cell: row => <div className="SubIssueName">{row.SubIssueName ? row.SubIssueName : "N.A"}</div>,

            },
            {
                name: "InsurerName",
                selector: "InsurerName",
                cell: row => <div className="InsurerName">{row.InsurerName ? row.InsurerName : "N.A"}</div>,

            },
            {
                name: "QueryType",
                selector: "QueryType",
                cell: row => <div className="QueryType">{row.QueryType ? row.QueryType : "N.A"}</div>,

            },
            {
                name: "CTCAPISoure",
                selector: "CTCAPISoure",
                cell: row => <div className="CTCAPISoure">{row.CTCAPISoure ? row.CTCAPISoure : "N.A"}</div>,
            },
            // {
            //     name: "IsTicketAssignedAgent",
            //     selector: "IsTicketAssignedAgent",
            //     type: "bool",

            // },
            // {
            //     name: "BMSQueueName",
            //     selector: "BMSQueueName",
            //     cell: row => <div className="BMSQueueName">{row.BMSQueueName ? row.BMSQueueName : "N.A"}</div>,

            // },
            {
                name: "TicketID",
                selector: "TicketID",
                cell: row => <div className="TicketID">{row.TicketID ? row.TicketID : "N.A"}</div>,
            },
            {
                name: "CallRouteFrom",
                selector: "callRouteFrom",
                cell: row => <div className="callRouteFrom">{row.callRouteFrom ? row.callRouteFrom : "N.A"}</div>,
            },
            {
                name: "CallENDDescription",
                selector: "CallENDDescription",
                width: "150px",
                cell: row => <div className="CallENDDescription">{row.CallENDDescription ? row.CallENDDescription : "N.A"}</div>,
            },
            {
                name: "ClaimBUId",
                selector: "claimibuid",
                cell: row => <div className="claimibuid">{row.claimibuid ? row.claimibuid : "N.A"}</div>
            },
            {
                name: "ClaimId",
                selector: "claimid",
                cell: row => <div className="claimid">{row.claimid ? row.claimid : "N.A"}</div>
            },
            {
                name: "LastData",
                selector: "lastdata",
                cell: row => <div className="lastdata">{row.lastdata ? row.lastdata : "N.A"}</div>
            },
            {
                name: "holdtime",
                selector: "holdtime",
                cell: row => <div className="holdtime">{row.holdtime ? row.holdtime : "N.A"}</div>
            }
            // {
            //     name: "IsEWTPlayed",
            //     selector: "IsEWTPlayed",
            //     type: "bool",
            // },

        ];

        this.IvrTypeList = {
            config:
            {
                root: "Ivrtypelist",
                data: [
                    { Id: "service", Display: "Service" }, 
                    { Id: "Sales", Display: "Sales" }, 
                    { Id: "Claim", Display: "Claim" },
                    {Id: "PbPartners", Display: "PbPartners"}
                ],
            }
        };


    }


    componentWillMount() {
        // this.props.GetMySqlData({
        //   root: this.state.root,
        //   startdate: this.state.startdate,
        //   enddate: this.state.enddate,
        //   conftype: 'sales_to_service',
        // }, function (result) {
        //   this.setState({ InboundQueueReport: result.data.data[0] });
        // }.bind(this));

    }

    fnBindStore(col, nextProps) {
        //debugger;
        if (col.type == "dropdown") {
            let items;
            if (nextProps.CommonData[this.state.root] && (nextProps.CommonData[col.config.root] || col.config.data)) {
                items = joinObject(nextProps.CommonData[this.state.root], (nextProps.CommonData[col.config.root] || col.config.data), col.name)
                this.setState({ items: items });
            }
        }
    }

    componentWillReceiveProps(nextProps) {
        //debugger;
        if (!nextProps.CommonData.isError) {
            //debugger;
            //this.setState({ InboundQueueReport: nextProps.CommonData[this.state.root] });
            this.setState({ items: nextProps.CommonData[this.state.root] });
            this.setState({ store: nextProps.CommonData });
            this.columnlist.map(col => (
                this.fnBindStore(col, nextProps)
            ));
        }
    }

    fnDatatableCol() {

        var columns = fnDatatableCol(this.columnlist);
        return columns;
    }

    fetchInboundData() {
        this.setState({addClass:'disabled'});
        if (this.state.ivrType == 0) {
            toast("Please enter ivrType", { type: 'error' });
            this.setState({ addClass: '' });
            return;
        }
        
        if (this.state.selectedProdValue.length == 1 && this.state.selectedProdValue[0].label == "Select IVR Products") {
            toast("Please enter ivrProducts", { type: 'error' });
            this.setState({ addClass: '' });
            return;
        }
        if (this.state.selectedValue.length == 1 && this.state.selectedValue[0].label == "Select IVR Queues") {
            toast("Please enter ivrQueues", { type: 'error' });
            this.setState({ addClass: '' });
            return;
        }  
        
        this.setState({ IBLoader : true});

        let queues = this.state.selectedValue;

        var serverip = queues.map(function (val) {
            return val.serverip;
        });

        var queuestring = queues.map(function (val) {
            return val.label;
        });
        var selectedqueues = queuestring.join(",");

        this.props.GetCommonData({
            root: this.state.root,
            startdate: this.state.startdate,
            enddate: this.state.enddate,
            queues: selectedqueues,
        }, function (result) {
            this.setState({ InboundQueueReport: result.data.data[0] ,addClass:'', IBLoader : false});
            let nextProps = this.props;
            this.columnlist.map(col => (
                this.fnBindStore(col, nextProps)
            ));
        }.bind(this));

        this.setState({ addClass: '' });

    }

    fetchProductData() {

        this.props.GetMySqlData({
            root: "getProduct",
            ivrtype: this.state.ivrType 
        }, function (result) {

            const arr = result?.data?.data;
            if(arr){
                const dataProds = [];
                arr.forEach((element) => {
                    dataProds.push({
                        label: element.Display,
                        value: element.Display.toLowerCase(),
                        Id: element.Id,
                    })
                })
                if(arr.length == 1){
                    this.setState({
                        ivrProducts: dataProds,
                        selectedProdValue: [{label: "Select IVR Products", value: "0" }],
                    })
                } else {
                this.setState({ ivrProducts: dataProds });
                }
            }
        }.bind(this));

    }

    fetchQueueData(newselectedList) {
        //debugger;
        this.props.GetMySqlData({
            root: "getIvrQueue",
            ivrType: this.state.ivrType,
            ivrProduct: this.state.selectedIvrProduct,
        }, function (result) {
            this.setState({
                ivrQueues: result && result.data && result.data.data,
                selectedProdValue: newselectedList,

            });
        }.bind(this));

    }

    ivrtypechange(e, props) {
        this.setState({
            ivrType: e.target.value,
            ivrProducts: [],
            ivrQueues: [],
            selectedValue: [{ label: "Select IVR Queues", value: "0" }],
            selectedProdValue: [{label: "Select IVR Products", value: "0" }],

        }, function () {
            this.fetchProductData();
        });
    }

    ivrproductchange(selectedList, selectedItem) {

        const newselectedList = selectedList.filter(task => {
            return (
                task.label !== 'Select IVR Products'
            )
        });

        let queuestring = newselectedList.map(function (val) {
            return val.Id;
        });

        let selectedqueues = queuestring.join(",");

        this.setState({

            selectedIvrProduct: selectedqueues,
            selectedProdValue: newselectedList,
            ivrQueues : [],
            selectedValue : [{ label: "Select IVR Queues", value: "0" }]
        }, function () {
            if(newselectedList.length > 0) {
                this.fetchQueueData(newselectedList);
            }
        });
    }

    conftypechange(e, props) {
        this.setState({
            ConfType: e.target.value
        }, function () {
            //this.fetchConferenceData();
        });

    }

    handleChange = (e, props) => {

        if (e._isAMomentObject) {
            this.setState({ ReportDate: e.format("YYYY-MM-DD") }, function () {
                this.fetchCallBackData();
            });
        }
    }

    onSelect(selectedList, selectedItem) {
        const newselectedList = selectedList.filter(task => {
            return (
                task.label !== 'Select IVR Queues'
            )
        });
        this.setState({ selectedValue: newselectedList });
    }

    // Validation on StartDate
    validation = (StartDateValue) => {
        
        const date = new Date();
        const startDate = new Date(StartDateValue);

        startDate.setDate(startDate.getDate()+9);

        if(date-startDate<=0){
            this.setState({
                startdate: StartDateValue,
                enddate: moment().format("YYYY-MM-DD 23:59:59")
            })
        } else {
            this.setState({
                startdate: StartDateValue,
                enddate: moment(StartDateValue).add(9, 'days').format("YYYY-MM-DD 23:59:59")
            })
        }
    };

    // Validation on EndDate
    validationEndDate = (EndDateValue) => {
        this.setState({ enddate: EndDateValue});
    };

    CheckLoader() {
          if (this.state.IBLoader)
            return <Loader />;
        
      }

    render() {
        const columns = this.columnlist;
        //const columns = this.fnDatatableCol();
        const moreinfocolumns = this.moreinfolist;
        const { items, PageTitle, InboundQueueReport, showAlert, AlertMsg, AlertVarient, ReportTime, MoreInfoData } = this.state;
        let selectedLeads = [];


        return (
            <>
                <div className="content">
                    <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>
                                    <Row>
                                        <Col md={12}>
                                            <CardTitle tag="h5">{PageTitle}</CardTitle>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col md={2} className="mt-4">
                                            <Form.Group controlId="ivrtype_dropdown">
                                                <DropDownListMysql firstoption="Select Ivr Type" value={this.state.ivrType} col={this.IvrTypeList} onChange={this.ivrtypechange}>
                                                </DropDownListMysql>
                                            </Form.Group>
                                        </Col>
                                        <Col md={2} className="mt-4">
                                            <MultiSelect
                                                options={this.state.ivrProducts} // Options to display in the dropdown
                                                value={this.state.selectedProdValue} // Preselected value to persist in dropdown
                                                onChange={this.ivrproductchange} 
                                                // Function will trigger on select event                                                
                                                // labelledBy={"Select IVR Products"}
                                                selectAllLabel={"Select ALL IVR Products"}
                                            />

                                        </Col>
                                        <Col md={3} className="mt-4">
                                            <MultiSelect
                                                options={this.state.ivrQueues} // Options to display in the dropdown
                                                value={this.state.selectedValue} // Preselected value to persist in dropdown
                                                onChange={this.onSelect} // Function will trigger on select event                                                
                                                // labelledBy={"Select IVR Queues"}
                                                selectAllLabel={"Select ALL IVR Queues"}
                                            />
                                        </Col>

                                        <Col md={4} style={{display: 'flex', gap: '1rem'}}>
                                            <DateRange 
                                                days={9}
                                                FromDate= {" "} 
                                                ToDate= {" "}
                                                startDate={this.state.startdate}
                                                endDate={this.state.enddate}
                                                EndDateFixed={true}
                                                onStartDate={this.validation}
                                                onEndDate={this.validationEndDate}
                                                >
                                            </DateRange>
                                        </Col>
                                        
                                        <Col md={1} className="mt-4">
                                            <Button className={this.state.addClass} variant="primary" onClick={() => this.fetchInboundData()}>Fetch{this.CheckLoader() }</Button>
                                        </Col>

                                    </Row>

                                </CardHeader>
                                <CardBody className="InboundQueueReport">
                                    <DataTable
                                        columns={columns}
                                        data={(InboundQueueReport && InboundQueueReport.length > 0) ? InboundQueueReport : []}
                                    />
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>



                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetMySqlData,
        GetDataDirect,

    }
)(InboundQueueReport);
