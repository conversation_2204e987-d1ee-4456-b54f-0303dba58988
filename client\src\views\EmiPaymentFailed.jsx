
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, PostCommunicationData,ValidateAddLeadToPriorityQueue
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import _ from 'underscore';
import DropDown from './Common/DropDown';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
//import { func } from "prop-types";
//import moment from "moment";
import './customStyling.css';

class EmiPaymentFailed extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "EMI Payment Failed Leads",
      EmiPaymentFailedLeads: [],
      showAssignLeadPopUp: false,
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      formvalue: {},
      paymentStatuses: [
        {Id: 1, Display: "Rejected, multiple attempts not reachable"},
        {Id: 4, Display: "Paid "},
        {Id: 2, Display: "Not reachable- Call later "},
        {Id: 5, Display: "Not interested "},
        {Id: 3, Display: "Call later as asked by Customer"}
      ]
    };
    this.dtRef = React.createRef();
    this.myInputRef = React.createRef();

    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadId",        
      },
      {
        name: "Proposal No",
        selector: "ProposalNo",        
      },
      {
        name: "Insurer Name",
        selector: "InsurerName",        
      },
      {
        name: "Cancellation Reason",
        selector: "FailureReason",        
      },
      {
        name: "Mode Of Payment",
        selector: "PaymentMode",        
      },
      {
        name: "EMI Missed",
        selector: "EMIMissed",        
      },
      {
        name: "Total Amount Pending",
        selector: "AmountPending",        
      },
      {
        name: "Grace Period Ends By",
        sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.GracePeriodDate}</Moment>
      },
      {
        name: "Current Status",
        selector: "Status",
        width: "150px",
        cell: row => 
        row.Status !== '-' ? 
        (
          <div className="wrap currentPaymentStatus">
            {row.Status}
          </div>
        ) : row.Status        
      },
      {
        name: "Action",
        width: "120px",
        cell: row => 
        (
          <div className="py-1">
            <DropDown firstoption="Select" value={row.StatusId} id={"select_" + row.LeadId} items={this.state.paymentStatuses} onChange={(e) => this.handlePaymentStatusChange(e, row)}></DropDown>
          {/*
            <Row>
              <Col md="6">
              </Col>
              <Col md="6">
                <ButtonGroup aria-label="">
                  <Button variant="secondary" id={"save_" + row.LeadId} onClick={() => this.handleSave(row)}>Save</Button>
                </ButtonGroup>
              </Col>
            </Row>
          */}
          </div>
        )
      },
      
    ];
  }
  
  OpenAssignLeadPopUp(row) {
    this.setState({ showAssignLeadPopUp: true });
  }
  
  componentWillReceiveProps(nextProps) {
    
    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["EmiPaymentFailed"]) {
        let CBList = nextProps.CommonData["EmiPaymentFailed"];
        this.setState({ EmiPaymentFailedLeads: CBList[0] });
      }
    }
  }
  
  handlePaymentStatusChange(e, row) {
    //console.log(e.target.value, row);
    if(e.target.value === "0" || e.target.value === "") {
      toast.error("Select a status"); 
    } else {
      row.newStatus = parseInt(e.target.value);
      this.setState({ formvalue: row });
      this.handleSave(row);
    }
  };

  handleSave(row) {
    let formvalue = JSON.parse(JSON.stringify(row));

    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "UpdateEMIPaymentStatus",
      params: [{ UserId: getuser().UserID, LeadId: formvalue.LeadId, status: formvalue.newStatus }]
      }, function (data) {
        if(data.data.status === 200) 
        toast("Status submitted successfully!", { type: 'success' });
        else 
        toast.error("Status could not be submitted"); 
        
        setTimeout(function () {
          window.location.reload(false);
          //this.fetchCallBackData();
        }.bind(this), 2000);

      });
  }

  handleClose() {
    this.setState({ showAssignLeadPopUp: false })
  }

  componentDidMount() {
    this.getPaymentStatusList();
    this.fetchCallBackData();
  }

  fetchCallBackData() {
    var SelectedSupervisors = this.state.SelectedSupervisors;
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "EmiPaymentFailed",
      params: [{ UserId: getuser().UserID }]
      //params: [{ UserId: 8224 }]
    });

    if (this.state.SelectedRows.length > 0) {
      this.dtRef.current.handleClearRows();
    }

  }

  getPaymentStatusList() {
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: "EMIFailedStatusMaster",
      //cols: GetJsonToArray(this.SurveyMasterList, "name"),
      con: [{"IsActive" : 1}],
      c: "L",
    }, function (result) {
        var paymentStatuslist = [];
        result.data.data[0].forEach(element => {
            paymentStatuslist.push({
                "Id": element.Id,
                "Display": element.status
            });
        });
        this.setState({ paymentStatuses: paymentStatuslist });
    }.bind(this));
  }

  onSelectedAgent(e) {
    this.setState({ SelectedAgentAssigTo: e.target.value });
  }
  AssignLead() {
    const { SelectedRows, SelectedAgentAssigTo } = this.state;
    for (let index = 0; index < SelectedRows.length; index++) {
      const element = SelectedRows[index];
      //toast("Lead (" + element.LeadId + ") assign successfully", { type: 'success' });

      var lead = {
        "LeadId": element.LeadId,
        "Name": element.CustName,
        "CustomerId": element.CustomerID,
        "UserID": parseInt(getuser().UserID),
        "Priority": 0,
        "ProductId": element.ProductId,
        "Reason": 'BookedLead',
        "ReasonId": 30,
        "CallStatus": "",
        "IsAddLeadtoQueue":1,
        "IsNeedToValidate":0
      }
      var reqData = {
        "UserId": parseInt(getuser().UserID),
        "Leads": [lead]
      };

      ValidateAddLeadToPriorityQueue(reqData , function (resultData)
        {
          try{
            if (resultData != null) {
              if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
                  toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
              }
              toast(`${resultData.data.data.message}`, { type: 'error' });
            }
          }catch(e){
            toast(`${e}`, { type: 'error' });
          console.log(e)
        }
        }.bind(this));

      // this.props.PostCommunicationData({
      //   root: 'communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
      //   data: reqData
      // }, function (data) {
      //   toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
      // });
    }

    this.setState({ showAssignLeadPopUp: false });

    // setTimeout(function () {
    //   this.fetchCallBackData();
    // }.bind(this), 300);
  }
  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }
  render() {
    const columns = this.columnlist;
    const { items, PageTitle, EmiPaymentFailedLeads, showAssignLeadPopUp, showAlert, AlertMsg, AlertVarient, ReportTime, SelectedRows } = this.state;
    console.log(this.state.paymentStatuses);
    let selectedLeads = [];
    SelectedRows.forEach(element => {
      selectedLeads.push(element.LeadId);
    });

    return (
      <>
        <div className="content EmiPaymentFailedLeadsContainer">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={6}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <CardTitle tag="h5">
                        {ReportTime ? <Moment format="DD/MM/YYYY HH:mm:ss">{ReportTime}</Moment> : null}
                      </CardTitle>

                    </Col>
                    <Col md={2}>
                      {this.state.hideAssign ? null : <button className="btn btn-info btn-sm float-right" onClick={() => this.OpenAssignLeadPopUp()} >Add Lead</button>}
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={(EmiPaymentFailedLeads && EmiPaymentFailedLeads.length > 0) ? EmiPaymentFailedLeads : []}
                    defaultSortField="GracePeriodDate"
                    defaultSortAsc={false}
                    selectableRows={true}
                    export={false}
                    ref={this.dtRef}
                    onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showAssignLeadPopUp} onHide={this.handleClose.bind(this)} >
            <Modal.Header closeButton>
              <Modal.Title>Add Leads</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Row>
                <Col>
                  LeadId : {selectedLeads.join()}
                </Col>
              </Row>
              <Row>
                <Col>
                  {/* <Form.Control as="select" name="products" onChange={this.onSelectedAgent.bind(this)} >
                    <option key={0} value={0}>Select</option>
                    {
                      this.bindAgentDropdown()
                    }
                  </Form.Control> */}
                </Col>
              </Row>

            </Modal.Body>
            <Modal.Footer>

              <If condition={this.state.SelectedRows.length > 0}>
                <Then>
                  <Button variant="primary" onClick={this.AssignLead.bind(this)}>Add Lead</Button>
                </Then>
              </If>
              <Button variant="secondary" onClick={this.handleClose.bind(this)}>
                Close
                </Button>
            </Modal.Footer>
          </Modal>


        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    PostCommunicationData
  }
)(EmiPaymentFailed);