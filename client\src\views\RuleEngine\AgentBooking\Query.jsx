import axios from 'axios';
import React, { Component } from 'react';
import { Form , Row , Col , Button } from 'react-bootstrap';
import <PERSON><PERSON>oader from "react-spinners/BeatLoader";
import config from './../../../config';

class Query extends Component{
    constructor(props){
        super(props);
        this.state = {
            query : "",
            loading : false,
            response : false,
            columns : [],
            data:[],
        }
        this.handleQueryChange = this.handleQueryChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
    }
    
    handleQueryChange(event){
        event.preventDefault();
        this.setState({
            [event.target.name] : event.target.value,
        });
    }
    
    handleSubmit(event){
        event.preventDefault();

        this.setState({
            loading : true,
        })
        console.log(this.state.query);
        console.log(typeof this.state.query);
        const obj = JSON.parse(this.state.query);
        console.log(obj , typeof obj);
        
        const URL = `${config.api.base_url}/ruleengine/resolveRules`;
        axios.post(URL , obj)
        .then((res)=>{
            let { events, result } = res.data.data;
            this.setState({
                loading : false,
                response : true,
                data : {
                    events,      
                    result 
                },
            });
        });
    }


    render(){
        return (
            <React.Fragment>
                <h4>Query</h4>
                <div>
                    <form onSubmit={this.handleSubmit}>
                        <textarea 
                            style={{
                                width: "95%",
                                padding: "10px",
                                margin: "10px"
                            }}
                            name="query" 
                            value={this.state.query} 
                            onChange = {this.handleQueryChange} 
                            rows="15"
                            required
                        />
                        <Button type="submit">
                            Search
                        </Button>
                    </form>
                </div>
                {this.state.loading &&
                    <div style={{textAlign:"center"}}>
                        <BeatLoader size={20}
                            color={"#3498db"}
                            loading={true}
                        />
                    </div>
                }
                {this.state.response && !this.state.loading &&
                    <textarea 
                        style={{
                            width: "95%",
                            padding: "10px",
                            margin: "10px"
                        }}
                        rows="20"
                    >
                        {JSON.stringify(this.state.data,null,4)}
                    </textarea>
                }
            </React.Fragment>
        )
    }

}

export default Query;