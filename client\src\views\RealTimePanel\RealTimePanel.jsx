import React from "react";
import {
  GetCommonData, GetCommonspData, GetRealTimeAgentData, GetRealTimeTotalData
} from "../../store/actions/CommonAction";
import { hhmmss, getuser } from '../../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from '../Common/ManagerHierarchy';
import Moment from 'react-moment';
import { Web } from "sip.js";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  addRecord, UpdateData, gaEventTracker
} from "../../store/actions/CommonMongoAction";
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { Form } from 'react-bootstrap';
import moment from "moment";

class RealTimePanel extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "RealTime Panel",
      AgentData: [],
      TotalData: [],
      items: [],
      key: "ALL",
      onBarge: false,
      SelectedSupervisors: [],
      winactive: 0,
      BargeWith: "",
      Leads : null,
      body : [],
      AgentCode : "",
      AgentName : "",
      currLeadId: ""
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    this._handleKeyDown = this._handleKeyDown.bind(this);
    this.saveBargingLogs = this.saveBargingLogs.bind(this);
    //this.bargecall = this.bargecall.bind(this);
    this.userAgent = null;
    this.winactive = 0;
    this.schdular = null;
    this.columnlist = [
      {
        name: "Barging",
        selector: "Barging",
        sortable: true,
        width: "80px",
        cell: row =>
          <div className={row.Status == "BUSY" ? "" : "hide"} >

            <button onClick={(e) => this.bargecall(e, row)} className={row.Barge ? "hide" : "show"}><i className="fa fa-volume-up" aria-hidden="true"></i></button>


          </div>
      },
      {
        name: "Agent Code",
        selector: "AgentCode",

        sortable: true,
      },
      {
        name: "Status",
        selector: "Status",
        sortable: true,

        cell: row => <div className={this.displayStatus(row) + " RealtimeStatus"}>{this.displayStatus(row)}</div>
      },
      {
        name: "Is Mobile",
        selector: "IsWFH",
        cell: row => <div>{row.IsWFH ? "true" : "false"}</div>
      },
      {
        name: "Agent Name",
        selector: "AgentName",
        sortable: true,
      },
      {
        name: "Call Type",
        selector: "CallType",
      },
      {
        name: "Lead Id",
        selector: "LeadId",
        sortable: true,
      },
      {
        name: "D.C.",
        selector: "CallingCompany",

        sortable: true,
      },
      {
        name: "DIDNo",
        selector: "DIDNo",

        sortable: true,
      },
      // {
      //   name: "Asterisk_Url",
      //   selector: "Asterisk_Url",
      //   width: "120px",
      //   sortable: true,
      // },

      // {
      //   name: "Last Updated On",
      //   selector: "LastUpdatedOn",
      //   width: "130px",
      //   sortable: true,
      //   cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment format="HH:mm:ss A">{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      // },
      {
        name: "Since",
        selector: "LastUpdatedOn",
        sortable: true,
        width: "130px",
        cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment utc={true} from={row.ServerTime}>{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      },
      {
        name: "T Calls",
        selector: "TotalCalls",
        width: "70px",
        sortable: true,
      },
      {
        name: "U Dials",
        selector: "UniqueDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "C Dials",
        selector: "ConnectedDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "T TalkTime",
        selector: "TotalTalkTime",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.TotalTalkTime),
      },

    ];


  }

  componentDidMount() {

    const user = getuser();
    this.setState({ SelectedSupervisors: [this.props.m] }, function () {
      this.UserList();
    }.bind(this));

    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //if (!this.state.onBarge) {
        //if ((new Date()).getHours() >= 21 || (new Date()).getHours() < 9) {
        if (this.state.winactive == 1 || document.hasFocus()) {
          this.UserList();
          //this.totalList();
        }
        // }
        // else {
        //   this.UserList();
        //   this.totalList();
        // }
        //}
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }
  }
  saveBargingLogs(body) {
    this.props.addRecord({
      root: "BargeLogs",
      body: body
    });
  };

  totalList() {
    let context = this.props.c;
    if (context != "") {
      GetRealTimeTotalData(context, function (results) {
        this.setState({ TotalData: results.data });
      }.bind(this));
    }
  }

  UserList() {
    let managerid = this.props.m;
    let context = this.props.c
    const user = getuser();
    if (this.state.SelectedSupervisors.length > 0) {
      managerid = this.state.SelectedSupervisors.join()
    }

    if (managerid == "" && context == "") {
      managerid = user.EmployeeId;
      this.setState({ SelectedSupervisors: [managerid] });
    }

    GetRealTimeAgentData(managerid, context, function (results) {
      this.setState({ AgentData: results.data });
    }.bind(this));

  }

  handleShow(e) {
    this.setState({ SelectedSupervisors: e.SelectedSupervisors });
  }

  componentWillUnmount() {
    clearInterval(this.schdular);
    if (this.userAgent != null)
      this.userAgent.hangup();
  }

  displayStatus(row) {
    // var diff = (new Date() - new Date(row.LastUpdatedOn)) / 1000;
    // if (diff > 60 && row.Status == "IDLE") {
    //   return "Away";
    // } else return row.Status;
    let BargeWithAgent = this.state.BargeWithAgent;
    if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status != "BUSY") {

      // if (this.userAgent) {
      //   this.userAgent.hangup();
      //   this.userAgent = null;        
      // }
      // this.setState({ onBarge: false, BargeWith: "" });
      this.unbargecall();
    }
    else if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status == "BUSY") {
      // if (this.userAgent == null) {
      //   this.bargecall(null, BargeWithAgent);
      // }
    }
    return row.Status.toUpperCase()
  }

  unbargecall(e) {
    if (this.userAgent) {
      try {
        if(this.state.body != []){
        let UnbargeTimestamp = new Date();
        let unbargeLog = {
          Agent: this.state.body.Agent,
          BargedBy: getuser().EmployeeId,
          AgentName: this.state.body.AgentName,
          LeadID: sessionStorage.getItem("BargeWithLeadId"),
          Type: "Unbarge",
          Timestamp: UnbargeTimestamp,
        }
        gaEventTracker("BargeCall", JSON.stringify(unbargeLog), getuser().EmployeeId);
      }
      
      }
      catch (e) {

      }
      this.userAgent.hangup();
      this.userAgent = null;


    }
    sessionStorage.setItem("BargeWith", "");
  }


  bargecall(e, row) {
    try {
      let bargeTimestamp = new Date();
      if (this.userAgent) {
        this.userAgent.hangup();
        this.userAgent = null
      }
      
      let user = {
        Display: getuser().EmployeeId,
        User: getuser().EmployeeId,
        Pass: getuser().EmployeeId,
        Realm: row.Asterisk_Url,
        WSServer: "wss://" + row.Asterisk_Url + ":8089/ws"
      }
      this.state.AgentName = row.AgentName
      this.state.AgentCode = row.AgentCode
      this.LoginAsteriskServer(user, function () {
        setTimeout(function () {

          if (this.userAgent) {
            let target = "*222" + row.AgentCode;

            if (row.IsWFH || (row.CallingCompany == "WFH" || row.CallingCompany == "WFH_NEW")) {
              target = "*222" + row.DIDNo;
            }

            // if (row.CallingCompany == "WFH" || row.CallingCompany == "KNOWLARITY") {
            //   target = "*222" + row.DIDNo;
            // }

            this.userAgent.call(target);
          }
          this.bodyLeads = setInterval(function () {
            this.insertBargeLogs(row)
          }.bind(this), 5000)

          // setTimeout(function () {
          //   //this.forceUpdate();

          // }.bind(this), 500);

        }.bind(this), 1000);
      }.bind(this), function () {
        document.getElementById(row.AgentCode).checked = false;
      }.bind(this));

      // }
      // else {
      //   toast("Close previous call barging", { type: 'error' });
      //   e.target.checked = false;
      //   return false;
      // }

      
        let bargeLog = {
          Agent: row.AgentCode,
          BargedBy: getuser().EmployeeId,
          AgentName: row.AgentName,
          LeadID: row.LeadId,
          Type: "Barge",
          Timestamp: bargeTimestamp,
        }
        // setting leadid
        sessionStorage.setItem("BargeWithLeadId", row.LeadId);
        this.setState({body: bargeLog})
        gaEventTracker("BargeCall", JSON.stringify(bargeLog), getuser().EmployeeId);
      

      

    } catch (e) {

    }

  }
 
  LoginAsteriskServer(user, onsuccess, onerror) {

    if (user) {
      var config = {
        media: {
          remote: {
            //video: document.getElementById('remoteVideo'),
            // This is necessary to do an audio/video call as opposed to just a video call
            audio: document.getElementById('audioRemote')
          }
        },
        ua: {
          uri: user.User + '@' + user.Realm,
          wsServers: [user.WSServer],
          authorizationUser: user.Display,
          password: user.Pass
        }
      }

      if (Web) {
        this.userAgent = new Web.Simple(config);
        // this.userAgent = new Web.SimpleUser(config);

        //let remoteElem = document.getElementById('audioRemote');
        //let localElem = document.getElementById('audioLocal');
        this.userAgent.on('connected', function (e) {
          toast("Barging Connected!", { type: 'success' });
        });
        this.userAgent.on('disconnected', function (e) {

        });
        this.userAgent.on('registered', function (e) {
          if (onsuccess) {
            onsuccess();
          }

        });
        this.userAgent.on('registrationFailed', function (e) {
          toast("Make sure your VPN is connected!", { type: 'error' });
        });
        this.userAgent.on('unregistered', function (e) {
          toast("Dialer issue please contact administrator!", { type: 'error' });
          if (onerror) {
            onerror();
          }

        });
        this.userAgent.on('userMediaFailed', function (e) {

        });
        this.userAgent.on('userMediaRequest', function (e) {

        });
        this.userAgent.on('userMedia', function (e) {

        });
        this.userAgent.on('invite', function (e) {

        });
        this.userAgent.on('addStream', function (stream) {

        });
        this.userAgent.on('ended', function (stream) {

        });
      }
    }

    setTimeout(function () {
      if (this.userAgent && this.userAgent.ua && this.userAgent.ua.isRegistered() == false) {
        toast("Make sure your VPN is connected!", { type: 'error' });
      }
    }.bind(this), 10000);
    return this.userAgent;
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {

    }
  }

  statuschange(e) {
    this.setState({ key: e.target.value });

  }

  filterdata(e) {

    let alldata = this.state.AgentData
    let that = this;
    if (this.state.key === "ALL") {
      return alldata;
    }
    if (this.state.key === "Away") {
      let AgentData = [];
      alldata.forEach(element => {
        var diff = (new Date() - new Date(element.LastUpdatedOn)) / 1000;
        if (diff > 60 && element.Status === "IDLE") {
          AgentData.push(element);
        }
      });
      return AgentData;
    }

    let AgentData = [];
    alldata.forEach(element => {
      if (this.state.key.indexOf(element.Status) > -1) {
        AgentData.push(element);
      }
    });
    return AgentData;
  }
  _handleKeyDown(e) {

    if (e.key === 'Enter') {
      this.setState({ SelectedSupervisors: [e.target.value] });
    }
  }

  _handleOnClick(e) {


    this.setState({ SelectedSupervisors: [document.getElementById("EmpId").value] });

  }

  renderTotalData() {
    const context = this.props.c;
    let TotalData = this.state.TotalData;
    if (context != "") {
      return <div>

        <div className="totaldata">
          <Row></Row>
          <Row>
            <Col md={2}><span className="totaltext">Context : {TotalData.context}</span></Col>
            <Col md={2}><span className="totaltext">Answered : {TotalData.answered}</span></Col>
            <Col md={2}><span className="totaltext">Unanswered : {TotalData.unanswered}</span></Col>
            <Col md={2}><span className="totaltext"># Agents : {TotalData.totalAgents}</span></Col>
            <Col md={2}><span className="totaltext">Waiting Calls : {TotalData.waitingCalls}</span></Col>
            <Col md={2}><span className="totaltext"># Avail. Agents : {TotalData.totalAvailableAgents}</span></Col>
          </Row>
        </div>
        <br />
      </div>
    }
    else {
      return null;
    }
  }


  render() {
    const columns = this.columnlist;
    const totalcolumns = this.totalcolumnlist;
    const data = this.filterdata();
    const managerid = this.props.m;
    const context = this.props.c;

    const { items, PageTitle } = this.state;


    return (
      <>
        <ToastContainer />
        <Row>
          <Col md="12">
            <Card>
              <CardHeader>
                <Row>
                  <Col md={3}>
                    <CardTitle tag="h4">{PageTitle}</CardTitle>
                  </Col>
                  <Col md={4}>
                    <CardTitle tag="h6">{this.state.SelectedSupervisors.join()}</CardTitle>
                  </Col>
                  <Col md={3}>
                    <div className="input-group">

                      <Form.Control required type="text" name="EmpId" id="EmpId" onKeyDown={this._handleKeyDown} onChange={(e) => this.setState({ username: e.target.value })} value={this.state.username} placeholder={"Enter Supervisor Id"} />
                      <div className="input-group-append">
                        <button onClick={(e) => this._handleOnClick(e)} className="btn btn-primary input-group-button"><i className="fa fa-search" aria-hidden="true"></i></button>

                      </div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="form-group">
                      <select className="form-control" onChange={this.statuschange}>
                        <option value="ALL">ALL</option>
                        <option value="IDLE">IDLE</option>
                        <option value="Away">Away</option>
                        <option value="BUSY">BUSY</option>
                        <option value="Lunch,Tea,Training,Meeting,Day End">Break</option>
                        <option value="PAUSE">PAUSE</option>
                        <option value="Auto Logout,LOGOUT">LOGOUT</option>
                      </select>
                    </div>
                    {
                      (managerid == '' && context == '') ? <ManagerHierarchy handleShow={this.handleShow} value={/EmployeeId/g} ></ManagerHierarchy> : null
                    }
                    <button id="BargeWith" onClick={(e) => this.unbargecall(e)} className={this.state.BargeWith == "" ? "hide" : "btn btn-primary hangupwith show"} ><i className="fa fa-volume-off" aria-hidden="true"></i> Hang Up With: {this.state.BargeWith}</button>
                  </Col>

                </Row>

              </CardHeader>

              <CardBody>
                {/* {this.renderTotalData()} */}
                <div className="statusdata">
                  <DataTable
                    columns={columns}
                    data={data}
                    pagination={false}
                    striped={true}
                    noHeader={true}
                    highlightOnHover
                    dense

                  />

                </div>
              </CardBody>

            </Card>
          </Col>
          <audio id="audioRemote"></audio>
          <audio id="audioLocal"></audio>
        </Row>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    addRecord,
    UpdateData,
    gaEventTracker
  }
)(RealTimePanel);




