import React from 'react';

import {
    GetDataDirect
} from "../../store/actions/CommonAction";
import { getuser, getUserDetails } from '../../utility/utility.jsx';
import Offcanvas from 'react-bootstrap/Offcanvas';
import { Row, Col, Button } from 'react-bootstrap';
import {
    CardTitle,
} from "reactstrap";
import 'react-checkbox-tree/lib/react-checkbox-tree.css';
import CheckboxTree from 'react-checkbox-tree';
import { useEffect } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import { useState } from 'react';
import axios from 'axios';
import config from "../../config.jsx";
import { MY_BOOKINGS } from '../../variables/constants';

const GetCheckedValues = ({checkedOptions, module, state}) => {
debugger
  let options = checkedOptions;
  let errorStatus = 0;
  switch (module) {
    case MY_BOOKINGS:
        if(checkedOptions.length > 2) {
            errorStatus = 1;
            break;
        } 

      if(checkedOptions.length === 2){
        const opt = checkedOptions?.filter(opt => opt !== state[0]);
        options = opt;
        
      }
        break;
  
    default:
      break;
  }
  return { errorStatus, data: options }
}

const ManagerHierarchy_v2=(props)=> {

const {handleShow,selectAll,value,noCascade, module, message} = props
const [nodes,setNodes]=useState([]);
// const [selectedNodes, setSelectedNodes]= useState([]);
const [checked, setChecked]= useState([]);
const [EmployeeIds,setEmployeeIds]= useState([]);
const [showManagerHierarchy, setShowManagerHierarchy]= useState(false);
const [IsLoading, setIsLoading]= useState(false);
const [expanded,setExpanded]= useState([]);


let selectedNodes=[];
let TLnodes=[];

useEffect(()=>{

   let UserId =  parseInt(getUserDetails('UserId'));
    if(!UserId)
    {
    UserId = getuser().UserID;
    }

    if(UserId)
    {
    let Value= value;
    GetDataDirect({
        root: "Hierarchy2",
        ManagerId: UserId,
        statename: "Hierarchy-" + UserId,
        value: Value,
        state: true
    }, function (result) {
      debugger
       let expired=false;
        if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
        {
        expired=true;
        }
        // result.pop();
        let str = JSON.stringify(result);
        var res = str.replace(/UserName/g, "label");
        res = res.replace(Value, "value");
        let nodes= JSON.parse(res);
        
        for(let i=0;i<nodes.length;i++)
        {
          TLnodes.push(nodes[i].value);
          if(nodes[i].hasOwnProperty("children"))
          {
               searchChildNodes(nodes[i].children)
          }
        }
        
       
        setNodes(nodes);
        handleShow({
          SelectedSupervisors: TLnodes,
          nodesData: selectedNodes,
        });
        
        if(selectAll===true){
         
          setChecked(TLnodes);
        }
        else{
          setChecked([getuser().UserID]);
        }
     
        if(expired)
        {
          axios
          .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
          .then(function(response){
            if(response.data.status===200)
            {
               return;
            }
          })
        }
    });
  }

},[])


const searchChildNodes=(node)=>{
  for(let i=0;i<node.length;i++)
  {
    TLnodes.push(node[i].value);
    if(node[i].hasOwnProperty('children'))
    {
      searchChildNodes(node[i].children);
    }
  }
  return;
}

const getNodesData=(nodesData, SelectedSupervisors)=> {
    
    for (let i = 0; i < nodesData.length; i++) {
        let element = nodesData[i];
        if (SelectedSupervisors.indexOf(element.value) > -1) {
            selectedNodes.push(element);
        }
        if (element.children) {
            getNodesData(element.children, SelectedSupervisors)
        }
    }
}

const OnRefreshHierarchy=()=>{
    
   let UserId = parseInt(getUserDetails('UserId'));
   if(!UserId)
   {
   UserId = getuser().UserID;
   }

   if(UserId)
   {
    let Value= value;
    setIsLoading(true);
    debugger
    GetDataDirect({
        root: "Hierarchy2",
        ManagerId: UserId,
        statename: "Hierarchy-" + UserId,
        value: Value,
        state: true,
        ClearLocalStorage: true
    }, function (result) {
        let expired=false;
        if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
        {
        expired=true;
        }
        // result.pop();
        let str = JSON.stringify(result);
        var res = str.replace(/UserName/g, "label");
        res = res.replace(Value, "value");
        let nodes= JSON.parse(res);
        setNodes(nodes);
        setIsLoading(false);
        if(expired)
        {
          axios
          .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
          .then(function(response){
            if(response.data.status===200)
            {
              return;
            }
          })
        }
    });
  }
}
const handleShow1=()=>{
    // this.selectedNodes = [];
    getNodesData(nodes, checked);
    let arr = [];
    for (let i = 0; i < selectedNodes.length; i++) {
      arr.push(selectedNodes[i].EmployeeId)
    }
    setEmployeeIds(arr);
    setShowManagerHierarchy(false);
    
    
    handleShow({
      SelectedSupervisors: checked,
      nodesData: selectedNodes,
      FetchedViaClick: true
    });
    // this.forceUpdate();
}



const HandleOpenHierarchy=()=>{
    setShowManagerHierarchy(true);
}
const HandleCloseHierarchy=()=>{
    setShowManagerHierarchy(false);
}

const checkFunction = (values) => {
  if(selectAll === false){
    let difference = values.filter(x => !checked.includes(x));
    setChecked(difference)
  }
  else{
    debugger
    const { data, errorStatus }= GetCheckedValues({checkedOptions:values, module, state: checked})
    if(errorStatus) {
      // toast(HIERARCHY_MESSAGE_NOTE, { type: 'error' });
      return;
    }
    setChecked(data)
  }
 
}

return(
    <>
    <ToastContainer/>
    {
        nodes.length!==0 &&
        <div className="ShowHistoryBTn">
        <Button variant="primary" className="HierarchyBtn" onClick={HandleOpenHierarchy}>
          {/* {Array.isArray(EmployeeIds) && EmployeeIds.length > 0 ? EmployeeIds.slice(0, 2).join() + '...' : 'Filter'} */}
         Filter
        </Button>
        <Offcanvas show={showManagerHierarchy} onHide={HandleCloseHierarchy} backdrop="static" placement="end">

          <Offcanvas.Header closeButton>
            <Offcanvas.Title>
              Select Supervisors
            </Offcanvas.Title>
            <input type="button" className="btn btn-primary " onClick={handleShow1} value="Show" /> <i title="Refresh" className="fa fa-refresh refreshIcon" onClick={OnRefreshHierarchy}></i>
          </Offcanvas.Header>
          {IsLoading ?
            <h5 className='hierarchyLoading'>Loading...</h5> :
            <Offcanvas.Body className="hierarchyScrolling">
              
              {message && 
                <Row>
                <h4 style={{ fontSize: '15px', color: 'red'}}>{ message}</h4>
                </Row>
              }
              <Row>
                <Col>
                  <div className="managers">
                    <CheckboxTree
                      nodes={nodes}
                      checked={checked}
                      expanded={expanded}
                      checkModel="all"
                      name="UserName"
                      showNodeIcon={false}
                      onCheck={(checked)=>checkFunction(checked)}
                      noCascade= {noCascade}
                      // onCheck={(checked)=>setChecked(checked)}
                     onExpand={(expanded)=>setExpanded(expanded)}
                      showExpandAll={true}
                    />
                  </div>
                </Col>
              </Row>
              <br></br>
              <Col md={8}>
                <CardTitle  className= "managerEmployees" tag="h6">{EmployeeIds && Array.isArray(EmployeeIds) && EmployeeIds.join()}</CardTitle>
              </Col>
              </Offcanvas.Body>
          }
        </Offcanvas>
        </div>
    }
    {
        nodes.length===0 && null
    }
    </>
)

}

export default ManagerHierarchy_v2;
