const Joi = require("joi");

const LotterySchema = {
    GET_UserLotteyTicket_Schema: Joi.object().keys({
        UserID: Joi.number().required()
    }),
    GET_RewardsList_Schema: Joi.object().keys({
        BUId: Joi.number().required()
    }),
    GET_TicketCount_Schema: Joi.object().keys({
        BUId: Joi.number().required(),
        RewardId: Joi.number().required()
    }),
    POST_TicketWinnerDetail_Schema: Joi.object().keys({
        BUId: Joi.number().required(),
        RewardId: Joi.number().required(),
        TicketNumber: Joi.string().required()
    }),
    POST_TicketWinnerDetailJag_Schema: Joi.object().keys({
        BUId: Joi.number().required(),
        RewardId: Joi.number().required(),
        TicketNumber: Joi.string().required(),
        Version: Joi.string().required()
    })
};

module.exports = LotterySchema;