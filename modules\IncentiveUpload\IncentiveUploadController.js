

const { MY_BOOKINGS_BMS_API } = require("../common/ActionTypes");
const CommonController = require("../common/CommonController");
const IncentiveUploadMethods = require("./IncentiveUploadMethods");
const XLSX = require('xlsx');  // Import SheetJS
const path = require('path');
const { uploadAWSfiles, uploadAWSfilesCommon } = require("../../Libs/Utility");
const moment = require("moment");
const config = require('../../env_config');

const gethtmlFromExcel = async (req, res) => {
    try{
    //console.log('///////////',__dirname);
    if (!req.files || !req.files.file) {
        return res.status(400).send({ status: 400, message: "File not found in request" });
    }
    const myFile = req.files.file;
    let myFileName = myFile.name.split('.').join('-' + Date.now() + '.');//myFile.name;
    myFileName = myFileName.replace(/\s+/g, "");
    const today = moment(Date.now()).format('YYYY-MM-01');

    let filename =  `${config.IncentiveCriteriaUrl}${today}/${myFileName}`;
    let awsUploadResponse = uploadAWSfilesCommon(`${myFile.tempFilePath}`, filename, 'matrixdocuments');
    // console.log(awsUploadResponse);
    awsUploadResponse.then(function (resultaws) {
        if (!resultaws || !resultaws.Location) {
            throw new Error("AWS upload failed");
        }
        var awsFilePath = resultaws.Location;
        
          let response = IncentiveUploadMethods.UploadIncentiveData(req, awsFilePath);
          response.then(function (result) {
            // console.log("recordset",result.output) // "Some User token"
            if (!result || !result.output) {
                throw new Error("Failed to process incentive data");
            }
            res.send({
                status: 200,
                data: result.output
            });
          })
        

    })

    }
    catch(ex){
        console.log(ex);
        return res.status(500).send({ status: 500, message: ex.message });
    }
    
}

module.exports = {
    gethtmlFromExcel: gethtmlFromExcel
};