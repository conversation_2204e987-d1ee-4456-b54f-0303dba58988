const tblList = require("../constants");
let fs = require('fs');
const path = require("path");
const mySqlHelper = require("../../Libs/mysqlHelper");
const conf = require("../../env_config");

const methods = require("./Methods");
const { isInputSafe } = require("../common/CommonMethods");

async function getdata(req, res) {

  try {
    const AWSSecretConfig = global.SecretConfig;
    let connection = AWSSecretConfig.MYSQL_AWS_URL;
    var endpoint = req.query.root;
    var c = req.query.c ? req.query.c : "R";

    if (methods.FindRoot(req, res)) {
      return;
    }

    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }

    let tablename = tblList[endpoint];

    var query = 'SELECT  * FROM ' + tablename;

    if (req.query.cols && req.query.cols.length > 0) {
      for(let i=0;i<req.query.cols.length;i++)
        {
            if(isInputSafe(req.query.cols[i]))
            {
                continue;
            }
            else{
                return res.send({
                    status: 500,
                    error: "Please enter correct inputs."
                });
            }
        }
      query = 'SELECT  ' + req.query.cols.join() + ' FROM ' + tablename;
    }

    var whereCondition = [];
    if (req.query.con) {
      query = query + ` WHERE `;
      var querydata = req.query.con || {}
      for (var key in querydata) {
        whereCondition.push(`${key} = '${querydata[key]}'`);
      }
      query += whereCondition.join(' AND ');

      /*for (var key in req.query.con) {
          let json = JSON.parse(req.query.con[key]);
          for (var obj in json) {
              var objparam = obj;
              if (obj.indexOf('.') !== -1) {
                  var objparam = obj.replace('.', '')
              }
              //console.log(objparam);
              if (obj.toLowerCase().indexOf('date') > -1 || obj.toLowerCase().indexOf('time') > -1) {
                  whereCondition.push(`CAST(${obj} AS DATE) = ${objparam}`);
              }
              else {
                  whereCondition.push(`${obj} = ${objparam}`);
              }

          }
      }
      query = query + whereCondition.join(' and ');*/
    }

    if (req.query.order) {
      if (!req.query.direction)
        query = query + " ORDER BY " + req.query.order + " DESC";
      else
        query = query + " ORDER BY " + req.query.order + " " + req.query.direction;
    } else
      query = query + " ORDER BY 1 DESC";

    let response = await mySqlHelper.mysqlquery(connection, query, function (error, result) {
      if (!error) {
        res.send({
          status: 200,
          data: [result.rows],
          message: "Success"
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });

  } catch (err) {
    //console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {
    //await sql.close();
  }
}

async function insert(req, res) {
  try {
    const AWSSecretConfig = global.SecretConfig;
    let connection = AWSSecretConfig.MYSQL_AWS_URL;
    let endpoint = req.body.data.root;
    let inputdata = req.body.data.body;
    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }

    var query = 'INSERT INTO ' + tblList[endpoint] + '(';
    var columns = [];
    var VALUES = [];
    for (var key in inputdata) {
      columns.push(key);
      VALUES.push((typeof inputdata[key] === "boolean") ? Number(inputdata[key]) : inputdata[key]);
    }

    query += columns.join() + ") VALUES ( '";
    query += VALUES.join("','") + "')";

    //console.log("=============$$$$$$$$$$$===========");
    //console.log(inputdata);
    //console.log(query);
    //return false;
    let response = await mySqlHelper.mysqlquery(connection, query, function (error, result) {
      //console.log('Error: ' + error);
      if (!error) {
        //console.log('Result: ' + result);

        res.send({
          status: 200,
          data: [result.rows]
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });

  } catch (err) {
    //console.log('Excpetion: ' + err);
    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    //await sql.close();
  }
}

async function update(req, res) {
  //console.log('poiuytrgfdbvf', req.body);
  try {
    const AWSSecretConfig = global.SecretConfig;
    let connection = AWSSecretConfig.MYSQL_AWS_URL;
    let endpoint = req.body.data.root;
    let inputdata = req.body.data.body;
    let querydata = req.body.data.querydata;
    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }

    var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
    var updatedata = [];
    var updatequery = [];

    for (var key in inputdata) {
      if (key == 'createdOn') {
        continue;
      }
      updatedata.push(`${key} = '${(typeof inputdata[key] === "boolean") ? Number(inputdata[key]) : inputdata[key]}'`);
    }
    query += updatedata.join();
    query += ` WHERE `;

    for (var key in querydata) {
      updatequery.push(`${key} = '${querydata[key]}'`);
    }
    query += updatequery.join(' AND ');
    //console.log(query);

    let response = await mySqlHelper.mysqlquery(connection, query, function (error, result) {
      //console.log('Error: ' + error);
      if (!error) {
        //console.log('Result: ' + result);

        res.send({
          status: 200,
          data: [result.rows]
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });


  } catch (err) {
    //console.log(err);
    res.send({
      status: 500,
      error: err
    });
  }
  finally {
    await sql.close();
  }
}

const WinFrmSettings = async function (req, res) {
  try {

    // let settings = await matrixdb.collection("WinFrmSettings").findOne({});
    // res.send(settings);

    fs.readFile(path.join(appRoot, "public/json", "winfrm.json")

      , null, function (error, data) {
        if (error) {
          res.writeHead(404);
          res.write('Whoops! File not found!');
        } else {
          try {
            res.write(data);

          }
          catch (e) {
            res.write(e.toString());
          }
        }
        res.end();
      });

  } catch (e) {
    //console.log(e);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

module.exports = {
  insert: insert,
  update: update,
  WinFrmSettings: WinFrmSettings,
  getdata: getdata,
};
