const Collection = require("mongodb/lib/collection");
const {InsertDataMongo, DeleteMongoData, GetMongoData, GetSpecificMongoData, FetchMultipleDocuments, PipelineQuery, GetMongoDataMultipleQueryParams, FetchMatrixDashboardConfigAndCache} = require("../common/CommonMethods");


const StoreSocketId=async (UserId, SocketId)=>{
    let data ={UserID: parseInt(UserId), SocketId: SocketId};
    await InsertDataMongo({Collection:"FosSocketMaintenance",Database:"MATRIX_DASHBOARD_DB",Data:data})
    return
}

const RemoveSocketId= async (UserId, SocketId)=>{
    if(SocketId)
    {
        await DeleteMongoData("MATRIX_DASHBOARD_DB","FosSocketMaintenance","SocketId", SocketId)
        return
    }
    await DeleteMongoData("MATRIX_DASHBOARD_DB","FosSocketMaintenance","UserID",parseInt(UserId))
    return
}

const GetActiveSocketList= async()=>{

}

const AddSupervisorsList= async(UserId,TlList)=>{
    let Data={UserID: parseInt(UserId), TlList: TlList};
    await InsertDataMongo({Collection:"SupervisorsRequestedList",Database:"MATRIX_DASHBOARD_DB",Data: Data});
}


const  ReturnSocketList= async(TlId)=>{
     let result = await GetSpecificMongoData("MATRIX_DASHBOARD_DB","SupervisorsRequestedList","TlList",TlId,'UserID');
    
     if(result.length>0)
     {
        // console.log("RESULT IS ",result);
        let UserList=[];
        result.map((User)=>{
            UserList.push(User.UserID);
        }) 

        let projectionList=[];
        projectionList.push('SocketId');
        
        let SocketList = await FetchMultipleDocuments("MATRIX_DASHBOARD_DB","FosSocketMaintenance","UserID",UserList,projectionList);
        // console.log("The socket list is ", SocketList);
        let SocketIds=[];
        SocketList.map((socket)=>{
               SocketIds.push(socket['SocketId']);
        })
        return SocketIds;
      
     }
     return [];
}

const ReturnMongoData=async(UserId,TlList)=>{

     let projectionList=['CustomerName','ParentId','AppointmentId','AppointmentDateTime','RealTimeDistance','IdleTime',
      'ManagerId','UserName','RealTimeStatus','OTPVerified','LastAttempt','TTTalkTime','TTAttempts','EmployeeId','UserId',
      'City','RealTimeStatusId','distanceFromCustomer','overallDistance','StatusChangedOn','UpdatedOn','AgentBaseLocation','OS',
      'CustomerId','ProductId','WaitingAtCustomer','OTSValue','isPresent','LeaveType', 'MethodIdentifier','LogoutType'];
    //  console.log("TLlist is ", TlList);
     let pipeline = [
        { $match: { ManagerId: { $in: TlList } } },
        { $sort: { ManagerId: 1, UpdatedOn: -1 } },
        {
          $group: {
            _id: "$UserId",
            latestEntry: { $first: "$$ROOT" }
          }
        },
        { $replaceRoot: { newRoot: "$latestEntry" } }
      ];
     let docs =await PipelineQuery("MATRIX_DASHBOARD_DB","RealTimeAppointmentData",pipeline, projectionList);

     if(Array.isArray(docs) && docs.length>0)
     {
        return docs;
     }
    //  let data= data2
     // await FetchMultipleDocuments("MATRIX_DASHBOARD_DB","RealTimeAppointmentData", "TlList",TlList)
     
     return  [];

}

const ReturnAgentLatLong= async(AgentList)=>{
    let ProjectionList=['AgentId','Lat','Long','insertedAt'];

  

    let pipeline = [
        { $match: { AgentId: { $in: JSON.parse(JSON.stringify(AgentList)) }, subStatusId: {$in: [2193, 2194, 2124, 2003, 0]}}  },  
  { $sort: { _id: -1 } },  
  {
    $group: {
      _id: "$AgentId",
      latestEntry: { $first: "$$ROOT" }  
    }
  },
  { $replaceRoot: { newRoot: "$latestEntry" } }
      ];

   
    // console.log("Query Pipeline is ", pipeline)
    let LatLongData = await PipelineQuery("FOS_DB","UserLatLong",pipeline,ProjectionList);

    // console.log("Lat Long Data", LatLongData)

    if(Array.isArray(LatLongData) && LatLongData.length>0)
    {
        return LatLongData;
    }
    return [];
}


const convertToLocalTime=(date)=>{
try{
let utcDate = new Date(date);
// console.log(new Date());

if(isNaN(date.getTime()))
{
    return null
}
// utcDate.setHours(utcDate.getHours()-5);
// utcDate.setMinutes(utcDate.getMinutes()-30);
return utcDate
// return moment.utc(utcDate).local().format('YYYY-MM-DD HH:mm:ss');



}
catch (e)
{
    return null
}
}

const convertToLocalTimeAdd530=()=>{
    try{
    let utcDate = new Date();
  
    utcDate.setHours(utcDate.getHours()+5);
    utcDate.setMinutes(utcDate.getMinutes()+30);
    return utcDate
    // return moment.utc(utcDate).local().format('YYYY-MM-DD HH:mm:ss');
    
    }
    catch (e)
    {
        return null
    }
    }


const ReturnAgentLatLongByTime = async (AgentId, StartTime, EndTime) => {
    let result = {};

    let ProjectionList = ['AgentId', 'Lat', 'Long', 'subStatusId', 'AppointmentId', 'LeadId', 'insertedAt', 'ThroughLatLongOrSubStatus'];

    try{

    if (AgentId && StartTime && EndTime) {



        let SearchQuery = {
            "AgentId": AgentId,
            "insertedAt": {
                $gte: new Date(StartTime),
                $lt: new Date(EndTime)
            }
        }


        let LatLongData = await GetMongoDataMultipleQueryParams("FOS_DB", "UserLatLong", SearchQuery, ProjectionList);

        if (Array.isArray(LatLongData) && LatLongData.length > 0) {

            let AppointmentId = new Set();
            for (let i = 0; i < LatLongData.length; i++) {
                if (LatLongData[i]['subStatusId'] && [2193, 2194, 2124, 2003].includes(LatLongData[i]['subStatusId']) && LatLongData[i]['AppointmentId']) {
                    AppointmentId.add(LatLongData[i]['AppointmentId']);
                }

            }
            
            let startTime= new Date(StartTime);
            startTime.setHours(startTime.getHours()-2);
            startTime= startTime.toISOString();
            
            let fetchActive = [
                {"$match": {AgentId: AgentId,subStatusId: { "$in": [2193, 2194, 2124, 2003, 2004, 2005]},insertedAt: {
                "$gte": new Date(startTime),
                "$lt": new Date(EndTime)
            }, ThroughLatLongOrSubStatus: 1 } },
                { "$sort": { _id: -1 } },
                {
                    "$group": {
                        _id: "$AppointmentId",
                        latestEntry: { "$first": "$$ROOT" }
                    }
                },
              
                {
                    $match: {"latestEntry.subStatusId":{"$in":[2193,2124]}}
                 },
                {
                    "$project": {
                        "_id": 0, 
                        "AppointmentId": "$_id"
                    }
                },
                
                {
                    "$group": {
                        "_id": null,
                        "AppointmentIds": { "$push": "$AppointmentId" }
                    }
                }
            ];
            let getAppointmentData = await PipelineQuery("FOS_DB","UserLatLong", fetchActive);
            // console.log("The appointment data is ", getAppointmentData);

            if(Array.isArray(getAppointmentData) && getAppointmentData.length>0 &&  getAppointmentData[0].hasOwnProperty('AppointmentIds'))
            {
                let appIds= getAppointmentData[0].AppointmentIds;
                // console.log("The appIds is ", appIds);
                if(Array.isArray(appIds))
                {
                    for(let i=0;i<appIds.length;i++)
                    {
                        AppointmentId.add(appIds[i]);
                    }
                }
            }

            // await Promise.all(Array.from(AppointmentId).map(async (appointmentId) => {
                for (let appointmentId of AppointmentId) {
                // console.log('apoointment is : ', appointmentId)
                let pipeline = [
                    { $match: { AgentId: AgentId, AppointmentId: appointmentId, subStatusId: { $in: [2193, 2194, 2124, 2003] }, ThroughLatLongOrSubStatus: 1 } },
                    { $sort: { _id: -1 } },
                    {
                        $group: {
                            _id: "$subStatusId",
                            latestEntry: { $first: "$$ROOT" }
                        }
                    },
                    { $replaceRoot: { newRoot: "$latestEntry" } }
                ];

                let FetchSubStatusDataByAppointment = await PipelineQuery("FOS_DB", "UserLatLong", pipeline, ProjectionList);
                // console.log("FetchSubStatusDataByAppointment:", appointmentId);

                if (Array.isArray(FetchSubStatusDataByAppointment) && FetchSubStatusDataByAppointment.length > 0) {

                    let insertedAt = new Map();
                    for (let i = 0; i < FetchSubStatusDataByAppointment.length; i++) {
                        insertedAt.set(parseInt(FetchSubStatusDataByAppointment[i]['subStatusId']), FetchSubStatusDataByAppointment[i]['insertedAt']);
                    }

                    let insert_2193 = convertToLocalTime(insertedAt.get(2193)) || null;

                    let insert_2194 = convertToLocalTime(insertedAt.get(2194)) || null;
                    let insert_2124 = convertToLocalTime(insertedAt.get(2124)) || null;
                    let insert_2003 = convertToLocalTime(insertedAt.get(2003)) || null;

                    if (insert_2193) {
                        // if( insert_2003 && new Date(insert_2003)< new Date(insert_2193) )
                        // {
                        //     insertedAt.delete(2003);                           
                        // }

                        // if(insert_2124 && new Date(insert_2124)< new Date(insert_2193))
                        // {
                        //                 insertedAt.delete(2124);
                        // }
                        // if(insert_2194 && new Date(insert_2194)< new Date( insert_2193) )
                        // {
                        //             insertedAt.delete(2194);

                        // }
                        if (insert_2003 && insert_2003 < insert_2193) {
                            insertedAt.delete(2003);
                        }

                        if (insert_2124 && insert_2124 < insert_2193) {
                            insertedAt.delete(2124);
                        }
                        if (insert_2194 && insert_2194 < insert_2193) {
                            insertedAt.delete(2194);

                        }

                    }
                    else {
                        insertedAt.clear();
                    }

                    let SecondQuery = {
                        "AgentId": AgentId,
                        "insertedAt": {
                            $gte: new Date(insert_2193),
                            $lt: insert_2003? new Date(insert_2003): new Date(convertToLocalTimeAdd530())
                        }
                    }

                    // console.log("Datatobefetched:", appointmentId, SecondQuery)

                    let DataToBeFetched = await GetMongoDataMultipleQueryParams("FOS_DB", "UserLatLong", SecondQuery, ProjectionList);
                    // console.log("The dataTobefetched is ", DataToBeFetched)
                    result['AppointmenmtId_' + appointmentId] = { '2193': [], '2194': [], '2124': [], '2003': [] };

                    let AppointmentKey = 'AppointmenmtId_' + appointmentId;
                    if (Array.isArray(DataToBeFetched) && DataToBeFetched.length > 0) {
                        let substatusId = 0;
                        for (let i = 0; i < DataToBeFetched.length; i++) {
                            if (DataToBeFetched[i]['subStatusId'] != 0 ) {
                                if(DataToBeFetched[i]['AppointmentId']==appointmentId)
                                {
                                
                                substatusId = DataToBeFetched[i]['subStatusId'];
                                result[AppointmentKey][substatusId.toString()] ? result[AppointmentKey][substatusId.toString()].push(DataToBeFetched[i]) : null;
                                }
                                else{
                                    continue;
                                }
                            }
                            else {

                                if (substatusId != 0) {
                                    if(substatusId==2003)
                                    {
                                        continue;
                                    }
                                    else{
                                    result[AppointmentKey][substatusId.toString()] ? result[AppointmentKey][substatusId.toString()].push(DataToBeFetched[i]) : null;
                                    }
                                }
                                else {
                                    continue;
                                }
                            }


                        }

                    }

                }
            }
            // }));
            result['LatLongData'] = LatLongData;
        }
    }
    
    return result;
}
catch(e)
{
    return {};
}

}


const ProcessLatLongData=(LatLongData)=>{
    let subStatus2193=null;
    let subStatus2194=null;
    let subStatus2124=null;
    let subStatus2003 = null;
    LatLongData.map((data, index)=>{
        if(data.subStatusId==2193)
        {
            subStatus2193= data.insertedAt;
        }
        else if(data.subStatusId== 2194)
        {
            subStatus2194= data.insertedAt;
        }
        else if(data.subStatusId== 2124)
        {
            subStatus2124= data.insertedAt;
        }
        else if(data.subStatusId==2003)
        {
            subStatus2003 = data.insertedAt;
        }
    });

    if(subStatus2193 && subStatus2194)
    {
        if(subStatus2194<subStatus2193)
        {
            LatLongData=  LatLongData.filter((data)=>{
                if(data.subStatusId==2193)
                {
                    return data;
                }
            })
            return LatLongData;
        }
    }

    if(subStatus2194 && subStatus2124)
    {
        if(subStatus2124<subStatus2194)
            {
                LatLongData=  LatLongData.filter((data)=>{
                    if(data.subStatusId==2194 || data.subStatusId==2193)
                    {
                        return data;
                    }
                })
                return LatLongData; 
            }
    }


    if(subStatus2124 && subStatus2003)
    {
        if(subStatus2003<subStatus2124)
            {
                LatLongData=  LatLongData.filter((data)=>{
                    if(data.subStatusId==2124 || data.subStatusId==2194 || data.subStatusId== 2193)
                    {
                        return data;
                    }
                })
                return LatLongData;
            }

    }
    return LatLongData;
}


const ReturnAgentLatLongByAppointment = async (AgentId, AppointmentId) => {
    let result = {};

    let ProjectionList = ['AgentId', 'Lat', 'Long', 'subStatusId', 'AppointmentId', 'LeadId', 'insertedAt', 'ThroughLatLongOrSubStatus'];

    try {

        if (AgentId && AppointmentId) {


            let fetchActiveAppointmentData = [
                { "$match": { AgentId: AgentId, subStatusId: { "$in": [2193, 2194, 2124, 2003, 2004, 2005] }, ThroughLatLongOrSubStatus: 1, AppointmentId: AppointmentId } },
                { "$sort": { _id: -1 } },
                {
                    "$group": {
                        _id: "$subStatusId",
                        latestEntry: { "$first": "$$ROOT" }
                    }
                },
                { $replaceRoot: { newRoot: "$latestEntry" } }

            ];

            let fetchCurrentLocation =[
                { "$match": { "AgentId": AgentId, "subStatusId":{$nin:[2002,2004,2005]} } },
                { "$sort": { "_id": -1 } },
                { "$limit": 1 }
            ];
            
            let LatLongData = await PipelineQuery("FOS_DB", "UserLatLong", fetchActiveAppointmentData, ProjectionList);
            let CurrentLocation = await PipelineQuery("FOS_DB", "UserLatLong",fetchCurrentLocation,ProjectionList);

            

            if(Array.isArray(LatLongData) && LatLongData.length>0)
            {
                let ProcessedLatLongData =ProcessLatLongData(LatLongData);
                result['LatLongData']= ProcessedLatLongData;

            }
            else{
                result['LatLongData']=null;
            }
            if(Array.isArray(CurrentLocation) && CurrentLocation.length>0)
            {
                result['CurrentLocation']=CurrentLocation[0];
            }
            else{
                result['CurrentLocation']=null;
            }

        }
        return result;
    }
    catch (e) {
        return { }
    }

}


const FetchOTSData = async(userId,TlList)=>{
    
    let ProjectionList=['ManagerId','UserId','ServingAppointments','TotalAppointments','UpdatedOn','RealTimeStatusId'];

    let FetchAMwithAdvisors=await FetchMatrixDashboardConfigAndCache('AMwithAdvisors',60*60*1000);
    let finalTLlist =[...FetchAMwithAdvisors,...TlList];

    try{

        let pipelineQuery=[
            { "$match": { ManagerId: {"$in":finalTLlist} , ServingAppointments:{"$ne":null}, TotalAppointments: {"$ne":null}} },
            { "$sort": { UpdatedOn : -1 } },
            {
                "$group": {
                    _id: "$UserId",
                    latestEntry: { "$first": "$$ROOT" }
                }
            },
            { $replaceRoot: { newRoot: "$latestEntry" } }

        ];

        let docs =await PipelineQuery("MATRIX_DASHBOARD_DB","RealTimeAppointmentData",pipelineQuery, ProjectionList);
         
        if(Array.isArray(docs) && docs.length>0)
        {
            
            let CountSum = {};

            docs.forEach((val)=>{
                if(val.UpdatedOn && ((new Date()- new Date(val.UpdatedOn))/(1000*60))<720)
                {
            
                if(!CountSum[val.ManagerId])
                {
                    CountSum[val.ManagerId]={
                        // count:1,
                        // sum: val.OTSValue,
                        ServingAppointments: val.ServingAppointments,
                        TotalAppointments: val.TotalAppointments
                    }
                }
                else{
                    let obj = CountSum[val.ManagerId];
                    // CountSum[val.ManagerId].count= obj.count+1;
                    // CountSum[val.ManagerId].sum = obj.sum+val.OTSValue;
                    CountSum[val.ManagerId].ServingAppointments = obj.ServingAppointments + val.ServingAppointments;
                    CountSum[val.ManagerId].TotalAppointments = obj.TotalAppointments + val.TotalAppointments;
                }
            
                }
            })
   
            // let averageOTSByTL = [];
            // Object.keys(CountSum).forEach((val)=>{
            //     averageOTSByTL.push({
            //         TlId :  val,
            //         OTS : CountSum[val].sum/CountSum[val].count
            //     })
            // })

            return CountSum;
        }
        return {};
    }
    catch(e)
    {
        console.log("Error in OTSData: ",e );
        return {};
    }
}

const FetchSocialMediaAgents = async () => {
    try
    {
        let SocialMediaUsersList = await FetchMatrixDashboardConfigAndCache('SocialMediaUsers',60*60*1000);
        if(Array.isArray(SocialMediaUsersList) && SocialMediaUsersList.length>0)
        {
            return SocialMediaUsersList;
        }
    }
    catch(e)
    {
        console.log("Error in fetching Social Media Agents - " , e);
    }
    return [];
}


module.exports={
    StoreSocketId:StoreSocketId,
    RemoveSocketId: RemoveSocketId,
    GetActiveSocketList: GetActiveSocketList,
    AddSupervisorsList: AddSupervisorsList,
    ReturnSocketList: ReturnSocketList,
    ReturnMongoData: ReturnMongoData,
    ReturnAgentLatLong: ReturnAgentLatLong,
    ReturnAgentLatLongByTime: ReturnAgentLatLongByTime,
    ReturnAgentLatLongByAppointment:ReturnAgentLatLongByAppointment,
    FetchOTSData: FetchOTSData,
    FetchSocialMediaAgents : FetchSocialMediaAgents
}