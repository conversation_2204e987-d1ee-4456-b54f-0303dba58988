
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import {
  GetCommonData, addRecord, UpdateData, DeleteData
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownListMongo';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { fnRenderfrmControl, fnDatatableCol, fnCleanData } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class ChatAgentConfigure extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "livechat_department_agents",
      departmentId: "0",

      PageTitle: "Chat Agent Configuration",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {}
    };
    this.departmentchange = this.departmentchange.bind(this);
    this.handleRemove = this.handleRemove.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.selectedrow = {"username": "", "isOutbound": false, "chatbot": false, "offline": false, "newcar": false }
    this.columnlist = [
      {
        name: "_id",
        label: "id",
        type: "hidden",
        hide: true,
      },
      {
        name: "username",
        label: "Username",
        type: "string",
        searchable: true,
        editable: false
      },
      {
        name: "count",
        label: "OpenChat",
        type: "number",
        editable: false
      },
      {
        name: "order",
        label: "Grade",
        type: "number",
        searchable: true,
      },
      {
        name: "limit",
        label: "OpenLimit",
        type: "number"
      },
      {
        name: "dailylimit",
        label: "Dailylimit",
        type: "number"
      },
      {
        name: "assigned",
        label: "Assigned",
        type: "number",
        editable: false
      },

      {
        name: "isOutbound",
        label: "IsOutbound",
        type: "bool"
      },
      {
        name: "chatbot",
        label: "Chatbot",
        type: "bool"
      },
      {
 
        name: "offline",
        label: "Offline",
        type: "bool"
      },
      {
        name: "newcar",
        label: "Newcar",
        type: "bool"
      },
    ];
    let count = 0;
  }



  componentDidMount() {

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);
  }



  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {

      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.setState({ store: nextProps.CommonData });
    }

  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => { if (window.confirm('Are you sure you wish to block this agent?')) this.handleRemove(row)}}><i className="fa fa-trash" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
 handleRemove(row){

    this.props.DeleteData({
      root: this.state.root,
      body: {departmentId: row.departmentId, username: row.username}
    }, function (data) {
      if(data.data.status === 200) 
        toast("Agent removed!", { type: 'success' });
      else 
        toast.error("Agent could not be removed"); 
    })

    setTimeout(function () {
      this.props.GetCommonData({
        root: this.state.root,
        cols: {},
        c: "L",
        con: { "departmentId": row.departmentId},
      });
    }.bind(this), 1000);

  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: {...this.selectedrow}, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {
    if (document.getElementsByName("frmChatAgentConfigure").length > 0 &&
      document.getElementsByName("frmChatAgentConfigure")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      console.log("formvalue", formvalue);
      this.fnCleanData(formvalue);
      let id = formvalue["_id"];
      delete formvalue["_id"]

      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);

        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { _id: id },
          c: "L",

        }, function (data) {
          toast("Record Saved Successfully!", { type: 'success' });
        });



        this.props.addRecord({
          root: "History",
          body: {
            module: "ChatAgentDepartmentConfigure",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserId
          }
        }, function (data) {
          toast("History Maintained!", { type: 'success' });
        });
      } else{
        formvalue["departmentId"] = this.state.departmentId; 
        this.props.addRecord({
          root: this.state.root,
          body: formvalue
        }, function (data) {
          if(data.data.status === 400){
            toast.error(data.data.message) ; 
          } else
            toast("User Added Successfully!", { type: 'success' });
        });
      }
      let departmentid = formvalue["departmentId"];

      setTimeout(function () {
        this.props.GetCommonData({
          root: this.state.root,
          cols: {},
          c: "L",
          con: { "departmentId": departmentid },
        });
      }.bind(this), 1000);

      this.setState({ showModal: false });
    }
    return false;
  }
  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.type === 'number' ? parseInt(e.target.value) : e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }

  departmentchange(e) {
    this.setState({departmentId: e.target.value}); 
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: {},
      c: "L",
      con: { "departmentId": e.target.value },
    });

  }

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, event } = this.state;
    console.log("renderitems", items);
    // let departments = [];
    // departments.push(items);
    const ChatAgentDepartmentConfigure = {
      config:
      {
        root: "livechat_department",
        cols: { _id: 1, name: 1 },
        con: { enabled: true },
        statename: "",
        state: true,
        Idfield: /_id/g,
        Displayfield: /name/g,
      }
    }


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={7}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="Department_dropdown">
                        <DropDown firstoption="Select Department" col={ChatAgentDepartmentConfigure} onChange={this.departmentchange}>

                        </DropDown>
                      </Form.Group>

                    </Col>
                   <Col md={2}>
                      <Button disabled={this.state.departmentId==="0"&&true} variant="primary" onClick={this.handleShow}>Add User</Button>
                    </Col>
                   </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                    extention={true}
                    export={false}
                    print={false}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmChatAgentConfigure">
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    addRecord,
    UpdateData,
    addRecord,
    DeleteData
   }
)(ChatAgentConfigure);