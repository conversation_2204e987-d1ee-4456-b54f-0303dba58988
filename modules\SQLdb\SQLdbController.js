const tblList = require("../constants");
const tblListWithParams = require("../constants");
const methods = require("./SQLdbMethods");
const IncFileUpload = require("./IncFileUpload");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
const UploadMatrixFiles = require("./UploadMatrixFiles");
const UploadStoryFiles = require("./UploadStoryFile");
const newrelic = require('newrelic');
const env_config = require("../../env_config");
const { AesEncryption, isInputSafe, isValidQuery, ValidateUserForUpsertAccess } = require("../common/CommonMethods");
const LoggerMethods = require("../Loggerdb/Methods");

async function insert(req, res) {
    try {

        // console.log("Is this getting called");
        
        let error = false;
        let methodName = "";
        const logData = {};
        logData.requestTime = new Date(Date.now() + 330 * 60000)

        let endpoint = req.body?.data?.root;
        let inputdata = req.body?.data?.body;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        let userId = req.user?.userId ? req.user.userId : null;  
        // let hasAccess= await ValidateUserForUpsertAccess(userId, endpoint);
        // if (!hasAccess) {
        //           return res.send({
        //         status: 500,
        //         error: "Access to the executed action is blocked :("
        //     })
            
        // }
        // let userId = req.user?.userId ? req.user.userId : null;

        // let access = await ValidateUserForUpsertAccess(userId, endpoint);

        // if(!access)
        // {
        //     return res.status(500).json({
        //         status: 500,
        //         error: 'Access Denied'
        //     });
        // }

        var query = 'INSERT INTO ' + tblList[endpoint] + '(';
        var columns = [];
        var VALUES = [];
        let sqlparams = [];
        // for (var key in inputdata) {
        //     columns.push(key);
        //     VALUES.push(inputdata[key]);
        // }
        for (var key in inputdata) {

            if (!isInputSafe(key) || !isInputSafe(inputdata[key])) {
                error = true;
                methodName = "InsertDataColumnInjectionLog"
            }

            columns.push(`${key}`);
            VALUES.push(`@${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + columns.join() + ") VALUES ( ";
        query = query + VALUES.join() + ")";

        if (req.body.data.scope)
            query = query + "; SELECT SCOPE_IDENTITY()";
        // console.log(inputdata);
        // console.log(query);
        // console.log(sqlparams);

        try {
            if (!isValidQuery(query, "insert")) {
                error = true;
                methodName = "SQLCommandInjectionLog";
            } 
        } catch (error) {
            console.error('Exception occured in validating query: ' + error)
        }

        if (error) {
            logData.RequestText = query;
            logData.ResponseText = endpoint;
            logData.Method = methodName;
            logSQLQueryInfo(logData);
        }


        let result = await sqlHelper.sqlquery("L", query, sqlparams);
        // console.log(result);
        let e = JSON.stringify(result);
        var myArr = JSON.parse(e);

        // if (typeof (myArr[0]) !== 'undefined') {
        //     if (typeof (myArr[0].error) !== 'undefined') {
        //         return res.send({
        //             status: 500,
        //             error: myArr[0].error
        //         });
        //     }
        // }

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function update(req, res) {
    try {

        let error = false;
        let methodName = "";
        const logData = {};
        logData.requestTime = new Date(Date.now() + 330 * 60000)

        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        let querydata = req.body.data.querydata;

        let userId = req.user?.userId ? req.user.userId : null;  
        // let hasAccess= await ValidateUserForUpsertAccess(userId, endpoint);
        // if (!hasAccess) {
          
        //             return res.send({
        //         status: 500,
        //         error: "Access to the executed action is blocked :("
        //     })
            
        // }
        
        // if(endpoint=='Users')
        // {
        //     let query2 = 'select count(1) as access from CRM.UserMenuMap where MenuId IN (281,280,306,344) and UserId=@UserId';
        //     let sqlparam2 = [];
        //     sqlparam2.push({ key: "UserId", value: userId });
        //     let result = await sqlHelper.sqlquery("R", query2, sqlparam2);
        //     let records = result.recordsets[0][0];
        //     // console.log("the reocrds is ",records)
        //     if(!records.access)
        //     {
        //     return res.send({
        //         status: 500,
        //         error: "Access to the executed action is blocked :("
        //     })
        //     }
        // }

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
        var updatedata = [];
        var updatequery = [];
        let sqlparams = [];

        for (var key in inputdata) {

            if (!isInputSafe(key) || !isInputSafe(inputdata[key])) {
                error = true;
                methodName = "UpdateDataColumnInjectionLog"
            }

            updatedata.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + updatedata.join();
        query = query + ` WHERE `;

        for (var key in querydata) {
            if (!isInputSafe(key) || !isInputSafe(querydata[key])) {
                error = true;
                methodName = "WhereClauseInjectionLog"
            }
            updatequery.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: querydata[key] });
        }
        query = query + updatequery.join(' and ');
        // console.log(query);

        try {
            if (!isValidQuery(query, "update")) {
                error = true;
                methodName = "SQLCommandInjectionLog";
            } 
        } catch (error) {
            console.error('Exception occured in validating query: ' + error)
        }

        if (error) {
            logData.RequestText = query;
            logData.ResponseText = endpoint;
            logData.Method = methodName;
            logSQLQueryInfo(logData);
        }

        let result = await sqlHelper.sqlquery("L", query, sqlparams);

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });

    } catch (err) {
        console.log(err);
        return res.status(500).json({
            status: 500,
            error: err
        });
    }
    finally {
        await sql.close();
    }
}

// function isInputSafe(input) {
//     const blacklist = [
//         /--/,
//         /;/,          
//         /UNION SELECT/i,  
//         /OR '.*'='.*'/i,
//         /SELECT.*FROM/i,  
//         /INSERT/i,       
//         /UPDATE/i,        
//         /DELETE/i,        
//         /COUNT/i,         
//         /MIN/i,           
//         /MAX/i            
//     ];
    
//     return !blacklist.some(pattern => pattern.test(input));
// }

async function getdata(req, res) {
    try {
        const logData = {};
        let methodName = "";
        logData.requestTime = new Date(Date.now() + 330 * 60000)

        let error = false;
        let errorMessage = "";

        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        if (methods.FindRoot(req, res)) {
            return;
        }

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        // let skip = req.query.skip ? parseInt(req.query.skip) : 0,
        //     limit = req.query.limit ? parseInt(req.query.limit) : 10;

        let tablename = tblList[endpoint];
        if (tablename?.toUpperCase().indexOf("NOLOCK") == -1) {
            tablename = tablename + " (NOLOCK) "
        }

        var query = 'SELECT  * FROM ' + tablename;// + ' (NOLOCK) ';

        try {
            if (req.query.cols && req.query.cols.length > 0) {

                for(const column of req.query.cols)
                {
                    if(!isInputSafe(column))
                    {
                        error = true;
                        methodName = "ColumnInjectionLog"
                        errorMessage += "Invalid Columns, "
                        break;
                    }
                }
    
                query = 'SELECT  ' + req.query.cols.join() + ' FROM ' + tablename;// + ' (NOLOCK) ';
            }
    
            if (req.query.con && req.query.con.length > 0) {
    
                for(const condition of req.query.con)
                {
                    if(!isInputSafe(condition))
                    {
                        error = true;
                        methodName = "WhereClauseInjectionLog";
                        errorMessage += "Invalid WHERE clause, "
                        break;
                    }
                }
            }
        } catch (error) {
            console.error('Exception occured in validating query: ' + error)
        }

        

        var whereCondition = [];
        let sqlparams = [];
        if (req.query.con) {
            query = query + ` WHERE `;
            for (var key in req.query.con) {
                let json = req.query.con[key]
                for (var obj in json) {
                    // whereCondition.push(`${obj} = '${json[obj]}'`);
                    var objparam = obj;
                    if (obj.indexOf('.') !== -1) {
                        var objparam = obj.replace('.', '')
                    }
                    //console.log(objparam);
                    if (obj.toLowerCase().indexOf('receivedon') > -1 || obj.toLowerCase().indexOf('date') > -1 || obj.toLowerCase().indexOf('time') > -1) {
                        whereCondition.push(`CAST(${obj} AS DATE) = @${objparam}`);
                    }
                    else if (obj.toLowerCase().indexOf('likesearch') > -1) {
                        whereCondition.push(`${obj.substr(0, obj.indexOf('_'))} like @${objparam.substr(0, objparam.indexOf('_'))}`);
                    }
                    else {
                        whereCondition.push(`${obj} = @${objparam}`);
                    }
                    if (objparam.toLowerCase().indexOf('likesearch') > -1) {
                        sqlparams.push({ key: objparam.substr(0, objparam.indexOf('_')), value: '%' + json[obj] + '%' });

                    } else {
                        sqlparams.push({ key: objparam, value: json[obj] });
                    }
                }
            }
            query = query + whereCondition.join(' and ');
        }
        if (req.query.order) {
            if (!req.query.direction)
                query = query + " ORDER BY " + req.query.order + " DESC";
            else
                query = query + " ORDER BY " + req.query.order + " " + req.query.direction;
        } else {
            query = query + " ORDER BY 2 DESC";
        }
        // query =
        //     query +
        //     ` ORDER BY 1
        //     OFFSET ${skip} ROWS
        //     FETCH NEXT ${limit} ROWS ONLY`;

        try {
            if (!isValidQuery(query, "select")) {
                error = true;
                methodName = "SQLCommandInjectionLog";
                errorMessage += "Invalid query";
            } 
        } catch (error) {
            console.error('Exception occured in validating query: ' + error)
        }

        // console.log(query);

        if (error) {
            logData.RequestText = query;
            logData.ResponseText = endpoint;
            logData.Method = methodName;
            logSQLQueryInfo(logData);

            return res.send({
                status: 500,
                message: errorMessage,
            })
        }
        //console.log(sqlparams);

        // let result = await sql.query(query);
        // await sql.close();
        let result = await sqlHelper.sqlquery(c, query, sqlparams);
        //console.log(result.recordsets);
        return res.send({
            status: 200,
            data: result.recordsets,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}

const logSQLQueryInfo = (logData) => {

    const responseTime = new Date(Date.now() + 330 * 60000)

    logData.TrackingID = 0;
    logData.Application = "MatrixDashboard";
    logData.Exception = null;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixDashboard";

    LoggerMethods.LogKafka(logData);
}

async function getdatasp(req, res) {
    try {
        // console.log(req.query);
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        let userId = req.user?.userId ? req.user.userId : null;  
        // let hasAccess= await ValidateUserForUpsertAccess(userId, endpoint);
        // if (!hasAccess) {
          
        //         return res.send({
        //         status: 500,
        //         error: "Access to the executed action is blocked :("
        //     })
            
        // }
        var query = tblList[endpoint];
        let sqlparam = [];
        //const request = new sql.Request();
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                const obj = params[key] || {}
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        let result = await sqlHelper.sqlProcedure(c, query, sqlparam);
        return res.send({
            status: 200,
            data:  result.recordsets,
            message: "Success"
        });



    } catch (err) {
        console.log('catch error', err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}

const GetSqlParams = ({ required, user, params, endpoint }) => {
    try {
        const parameters = params && params.length > 0 && params[0] || '{}';
        const obj = parameters
        let sqlParams = [];

        for (let itr = 0; itr < required.length; itr++) {
            const item = required[itr];
            const { element, from, userKey } = item;

            if (from != 'user') {
                if (!(element in obj)) {
                    if(endpoint != 'InsertUserGroup'){
                        return [];
                    }
                } else {
                    sqlParams.push({
                        key: element,
                        value: obj[element]
                    });
                }

            } else if (from == 'user' && userKey) {
                const info = user[userKey] || null;
                sqlParams.push({
                    key: element,
                    value: info
                });
            }
        }
        return sqlParams;
    } catch (err) {
        console.log('Inside GetSqlParams : ', err);
        return [];
    }
}


async function getdataspV2(req, res) {
    try {
        let endpoint = req.query.root;
        let c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        let User = req.user || {};

        if (!tblListWithParams[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        if( endpoint in ['GetAllUserGroups','InsertUserGroup'] && req?.user?.roleId == 13){
          return res.status(401).json({
            message: 'User Not Authorised to see the content.',
            status: 401,
            data: [],
          })
        }

        let userId = req.user?.userId ? req.user.userId : null;  
        // let hasAccess= await ValidateUserForUpsertAccess(userId, endpoint);
        // if (!hasAccess) {
          
        //         return res.send({
        //         status: 500,
        //         error: "Access to the executed action is blocked :("
        //     })
            
        // }
        const query = tblListWithParams[endpoint].proc || "";
        const requiredParameters = tblListWithParams[endpoint].params || [];
        let sqlParams = GetSqlParams({ required: requiredParameters, user: req.user, params: params, endpoint: endpoint });

        // console.log('List SP V2:: ', sqlParams);
        let result = await sqlHelper.sqlProcedure(c, query, sqlParams);

        if(endpoint === 'InsertUserGroup'){
            if (result?.recordsets?.[0]?.[0]?.GroupExistsFlag === 1) {
                return res.status(400).json({ IsError: true, message: "UserGroupName already exists." });
            }
        }
        return res.send({
            status: 200,
            data: result?.recordsets || [],
            info: {},
            message: "Success"
        });

    } catch (err) {
        console.log('catch error', err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function deleteRow(req, res) {
    try {
        // console.log('---------');
        let endpoint = req.body.data.root;
        // var patharray = req.url.split("/");
        // var endpoint = patharray[patharray.length - 1];
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'DELETE FROM ' + tblList[endpoint];
        var updatequery = [];
        if (req.body.data.query) {
            query = query + ` WHERE `;
            for (var key in req.body.data.query) {
                updatequery.push(`${key} = '${req.body.data.query[key]}'`)
            }
            query = query + updatequery.join();
        }
        // console.log(query, '-------');
        let result;
        if (query.indexOf("WHERE") > -1) {
            result = await sqlHelper.sqlquery("L", query);
        }
        //let result = await sql.query(query);
        res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        await sql.close();
    }
}


async function agentidletime(req, response) {
    try {

        var query = "[MTX].[GetAgentIdleTime]  " + req.query.u;

        let result = await sqlHelper.sqlquery("R", query);

        let idletime = 0;
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                idletime = element.IdleTime;
            });
        }




        fs.readFile(path.join(appRoot, "client", "agentwidget.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{data}", Math.round(idletime / 60));
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}
function secondsToHms(d) {
    d = Number(d);
    var h = Math.floor(d / 3600);
    var m = Math.floor(d % 3600 / 60);
    var s = Math.floor(d % 3600 % 60);

    var hDisplay = h > 0 ? h + (h == 1 ? ":" : ":") : "";
    var mDisplay = m > 0 ? m + (m == 1 ? ":" : ":") : "";
    var sDisplay = s > 0 ? s + (s == 1 ? "" : "") : "";
    return hDisplay + mDisplay + sDisplay;
}
async function agentstats(req, response) {
    try {

        var query = "[MTX].[AgentLoginTracker]";
        let sqlparams = [];
        sqlparams.push({ key: "UserId", value: req.query.u });
        //console.log(sqlparams);
        let result = await sqlHelper.sqlProcedure("R", query, sqlparams);
        let AgentStats = {};
        // console.log(result.recordset);
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                AgentStats = element;
            });
        }
        fs.readFile(path.join(appRoot, "client", "agentstats.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{FLT}", moment(AgentStats.FIRSTLOGIN).utc().format("HH:mm:ss"));
                        data = data.replace("{talktime}", secondsToHms(AgentStats.TOTALTALKTIME));
                        data = data.replace("{totalbreaks}", AgentStats.SUM_DISPOSITIONMINS);
                        data = data.replace("{UniqueDailed}", AgentStats.UNIQUEDIAL);
                        data = data.replace("{MissedCallbacks}", AgentStats.MISSEDCALL);
                        data = data.replace("{APE}", AgentStats.APE);
                        data = data.replace("{BKGS}", AgentStats.BKGS);
                        data = data.replace("{IdleTime}", AgentStats.IDLETIME);
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}

async function login(req, res) {
    try {

        let inputdata = req.body;

        let query = "Select userid from CRM.UserDetails (NOLOCK) where EmployeeId = @EmployeeId and CAST(Password AS Varchar) = @Password";
        let sqlparams = [];
        sqlparams.push({ key: "EmployeeId", value: inputdata.EmpId });
        sqlparams.push({ key: "Password", value: inputdata.password });
        let result = await sqlHelper.sqlquery("R", query, sqlparams);

        if (result && result.recordset.length > 0) {
            res.cookie("userid", result.recordset[0].userid);
            res.cookie("AgentId", result.recordset[0].userid);
            res.redirect(req.headers.origin + "/admin/Users");
        } else {
            res.redirect(req.headers.origin + "?m=User Not found.");
        }
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

function hasExtension(filename) {
    const extensionRegex = /\.[a-zA-Z0-9]+$/;
    return extensionRegex.test(filename);
}

function checkRoleId(roleId)
{
    if(![0,13].includes(parseInt(roleId)))
    {
        return true;
    }
    return false;
}

async function awsfile(req, res) {
    try {

        let key = req.query.key;
        let roleId  = req.user?.roleId || 0;

        if(!hasExtension(key) || !checkRoleId(roleId))
        {
                return res.send({
                    status: 500,
                    error: "No such file"
                })
        } 
        let bucket = (req.query.bucket) ? req.query.bucket : 'newcctecbuckt'
        let result = await Utility.fetchAWSfiles(key, bucket);

        res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function getawsfile(req, res) {
    try {

        let key = req.query.key;
        let bucket = req.query.bucket;
        let result = await Utility.getAWSfiles(key, bucket);

        res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function UploadIncentiveFile(req, res) {
    try {
       
        methods.UploadIncentiveFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadMonthlyIncentiveFile(req, res) {
    try {
        methods.UploadMonthlyIncentiveFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function FileUpload(req, res) {
    try {
        methods.FileUpload(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


async function UploadVideoFile(req, res) {
    try {
        
        methods.UploadVideoFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadStoryFile(req, res) {
    try {
    
        UploadStoryFiles.UploadStoryFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}
async function UploadChatAgentFile(req, res) {
    try {
      
        await UploadMatrixFiles.UploadChatAgentFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function HealthCheck(req, res) {
    try {
        // console.log('healthcheck');
        methods.HealthCheck(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}


async function rejectLeads(req, res) {
    try {
        methods.uploadLeadRejectionFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function uploadUserGrades(req, res) {
    try {
        methods.uploadUserGradesFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function ProcessUploadIncentivefile(req, res) {
    try {
        IncFileUpload.ProcessUploadIncentivefile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadAllocationFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function HniLogicMaster(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}


async function AgentSurvey(req, res) {
    try {
        UploadMatrixFiles.AgentSurvey(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function openSalesView(req, res) {
    try {
        methods.openSalesView(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadAdultChildScoreFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

async function UploadCityScoreFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

async function getUserlistbyManagerId(req, res) {
    try {
        methods.getUserlistbyManagerId(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
       
    }
}

async function getDuplicateLeadsData(req, res) {
    try {
        methods.getDuplicateLeadsData(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
       
    }
}
async function getDuplicateLeadsDatabyProductId(req, res) {
    try {
        methods.getDuplicateLeadsDatabyProductId(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
       
    }
}

async function getIsAllowedDuplicateSearch(req, res) {
    try {
        methods.getIsAllowedDuplicateSearch(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
       
    }
}

async function getPaymentOverDueCountByManagerId(req, res) {
    try {
        methods.getPaymentOverDueCountByManagerId(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
       
    }
}

async function UploadPayUScoreRankFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

async function UploadMatrixFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

async function IsUserAttributesAuthorized(req, res) {
    try {
        methods.IsUserAttributesAuthorized(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

async function GetAdditionalLDBulkUploadData(req, res) {
    try {
        methods.GetAdditionalLDBulkUploadData(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}
async function FetchHealthRenewalPaymentLink(req, res) {
    try {
        methods.FetchHealthRenewalPaymentLink(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}
async function SetHealthRenewalPaymentLink(req, res) {
    try {
        methods.SetHealthRenewalPaymentLink(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}
async function UpdateRenewalFileStatus(req, res) {
    try {
        methods.UpdateRenewalFileStatus(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}
async function GetStagedFileData(req, res) {
    try {
        methods.GetStagedFileData(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}
async function GetUniqueGroups(req, res) {
    try {
        methods.GetUniqueGroups(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
    }
}

module.exports = {
    insert: insert,
    update: update,
    getdata: getdata,
    delete: deleteRow,
    getdatasp: getdatasp,
    getdataspV2: getdataspV2,
    agentidletime: agentidletime,
    agentstats: agentstats,
    awsfile: awsfile,
    login: login,
    UploadIncentiveFile: UploadIncentiveFile,
    UploadVideoFile: UploadVideoFile,
    UploadStoryFile: UploadStoryFile,
    UploadChatAgentFile: UploadChatAgentFile,
    HealthCheck: HealthCheck,
    getawsfile: getawsfile,
    rejectLeads: rejectLeads,
    uploadUserGrades: uploadUserGrades,
    ProcessUploadIncentivefile: ProcessUploadIncentivefile,
    UploadAllocationFile: UploadAllocationFile,
    AgentSurvey: AgentSurvey,
    UploadMonthlyIncentiveFile: UploadMonthlyIncentiveFile,
    FileUpload: FileUpload,
    openSalesView: openSalesView,
    HniLogicMaster: HniLogicMaster,
    getUserlistbyManagerId: getUserlistbyManagerId,
    getDuplicateLeadsData: getDuplicateLeadsData,
    getIsAllowedDuplicateSearch: getIsAllowedDuplicateSearch,
    UploadAdultChildScoreFile: UploadAdultChildScoreFile,
    UploadCityScoreFile: UploadCityScoreFile,
    getPaymentOverDueCountByManagerId: getPaymentOverDueCountByManagerId,
    getDuplicateLeadsDatabyProductId: getDuplicateLeadsDatabyProductId,
    UploadPayUScoreRankFile: UploadPayUScoreRankFile,
    UploadMatrixFile: UploadMatrixFile,
    IsUserAttributesAuthorized: IsUserAttributesAuthorized,
    GetAdditionalLDBulkUploadData: GetAdditionalLDBulkUploadData,
    UpdateRenewalFileStatus : UpdateRenewalFileStatus,
    GetStagedFileData : GetStagedFileData,
    GetUniqueGroups : GetUniqueGroups,
    FetchHealthRenewalPaymentLink : FetchHealthRenewalPaymentLink,
    SetHealthRenewalPaymentLink : SetHealthRenewalPaymentLink
};