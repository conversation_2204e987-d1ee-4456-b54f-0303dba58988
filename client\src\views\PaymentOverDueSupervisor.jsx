import React from "react";
import {
  GetAgentDetailsByManager,
  GetCommonData, GetCommonspData, GetCommonspDataV2, GetComunicationData,
  GetPaymentOverDueCountByManagerId
} from "../store/actions/CommonAction";
import { getuser,fnDatatableCol, getCookie, getBaseUrl } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import DropDown from './Common/DropDown';
import { Button, Form } from 'react-bootstrap';
import { Loader } from "react-bootstrap-typeahead";
import config from "config";
import DataTableWithFilter from "./Common/DataTableWithFilter";

class PaymentOverDueSupervisor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Payment Overdue Dashboard",
      items: {},
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      countkey: "-1",
      productList: [{Id: 2, Display: 'Health'},{Id: 7, Display: 'TermLife'},{Id: 115, Display: 'Investments'}],
      PaymentLoader: false,
      ProductId: 0,
      showFilterCount: false,
      Users: []
    };
    this.handleShow = this.handleShow.bind(this);
    this.countchange = this.countchange.bind(this);

    // const Cell = ({ v }) => (
    //   <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    // );
    this.columnlist = [

      {
        name: "EmployeeId",
        label: "EmployeeId",
        sortable: true,
      },
      {
        name: "UserId",
        label: "UserId",
        hide: true,
        type: "number",
        sortable: true,
      },
      {
        name: "UserName",
        label: "UserName",
        sortable: true,
      },
      // {
      //   name: "GroupName",
      //   label: "GroupName",
      //   sortable: true,
      // },
      {
        name: "Pendingcount",
        label: "Overdue Count",
        sortable: true,
      },
      {
        name: "Link",
        label: "Dashboard link",
        type: "string",
        editable: true,
        cell: row => <a style={{cursor: "pointer",color: "#0075FF"}} target="_blank" onClick={(e) => this.CreateDashboardURL(e, row)}>View Dashboard</a>,
        width: "200px"
      },
      
    ];
  }

  async CreateDashboardURL(e, row){
    //let domain = getCookie("cdomain");
    let UserId  = btoa(Number(row.UserId));
    let base_url = await getBaseUrl();
    let url = base_url+"PaymentFailedCases?UserId="+UserId
    window.open(url, "_blank");
  }
  handleShow(e) {

    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);

  }

  countchange(e) {
    this.setState({ countkey: e.target.value });
  }

  filterdata(e) {

    let alldata = (this.state.items.UsersList)?this.state.items.UsersList:[]
    let AgentData = [];
    if ( this.state.countkey === "-1") {
      return alldata;
    }else if(this.state.countkey !== "-1"){
        alldata.forEach(element => {           
            if (this.state.countkey === "5" && this.state.countkey >= element.Pendingcount) {
              AgentData.push(element);
            } 
            if(this.state.countkey === "6" && this.state.countkey <= element.Pendingcount){
                AgentData.push(element);
            }     
          });
        return AgentData; 
    }  
    return AgentData;
  }

  componentDidUpdate(prevProps, prevState){

    if((prevState.pendingCount !== this.state.pendingCount ) || (prevState.UserListArray !== this.state.UserListArray)){
    if(this.state.pendingCount && this.state.pendingCount.length > 0){
    let UserListPendingCount = (this.state.pendingCount && this.state.UserListArray)?this.mergeArrays(this.state.pendingCount, this.state.UserListArray):[];
    let items = { UsersList:  UserListPendingCount }
    this.setState({ items: items , showFilterCount : true});
    }
    }
  }

  componentDidMount() {
    //this.GetProductList();
    const user = getuser();
    this.setState({ ReportTime: new Date(),  Users: user });
    // setTimeout(function () {
    //   this.fetchData();
    // }.bind(this), 500);
  }

  
  // GetProductList(){
  //   this.props.GetCommonspDataV2({
  //     root: "GetUserProductList"
  //   }, function (result) {
  //     if(result && result.data && result.data.data){
  //       var array = result.data.data[0];
  //       var productids = array.map(function(elm) {
  //         return { Id: elm.ProductId, Display: elm.ProductDisplayName};
  //       });

  //       this.setState({productList : productids});
  //     }
  //     }.bind(this));
    
  // }

  fetchData = async () => {
    if(this.state.ProductId == 0){
      toast("Please enter Product", { type: 'error' });
      return false;
    }
    let that = this;
    //var SelectedSupervisors = this.state.SelectedSupervisors;
    this.setState({ PaymentLoader : true});
    await GetAgentDetailsByManager({productId: this.state.ProductId, SupervisorIds: (this.state.SelectedSupervisors)?this.state.SelectedSupervisors.join(','):[]}, await function (data) {
    // await this.props.GetCommonspDataV2({
    //   root: "GetUserlistbyManagerId",
    //   params: [ { productId: this.state.ProductId }],
    // }, await function (data) {
     // this.setState({ PaymentLoader : false});
      if (data && data.data && data.data.data && data.data.data.length > 0) {
        this.setState({UserListArray : data.data.data})
      }
    }.bind(this));

    // await this.props.GetCommonspDataV2({
    //   root: "GetPaymentOverDueCountByManagerId",
    //   params: [{ ProductID: this.state.ProductId }],
    // }, await function (data) {
      await GetPaymentOverDueCountByManagerId({ProductId: this.state.ProductId, SupervisorIds: (this.state.SelectedSupervisors)?this.state.SelectedSupervisors.join(','):[]}, await function (data) {
      this.setState({ PaymentLoader : false});
      if (data && data.data && data.data.data && data.data.data[0] && data.data.data[0].length > 0) {
        this.setState({pendingCount : data.data.data[0]})
       //let pendingCount = [{ "AgentId": 90583,"Pendingcount": 34},{ "AgentId": 8223,"Pendingcount": 24}]
      }else{
        this.setState({ items: []});
      }
    }.bind(this));

    if (that.state.SelectedRows.length > 0) {
      that.dtRef.current.handleClearRows();
    }

  }

  mergeArrays = (pendingCount, UserListArray) => {
  //async mergeArrays(pendingCount, UserListArray) {
    // Create dictionaries based on UserId and AgentId
    const userDict = UserListArray &&  UserListArray.reduce((acc, item) => {
      acc[item.UserId] = item;
      return acc;
    }, {});

    const agentDict = pendingCount &&  pendingCount.reduce((acc, item) => {
      acc[item.AgentId] = item;
      return acc;
    }, {});

    // Merge arrays based on common keys
    const mergedArray = userDict && Object.keys(userDict).map((userId) => {
      const user_info = userDict[userId];
      const agent_info = (agentDict && agentDict[userId])?agentDict[userId]:0;

      const mergedInfo = {
        ...user_info,
        ...(agent_info || { "AgentId": Number(userId), "Pendingcount": 0 }),
      };

      return mergedInfo;

    });
    return mergedArray;
  }

  productchange= (e) => {
    this.setState({ ProductId: e.target.value });
  }

  CheckLoader(action) {
    if (action === 'payment') {
      if (this.state.PaymentLoader)
        return <Loader />;
    } 
  }


  render() {
    //const columns = this.columnlist;
    const columns = fnDatatableCol(this.columnlist);

    const { items, PageTitle } = this.state;
    console.log(items);
    const data = this.filterdata();
    let cl = [];

    if(items && Object.keys(items).length > 0 && items['UsersList'] && Object.keys(items['UsersList']).length > 0){
        cl.push(<option value={5}>{"<=5"}</option>)
        cl.push(<option value={6}>{">5"}</option>)
    }


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={5}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>

                    {this.state.Users && ![12,13].includes(this.state.Users.RoleId) &&<ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>
                    }

                  </Row>
                  <Row>  
                  <Col md={3}>
                    <span>Select Product</span>
                    <Form.Group as={Col} md={12} controlId="prod_dropdown" >
                    <DropDown firstoption="Select Product" items={this.state.productList} onChange={this.productchange}>
                    </DropDown>
                    </Form.Group> 
                  </Col>
                   
                  <Col md={2}>
                    {[12,13].includes(this.state.Users.RoleId) && <Button variant="primary" className="mt-4" onClick={() => this.fetchData()}>Fetch{this.CheckLoader('payment') }</Button>}
                    {![12,13].includes(this.state.Users.RoleId) && this.CheckLoader('payment') }
                  </Col>
                  </Row>
                  <Row>
                  {this.state.showFilterCount && <>
                  <Col md={9} sm={9} xs={9}></Col>
                  <Col md={3}>

                      <div className="form-group">
                    
                        <select className="form-select" onChange={this.countchange}>
                          <option value="0">Filter OverDue Count</option>
                          <option value="-1">ALL</option>
                          {cl}
                        </select>
                      </div>
                  </Col>
                  </>}
                    </Row>
            

                </CardHeader>

                <CardBody>
                  <div className="statusdata">
                    <DataTableWithFilter
                      columns={columns}
                      data={data && data.length > 0 ? data : []}
                      pagination={true}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense
                      ref={this.dtRef}
                    />

                  </div>
                </CardBody>

              </Card>
            </Col>

          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetComunicationData,
    GetCommonspDataV2
  }
)(PaymentOverDueSupervisor);




