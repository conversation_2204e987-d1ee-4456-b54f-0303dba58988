
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal, <PERSON>, Glyphicon } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, GetCommonspData, PostUploadStoryFormData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import moment from 'moment';
import { CompareJson, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  <PERSON>,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import Moment from 'react-moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import _, { bind } from 'underscore';
import CKTextEditor from './Common/CKTextEditor';

class AgentStories extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      root: "GetStoryList",
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      //root: "ProductGrpMapping",
      PageTitle: "Upload Story",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      prolist: '',
      selectedFile: null,
      StoryData: [],
      PostUploadVideoFormData: [],
      errors: {},
      addClass: 'btn btn-primary',
      clickVideo: false,
      clickModalUrl: '',
      content: '',
      editorText: '',
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);

    this.selectedrow = { "Id": 0, "ProductId": null, "IsActive": true,"EnableReaction": true, "StartDate": new Date(), "EndDate": moment(new Date()).add(1, 'days') }
    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );
    this.columnlist = [
      {
        name: "StoryID",
        label: "StoryId",
        type: "int",
        //hide: true,
        addable: false,
        editable: false,
        hideonmodal: true,
      },
      {
        name: "StoryName",
        label: "Enter Story Title",
        type: "string",
        searchable: true,
        cell: row => <Cell v={row.StoryName} />,
      },
      {
        name: "AudienceControl",
        label: "AudienceControl",
        type: "dropdown",
        config: {
          root: 'audienceControl',  
          data: [{ Id: 1, Display: "Product/BU Wise" },{ Id: 2, Display: "Excel Upload" }],
        },
        editable: false,
        hide: true,
      },
      {
        name: "Excel",
        label: "Choose Excel",
        type: "video",
        editable: false,
        hide: true,
      },
   
     
      {
        name: "EmployeeId",
        label: "EmployeeId",
        type: "string",
        searchable: true,
        editable: false,
        addable: false,
        cell: row => <Cell v={row.EmployeeId} />,
        width: "200px",
        hideonmodal: true,
      },
      {
        name: "Product",
        label: "Product",
        type: "dropdown",
        config: {
          root: "Products",
          cols: ["ID AS Id", "ProductName AS Display"],
          con: [{ "Isactive": 1 }]
        },
        searchable: true,
        editable: false,
        addable: false,
        hideonmodal: true,
      },
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        config: {
          root: "Products",
          cols: ["ID AS Id", "ProductName AS Display"],
          con: [{ "Isactive": 1 }]
        },
        hide: true,
        editable: false,
      },

    //   {
    //     name: "RoleId",
    //     label: "Role",
    //     type: "multiselect",
    //     searchable: true,
    //     distinct: true,
    //     config: {
    //       root: 'roleids',
    //       data: [{ Id: 13, Display: "Agent" }, { Id: 12, Display: "Supervisor" }, { Id: 2, Display: "Admin" }],
    //     },
    //     hide: true,
    //     editable: false,

    //   },// None value should be always 1 

    //   {
    //     name: "RoleName",
    //     label: "RoleName",
    //     type: "string",
    //     searchable: true,
    //     distinct: true,
    //     editable: false,
    //     addable: false,
    //     cell: row => <Cell v={row.RoleName} />,
    //     width: "200px",
    //     hideonmodal: true,
    //   },
      {
        name: "GroupId",
        label: "Group",
        type: "multiselect",
        searchable: true,
        distinct: true,
        config: {
          root: "vwMatrixUserGroup",
          cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display", "ProductID"],
        },
        hide: true,
        editable: false,
      },
      {
        name: "UserGroupName",
        label: "GroupName",
        type: "string",
        searchable: true,
        distinct: true,
        editable: false,
        addable: false,
        cell: row => <Cell v={row.UserGroupName} />,
        width: "200px",
        hideonmodal: true,

      },
      {
        name: "StartDate",
        label: "FromDate",
        type: "datetime",
        utc: "no",
      },
      {
        name: "EndDate",
        label: "ToDate",
        type: "datetime",
        utc: "no",
      },
      {
        name: "IsActive",
        label: "IsActive",
        type: "bool"
      },
      {
        name: "EnableReaction",
        label: "EnableReaction",
        type: "bool"
      },
      {
        name: "ContentType",
        label: "ContentType",
        type: "dropdown",
        config: {
          data: [{ Id: 1, Display: "Text" },{ Id: 2, Display: "Video" }, { Id: 4, Display: "Image" }],
        },
        editable: false,
      },
      {
        name: "Video",
        label: "Choose File",
        type: "video",
        editable: false,
        hide: true,
      },
      {
        name: "Text",
        label: "Enter Text",
        type: "textarea",
        editable: false,
        hide: true,
      },
    //   {
    //     name: "Link",
    //     label: "Link",
    //     type: "string",
    //     editable: true,
    //     cell: row => <a href={row.Link} target="_blank"><Cell v={row.Link} /></a>,
    //     width: "200px"
    //   },
      {
        name: "Listen",
        label: "View",
        cell: row =>
          <div className="listenUserDetails">
            <a onClick={(e) => this.getHtmlListen(e, row)} className="detailsinfo">
              <i className="fa fa-eye"></i></a>
          </div>,
        editable: false,
        addable: false,
        hideonmodal: true,

      },

    ];

    this.formColumnList = this.columnlist;
    let count = 0;
    this.ProductList = {
      config:
      {
        root: "Products",
        cols: ["ID AS Id", "ProductName AS Display"],
        con: [{ "Isactive": 1 }],
        //state: false
      }
    };
  }



  componentDidMount() {

  
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    this.props.GetCommonspData({
      root: 'GetStoryList',
      c: "L",
      params: [{ "ProductID": 0 , "userId":  getuser().UserID }],//getuser().UserID
    }, function (result) {
      if (result.data && result.data.data[0].length > 0) {
        debugger;
        console.log(result.data.data[0]);
        this.setState({ StoryData: result.data.data[0] });
      }
    }.bind(this));

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(this.state.StoryData, nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200) {
        //alert(nextProps.CommonData.InsertSuccessData.error);
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      }
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
      }
    }

  }

  validURL(str) {
    var pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
    return !!pattern.test(str);
  }

  getHtmlListen(e, row) {//debugger;
    // console.log('-----------------',row);
    // console.log('/////////////////////////////',row.PageHtmlUrl);
    this.setState({ clickVideo: true, clickModalUrl: row.Url, content: row.ContentType, clickModalText: row.Message});
    if (row.ContentType == 2) {
      var video = document.getElementById('StoryVideo');
      if (video) {
        video.play();
      }
    }
  }

  fnDatatableCol() {
    const columnslist = this.columnlist;//.filter(task => task.label !== 'Enter Link Url');


    var columns = fnDatatableCol(columnslist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        {/* <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button> */}
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }

  // async handleCopy(row) {
  //   debugger;
  //   let results = await this.props.GetCommonspData({
  //     root: 'GetStoryList',
  //     c: "L",
  //     params: [{ "pageId": row.PageId }],
  //   }, function (result) {
  //     if (result.data && result.data.data[0]) {//debugger;
  //       console.log('-------------------------------', result.data.data[0], result.data.data[1], result.data.data[2], result.data.data[3]);
  //       //this.setState({ StoryData: result.data.data[0] });
  //       if (result.data.data[1].length > 0) {
  //         if (result.data.data[1][0].RoleId == 0) {
  //           row.RoleId = [{ label: "All items are Selected", value: "*" }]
  //         } else {
  //           let RoleIdArray = [];
  //           result.data.data[1].map(function (val) {

  //             RoleIdArray.push({
  //               "label": val.RoleName.toString(),
  //               "value": val.RoleId,
  //             });
  //           })
  //           row.RoleId = RoleIdArray;
  //         }
  //       }

  //       if (result.data.data[3] && result.data.data[3].length > 0) {
  //         row.ProductID = result.data.data[3][0].ProductID;
  //       }

  //       if (result.data.data[2].length > 0) {
  //         if (result.data.data[2][0].GroupId == 0) {
  //           row.GroupId = [{ label: "All items are Selected", value: "*" }]
  //         } else {
  //           let GroupIdArray = [];
  //           result.data.data[2].map(function (val) {

  //             GroupIdArray.push({
  //               "label": val.UserGroupName.toString(),
  //               "value": val.GroupId,
  //             });
  //           })
  //           row.GroupId = GroupIdArray;
  //         }
  //       }
  //       console.log('////////////////////////////////////', row);
  //       this.setState({ formvalue: row, event: "Copy", showModal: true, FormTitle: "Copy Record" });
  //     }
  //   }.bind(this));

  // }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.selectedrow = { "Id": 0, "ProductId": null, "IsActive": true,"EnableReaction": true, "StartDate": new Date(), "EndDate": moment(new Date()).add(1, 'days') }
    // delete this.selectedrowshow.Link;

    // console.log('selectedrow',this.selectedrowshow);

    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  async handleSave(e) {debugger;
    e.preventDefault();

    //if (document.getElementsByName("frmUploadStory").length > 0 &&
    //  document.getElementsByName("frmUploadStory")[0].reportValidity()) {
       var startDate = this.state.formvalue.StartDate;
      var endDate = this.state.formvalue.EndDate;
      if (this.state.event == "Add" ) {
      if(typeof(startDate) == 'object'){  
      this.state.formvalue.StartDate.toJSON = function(){ return moment(this).format("YYYY-MM-DDTHH:mm:ss"); }
      }else{
        this.state.formvalue.StartDate = startDate.substring(0, startDate.indexOf('+'));
      }
      if(typeof(endDate) == 'object'){
      this.state.formvalue.EndDate.toJSON = function(){ return moment(this).format("YYYY-MM-DDTHH:mm:ss"); }
      }else{
        this.state.formvalue.EndDate = endDate.substring(0, endDate.indexOf('+'));
      }
      } 
      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      console.log("formvalue", formvalue);

      let id = formvalue["Id"];
      let by = getuser().UserID;

      delete formvalue["Id"]
      if (this.state.event == "Edit") {//debugger;
        this.fnCleanData(formvalue);
        this.setState({ selectedFile: '' });
        var result = await this.addVideo(formvalue, "edit");
        if (result == false) {
          return false;
        }
        // let res =  this.props.GetCommonspData({
        //   root: 'InsertStoryData',
        //   c: "L",
        //   params: [{ "pageId" : formvalue["pageId"] , "IsRequired" : formvalue["IsRequired"] , "SurveyName" : formvalue["SurveyName"] , "StartDate" : formvalue["StartDate"]
        //   , "Endtime" : formvalue["EndDate"], "PageUrl" : '',"ContentType": 0, "UserId" : by }],
        // });

      } else if (this.state.event == "Copy") {
        debugger;
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        //formvalue["CreatedOn"] = new Date();
        console.log('formvalueee////////////////////////////', formvalue);
        this.fnCleanData(formvalue);
        var result = await this.addVideo(formvalue, "add");
        if (result == false) {
          return false;
        }

      } else {
        debugger;

        var result = await this.addVideo(formvalue, "add");
        if (result == false) {
          return false;
        }

      }
      let productid = parseInt(formvalue["ProductID"]);
      
      if (productid) {
        var con = { "ProductId": productid, "userId":  getuser().UserID};
      } else {
        var con = { "StoryId": formvalue["StoryID"], "userId":  getuser().UserID};
      }
     
      //setTimeout(function () {

        this.props.GetCommonspData({
          root: 'GetStoryList',
          c: "L",
          params: [con],
        }, function (result) {
          if (result.data && result.data.data[0]) {//debugger;
            console.log(result.data.data[0]);
            this.setState({ StoryData: result.data.data[0] });
          }
        }.bind(this));
      

      // }.bind(this), 2000);
      this.setState({ showModal: false });
    //}
    this.setState({ addClass: 'btn btn-primary' })
    return false;
  }

  async addVideo(formvalue, event) {
    debugger;
    // if (formvalue["GroupId"] == '' || typeof formvalue["GroupId"] == "undefined") {
    //   alert("Please enter group");
    //   return false;
    //     }
    console.log(formvalue);
    if (event == "add") {
      // if (formvalue["RoleId"] == '' || typeof formvalue["RoleId"] == "undefined") {
      //   alert("Please enter Role");
      //   return false;
      // }
      if (formvalue["ContentType"] == '' || typeof formvalue["ContentType"]  == "undefined") {
      alert("Please enter ContentType");
      return false;
      } 
      if (formvalue["StoryName"] == '' || typeof formvalue["StoryName"] == "undefined") {
        alert("Please enter StoryName");
        return false;
      }
    
      if (formvalue["AudienceControl"] == '' || typeof formvalue["AudienceControl"] == "undefined") {
        alert("Please enter AudienceControl");
        return false;
      }
      if (formvalue["ContentType"] == 1 && this.state.editorText  == '') {
        alert("Please enter content text");
        return false;          
      }
      if (formvalue["ContentType"] == 2 && this.state.selectedFile  == null) {
        alert("Please enter content video");
        return false;          
      } 
      if (formvalue["ContentType"] == 4 && this.state.selectedFile  == null) {
        alert("Please enter content image");
        return false;          
      } 

    }
    if (Object.keys(this.state.errors).length > 0) {
      console.log('errorssssssssssssssssss', this.state.errors);
      alert("Form not Submitted");
      return false;
    }
    var newItem = document.createElement('span');
    newItem.setAttribute("class", "UploadStorySave fa fa-spin fa-spinner");
    document.getElementById('saveStoryForm').insertBefore(newItem, document.getElementById('saveStory'));

    let by = getuser().UserID;

    // Create an object of formData 
    const formData = new FormData();
    console.log(formData);
    // Update the formData object 
    if (event == "add" && this.state.editorText == '') {

      formData.append(
        "myFile",
        this.state.selectedFile,
        this.state.selectedFile.name
      );
    }
    if (event == "add") {
    //   if (Array.isArray(formvalue["RoleId"])) {
    //     var roleIds = formvalue["RoleId"].map(function (val) {
    //       if (val.value == '*')
    //         return 0;
    //       else
    //         return val.value;
    //     });
    //     var selectedroles = roleIds.join(",");
    //   } else {
         var selectedroles = 13;
    //   }
      if (Array.isArray(formvalue["GroupId"])) {
        var groupIds = formvalue["GroupId"].map(function (val) {
          if (val.value == '*')
            return 0;
          else
            return val.value;
        });
        var selectedgroups = groupIds.join(",");
      } else {
        var selectedgroups = 0;
      }
    }
    if (event == "add") {
      if (formvalue["ProductID"]) {
        formData.append('ProductId', formvalue["ProductID"]);
      } else {
        formData.append('ProductId', '');
      }
      formData.append('roleId', selectedroles);
      formData.append('groupId', selectedgroups);
    }
    if (event == "edit") {
      formData.append('StoryId', formvalue["StoryID"]);
    }else{
      formData.append('StoryId', 0);
    }
    formData.append('StartDate', formvalue["StartDate"]);
    formData.append('Endtime', formvalue["EndDate"]);
    formData.append('StoryName', formvalue["StoryName"]);
    formData.append('IsActive', formvalue["IsActive"]);
    formData.append('userId', by);
    formData.append('ContentType', formvalue["ContentType"]);
    formData.append('EnableReaction', formvalue["EnableReaction"]);
    formData.append('Story', this.state.editorText);

    
    // if(formvalue["Link"] && formvalue["ContentType"] == 4){
    // formData.append('Link', formvalue["Link"]);  
    // }else{
    //   formData.append('Link', '');   
    // }

    let resultsop = await PostUploadStoryFormData(formData, function (results) {//debugger;
      console.log(results);
      if (event == "add") {
        alert('Record Added');
      } else if (event == "edit") {
        alert('Record Updated');
      }
      this.setState({ pageId: results.data.data[0].pageId });
    }.bind(this));

    console.log(resultsop);

  }

  getExtension(filename) {
    var parts = filename.split('.');
    return parts[parts.length - 1];
  }

  isVideo(filename) {//debugger;
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case 'm4v':
      case 'avi':
      case 'mpg':
      case 'mp4':
      case 'webm':
      case 'mov':
        // etc
        return true;
    }
    return false;
  }

  isImage(filename) {
    debugger;
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case 'gif':
      case 'jpeg':
      case 'png':
      case 'jpg':
        // etc
        return true;
    }
    return false;
  }

  bytesToMegaBytes(bytes) { //debugger;
    return bytes / (1024 * 1024);
  }

  handleEditorChange = (editorText) => {
      this.setState({ editorText : editorText});

      if(editorText && editorText.match(/(\w+)/g).length > 300){
      this.setState({ errors: Object.assign({}, this.state.errors, { editor: "Text max limit is 250 words" }) })
      }else{
        this.setState({ errors: _.omit(this.state.errors, 'editor') })
      }
      debugger;
  }

  handleChange = (e, props) => {
    debugger;

    let formvalue = this.state.formvalue;
    console.log(formvalue);
    let od = this.state.od;


    if (e.target && e.target.type == "select-one" && e.target.id == "ContentType") {
      formvalue[e.target.id] = e.target.value;
      this.setState({ selectedFile: null, errors: {} });
      var el = document.getElementById('Video');
      el.value = null;


    //   if (e.target.value == 4) {
    //     document.getElementById("Link").style.display = "block";
    //     document.querySelector('label[for="Link"]').style.display = "block";
    //   } else {
    //     document.getElementById("Link").style.display = "none";
    //     document.querySelector('label[for="Link"]').style.display = "none";
    //   }
      console.log('columnsttttttt', this.formColumnList);
    } else if (e.target && e.target.id == "Link") {
      debugger;
      formvalue[e.target.id] = e.target.value;

      if (e.target.value && !this.validURL(e.target.value)) {

        this.setState({ errors: Object.assign({}, this.state.errors, { Link: "Select Valid Link Url" }) })
      } else {
        console.log('beforee', this.state.errors);
        this.setState({ errors: _.omit(this.state.errors, 'Link') });
        //setTimeout(function(){  console.log('after', this.state.errors);}.bind(this),500);   

      }

    }
    else if (e.target && e.target.type == "checkbox") {//debugger;
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e.target && e.target.type == "file") {
      debugger;
      if (e.target.files[0]) {
        var content = document.getElementById('ContentType').value;

        if (typeof content == "undefined" || content == null || content == '') {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Select Content Type First" }) })
        } else if (content == 2 && ((!this.isVideo(e.target.files[0].name)) || this.bytesToMegaBytes(e.target.files[0].size) > 60)) {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Please select a valid video file of size up to 60MB." }) })

        } else if (content == 4 && (!this.isImage(e.target.files[0].name))) {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Please select a valid image file" }) })
        }
        else {
          console.log('beforeeeeee', this.state.errors);
          this.setState({ errors: _.omit(this.state.errors, 'video') })
          console.log('after', this.state.errors);
        }
        this.setState({ selectedFile: e.target.files[0] , editorText:''});
      }
    }
    else if (e && e.type == "multiselect") {
      //this.setState({ selectedFile: e.target.files[0] }); 
      //debugger;
      if (e.selectAll == true) {
        formvalue[e.id] = 5;
      } else {
        formvalue[e.id] = e.newselectedList;
      }
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else if(e.target && e.target.type == "select-one" && e.target.id == "AudienceControl" && e.target.value == ''){
      formvalue["ProductID"] = 0
      formvalue["GroupId"] = 0
      formvalue["AudienceControl"] = ''

    }
    else {
      formvalue[e.target.id] = e.target.value;
      console.log(formvalue[e.target.id]);
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true, });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }
  productchange(e, props) {
    // setTimeout(function () {
    this.props.GetCommonspData({
      root: 'GetStoryList',
      c: "L",
      params: [{ "ProductId": e.target.value , "userId":  getuser().UserID }],
    }, function (result) {
      if (result.data && result.data.data[0]) {//debugger;
        console.log(result.data.data[0]);
        this.setState({ StoryData: result.data.data[0] });
      }
    }.bind(this));
    //}.bind(this), 1000);


  }

  componentWillUpdate(){
    debugger;
  }

  fnRenderfrmControl(col, formvalue, handleChange, event, errors, handleEditorChange) {
    // if((event == 'Edit' || event == 'Copy') && col.label == 'Enter Link Url'){
    // return;
    // }

    setTimeout(() => {

      if (formvalue && formvalue['ContentType'] == 4) {
        this.changeStyleById('Link','block')

      } else {
        this.changeStyleById('Link','none')
      }
      if (formvalue && formvalue['ContentType'] == 1) {
        this.changeStyleById('Text','block')
        this.changeStyleByClass('ck-toolbar','block')
        this.changeStyleByClass('ck-content','block')
        this.changeStyleById('Video','none')

      } else {
        this.changeStyleById('Text','none')
        this.changeStyleByClass('ck-toolbar','none')
        this.changeStyleByClass('ck-content','none')
        this.changeStyleById('Video','block')
      }

      if (formvalue && formvalue['AudienceControl'] == 2) {
        this.changeStyleById('Excel','block')
      } else {
        this.changeStyleById('Excel','none')

      }

      if (formvalue && formvalue['AudienceControl'] == 1) {
        this.changeStyleById('ProductID','block')
        this.changeStyleByClass("GroupId", 'block');      
   
      } else {
        this.changeStyleById('ProductID','none')
        this.changeStyleByClass("GroupId", 'none');   
        this.changeStyleById('label-GroupId','none')  
      }

    }, 200);

   

    return fnRenderfrmControl(col, formvalue, handleChange, event, errors, handleEditorChange)

  }

   changeStyleByClass(name, style){
    if(document.getElementsByClassName(name)){
        var aColl = document.getElementsByClassName(name);
    
        for(var i=0, len=aColl.length; i<len; i++)
        {
            aColl[i].style["display"] = style;
        }
    }
    if(document.querySelector("label[for="+name+"]")){    
    document.querySelector("label[for="+name+"]").style.display = "block";
    }
    }

   changeStyleById(name,style){
        if(document.getElementById(name)){
            document.getElementById(name).style.display = style;
        }
        if(document.querySelector("label[for="+name+"]")){    
            document.querySelector("label[for="+name+"]").style.display = style;
        }
    }

  renderViewData(ContentType, clickModalUrl, clickModalText) {
    if (ContentType == 4) {
      return <img src={clickModalUrl} />
    } else if (ContentType == 2) {
      return <video id="StoryVideo" width="450" controls>
        <source src={clickModalUrl} />
      </video>
    }else if(ContentType == 1){
      return (clickModalText ? <div dangerouslySetInnerHTML={{ __html: clickModalText }} /> : null)
    }
  }



  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, errors, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event, content } = this.state;
    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={7}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="product_dropdown">
                        <DropDown firstoption="Select Product" col={this.ProductList} onChange={this.productchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={this.state.StoryData}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} enforceFocus= {false} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmUploadStory">
                <Row>
                  {this.formColumnList.map(col => (
                    this.fnRenderfrmControl(col, formvalue, this.handleChange, event, errors, this.handleEditorChange)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <form ref="form" onSubmit={this.handleSave} id="saveStoryForm">
                    <input type="submit" id="saveStory" className='btn btn-primary btnsaveStory' value="Save Changes" />
                  </form>
                </Then>
              </If>
            </Modal.Footer>
          </Modal>

          <Modal show={this.state.clickVideo} onHide={() => this.setState({ clickVideo: false })} dialogClassName="modal-50w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                {this.renderViewData(content, this.state.clickModalUrl, this.state.clickModalText)}
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord,
    GetCommonspData
  }
)(AgentStories);