const Utility = require("../../Libs/Utility");
const sqlHelper = require("../../Libs/sqlHelper");
const { GET_UserLotteyTicket_Schema, GET_RewardsList_Schema, GET_TicketCount_Schema, POST_TicketWinnerDetail_Schema, POST_TicketWinnerDetailJag_Schema } = require("./Joi");
const cache = require('memory-cache');
const IncentivesqlHelper = require("../../LibsPBIncentive/sqlHelper");

//Code By Shailen<PERSON>
async function GetUserLotteryTickets(req, res) {
  const { error } = GET_UserLotteyTicket_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let query = `SELECT * FROM enc.Lottery_Tickets (NOLOCK) WHERE IsActive = 1 AND AssignToUserId = @UserID`;
    // console.log(query);
    let sqlparam = [];
    sqlparam.push({ key: "UserID", value: req.query.UserID });
    
    let result = await sqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetBuList(req, res) {

  try {

    let query = `SELECT LBU.Id, LBU.BUName as Display FROM enc.Lottery_BU LBU (NOLOCK) WHERE IsActive = 1 and QuizId = @QuizId AND ProductId = @ProductId`;
    
    let sqlparam = [];
    sqlparam.push({ key: "QuizId", value: req.query.QuizId });
    sqlparam.push({ key: "ProductId", value: req.query.ProductId });
    
    let result = await IncentivesqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}


async function getCurrentQuiz(req, res) {

  try {
    let query = "";
    let sqlparam = [];
    if(req.query == null){
     query = `SELECT top 1 * from enc.Lottery_QuizMaster (nolock) where IsActive = 1`;
    }
    else{
      query = `SELECT top 1 * from enc.Lottery_QuizMaster (nolock) where ID = @QuizId`;
      sqlparam.push({ key: "QuizId", value: req.query.QuizId });
    }
    
    let result = await IncentivesqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetRewardsList(req, res) {
  const { error } = GET_RewardsList_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let query = `SELECT RewardId, RewardTitle, MaxRewards, PendingRewards, BURM.IsActive FROM enc.Lottery_BU_Rewards_Mapping BURM (NOLOCK)
                    INNER JOIN enc.Lottery_Rewards RW (NOLOCK) ON RW.id = BURM.RewardId
                    WHERE BURM.IsActive = 1 AND BURM.PendingRewards > 0 AND BURM.BUId = @BUId`;
    
                    
    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: req.query.BUId });

    let result = await IncentivesqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}
async function GetRewardDetails(req, res) {

  try {

    let query = `SELECT * FROM enc.Lottery_BU_Rewards_Mapping where BUId = @BUId and RewardId= @RewardId`;
    // console.log(query);
    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: req.query.BUId });
    sqlparam.push({ key: "RewardId", value: req.query.RewardId });

    let result = await IncentivesqlHelper.sqlquery("R", query, sqlparam);
    return res.status(200).json({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    return res.status(400).json({
      status: 400,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetTicketCount(req, res) {
  const { error } = GET_TicketCount_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    //let query = `SELECT COUNT(1) TicketCount FROM enc.Lottery_Tickets where BUId = ${req.query.BUId} AND RewardId = ${req.query.RewardId}`;
    let query = `SELECT SUM(TotalTickets) TicketCount,COUNT(1) AgentCount,SUM(CASE WHEN EA.TotalTickets > 0 THEN 1 else 0 END) EligibleAgents,EA.BUId,EA.RewardId, BU.BUName, RD.RewardTitle FROM enc.Lottery_EligibleAgents EA
    JOIN enc.Lottery_BU BU ON BU.Id = EA.BUId
    JOIN enc.Lottery_Rewards RD ON RD.Id = EA.RewardId
    WHERE EA.BUId = @BUId and EA.RewardId = @RewardId and EA.IsActive = 1
    GROUP BY EA.BUId,EA.RewardId, BU.BUName, RD.RewardTitle
    `;

    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: req.query.BUId });
    sqlparam.push({ key: "RewardId", value: req.query.RewardId });


    // console.log(query);
    let result = await IncentivesqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetWinnerDetail(req, res) {
  const { error } = POST_TicketWinnerDetail_Schema.validate(req.body.params);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let param = req.body.params;

    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: param.BUId });
    sqlparam.push({ key: "RewardId", value: param.RewardId });
    sqlparam.push({ key: "TicketNumber", value: param.TicketNumber });

    let spresult = await IncentivesqlHelper.sqlProcedure("L", "[enc].[DrawLottery]", sqlparam);
    //let spresult = await sqlHelper.sqlquery("L", query, sqlparam);

    let params = [];
    let query = `Select Top(1) EmployeeId, UserName, Location from MTX.AgentProcessDetails
    where 
    IsActive = 1 and AgentId = @UserId order by IncentiveMonth desc`;

    params.push({ key: "UserId", value: spresult?.recordset[0]?.UserId });

    let result = await sqlHelper.sqlquery("L", query, params);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    await sql.close();
  }
}


async function GetWinnerDetailJag(req, res) {
  const { error } = POST_TicketWinnerDetailJag_Schema.validate(req.body?.params || {});
  if (error && error.details[0]) {
    return res.status(403).json({
      status: 403,
      message: error.details[0]?.message || "error occured",
    });
  }

  try {

    let param = req.body?.params || {};

    let sqlParam = [], sqlParamJagDetails= [];

    // let agentDetailsQuery = `SELECT TOP 1 * FROM enc.JAG_Agents 
    // WHERE EmployeeId=@EmployeeId and version=@Version and IsActive = 1`;

    // let agentDetailsQuery = `SELECT c.*, ISNULL(p.LOCATION, 'Not Found') Location
    //   FROM enc.JAG_Agents c INNER JOIN 
    //       (SELECT RANK() OVER (PARTITION BY AgentId ORDER BY Id DESC) r, *
    //               FROM mtx.AgentProcessDetails ) p
    //   ON (c.UserId = p.AgentId)
    //   WHERE p.r = 1 and c.EmployeeId=@EmployeeId and c.version= @Version and c.IsActive = 1`;

    let agentDetailsQuery = `
      SELECT c.*, 
      CASE 
          WHEN p.LOCATION IN ('Bangalore', 'Banglore', 'BNG', 'Bengaluru') THEN 'Bengaluru'
          WHEN p.LOCATION = 'Bhubaneswar' THEN 'Bhubaneswar'
          WHEN p.LOCATION IN ('Central Delhi', 'Delhi', 'Delhi/NCR', 'East Delhi', 'Faridabad', 'Ggn', 'Ggn_NRI', 'Ggn_Pension', 'Ggn_Retainer', 'Ggn_Upsell', 'Ghaziabad', 'Gurgaon', 'NCR', 'Noida') THEN 'Gurgaon'
          WHEN p.LOCATION IN ('Hyd', 'Hyderabad', 'Hyderbad', 'Hydrabad') THEN 'Hyderabad'
          WHEN p.LOCATION IN ('Kochi', 'Kochi_NRI') THEN 'Kochi'
          WHEN p.LOCATION = 'Kolkata' THEN 'Kolkata'
          WHEN p.LOCATION IN ('Nagpur', 'Navi Mumbai', 'Mumbai') THEN 'Mumbai'
          WHEN p.LOCATION = 'Ahmedabad' THEN 'Ahmedabad'
          WHEN p.LOCATION = 'Amritsar' THEN 'Amritsar'
          WHEN p.LOCATION = 'Chandigarh' THEN 'Chandigarh'
          WHEN p.LOCATION = 'Chennai' THEN 'Chennai'
          WHEN p.LOCATION = 'Indore' THEN 'Indore'
          WHEN p.LOCATION = 'Jaipur' THEN 'Jaipur'
          WHEN p.LOCATION = 'Kanpur' THEN 'Kanpur'
          WHEN p.LOCATION = 'Lucknow' THEN 'Lucknow'
          WHEN p.LOCATION = 'Patna' THEN 'Patna'
          WHEN p.LOCATION = 'Pune' THEN 'Pune'
          WHEN p.LOCATION = 'Surat' THEN 'Surat'
          WHEN p.LOCATION = 'Thane' THEN 'Thane'
          WHEN p.LOCATION = 'Vadodara' THEN 'Vadodara'
          ELSE 'Gurgaon'
      END AS Location
      FROM enc.JAG_Agents c INNER JOIN 
          (SELECT RANK() OVER (PARTITION BY AgentId ORDER BY Id DESC) r, *
                  FROM mtx.AgentProcessDetails ) p
      ON (c.UserId = p.AgentId)
      WHERE p.r = 1 and c.EmployeeId=@EmployeeId and c.version= @Version and c.IsActive = 1`;

    sqlParam.push({ key: "BUId", value: param?.BUId });
    sqlParam.push({ key: "RewardId", value: param?.RewardId });
    sqlParam.push({ key: "TicketNumber", value: param?.TicketNumber });

    let spresult = await IncentivesqlHelper.sqlProcedure("L", "[enc].[DrawLottery]", sqlParam);
    let winner =  spresult?.recordset?.length > 0 && spresult.recordset[0] || {};
    let EmployeeId = winner?.EmployeId || '';

    sqlParamJagDetails.push({ key: "EmployeeId", value: EmployeeId });
    sqlParamJagDetails.push({ key: "Version", value: param?.Version });
     
    let queryResult = await sqlHelper.sqlquery("R", agentDetailsQuery, sqlParamJagDetails);
    let jagDetails =  queryResult?.recordset?.length > 0 && queryResult.recordset[0] || {};
    let response = { ...winner,  ...jagDetails, EmployeeId };

    return res.status(200).json({
      status: 200,
      data: response
    });
  }
  catch (e) {
    console.log('catch GetWinnerDetailJagL', e);
    return res.status(400).json({
      status: 400,
      message: e.toString()
    });
  }
  finally {
    //await sql.close();
  }
}


async function GetWinners(req, res) {

  try {

    let query = `SELECT RANK() OVER (order by updatedOn) winno, TicketNumber,IsWinner, AssignToUserId FROM enc.Lottery_Tickets (nolock) 
    WHERE BUId = @BUId and RewardId = @RewardId
    and IsWinner=1
    `;

    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: req.query.BUId });
    sqlparam.push({ key: "RewardId", value: req.query.RewardId });

    let spresult = await IncentivesqlHelper.sqlquery("L", query, sqlparam);

    let userId = spresult?.recordsets[0];

    let userQuery = `Select Top(1) EmployeeId, UserName, Location from MTX.AgentProcessDetails
    where 
    IsActive = 1 and AgentId = @UserId order by IncentiveMonth desc`;

    winnersUsersList = [];
    for(let i= 0 ; i < userId.length; i++){
      // winnersUsersList.push(userId[i].AssignToUserId);
      let params = [];
      params.push({ key: "UserId", value: userId[i].AssignToUserId });
  
      let result = await sqlHelper.sqlquery("L", userQuery, params);
    
      let res = {...result.recordsets[0][0], winno : userId[i].winno, TicketNumber : userId[i].TicketNumber }
      winnersUsersList.push(res);
    }

    // console.log("Query Result: ", winnersUsersList);
    res.send({
      status: 200,
      data: winnersUsersList
    });

    // let query = `SELECT RANK() OVER (order by t.updatedOn) winno, t.TicketNumber,IsWinner,ud.EmployeeId,ud.UserName FROM enc.Lottery_Tickets (nolock) t join crm.userdetails ud (nolock) on t.assigntouserid = ud.userid
    // WHERE t.BUId = @BUId and t.RewardId = @RewardId
    // and IsWinner=1
    // `;

    // let sqlparam = [];
    // sqlparam.push({ key: "BUId", value: req.query.BUId });
    // sqlparam.push({ key: "RewardId", value: req.query.RewardId });

    // let spresult = await sqlHelper.sqlquery("L", query, sqlparam);

    // console.log("Query Result: ", spresult);
    // res.send({
    //   status: 200,
    //   data: spresult.recordsets
    // });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    await sql.close();
  }
}

async function GetTickets(req, res) {


  try {

    //let query = `SELECT COUNT(1) TicketCount FROM enc.Lottery_Tickets where BUId = ${req.query.BUId} AND RewardId = ${req.query.RewardId}`;
    let query = `Select * from enc.Lottery_Tickets 
    where IsActive = 1 
    and BUId = @BUId and RewardId = @RewardId
    order by RandomNo desc`;

    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: req.query.BUId });
    sqlparam.push({ key: "RewardId", value: req.query.RewardId });


    // console.log(query);
    let result = await IncentivesqlHelper.sqlquery("L", query, sqlparam);
    // console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetLottery(req, res) {

  try {
    
    // console.log("GetLottery Start  ");
    // console.log(new Date());
    //console.log(req.query);
    var endpoint = "LotteryContestAgents";
    var c = "M";
    //var query = JSON.parse(req.query.con);
    var userid = req.query.userid;
    var query = { Userid: req.query.userid, IsActive: 1 };
    var db = commondb;
    // if (c === "M") {
      db = matrixdb;
    // }

    let cachedGetLottery = cache.get('GetLottery_'+userid);
    if (cachedGetLottery) {
      res.send({
        status: 200,
        data: cachedGetLottery,
        message: "Success"
      });
      return cachedGetLottery;
    } 


    let data = await db.collection(endpoint)
      .find(query)
      .sort({ QuizID: -1 })
      .toArray();

    for (let i = 0; i < data.length; i++) {
      var QuizId = data[i].QuizID;

      let Quizquery = `SELECT top 1 * from enc.Lottery_QuizMaster (nolock) where id = @QuizId`;
     
      let Quizquerysqlparam = [];
      Quizquerysqlparam.push({ key: "QuizId", value: QuizId });
      let Quizqueryresult = await IncentivesqlHelper.sqlquery("R", Quizquery, Quizquerysqlparam);
      if (Quizqueryresult && Quizqueryresult.recordset.length > 0) {
        data[i]["QuizMaster"] = Quizqueryresult.recordset[0];
      }

      let query = `SELECT     *
                   FROM		    ENC.Lottery_Tickets LT (NOLOCK)
                   INNER JOIN	ENC.Lottery_BU BU (NOLOCK) ON LT.BUId = BU.id
                   WHERE		  LT.IsActive = 1 AND LT.QuizId = @QuizId AND AssignToUserId = @UserId AND bu.ShowTickets = 1
                   ORDER BY   LT.Id;`;
      let sqlparam = [];
      sqlparam.push({ key: "QuizId", value: QuizId });
      sqlparam.push({ key: "UserId", value: req.query.userid });

      // console.log(query);
      let result = await IncentivesqlHelper.sqlquery("R", query, sqlparam);
      if (result && result.recordset.length > 0) {
        data[i]["TicketNumbers"] = result.recordset;
      }
      //arr.push(result);
    }

    cache.put('GetLottery_'+userid, data, (5 * 60 * 1000));

    // console.log("GetLottery END  ");
    // console.log(new Date());

    res.send({
      status: 200,
      data: data,
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {
    //await sql.close();
  }
}

module.exports = {
  GetUserLotteryTickets: GetUserLotteryTickets,
  GetBuList: GetBuList,
  GetRewardsList: GetRewardsList,
  GetTicketCount: GetTicketCount,
  GetWinnerDetail: GetWinnerDetail,
  GetWinnerDetailJag: GetWinnerDetailJag,
  GetWinners: GetWinners,
  GetRewardDetails: GetRewardDetails,
  GetTickets: GetTickets,
  getCurrentQuiz,
  GetLottery: GetLottery
};