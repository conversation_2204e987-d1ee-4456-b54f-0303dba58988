
// import React from "react";
// import {
//   GetCommonData, GetCommonspData, GetDataDirect
// } from "../store/actions/CommonAction";
// import { connect } from "react-redux";
// import { OpenSalesView, getUrlParameter, getuser } from '../utility/utility.jsx';
// import DataTable from './Common/DataTableWithFilter';
// import DropDown from './Common/DropDownList';
// import ManagerHierarchy from './Common/ManagerHierarchy';
// import Moment from 'react-moment';
// import moment from 'moment';
// import { If, Then, Else } from 'react-if';
// import AlertBox from './Common/AlertBox';
// import { ToastContainer, toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';

// import Datetime from 'react-datetime';
// import 'react-datetime/css/react-datetime.css'

// import { Chart } from "react-google-charts";
// import _ from 'underscore';

// // reactstrap components
// import {
//   Card,
//   CardHeader,
//   CardBody,
//   CardTitle,
//   Table,
//   Row,
//   Col
// } from "reactstrap";
// import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
// import { func } from "prop-types";

// class IVRActivityChart extends React.Component {
//   constructor(props) {
//     super(props);
//     this.state = {
//       PageTitle: "IVR Activity Chart",
//       IVRActivityChart: [],
//       ProductId: 117,
//       startdate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
//       enddate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
//       maxdate: moment().subtract(20, 'days').format("YYYY-MM-DD"),
//       ReportTime: null,
//       SelectedSupervisors: [],
//       ChartData: [],
//       charttype: 'ColumnChart',

//     };
//     this.productchange = this.productchange.bind(this);
//     this.chartchange = this.chartchange.bind(this);
//     this.handleShow = this.handleShow.bind(this);
//     this.columnlist = [
//       {
//         "name": "utcallid",
//         "selector": "utcallid"
//       },
//       {
//         "name": "calltime",
//         "selector": "calltime"
//       },
//       {
//         "name": "ProductName",
//         "selector": "ProductName"
//       },
//       {
//         "name": "leadid",
//         "selector": "leadid"
//       },
//       {
//         "name": "isAlready WhatsAppOptIn",
//         "selector": "isAlreadyWhatsAppOptIn"
//       },
//       {
//         "name": "isWhatsApp Opted",
//         "selector": "isWhatsAppOpted"
//       },
//       {
//         "name": "OpenTicket Exist",
//         "selector": "OpenTicketExist"
//       },
//       {
//         "name": "Ticket TatBusted",
//         "selector": "TicketTatBusted"
//       },
//       {
//         "name": "ticketStatus CustInput",
//         "selector": "ticketStatusCustInput"
//       },
//       {
//         "name": "Booking > 30Days",
//         "selector": "BookingGreaterThen30Days"
//       },
//       {
//         "name": "isPolicyIssued",
//         "selector": "isPolicyIssued"
//       },
//       {
//         "name": "ivrOption",
//         "selector": "ivrOption"
//       },
//       {
//         "name": "softCopyOption",
//         "selector": "softCopyOption"
//       },
//       {
//         "name": "isSatisify With IvrOption",
//         "selector": "isSatisifyWithIvrOption"
//       },
//       {
//         "name": "playnstp",
//         "selector": "playnstp"
//       },
//       {
//         "name": "playnstpid",
//         "selector": "playnstpid"
//       },
//       {
//         "name": "playnstpOption",
//         "selector": "playnstpOption"
//       },
//       {
//         "name": "AssignedAgent",
//         "selector": "AssignedAgent"
//       },
//       {
//         "name": "Assigned Agent CallStatus",
//         "selector": "AssignedAgentCallStatus"
//       },
//       {
//         "name": "newcallid",
//         "selector": "newcallid"
//       },
//       {
//         "name": "language",
//         "selector": "language"
//       },
//       {
//         "name": "QName",
//         "selector": "QName"
//       },
//       {
//         "name": "Qentertime",
//         "selector": "Qentertime"
//       },
//       {
//         "name": "answertime",
//         "selector": "answertime"
//       },
//       {
//         "name": "hanguptime",
//         "selector": "hanguptime"
//       },
//       {
//         "name": "GroupCallStatus",
//         "selector": "GroupCallStatus"
//       },
//       {
//         "name": "callendreason",
//         "selector": "callendreason"
//       },
//       {
//         "name": "BookingNo",
//         "selector": "BookingNo"
//       },
//       {
//         "name": "PolicyNo",
//         "selector": "PolicyNo"
//       },
//       {
//         "name": "SupplierName",
//         "selector": "SupplierName"
//       },
//       {
//         "name": "policyType",
//         "selector": "policyType"
//       },
//       {
//         "name": "booking CreatedDate",
//         "selector": "bookingCreatedDate"
//       },
//       {
//         "name": "booking StampingDate",
//         "selector": "bookingStampingDate"
//       },
//       {
//         "name": "Registration Number",
//         "selector": "registrationNumber"
//       },
//       {
//         "name": "isWhatsApp OptIn",
//         "selector": "isWhatsAppOptIn"
//       },
//       {
//         "name": "isStp",
//         "selector": "isStp"
//       },
//       {
//         "name": "isSoftCopy Available",
//         "selector": "isSoftCopyAvailable"
//       }
//     ]

//     this.ProductList = {
//       config:
//       {
//         root: "Products",
//         cols: ['id AS Id', 'ProductName AS Display'],
//         con: [{ "ISActive": true }]
//       }
//     }
//     this.chartList = {
//       config:
//       {
//         root: "chartList",
//         data: [
//           { Id: 'BarChart', Display: "BarChart" },
//           { Id: 'ColumnChart', Display: "ColumnChart" },
//           { Id: 'AreaChart', Display: "AreaChart" },
//           { Id: 'LineChart', Display: "LineChart" },
//           { Id: 'Bar', Display: "Bar" },
//           { Id: 'PieChart', Display: "PieChart" }
//         ],
//       }
//     }

//     //"AnnotationChart" | "AreaChart" | "BarChart" | "BubbleChart" | "Calendar" | "CandlestickChart" | "ColumnChart" | "ComboChart" | "DiffChart" | "DonutChart" | "Gantt" | "Gauge" | "GeoChart" | "Histogram" | "LineChart" | "Line" | "Bar" | "Map" | "OrgChart" 
//     //| "PieChart" | "Sankey" | "ScatterChart" | "SteppedAreaChart" | "Table" | "Timeline" | "TreeMap" | "WaterfallChart" | "WordTree"
//   }


//   componentWillMount() {
//     setTimeout(function () {
//       this.fetchCallBackData();
//     }.bind(this), 500);
//   }

//   componentWillReceiveProps(nextProps) {
//     if (!nextProps.CommonData.isError) {
//       this.setState({ IVRActivityChart: nextProps.CommonData["IVRActivityReport"] });

//       this.BindChartData(nextProps.CommonData["IVRActivityReport"]);
//     }
//   }
//   handleShow(e) {

//     this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
//     setTimeout(function () {
//       this.fetchCallBackData();
//     }.bind(this), 500);

//   }



//   fetchCallBackData() {
//     var SelectedSupervisors = this.state.SelectedSupervisors;
//     this.props.GetCommonData({
//       root: "IVRActivityReport",
//       ProductId: this.state.ProductId,
//       startdate: this.state.startdate,
//       enddate: this.state.enddate,
//     });



//   }
//   productchange(e, props) {
//     this.setState({
//       ProductId: e.target.value
//     }, function () {
//       this.fetchCallBackData();
//     });


//   }
//   chartchange(e, props) {
//     this.setState({
//       charttype: e.target.value
//     });


//   }
//   handleStartDateChange = (e, props) => {

//     if (e._isAMomentObject) {
//       this.setState({ startdate: e.format("YYYY-MM-DD") }, function () {
//         //this.fetchCallBackData();
//       });
//     }


//   }
//   handleEndDateChange = (e, props) => {

//     if (e._isAMomentObject) {
//       this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
//         //this.fetchCallBackData();
//       });
//     }


//   }

//   BindChartData(ReportData) {

//     if (ReportData) {

//       let data = [];
//       let TransferToAgentData = [];
//       let FinalData = [];
//       let TicketData = [];
//       var ProductName = _.groupBy(ReportData, 'ProductName');
//       //data.push(["Product Name", "Total", "Open Tickets", "TAT Busted", "WithIn TAT", "Transfer to Agent"]);
//       //data.push(["Product Name", "Total", "Agent Answer", { label: "Auto", type: "number", role: 'annotation' }]);



//       Object.keys(ProductName).forEach(function (key) {

//         data.push([key, "Total",
//           { role: "style" },
//           { role: 'annotation' }]);
//         TransferToAgentData.push([key, "Total",
//           { role: "style" },
//           { role: 'annotation' }]);
//         FinalData.push([key, "Total",
//           { role: "style" },
//           { role: 'annotation' }]);
//         TicketData.push([key, "Total",
//           { role: "style" },
//           { role: 'annotation' }]);

//         //Ticket Data
//         let OpenTicketExist = _.countBy(ProductName[key], function (item) {
//           return item.OpenTicketExist == 'yes';
//         })
//         TicketData.push(["Open Ticket", OpenTicketExist.true, "#3f51b5", OpenTicketExist.true]);

//         let TicketTatBusted = _.countBy(ProductName[key], function (item) {
//           return item.OpenTicketExist == 'yes' && item.TicketTatBusted == 'yes';
//         })
//         TicketData.push(["Tat Busted", TicketTatBusted.true, "#fb8c00", TicketTatBusted.true]);

//         let WithInTAT = _.countBy(ProductName[key], function (item) {
//           return item.OpenTicketExist == 'yes' && item.TicketTatBusted == 'no';
//         })
//         TicketData.push(["WithInTAT", WithInTAT.true, "#757575", WithInTAT.true]);

//         let toagent = _.countBy(ProductName[key], function (item) {
//           return item.OpenTicketExist == 'yes' && item.TicketTatBusted == 'no' && item.ticketStatusCustInput == 'toagent';
//         })
//         TicketData.push(["toagent", toagent.true, "#ffeb3b", toagent.true]);


//         //data.push([key, ProductName[key].length, OpenTicketExist.true, TicketTatBusted.true, WithInTAT.true, toagent.true]);

//         data.push(["Total Calls", ProductName[key].length, "#3f51b5", ProductName[key].length]);

//         let TransferToAgent = _.countBy(ProductName[key], function (item) {
//           return item.QName != '';
//         });
//         data.push(["Transfer To Agent", TransferToAgent.true, "#fb8c00", TransferToAgent.true]);
//         TransferToAgentData.push(["Transfer To Agent", TransferToAgent.true, "#3f51b5", TransferToAgent.true]);

//         let Auto = _.countBy(ProductName[key], function (item) {
//           return item.QName == '';
//         })
//         data.push(["Auto", Auto.true, "#757575", Auto.true]);

//         let Answered = _.countBy(ProductName[key], function (item) {
//           return item.QName != '' && item.AssignedAgentCallStatus == "ANSWER";
//         });
//         TransferToAgentData.push(["Answered", Answered.true, "#fb8c00", Answered.true]);

//         let ABANDON = _.countBy(ProductName[key], function (item) {
//           return item.QName != '' && item.AssignedAgentCallStatus == "ABANDON";
//         })
//         TransferToAgentData.push(["ABANDON", ABANDON.true, "#ffeb3b", ABANDON.true]);

//         var ivrOption = _.groupBy(ProductName[key], 'ivrOption');
//         Object.keys(ivrOption).forEach(function (key1) {
//           if (key1 == 'noinput' || key1 == '' || key1 == 'null' || key1 == null) {

//           }
//           else {
//             FinalData.push([key1, ivrOption[key1].length, "#ffeb3b", ivrOption[key1].length]);
//           }
//         });





//         //data.push([key, ProductName[key].length, Answered.true, Auto.true]);
//       });
//       this.setState({ ChartData: data, TransferToAgent: TransferToAgentData, FinalData: FinalData, TicketData: TicketData });
//       // ReportData.forEach(item => {

//       // });

//     }
//   }
//   CreateChart(ChartData, title) {
//     let lineChart = null;
//     if (ChartData && ChartData.length > 0) {

//       lineChart = (

//         <Chart
//           chartType={this.state.charttype}
//           data={ChartData}
//           loader={<div>Loading Chart</div>}
//           width="100%"
//           height="200px"
//           options={{
//             title: title,
//             is3D: true,
//             isStacked: true,
//             chartArea: { width: '100%' },
//             hAxis: {
//               title: ChartData[0][0],
//               minValue: 0,
//             },
//             vAxis: {
//               title: 'Total',
//               minValue: 0,
//             },
//             bar: { groupWidth: '50%' },
//             legend: { position: 'none' },
//             animation: {
//               startup: true,
//               easing: 'linear',
//               duration: 1000,
//             }
//           }}
//           legendToggle
//         />

//       )
//       return lineChart;
//     }
//   }

//   validation = (currentDate) => {
//     return !currentDate.isBefore(moment(this.state.maxdate));
//   };

//   validationEndDate = (currentDate) => {
//     return !currentDate.isBefore(moment(this.state.startdate));
//   };

//   render() {
//     const columns = this.columnlist;
//     const { items, PageTitle, IVRActivityChart, showAlert, AlertMsg, AlertVarient, ReportTime } = this.state;
//     console.log(this.state);
//     let selectedLeads = [];

//     let AutoAndAnswerChart = this.CreateChart(this.state.ChartData, 'Calls: Auto Answered');
//     let TransferToAgent = this.CreateChart(this.state.TransferToAgent, 'Abandon Report');
//     let finalChart = this.CreateChart(this.state.FinalData, 'IVR Options Report');
//     let TicketData = this.CreateChart(this.state.TicketData, 'Ticket Report');

//     return (
//       <>
//         <div className="content">
//           <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
//           <ToastContainer />
//           <Row>
//             <Col md="12">
//               <Card>
//                 <CardHeader>
//                   <Row>
//                     <Col md={3}>
//                       <CardTitle tag="h4">{PageTitle}</CardTitle>
//                     </Col>
//                     <Col md={2}>
//                       <DropDown
//                         value={this.state.charttype}
//                         col={this.chartList}
//                         onChange={this.chartchange}>
//                       </DropDown>
//                     </Col>
//                     <Col md={2}>
//                       <Form.Control as="select" value={this.state.ProductId} onChange={this.productchange}>
//                         <option value="117">NewCar</option>
//                         <option value="114">Twowheeler</option>
//                       </Form.Control>
//                     </Col>
//                     <Col md={2}>
//                       <Datetime value={new Date()}
//                         dateFormat="YYYY-MM-DD"
//                         value={this.state.startdate}
//                         isValidDate={this.validation}
//                         onChange={moment => this.handleStartDateChange(moment)}
//                         utc={true}
//                         timeFormat={false}
//                       />
//                     </Col>
//                     <Col md={2}>
//                       <Datetime value={new Date()}
//                         dateFormat="YYYY-MM-DD"
//                         value={this.state.enddate}
//                         isValidDate={this.validationEndDate}
//                         onChange={moment => this.handleEndDateChange(moment)}
//                         utc={true}
//                         timeFormat={false}
//                       />
//                     </Col>
//                     <Col md={1}>
//                       <Button variant="primary" onClick={() => this.fetchCallBackData()}>Fetch</Button>
//                     </Col>

//                   </Row>

//                 </CardHeader>
//                 <CardBody>
//                   <Row>
//                     <Col md="3">
//                       {AutoAndAnswerChart}
//                     </Col>
//                     <Col md="3">
//                       {TransferToAgent}
//                     </Col>
//                     <Col md="3">
//                       {finalChart}
//                     </Col>
//                     <Col md="3">
//                       {TicketData}
//                     </Col>
//                   </Row>

//                   <DataTable
//                     columns={columns}
//                     data={(IVRActivityChart && IVRActivityChart.length > 0) ? IVRActivityChart : []}


//                   />
//                 </CardBody>
//               </Card>
//             </Col>
//           </Row>




//         </div>
//       </>
//     );
//   }
// }


// function mapStateToProps(state) {
//   return {
//     CommonData: state.CommonData
//   };
// }

// export default connect(
//   mapStateToProps,
//   {
//     GetCommonData,
//     GetCommonspData
//   }
// )(IVRActivityChart);