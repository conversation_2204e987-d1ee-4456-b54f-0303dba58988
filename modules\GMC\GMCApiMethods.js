const { ObjectId } = require('mongodb');
const cache = require('memory-cache');
const sqlHelper = require("../../Libs/sqlHelper");

async function GetUserProfile(userId) {
  try {
   
    let sqlparam = [];
    sqlparam.push({ key: "UserId", value: userId });
    let result = await sqlHelper.sqlProcedure("R", "[MDB].[GetUserProfile]", sqlparam);

    return result;

  }
  catch (e) {
    console.log(e);
    return null;

  }
  finally {

  }
}



async function getGMCData(EMPLOYEE_ID) {
  try {


    let data = await matrixdashboarddb.collection("PB_GMC").find({ "EMPLOYEE_ID": EMPLOYEE_ID }).toArray();

    return data


  } catch (err) {
    console.log("getGPAData", err)
    return null;
  }
  finally {
  }
}


async function getGPAData(EMPLOYEE_ID) {
  try {


    let data = await matrixdashboarddb.collection("PB_GPA").find({ "EMPLOYEE_ID": EMPLOYEE_ID }).toArray();

    return data


  } catch (err) {
    console.log("getGPAData", err)
    return null;
  }
  finally {
  }
}


async function getUserInfo(req, res) {
  try {
    // console.log("Request is", req.user)
    //throw new error(ee);
    let MatrixDashboardDb = matrixdashboarddb;
   
      if (req.user) {
       
      let EMPLOYEE_ID = req.user.employeeId; 
      let userId = req.user.userId; 

      let cachedGetProfile = cache.get('GetProfile_' + EMPLOYEE_ID);
      if (cachedGetProfile) {
        res.send({
          status: 200,
          data: cachedGetProfile,
          message: "Success"
        });
        return cachedGetProfile;
      }

      let UserProfile = await GetUserProfile(userId);
      let GMCData = await getGMCData(EMPLOYEE_ID);
      let GPAData = await getGPAData(EMPLOYEE_ID);

      let Profile = {
        UserProfile,
        GMCData,
        GPAData
      }


      // console.log("data is", Profile);

      cache.put('GetProfile_' + EMPLOYEE_ID, Profile, (6 * 60 * 60 * 1000));

      res.send({
        status: 200,
        data: Profile,
        message: "Success"
      });

    }
    else {
      res.send({
        status: 404,
        message: "UserNotFound"
      });
    }

  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {

  }
}

module.exports = {
  getUserInfo: getUserInfo
}
