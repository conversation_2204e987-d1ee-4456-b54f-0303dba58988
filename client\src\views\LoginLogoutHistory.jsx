
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, GetFileExists, GetAwsRecordingUrl, GetRecordingName, PostLoginLogoutHistory
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';

import Moment from 'react-moment';
import moment from 'moment';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


import { CompareJson, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class LoginLogoutHistory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "LoginLogoutHistory",
      PageTitle: "Login Logout History",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      empid: '',
      CallDate: moment().format("YYYY-MM-DD"),
      maxdate: moment().subtract(59, 'days').format("YYYY-MM-DD"),
      ResponseData :[],
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);

    this.empchange = this.empchange.bind(this);
    this.selectedrow = { "CallDataId": 0, "CallID": null, "LeadID": null }
    this.columnlist = [
      {
        selector: "employeeId",
        name: "EmployeeId",
        type: "string",
      },
      {
        selector: "username",
        name: "UserName",
        type: "string",
      },
      {
        selector: "LoginTime",
        name: "LoginTime",
        type: "datetime",
        cell: row => <div className="LoginTime">{row.LoginTime ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss"
                >{row.LoginTime}</Moment> : "N.A"}</div>,
      },
      {
        selector: "LogoutTime",
        name: "LogoutTime",
        type: "datetime",
        cell: row => <div className="LogoutTime">{row.LogoutTime ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss"
                >{row.LogoutTime}</Moment> : "N.A"}</div>,
      }

    ];
    let count = 0;
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }
  }

  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    // columns.push({
    //   name: "Action",
    //   cell: row => <ButtonGroup aria-label="Basic example">
    //     <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
    //     <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
    //   </ButtonGroup>
    // });
    return columns;
  }
  handleCopy(row) {
    this.setState({ formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }


  empchange(e, props) {
    this.setState({ empid: e.target.value });
  }

  CallDateChange(e, props) {
    if (e._isAMomentObject) {
      this.setState({ CallDate: e.format("YYYY-MM-DD") }, function () {
      });
    }
  }
  fetchData() {     

    if (!this.state.empid) {
      toast("Please enter Employee Id", { type: 'error' });
      return;
    }
    if (!this.state.CallDate) {
      toast("Please select Date", { type: 'error' });
      return;
    }
    var json = {
       "agentid" : this.state.empid,
       "departmentname": "Health",
       "searchdate": this.state.CallDate,
       };

    PostLoginLogoutHistory(json, function (results) {
        console.log(results);
        this.setState({ ResponseData: results.data.data, items:[] }, () =>this.AgentChatLoginData()
        );
    }.bind(this));
  }

  AgentChatLoginData(){
      var data = this.state.ResponseData;
      var res = [];
      data.map((val1,index) => {
        var history = val1.history;
        history.map((val2,index) => {
            var obj = {
                employeeId: val1.employeeId,
                username:   val1.username,
                LoginTime: val2.loginTime,
                LogoutTime: val2.logoutTime
            };

            res.push(obj);
        });
      });

    console.log(res);
    this.setState({ items: res });

  }

  validationdate = (currentDate) => {
    return !currentDate.isBefore(moment(this.state.maxdate));
  };

  render() {
    const columns = this.columnlist;
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event } = this.state;
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    {/* <Col md={4}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col> */}
                    <Col md={2}>
                      <Form.Control type="text" onChange={this.empchange} placeholder={"Enter EmployeeId"} />
                    </Col>                
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.CallDate}
                        isValidDate={this.validationdate}
                        onChange={moment => this.CallDateChange(moment)}
                        utc={true}
                        timeFormat={false} />
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={() => this.fetchData()}>Fetch</Button>
                    </Col>
                    <Col md={1}>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
          <audio src="" id={"audio1"}></audio>
          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>

            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
  }
)(LoginLogoutHistory);