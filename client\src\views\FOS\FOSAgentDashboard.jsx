import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetCommonData, InsertData, UpdateData, GetCommonspData
} from "../../store/actions/CommonAction";
import {
    addRecord
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from '../Common/DataTableWithFilter';
import { OpenNewSalesView, LeadView,GetJsonToArray, getUrlParameter, getuser } from '../../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import DropDown from "../Common/DropDown";
import DateRange from "../Common/DateRange"
import moment from 'moment';
import ManagerHierarchyv2 from "views/Common/ManagerHierarchy_v2";
import ManagerHierarchy from "views/Common/ManagerHierarchy";
import { FosAgentDashboard } from "variables/constants";
import { HIERARCHY_MESSAGE_NOTE } from "views/MyBookings/Utility";
import { MY_BOOKINGS } from "variables/constants";
import Loader from '../Common/Loader';


const FOSAgentDashboard = (props) => {

    const [items, setitems] = useState([])
    const user = getuser();   
    const userId=user.UserID
    const RoleId = user.RoleId;
    const [root] = useState(["GetFosAllocationPanel"])
    const [PageTitle] = useState((RoleId === 13)?["FOS Agent Dashboard "]:["FOS Supervisor Dashboard "])
    const [Filter, setFilter] = useState(1);
    // const [startDate, setstartDate] = useState(moment().format("YYYY-MM-DD"));
    // const [endDate, setendDate] = useState(moment().add(15, 'days').format("YYYY-MM-DD 23:59:59"));
    const [startDate, setstartDate] = useState(moment().subtract(15, 'days').format("YYYY-MM-DD"));
    const [endDate, setendDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));
    const [FOSAgentDetails, setFOSAgentDetails] = useState([]);
    const [AgentList, setAgentList] = useState(0);
    const [ManagerIds, setManagerIds] = useState('');
    const [refresh, setRefresh]= useState(false);
    const [clickSearch, setClickSearch]= useState(false);

    let dtRef = React.createRef();
    const Cell = ({ v }) => (
        <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );
    const columnlist = [

        {

            name: 'Lead Id',
            selector: 'Leadid',
            type: "string",
            cell: row => 
                <a href={LeadView(row.CustomerID, row.Leadid, row.ProductID,userId)} target="_blank">{row.Leadid} </a>
            ,
            editable: false,
            sortable: true,
            width: '120px',
            //searchable: true
        },
        {
            name: 'Appointment Id',
            selector: 'AppointmentID',
           type: "string",
           editable: false,
            sortable: true,
            width: '120px',
        },

        {

            name: 'Customer Name',
            selector: 'Customername',
            type: "string",
            editable: false,
            sortable: true,
            width: '120px',
            //searchable: true
            hide: true
        },
        {

            name: 'City',
            selector: 'City',
            type: "string",
            cell: row => <div>{row.City ? <Cell v={row.City} /> : 'N.A'}</div>,
            editable: false,
            sortable: true,
            width: '120px'
        },
        {

            name: 'Location Captured',
            selector: 'LocationCaptured',
            type: "string",
            cell: row => <div>{row.LocationCaptured == 1 ? "Yes" : "No"}</div>,
            editable: false,
            sortable: true,
            width: '80px'
        },
        {

            // name: 'Last Appointment Date',
            name: 'Appointment Date',
            selector: 'AppointmentDateTime',
            type: "datetime",
            cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            format: 'YYYY-MM-DD HH:mm:ss',
            //searchable: true
        },

        {
            name: 'Assignment Type',
            selector: 'AssignmentType',
            type: "string",
            width: '150px',
            //sortable: true,
            //searchable: true
        },

        {

            // name: 'Last Appointment Status',
            name: 'Appointment SubStatus',
            selector: 'AppointmentSubStatus',
            type: "string",
            width: '180px',
            //sortable: true,
            //searchable: true
        },
       

        {

            name: 'Created By User',
            selector: 'CreatedBy',
            type: "string",
            width: '200px',
            //sortable: true,
            searchable: true
        },

        {

            name: 'Appointment CreatedOn',
            selector: 'Appointment_Created_on',
            type: "datetime",
            cell: row => <div>{row.Appointment_Created_on ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.Appointment_Created_on}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            format: 'YYYY-MM-DD HH:mm:ss',
            //searchable: true
        },

       {
            name: 'Appointment UpdatedOn',
            selector: 'UpdatedOn',
            type: "datetime",
            cell: row => <div>{row.UpdatedOn ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.UpdatedOn}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            format: 'YYYY-MM-DD HH:mm:ss',
            //searchable: true
        },

        
        {

            // name: 'Last Assigned To',
            name: 'Assigned To User',
            selector: 'AssignedTo',
            //cell: row => <div>{row.LastAssignedTo ? row.LastAssignedTo + "/" + row.UserName : ""}</div>,
            sortable: true,
            type: "string",
            sortable: false,
            width: '200px',
            searchable: true,
        },
        {

            // name: 'Last Assigned Date',
            name: 'Lead Assigned Date',
            selector: 'AssignedDate',
            type: "datetime",
            cell: row => <div>{row.AssignedDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AssignedDate}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            format: 'YYYY-MM-DD HH:mm:ss',
            //searchable: true
        },
        {

            name: 'Is Booked',
            selector: 'IsBooked',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },
        
    ];


    const FilterChange = (e) => {
        setFilter(e.target.value);
    }


    useEffect(() => {
       if (!props.CommonData.isError) {         
        if((RoleId !== 13)){     
            setManagerIds(user.UserID);  // 45650      
            setAgentList(user.UserID);
  
            //setManagerIds(45650);    
        }else{
            //setAgentList(user.UserID);
            setAgentList(user.UserID);
        }
      }     

    }, [])

    useEffect(() => {
        if (AgentList || ManagerIds) {
          setClickSearch(true);  
          GetData();
        }
    }, [AgentList,ManagerIds,Filter, startDate, endDate]);

    const GetData = () => {

        if (user) {
            if(Filter== 1){
                //FosData(user.UserID, 0, startDate, endDate)
                FosData(AgentList, 0, startDate, endDate, ManagerIds)
            }
            else if(Filter== 2){
                //FosData(0, user.UserID, startDate, endDate)
                FosData(0, AgentList, startDate, endDate, ManagerIds)
            }
            // else{
            //     //FosData(user.UserID, user.UserID, startDate, endDate)
            //     FosData(AgentList, AgentList , startDate, endDate)
            // }
            
        }

    }

    const FosData = (CreatedBy, AssignedToUserID, startDate, endDate, ManagerIds) =>{
        props.GetCommonspData({
            root: 'GetAgentDashboardDetails',
            c: "R",
            params: [{ CreatedBy: CreatedBy }, { AssignedToUserID: AssignedToUserID }, { AppointmentCreatedFrom: startDate }, 
                { AppointmentCreatedTo: endDate }, {ManagerId: ManagerIds}],

        }, function (data) {
            setClickSearch(false);
            if (data && data.data && data.data.data) {
                let item = data.data.data[0]
                setFOSAgentDetails(item);
            }
        })
    }


    const handleStartDate = (StartDateValue) => {
        setstartDate(StartDateValue);
        const date = new Date()
        const startDate= new Date(StartDateValue)

        startDate.setDate(startDate.getDate()+15); 
        if(date-startDate<=0){
            setendDate(moment().format("YYYY-MM-DD 23:59:59"))
        }
        else{
            setendDate(moment(StartDateValue).add(15, 'days').format("YYYY-MM-DD 23:59:59"))
        }
    }
    
    const handleEndDate = (EndDateValue) => {
        setendDate (EndDateValue);
    }

    const handleShow = (e) => {
        if(RoleId === 2){
          const Managers = e.SelectedSupervisors?.join(',') || '';
          setManagerIds(Managers);
        }
        else if(e.FetchedViaClick) {
          const Managers = e.SelectedSupervisors?.join(',') || '';
          setManagerIds(Managers);
        }
    }


    return (
        <>

            <div className="content fosAgentDashboard footerSection">
                <ToastContainer />
                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                        {/* <p style={{marginLeft:'354px', marginBottom:'3px', color: 'red'}}><i>*</i> Date range can be selected from last 3 months only</p>

                                        <p style={{marginLeft:'363px',marginBottom:'5px'}}> Appointment Created Date</p> */}
                                    </Col>
                                 
                                    {RoleId && (RoleId !== 13) && (RoleId !== 2) &&
                                    <Col md={3} className="text-center mt-4">
                                        <ManagerHierarchyv2 
                                        handleShow={handleShow}
                                        value={/UserID/g}
                                        module={MY_BOOKINGS}
                                        message={HIERARCHY_MESSAGE_NOTE}
                                        />
                                    </Col>
                                    }
                                    {RoleId && (RoleId === 2) &&
                                    <Col md={3} className="text-center mt-4">
                                        <ManagerHierarchy 
                                        handleShow={handleShow}
                                        value={/UserID/g}
                                        module={MY_BOOKINGS}
                                        message={HIERARCHY_MESSAGE_NOTE}
                                        />
                                    </Col>
                                    }
                                </Row>
                            </CardHeader>
                            <CardBody>
                           
                                <Row>
                                    <Col md={4}>
                                        <Form.Label><i>*</i> Filter </Form.Label>
                                        <DropDown firstoption={(RoleId !== 13)?"Created by advisors":"Created by me"} firstoptionValue={1} items={[{ Id: 2, Display: [12,13,19].includes(RoleId)?"Assigned to advisors":"Assigned to me" }]} onChange={FilterChange}>
                                        </DropDown><br></br>
                                    </Col>

                                    
                                    <Col md={8}>
                                        <Row>

                                        <DateRange days={15} FromDate= {"Appointment Created From "} ToDate= {"Appointment Created To "}
                                            startDate={startDate} endDate={endDate} ref={dtRef} onStartDate={handleStartDate} onEndDate={handleEndDate}
                                            EndDateFixed={true} LimitStartdate={90}>
                                        </DateRange>
                                        </Row>
                                  </Col>
                                </Row>
                                <Row style={{ color: 'red'}}>* You can select 15 days window at once (Date range can be selected from past 3 months only)</Row>
                                {clickSearch && <center><Loader /></center>}

                                {!clickSearch && <DataTable

                                    columns={columnlist}
                                    data={FOSAgentDetails}
                                    printexcel={true}
                                    ref={dtRef}
                                    // className="fhdf"
                                />}

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(FOSAgentDashboard);