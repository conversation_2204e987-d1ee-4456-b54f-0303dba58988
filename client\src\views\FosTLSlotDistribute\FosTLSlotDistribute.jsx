import { useEffect, useState } from "react";
import { OverlayTrigger, Popover, Button, Row, Col } from 'react-bootstrap';

import TogglePopup from './TogglePopup';
import UnassignedLeadPopup from './UnassignedLeadPopup';
import moment from 'moment';
import { getuser, TimeConversion, getCookie } from '../../utility/utility.jsx';
import _ from 'underscore';
import {
  GetCommonData, GetCommonspData, SalesView
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";
// import ViewMoreConfirmedLead from '../FosTLSlotDistribute/ViewMoreConfirmedLead';
import MoreConfirmedLead from './MoreConfirmedLead';
import ManagerHierarchy from '../Common/ManagerHierarchy';
import Spinner from '../FosTLSlotDistribute/Spinner';
import { GetCommonData as GetMongoData } from "../../store/actions/CommonMongoAction";
import SlotTableView from "./SlotTableView";


const FosTLSlotDistribute = (props) => {

  const [SelectedSupervisors, setSelectedSupervisors] = useState([]);
  const [UnassignedLead, setUnassignedLeadShow] = useState(false);
  const [ConfirmLeadShow, setConfirmLeadShow] = useState(false);
  const [leadsData, setLeadsData] = useState([]);
  const [GroupedData, setGroupedData] = useState([]);
  const [toggle, setToggle] = useState();
  const [activeTab, setActiveTab] = useState(2);
  const [AppointmentSlots, setAppointmentSlots] = useState([]);
  const [Loader, setLoader] = useState(false);
  const [mode, setMode] = useState(0);
  const [EmployeeInfo, setEmployeeInfo] = useState()
  const [EmployeeFlag, setEmployeeFlag] = useState(0)
  const [timeLeft, setTimeLeft] = useState("");
  const [blink,setBlink] = useState(false)


  // To Check EmployeeId in mongo For Unassigned Leads of Particular Product
  useEffect(() => {
    try {
      props.GetMongoData({
        root: "FosUnassignedLeads",
        // cols: JSON.stringify(["PW28074"]),
        con: { EmployeeId: getuser().EmployeeId },
        c: "MATRIX_DASHBOARD_CLIENT",
      }, function (data) {
        if (data && data.data && data.data.data && data.data.data.length > 0) {
          console.log(data.data.data[0])
          setEmployeeInfo(data.data.data[0])
          setEmployeeFlag(1)
        }
        else {
          setEmployeeFlag(2)
        }

      })
    }
    catch (ex) {
      console.log(ex)
    }

  }, [])


  // To get Appointment Slots
  useEffect(() => {
    try {
      if (!props.CommonData.isError) {
        props.GetCommonData({
          limit: 10,
          skip: 0,
          root: 'FOS_AppointmentSlots',
          con: [{ "IsActive": 1 }],
          c: "R",
        }, function (data) {
          if (data && data.data && data.data.data) {
            let item = data.data.data[0]
            item.sort(function (a, b) {
              return a.Id - b.Id;
            });
            setAppointmentSlots(item);
          }
        });
      }
    }
    catch (ex) {
      console.log(ex)
    }
  }, [])


  useEffect(() => {
    if (AppointmentSlots.length > 0) {
      GetData();
    }

  }, [toggle, EmployeeFlag, AppointmentSlots])

  useEffect(() => {
    setTimeLeft(calculateTimeLeft())
    const time = setInterval(function () {
      setTimeLeft(calculateTimeLeft());
    }, 5*60000)

    return () => clearInterval(time)
  }, []);


  const calculateTimeLeft = () => {

    let timeLeft = moment().format("HH:mm");
    let time = timeLeft.split(":");
    let hours = time[0];
    let minutes = time[1];
    if(hours == 22 || (hours==23 && minutes <= 30)){
      GetData();
      setBlink(true)
    }
    else{
      setBlink(false)
    }
      console.log(timeLeft)
      return timeLeft;
  }


  // Get The Data of Appointments
  const GetData = () => {

    let Supervisors;
    let paramsData;
    var url_string = document.location;
    var url = new URL(url_string);
    var c = url.searchParams.get("mode");
    setMode(c)
    setLoader(true)

    if (SelectedSupervisors.length > 0) {
      setLoader(true)
      Supervisors = SelectedSupervisors.join()
    }
    if (SelectedSupervisors.length == 0) {
      console.log(getuser().UserID)
      Supervisors = getuser().UserID;
    }

    if (activeTab == 1) {
      paramsData = moment().subtract(1, 'days').format("YYYY-MM-DD")
    } else if (activeTab == 2) {
      paramsData = moment().format("YYYY-MM-DD")
    }
    else {
      paramsData = moment().add(1, 'days').format("YYYY-MM-DD")
    }
    try {
      if (EmployeeFlag) {
        props.GetCommonData({
          limit: 10,
          skip: 0,
          root: "SetSlotDistribution",
          // ManagerIds: SelectedSupervisors.join(),
          ManagerIds: Supervisors,
          slotdate: paramsData,
          c: "R"
          // '2023-03-22'
        }, function (result) {
          if (result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0) {
            debugger;
            // setFOSTLDashboardData(result.data.data[0]);
            let resultData = result.data.data[0]

            let arr = []
            resultData.forEach((item) => {
              if (item.EmployeeId) {
                arr.push(item)
              }
              else {
                if (EmployeeInfo && EmployeeInfo.EmployeeId == getuser().EmployeeId) {
                  for (let i = 0; i < EmployeeInfo.ProductId.length; i++) {
                    if (EmployeeInfo.ProductId[i] == item.ProductId) {
                      arr.push(item)
                    }
                  }
                }
              }
            })

            let GroupedData = _.groupBy(arr, (item) => {
              return item.EmployeeId
            })

            console.log(GroupedData)
            setGroupedData(GroupedData)
            setLoader(false)
          }
          else {
            setGroupedData([])
          }
        })
      }
    }
    catch (ex) {
      console.log(ex)
      setLoader(false)
      setGroupedData([])
    }

  }

  // useEffect(() => {
  //   props.GetCommonspData({
  //     limit: 10,
  //     skip: 0,
  //     root: "GetAppointmentsTracking",
  //     //params: [{ ManagerID: getuser().UserID }]//90022 }]
  //     params: [{ ManagerIds: SelectedSupervisors.join() }]//90022 }]

  //   }, function (result) {
  //     if (result && result.data && result.data.data && Array.isArray(result.data.data[0]) && result.data.data[0].length > 0) {
  //       debugger;
  //       setFOSTLDashboardData(result.data.data[0]);
  //       let GroupedData = _.groupBy(result.data.data[0], (item) => {
  //         return item.EmployeeId
  //       })
  //       setGroupedData(GroupedData)
  //     }

  //   })
  // }, [toggle])


  const handleShow = (e) => {
    let arr = [];
    for (let i = 0; i < e.nodesData.length; i++) {
      arr.push(e.nodesData[i].EmployeeId)
    }

    setSelectedSupervisors(e.SelectedSupervisors)
    setToggle(!toggle)
  }

  const tabChange = (e) => {
    console.log(e.target.value);
    setActiveTab(e.target.value);
    setToggle(!toggle)
  }

  //onScroll = {()=> document.getElementById("popover-positioned-bottom").style.visibility = "hidden"}

  const scroll = (event) => {
    console.log(event)
  }

  const handleMore = (array) => {
    setConfirmLeadShow(true);
    setLeadsData(array);
  }

  const newURL = () => {
    // console.log(document.location.href)
    let url = document.location.href + "?mode=1"
    window.open(url, "_blank");
  }

  const refreshData = () => {
    GetData();
  }



  const unassignleads = <>
    {activeTab != 1 && <tr>
      <th>Unassigned Leads</th>
      <td>
        <div className="UnassignedLead"><p><b>Lead ID :</b> 527344440
          <TogglePopup />
        </p>
        </div>
        <div className="UnassignedLead"><p><b>Lead ID :</b> 527344440
          <TogglePopup />
        </p>
        </div>
        <div className="UnassignedLead"><p><b>Lead ID :</b> 527344440
          <TogglePopup />
        </p>
        </div>
        <button className="morebtn" variant="primary" onClick={() => setUnassignedLeadShow(true)}>+2 More</button>
      </td>

    </tr>}
  </>

  const GridData = <>
    {
      GroupedData && Object.values(GroupedData).map((AgentData, index) => {

        const UserData = _.groupBy(AgentData, (item) => {
          return item.SlotId;
        })
        // console.log(UserData)
        let UserName = AgentData[0].UserName;
        let EmployeeId = AgentData[0].EmployeeId;
        // console.log(Object.keys(UserData))

        return (
          <>
            <tr>
              {EmployeeId ? <th>
                {UserName}
                <p>(Emp ID: {EmployeeId})</p>
              </th> :
                <th>Unassigned Leads</th>}


              <SlotTableView UserData={UserData} AppointmentSlots={AppointmentSlots} handleMore={handleMore}
                GetData={GetData} activeTab={activeTab} EmployeeFlag={EmployeeFlag} blink={blink}/>
            </tr>
            {/* {AppointmentSlots.map((slot) => (
                  UserData && Object.keys(UserData).indexOf(slot.Id.toString()) > -1 ?
                    Object.values(UserData).map((AgData) => (
                      slot.Id == AgData[0].SlotId &&
                      <td>
                        {
                          AgData.slice(0, 3).map((data) => (
                            <div className={data.SubStatusName == 'Start Journey' ? "StartJourney" : data.SubStatusName == 'End Journey' ? "EndJourney" :
                              data.SubStatusName.replace("Appointment", " ")} >
                              <b>Lead ID :</b>{data.LeadId}
                              <p><b>Status :</b>{data.SubStatusName.replace("Appointment", " ")} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={GetData} /></p>
                              
                            </div>

                          ))
                        }
                        {AgData && AgData.length > 3 && <button className="morebtn" variant="primary" onClick={() => handleMore(AgData)} >+{AgData.length - 3} more</button>}

                      </td>
                    ))
                    : <td></td>

                ))
                } */}


          </>
        )


        // let UserName;
        // let EmployeeId;
        // for (let i = 1; i <= AppointmentSlots.length; i++) {
        //   // console.log(i);
        //   if (UserData[i]) {
        //     UserName = UserData[i][0].UserName;
        //     EmployeeId = UserData[i][0].EmployeeId;
        //     break;
        //   }
        // }

        // return (

        //   <tr>
        //     <th>
        //       {UserName}
        //       <p>(Emp ID: {EmployeeId})</p>
        //     </th>
        //     {
        //       !UserData.hasOwnProperty("1") ? <td></td> :
        //         <td>
        //           {
        //             UserData["1"].slice(0, 3).map((data) => (
        //               <div className={data.SubStatusName != 'Start Journey' ? "Completed" : "journeyStarted"} >
        //                 <b>Lead ID :</b>{data.LeadId}
        //                 <p><b>Status :</b>{data.SubStatusName.replace("Appointment", " ")} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab}/></p>
        //                 {/* <p><b>Status :</b> {data.SubStatusName} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab}/></p> */}
        //               </div>
        //             ))
        //           }
        //           {UserData["1"].length > 3 && <button className="morebtn" variant="primary" onClick={() => handleMore(UserData["1"])} >+{UserData["1"].length - 3} more</button>}

        //         </td>
        //     }    

        //   </tr>
        // )


      }

      )
    }
  </>


  return (
    <>
      {Loader && <div className="Backdrop"> <Spinner /></div>}
      <ManagerHierarchy
        handleShow={handleShow} value={/UserID/g}
      >
      </ManagerHierarchy>
      <div className="content fosSlotDistributionSection">
        <h1>Lead-Slot distribution {mode != 1 && <img onClick={newURL} src="/fosTlDashboard/Maximize.jpeg" />}</h1>
        <Row>
          <Col md={4}>
            <ul className="DayTab" >
              <li className={activeTab == 1 && "activetab"} value='1' onClick={tabChange}>Yesterday</li>
              <li className={activeTab == 2 && "activetab"} value='2' onClick={tabChange}>Today</li>
              <li className={activeTab == 3 && "activetab"} value='3' onClick={tabChange}>Tomorrow</li>
            </ul>
          </Col>
          <Col md={2}>
            <Button onClick={refreshData}>Refresh</Button>
          </Col>

        </Row>
        
        {AppointmentSlots && Array.isArray(AppointmentSlots) && AppointmentSlots.length > 0 && <div className="leadSortDistribution">
          <table className="table table-bordered" onScroll={scroll}>
            <thead>
              <tr>
                <th>Advisor/Slots</th>
                {
                  AppointmentSlots.map((slots) => {
                    // console.log(TimeConversion(slots.StartTime))
                    return (
                      <th>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</th>
                      // <th>{`${slots.StartTime} PM - ${slots.EndTime} PM`}</th>
                    )
                  })
                }

                {/* <th>8:00 AM - 10:00 AM</th>
                <th className="activeTimeSlot">10:00 PM - 12:00 PM</th>
                <th>12:00 PM - 2:00 PM</th>
                <th>2:00 PM - 4:00 PM</th>
                <th>4:00 PM - 6:00 PM</th>
                <th>6:00 PM - 8:00 PM</th>
                <th>8:00 PM - 10:00 PM</th> */}
              </tr>
            </thead>
            <tbody>
              {/* {unassignleads} */}
              {GridData}
            </tbody>
          </table>
        </div>}
        {/* <UnassignedLeadPopup
          show={UnassignedLead}
          onHide={() => setUnassignedLeadShow(false)}
        /> */}

        <MoreConfirmedLead
          leadsData={leadsData}
          AppointmentSlots={AppointmentSlots}
          show={ConfirmLeadShow}
          activeTab={activeTab}
          IsSuccess={GetData}
          EmployeeFlag={EmployeeFlag}
          onHide={() => setConfirmLeadShow(false)}
        />

      </div>
    </>
  )

}

// export default FosTLSlotDistribute;

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetMongoData
  }
)(FosTLSlotDistribute);
