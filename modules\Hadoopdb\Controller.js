const tblList = require("../constants");
const { getHadoopData } = require('./Helper');
let fs = require('fs');
const path = require("path");
const conf = require("../../env_config");
const hive = require('hive-driver');
const { TCLIService, TCLIService_types } = hive.thrift;
const client = new hive.HiveClient(
  TCLIService,
  TCLIService_types
);
const utils = new hive.HiveUtils(
  TCLIService_types
);


async function getAgentBookingsdata(req, res) {
  try {
    const { agentId, incentiveMonth } = req.query;
    let startDate = "";
    let endDate = "";

    if(incentiveMonth !==null && incentiveMonth !== undefined) {
      let month = incentiveMonth.split("-")[1];
      let year = incentiveMonth.split("-")[0];
      startDate = incentiveMonth;
      endDate = year + "-" + month + "-31";
    }


    let queryString = `SELECT * FROM Incentive.BookingReport
                        WHERE bookingdate BETWEEN '${startDate}' AND '${endDate}'
                          AND salesagent=${agentId}`;

    // let queryString = `SELECT * FROM sisense1.Incentive_BookingReport 
    //                     WHERE salesagent='${agentId}' AND
    //                       agentprocessgroup is not null  AND
    //                       agentsubprocessgroup is not null`;

    let response = await getHadoopData(queryString);
    if(response.errorStatus === 0){

      // let RuleEngineDB = ruleenginedb;
      // let collection = RuleEngineDB.collection("AgentBookings");
      // let bookings = response.data;

      // for (let index = 0; index < bookings.length; index++) {
      //   let booking = bookings[index];
      //   let leadid = booking.leadid;
      //   let res = await collection.find({ leadid: leadid }).toArray();

      //   if(res.length === 0){
      //     let result = await collection.insertOne(booking);
      //   }
      // }

      res.send({
        status: 200,
        message: "Success",
        data: response.data,
      });
    } else {
      res.send({
        status: 500,      
        message: response.error
      });
    }
    
  } catch(err) {
    res.send({
      status: 500,      
      message: err
    });
  }
}

async function getBookingdata(req, res) {
  try {
    const { leadid } = req.query;
    // console.log(req.query);

    let queryString = `SELECT * FROM sisense1.Incentive_BookingReport 
                        WHERE leadid=${leadid}`

    let response = await getHadoopData(queryString);
    
    if(response.errorStatus === 0){
      res.send({
        status: 200,
        message: "Success",
        data: response.data,
      });
    } else {
      res.send({
        status: 500,      
        message: response.error
      });
    }
    
  } catch(err) {
    res.send({
      status: 500,      
      message: err
    });
  }
}


async function getAgentDetails(req, res) {
  try {
    const { agentId, incentiveMonth } = req.query;
    let startDate = "";
    let endDate = "";

    if(incentiveMonth !==null && incentiveMonth !== undefined) {
      let month = incentiveMonth.split("-")[1];
      let year = incentiveMonth.split("-")[0];
      startDate = incentiveMonth;
      endDate = year + "-" + month + "-31";
    }
   

    let queryString = `SELECT * FROM sisense1.Incentive_AgentDetails 
                        WHERE empid='${agentId}' AND agentprocessgroup is not null
                         AND agentsubprocessgroup is not null`
    
      // let queryString = `select * from sisense1.incentive_Agentdetails
      //     where process is not null  AND 
      //     agentsubprocessgroup is not null
      //      limit 10`

    // let queryString = `select * from  sisense1.Incentive_ProcessMaster
    //       where process is not null`
     

    let response = await getHadoopData(queryString);
    
    if(response.errorStatus === 0){

      let RuleEngineDB = ruleenginedb;
      let collection = RuleEngineDB.collection("AgentDetails");
      let agents = response.data;

      for (let index = 0; index < agents.length; index++) {
        let agent = agents[index];
        let result = await collection.insertOne(agent);
       
      }

      res.send({
        status: 200,
        message: "Success",
        data: response.data,
      });
    } else {
      res.send({
        status: 500,      
        message: response.error
      });
    }
    
  } catch(err) {
    res.send({
      status: 500,      
      message: err
    });
  }
}


async function executeHadoopQuery(req, res) {
  try {
 
    let { queryString } = req.body;

    let response = await getHadoopData(queryString);
    res.send({
      status: 200,
      message: "Success",
      data: response && response.data,
    });
    
  } catch(err) {
    res.send({
      status: 500,      
      message: err
    });
  }
}

// async function getdata(req, res) {

//   const { salesagent, startDate, endDate } = req.query;

//   client.connect(
//     conf.HadoopConnection.connect,
//     new hive.connections.TcpConnection(),
//     new hive.auth.PlainTcpAuthentication(conf.HadoopConnection.cred)
//     ).then(async client => {
//       const session = await client.openSession({
//       client_protocol: TCLIService_types.TProtocolVersion.HIVE_CLI_SERVICE_PROTOCOL_V10
//       });
//       const operation = await session.executeStatement(
//         `SELECT * FROM sisense1.Incentive_BookingReport 
//           WHERE bookingdate BETWEEN '${startDate}' AND '${endDate}' AND salesagent='${salesagent}'`
//       );
//       await utils.waitUntilReady(operation, false, () => { });
//       let selectDataOperation = await utils.fetchAll(operation);

//     res.send({
//       status: 200,
//       data: utils.getResult(selectDataOperation).getValue(),
//       message: "Success"
//     });
  
//     await operation.close();
//     await session.close();

//   }).catch(error => {    
//     res.send({
//       status: 500,      
//       message: error
//     });
//   });
  
// }

module.exports = {
  getAgentBookingsdata: getAgentBookingsdata,
  getBookingdata: getBookingdata,
  getAgentDetails: getAgentDetails,
  executeHadoopQuery: executeHadoopQuery
};
