import React, { Component } from 'react';

import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";

import OtpInput from "react-otp-input";
import { If, Then, Else } from 'react-if';
import moment from 'moment';


class OtpEnterVerification extends Component{

    constructor(props) {
        super(props);
        this.state = {
            userIcon:'/userid.svg',
            dateIcon:'/date.svg',
            timeIcon:'/time.svg',
            productIcon:'/product.svg',
            verifyImg:'/verify-ad.svg',
            bgImg: '/Graphic.svg' ,   
            advImg: '/Logo.svg' ,
            enterImg:'/enter.svg',   
            enterBigImg:'/enterBig.svg'
    }
    }

    back  = (e) => {
        //window.location.reload();
        e.preventDefault();
        this.props.prevStep(1);
    }
 
    renderButtonCallLog(isCookieExists, loginCookie){
        if(isCookieExists){
            return <Button variant="primary" className="clHistoryBtn HistoryBtn" onClick={(e) => this.props.checkCallHistory(loginCookie)}>Click to check Call History</Button>    

        } else {
            return <Button variant="primary" id="central-login-module-trigger" className="clHistoryBtn HistoryBtn">Sign in to check Call History</Button>    

        }
    }

    render(){

        const { verifyImg, UserInfo,bgImg, otp, Error, isCookieExists, loginCookie } = this.props
        return(
            
            <Row>  
             <Col md={6} xs={12}>
             <Button className="backCallLog" onClick={this.back}><i className="fa fa-arrow-left"></i></Button> 
                <div className="verifybgImg"><img src={this.state.bgImg}/>    </div></Col>
            <Col md={6} xs={12}>            
        <Row>                                                                       
        <Col md={12} xs={12} lg={9} className=" mt-2">
        <Form.Text className="text-muted">
        <h2 className="otp-title">Verify your advisor</h2>    
        <p class="otp-caption">Concerned whether your advisor is actually from Policybazaar? Enter the 6 digit pin shared by your advisor to verify instantly</p>
        </Form.Text> 
        </Col> 
        </Row>
        <Row className="bgBlue">
            <Col md={3} xs={3} lg={2}><img src={this.state.advImg}/> </Col>
            <Col md={4} xs={4} lg={3}><span className="wantTo">Want to</span>
            <p><a onClick={this.props.knowmore.bind(this)} className="knowMore">Know more <i class="nc-icon nc-minimal-right"></i> </a></p>   
            </Col>
            <Col md={5} xs={5} lg={3}>
            <span className="wantTo">Still not sure</span>
            <p><a onClick={this.props.howitworks.bind(this)} className="knowMore">See how it works <i class="nc-icon nc-minimal-right"></i> </a></p>  
            </Col>           
        </Row>
        <Row className="bgWhite">
            <Col md={12} xs={12} lg={9} className="mt-3">
             <label>Enter PIN  <hr/></label>
            <OtpInput className="otp-no"
                value={otp}
                onChange={this.props.handleChange}
                numInputs={6}  
            /> 
            
            <If condition={Error}>
            <Then>
            <Form.Text className="error-text">
            Incorrect Code. Please try again
            </Form.Text> 
            </Then></If>  
            <Button variant="primary" className="clHistoryBtn mt-3" onClick={() => this.props.onClickSubmit(otp)} disabled={(otp) ? "" : "disabled"}>Verify Now</Button>             
            </Col>               
          </Row>
            <Row>
         <Col  md={12} xs={12} lg={9}>             
            <div className="clHistoryCheck">                
            <h4 className="seperateLine"><span>Or</span></h4>    
            <Row>
            <Col md={3} xs={12} lg={2} className="text-center"><img className="enterlg" src={this.state.enterBigImg}/>  </Col>
            <Col md={9} xs={12} lg={10}>  
            {this.renderButtonCallLog(isCookieExists, loginCookie)}  

            {/* <Button variant="primary" id="central-login-module-trigger" className="clHistoryBtn HistoryBtn">Sign in to check Call History</Button>  */}
                <p className="pera">Check details of the conversations that you had with our advisors in past</p></Col>
                </Row> 
                </div>
                        
            </Col>  
                               
            </Row>                                  
             
             </Col>
               </Row>      
                                                   
        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default OtpEnterVerification;