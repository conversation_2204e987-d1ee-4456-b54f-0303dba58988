import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {


    <PERSON><PERSON><PERSON>eader,
    <PERSON>dalBody,
    ModalFooter,
    Input,
    FormGroup,
    Label,
    FormText
} from 'reactstrap';

import {
    GetCommonData, InsertData, UpdateData, GetCommonspData
} from "../../store/actions/CommonAction";
import {
    addRecord
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTableWithFilter from '../Common/DataTableWithFilter';
import SweetAlert from 'react-bootstrap-sweetalert';
import { If, Then } from 'react-if';
import axios from "axios";
import config from "../../config"
import { OpenNewSalesView, LeadView, GetJsonToArray, getUrlParameter, getuser, fnDatatableCol } from '../../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardT<PERSON>le,
    Row,
    Col
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import DropDown from "../Common/DropDown";
import DateRange from "../Common/DateRange"
import moment from 'moment';
import Select from 'react-select';

const FOSCityPanel = (props) => {
    const [items, setitems] = useState([])
    const [root] = useState(["GetFosAllocationPanel"])
    const [PageTitle] = useState(["FOS City Panel "])
    const [event] = useState([]);
    let [rows, setRows] = useState([]);
    let [item, setitem] = useState([]);
    const [AssignmentType, setAssignmentType] = useState();
    const [endDate, setendDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));
    const [startDate, setstartDate] = useState(moment().subtract(15, 'days').format("YYYY-MM-DD"));
    const [FOSAgentDetails, setFOSAgentDetails] = useState([]);
    //const [ProductList, setProductList] = useState([]);
    const [ProductId, setProductId] = useState("");
    const [AppointmentAndAssignmentMaster, setAppointmentAndAssignmentMaster] = useState({})
    const [AppointmentTypeMaster, setAppointmentTypeMaster] = useState([])
    const [AssignmentMaster, setAssignmentMaster] = useState([])
    const [AppointmentType, setAppointmentType] = useState("0");
    const [Cities, setCities] = useState([]);
    const [showModal, setshowModal] = useState(false);
    const [EditableRow, setEditableRow] = useState({});
    const [changedRowData, setChangedRowData] = useState({ active: [], inactive: [] });
    const [columnlist, setColumnList] = useState([]);
    const [ModalValueChanged, setModalValueChanged] = useState(false);
    const [UpdateAssignmentList, setUpdateAssignmentList] = useState([]);
    const [TotalCities, setTotalCities] = useState([]);

    const user = getuser();
    const userId = user.UserID
    let dtRef = React.createRef();

    const preFixedColumnList = [
        // {

        //     name: 'CityId',
        //     selector: 'CityId',
        //     // type: "string",
        //     // cell: row =>
        //     //     <a href={LeadView(row.CustomerId, row.LeadId, row.ProductID, userId)} target="_blank">{row.LeadId} </a>

        //     // ,
        //     editable: false,
        //     sortable: true,
        //     width: '100px',
        //     //searchable: true
        // },
        {

            name: 'City',
            selector: 'City',
            type: "string",
            editable: false,
            sortable: true,
            width: '100px',
            searchable: true
        },
        


    ]
    const ZoneList = [

        {

            name: 'Zone',
            selector: 'Zone',
            type: "string",
            width: '100px',
            //hide:true
            sortable: true,
            //searchable: true
        }


    ]

    const ProductList =
        [{ Id: "2", Display: "Health" },{ Id: "147", Display: "Health Renewals" } ,{ Id: "7", Display: "TermLife" },
             { Id: "115", Display: "Investments" }, { Id: "131", Display: "SME/GMC" },{Id:"117",Display:"Motor"},{Id:"139",Display:"Commercial Insurance"}]

    const postFixedColumnList = [
        {
            name: 'Edit',
            //selector: 'AssignmentId',
            //type: "datetime",
            cell: row => <ButtonGroup>
                <Button variant="secondary" className="editBtn" onClick={() => { handleEdit(row) }}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
            </ButtonGroup>,
            //sortable: true,
            width: '180px',
            //searchable: true
        }
    ]



    const handleSave = async () => {
        console.log("Product Id", ProductId)
        //console.log("AppointmentTypeId",AppointmentTypeId)
        console.log("AppointmentType", AppointmentType)
        console.log("Assignment Entries", EditableRow)
        if (user) {
            try {
                let headers = { 'Content-Type': 'application/json' };
                let url = config.api.base_url + '/MatrixCoreApi/UpdateFosCity';
                let body = {
                    "AgentId": userId,
                    "Data": UpdateAssignmentList
                }
                console.log("Handlesave", url, body, headers)
                let result = await axios.post(url, body, { headers: headers });
                console.log("result is", result);
                if (result.status == 200) {
                    alert("Assignment updated successfully")
                    clearState();
                    setshowModal(false);
                    GetOfflineCitiesData();

                }

                return result;
            } catch (err) {
                console.log(err);
            }

        }


    }
    const clearState = () => {
        setUpdateAssignmentList([]);
        setEditableRow({});

    }
    const handleEdit = (row) => {
        let _row = JSON.parse(JSON.stringify(row));
        setshowModal(true);
        setEditableRow(_row);

    }


    const GetOfflineCitiesData = () => {
        if (user) {
            props.GetCommonspData({
                root: 'GetOfflineCities',
                c: "R",

                // params: [{ UserId: user.UserID }, { ProductId: ProductId }],
                params: [{ ProductId: ProductId }],

            }, function (data) {
                if (data && data.data && data.data.data) {
                    let _rawCityData = data.data.data[0]
                    // const unique = [...new Set(item.map(i => i.CityId + "_" + i.ZoneId))];
                    // console.log("unique is", unique)
                    //_rawCityData=_rawCityData.filter(i=>i.AppointmentTypeId==AppointmentType)
                    let citiesList = [];
                    let _cityZoneObj = {};
                    for (var i = 0; i < _rawCityData.length; i++) {
                        const cityObj = _rawCityData[i];

                        try {

                            const key = cityObj.CityId + "_" + cityObj.ZoneId;
                            if (!_cityZoneObj[key]) {
                                _cityZoneObj[key] = {
                                    CityId: cityObj.CityId,
                                    ZoneId: cityObj.ZoneId,
                                    City: cityObj.City,
                                    Zone: cityObj.Zone,
                                    fosMapping: []
                                }
                            }
                            _cityZoneObj[key].fosMapping.push({
                                "AppointmentId": cityObj.AppointmentTypeId,
                                "AssignmentId": cityObj.AssignmentId,
                                "Id": cityObj.Id,
                                "IsActive": cityObj.IsActive
                            })
                        }
                        catch (err) {
                            console.error('Something Went Wrong: ', err)
                        }
                    }
                    citiesList = Object.values(_cityZoneObj);
                    //setCities(citiesList);
                    setTotalCities(citiesList);

                    if (AppointmentType == 4 || AppointmentType == 6) {
                        let array = citiesList.filter(i => i.ZoneId == null)
                        console.log("Without zone", array)
                        console.log("With Zone", Cities)
                        setCities(array)
                        updateAssignmentColumns();
                        // setCities(TotalCities);

                    }
                    else if (AppointmentType && AppointmentType!="0" ) {
                        setCities(citiesList)
                        updateAssignmentColumns();
                        // setCities(TotalCities);
                    }
                    console.log("modified Cities list is", citiesList)
                }
            })
        }
    }

   

    const ProductChange = (e) => {
        //setProcessselected([])
        var PId = e.target.value;
        setProductId(e.target.value);

    }
    const GetAppointmentTypebyProduct = (id) => {
        if (user) {
            setAppointmentTypeMaster([]);
            props.GetCommonspData({
                root: 'GetAppointmentTypebyProduct',
                c: "R",

                // params: [{ UserId: user.UserID }, { ProductId: id }],
                params: [{ UserId: userId }, { ProductId: id }],


            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data
                    // TODO : check values here !!!!
                    try {
                        if (Array.isArray(item[1])) {
                            setAssignmentMaster(item[1]);
                        }
                        if (Array.isArray(item[0])) {
                            setAppointmentTypeMaster(item[0].map((appointment) => ({
                                Id: appointment.AppointmentId,
                                Display: appointment.AppointmentValue,
                                ProcessId: appointment.ProcessId
                            })));

                        }
                    }
                    catch (err) {
                        console.error('Something Went Wrong: ', err);

                    }
                }
            })
        }
    }
    const AppointmentTypeChange = (e) => {
        setAppointmentType(e.target.value);
        // GetOfflineCitiesData();
    }
    const updateAssignmentColumns = () => {
        let _columns = [];
        if (AppointmentType != 4 && AppointmentType != 6) {
            _columns = [...preFixedColumnList, ...ZoneList]
        }
        else {
            _columns = [...preFixedColumnList];
        }

        // Push Assignment columns as per Appointment Type
        AssignmentMaster.forEach((_assignmentObj) => {
            if (_assignmentObj.AppointmentTypeId != AppointmentType) return;

            _columns.push({
                name: _assignmentObj.AssignmentType,
                selector: row => row.fosMapping.find((mapping) =>
                    (_assignmentObj.AssignmentId == mapping.AssignmentId && _assignmentObj.AppointmentTypeId == mapping.AppointmentId && mapping.IsActive == 1)) ? "Yes" : "No",
                //type: "datetime",
                cell: row => <div>{row.fosMapping.find((mapping) =>
                    (_assignmentObj.AssignmentId == mapping.AssignmentId && _assignmentObj.AppointmentTypeId == mapping.AppointmentId && mapping.IsActive == 1)) ? "Yes" : "No"}</div>,

                sortable: true,
                width: '180px',
                //searchable: true
            })
        });

        // Push Edit Column
        _columns = [..._columns, ...postFixedColumnList]
        console.log("column list", _columns)
        setColumnList(_columns);
    }
    const handleClose = () => {
        setshowModal(false)
        clearState();
    }
   
    const handleAssignmentChange = (e, row, AssignmentId, AppointmentId, istickActive, Id) => {
        let _updateList = [...UpdateAssignmentList];
        let updatedValue = e.target.checked;
        let updatedRow = JSON.parse(JSON.stringify(row));
        let _changedRowData = JSON.parse(JSON.stringify(changedRowData));
        if (updatedValue === true) {
            if (!istickActive) {
                //updatedRow.fosMapping.push({ AppointmentId, AssignmentId, Id, IsActive: true });

                let index = updatedRow.fosMapping.findIndex(i => i.Id == Id)
                if (updatedRow.fosMapping.findIndex(i => (i.Id == Id && i.AppointmentId == AppointmentId && i.AssignmentId == AssignmentId)) != -1) {
                    updatedRow.fosMapping[updatedRow.fosMapping.findIndex(i => (i.Id == Id && i.AppointmentId == AppointmentId && i.AssignmentId == AssignmentId))].IsActive = updatedValue;
                }
                else {
                    updatedRow.fosMapping.push({ AppointmentId, AssignmentId, Id, IsActive: updatedValue });
                }


            }


        }

        else {
            if (istickActive) {
                let index = updatedRow.fosMapping.findIndex(i => (i.Id == Id && i.AppointmentId == AppointmentId && i.AssignmentId == AssignmentId))
                if (updatedRow.fosMapping.findIndex(i => (i.Id == Id && i.AppointmentId == AppointmentId && i.AssignmentId == AssignmentId)) != -1) {
                    updatedRow.fosMapping[updatedRow.fosMapping.findIndex(i => (i.Id == Id && i.AppointmentId == AppointmentId && i.AssignmentId == AssignmentId))].IsActive = updatedValue;
                }
                else {
                    updatedRow.fosMapping.push({ AppointmentId, AssignmentId, Id, IsActive: updatedValue });
                }


                console.log("Updated List", _updateList)
                
            }
        }
        let obj = {};
        if (Id) {
            let index = _updateList.findIndex(i => Id == i.Id)
            if (_updateList.findIndex(i => Id == i.Id) != -1) {
                _updateList[_updateList.findIndex(i => Id == i.Id)].IsActive = updatedValue;
            }
            else {
                obj = {
                    "Id": Id,
                    "CityId": row.CityId,
                    "IsActive": updatedValue
                }
                _updateList.push(obj);
            }
        }
        else if (row.CityId) {
            let z = !row.ZoneId ? 0 : row.ZoneId;
            let key = AppointmentId + '-' + AssignmentId;
            if (_updateList.findIndex(i => (i.AppointmentTypeId + '-' + i.AssignmentTypeId) == key) != -1) {
                if (updatedValue == true) {
                    obj = {
                        "CityId": row.CityId,
                        "ZoneId": (AppointmentType == 4 || AppointmentType == 6) ? 0 : z,
                        "AppointmentTypeId": AppointmentId,
                        "AssignmentTypeId": AssignmentId,
                        "ProductId": ProductId,
                        "IsActive": true
                    }
                    _updateList.push(obj);
                }
                else {
                    _updateList = _updateList.filter(i => !(i.AppointmentTypeId == AppointmentId && i.AssignmentTypeId == AssignmentId))
                }
            }
            else {
                obj = {
                    "CityId": row.CityId,
                    "ZoneId": (AppointmentType == 4 || AppointmentType == 6) ? 0 : z,
                    "AppointmentTypeId": AppointmentId,
                    "AssignmentTypeId": AssignmentId,
                    "ProductId": ProductId,
                    "IsActive": true
                }
                _updateList.push(obj);
            }

        }

        console.log(_updateList)
        setUpdateAssignmentList(_updateList)

        setChangedRowData(_changedRowData);
        setEditableRow(updatedRow);
        setModalValueChanged(true)
        // row.fosMapping.find(()=>{

        // })
        // let _inActive
        // setChangedRowData()
    }
    console.log("Updated Assignement List State is", UpdateAssignmentList)
    useEffect(() => {
        setAppointmentType("0")
        setColumnList([]);
        setCities([]);
        GetAppointmentTypebyProduct(ProductId)
        GetOfflineCitiesData();



    }, [ProductId]);
    const UpdateDataforAppointments = () => {
        if (AppointmentType == 4 || AppointmentType == 6) {
            let array = TotalCities.filter(i => i.ZoneId == null)
            console.log("Without zone", array)
            console.log("With Zone", Cities)
            setCities(array)
            updateAssignmentColumns();
            // setCities(TotalCities);

        }
        else if (AppointmentType) {
            setCities(TotalCities)
            updateAssignmentColumns();
            // setCities(TotalCities);
        }
    }
    useEffect(() => {

        UpdateDataforAppointments();



    }, [AppointmentType, AssignmentMaster, AppointmentTypeMaster]);

    return (
        <>

            <div className="content fosCityPanel">
                <ToastContainer />
                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                        
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>

                                <Row>
                                    <Col md={4}>
                                        <Form.Label><i>*</i> Product </Form.Label>
                                        <DropDown firstoption="Select Product" items={ProductList} onChange={ProductChange}>
                                        </DropDown><br></br>
                                    </Col>
                                    <Col md={4}>
                                        <Form.Label><i>*</i> Appointment Type </Form.Label>
                                        <DropDown firstoption="Select Appointment Type" items={AppointmentTypeMaster} onChange={AppointmentTypeChange}>
                                        </DropDown><br></br>
                                    </Col>

                                </Row>

                                {AppointmentType != 0 && <DataTableWithFilter

                                    columns={columnlist}
                                    data={Cities}
                                    printexcel={false}
                                    ref={dtRef}
                                // className="fhdf"
                                />

                                }


                                <Modal show={showModal} onHide={() => { handleClose() }} dialogClassName="modal-50w fosCityEditpopup">

                                    <Modal.Header closeButton>
                                        <Modal.Title>FOS Assignment Update</Modal.Title>
                                    </Modal.Header>
                                    <Modal.Body>
                                        <FormGroup>
                                            <Label for="City">City</Label>
                                            <Input
                                                type="text"
                                                name="City"
                                                id="City"
                                                value={EditableRow.City}
                                                //onChange={() => { }}
                                                disabled
                                            />
                                        </FormGroup>
                                        {EditableRow.Zone && <FormGroup>
                                            <Label for="Zone">Zone</Label>
                                            <Input
                                                type="text"
                                                name="Zone"
                                                id="Zone"
                                                value={EditableRow.Zone}
                                                // onChange={() => { }}
                                                disabled
                                            />
                                        </FormGroup>}
                                        {AssignmentMaster.map((_assignmentObj) => {
                                            if (_assignmentObj.AppointmentTypeId != AppointmentType) return null;
                                            if (!EditableRow || !EditableRow.fosMapping) return null;
                                            const istickActive = !!EditableRow.fosMapping.find((mapping) => (_assignmentObj.AssignmentId == mapping.AssignmentId && _assignmentObj.AppointmentTypeId == mapping.AppointmentId && mapping.IsActive == 1));
                                            const obj = EditableRow.fosMapping.find((mapping) => (_assignmentObj.AssignmentId == mapping.AssignmentId && _assignmentObj.AppointmentTypeId == mapping.AppointmentId));
                                            let Id = obj ? obj.Id : null;
                                            return (
                                                <FormGroup>
                                                    <Label for={_assignmentObj.AssignmentType}>
                                                        {_assignmentObj.AssignmentType}</Label>
                                                    <input
                                                        type="checkbox"
                                                        name={_assignmentObj.AssignmentType}
                                                        value={istickActive}
                                                        id={_assignmentObj.AssignmentType}
                                                        checked={istickActive}
                                                        onClick={(e) => { handleAssignmentChange(e, EditableRow, _assignmentObj.AssignmentId, _assignmentObj.AppointmentTypeId, istickActive, Id) }}
                                                    // onChange={() => { handleNameChange() }}
                                                    />
                                                </FormGroup>)
                                        })}
                                    </Modal.Body>
                                    <Modal.Footer>
                                        <Button variant="secondary" onClick={() => { handleClose() }} >
                                            Close
                                        </Button>
                                        <If condition={ModalValueChanged}>
                                            <Then>
                                                <Button variant="primary" onClick={() => { handleSave() }}>Save Changes</Button>
                                            </Then>
                                        </If>
                                    </Modal.Footer>
                                </Modal>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(FOSCityPanel);