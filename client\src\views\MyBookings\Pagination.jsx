import React from "react";
import ReactPaginate from 'react-paginate';

const Pagination = (props) => {
  const { handlePageClick, pageCount } = props;

  return (
    <ReactPaginate
      breakLabel="..."
      nextLabel=" >>"
      onPageChange={handlePageClick}
      pageRangeDisplayed={5}
      pageCount={pageCount}
      previousLabel="<< "
      renderOnZeroPageCount={null}
      activeClassName="active"
      pageClassName="page-item"
      pageLinkClassName="page-link"
      previousClassName="page-item"
      previousLinkClassName="page-link"
      nextClassName="page-item"
      nextLinkClassName="page-link"
      breakClassName="page-item"
      breakLinkClassName="page-link"
      marginPagesDisplayed={2}
      containerClassName="pagination"
    //forcePage={SelectedPage}
    />
  );
}

export default Pagination;