
import React from "react";
import { <PERSON><PERSON>, Form } from 'react-bootstrap';

import { GetCommonData, GetCommonspData } from "../../store/actions/CommonAction";

import { connect } from "react-redux";
import { getuser } from '../../utility/utility.jsx';
//import { fnBindRootData, fnRenderfrmControl, fnCleanData, GetJsonToArray, getMax, joinObject, getuser } from '../../utility/utility.jsx';

import DropDownList from "./../Common/DropDownList.jsx"
import DropDown from "./../Common/DropDown.jsx"
import RadioButton from './../Common/RadioOptions';
import Datetime from 'react-datetime';

//import moment from "moment";
import moment from "moment";
//import AutoCompleteDropDown from './../Common/AutoCompleteDropDown.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col,
  //FormGroup,
  Input,
} from "reactstrap";
import DropDownAutoComplete from "views/Common/DropDownAutoComplete";
import Select from "react-select";

class LeadCommonFields extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      //isLoaded: false,
      //showModal: false,
      //store: [],
      //activePage: 1,
      //root: "CreateLead",
      //PageTitle: "Create Lead",
      //FormTitle: "",
      //formvalue: {},
      event: "",
      //errors: {},
      fields: {
        // DateOfBirth: moment().subtract(0, 'years').format("YYYY-MM-DD")
      },
      //LeadSource: 3,
      showProspectFields: false,
      showBankFields: false,
      ProductId: this.props.ProductId,
      UseExistingMobileNo: null,
      encryptedMobileNO: '',
      Country: props.Country || 392,
      CountryName: props.CountryName || 'India'
    };
    this.Cities = [];
    this.Countries = [];
    this.CarMakeList = [];
    this.CarMakeModelList = [];
    this.SuppliersList = [];
    this.PlansList = [];
    this.CategoriesList = [];
    this.getCountryNames = this.getCountryNames.bind(this);
    this.onChangeCountry = this.onChangeCountry.bind(this);
  }

  onChangeCountry(e) {
    this.setState({ Country: e.value });
    this.setState({ CountryName: e.label });
  }

  getCountryNames() {
    if (!this.props.CommonData.isError) {
      this.props.GetCommonData({
        skip: 0,
        root: 'CountryMaster',
        cols: ["CountryId AS id", "Country AS display"],
        con: [{ "Isactive": 1 }],
        c: "R",
      }, function (data) {
        if (data && data.data && data.data.data) {
          let countries = data.data.data[0];
          countries.sort(function(a, b){
            if(a.display < b.display) { return -1; }
            if(a.display > b.display) { return 1; }
            return 0;
        })
          countries = countries.map((it) => {
            let countryName = it.display.charAt(0).toUpperCase() + it.display.substr(1).toLowerCase();
            let countryId = it.id;
            return { id: countryId, display: countryName };
          }).filter(item => item.display !== 'Other');
          this.Countries = countries;
        }
      }.bind(this));
    }
  }

  componentDidMount() {
    this.getCities();
    this.getCarMakes();
    this.getCategories(this.state.ProductId);
    this.getCountryNames();
  }


  componentWillReceiveProps(nextProps) {
    this.setState({ fields: nextProps.LeadDetails })

    if (nextProps.LeadDetails.MobileNo) {
      let existmobno = document.getElementById('ExistMobNo');
      this.setState({
        encryptedMobileNO: (existmobno && existmobno.checked) ? (nextProps.LeadDetails.MobileNo.replace(/^.{8}/g, 'XXXXXXXX')) : nextProps.LeadDetails.MobileNo,
        UseExistingMobileNo: (nextProps.LeadDetails.MobileNo.replace(/^.{8}/g, 'XXXXXXXX'))
      });
    }
    if ((nextProps.ProductId && nextProps.ProductId != this.props.ProductId)) {
      this.getCategories(nextProps.ProductId);
    }
  }

  handleChange(field, e) {
    debugger

    this.props.onChange(e, field);
    let fields = this.state.fields;
    if (e.target && e.target.type == "checkbox") {
      fields[field] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      try {
        fields[field] = e.format("DD-MM-YYYY")
      }
      catch (e) { }
    } else if(e == 'Country'){
      this.setState({ countryName: field.label, countryCode: field.value});
    } else {
      fields[field] = e.target.value;
    }
    this.setState({ fields });
    if (field == 'Make') {
      this.getCarMakeModels(e.target.value);
    } else if (field == 'Supplier') {
      this.getPlansList(e.target.value);
    } else if (field == 'ExistingMobileNo') {
      this.checkExistingNo()
    } else if (field == 'MobileNo') {
      this.setState({ encryptedMobileNO: e.target.value })
    }
  }

  checkExistingNo() {
    debugger;
    let fields = this.state.fields;
    if (this.state.fields.ExistingMobileNo == true) {
      //this.state.fields.MobileNo = this.state.UseExistingMobileNo;
      this.setState({ encryptedMobileNO: this.state.UseExistingMobileNo })

    } else {
      //this.state.fields.MobileNo = '';
      this.setState({ encryptedMobileNO: '' })
    }
    this.setState({ ...fields });
  }

  handleStatusChange(field, e) {
    if (e.target.value == 11) { //Prospect
      this.setState({ showProspectFields: true, showBankFields: false });
      this.getSuppliersList();
    } else {
      this.setState({ showProspectFields: false, showBankFields: false });
      this.SuppliersList = [];
    }
    this.props.onChange(e, field);
  }

  handlePaymentModeChange(field, e) {
    if (e.target.value == 3001 || e.target.value == 5001) {
      this.setState({ showBankFields: true });
    } else {
      this.setState({ showBankFields: false });
    }
    this.props.onChange(e, field);
  }

  getCarMakes() {
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetCarMakeList",
    }, function (result) {
      let List = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        result.data.data[0].forEach((element, index) => {
          List.push({
            Id: element.MakeId, Display: element.MakeName
          })
        });
      }
      //console.log(List);
      this.CarMakeList = List;
    }.bind(this));
  }

  getCategories(proId) {
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetSubProductType",
      params: [{ "ProductId": proId }],
    }, function (result) {
      let List = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        result.data.data[0].forEach((element, index) => {
          List.push([
            element.Name, parseInt(element.SubProductTypeId, 10)]
          )
        });
      }
      console.log(List);
      this.CategoriesList = List;
    }.bind(this));
  }

  getCarMakeModels(makeId) {
    if (makeId == "" || makeId == "0" || makeId == 0) {
      this.CarMakeModelList = [];
      return;
    }
    //let formvalue = JSON.parse(JSON.stringify(this.state.fields));
    //console.log(formvalue['Make']);
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetCarMakeModelList",
      params: [{ "MakeId": makeId }]
    }, function (result) {
      let List = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        result.data.data[0].forEach((element, index) => {
          List.push({
            Id: element.ModelId, Display: element.ModelName
          })
        });
      }
      this.CarMakeModelList = List;
    }.bind(this));
  }

  getSuppliersList() {
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetSuppliersPlansList",
      params: [{ "ProductId": this.state.ProductId }]
    }, function (result) {
      let List = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        result.data.data[0].forEach((element, index) => {
          List.push({
            Id: element.SupplierID, Display: element.SupplierName
          })
        });
      }
      console.log(List);
      this.SuppliersList = List;
    }.bind(this));
  }

  getPlansList(supplierId) {
    if (supplierId == "" || supplierId == "0" || supplierId == 0) {
      this.PlansList = [];
      return;
    }
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetSuppliersPlansList",
      params: [{ "ProductId": this.state.ProductId, "SupplierId": supplierId }]
    }, function (result) {
      let List = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        result.data.data[0].forEach((element, index) => {
          List.push({
            Id: element.PlanID, Display: element.Planname
          })
        });
      }
      this.PlansList = List;
    }.bind(this));
  }

  getCities() {
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetStateCitiesList",
    }, function (result) {
      let citiesList = [];
      if (result.data.data[0] && result.data.data[0].length > 0) {
        delete result.data.data[0][0];
        result.data.data[0].forEach((element, index) => {
          citiesList.push({
            name: element.Display,
            value: element.Id
          })
        });
      }
      this.Cities = citiesList;
    }.bind(this));
  }

  validation = (currentDate) => {
    return currentDate.isBefore(moment());
  };


  render() {
    const { showBankFields, showProspectFields, items, FormTitle, event, Country, CountryName } = this.state;
    const { LeadDetails } = this.props;
    let countryCode = Country;
    let countryName = '';
    if (CountryName) {
      countryName = CountryName.charAt(0).toUpperCase() + CountryName.substr(1).toLowerCase();
    }
    console.log('prodid', ['7', '2'].includes(this.state.ProductId));
    /*if (datestr === "") {
      const datestr = new Date();
    } else {
      const datestr = new Date(datestr);
    }*/
    //console.log(this.state.fields['DateOfBirth']);
    const datestr = this.state.fields['DateOfBirth'] ? moment(this.state.fields['DateOfBirth']).format("DD-MM-YYYY") : moment().subtract(0, 'years').format("DD-MM-YYYY");

    return (
      <Row className="CreateLeadContainer">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Name <span>*</span></Form.Label>
            <Input
              placeholder="Enter Name"
              type="text"
              name="Name"
              maxLength={30}
              onChange={this.handleChange.bind(this, "Name")}
              value={(this.state.fields['Name'] != undefined) ? this.state.fields['Name'] : ""}
            //disabled={['7','2','115'].includes(this.state.ProductId)? true: false}
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>Email Id</Form.Label>
            <Input placeholder="Enter Email Id" type="text" name="EmailId" maxLength={50} onChange={this.handleChange.bind(this, "EmailId")}
              value={(this.state.fields['EmailId'] != undefined) ? this.state.fields['EmailId'] : ""} />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group className="GenderContainer">
            <Form.Label>Gender? {(["2", "7"].includes(this.props.ProductId)) ? <span>*</span> : ''}</Form.Label>
            <RadioButton
              name="Gender"
              changed={this.handleChange.bind(this, "Gender")}
              items={[["Male", "1"], ["Female", "0"]]}
              isSelected={(this.state.fields['Gender'] != undefined) ? this.state.fields['Gender'] : ""}
              checked={(this.state.fields['Gender'] != undefined) ? this.state.fields['Gender'] : ""}
            //disabled= {['7','2'].includes(this.state.ProductId)? true: false}
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>Date Of Birth {(["2", "7"].includes(this.props.ProductId)) ? <span>*</span> : ''}</Form.Label>
            <Datetime
              //value={datestr}
              id="DateOfBirth"
              name="DateOfBirth"
              dateFormat="DD-MM-YYYY"
              timeFormat={false}
              onChange={moment => this.handleChange("DateOfBirth", moment)}
              utc={true}
              inputProps={{
                id: "DateOfBirth",
                name: "DateOfBirth",
                required: true,
                disabled: "",
                value: (this.state.fields['DateOfBirth'] != undefined) ? this.state.fields['DateOfBirth'] : datestr,
                onKeyDown: (e) => { e.preventDefault() },
                onPaste: (e) => { e.preventDefault() },
                onCopy: (e) => { e.preventDefault() },
                onCut: (e) => { e.preventDefault() }
              }
              }
              isValidDate={this.validation}

            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>Address</Form.Label>
            <Form.Control
              disabled={false}
              name="Address"
              type="text"
              as="textarea"
              placeholder={"Enter Address"}
              onChange={this.handleChange.bind(this, "Address")}
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>City {(["2"].includes(this.props.ProductId)) ? <span>*</span> : ''}</Form.Label>
            <DropDownAutoComplete
              col={{
                name: "City",
                label: "City",
                type: "autodropdown",
                config: {
                  root: "GetStateCitiesList",
                  statename: "Cities",
                  executionType: 'SP',
                  state: true,
                  sp: true
                }
              }}
              filterkey={null}
              filtervalue={null}
              onChange={this.handleChange.bind(this, "City")}
              required={true}
              value={(this.state.fields['CityID'] != undefined) ? this.state.fields['CityID'] : ""}
              name="City"
            />
            {/* <AutoCompleteDropDown name={"City"} data={this.Cities} onChange={this.handleChange.bind(this, "City")} /> */}
            {/* <Input placeholder="Enter City" type="text" name="CityName" maxLength={30} value= {(this.state.fields['City'] != undefined)? this.state.fields['City'] : ""}
                disabled={true} />
                <Input type="hidden" name="City" maxLength={30} 
                value= {(this.state.fields['CityID'] != undefined)? this.state.fields['CityID'] : ""}/> */}
          </Form.Group>
        </Col>
        <Col md={4}>
          {(this.props.isReferral) && <Form.Group>
            <Form.Label>Use Existing Mobile No.</Form.Label>
            <input id="ExistMobNo" type="checkbox" checked={(this.state.fields['ExistingMobileNo'] != undefined) ? this.state.fields['ExistingMobileNo'] : this.props.useExistingMobile} onChange={this.handleChange.bind(this, "ExistingMobileNo")} ></input>
          </Form.Group>}
          <Form.Group>
            <Form.Label>Mobile No. <span>*</span></Form.Label>
            <Input placeholder="Enter Mobile" type="text" name="MobileNo" maxLength={10} onChange={this.handleChange.bind(this, "MobileNo")}
              value={(this.state.fields['MobileNo'] != undefined) ? this.state.encryptedMobileNO : ""}
              disabled={(this.props.LeadSource == 'CrossSell') ? true : false}
            />
          </Form.Group>
        </Col>
        {/* <Col md={4}>
              <Form.Group>
                <Form.Label>UTM Medium</Form.Label>
                <Input placeholder="Enter UTM Medium" type="text" name="UtmMedium" maxLength={30} onChange={this.handleChange.bind(this, "UtmMedium")} />
              </Form.Group>
            </Col> */}
        <Col md={4}>
          <Form.Group>
            <Form.Label>Postal Code {(["2"].includes(this.props.ProductId)) ? <span>*</span> : ''}</Form.Label>
            <Input placeholder="Enter PostalCode" type="text" name="PostalCode" maxLength={6} onChange={this.handleChange.bind(this, "PostalCode")}
              value={(this.state.fields['PostalCode'] != undefined) ? this.state.fields['PostalCode'] : ""}
            //disabled={['2'].includes(this.state.ProductId)? true: false}
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>Annual Income</Form.Label>
            <DropDownList
              col={{
                name: "AnnualIncome",
                label: "Annual Income",
                type: "dropdown",
                config: {
                  root: "AnnualIncome",
                  data: [
                    { Id: "299999", Display: "upto 3 lakhs" },
                    { Id: "499999", Display: "3 - 5 lakhs" },
                    { Id: "699999", Display: "5 - 7 lakhs" },
                    { Id: "899999", Display: "7 - 10 lakhs" },
                    { Id: "1000000", Display: "10 - 15 lakhs" },
                    { Id: "1500000", Display: "15 lakhs +" },
                  ],
                }
              }}
              filterkey={null}
              filtervalue={null}
              onChange={this.handleChange.bind(this, "AnnualIncome")}
              required={true}
            />

          </Form.Group>
        </Col>
        {/* <Col md={4}>
              <Form.Group>
                <Form.Label>Make</Form.Label>
                <DropDown
                  disabled={false}
                  visible={true} 
                  items= {this.CarMakeList}
                  name= "Make"
                  firstoption="Select Make"
                  onChange={this.handleChange.bind(this, "Make")}
                />

              </Form.Group>
            </Col> */}
        {/* <Col md={4}>
              <Form.Group>
                <Form.Label>Model</Form.Label>
                <DropDown
                  disabled={false}
                  visible={true} 
                  items= {this.CarMakeModelList}
                  name= "Model"
                  firstoption="Select Model"
                  onChange={this.handleChange.bind(this, "Model")}
                />

              </Form.Group>
            </Col> */}

        {this.CategoriesList && this.CategoriesList.length > 0 && <Col md={4}>
          <Form.Group className="Category">
            <Form.Label>Category {(["115", "7"].includes(this.props.ProductId)) ? <span>*</span> : ''}</Form.Label>
            <RadioButton
              name="ProductTypeID"
              changed={this.handleChange.bind(this, "productTypeID")}
              items={this.CategoriesList}
              isSelected={(this.state.fields['productTypeID'] != undefined) ? parseInt(this.state.fields['productTypeID']) : ""}
              checked={(this.state.fields['productTypeID'] != undefined) ? parseInt(this.state.fields['productTypeID']) : ""}
            />
          </Form.Group>
        </Col>}

        <Col md={4}>
          <Form.Group>
            <Form.Label>Status</Form.Label>
            <DropDownList
              firstoption="Select Status"
              col={{
                name: "Status",
                label: "Status",
                type: "dropdown",
                config: {
                  root: "Status",
                  data: [
                    //{ Id: 2, Display: "Valid" }, 
                    //{ Id: 3, Display: "Contacted" },
                    { Id: 4, Display: "Interested" },
                    //{ Id: 11, Display: "Prospect" },
                  ],
                }
              }}
              filterkey={null}
              filtervalue={null}
              onChange={this.handleStatusChange.bind(this, "Status")}
              required={true}
              value="4"
              disabled={true}
            />
          </Form.Group>
        </Col>

        <Col md={4}>
          <Form.Group>
            <Form.Label>Country<span>*</span> </Form.Label>
            <Select
              options={this.Countries.map((item) => {
                return { label: item.display, value: item.id }
              })}
              onChange={(e) => this.handleChange(e, 'Country')}
              defaultValue={{ value: countryCode, label: countryName }}
            />
          </Form.Group>
        </Col>

        {showProspectFields && (
          <>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Supplier <span>*</span></Form.Label>
                <DropDown
                  firstoption="Select Supplier"
                  name="Supplier"
                  items={this.SuppliersList}
                  filterkey={null}
                  filtervalue={null}
                  onChange={this.handleChange.bind(this, "Supplier")}
                  required={true}
                />

              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Plan Name <span>*</span></Form.Label>
                <DropDown
                  firstoption="Select Supplier"
                  name="PlanName"
                  items={this.PlansList}
                  filterkey={null}
                  filtervalue={null}
                  onChange={this.handleChange.bind(this, "PlanName")}
                  required={true}
                />

              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Premium</Form.Label>
                <Input placeholder="Enter Premium" type="text" name="Premium" onChange={this.handleChange.bind(this, "Premium")} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>SA/IDV</Form.Label>
                <Input placeholder="" type="text" name="SaIdv" />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Mode Of Payment <span>*</span></Form.Label>
                <DropDownList
                  firstoption="Select Mode"
                  col={{
                    name: "PaymentSource",
                    label: "Payment Source",
                    type: "dropdown",
                    config: {
                      root: "PaymentSource",
                      data: [
                        { Id: "300", Display: "Online Payment" },
                        { Id: "5001", Display: "Cheque" },
                        { Id: "3001", Display: "Transfer" },
                      ],
                    }
                  }}
                  filterkey={null}
                  filtervalue={null}
                  onChange={this.handlePaymentModeChange.bind(this, "PaymentSource")}
                  required={true}
                />

              </Form.Group>
            </Col>
            {showBankFields && (
              <>
                <Col md={4}>
                  <Form.Group>
                    <Form.Label>Bank Branch Name</Form.Label>
                    <Input placeholder="Enter Branch Name" type="text" name="BranchName" onChange={this.handleChange.bind(this, "BranchName")} />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group>
                    <Form.Label>Cheque No.</Form.Label>
                    <Input placeholder="Enter Cheque No." type="text" name="Cheque" onChange={this.handleChange.bind(this, "Cheque")} />
                  </Form.Group>
                </Col></>
            )}
          </>
        )}


      </Row>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
  }
)(LeadCommonFields);