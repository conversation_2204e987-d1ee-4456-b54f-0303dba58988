const tblList = require("../constants");
let fs = require('fs');
const path = require("path");
const { Rule, Fact, Engine } = require('json-rules-engine');
const { ObjectID, ObjectId } = require('mongodb');
let engine = new Engine();
const { expressionParser } = require('./expressionParser');
const { evaluate } = require('mathjs');
const sqlHelper = require("../../Libs/sqlHelper");
const SQLMethods = require("./SQLMethods");
const Controller = require("./Controller");
var _ = require('underscore');
const axios = require('axios');
const config = require('../../env_config');
const e = require("express");
const HadoopHelper = require("../Hadoopdb/Helper");
const LoggerMethods = require("../Loggerdb/Methods");
const LoggerController = require("../Loggerdb/Controller");


async function getAgentBookingsFromDB({ agentId, startDate, endDate }) {
  let RuleEngineDB = ruleenginedb;
  console.log({ agentId, startDate, endDate });

  let key = await RuleEngineDB.collection('FactValues')
    .find({ key : 'datasource'}).toArray();

  let datasource = '', bookings= [];
  if(key.length > 0) {
    datasource = key[0].source;
  }

    /// 1 agent,2 booking 3 both
    if(datasource === '2'  || datasource === '3' ) {
      bookings = await RuleEngineDB.collection('AgentBookings')
        .find({
          $and: [
            { salesagent: agentId },
            {
              bookingdate: {
                $gte: startDate,
                $lte: endDate
              }
            }
          ]
        })
        .toArray();
    } else {
        let result=null;
        let query = `SELECT * FROM Incentive.BookingReport WHERE bookingdate BETWEEN  cast('${startDate}' as date) AND cast('${endDate}' as date) AND salesagent='${agentId}'`;

        result = await HadoopHelper.queryAthenaData(query);

         if(result !== null &&
            result.errorStatus === 0 && 
              result.results !== undefined &&
                result.results.Items !== undefined) {

                  bookings = result.results.Items;
        }


        /*************  Testing API for local bookings data  **************/

        // let body = {
        //   "queryString":  `SELECT * FROM Incentive.BookingReport WHERE bookingdate BETWEEN  cast('${startDate}' as date) AND cast('${endDate}' as date) AND salesagent='${agentId}'`
        // }

        // let url = "https://incentivedashboardqa.policybazaar.com/api/v1/hdb/athenaData"
        // let data = await axios.post(url, body);
        // bookings = data.data.data.Items;

        // console.log('total bookings fetched: ', bookings.length);
      }
    
  return bookings;
}


async function getAgentDetails(req, res) {
  try {
    const { agentId, year, month } = req.query;
    let data = await agentDetailsData({ agentId, year, month });

    if(data !== null && data.length > 0) {
          
      res.status(200).json({
        status: 200,
        message: 'success',
        data: data
      });

    }
    else {
        res.status(404).json({
          status: 404,
          data: null
        });
    }
  } catch(err) {
      console.log(err);
      res.status(500).json({
        status: 500,
        data: err
      });
  }

}

async function agentDetailsData({ agentId, year, month }) {

  let RuleEngineDB = ruleenginedb;
  let key = await RuleEngineDB.collection('FactValues')
    .find({ key : 'datasource'}).toArray();

  let datasource = '', agentDetails=[];
  if(key.length > 0) {
    datasource = key[0].source;
  }

   /// 1 agent,2 booking 3 both
   if(datasource === '1'  || datasource === '3' ) {

    let collection = RuleEngineDB.collection("AgentDetails");

    agentDetails = await collection.find({
      empid: agentId,
      processyear: Number(year),
      processmonth: Number(month)
    }).toArray();

  } else {

    let result=null;
    let query = `SELECT * FROM incentive.agentdetails
                  WHERE empid='${agentId}' AND
                    processyear=${year} AND
                    processmonth=${month}`;

    result = await HadoopHelper.queryAthenaData(query);
    
    if(result !== null &&
        result.errorStatus === 0 &&
          result.results !== undefined &&
            result.results !== null &&
              result.results.Items !== undefined) {
      agentDetails = result.results.Items;
    }
  }

  return agentDetails;
}

async function getAgentCTC(req, res) {

  try {
    let data = await agentCTC(req.query);

    if(data.errorStatus === 0) {
      res.status(200).json({
        status: 200,
        message: 'success',
        data: data.ctc
      });
    } else {
      res.status(500).json({
        status: 500,
        data: null
      });
    }

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }

}

async function agentCTC({ agentId, incentiveMonth }) {

  try {
    let errorStatus = 0;
    let arr = incentiveMonth.split('-');
    let month = parseInt(arr[1], 10);
    let year  = arr[0];
    let day = arr[2];
  
    let monthString = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    
    let date = day + '-' + monthString[month - 1] + '-' +year;
    let base_url = config.AGENT_CTC_BASE_URL;
    let url =base_url + '?ECode=' + agentId +'&FilterDate=' + date;
    const headers = {
      headers: { Authorization: `Bearer ${config.AGENT_CTC_TOKEN}` }
    };

    let response = await axios.get(url, headers);
    
    let ctc = null;
    if(response !==null && response.data !== undefined && response.data.ReturnData !== undefined) {
      ctc = response.data.ReturnData.AgentCTC
    }

    if(ctc !== null) {
      let url = config.CTC_INCENTIVE_URL;
      let data = { value: ctc };
      let headers = {'Content-Type': 'application/json'};
      ctc = await axios.post(url, data, headers);
    }

    if(ctc !== null) {
      ctc = ctc.data.data;
      return { errorStatus, ctc};
    } else {
      errorStatus=1;
      return { errorStatus, ctc};
    }

  } catch (err) {

    let errorStatus = 1;
    return { errorStatus, err};
  }

}

async function getAggregateAgentBookingsData({ agentId, incentiveMonth, bookings, agentDetails }) {

  // let query = `select * from sisense1.incentive_Agentdetails
  //               where process is not null  AND 
  //               agentsubprocessgroup is not null AND
  //               empid='${agentId}'`;

  // let agent = await getHadoopData(query); // data from hadoop basis on agentId
  try {
     
    // let arr = incentiveMonth.split("-");
    // let month = parseInt(arr[1], 10);
    // let year  =  parseInt(arr[0]);
  
    // let RuleEngineDB = ruleenginedb;

    // agent details function
    // let agent = await agentDetailsData({ agentId, year, month });
    let aggregationKeys = [
      {
        name: '',
        productId: 117,
        values: [
          { type : 'string', name : 'insurercategory' },
          { type : 'string', name : 'plancategory' }
        ]
      },
      {
        name: '',
        productId: 117,
        values: [
          { type : 'number', name : 'insurerid' },
          { type : 'string', name : 'plancategory' }
        ]
      }
    ];

    let agentFacts = [];
    for (let i = 0; i < aggregationKeys.length; i++) {

      let item = aggregationKeys[i];
      let values = item.values;

      bookingsFilter =_.filter(bookings, function(booking){
         return (booking.policytype === "New" || booking.policytype === "Rollover"); 
      });

      console.log("filtered bookings count: ", bookingsFilter.length);

      let aggr = _.groupBy(bookingsFilter, function(booking) {

        let aggregationGroup="";

        for (let j = 0; j < values.length; j++) {

          let element = values[j].name;

          if(j < values.length - 1) {
            aggregationGroup = aggregationGroup +  booking[element] + '#';
          } else {
            aggregationGroup = aggregationGroup +  booking[element];
          }
        }
        return aggregationGroup;
      });

      for( key in aggr){
        let count = 0;
        if(key!=null && aggr[key]!=undefined) {
          count = aggr[key].length;
          console.log(key + " : " + count);
        }
        let agent = {};
        if(agentDetails.length > 0){
          agent = agentDetails[0];
        }
    
        let objAgg = {
          ...agent,
          agentId: agentId,
          TotalBookings: bookings.length,
        }

        let compositeKeys = key.split('#');
        for (let index = 0; index < compositeKeys.length && values.length; index++) {

          let element = compositeKeys[index];
          let aggrKey = values[index].name;
          let type = values[index].type;

          if(aggrKey !== undefined) {
            if(type === 'number') {
              objAgg[aggrKey] = parseInt(element);
            } else {
              objAgg[aggrKey] = element;
            }
            console.log( aggrKey + ':' + element);
          }
          
        }
        console.log('\n');

        objAgg['bookingsCount'] = count;
        objAgg['policytype'] = "New";
        agentFacts.push(objAgg);
        // console.log('obj', objAgg);
    
      }
    }
    return agentFacts;

  } catch (error) {
    console.log(error);
    return error;
  }

}


async function getAgentBookings(req, res) {
  try {
    const { agentId, incentiveMonth } =req.query;

    let startDate = "";
    let endDate = "";

    if(incentiveMonth !==null && incentiveMonth !== undefined) {
      let month = incentiveMonth.split("-")[1];
      let year = incentiveMonth.split("-")[0];
      startDate = incentiveMonth;
      endDate = year + "-" + month + "-31";
    }

    let bookings = await getAgentBookingsFromDB({ agentId, startDate, endDate });

    res.status(200).json({
      status: 200,
      message: 'success',
      data: bookings
    });

  } catch (err) {

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}

async function getBooking(req, res) {
  try {
    const { leadid } = req.query;
    let booking = await getBookingData(leadid);

    if(booking.length > 0) {
    
      res.status(200).json({
        status: 200,
        message: 'success',
        data: booking[0]
      });

    } else {
        res.status(404).json({
          status: 404,
          data: booking
        });
    }
    
  } catch (err) {
    console.log(err);
    res.status(500).json({
      status: 500,
      message: err
    })
  }
}


async function getBookingData(leadid) {

  let RuleEngineDB = ruleenginedb;
  let key = await RuleEngineDB.collection('FactValues')
    .find({ key : 'datasource'}).toArray();

  let datasource = '', booking=[];
  if(key.length > 0) {
    datasource = key[0].source;
  }
  
  /// 1 agent,2 booking 3 both
  if(datasource === '1'  || datasource === '3' ) {

    booking = await RuleEngineDB.collection('AgentBookings')
      .find({ leadid: Number(leadid) })
      .toArray();
    return booking;

  } else {

      let result=null, booking={};
      let query = `SELECT * FROM Incentive.BookingReport
                    WHERE leadid=${leadid}`;

      result = await HadoopHelper.queryAthenaData(query);
      
      if(result !== null &&
          result.errorStatus === 0 &&
            result.results !== undefined &&
              result.results !== null &&
                result.results.Items !== undefined) {
                booking = result.results.Items;
      }
      return booking;
  }
   
}


async function CalculateBookingIncentive(req, res) {
  let requestTime = new Date().toISOString();
  try {

    let result = await CalculateBooking(req.query);

    if (result !==null && result.resolveResult != null && result.errorStatus === 0) {
      res.status(200).json({
        status: 200,
        message: 'resolve success',
        data: result.resolveResult
      });
    } else if (result !== null && result.errorStatus === 1) {
      res.status(500).send({
        status: 500,
        error: result.err
      });
    } else {
      res.status(404).send({
        status: 404,
        error: "No Rules applied"
      });
    }

  } catch (err) {
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = JSON.stringify(req.leadid)
    logData.Exception = err.message;
    logData.Method = "CalculateBookingIncentive";
    logData.Application = "MatrixDashboard";
    logData.Channel = "RuleEngine";
    logData.RequestText = JSON.stringify(req.query);
    logData.ResponseText = err.message;
    logData.Requesttime =  requestTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixRuleEngine";
    logData.IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() ||
                  req.socket.remoteAddress
  
    let response = await LoggerMethods.LogKafka(logData);

    res.status(500).json({
      status: 500,
      message: err
    })
  }
}


async function CalculateBooking({leadid, incentiveMonth}) {
  try {

    //Fetch booking
    let errorStatus = 0;
    let booking = await getBookingData(leadid);

    // resolve rules
    let bookingFact = {}, productId=null;
    if(booking.length>0){
      bookingFact = booking[0];
      productId = bookingFact.productid;
    }

    let body = {
      facts: bookingFact,
      criteria: 'booking',
      operationType: 'basic'
    };

    if(productId !== null && incentiveMonth!==undefined) {
      let searchKeys = [
        { key: "Product", value: productId },
        { key: "IncentiveMonth", value: incentiveMonth},
      ];

      body.searchKeys = searchKeys;
    }
   
    
    let resolveResult = await Controller.resolve(body);
    //Update IsActive = false and Insert Results with IsActive = true

    let withJustificationAmount = 0, withoutJustificationAmount=0;
   
    if(resolveResult !== null ){
      let events = resolveResult.events;
      for (let index = 0; index < events.length; index++) {
        const element = events[index].params;
        const { justification, amount} = element;
        if(justification!== undefined && amount!==undefined){
          if(justification === 'true') {
            withJustificationAmount = parseInt(amount);
          } else {
            withoutJustificationAmount = parseInt(amount);
          }
        }
        
      }
    }

    let ape=0, result=0;
    if(booking.length>0 && booking[0].ape!==undefined) {
      ape = booking[0].ape;
    }
    if(resolveResult!==null && resolveResult.result !== undefined) {
      result = resolveResult.result;
    }
    // console.log(withJustificationAmount, " ", withoutJustificationAmount)

    
    // if(incentiveMonth !== null && incentiveMonth !== undefined) {
    //   let response = await  SQLMethods.SaveBookingDetails({
    //     BookingId: leadid,
    //     incentivemonth: incentiveMonth,
    //     BookingStatus: "Issued",
    //     salesAgent: "PW15876",
    //     APE: ape,
    //     WeightedAPE: result,
    //     AmountwithJustification: withJustificationAmount,
    //     AmountwithoutJustification: withoutJustificationAmount
    //   });
    // }

    //let collection = await RuleEngineDB.collection("CalculatedBookingIncentive");
    // let result = await collection.find({ leadid: Number(leadid) })
    //   .toArray();

    // if(result?.length === 0 && resolveResult?.errorStatus === undefined){
    //resolveResult.leadid = Number(leadid);
    // let res = await collection.insertOne(resolveResult);

    // } else if(result?.length > 0) {

    //   resolveResult.leadid = leadid;
    //   collection.updateOne(
    //       { leadid: leadid },
    //       resolveResult
    //     )
    // }

    return { errorStatus, resolveResult, withJustificationAmount };

  } catch (err) {
    console.log('err', err)
    let errorStatus = 1;

    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = JSON.stringify(leadid)
    logData.Exception = err.message;
    logData.Method = "CalculateBookingIncentive";
    logData.Application = "MatrixDashboard";
    logData.Channel = "RuleEngine";
    logData.RequestText = JSON.stringify(leadid)
    logData.ResponseText = err.message;
    logData.Requesttime =  requestTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixRuleEngine";
    logData.IP = null
  
    let response = await LoggerMethods.LogKafka(logData);
    return { errorStatus, err };
  }
}


async function CalculateAgentIncentive(req, res) {
  const { agentId, incentiveMonth, productId } = req.query;
  let requestTime = new Date().toISOString();
  try {
    let response = await CalculateAgentBooking({
                    agentId,
                    incentiveMonth,
                    productId
                  });

    // console.log(JSON.stringify(response, null, 4));
    if (response!=null && response.errorStatus === 0 && response.result !== undefined ) {
      res.status(200).json({
        status: 200,
        message: 'resolve success',
        data: response.result
      });
    } else if (response !== null && response.errorStatus === 1) {
      res.status(500).send({
        status: 500,
        error: response.err
      });
    } else {
      res.status(404).send({
        status: 404,
        error: "No Rules applied"
      });
    }

  } catch (err) {
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID =  agentId;
    logData.Exception = err.message;
    logData.Method = "CalculateAgentIncentive";
    logData.Application = "MatrixDashboard ";
    logData.Channel = "RuleEngine";
    logData.RequestText = JSON.stringify(req.query);
    logData.ResponseText = err.message;
    logData.Requesttime =  requestTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixRuleEngine";
    logData.IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() ||
                  req.socket.remoteAddress
  
    let response = await LoggerMethods.LogKafka(logData);

    res.status(500).json({
      status: 500,
      message: err.message
    })
  }
}


async function CalculateAgentBooking({ agentId, incentiveMonth, productId }) {
  let query = { agentId, incentiveMonth, productId };
  let requestTime = new Date().toISOString();
  try {

    //Fetch booking
    let errorStatus = 0;
    let startDate = "";
    let endDate = "";
    let TotalAmountWithJustification = 0;
    let TotalBookingIncentive = 0;
    let TotalIncentive = 0;

    if(incentiveMonth !==null && incentiveMonth !== undefined) {
      let month = incentiveMonth.split("-")[1];
      let year = incentiveMonth.split("-")[0];
      startDate = incentiveMonth;
      endDate = year + "-" + month + "-31";
    }


    let bookings = await getAgentBookingsFromDB({ agentId, startDate, endDate })
    // let agentCTC = await agentCTC({ agentId, incentiveMonth });

    // resolve booking rules

    let bookingResponse = [];
    for (let index = 0; index < bookings.length; index++) {
      const element = bookings[index];
      let bookingResult = {};

      let response = await CalculateBooking({
        leadid: element.leadid,
        incentiveMonth: incentiveMonth
      });

      if(response.errorStatus === 0){
        TotalAmountWithJustification+= parseFloat(response.withJustificationAmount);
        
        if(response.resolveResult!== null) {

          let bookingResolve = response.resolveResult;
          bookingResult.leadid = element.leadid;
          for( key in bookingResolve) {
            if(key !== 'rules') {
              bookingResult[key] = bookingResolve[key];
            }
            if(key === 'result') {
              TotalBookingIncentive+= parseFloat(response.resolveResult.result);
            }
          }
          bookingResponse.push(bookingResult);
        }
      }
    }

    // agent details
    let arr = incentiveMonth.split("-");
    let month = parseInt(arr[1], 10);
    let year  =  parseInt(arr[0]);
    let agentDetails = await agentDetailsData({ agentId, year, month });

    // get aggregate booking 
    let bookingsAgg = await getAggregateAgentBookingsData({
        agentId,
        incentiveMonth,
        bookings,
        agentDetails
      });
      console.log('aggr', bookingsAgg)

    // resolve agent rules
    let resolveResult = {};
    let processName = "";
    let qualityScore = 0;
    let bookingcount = 0;
    let TotalAgentIncentive = 0;
    let AgentTotalBeforeAddDeduction = 0;
    if(bookings.length >0) {
      bookingcount = bookings.length;
    }

    let agentResponse = [], agent;
    let addonResponse = [], deductionResponse = [];
    let operations = ['basic', 'addon', 'deduction'];
    if(agentDetails.length > 0){
      agent = agentDetails[0];
    }


    for (let i = 0; i < operations.length; i++) {
      let operationType = operations[i];

      if( operationType === 'basic') {
        for (let j = 0; j < bookingsAgg.length; j++) {
          let agentResult = {};
          const agent = bookingsAgg[j];
          // processName = agent.agentprocessgroup;
          // qualityScore = agent.qualityScore;
    
          let body = { facts: agent, criteria: "agent", operationType: operationType};
    
          if(productId !== undefined && incentiveMonth!==undefined) {
            let searchKeys = [
              { key: "Product", value: parseInt(productId)},
              { key: "IncentiveMonth", value: incentiveMonth},
            ];
      
            body.searchKeys = searchKeys;
          }
    
          resolveResult = await Controller.resolve(body);
          if(resolveResult !== null && resolveResult.errorStatus === undefined){
            agentResult.agentId = agentId;
            for( key in resolveResult) {
              if(key !== 'rules') {
                agentResult[key] = resolveResult[key];
              }
            }
            TotalAgentIncentive+= resolveResult.result;
            agentResponse.push(agentResult);
          }
        }
        TotalIncentive = TotalBookingIncentive + TotalAgentIncentive;
        AgentTotalBeforeAddDeduction = TotalIncentive;

      } else {
       
          let agentResult = {};
          // processName = agent.agentprocessgroup;
          // qualityScore = agent.qualityScore;
          agent.incentive = TotalIncentive;
          console.log(TotalIncentive);
    
          let body = { facts: agent, criteria: "agent", operationType: operationType};
    
          if(productId !== undefined && incentiveMonth!==undefined) {
            let searchKeys = [
              { key: "Product", value: parseInt(productId)},
              { key: "IncentiveMonth", value: incentiveMonth},
            ];
      
            body.searchKeys = searchKeys;
          }
    
          resolveResult = await Controller.resolve(body);
          if(resolveResult !== null && resolveResult.errorStatus === undefined){
            agentResult.agentId = agentId;
            for( key in resolveResult) {
              if(key !== 'rules') {
                agentResult[key] = resolveResult[key];
              }
            }
            TotalIncentive= parseInt(resolveResult.result);
            console.log(TotalIncentive)
            if(operationType === 'addon') {
              addonResponse.push(agentResult);
            } else if(operationType === 'deduction') {
              deductionResponse.push(agentResult);
            }
            
          }

      }

    }
     
    // console.log('TotalAgentIncentive', TotalAgentIncentive);
    // console.log('TotalBookingIncentive', TotalBookingIncentive);
    // console.log('TotalAmountWithJustification', TotalAmountWithJustification);
     
    //console.log('agentLevelIncentive', TotalAgentIncentive);
  
     // Insert Into SQL agents
    // SQLMethods.SaveAgentDetails({
    //   Ecode: agentId,
    //   productId: 117,
    //   Incentivemonth: incentiveMonth,
    //   ProcessName: processName,
    //   Sourcedbkgs: bookingcount,
    //   IssuedBkgs: bookingcount,
    //   IssuedAPE: 0,
    //   AmountMade: 0,
    //   QualityScore: qualityScore,
    //   FinalIncentive: totalResult
    // })
    let result = {
      booking: bookingResponse,
      agent: agentResponse,
      addon: addonResponse,
      deduction: deductionResponse,
      AgentLevelResult: AgentTotalBeforeAddDeduction,
      FinalIncentive : TotalIncentive,
      AgentDetails: agent
    }
    return { errorStatus, result };

  } catch (err) {
    console.log('err', err);
    let errorStatus = 1;
    
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID =  agentId   
    logData.Exception = err.message;
    logData.Method = "CalculateAgentIncentive";
    logData.Application = "MatrixDashboard";
    logData.Channel = "RuleEngine";
    logData.RequestText = JSON.stringify(query);
    logData.ResponseText = err.message;
    logData.Requesttime =  requestTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixRuleEngine";
    logData.IP = null;
  
    let response = await LoggerMethods.LogKafka(logData);
    return { errorStatus, err };
  }
}

module.exports = {
  getAgentBookings: getAgentBookings,
  getBooking: getBooking,
  CalculateBookingIncentive: CalculateBookingIncentive,
  CalculateAgentIncentive: CalculateAgentIncentive,
  getAgentCTC: getAgentCTC,
  getAgentDetails: getAgentDetails
};
