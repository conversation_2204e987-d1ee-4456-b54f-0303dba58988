import React from "react";
import {
    GetCommonData, GetCommonspData, GetDataDirect, GetMoodleCourse, GetExportQuiz, GetSummaryQuiz, GetSummaryCourse, GetSummaryLMS
} from "../store/actions/CommonAction";
import {
    GetMySqlData
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser, fnDatatableCol, hhmmss, joinObject } from '../utility/utility.jsx';
//import { Multiselect } from 'multiselect-react-dropdown';
//import { MDBSelect } from "mdbreact";
//import Select from 'react-select'; 
//import makeAnimated from 'react-select/animated'; 
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDown';
import DropDownList from './Common/DropDownList';
import ManagerHierarchy from './MoodleComponent/MoodleManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import Loading from './Common/Loading';


import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'

import _, { bind } from 'underscore';

// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class MoodleReport extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "MoodleReportData",
            PageTitle: "LMS Report",
            showLoading: false,
            ExportQuizList: [],
            QuizSelectList: [],
            QuizMultiList: [],
            QuizSelectList: [],
            CourseMultiList: [],
            SummaryQuizData: [],
            SummaryCourseData: [],
            QuizSummaryData: [],
            CourseSummaryData: [],
            AllManagerData: [],
            ProductId: '',
            DefaultSelectedQuiz: null,
            DefaultSelectedSummary: null,
            SelectedUsers: [],
            LMSSummaryData: [],
            StartDate: moment().format("YYYY-MM-DD"),
            EndDate: moment().format("YYYY-MM-DD"),
            DaysList: [],
            DefaultSelectedDaysList: null,
            DropDownStartDate: moment().format("YYYY-MM-DD"),
            DropDownEndDate: moment().format("YYYY-MM-DD"),
            GrandTotalData: [],
            GrandTotalColumns: [],

            QuizColumns: [
                {
                    name: "EmployeeId",
                    selector: "EmployeeId",
                    searchable: true,

                },
                {
                    name: "Employee Name",
                    selector: "UserName",
                    searchable: true,

                },
                {
                    name: "TL Name",
                    selector: "TL_Name",
                    searchable: true,

                },
                {
                    name: "AM/DM Name",
                    selector: "AM_Name",
                    searchable: true,

                },
                {
                    name: "Manager Name",
                    selector: "Manager_Name",
                    searchable: true,

                },
                {
                    name: "Team Name",
                    selector: "Team_Name",
                    searchable: true,

                },

            ],

            QuizSummaryColumns: [
                {
                    name: "Team Coaches",
                    selector: "Reportees",
                    searchable: true,
                    sortable: true,
                    width: "350px"

                },
                {
                    name: "Total Agents",
                    selector: "TotalAgents"
                },
                {
                    name: "Didn't Appear",
                    selector: "DontAppear"
                },
                {
                    name: "Pass",
                    selector: "Pass"
                },
                {
                    name: "Fail",
                    selector: "Fail"
                },
                {
                    name: "Team Average % Score",
                    selector: "Average"
                },
                {
                    name: "Attendees Average % Score",
                    selector: "AverageScoreAttendes"
                },
                
            ],

            CourseSummaryColumns: [
                {
                    name: "Team Coaches",
                    selector: "Reportees",
                    searchable: true,
                    sortable: true,
                    width: "350px"

                },
                {
                    name: "Total Agents",
                    selector: "TotalAgents"
                },
                {
                    name: "Complete",
                    selector: "Complete"
                },
                {
                    name: "Pending",
                    selector: "Pending"
                }
            ],

            LMSSummaryColumns: [],
            QuizData: [],

            ExportCourseColumns: [],
            ExportCourseData: [],
            ExportLMSColumns: [],
            ExportLMSData: [],
        };

        this.LMSSummaryColumns = [
            {
                name: "Team Coaches",
                selector: "Reportees",
                width: "350px",
                searchable: true
            },
            {
                name: "Total Agents",
                selector: "TotalAgents"
            }
        ]
        this.DaysList = {
            config:
            {

                data: [{ Id: "1", Display: "2july to 8 july" }, { Id: "2", Display: "9 july to 15 july" }],
            }
        };


    }


    componentWillMount() {
        GetDataDirect({
            root: "Hierarchy",
            ManagerId: 75,
            statename: "Hierarchy-" + 75,
            Hierarchy : 1,
            state: true
        }, function (result) {

            this.setState({ AllManagerData: result });
        }.bind(this));
    }

    //Filter Selections Start
    handleQuizSelect = (QuizValue) => {
        this.setState({ QuizSelectList: QuizValue });
    }
    handleProductId = (ProductId) => {
        this.setState({ ProductId: ProductId });
    }

    handleSelectManager(e) {
        if (e && e.SelectedSupervisors) {
            this.setState({
                ReportTime: new Date(),
                SelectedSupervisors: e.SelectedSupervisors,
                SelectedUsers: _.pluck(e.SelectedUsers, 'EmployeeId')

            });
        }
    }

    coursechange(e, props) {
        var courseid = e.target.value;
        this.setState({ DefaultSelectedCourse: courseid, showLoading: true });
         this.LoadCourseData(courseid);
    }

    quizchange(e, props) {
        var quizid = e.target.value;
        this.setState({ DefaultSelectedQuiz: quizid, showLoading: true });
        this.LoadQuizData(quizid);
    }
    //Filter Selections End

    //Common Function
    findById(obj, id) {
        var result;
        let that = this;
        for (var p in obj) {
            if (obj.UserID == id) {
                return obj;
            } else {
                if (typeof obj[p] === 'object') {
                    result = that.findById(obj[p], id);
                    if (result) {
                        return result;
                    }
                }
            }
        }
        return result;
    }

    BindDefaultExportColumns() {
        let Columns = [
            {
                name: "EmployeeId",
                selector: "EmployeeId",
                searchable: true,

            },
            {
                name: "Employee Name",
                selector: "UserName",
                searchable: true,

            },
            {
                name: "TL Name",
                selector: "TL_Name",
                searchable: true,

            },
            {
                name: "AM/DM Name",
                selector: "AM_Name",
                searchable: true,

            },
            {
                name: "Manager Name",
                selector: "Manager_Name",
                searchable: true,

            },
            {
                name: "Team Name",
                selector: "Team_Name",
                searchable: true,
            },

        ]
        return Columns;
    }
    BindDefaultExportData(ExportData) {

        let res = [];
        let that = this;
        if (ExportData) {
            Object.keys(ExportData).forEach(function (key) {
                try {


                    let AgentData = {};
                    if (Array.isArray(ExportData[key])) {
                        AgentData = ExportData[key][0];
                    }
                    else {
                        AgentData = ExportData[key];
                    }

                    var managerId = AgentData.managerId;
                    var tlId = AgentData.tlId;
                    var tlName = AgentData.tlName;

                    var TLdata = that.findById(that.state.AllManagerData, managerId);
                    if (TLdata && TLdata.ManagerId) {
                        var AMid = TLdata.ManagerId;
                        var AMdata, MGRdata;

                        AMdata = that.findById(that.state.AllManagerData, AMid);
                        if (AMdata && AMdata.ManagerId) {
                            var MGRid = AMdata.ManagerId;
                            MGRdata = that.findById(that.state.AllManagerData, MGRid);
                        }

                        var obj = {
                            EmployeeId: key.toUpperCase(),
                            UserName: AgentData.userName,
                            TL_Name: tlName,
                            AM_Name: AMdata == undefined ? "" : AMdata.UserName,
                            Manager_Name: MGRdata == undefined ? "" : MGRdata.UserName,
                            Team_Name: AgentData.userGroupName,

                        };

                        res.push(obj);
                    }
                    else {
                        console.log("ExportData[i]", ExportData[key]);
                        console.log("TLdata", TLdata);
                    }
                }
                catch (e) {
                    console.error(e);
                }

            });
            return res;

        }
        else {
            return null;
        }
    }
    //Common Function


    //Quiz Summary Start
    handleSummaryQuiz = (QuizValue) => {

        this.setState({ QuizMultiList: QuizValue, showLoading: true });
        var obj = [];
        for (var i = 0; i < QuizValue.length; i++) {
            obj.push({
                Id: QuizValue[i].value,
                Display: QuizValue[i].label,
            });
        }

        setTimeout(function () {
            if (obj && obj.length > 0) {
                this.LoadQuizData(obj[0].Id);
                this.setState({ QuizSelectList: obj, DefaultSelectedQuiz: obj[0].Id })
            }
        }.bind(this), 500);

    }

    LoadQuizData(quizid) {
        let QuizMultiList = this.state.QuizMultiList;
        var data = QuizMultiList.filter(item => item.value == quizid);
        var courseid = data[0].courseId;

        var json = {
            "quizId": quizid,
            "courseId": courseid,
            "userId": this.state.SelectedUsers.join(),
            "format": "summary",
            productId: this.state.ProductId
        };
        GetSummaryQuiz(json, function (results) {

            if (results && results.data && results.data.data && results.data.data["total agents"] == 0) {
                toast("No Record Found", { type: 'error' });
            }
            this.setState({ SummaryQuizData: results.data.data });
            this.CalcSummaryQuizData();

        }.bind(this));
    }

    CalcSummaryQuizData() {
        let res = [];

        let SummaryQuizData = this.state.SummaryQuizData.data;
        let that = this;
        if (SummaryQuizData) {
            Object.keys(SummaryQuizData).forEach(function (key) {


                let userQuizSummary = SummaryQuizData[key];
                for (var i = 0; i < userQuizSummary.length; i++) {
                    try {

                        var TLid = userQuizSummary[i].managerId;
                        let obj = [];
                        res.push({
                            empid: key,
                            TLId: userQuizSummary[i].tlId,
                            TLUserName: userQuizSummary[i].tlName,
                            pass: typeof userQuizSummary[i].pass == 'undefined' ? "DontAppear" : userQuizSummary[i].pass,
                            percentage: typeof userQuizSummary[i].percentage == 'undefined' ? 0 : userQuizSummary[i].percentage,
                            //score: typeof userQuizSummary[i].score == 'undefined' ? 0 : userQuizSummary[i].score,
                        });

                    }
                    catch (e) {
                        console.error(e);
                    }

                }


            });
            console.log("res", res);
            let resp = [];
            let respGrandTotal = [];
            var TLData = _.groupBy(res, 'TLId');

            Object.keys(TLData).forEach(function (key) {
                try {

                    let pass = _.countBy(TLData[key], function (item) {
                        return item.pass == "Yes";
                    });
                    let fail = _.countBy(TLData[key], function (item) {
                        return item.pass == "No";
                    });
                    let DontAppear = _.countBy(TLData[key], function (item) {
                        return item.pass == "DontAppear";
                    });
                    let Appear = DontAppear.true ? TLData[key].length - DontAppear.true : TLData[key].length;

                    let sum = _.reduce(TLData[key], function (s, f) {
                        return s + f.percentage;
                    }, 0);

                    // let SumOfScore = _.reduce(TLData[key], function (s, f) {
                    //     return s + f.score;
                    // }, 0);

                    var obj = {
                        Reportees: key.toUpperCase() + " - " + TLData[key][0].TLUserName,
                        Pass: pass.true ?? 0,
                        Fail: fail.true ?? 0,
                        DontAppear: DontAppear.true ?? 0,
                        TotalAgents: TLData[key].length,
                        Average: (sum / TLData[key].length).toFixed(1),
                        AverageScoreAttendes: Appear ? (sum / Appear).toFixed(1) : (0).toFixed(1),
                        GrandTotalSumPercentage: sum,

                    };

                    resp.push(obj);
                }
                catch (e) {
                    console.error(e);
                }
            });

            //debugger;
            var TotalAgents = _.reduce(resp,
                function (memoizer, value) {
                    return memoizer + value.TotalAgents;
                }, 0);
            var DontAppear = _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.DontAppear;
                    }, 0);  
 
            var obj = {
                Pass: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.Pass;
                    }, 0),
                Fail: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.Fail;
                    }, 0),
                  
                DontAppear: DontAppear,
                TotalAgents: TotalAgents,
                Average: ((_.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + parseFloat(value.GrandTotalSumPercentage);
                    }, 0)) / (TotalAgents)).toFixed(1),
                AvgScoreAttended: ((_.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + parseFloat(value.GrandTotalSumPercentage);
                    }, 0) )/ (TotalAgents-DontAppear)).toFixed(1),
            }

            respGrandTotal.push(obj);

            this.setState({
                QuizSummaryData: resp,
                GrandTotalData: respGrandTotal,
                CourseSummaryData: [],
                QuizData: [],
                LMSSummaryData: [],
                ExportCourseData: [],
                ExportLMSData: [], showLoading: false
            });


        }
        else {
            return null;
        }

    }
    //Quiz Summary End



    //Course Summary Start

    handleSummaryCourse = (CourseValue, startdate, enddate) => {debugger;

        this.setState({ CourseMultiList: CourseValue, showLoading: true });
        var obj = [];
        for (var i = 0; i < CourseValue.length; i++) {
            obj.push({
                Id: CourseValue[i].value,
                Display: CourseValue[i].label,
            });
        }

        setTimeout(function () {
            if (obj && obj.length > 0) {
                this.LoadCourseData(obj[0].Id, startdate, enddate);
                this.setState({ CourseSelectList: obj, DefaultSelectedCourse: obj[0].Id })
            }
        }.bind(this), 500);

    }

    LoadCourseData(courseid, startdate, enddate) {									

        var json = {
            "courseId": courseid,//coursestring.join(","),
            "userId": this.state.SelectedUsers.join(),
            "productId": this.state.ProductId,
            "startDate": startdate + " 00:00:00",
            "endDate": enddate + " 23:59:59",
            "format": "summary"
        };
         GetSummaryCourse(json, function (results) {
            //debugger;
            ;
            if (results && results.data && results.data.data && results.data.data["total agents"] == 0) {
                toast("No Record Found", { type: 'error' });

            }
            this.setState({ SummaryCourseData: results.data.data, showLoading: true });
            this.renderSummaryCourseData();

        }.bind(this));
    }


    renderSummaryCourseData() {
        //debugger;

        let res = [];

        let SummaryCourseData = this.state.SummaryCourseData.data;
        let that = this;

        if (SummaryCourseData) {
            Object.keys(SummaryCourseData).forEach(function (key) {

                let userCourseSummary = SummaryCourseData[key];
                for (var i = 0; i < userCourseSummary.length; i++) {
                    try {
                        res.push({
                            empid: key,
                            TLId: userCourseSummary[i].tlId,
                            TLUserName: userCourseSummary[i].tlName,
                            attendance: typeof userCourseSummary[i].attendance == 'undefined' ? "No" : userCourseSummary[i].attendance,
                        });
                    }
                    catch (e) {
                        console.error(e);
                    }
                }
            });
            let resp = [];
            let respGrandTotal = [];

            var TLdata = _.groupBy(res, 'TLId');

            Object.keys(TLdata).forEach(function (key) {
                try {
                    let Complete = _.countBy(TLdata[key], function (item) {
                        return item.attendance == "Yes";
                    });

                    var obj = {
                        Reportees: key.toUpperCase() + " - " + TLdata[key][0].TLUserName,
                        Complete: Complete.true ?? 0,
                        Pending: Complete.false ?? 0,
                        TotalAgents: TLdata[key].length,
                    };

                    resp.push(obj);
                }
                catch (e) {
                    console.error(e);
                }
            });

            var obj = {
                Complete: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.Complete;
                    }, 0),
                Pending: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.Pending;
                    }, 0),
                TotalAgents: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.TotalAgents;
                    }, 0),
            }

            respGrandTotal.push(obj);



            this.setState({
                CourseSummaryData: resp,
                GrandTotalData: respGrandTotal,
                QuizSummaryData: [],
                QuizData: [],
                LMSSummaryData: [],
                ExportCourseData: [],
                ExportLMSData: [], showLoading: false
            });

        }
        else {
            return null;
        }

    }

    //Course Summary End

    //LMS Summary Start
    handleSummaryLMS = (startdate, enddate) => {

        let DaysList = this.enumerateWeeksBetweenDates(startdate, enddate);
        this.setState({ DaysList: DaysList, DefaultSelectedDaysList: DaysList[0].Id, StartDate: startdate, EndDate: enddate, showLoading: true });

        setTimeout(function () {
            if (DaysList && DaysList.length > 0) {

                var str = DaysList[0].Id;
                var index = DaysList[0].Id.indexOf("_");
                var startdate = str.substr(0, index);
                var enddate = str.substr(index + 1);
                this.setState({ DropDownStartDate: startdate, DropDownEndDate: enddate });

                this.LoadLMSData(startdate, enddate);
            }
        }.bind(this), 500);

    }



    renderSummaryLMSData() {
        let res = [];

        let SummaryLMSData = this.state.SummaryLMSData;
        let that = this;

        if (SummaryLMSData) {
            let AllDates = this.enumerateDaysBetweenDates(this.state.DropDownStartDate, this.state.DropDownEndDate);

            Object.keys(SummaryLMSData).forEach(function (key) {
                try {

                    let userLMSSummary = SummaryLMSData[key];
                    if (userLMSSummary && userLMSSummary.managerId) {
                        var TLid = userLMSSummary.managerId;

                        res.push({
                            empid: key,
                            TLId: userLMSSummary.tlId,
                            TLUserName: userLMSSummary.tlName,
                            dates: typeof userLMSSummary.dates == 'undefined' ? "-" : userLMSSummary.dates,
                        });

                    }
                } catch (e) {
                    console.error(e);
                }
            });
            let resp = [];
            let respGrandTotal = [];


            var TLData = _.groupBy(res, 'TLId');
            let LMSSummaryColumns = Object.assign([], this.LMSSummaryColumns);

            AllDates.forEach(element => {
                LMSSummaryColumns.push({
                    "name": element,
                    "selector": element.replace("-", "").replace("-", "")
                });
            });

            Object.keys(TLData).forEach(function (key) {

                var list = TLData[key];
                let dates = _.pluck(list, 'dates');
                var obj = {
                    Reportees: key.toUpperCase() + " - " + TLData[key][0].TLUserName,
                    TotalAgents: TLData[key].length,
                };

                AllDates.forEach(element => {
                    var result = _.countBy(list, function (item) {
                        return item.dates.indexOf(element) > -1;
                    });

                    obj[element.replace("-", "").replace("-", "")] = result.true ?? 0
                });
                resp.push(obj);
            });

            var obj = {
                TotalAgents: _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value.TotalAgents;
                    }, 0)
                }
                AllDates.forEach(element => {
                    var col = element.replace("-", "").replace("-", "");
                    obj[col] = _.reduce(resp,
                    function (memoizer, value) {
                        return memoizer + value[col];
                    }, 0);         
                });

                respGrandTotal.push(obj);

            this.setState({
                LMSSummaryColumns: LMSSummaryColumns,
                LMSSummaryData: resp,
                GrandTotalData: respGrandTotal,
                GrandTotalColumns: LMSSummaryColumns,
                CourseSummaryData: [],
                QuizSummaryData: [],
                QuizData: [],
                ExportCourseData: [],
                ExportLMSData: [], showLoading: false
            });

        }
        else {
            return null;
        }
    }

    enumerateDaysBetweenDates(startDate, endDate) {

        var dates = [];
        dates.push(startDate);


        var currDate = moment(startDate).startOf('day');
        var lastDate = moment(endDate).startOf('day');
        while (currDate.add(1, 'days').diff(lastDate) < 0) {
            dates.push(currDate.format('YYYY-MM-DD'));
        }

        dates.push(endDate);

        return dates;
    }

    enumerateWeeksBetweenDates(startDate, endDate) {

        var dates = [];
        var currDate = moment(startDate).startOf('day');
        var lastDate = moment(endDate).startOf('day');

        while (currDate.add(6, 'days').diff(lastDate) < 0) {
            dates.push({
                Id: startDate + '_' + currDate.format('YYYY-MM-DD'),
                Display: startDate + ' to ' + currDate.format('YYYY-MM-DD'),
            });
            var startDate = currDate.add(1, 'days').format('YYYY-MM-DD');
        }
        dates.push({
            Id: startDate + '_' + endDate,
            Display: startDate + ' to ' + endDate,
        });
        return dates;
    }

    dayslistchange(e, props) {
        var str = e.target.value;
        var index = str.indexOf("_");
        var startdate = str.substr(0, index);
        var enddate = str.substr(index + 1);
        this.setState({ DefaultSelectedDaysList: str, DropDownEndDate: enddate, DropDownStartDate: startdate, LMSSummaryData: [], showLoading: true  });

        setTimeout(function () {
            this.LoadLMSData(startdate, enddate);
        }.bind(this), 500);
    }

    LoadLMSData(startdate, enddate) {

        var json = {
            "startDate": startdate + " 00:00:00",
            "userId": this.state.SelectedUsers.join(),
            "endDate": enddate + " 23:59:59",
        };
        GetSummaryLMS(json, function (results) {////debugger;
            // if (results && results.data && results.data.data && results.data.data["total agents"] == 0) {
            //     toast("No Record Found", { type: 'error' });

            // }

            this.setState({ SummaryLMSData: results.data.data });
            this.renderSummaryLMSData();

        }.bind(this));

    }
    //LMS Summary End

    //Quiz Export Start

    handleExportQuiz = (QuizValue) => {
        let QuizColumns = this.state.QuizColumns;
        var quiz = QuizValue;
        var quizid = quiz.map(function (val) {

            QuizColumns.push({
                "name": val.label,
                "selector": val.value.toString(),
            })
            return val.value;
        });

        this.setState({ showLoading: true });

        var selectedquiz = quizid.join(",");

        var courseid = quiz.map(function (val) {
            return val.courseId;
        });
        var selectedcourse = courseid.join(",");

        var json = {
            "quizId": selectedquiz,
            "courseId": selectedcourse,
            "userId": this.state.SelectedUsers.length == 0 ? null : this.state.SelectedUsers.join(),
            "productId": this.state.ProductId,
            "format": "export"
        };
        GetExportQuiz(json, function (results) {

            this.setState({ ExportQuizList: results.data.data, QuizMultiList: QuizValue, QuizColumns: QuizColumns },
                function () {
                    this.renderExportQuizData();
                }.bind(this));

        }.bind(this));

    }

    renderExportQuizData() {


        let res = [];
        let ExportQuizList = this.state.ExportQuizList;
        let QuizMultiList = this.state.QuizMultiList;
        let that = this;

        if (ExportQuizList) {
            Object.keys(ExportQuizList).forEach(function (key) {

                try {
                    var TLid = ExportQuizList[key][0].managerId;
                    var TLdata = that.findById(that.state.AllManagerData, TLid);
                    if (TLdata && TLdata.ManagerId) {
                        var AMid = TLdata.ManagerId;
                        var AMdata, MGRdata;

                        AMdata = that.findById(that.state.AllManagerData, AMid);
                        if (AMdata && AMdata.ManagerId) {
                            var MGRid = AMdata.ManagerId;
                            MGRdata = that.findById(that.state.AllManagerData, MGRid);
                        }
                        var obj = {
                            EmployeeId: key.toUpperCase(),
                            UserName: ExportQuizList[key][0].userName,
                            TL_Name: TLdata == undefined ? "" : TLdata.UserName,
                            AM_Name: AMdata == undefined ? "" : AMdata.UserName,
                            Manager_Name: MGRdata == undefined ? "" : MGRdata.UserName,
                            Team_Name: ExportQuizList[key][0].userGroupName,
                            Quiz: []
                        };


                        QuizMultiList.map(function (val) {
                            obj[val.value.toString()] = '-';
                        });

                        let userQuiz = ExportQuizList[key];
                        for (var i = 0; i < userQuiz.length; i++) {
                            if (userQuiz[i].quizId) {
                                //obj[userQuiz[i].quizId] = userQuiz[i].score
                                obj[userQuiz[i].quizId] = userQuiz[i].percentage

                            }
                        }

                        res.push(obj);
                    }
                    else {
                        console.log("ExportQuizList[i]", ExportQuizList[key][0]);
                        console.log("TLdata", TLdata);
                    }
                }
                catch (e) {
                    console.error(e);
                }

            });
            this.setState({
                QuizData: res,
                CourseSummaryData: [],
                QuizSummaryData: [],
                LMSSummaryData: [],
                ExportCourseData: [],
                ExportLMSData: [], showLoading: false
            });

        }
        else {
            return null;
        }
    }

    //Quiz Export End

    //LMS Export Start
    handleExportLMS = (startdate, enddate) => {

        let DaysList = this.enumerateWeeksBetweenDates(startdate, enddate);
        this.setState({ DaysList: DaysList, DefaultSelectedDaysList: DaysList[0].Id, StartDate: startdate, EndDate: enddate, showLoading: true });

        setTimeout(function () {
            if (DaysList && DaysList.length > 0) {

                var str = DaysList[0].Id;
                var index = DaysList[0].Id.indexOf("_");
                var startdate = str.substr(0, index);
                var enddate = str.substr(index + 1);
                this.setState({ DropDownStartDate: startdate, DropDownEndDate: enddate });

                this.LoadLMSExportData(startdate, enddate);
            }
        }.bind(this), 500);

    }

    renderExportLMSData(SummaryLMSData) {

        let res = [];
        let resp = [];

        let that = this;

        if (SummaryLMSData) {

            let res = that.BindDefaultExportData(SummaryLMSData);


            let AllDates = this.enumerateDaysBetweenDates(this.state.DropDownStartDate, this.state.DropDownEndDate);

            res.forEach(element => {
                ////debugger;
                var list = SummaryLMSData[element.EmployeeId];
                if (list == undefined) {
                    list = SummaryLMSData[element.EmployeeId.toLowerCase()];
                }
                let dates = list.dates;
                var obj = element;

                AllDates.forEach(element => {
                    ////debugger;
                    var result = _.countBy(dates, function (item) {
                        return item.indexOf(element) > -1;
                    });

                    obj[element.replace("-", "").replace("-", "")] = result.true > 0 ? "Yes" : "No";
                });
                resp.push(obj);


            });



            let ExportLMSColumns = this.BindDefaultExportColumns()

            AllDates.forEach(element => {
                ExportLMSColumns.push({
                    "name": element,
                    "selector": element.replace("-", "").replace("-", "")
                });
            });



            this.setState({
                ExportLMSColumns: ExportLMSColumns,
                ExportLMSData: resp,
                CourseSummaryData: [],
                QuizSummaryData: [],
                LMSSummaryData: [],
                QuizData: [],
                ExportCourseData: [],
                showLoading: false

            });

        }
        else {
            return null;
        }
    }



    ExportLMSdayslistchange(e, props) {
        var str = e.target.value;
        var index = str.indexOf("_");
        var startdate = str.substr(0, index);
        var enddate = str.substr(index + 1);
        this.setState({ DefaultSelectedDaysList: str, DropDownEndDate: enddate, DropDownStartDate: startdate, LMSSummaryData: [] });

        setTimeout(function () {
            this.LoadLMSExportData(startdate, enddate);
        }.bind(this), 500);
    }

    LoadLMSExportData(startdate, enddate) {
        //debugger
        var json = {
            "startDate": startdate + " 00:00:00",
            "userId": this.state.SelectedUsers.join(),
            "endDate": enddate + " 23:59:59",
        };
        GetSummaryLMS(json, function (results) {//////debugger;
            // if (results && results.data && results.data.data && results.data.data["total agents"] == 0) {
            //     toast("No Record Found", { type: 'error' });

            // }


            this.renderExportLMSData(results.data.data);

        }.bind(this));

    }


    //LMS Export End


    // Course Export Start

    handleExportCourse = (CourseValue, startdate, enddate) => {
        //debugger;
        setTimeout(function () {
            this.setState({ CourseMultiList: CourseValue, showLoading: true });
            this.LoadCourseExportData(CourseValue, startdate, enddate);
        }
            .bind(this), 500);

    }

    LoadCourseExportData = (CourseValue, startdate, enddate) => {
        //debugger;
        // var courseid = CourseValue;
        var coursestring = CourseValue.map(function (val) {
            return val.value;
        });
        var json = {
            "courseId": coursestring.join(","),//courseid,
            "userId": this.state.SelectedUsers.join(),
            "productId": this.state.ProductId,
            "startDate": startdate + " 00:00:00",
            "endDate": enddate + " 23:59:59",
            "format": "summary"
        };
        GetSummaryCourse(json, function (results) {
            ;
            if (results && results.data && results.data.data && results.data.data["total agents"] == 0) {
                toast("No Record Found", { type: 'error' });

            }
            this.renderExportCourseData(results.data.data, CourseValue);

        }.bind(this));

    }

    renderExportCourseData(SummaryCourseData, CourseValue) {
        //debugger;

        let res = [];
        let resp = [];
        let that = this;

        if (SummaryCourseData) {
            let res = that.BindDefaultExportData(SummaryCourseData.data);
            let AllCourse = CourseValue;

            res.forEach(element => {

                try {
                    var list = SummaryCourseData.data[element.EmployeeId];
                    if (list == undefined) {
                        list = SummaryCourseData.data[element.EmployeeId.toLowerCase()];
                    }

                    var obj = element;
                    AllCourse.forEach(item => {

                        let Course = item.value;
                        if (Course) {
                            let Complete = _.countBy(list, function (j) {
                                return j.courseId == Course;
                            });
                            obj[item.value.toString()] = Complete.true > 0 ? "Yes" : "No";
                        }
                        else {
                            obj[item.value.toString()] = "No";
                        }

                    });
                    resp.push(obj);




                }
                catch (e) { }

            });

            let ExportCourseColumns = this.BindDefaultExportColumns()
            //debugger;
            AllCourse.forEach(element => {
                ExportCourseColumns.push({
                    "name": element.label,
                    "selector": element.value.toString(),
                    searchable: true
                });
            });



            this.setState({
                ExportCourseColumns: ExportCourseColumns,
                ExportCourseData: resp,
                CourseSummaryData: [],
                LMSSummaryData: [],
                QuizSummaryData: [],
                QuizData: [],
                ExportLMSData: [],
                showLoading: false

            });

        }
        else {
            return null;
        }
    }


    // Course Exprt End



    renderQuizSummary() {

        if (this.state.QuizSummaryData && this.state.QuizSummaryData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={9}>
                                    <CardTitle tag="h5">Quiz Summary</CardTitle>
                                </Col>
                                <Col md={3}>
                                    <Form.Group controlId="quiz_dropdown">
                                        <DropDown firstoption="Select Quiz"
                                            items={this.state.QuizSelectList}
                                            value={this.state.DefaultSelectedQuiz}
                                            onChange={this.quizchange.bind(this)}>
                                        </DropDown>
                                    </Form.Group>
                                </Col>

                            </Row>
                        </CardHeader>
                        <CardBody>
                            {this.renderQuizGrandTotalData()}
                            <DataTable columns={this.state.QuizSummaryColumns} data={this.state.QuizSummaryData} />

                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderQuizExport() {

        if (this.state.QuizData && this.state.QuizData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>

                            <Row>
                                <Col md={12}>
                                    <CardTitle tag="h6">Quiz Data</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={this.state.QuizColumns} data={this.state.QuizData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderLMSSummary() {

        if (this.state.LMSSummaryData && this.state.LMSSummaryData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={9}>
                                    <CardTitle tag="h5">LMS Summary</CardTitle>
                                </Col>
                                <Col md={3}>
                                    <Form.Group controlId="days_dropdown">
                                        <DropDown firstoption="Select Days"
                                            items={this.state.DaysList}
                                            value={this.state.DefaultSelectedDaysList}
                                            onChange={this.dayslistchange.bind(this)}
                                        >
                                        </DropDown>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            {this.renderLMSGrandTotalData()}
                            <DataTable columns={this.state.LMSSummaryColumns} data={this.state.LMSSummaryData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderCourseSummary() {

        if (this.state.CourseSummaryData && this.state.CourseSummaryData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={9}>
                                    <CardTitle tag="h6">Course Summary</CardTitle>
                                </Col>
                                <Col md={3}>
                                    <Form.Group controlId="course_dropdown">
                                        <DropDown firstoption="Select Course"
                                            items={this.state.CourseSelectList}
                                            value={this.state.DefaultSelectedCourse}
                                            onChange={this.coursechange.bind(this)}>
                                        </DropDown>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            {this.renderCourseGrandTotalData()}
                            <DataTable columns={this.state.CourseSummaryColumns} data={this.state.CourseSummaryData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderCourseExport() {

        if (this.state.ExportCourseData && this.state.ExportCourseData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={12}>
                                    <CardTitle tag="h6">Course Export</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={this.state.ExportCourseColumns} data={this.state.ExportCourseData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderLMSExport() {

        if (this.state.ExportLMSData && this.state.ExportLMSData.length > 0) {
            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={9}>
                                    <CardTitle tag="h5">LMS Export Data</CardTitle>
                                </Col>
                                <Col md={3}>
                                    <Form.Group controlId="days_dropdown">
                                        <DropDown firstoption="Select Days"
                                            items={this.state.DaysList}
                                            value={this.state.DefaultSelectedDaysList}
                                            onChange={this.ExportLMSdayslistchange.bind(this)}
                                        >
                                        </DropDown>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={this.state.ExportLMSColumns} data={this.state.ExportLMSData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderCourseGrandTotalData() {
        let tr = [];
        let th = [];
        let GrandTotalData = this.state.GrandTotalData;
        if (GrandTotalData && GrandTotalData.length > 0) {
            GrandTotalData.forEach(element => {
                tr.push(<tr>
                    <td>Grand Total</td>
                    <td>{element.TotalAgents}</td>
                    <td>{element.Complete}</td>
                    <td>{element.Pending}</td>
                </tr>)
            });

            return <Table responsive>
                <thead className="text-primary">
                    <tr>
                        <th>Grand Total</th>
                        <th>Total Agents</th>
                        <th>Complete</th>
                        <th>Pending</th>
                    </tr>
                </thead>
                <tbody>
                    {tr}
                </tbody>
            </Table>
        }
        else {
            return null;
        }
    }

    renderQuizGrandTotalData() {
        debugger;
        let tr = [];
        let th = [];
        let GrandTotalData = this.state.GrandTotalData;
        if (GrandTotalData && GrandTotalData.length > 0) {
            GrandTotalData.forEach(element => {
                tr.push(<tr>
                    <td>Grand Total</td>
                    <td>{element.TotalAgents}</td>
                    <td>{element.DontAppear}</td>
                    <td>{element.Pass}</td>
                    <td>{element.Fail}</td>
                    <td>{element.Average}</td>
                    <td>{element.AvgScoreAttended}</td>
                    
                </tr>)
            });

            return <Table responsive>
                <thead className="text-primary">
                    <tr>
                        <th>Grand Total</th>
                        <th>Total Agents</th>
                        <th>Didn't Appear</th>
                        <th>Pass</th>
                        <th>Fail</th>
                        <th>Team Average % Score</th>
                        <th>Attendees Average % Score</th>
                    </tr>
                </thead>
                <tbody>
                    {tr}
                </tbody>
            </Table>
        }
        else {
            return null;
        }
    }

    renderLMSGrandTotalData() {debugger;
        let tr = [];
        let th = [];
        let GrandTotalData = this.state.GrandTotalData;
        let GrandTotalColumns = this.state.GrandTotalColumns.filter(task => task.selector !== 'Reportees');

        if (GrandTotalData && GrandTotalData.length > 0) {

            let th = GrandTotalColumns.map((item) => (
                  <th>{item.name}</th>
            ));

            let tr = GrandTotalColumns.map((item) => (
                <td>{GrandTotalData[0][item.selector]}</td>
            ));
               
                 return <Table responsive>
                <thead className="text-primary">                    
                    <tr>
                        <th>GrandTotal</th>
                        {th}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>GrandTotal</td>
                        {tr}
                    </tr>                    
                </tbody>
            </Table>
        }
        else {
            return null;
        }
    }

    render() {
        //const columns = this.columnlist;
        //const columns = this.fnDatatableCol();
        const moreinfocolumns = this.moreinfolist;
        const { items, PageTitle, MoodleReport, showAlert, AlertMsg, AlertVarient, ReportTime, MoreInfoData, showLoading } = this.state;
        console.log("CourseData", this.state.CourseData);
        //console.log("columns", columns);
        let selectedLeads = [];


        return (
            <>
                <div className="content">
                    <Loading show={showLoading} />
                    <ToastContainer />
                    <ManagerHierarchy
                        onExportQuiz={this.handleExportQuiz.bind(this)}
                        onExportLMS={this.handleExportLMS.bind(this)}
                        onExportCourse={this.handleExportCourse.bind(this)}
                        //onQuizValue={this.handleQuizSelect.bind(this)}
                        handleSelectManager={this.handleSelectManager.bind(this)} value={/UserID/g}
                        onSummaryQuiz={this.handleSummaryQuiz.bind(this)}
                        onSummaryCourse={this.handleSummaryCourse.bind(this)}
                        onSummaryLMS={this.handleSummaryLMS.bind(this)}
                        onProductId={this.handleProductId.bind(this)}
                    >
                    </ManagerHierarchy>
                    {this.renderQuizSummary()}
                    {this.renderCourseSummary()}
                    {this.renderLMSSummary()}
                    {this.renderQuizExport()}
                    {this.renderLMSExport()}
                    {this.renderCourseExport()}



                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetMySqlData,
        GetDataDirect,

    }
)(MoodleReport);