import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists, PostIncentiveFormData, PostAgentChatFileData, UpdateAgentChatParams
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, joinObject, getUrlParameter } from '../utility/utility.jsx';
import DropDownListMysql from './Common/DropDownListMysql';
import DropDown from './Common/DropDownList';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import DataTable from './Common/DataTableWithFilter';
import Date from "./Common/Date"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'

// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class BulkUpload extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            addClass: 'btn btn-primary',
            UserId: '',
            selectedFile: null,
            uploadFile: '',
            ManagerId: '',
            DownloadedFile: '',
            ResponseData:'',
            UploadStatusColumns: [],
            StartDate: moment().format("YYYY-MM-01"),
        };

        this.dateRangeRef = React.createRef();

    }

    componentDidMount() {
        this.UserList();
        let type = this.getType();
        this.setState({ DownloadedFile : '/SampleExcelfiles/'+type+'.xlsx'})

    }

    fnDatatableCol(columnlist) {

        var columns = fnDatatableCol(columnlist);
        return columns;
    }

    UserList() {debugger;
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ManagerId : managerid});
    }

    // On file select (from the pop up) 
    onFileChange(event) { debugger;
        console.log(event.target.files[0]);
        // Update the state 
        this.setState({ selectedFile: event.target.files[0] }); 
       
    }; 

    getType(){
        var loc = window.location.href;
        let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
        
        lastUrl = lastUrl.split('?');
        //console.log('lasturl',lastUrl[0]);
        return lastUrl[0];    }

    // On file upload (click the upload button) 
    onFileUpload(e)
    {   
        this.setState({addClass:'btn btn-primary fa fa-spinner'});
        e.preventDefault();
         debugger;
        // if (this.state.uploadFile == '') {
        // toast("Please select Upload File Type", { type: 'error' });
        // return;
        // }
        if (this.state.selectedFile == null) {
        toast("Please choose Excel File", { type: 'error' });
        return;
        }
        // Create an object of formData 
        const formData = new FormData(); 
        console.log(formData);
        // Update the formData object 
       
        var type =  this.getType();
        formData.append( 
          "myFile",                   
          this.state.selectedFile, 
          this.state.selectedFile.name 
        ); 
        formData.append('type', type); 
        formData.append('UserId', this.state.ManagerId); 
        // formData.append('ValidFrom', moment(this.state.StartDate).format("YYYY-MM-01"));    
               
        // Details of the uploaded file 
        console.log(this.state.selectedFile);      
        // Request made to the backend api 
        // Send formData object 
        PostAgentChatFileData(formData, function (results) {
            console.log(results);
            this.setState({ ResponseData: results.data.data });
            this.setState({ addClass: 'btn btn-primary' });
       
        }.bind(this));
    }
    
    fileData() 
    {      
        if (this.state.selectedFile) { 
            
          return ( 
            <div> 
              {/* <span>File Details:</span>  */}
              <span>File Name: {this.state.selectedFile.name}</span> 
              <p>File Type: {this.state.selectedFile.type}</p> 
              {/* <p> 
                Last Modified:{" "} 
                {this.state.selectedFile.lastModifiedDate.toDateString()} 
              </p>  */}
            </div> 
          ); 
        } else { 
          return ( 
            <div> 
              <br /> 
              <h4>Choose before Pressing the Upload button</h4> 
            </div> 
          ); 
        } 
    } 

    uploadfilechange(e, props){
        this.setState({
            uploadFile: e.target.value,
            DownloadedFile: '/SampleExcelfiles/FirstPremPaid.xlsx'
         });
       
    }

    renderDownloadFile(){
        if (this.state.DownloadedFile) {
            return  <Link to={this.state.DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    renderUploadStatus() {

        if (this.state.ResponseData && this.state.ResponseData.length > 0) {
            let UploadStatusColumns = [];
            let ResponseData = this.state.ResponseData;
            debugger;
            let keys = Object.keys(ResponseData[0]);
            console.log(keys);
            for(var index=0;index<keys.length;index++){
                if(keys[index] == 'status'){                       
                    UploadStatusColumns.push({ label: "Status",
                                name: "status",
                                cell: row => <div className="Status">{row.status ? row.status : "N.A"}</div>,
                    })      
                }else if(keys[index] == 'ecode'){
                    UploadStatusColumns.push({
                        "name": keys[index].toString(),
                        "label": keys[index].toString(),
                         searchable: true,  
                        }) 
                }
                else{
                    UploadStatusColumns.push({
                    "name": keys[index].toString(),
                    "label": keys[index].toString(),
                    })
                    }
            }


            const columns = this.fnDatatableCol(UploadStatusColumns);

            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={7}>
                                    <CardTitle tag="h6">Upload Data Status</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <Col md={7}>
                        <CardBody>
                            <DataTable columns={columns} data={this.state.ResponseData} />
                        </CardBody>
                        </Col>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    render() {
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>                                    
                                    <Row>
                                    <form ref="form" onSubmit={this.onFileUpload.bind(this)} id="paymentForm">
                                            <input type="file" onChange={this.onFileChange.bind(this)} /> 
                                            <button type="submit" className={this.state.addClass}>Upload!</button>
                                    </form>
                                   </Row>

                                </CardHeader>
                                <CardBody>
                                {this.renderDownloadFile()}   
                                {this.fileData()}
                                {this.renderUploadStatus()}
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>
                 </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData,
        GetCommonspData
    }
)(BulkUpload);