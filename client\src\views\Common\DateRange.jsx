
import React from "react";
import { Form } from 'react-bootstrap';

// reactstrap components
import moment from 'moment';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'


import { Row, Col } from 'react-bootstrap';



class DateRange extends React.Component {
    constructor(props) {
        super(props);
        this.handleStartDateChange = this.handleStartDateChange.bind(this);
        this.handleEndDateChange = this.handleEndDateChange.bind(this);

        let minDaterange = this.props.days!= undefined? this.props.days:30;

        if(this.props.future_endDate){
            this.state = {
                startdate: moment().format("YYYY-MM-DD 00:00:00"),
                enddate: moment().add(this.props.days, 'days').format("YYYY-MM-DD 23:59:59"),
                
            }
        }
        else{
            this.state = {
                startdate: moment().subtract(this.props.days, 'days').format("YYYY-MM-DD 00:00:00"),
                enddate: moment().format("YYYY-MM-DD 23:59:59"),
                
            }
        }
        
    }
    componentDidMount() {

    }
    componentWillReceiveProps(nextProps) {

    }

    handleStartDateChange = (e, props) => {

        let minDaterange = this.props.days!= undefined? this.props.days:30;
        if (e._isAMomentObject) {
            // const current = new Date(e._i);
            const current = new Date(e.format("YYYY-MM-DD 00:00:00"));
            const date = new Date();
            if(this.props.EndDateFixed){
                current.setDate(current.getDate()+this.props.days);
            }
            
            this.props.onStartDate(e.format("YYYY-MM-DD 00:00:00"));
            
                // this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"), 
                // enddate: e.add(this.props.days, 'days').format("YYYY-MM-DD 23:59:59") });
            if(this.props.EndDateFixed){
                if(current-date>0){
                    this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"),
                    enddate: moment().format("YYYY-MM-DD 23:59:59") });
                }
                else{
                    this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"), 
                    enddate: e.add(this.props.days, 'days').format("YYYY-MM-DD 23:59:59") });
                }
            }
            else if(this.props.futuredate){
                if(current-date>0){
                    this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"),
                    enddate: moment().add(this.props.futuredate, 'days').format("YYYY-MM-DD 23:59:59") });
                }
                else{
                    this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"), 
                    enddate: e.add(this.props.futuredate, 'days').format("YYYY-MM-DD 23:59:59") });
                }
            }
            else{
                this.setState({ startdate: e.format("YYYY-MM-DD 00:00:00"), 
                enddate: e.add(this.props.days, 'days').format("YYYY-MM-DD 23:59:59") });
            }
            
        }
    }

    handleEndDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.props.onEndDate(e.format("YYYY-MM-DD 23:59:59"));
            this.setState({ enddate: e.format("YYYY-MM-DD 23:59:59") }, function () {
                //this.fetchCallBackData();
            });
        }
    }
    validation = (currentDate) => {

        if (this.props.LimitStartdate && currentDate.isBefore(moment().subtract(this.props.LimitStartdate, "days").format("YYYY-MM-DD"))) {
            return false;
          }

        if (this.props.futuredate && currentDate.isBefore(moment().add(this.props.futuredate, "days"))) {
            return true;
        }
    
        return currentDate.isBefore(moment());
    };

    validationEndDate = (currentDate) => {  

        if(currentDate.isBefore(moment(this.state.startdate))) {
            return false;
        }
        
        if(!currentDate.isBefore(moment(this.state.startdate).add(this.props.days+1, 'days'))){
            return false;
        }
        
        // condition for EndDateFixed
        if(this.props.EndDateFixed){
            if (!currentDate.isBefore(moment())) {
                return false;
            }
        }
        
        return true;
        
    };

    getSelectedDateRange() {
        return {
            startdate: this.state.startdate,
            enddate: this.state.enddate
        }
    }

    render() {


        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <>
                <Col md={4}>
                    <Form.Group controlId="startdate_field">
                        <Form.Label>{this.props.FromDate || "From Date :"}</Form.Label>
                        <Datetime 
                        //value={new Date()}
                            dateFormat="YYYY-MM-DD"
                            value={this.state.startdate && moment(this.state.startdate).format('YYYY-MM-DD')}
                            isValidDate={this.validation.bind(this)}
                            onChange={moment => this.handleStartDateChange(moment)}
                            utc={true}
                            timeFormat={false}
                            className="form-group"

                        />
                    </Form.Group>
                </Col>
                <Col md={4}>
                    <Form.Group controlId="enddate_field">
                        <Form.Label>{this.props.ToDate || "To Date :"}</Form.Label>
                        <Datetime 
                        //value={new Date()}
                            dateFormat="YYYY-MM-DD"
                            value={this.state.enddate && moment(this.state.enddate).format('YYYY-MM-DD')}
                            isValidDate={this.validationEndDate.bind(this)}
                            onChange={moment => this.handleEndDateChange(moment)}
                            utc={true}
                            timeFormat={false}
                            className="form-group right0"
                        />
                    </Form.Group>
                </Col>
            </>

        );
    }
}


export default DateRange;

