import React from "react";
import { But<PERSON> } from 'react-bootstrap';

import { GetCommonData, GetDashboardUrl, GetCommonspData } from "../store/actions/CommonAction";


import { connect } from "react-redux";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class QuickSightTL extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "QuickSight Visualization",
      QuicksightUrl: []
    };
    this.ecode = null;
    this.mapping = {
      'TL Dashboard': {
        "DashboardId": "3ee52d5c-44e6-4f2a-b97d-e91d3846ba99",
      }
    };
  }


  componentDidMount() {
    console.log("getuser", getuser());
    let ecode = getuser().EmployeeId;
    this.ecode = ecode;
    let mapping = this.mapping;
    for (let department in mapping) {
      debugger;
      //if (mapping[department]['users'][ecode]) {
        this.setState((prevState) => ({
          QuicksightUrl: [...prevState.QuicksightUrl, {
            department
          }]
        }));
      //}
    }
  }


  handleOpen(department) {
    console.log('----------department', department);
    GetDashboardUrl({
      "userid": this.ecode,
      "DashboardId": this.mapping[department]['DashboardId']
    }, (result) => {
      console.log('-----result', result);
      if (result && result.data && result.data.QuicksightUrl) {
        window.open(result.data.QuicksightUrl, '_blank');
      }
    });
    try {
      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "InsertAgentIncentiveLog",
        c: "L",
        params: [{
          UserId: getuser().UserID,
          SuperGroupId: 0,
          ProductId: 0,
          incentiveMonth: '03-03-2021',
          Source: 'MatrixDashboard',
          PageName: 'QuickSight',
          EventName: department

        }]
      }, function () {

      })
    }
    catch (e) { }
  }

  render() {
    const { QuicksightUrl, PageTitle } = this.state;
    console.log('-------QuicksightUrl-------', QuicksightUrl);
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h3">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  {QuicksightUrl && QuicksightUrl.map((item) => {
                    return <div>
                      <h5>{item.department}</h5>
                      <Button onClick={() => this.handleOpen(item.department)} variant="primary" style={{ marginBottom: "3.5%" }}>Click to view dashboard</Button>
                    </div>
                  })}
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData


  }
)(QuickSightTL);