import React from "react";
import { GetCommonData, InsertData, UpdateData, GetCommonspData} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import RadioButton from './Common/RadioOptions';
import {getUrlParameter} from '../utility/utility.jsx';
import { If, Then, Else } from 'react-if';
import moment from 'moment';
import FeedbackQuestion from './AgentsFeedback/FeedbackQuestion';
import Success from './ITAssetSurvey/Success';

// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class AgentsFeedback extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            uid: '',
            errors: {},
            fields: {},
            hover: false,
            step: 1,
            stepOnePart: 1,
            termsconditions: '',
            question2: '',
            question3: '',
            sid: '',

        }; 
         this.handleValidation = this.handleValidation.bind(this);

    }


    componentDidMount() {
        let uid = getUrlParameter("u");
        let sid = getUrlParameter("sid");
        debugger;
        if (uid) {
            this.props.GetCommonspData({
                root: 'CheckSurveyAgent',
                c: "L",
                params: [{ "UserId": uid , "SurveyId" : sid}],
            }, function (result) {
                debugger
                if (result.data && result.data.data[0].length > 0 ) {console.log(result.data.data[0][0].Eligible,result.data.data[0][0].IsComplete)
                    if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == false) {
                    this.setState({ uid: uid, sid: sid, stepOnePart: 4 });
                } else if (result.data.data[0][0].Eligible == true && result.data.data[0][0].IsComplete == true){
                    this.setState({ stepOnePart: 3 });
                } else{
                    this.setState({ stepOnePart: 2 });
                }
            }
            }.bind(this));

        } else {
            this.setState({ stepOnePart: 2 });
        }

    }

    handleValidation = (part, values) =>  {

        let fields = this.state.fields;
        let errors = {};
        let formIsValid = true;
        if (part == 'part1') { 

        if(!values.termsconditions){
            formIsValid = false;
            errors["termsconditions"] = "This field is required";
        }

    }
     
       this.setState({errors: errors});
       return formIsValid;
   }

    showdetails() {debugger;
        this.setState({hover: true});
    }

    nextStep = () => {debugger;
        const { step } = this.state
        this.setState({
            step : step + 1
        })
    }

    prevStep = () => {
        const { step } = this.state
        this.setState({
            step : step - 1
        })
    }

    handleChange = input => event => {
        if (event.target && event.target.type == "checkbox") {
            this.setState({ [input] : event.target.checked })
        }else{
        this.setState({ [input] : event.target.value })
        }
    }

    SaveFeedbackDetails = (values) =>  {console.log(values)
        alert('Form submitted successfully');

        Object.entries(values).map(([key, value], index)=>{
            index = index + 23;
            console.log('key',key,'value',value.toString(),index+1)
            var json = { 'AnswerText': value.toString(), 
            'CreatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
            'SurveyId': this.state.sid,
            'UserId': this.state.uid,
            'QuestionId': index,
            'Description': '',
            'AnswerId': 1,
            'IsActive':1,
        }
        this.props.InsertData({
            root: "SurveyResponse",
            body: json,
            c: "L",
          });
        
          })
          // Update survey reponse completed
          let res = this.props.UpdateData({
            root: "SurveyAgentMapping",
            body: { 'IsCompleted': 1, 
            'UpdatedOn': moment().format("YYYY-MM-DD HH:mm:ss")},
            querydata: { "UserId": this.state.uid, "SurveyId": this.state.sid },
            c: "L",
          });
    }

    renderSwitch(step, values, errors, stepOnePart){debugger;
        switch(step) {
            case 1:
                if (stepOnePart == 1)
                return <i class="fa fa-spinner fa-spin"></i>

                if (stepOnePart == 2)
                return <p class="survey-response info">Invalid action</p>

                if (stepOnePart == 3)
                return <p class="survey-response info">Your survey response is already recorded</p>

                if (stepOnePart == 4)
                 return ( 
                 <div>               
                 {/* <p>We are seeking feedback from you all, to help us improve and move towards being one of the best place to work. We assure you that your feedback will remain anonymous.</p>
                 <b><i>Please rate on a scale of 1-10. Where 1 being the lowest & 10 the highest</i></b> */}
                 <FeedbackQuestion
                        nextStep={this.nextStep}
                        handleChange = {this.handleChange.bind(this)}
                        onValidation = {this.handleValidation.bind(this)}
                        SaveFeedbackDetails = {this.SaveFeedbackDetails.bind(this)}
                        values={values}
                        errors={errors}
                        />
                </div>        
                )


            case 2:
                return <Success />
        }
    }


    render() {
        const { Error, UserInfo } = this.state;
        const {step} = this.state;
        const {termsconditions, errors, uid, stepOnePart } = this.state;
        const values = { termsconditions};
        return (
        <div className="content">
            <Row>
            <Col>
            <Card className="surveyform col-md-6 col-xs-12"> 
            <CardHeader>   
            <Col md={6} className="col-md-12 text-center">
                <h3>Incentive Policy</h3>
            </Col> 
            </CardHeader>
            <CardBody>
            {this.renderSwitch(step, values, errors, stepOnePart)}            
            </CardBody>  
            </Card>           
            </Col>
            </Row>

        </div>
        )

    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        InsertData,
        UpdateData,
        GetCommonspData
    }
)(AgentsFeedback);