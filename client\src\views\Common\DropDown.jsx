
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
// reactstrap components

import {
    GetCommonData
} from "../../store/actions/CommonAction";

import { Typeahead } from 'react-bootstrap-typeahead';
import 'react-bootstrap-typeahead/css/Typeahead.css';

class DropDown extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            items: [],
        }
    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {
        this.setState({ items: nextProps.items });

    }

    displayoption(item) {
        return <option key={item.Id} value={item.Id}>{item.Display}</option>
    }

    render() {


        let { value, onChange, visible, items } = this.props;
        if (!items) {
            items = [];
        }
        if (visible == false) {
            return null;
        }
        return (

            <div>

                {this.props.AutoList == true ? <>
                    <Typeahead onChange={onChange}
                        placeholder= {this.props.firstoption||"Select"}
                        id="keep-menu-open"
                        options={items}
                        labelKey="Display"
                    />
                </>

                    :

                    <Form.Select as="select" disabled={this.props.disabled} value={value} onChange={onChange}>
                        {(this.props.firstoption !== false) && (
                            <option key={0} value={(this.props.firstoptionValue)?this.props.firstoptionValue:0}>{this.props.firstoption ? this.props.firstoption : "Select"}</option>
                        )}
                        {items && items.length > 0 && items.map(item => (
                            this.displayoption(item)
                        ))}

                    </Form.Select>
                }

            </div>

        );
    }
}


export default DropDown;
