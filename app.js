const compression = require('compression');
const newrelic = require('newrelic')
const express = require("express");
const path = require("path");
const bodyParser = require("body-parser");
const MongoClient = require("mongodb").MongoClient;
const http = require("http");
sql = require("mssql");
mysql = require('mysql');
const cors = require("cors");
const SQLdb = require("./modules/SQLdb/SQLdbRoute");
const SQLdbPBIncentive = require("./modules/SQLdbPBIncentive/SQLdbRoute");
const Mongodb = require("./modules/Mongodb/Route");
const MySqldb = require("./modules/MySqldb/Route");
const Hadoopdb = require("./modules/Hadoopdb/Route");
const RegisterCustomer = require("./modules/RegisterCustomer/Route");
const Communication = require("./modules/Communication/Route");
const RuleEngine = require("./modules/RuleEngine/Route");
const Loggerdb = require("./modules/Loggerdb/Route");
const fileUpload = require('express-fileupload');
const ApiComm = require("./modules/ApiComm/ApiCommRoute");
const DialerApi = require("./modules/DialerApi/DialerApiRoute");
const MatrixCoreApi = require("./modules/MatrixCoreAPI/MatrixCoreApiRoute");
const GMCApi = require("./modules/GMC/GMCApiRoute");
const CommonApi = require("./modules/common/Route");
const Contest = require("./modules/Contest/Route");
const FosApi = require("./modules/FosApi/FosRoute");
const SendEmailCommunication = require("./modules/Emailer/Route");
const MyBookingRouter = require("./modules/MyBooking/MyBookingApiRoute");
const IncentiveUploadRouter = require("./modules/IncentiveUpload/IncentiveUploadApiRoute");

require("./getMongoConnection");
const conf = require("./env_config");
const { Auth } = require('./auth');
const { IpAuth } = require('./IpAuth');
const { AuthHeader } = require('./AuthHeader');
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');
global.appRoot = path.resolve(__dirname);
require('dotenv').config({ path: `.${process.env.NODE_ENV}.env`});
var app = express();

const csp = require('content-security-policy');
const sts = require('strict-transport-security');
const referrerPolicy = require('referrer-policy');
const nosniff = require('dont-sniff-mimetype');
//const xXssProtection = require("x-xss-protection");
const featurePolicy = require('feature-policy');
const { consumeFosData } = require('./modules/FosTracker/FosMethods');
const { ConnectSocket } = require('./modules/FosTracker/FosSocket');
const { connectToKafka } = require('./modules/common/kafkaConnect');
const Incentive = require('./modules/RuleEngine/Incentive');

const cspPolicy = {
  'frame-ancestors': [csp.SRC_SELF, "*.policybazaar.com", "*.policybazaar.ae"],
  'object-src': [csp.SRC_NONE]
};

const globalCSP = csp.getCSP(cspPolicy);
const globalSTS = sts.getSTS({ 'max-age': { 'days': 30 }, 'includeSubDomains': true });


// Compress all HTTP responses
app.use(compression());
app.use(globalCSP);
// This will apply this policy to all requests
app.use(globalSTS);
app.use(referrerPolicy({ policy: 'strict-origin' }));
app.use(nosniff());
app.use(featurePolicy({
  features: {
    fullscreen: ["'self'"],
    vibrate: ["'self'"],
    syncXhr: ["'self' *.policybazaar.com *.policybazaar.ae"]
  }
}))

app.disable("x-powered-by");
app.set("port", conf.PORT);

app.use(cors());
app.use(fileUpload({
  limits: { fileSize: 50 * 1024 * 1024 },
  useTempFiles: true,
  tempFileDir: '/tmp/'
}));

app.use(
  bodyParser.urlencoded({
    limit: "50mb",
    extended: true
  })
);

app.use(
  bodyParser.json({
    limit: "50mb"
  })
);

app.use("/api/v1/db/", Auth, SQLdb);
app.use("/api/v1/PBIncentive/", Auth, SQLdbPBIncentive);
app.use("/html/", Auth, SQLdb);
app.use("/api/v1/registerCustomer/", Auth, RegisterCustomer);
app.use("/api/v1/mdb/", Auth, Mongodb);
app.use("/api/v1/mydb/", Auth, MySqldb);
app.use("/api/v1/comm/", Auth, Communication);

// app.use("/api/v1/ruleengine", Auth,   RuleEngine);
//app.use("/api/v1/hdb/", Auth,  Hadoopdb);
app.use("/api/v1/hdb/", IpAuth, Hadoopdb);
app.use("/api/v1/logger/", Auth, Loggerdb);
app.use("/api/v1/apicomm/", Auth, ApiComm);
app.use("/api/v1/dialerapi/", Auth, DialerApi);
app.use("/api/v1/MatrixCoreApi/", Auth, MatrixCoreApi);
app.use("/api/v1/GMC/", Auth, GMCApi);
app.use("/api/v1/Common/", Auth, CommonApi);
app.use("/api/v1/Contest/", AuthHeader, Contest);

app.use("/api/v1/Fos/", Auth, FosApi);
app.use("/api/v1/Bms/", Auth, SendEmailCommunication);
app.use("/api/v1/MyBooking/",Auth, MyBookingRouter);
app.use("/api/v1/UploadIncentive/",Auth, IncentiveUploadRouter);

app.use(express.static(path.join(__dirname, "public")));
app.use(Auth, express.static(path.join(__dirname, "client/build")));

// app.get("*", Auth, function (req, res) {
//   res.sendFile(path.join(__dirname, "client/build", "index.html"));
// });

app.use(function (err, req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, OPTIONS, PUT, PATCH, DELETE"
  );
  res.setHeader(
    "Access-Control-Allow-Headers",
    "X-Requested-With,access_token, api_key, content-type,versions"
  );
  res.setHeader("Access-Control-Allow-Credentials", true);
  res.setHeader("X-XSS-Protection", "1; mode=block");

  // res.setHeader("Content-Security-Policy", "frame-ancestors 'self' *.policybazaar.com");
  // res.setHeader("Referrer-Policy", "strict-origin");
  // res.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
  // res.setHeader("X-Content-Type-Options", "nosniff");
  // res.setHeader("X-XSS-Protection", "1; mode=block");

  if ('OPTIONS' == req.method) {
    res.sendStatus(200);
  } else {
    next();
  }
});

global.app = app;

var startServer = http.createServer(app).listen(app.get("port"), async function () {
  console.log("Server connected on port :", app.get("port"));
  await startInitialProcess();
});

global.io = require('socket.io')(startServer, { path: '/api/socketio' ,
pingInterval: 25000, 
pingTimeout: 30000,  
cors:{
origin: ["http://localhost:3000", "https://matrixdashboardqa.policybazaar.com", "https://matrixdashboard.policybazaar.com", "https://svlocalhost.policybazaar.com:3000"],
methods:["GET","POST"]
}
})

async function startInitialProcess() {
  let SecretConfig = {};
  try {
    if(process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      console.log("Database Connected Local >> ", SecretConfig?.SQL_URL?.server);
    } else {
      SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
      console.log("Database Connected :L >> ", SecretConfig?.SQL_URL?.server);
    }

    //common db connection
    commonclient = await MongoClient.connect(SecretConfig?.MONGO_URL_COMMON?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_COMMON?.user,
        password: SecretConfig?.MONGO_URL_COMMON?.password
      }
    });
    commondb = commonclient.db(SecretConfig?.MONGO_URL_COMMON?.collection);
    //health db
    healthclient = await MongoClient.connect(SecretConfig?.MONGO_URL_HEALTH?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_HEALTH?.user,
        password: SecretConfig?.MONGO_URL_HEALTH?.password
      }
    });
    healthdb = healthclient.db(SecretConfig?.MONGO_URL_HEALTH?.collection);
    //motor db
    carclient = await MongoClient.connect(SecretConfig?.MONGO_URL_MOTOR?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_MOTOR?.user,
        password: SecretConfig?.MONGO_URL_MOTOR?.password
      }
    });
    cardb = carclient.db(SecretConfig?.MONGO_URL_MOTOR?.collection);
    //oneleaddb connection
    matrixclient = await MongoClient.connect(SecretConfig?.MONGO_URL_MATRIX?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_MATRIX?.user,
        password: SecretConfig?.MONGO_URL_MATRIX?.password
      }
    });
    matrixdb = matrixclient.db(SecretConfig?.MONGO_URL_MATRIX?.collection);

    matrixdashboardclient = await MongoClient.connect(SecretConfig?.MONGO_URL_MATRIXDASHBOARD?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_MATRIXDASHBOARD?.user,
        password: SecretConfig?.MONGO_URL_MATRIXDASHBOARD?.password
      }
    });
    matrixdashboarddb = matrixdashboardclient.db(SecretConfig?.MONGO_URL_MATRIXDASHBOARD?.collection);

    fosclient = await MongoClient.connect(SecretConfig?.MONGO_URL_FOS?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_FOS?.user,
        password: SecretConfig?.MONGO_URL_FOS?.password
      }
    });
    fosdb = await fosclient.db(SecretConfig?.MONGO_URL_FOS?.collection);
    
    ruleengineclient = await MongoClient.connect(SecretConfig?.MONGO_URL_RULE?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_RULE?.user,
        password: SecretConfig?.MONGO_URL_RULE?.password
      }
    });
    ruleenginedb = ruleengineclient.db(SecretConfig?.MONGO_URL_RULE?.collection);

    loggerclient = await MongoClient.connect(SecretConfig?.MONGO_URL_LOGGER?.connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      auth: {
        user: SecretConfig?.MONGO_URL_LOGGER?.user,
        password: SecretConfig?.MONGO_URL_LOGGER?.password
      }
    });
    loggerdb = loggerclient.db(SecretConfig?.MONGO_URL_LOGGER?.collection);

    // await consumeFosData();
    // await ConnectSocket();
    // let consumer =await connectToKafka();
    // console.log("consumer is " ,consumer);
    // if(consumer)
    // {
    //     consumeFosData(consumer)
    // }
    //await sql.connect(conf.SQL_URL);
  } catch (error) {
    console.log('Inside startInitialProcess', error);
    //throw error;
  }
}

process.on("message", function (message) {
  console.log("Received signal : " + message);
  if (message === "shutdown") {
    startServer.close();
    setTimeout(function () {
      process.exit(0);
    }, 15000);
  }
});

process.on('exit', (code) => {
  console.log(`Process is about to exit with code: ${code}`);
  console.log(`SHUTTING DOWN`);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT signal. Gracefully shutting down...');
  process.exit(0);
});
