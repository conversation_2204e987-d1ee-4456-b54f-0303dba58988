import React from 'react';
import { Col, Dropdown, Form, Modal, Row } from 'react-bootstrap';
import TooglePopop from './TogglePopup';
const UnassignedLeadPopup = (props) => {
  return (
    <Modal
      {...props}
      size="md"
      aria-labelledby="contained-modal-title-vcenter"
      centered
      className="UnassignedLeadPopup"
    >
      <Modal.Header closeButton>
        <h3>Unassigned Leads</h3>
        <p>Appointment Slot</p>
        <Row className="justify-content-center">
          <Col xs={4} sm={4}>
            <Form.Select aria-label="Default select example">
              <option>8 Mar 2023</option>
              <option value="1">8 Mar 2023</option>
              <option value="2">8 Mar 2023</option>

            </Form.Select>

          </Col>
          <Col xs={5} sm={5}>
            <Form.Select aria-label="Default select example">
              <option>2:00 PM - 4:00 PM</option>
              <option value="1">8:00 PM - 10:00 PM</option>
              <option value="2">10:00 PM - 12:00 PM</option>
              <option value="3">4:00 PM - 6:00 PM</option>
            </Form.Select>
          </Col>
        </Row>
      </Modal.Header>
      <Modal.Body>
        <div className="UnassignedLead"><p><b>Lead ID : </b> 527344440
          <TooglePopop />
        </p>
        </div>
        <div className="UnassignedLead"><p><b>Lead ID : </b> 527344440
          <TooglePopop />
        </p>
        </div>
        <div className="UnassignedLead"><p><b>Lead ID : </b> 527344440
          <TooglePopop />
        </p>
        </div>
        <div className="UnassignedLead"><p><b>Lead ID : </b> 527344440
          <TooglePopop />
        </p>
        </div>

        <div className="UnassignedLead"><p><b>Lead ID : </b> 527344440
          <TooglePopop />
        </p>
        </div>

      </Modal.Body>

    </Modal>
  )

}

export default UnassignedLeadPopup;