#central-login-module-content {
  display: none;
}

.trigger-button {
  font-size: 17px;
  position: relative;
  top: 100px;
  display: block;
  margin: auto;
  padding: 10px 30px;
  cursor: pointer;
  color: #fff;
  border: 0;
  border-radius: 3px;
  outline: none;
  background: #2ecc71;
  box-shadow: 0 5px 1px #27ae60;
}

.trigger-button:hover {
  background: #27ae60;
  box-shadow: 0 5px 1px #145b32;
}

.trigger-button:focus {
  border-top: 5px solid white;
  box-shadow: none;
}

/* added for error resend */
.centarla-login-module-resentotp-message-class {
  color: #36b37e;
  display: block;
  margin-top: 4px;
}

.central-login-module-login-module-container #error-sign-in,
#error-sentotp-in {
  font-size: 12px;
  color: #DE350B;
  margin-top: 8px !important;
  display: block;
}

.central-login-module-login-module-container #error-sign-in img,
#error-sentotp-in img {
  vertical-align: middle;
  margin-right: 5px;
  margin-top: -2px;
}

.central-login-module-login-module-container button:focus {
  outline: none !important;
}

.central-login-module-login-module-container * {
  line-height: 1;
  padding: 0;
  margin: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
}

.central-login-module-login-module-container {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  overflow: hidden;
  border-radius: 32px 32px 0 0;
  background: #fff;
  z-index: 99999;
}

.central-login-module-login-module-container a {
  text-decoration: none;
}

.central-login-module-login-module-container .login-module-inner {
  padding: 41px 16px 16px;
  position: relative;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header {
  margin-bottom: 30px;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .popup-close {
  position: absolute;
  top: 24px;
  right: 16px;
  height: 18px;
  width: 18px;
  cursor: pointer;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .popup-close img {
  width: 100%;
  max-width: 100%;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper {
  -webkit-flex-basis: 60px;
  flex-basis: 60px;
  max-width: 60px;
}


.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img {
  width: 100%;
  max-width: 100%;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img.login-img {
  width: 184px;
  max-width: 184px;
  display: block;
  margin: 0 auto 38px;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img.desktop-img {
  width: 150px;
  max-width: 150px;
  margin: 0 auto 36px;
  display: block;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper {
  padding-left: 20px;
  max-width: calc(100% - 60px);
  -webkit-flex-basis: calc(100% - 60px);
  flex-basis: calc(100% - 60px);
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper h6 {
  font-size: 18px;
  line-height: 27px;
  color: #172B4D;
  font-weight: 400;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper h6 > span {
  display: block;
  font-weight: bold;
}


.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper a {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #0065FF;
  margin-top: 12px;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body {
  margin-bottom: 24px;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder {
  position: relative;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder label {
  position: absolute;
  top: 20px;
  left: 16px;
  font-size: 16px;
  color: #7A869A;
  letter-spacing: 0.25px;
  pointer-events: none;
  background: #fff;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder input {
  border-radius: 4px;
  border: 1px solid #7A869A;
  height: 56px;
  width: 100%;
  padding: 0 16px;
  line-height: 56px;
  font-size: 16px;
  color: #253858;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder input:focus {
  outline: 0;
  box-shadow: 0;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder input:focus~label {
  top: -5px;
  font-size: 12px;
  font-weight: 400;
  padding-left: 4px;
  padding-right: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper .floating-placeholder.active label {
  top: -5px;
  font-size: 12px;
  font-weight: 400;
  padding-left: 4px;
  padding-right: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper p {
  font-size: 12px;
  font-weight: 500;
  color: #000;
  margin-top: 20px;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-body .central-login-card-form-wrapper p a {
  color: #0065FF;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-footer .btn-control-block .btn-sign-in {
  border-radius: 4px;
  background-color: #DFE1E6;
  border: 1px solid #DFE1E6;
  height: 48px;
  line-height: 46px;
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  color: #7A869A;
  display: block;
  text-align: center;
  cursor: pointer;
}

.central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-footer .btn-control-block .btn-sign-in.active {
  background: #FF5630;
  border-color: #FF5630;
  color: #fff;
}

.central-login-module-login-module-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #172B4D;
  z-index: 99990;
  animation: fade-in-backdrop 0.3s linear forwards;
  -webkit-animation: fade-in-backdrop 0.3s linear forwards;
  -moz-animation: fade-in-backdrop 0.3s linear forwards;
  -ms-animation: fade-in-backdrop 0.3s linear forwards;
}

/* ============= RESPONSIVE =============== */

@media (max-width: 768px) {
  .central-login-module-login-module-container {
    animation: popup-slide-up 0.3s linear forwards;
    -webkit-animation: popup-slide-up 0.3s linear forwards;
    -moz-animation: popup-slide-up 0.3s linear forwards;
    -ms-animation: popup-slide-up 0.3s linear forwards;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img.desktop-img {
    width: 60px;
    max-width: 60px;
    margin-bottom: 0;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img.login-img {
    display: none;
  }
}


@media (min-width: 768px) {
  .central-login-module-login-module-container {
    height: 100%;
    width: 100%;
    background: transparent;
    border-radius: 0;
    max-width: 100% !important;
    animation: popup-slide-up 0.3s linear forwards;
    -webkit-animation: popup-slide-up 0.3s linear forwards;
    -moz-animation: popup-slide-up 0.3s linear forwards;
    -ms-animation: popup-slide-up 0.3s linear forwards;
  }

  .central-login-module-login-module-container .scotch-content {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%;
    max-width: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
  }

  .central-login-module-login-module-container .login-module-inner {
    padding: 36px 61px 40px;
    width: 450px;
    max-width: 450px;
    -webkit-align-self: center;
    -ms-flex-item-align: center;
    align-self: center;
    background-color: #fff;
    border-radius: 8px;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper h6 span {
    font-weight: 600;
    font-size: 24px;
  }
  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper .central-login-module-success-message h6 span {
    text-align:center !important;
    display: block;
  }
  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper h6 span span {
    display: inline;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper h6 {
    font-size: 16px;
    color: #253858;
    line-height: 36px;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .text-wrapper {
    display: block;
    max-width: 100%;
    padding-left: 0;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper img.mobile-img {
    display: none;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block .img-wrapper {
    display: block;
    max-width: 100%;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .central-login-card-header-block {
    display: block;
  }

  .central-login-module-login-module-container .login-module-inner .central-login-card .central-login-card-header .popup-close {
    top: 16px;
  }
}


@-webkit-keyframes fade-in-backdrop {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 0.95;
  }
}

@keyframes fade-in-backdrop {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 0.95;
  }
}

@media (max-width: 768px) {
  @-webkit-keyframes popup-slide-up {
    0% {
      bottom: -100%;
    }

    100% {
      bottom: 0;
    }
  }

  @keyframes popup-slide-up {
    0% {
      bottom: -100%;
    }

    100% {
      bottom: 0;
    }
  }
}

@media (min-width: 768px) {
  @-webkit-keyframes popup-slide-up {
    0% {
      -webkit-transform: scale(0);
      transform: scale(0);
      -webkit-transform-origin: center;
      transform-origin: center;
    }

    100% {
      -webkit-transform: scale(1);
      transform: scale(1);
      -webkit-transform-origin: center;
      transform-origin: center;
    }
  }

  @keyframes popup-slide-up {
    0% {
      -webkit-transform: scale(0);
      transform: scale(0);
      -webkit-transform-origin: center;
      transform-origin: center;
    }

    100% {
      -webkit-transform: scale(1);
      transform: scale(1);
      -webkit-transform-origin: center;
      transform-origin: center;
    }
  }
}

button.btn-loader {
  position: relative;
  background: #0065ff;
  border: 0;
  padding: 20px 40px;
  border-radius: 4px;
  height: 77px;
  width: 300px;
}

button.btn-loader span {
  color: #fff;
  font-size: 20px;
}

.pre__loader {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 50%;
  width: 50%;
  transform: translate(-50%, -50%);
}

.pre__loader img {
  max-height: 100%;
}

/*# sourceMappingURL=login.css.map */

/* Loader css */
button.btn-loader {
  position: relative;
  background: #0065ff;
  border: 0;
  padding: 4px 0px;
  border-radius: 4px;
  height: 77px;
  width: 300px;
}

#loader_image {
  display: none;
}

button.btn-loader span {
  color: #fff;
  font-size: 20px;
}

.pre__loader {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 50%;
  width: 50%;
  transform: translate(-50%, -50%);
}

.pre__loader img {
  max-height: 100%;
}

/* #error-sign-in{
      color: red;
      margin-top: 5px;
      display: block;
    }
    #error-sentotp-in{
      color: red;
      margin-top: 5px;
      display: block;
    } */


/* Chrome, Safari, Edge, Opera */
.central-login-module-login-module-container input::-webkit-outer-spin-button,
.central-login-module-login-module-container input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.central-login-module-login-module-container input[type=number] {
  -moz-appearance: textfield;
}