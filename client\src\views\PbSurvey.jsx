import React from "react";
import {
    GetCommonData, InsertData} from "../store/actions/CommonAction";
import {
    GetMySqlData} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, getUrlParameter } from '../utility/utility.jsx';
import RadioButton from './Common/RadioOptions';
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    Row,
    Col
} from "reactstrap";
import { Form } from 'react-bootstrap';

class PbSurvey extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            UserId: '',
            ManagerId: '',      
            UserInfo: '',
            IsSuccess: '',
            Error: false,
            answerone: '',
            answertwo: '',
            answerthree: '',
            uid: '',
            surveyFormDisplay : '',
            validUser: true,
            onSubmit: false,
            IsLoading: true,
            errors: {},
            fields: {},
            showDescriptionOne: false,
            showDescriptionTwo: false,
            showDescriptionThree: false,

        };
         this.QuestionOneAnswer = [["Always", "3_Always"], ["Mostly", "3_Mostly"], ["Sometimes", "3_Sometimes"], ["Never", "3_Never"]]   
         this.QuestionTwoAnswer = [["Always", "4_Always"], ["Mostly", "4_Mostly"], ["Sometimes", "4_Sometimes"], ["Never", "4_Never"]]   
         this.QuestionThreeAnswer = [["Very satisfied", "5_Verysatisfied"], ["Satisfied", "5_Satisfied"], ["Dissatisfied", "5_Dissatisfied"], ["Very dissatisfied", "5_Verydissatisfied"]]    
         //  this.QuestionOne = [["Do you get required support and help when needed? ", "Q1"]]    
        //  this.QuestionTwo = [["Are you listened to and treated fairly at work?", "Q2"]]    
        //  this.QuestionThree = [["Overall, are you satisfied or dissatisfied with the company?", "Q3"]]    

    }

    componentWillReceiveProps(nextProps) {
        debugger;
        if (!nextProps.CommonData.isError) {
          this.setState({ items: nextProps.CommonData['SurveyResponse'] });
          this.setState({ store: nextProps.CommonData });
         
        }
    }

    componentDidMount() {debugger;
        window.scrollTo(0, 0);
        this.UserList();
        this.props.GetCommonData({
            root: 'AesDecryption',
            uid: getUrlParameter("uid"),
        }, function (results) {
            if (results.data.status == 200) {
            this.setState({ uid: results.data.data }, () =>
            this.CheckSurveyResponseFilled());
        }else{
            this.setState({IsLoading: false, validUser : false})
        }
        }.bind(this));
    }

    CheckSurveyResponseFilled(){debugger;
        let uid = this.state.uid;
        this.props.GetCommonData({
            root: 'SurveyResponse',
            c: "L",
            con: [{ "UserId": uid }],
          }, function (result) {
            debugger;
            if(result.data && result.data.data[0].length > 0){
            this.setState({ surveyFormDisplay: false , IsLoading : false});
            }else{
            this.setState({ surveyFormDisplay: true, IsLoading : false });    
            }

          }.bind(this));

    }

    fnDatatableCol(columnlist) {

        var columns = fnDatatableCol(columnlist);
        return columns;
    }

    UserList() {debugger;
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ManagerId : managerid});
    }

    setAnswerOne(e) {
        console.log(e.target.value);
        this.setState({answerone : e.target.value, showDescriptionOne: true});
    }
    setAnswerTwo(e) {
        console.log(e.target.value);
        this.setState({answertwo : e.target.value, showDescriptionTwo: true});
    } 
    setAnswerThree(e) {
        console.log(e.target.value);
        this.setState({answerthree : e.target.value, showDescriptionThree: true});
    }
    description(){
        
    }

    handleChange(field, e){         
        let fields = this.state.fields;
        fields[field] = e.target.value;        
        this.setState({fields});
    }

    handleValidation(){
        let fields = this.state.fields;
        let errors = {};
        let formIsValid = true;

        if(!fields["bu"]){
           formIsValid = false;
           errors["bu"] = "This field is required";
        }
   
        if(!fields["supervisor"]){
           formIsValid = false;
           errors["supervisor"] = "This field is required";
        }
  
       this.setState({errors: errors});
       return formIsValid;
   }
    

    onFormSubmit(e){
        e.preventDefault();

        //if(this.handleValidation()){
          // Create an object of formData 
          const formData = new FormData(); 
          console.log(formData);
          debugger;
          const qus = document.querySelectorAll('.questions');
          var countChecked = document.querySelectorAll('input:checked').length;
            console.log(qus.length);
          var countQuestions =  qus.length; 
            if(countChecked != countQuestions){
                toast("Please enter required fields", { type: 'error' });
                return; 
            }
            alert('Form submitted successfully');

            for (const qu of qus) {
                console.log(qu);
               console.log(qu.id);
               const desc = document.getElementsByName(qu.id+'_desc');
               console.log(document.querySelectorAll('input[name='+qu.id+'_desc]'));
                    console.log(desc);
                    var description = '';
                    if(desc.length > 0){
                        var description = document.querySelectorAll('textarea[name='+qu.id+'_desc]')[0].value;
                        console.log(description);
                    }
               const radio = document.querySelectorAll('input[name='+qu.id+']:checked');

                console.log(radio[0].defaultValue);
                var selectedVal = radio[0].defaultValue;
                var qaarray = selectedVal.split('_');
                var json = { 'AnswerText': qaarray[1], 
                    'CreatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
                    'SurveyId': 1,
                    'UserId': this.state.uid,
                    'QuestionId': qaarray[0],
                    'Description': description,
                    'AnswerId': 1,
                    'IsActive':1,
               
                }
        
                  this.props.InsertData({
                    root: "SurveyResponse",
                    body: json,
                    c: "L",
                  });
            }

            // const surveyTextField = document.querySelectorAll('.surveytextfield');
            // var i = 1;
            // for (const tf of surveyTextField) {debugger;
            //     var textval = document.querySelectorAll('input[name='+tf.id+']')[0].value;
            //     console.log(textval);
            //     var json = { 'AnswerText': textval, 
            //     'CreatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
            //     'SurveyId': 1,
            //     'UserId': this.state.uid,
            //     'QuestionId': i,
            //     'Description': '',
            //     'AnswerId': 1,
            //     'IsActive':1,
           
            // }
    
            //   this.props.InsertData({
            //     root: "SurveyResponse",
            //     body: json,
            //     c: "L",
            //   });
            //   i++;
            // }
            this.setState({onSubmit : true, surveyFormDisplay: false, validUser: false });

        // }else{
        //     alert('Form not submitted');
        // }

         
    }

    renderSurveyForm(){
        var html = '';
        if(this.state.IsLoading){
            html = (<i class="fa fa-spinner fa-spin"></i>);
        }else{
        if (this.state.surveyFormDisplay && this.state.validUser){
        var html = (  
        <form ref="form" name="surveyform" onSubmit={this.onFormSubmit.bind(this)}>
          {/* <Col md={6} className="pull-left mt-4">
          <Form.Label>BU <span>*</span></Form.Label>
          <Form.Control type="text" name="bu" className="surveytextfield" id="bu" onChange={this.handleChange.bind(this, "bu")} />
          <span style={{color: "red"}}>{this.state.errors["bu"]}</span>
          </Col>
          <Col md={6} className="pull-left mt-4">
          <Form.Label>Supervisor's Name <span>*</span></Form.Label>
          <Form.Control type="text" name="supervisor" className="surveytextfield" id="supervisor" onChange={this.handleChange.bind(this, "supervisor")} />
          <span style={{color: "red"}}>{this.state.errors["supervisor"]}</span>
          </Col> */}
          <Col md={12} className="pull-left mt-4">
            <Form.Text className="questions" id="question1">
                  <p class="survey-text mb-3">How often do you get the required support and help from your manager? <span>*</span></p>
            </Form.Text>
            <RadioButton 
              name= "question1"
              changed={ this.setAnswerOne.bind(this) } 
              items = {this.QuestionOneAnswer}
              isSelected={ this.state.answerone } 
            />
           </Col>
           { this.state.showDescriptionOne ? ( <Col md={12} className="pull-left mt-4">
          <Form.Label>Description </Form.Label>
          <Form.Control as="textarea" rows={3} id="question1_desc" name="question1_desc" placeholder="Please tell us some examples of when you were not able to get the desired support, if any" onChange={this.description.bind(this)} />
          </Col>) : null }
         
          <Col md={12} className="pull-left mt-4">
          <Form.Text className="questions" id="question2">
                  <p class="survey-text mb-3">Do you feel you are listened to and treated fairly at work? <span>*</span></p>
          </Form.Text>
           <RadioButton 
              name= "question2"
              changed={ this.setAnswerTwo.bind(this) } 
              items = {this.QuestionTwoAnswer}
              isSelected={ this.state.answertwo } 
            />
            </Col>
            { this.state.showDescriptionTwo ? (<Col md={12} className="pull-left mt-4">
          <Form.Label>Description </Form.Label>
          <Form.Control as="textarea" rows={3} id="question2_desc" placeholder="Please tell us some examples of when you felt you were not being treated fairly, if any" name="question2_desc" onChange={this.description.bind(this)} />                            
          </Col>) : null }
            
          <Col md={12} className="pull-left mt-4">
          <Form.Text className="questions" id="question3">
                  <p class="survey-text mb-3">Please rate your satisfaction with the company...<span>*</span></p>
          </Form.Text>
          <RadioButton 
            name= "question3"
            changed={ this.setAnswerThree.bind(this) } 
            items = {this.QuestionThreeAnswer}
            isSelected={ this.state.answerthree } 
           />
           </Col>
           { this.state.showDescriptionThree ? ( <Col md={12} className="pull-left mt-4">
          <Form.Label>Description </Form.Label>
          <Form.Control as="textarea" rows={3} id="question3_desc" placeholder="Please let us know reason(s) for your dissatisfaction, if any" name="question3_desc" onChange={this.description.bind(this)} />                            
          </Col>) : null }
         
            <Col md={4} className="pull-left mt-4 offset-md-4 mb-4">
            <button type="submit" className="btn btn-primary">Submit</button>
          </Col>
         </form>);
        }else if(this.state.onSubmit){
            var html = (<p class="survey-response success">Your response is submitted successfully</p>);
        }else if((! this.state.validUser)){
            var html = ( <p class="survey-response invalid">Invalid Action</p>);
        }else if(!this.state.surveyFormDisplay && this.state.validUser){
            var html = ( <p class="survey-response info">Your survey response is already recorded</p>);
        }
    }
        return html;
    }

    render() {
        
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col>
                            <Card className="surveyform col-md-6 col-xs-12"> 
                            <CardHeader>   
                            <Col md={6} className="offset-md-3 mt-3 text-center">
                                <h3>Feedback Survey</h3>
                            </Col> 
                            </CardHeader>
                              <CardBody>
                              { this.renderSurveyForm() }
                              {/* <If condition={this.state.surveyFormDisplay && this.state.validUser}>
                                    <Then>
                            
                              </Then>
                                </If>
                                <If condition={(! this.state.validUser)}>
                                    <Then>
                                        <p>Invalid User</p>
                                    </Then>
                                </If>
                                <If condition={(!this.state.surveyFormDisplay && this.state.validUser)}>
                                    <Then>
                                        <p>Your survey response already recorded</p>
                                    </Then>
                                </If> */}
                              </CardBody>  

                            </Card>
                      
                        </Col>
                    </Row>
                 </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetMySqlData,
        InsertData,
    }
)(PbSurvey);