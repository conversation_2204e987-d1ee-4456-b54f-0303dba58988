<svg id="Group_273813" data-name="Group 273813" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="99.298" height="80.698" viewBox="0 0 99.298 80.698">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_40052" data-name="Rectangle 40052" width="99.298" height="80.698" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_233250" data-name="Path 233250" d="M579.678,105.15a314.77,314.77,0,0,1,28.077-26.562q1.552-3.012,3.094-5.983a371.131,371.131,0,0,0-31.171,32.545" transform="translate(-579.678 -72.605)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="-1.59" y1="2.289" x2="-1.587" y2="2.289" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0b838"/>
      <stop offset="1" stop-color="#eba626"/>
    </linearGradient>
    <clipPath id="clip-path-3">
      <path id="Path_233251" data-name="Path 233251" d="M579.678,162.853h0m0,0a239.54,239.54,0,0,1,26.26-20.373q-3.2-3.485-6.439-6.776c-6.556,8.46-13.192,17.51-19.821,27.148" transform="translate(-579.678 -135.704)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="-1.887" y1="2.545" x2="-1.884" y2="2.545" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-4">
      <path id="Path_233252" data-name="Path 233252" d="M612.378,283.227h0c-12.41,2.405-21.69,8.03-32.7,14.484a118.6,118.6,0,0,1,40.583-8c-2.443-2.123-4.345-6.538-7.3-6.538a3.084,3.084,0,0,0-.587.057" transform="translate(-579.678 -283.17)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" x1="-1.221" y1="3.885" x2="-1.219" y2="3.885" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-5">
      <path id="Path_233253" data-name="Path 233253" d="M579.678,235.328a159.946,159.946,0,0,1,25.36-12.507q.45-3.936.9-7.867a239.523,239.523,0,0,0-26.261,20.374" transform="translate(-579.678 -214.954)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-4" x1="-1.887" y1="3.059" x2="-1.884" y2="3.059" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#eba626"/>
      <stop offset="0.328" stop-color="#ecac2c"/>
      <stop offset="0.766" stop-color="#f0bc3c"/>
      <stop offset="1" stop-color="#f4c94a"/>
    </linearGradient>
    <clipPath id="clip-path-6">
      <path id="Path_233254" data-name="Path 233254" d="M216.132,81.826q1.534,2.929,3.078,5.9a307.266,307.266,0,0,1,28,25.853A363.842,363.842,0,0,0,216.132,81.826Z" transform="translate(-216.132 -81.826)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-5" x1="-0.595" y1="2.321" x2="-0.592" y2="2.321" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-7">
      <path id="Path_233255" data-name="Path 233255" d="M273.266,148.469a233.709,233.709,0,0,1,26.2,19.712c-6.627-9.473-13.249-18.36-19.785-26.651q-3.22,3.374-6.411,6.939" transform="translate(-273.266 -141.53)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-6" x1="-0.892" y1="2.574" x2="-0.889" y2="2.574" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-8">
      <path id="Path_233256" data-name="Path 233256" d="M106.779,300.214a116.165,116.165,0,0,1,40.431,6.971c-11.009-6.177-20.215-12.217-32.6-13.656q-3.953,3.192-7.828,6.684" transform="translate(-106.779 -293.53)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-7" x1="-0.226" y1="4.072" x2="-0.224" y2="4.072" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-9">
      <path id="Path_233257" data-name="Path 233257" d="M273.266,222.687l.9,7.845a155.768,155.768,0,0,1,25.3,11.867A233.707,233.707,0,0,0,273.266,222.687Z" transform="translate(-273.266 -222.687)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-8" x1="-0.892" y1="3.128" x2="-0.889" y2="3.128" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4c94a"/>
      <stop offset="0.234" stop-color="#f0bc3c"/>
      <stop offset="0.672" stop-color="#ecac2c"/>
      <stop offset="1" stop-color="#eba626"/>
    </linearGradient>
    <clipPath id="clip-path-10">
      <path id="Path_233258" data-name="Path 233258" d="M587.27,127.118q-3.792,11.045-7.592,22.348,7.455-14.7,14.885-28.4-3.636,2.906-7.293,6.053" transform="translate(-579.678 -121.065)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-9" x1="-3.33" y1="2.477" x2="-3.324" y2="2.477" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-11">
      <path id="Path_233259" data-name="Path 233259" d="M405.862,125.413q7.392,13.518,14.861,28.029-3.794-11.207-7.584-22.158Q409.49,128.23,405.862,125.413Z" transform="translate(-405.862 -125.413)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-10" x1="-2.335" y1="2.497" x2="-2.329" y2="2.497" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-12">
      <path id="Path_233260" data-name="Path 233260" d="M467.074,255.589h0q4.8,5.831,9.625,12.079h0q0-8.674,0-17.348l0,0q-4.82,2.427-9.625,5.271" transform="translate(-467.074 -250.318)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-11" x1="-1.77" y1="3.323" x2="-1.766" y2="3.323" xlink:href="#linear-gradient-4"/>
    <clipPath id="clip-path-13">
      <path id="Path_233261" data-name="Path 233261" d="M579.68,250.312v17.347l0,0h0v0q4.821-6.367,9.634-12.316-4.807-2.723-9.633-5.031Z" transform="translate(-579.678 -250.311)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-12" x1="-2.37" y1="3.135" x2="-2.366" y2="3.135" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#eba626"/>
      <stop offset="1" stop-color="#f0b838"/>
    </linearGradient>
    <clipPath id="clip-path-14">
      <rect id="Rectangle_40033" data-name="Rectangle 40033" width="94.596" height="80.693" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-15">
      <rect id="Rectangle_40032" data-name="Rectangle 40032" width="81.017" height="28.401" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-16">
      <path id="Path_233262" data-name="Path 233262" d="M154.8,127.124q-1.284,3.74-2.567,7.516-2.508-1.317-5.02-2.516-2.512,1.265-5.019,2.645-1.284-3.739-2.567-7.455-3.647-3.054-7.274-5.871,3.881,7.1,7.787,14.473-.946.537-1.891,1.1-5.436-7.356-10.821-14.19-3.221,3.374-6.411,6.939.429,3.742.858,7.487a47.435,47.435,0,0,0-7.265-1.432q-3.95,3.192-7.827,6.685a116.231,116.231,0,0,1,40.432,6.972q-1.4-.783-2.754-1.559,1.377.764,2.754,1.559,2-1.207,3.992-2.339c-1.311.764-2.637,1.545-3.992,2.339a118.591,118.591,0,0,1,40.583-8c-2.6-2.261-4.587-7.119-7.884-6.481h0a63.634,63.634,0,0,0-7.332,1.884q.444-3.889.889-7.774-3.2-3.484-6.439-6.776-5.4,6.961-10.848,14.461-.946-.53-1.892-1.048,3.909-7.467,7.8-14.663-3.636,2.908-7.293,6.053" transform="translate(-106.779 -121.071)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-13" x1="-0.113" y1="2.477" x2="-0.112" y2="2.477" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f09301"/>
      <stop offset="1" stop-color="#f0ea38"/>
    </linearGradient>
    <clipPath id="clip-path-17">
      <path id="Path_233266" data-name="Path 233266" d="M251.909,554.582h0q-1.414,2.751-2.821,5.457a338.414,338.414,0,0,0,28.433-29.687,286.465,286.465,0,0,1-25.612,24.23" transform="translate(-249.088 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-14" x1="-0.749" y1="1.191" x2="-0.746" y2="1.191" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-18">
      <path id="Path_233267" data-name="Path 233267" d="M301.481,548.938q2.921,3.178,5.875,6.181c5.979-7.719,12.032-15.975,18.078-24.767a218.666,218.666,0,0,1-23.953,18.586" transform="translate(-301.481 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-15" x1="-1.076" y1="1.427" x2="-1.073" y2="1.427" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-19">
      <path id="Path_233268" data-name="Path 233268" d="M148.627,537.657c2.373,2.062,4.185,6.5,7.191,5.912,11.319-2.194,19.787-7.325,29.83-13.212a108.15,108.15,0,0,1-37.022,7.3" transform="translate(-148.627 -530.357)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-16" x1="-0.343" y1="2.563" x2="-0.341" y2="2.563" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-20">
      <path id="Path_233269" data-name="Path 233269" d="M302.3,541.76l-.82,7.178a218.666,218.666,0,0,0,23.953-18.586A145.629,145.629,0,0,1,302.3,541.76" transform="translate(-301.481 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-17" x1="-1.076" y1="1.902" x2="-1.073" y2="1.902" xlink:href="#linear-gradient-4"/>
    <clipPath id="clip-path-21">
      <path id="Path_233270" data-name="Path 233270" d="M581.642,530.352A332.144,332.144,0,0,0,610,559.32q-1.4-2.671-2.806-5.385A280.488,280.488,0,0,1,581.642,530.352Z" transform="translate(-581.642 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-18" x1="-1.754" y1="1.22" x2="-1.751" y2="1.22" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-22">
      <path id="Path_233271" data-name="Path 233271" d="M599.708,554.664h0q2.937-3.078,5.846-6.33h0a213.153,213.153,0,0,1-23.9-17.981c6.047,8.639,12.088,16.745,18.049,24.312" transform="translate(-581.659 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-19" x1="-2.081" y1="1.454" x2="-2.078" y2="1.454" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-23">
      <path id="Path_233272" data-name="Path 233272" d="M611.4,542.813h0q3.6-2.909,7.141-6.1a105.958,105.958,0,0,1-36.88-6.359c10.043,5.635,18.44,11.143,29.738,12.456" transform="translate(-581.665 -530.357)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-20" x1="-1.348" y1="2.838" x2="-1.346" y2="2.838" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-24">
      <path id="Path_233273" data-name="Path 233273" d="M581.642,530.352a213.148,213.148,0,0,0,23.9,17.982h0q-.408-3.576-.819-7.156A142.08,142.08,0,0,1,581.642,530.352Z" transform="translate(-581.642 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-21" x1="-2.081" y1="1.966" x2="-2.077" y2="1.966" xlink:href="#linear-gradient-8"/>
    <clipPath id="clip-path-25">
      <path id="Path_233274" data-name="Path 233274" d="M422.846,556.264h0q3.318-2.65,6.652-5.521,3.461-10.075,6.925-20.387-6.8,13.406-13.577,25.907" transform="translate(-422.846 -530.357)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-22" x1="-2.663" y1="1.365" x2="-2.657" y2="1.365" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-26">
      <path id="Path_233275" data-name="Path 233275" d="M581.642,530.352q3.463,10.224,6.92,20.214,3.328,2.784,6.636,5.354h0Q588.457,543.588,581.642,530.352Z" transform="translate(-581.642 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-23" x1="-3.668" y1="1.383" x2="-3.662" y2="1.383" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-27">
      <path id="Path_233276" data-name="Path 233276" d="M581.631,546.179h0q4.4-2.214,8.781-4.809-4.379-5.32-8.781-11.018Z" transform="translate(-581.63 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-24" x1="-2.51" y1="2.178" x2="-2.505" y2="2.178" xlink:href="#linear-gradient-4"/>
    <clipPath id="clip-path-28">
      <path id="Path_233277" data-name="Path 233277" d="M478.836,541.612q4.388,2.485,8.788,4.591h0V530.375q-4.394,5.806-8.788,11.237" transform="translate(-478.836 -530.375)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-25" x1="-2.123" y1="2.066" x2="-2.118" y2="2.066" xlink:href="#linear-gradient-12"/>
    <clipPath id="clip-path-30">
      <rect id="Rectangle_40047" data-name="Rectangle 40047" width="73.909" height="25.91" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-31">
      <path id="Path_233278" data-name="Path 233278" d="M185.655,530.353q1.273.714,2.513,1.423-1.257-.7-2.513-1.423m-37.028,7.3c2.373,2.062,4.185,6.495,7.191,5.911h0a57.715,57.715,0,0,0,6.689-1.718q-.407,3.549-.812,7.092,2.921,3.179,5.875,6.182,4.921-6.351,9.895-13.193.863.484,1.728.956-3.565,6.813-7.118,13.379h0q3.317-2.65,6.652-5.521,1.171-3.413,2.344-6.856,2.288,1.2,4.579,2.3,2.291-1.154,4.58-2.413,1.171,3.41,2.341,6.8,3.328,2.785,6.637,5.355-3.54-6.475-7.1-13.2.863-.494,1.725-1,4.958,6.712,9.871,12.943,2.938-3.078,5.848-6.331l-.783-6.83a43.161,43.161,0,0,0,6.627,1.305q3.6-2.908,7.137-6.094v0a105.951,105.951,0,0,1-36.877-6.359q-1.827,1.1-3.647,2.132,1.794-1.045,3.641-2.133a108.159,108.159,0,0,1-37.022,7.3" transform="translate(-148.627 -530.352)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-26" x1="-0.172" y1="1.365" x2="-0.171" y2="1.365" xlink:href="#linear-gradient-13"/>
    <clipPath id="clip-path-32">
      <path id="Path_233282" data-name="Path 233282" d="M27.5,561.316,71.03,564.93h7.534l43.531-3.614Z" transform="translate(-27.499 -561.316)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-27" x1="-0.025" y1="9.051" x2="-0.024" y2="9.051" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-33">
      <path id="Path_233283" data-name="Path 233283" d="M27.5,385.894h0v15h94.6v-15Z" transform="translate(-27.499 -385.894)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-28" x1="-1.165" y1="0.92" x2="-1.162" y2="0.92" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4c94a"/>
      <stop offset="0.193" stop-color="#f6cf3f"/>
      <stop offset="0.654" stop-color="#fbda2f"/>
      <stop offset="1" stop-color="#fdde29"/>
    </linearGradient>
  </defs>
  <g id="Group_273812" data-name="Group 273812" clip-path="url(#clip-path)">
    <g id="Group_273745" data-name="Group 273745" transform="translate(49.561 6.208)">
      <g id="Group_273744" data-name="Group 273744" clip-path="url(#clip-path-2)">
        <rect id="Rectangle_40019" data-name="Rectangle 40019" width="31.171" height="32.545" transform="translate(0 0)" fill="url(#linear-gradient)"/>
      </g>
    </g>
    <g id="Group_273747" data-name="Group 273747" transform="translate(49.561 11.602)">
      <g id="Group_273746" data-name="Group 273746" clip-path="url(#clip-path-3)">
        <rect id="Rectangle_40020" data-name="Rectangle 40020" width="26.261" height="27.149" transform="translate(0 0)" fill="url(#linear-gradient-2)"/>
      </g>
    </g>
    <g id="Group_273749" data-name="Group 273749" transform="translate(49.561 24.21)">
      <g id="Group_273748" data-name="Group 273748" clip-path="url(#clip-path-4)">
        <rect id="Rectangle_40021" data-name="Rectangle 40021" width="40.583" height="14.541" transform="translate(0 0)" fill="url(#linear-gradient-3)"/>
      </g>
    </g>
    <g id="Group_273751" data-name="Group 273751" transform="translate(49.561 18.378)">
      <g id="Group_273750" data-name="Group 273750" clip-path="url(#clip-path-5)">
        <rect id="Rectangle_40022" data-name="Rectangle 40022" width="26.261" height="20.374" transform="translate(0 0)" fill="url(#linear-gradient-4)"/>
      </g>
    </g>
    <g id="Group_273753" data-name="Group 273753" transform="translate(18.479 6.996)">
      <g id="Group_273752" data-name="Group 273752" clip-path="url(#clip-path-6)">
        <rect id="Rectangle_40023" data-name="Rectangle 40023" width="31.082" height="31.756" transform="translate(0 0)" fill="url(#linear-gradient-5)"/>
      </g>
    </g>
    <g id="Group_273755" data-name="Group 273755" transform="translate(23.364 12.1)">
      <g id="Group_273754" data-name="Group 273754" clip-path="url(#clip-path-7)">
        <rect id="Rectangle_40024" data-name="Rectangle 40024" width="26.196" height="26.651" transform="translate(0 0)" fill="url(#linear-gradient-6)"/>
      </g>
    </g>
    <g id="Group_273757" data-name="Group 273757" transform="translate(9.129 25.096)">
      <g id="Group_273756" data-name="Group 273756" clip-path="url(#clip-path-8)">
        <rect id="Rectangle_40025" data-name="Rectangle 40025" width="40.431" height="13.656" transform="translate(0 0)" fill="url(#linear-gradient-7)"/>
      </g>
    </g>
    <g id="Group_273759" data-name="Group 273759" transform="translate(23.364 19.039)">
      <g id="Group_273758" data-name="Group 273758" clip-path="url(#clip-path-9)">
        <rect id="Rectangle_40026" data-name="Rectangle 40026" width="26.196" height="19.712" transform="translate(0 0)" fill="url(#linear-gradient-8)"/>
      </g>
    </g>
    <g id="Group_273761" data-name="Group 273761" transform="translate(49.561 10.351)">
      <g id="Group_273760" data-name="Group 273760" clip-path="url(#clip-path-10)">
        <rect id="Rectangle_40027" data-name="Rectangle 40027" width="14.885" height="28.401" fill="url(#linear-gradient-9)"/>
      </g>
    </g>
    <g id="Group_273763" data-name="Group 273763" transform="translate(34.7 10.723)">
      <g id="Group_273762" data-name="Group 273762" clip-path="url(#clip-path-11)">
        <rect id="Rectangle_40028" data-name="Rectangle 40028" width="14.861" height="28.029" transform="translate(0 0)" fill="url(#linear-gradient-10)"/>
      </g>
    </g>
    <g id="Group_273765" data-name="Group 273765" transform="translate(39.934 21.402)">
      <g id="Group_273764" data-name="Group 273764" clip-path="url(#clip-path-12)">
        <rect id="Rectangle_40029" data-name="Rectangle 40029" width="19.713" height="17.929" transform="translate(-8.212 11.471) rotate(-54.402)" fill="url(#linear-gradient-11)"/>
      </g>
    </g>
    <g id="Group_273767" data-name="Group 273767" transform="translate(49.561 21.401)">
      <g id="Group_273766" data-name="Group 273766" clip-path="url(#clip-path-13)">
        <rect id="Rectangle_40030" data-name="Rectangle 40030" width="18.946" height="19.209" transform="matrix(0.724, -0.69, 0.69, 0.724, -8.666, 8.259)" fill="url(#linear-gradient-12)"/>
      </g>
    </g>
    <g id="Group_273774" data-name="Group 273774" transform="translate(2.351 0)">
      <g id="Group_273773" data-name="Group 273773" clip-path="url(#clip-path-14)">
        <g id="Group_273772" data-name="Group 273772" transform="translate(6.778 10.351)" opacity="0.5">
          <g id="Group_273771" data-name="Group 273771">
            <g id="Group_273770" data-name="Group 273770" clip-path="url(#clip-path-15)">
              <g id="Group_273769" data-name="Group 273769" transform="translate(0 0.001)">
                <g id="Group_273768" data-name="Group 273768" clip-path="url(#clip-path-16)">
                  <rect id="Rectangle_40031" data-name="Rectangle 40031" width="81.015" height="28.401" transform="translate(0 0)" fill="url(#linear-gradient-13)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
        <path id="Path_233263" data-name="Path 233263" d="M822.887,72.588q2.132,3.987,4.246,8.056,4.135,1.881,8.183,4.092-2.611,1.681-5.266,3.505,1.13,4.432,2.254,8.887-3.9-3.391-7.884-6.481-3.645.863-7.342,1.978.451-3.936.9-7.868-3.2-3.484-6.439-6.776,4.154.136,8.256.589,1.552-3.015,3.094-5.984" transform="translate(-744.506 -66.381)" fill="#fdde29"/>
        <path id="Path_233264" data-name="Path 233264" d="M84.157,81.821q1.533,2.93,3.077,5.9,4.081-.558,8.219-.8-3.221,3.374-6.411,6.939.449,3.921.9,7.845-3.679-1.02-7.307-1.789-3.95,3.191-7.827,6.685,1.114-4.485,2.235-8.947-2.631-1.751-5.223-3.367,4.01-2.32,8.121-4.306,2.1-4.127,4.217-8.167" transform="translate(-68.03 -74.826)" fill="#fdde29"/>
        <path id="Path_233265" data-name="Path 233265" d="M420.731,0q2.293,4.066,4.588,8.228,5.159.824,10.294,2.123-3.635,2.908-7.293,6.053,1.024,5,2.045,10.028-4.808-2.723-9.634-5.031-4.821,2.428-9.627,5.272,1.02-5.049,2.04-10.08-3.648-3.054-7.275-5.871,5.114-1.426,10.274-2.379,2.292-4.22,4.589-8.344" transform="translate(-373.518 0)" fill="#fdde29"/>
      </g>
    </g>
    <g id="Group_273776" data-name="Group 273776" transform="translate(21.296 45.344)">
      <g id="Group_273775" data-name="Group 273775" clip-path="url(#clip-path-17)">
        <rect id="Rectangle_40034" data-name="Rectangle 40034" width="28.433" height="29.687" transform="translate(0 0)" fill="url(#linear-gradient-14)"/>
      </g>
    </g>
    <g id="Group_273778" data-name="Group 273778" transform="translate(25.776 45.344)">
      <g id="Group_273777" data-name="Group 273777" clip-path="url(#clip-path-18)">
        <rect id="Rectangle_40035" data-name="Rectangle 40035" width="23.953" height="24.767" transform="translate(0 0)" fill="url(#linear-gradient-15)"/>
      </g>
    </g>
    <g id="Group_273780" data-name="Group 273780" transform="translate(12.707 45.344)">
      <g id="Group_273779" data-name="Group 273779" clip-path="url(#clip-path-19)">
        <rect id="Rectangle_40036" data-name="Rectangle 40036" width="37.022" height="13.795" transform="translate(0 0)" fill="url(#linear-gradient-16)"/>
      </g>
    </g>
    <g id="Group_273782" data-name="Group 273782" transform="translate(25.776 45.344)">
      <g id="Group_273781" data-name="Group 273781" clip-path="url(#clip-path-20)">
        <rect id="Rectangle_40037" data-name="Rectangle 40037" width="23.953" height="18.586" transform="translate(0 0)" fill="url(#linear-gradient-17)"/>
      </g>
    </g>
    <g id="Group_273784" data-name="Group 273784" transform="translate(49.729 45.344)">
      <g id="Group_273783" data-name="Group 273783" clip-path="url(#clip-path-21)">
        <rect id="Rectangle_40038" data-name="Rectangle 40038" width="28.353" height="28.968" transform="translate(0 0)" fill="url(#linear-gradient-18)"/>
      </g>
    </g>
    <g id="Group_273786" data-name="Group 273786" transform="translate(49.73 45.344)">
      <g id="Group_273785" data-name="Group 273785" clip-path="url(#clip-path-22)">
        <rect id="Rectangle_40039" data-name="Rectangle 40039" width="23.895" height="24.313" transform="translate(0)" fill="url(#linear-gradient-19)"/>
      </g>
    </g>
    <g id="Group_273788" data-name="Group 273788" transform="translate(49.731 45.344)">
      <g id="Group_273787" data-name="Group 273787" clip-path="url(#clip-path-23)">
        <rect id="Rectangle_40040" data-name="Rectangle 40040" width="36.88" height="12.456" transform="translate(0 0)" fill="url(#linear-gradient-20)"/>
      </g>
    </g>
    <g id="Group_273790" data-name="Group 273790" transform="translate(49.729 45.344)">
      <g id="Group_273789" data-name="Group 273789" clip-path="url(#clip-path-24)">
        <rect id="Rectangle_40041" data-name="Rectangle 40041" width="23.897" height="17.982" transform="translate(0 0)" fill="url(#linear-gradient-21)"/>
      </g>
    </g>
    <g id="Group_273792" data-name="Group 273792" transform="translate(36.152 45.344)">
      <g id="Group_273791" data-name="Group 273791" clip-path="url(#clip-path-25)">
        <rect id="Rectangle_40042" data-name="Rectangle 40042" width="13.577" height="25.908" transform="translate(0 0)" fill="url(#linear-gradient-22)"/>
      </g>
    </g>
    <g id="Group_273794" data-name="Group 273794" transform="translate(49.729 45.344)">
      <g id="Group_273793" data-name="Group 273793" clip-path="url(#clip-path-26)">
        <rect id="Rectangle_40043" data-name="Rectangle 40043" width="13.557" height="25.568" transform="translate(0 0)" fill="url(#linear-gradient-23)"/>
      </g>
    </g>
    <g id="Group_273796" data-name="Group 273796" transform="translate(49.728 45.344)">
      <g id="Group_273795" data-name="Group 273795" clip-path="url(#clip-path-27)">
        <rect id="Rectangle_40044" data-name="Rectangle 40044" width="17.982" height="16.353" transform="matrix(0.582, -0.813, 0.813, 0.582, -7.491, 10.467)" fill="url(#linear-gradient-24)"/>
      </g>
    </g>
    <g id="Group_273798" data-name="Group 273798" transform="translate(40.939 45.346)">
      <g id="Group_273797" data-name="Group 273797" clip-path="url(#clip-path-28)">
        <rect id="Rectangle_40045" data-name="Rectangle 40045" width="17.283" height="17.52" transform="translate(-7.905 7.537) rotate(-43.636)" fill="url(#linear-gradient-25)"/>
      </g>
    </g>
    <g id="Group_273805" data-name="Group 273805" transform="translate(2.351 0)">
      <g id="Group_273804" data-name="Group 273804" clip-path="url(#clip-path-14)">
        <g id="Group_273803" data-name="Group 273803" transform="translate(10.356 45.344)" opacity="0.5">
          <g id="Group_273802" data-name="Group 273802">
            <g id="Group_273801" data-name="Group 273801" clip-path="url(#clip-path-30)">
              <g id="Group_273800" data-name="Group 273800" transform="translate(0 0)">
                <g id="Group_273799" data-name="Group 273799" clip-path="url(#clip-path-31)">
                  <rect id="Rectangle_40046" data-name="Rectangle 40046" width="73.905" height="25.909" fill="url(#linear-gradient-26)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
        <path id="Path_233279" data-name="Path 233279" d="M127.827,638.134q-1.945-3.636-3.871-7.348-3.772-1.716-7.465-3.732,2.383-1.535,4.8-3.2-1.031-4.042-2.056-8.109,3.56,3.094,7.191,5.912,3.326-.785,6.7-1.8-.41,3.592-.821,7.178,2.922,3.178,5.875,6.181-3.788-.125-7.531-.537-1.415,2.75-2.822,5.458" transform="translate(-108.882 -563.102)" fill="#fdde29"/>
        <path id="Path_233280" data-name="Path 233280" d="M803.06,627.28q-1.4-2.67-2.806-5.384-3.722.508-7.5.729,2.938-3.078,5.848-6.331-.408-3.576-.82-7.159,3.357.932,6.665,1.629,3.6-2.909,7.141-6.1-1.017,4.092-2.04,8.161,2.4,1.6,4.767,3.072-3.658,2.114-7.406,3.927-1.914,3.765-3.848,7.449" transform="translate(-727.328 -552.969)" fill="#fdde29"/>
        <path id="Path_233281" data-name="Path 233281" d="M436.426,683.586q-2.092-3.709-4.185-7.5-4.705-.749-9.39-1.935,3.318-2.651,6.653-5.522-.935-4.566-1.866-9.148,4.388,2.484,8.788,4.591,4.4-2.215,8.782-4.811-.93,4.608-1.86,9.2,3.328,2.785,6.637,5.355-4.667,1.3-9.371,2.172-2.092,3.846-4.187,7.61" transform="translate(-389.049 -602.893)" fill="#fdde29"/>
      </g>
    </g>
    <g id="Group_273807" data-name="Group 273807" transform="translate(2.351 47.991)">
      <g id="Group_273806" data-name="Group 273806" clip-path="url(#clip-path-32)">
        <rect id="Rectangle_40049" data-name="Rectangle 40049" width="94.596" height="3.613" transform="translate(0)" fill="url(#linear-gradient-27)"/>
      </g>
    </g>
    <g id="Group_273809" data-name="Group 273809" transform="translate(2.351 32.993)">
      <g id="Group_273808" data-name="Group 273808" clip-path="url(#clip-path-33)">
        <rect id="Rectangle_40050" data-name="Rectangle 40050" width="29.828" height="95.777" transform="translate(-2.351 14.62) rotate(-80.864)" fill="url(#linear-gradient-28)"/>
      </g>
    </g>
    <g id="Group_273811" data-name="Group 273811">
      <g id="Group_273810" data-name="Group 273810" clip-path="url(#clip-path)">
        <path id="Path_233284" data-name="Path 233284" d="M124.352,439.846a.312.312,0,0,0-.084-.163.29.29,0,0,0-.213-.064h-1.841v.92h3.176a.137.137,0,0,0,.118-.054.58.58,0,0,0,.059-.242h.118v1.692h-.118a.261.261,0,0,0-.059-.158.192.192,0,0,0-.118-.03h-4.937v-.118a.442.442,0,0,0,.233-.064.154.154,0,0,0,.044-.114v-4.611a.154.154,0,0,0-.044-.114.444.444,0,0,0-.233-.064v-.119h4.937a.137.137,0,0,0,.118-.054.526.526,0,0,0,.059-.223h.118v1.722h-.118a.526.526,0,0,0-.059-.223.137.137,0,0,0-.118-.054h-3.176v.821h1.84a.3.3,0,0,0,.213-.064.306.306,0,0,0,.084-.163h.118v1.543Zm3.937,1.6a.131.131,0,0,0,.079.139.719.719,0,0,0,.2.039v.118h-1.553v-.118a.623.623,0,0,0,.154-.039.131.131,0,0,0,.074-.139V437.69a.133.133,0,0,0-.074-.139.589.589,0,0,0-.154-.039v-.118h2.147v.118q-.079.01-.134.02a.056.056,0,0,0-.054.059,1.407,1.407,0,0,0,.114.287q.114.247.262.543t.277.559q.129.263.159.341.118-.237.272-.519t.287-.534q.134-.252.223-.44a.838.838,0,0,0,.089-.237.057.057,0,0,0-.054-.059q-.054-.01-.134-.02v-.118h2.157v.118a.555.555,0,0,0-.139.04q-.07.03-.069.139v3.76q0,.109.069.139a.518.518,0,0,0,.139.039v.118H130.97v-.118a.661.661,0,0,0,.183-.039.131.131,0,0,0,.074-.139V438.64h-.02l-1.474,2.859-1.425-2.859h-.02Zm10.121-.574q.2,0,.257-.247h.118v1.3h-.1q-.049-.139-.1-.159a.55.55,0,0,0-.173-.02H134v-.118a.8.8,0,0,0,.193-.039.129.129,0,0,0,.084-.139v-3.76a.131.131,0,0,0-.084-.139.766.766,0,0,0-.193-.039v-.118h4.493a.212.212,0,0,0,.118-.039q.059-.04.059-.178h.118V438.5h-.118a.237.237,0,0,0-.059-.193.192.192,0,0,0-.118-.044H135.6v.8h1.86a.2.2,0,0,0,.118-.044.221.221,0,0,0,.059-.183h.118v1.284h-.118a.221.221,0,0,0-.059-.183.2.2,0,0,0-.118-.044H135.6v.98Zm4.6-1.583a.831.831,0,0,0,.5-.134.464.464,0,0,0,.183-.4.516.516,0,0,0-.154-.381.855.855,0,0,0-.6-.151h-1.336v1.068Zm-2.652-1.6q0-.109-.069-.139a.523.523,0,0,0-.148-.039v-.118h3.176a1.868,1.868,0,0,1,1.182.346,1.223,1.223,0,0,1,.44,1.031,1.143,1.143,0,0,1-.311.846,2.174,2.174,0,0,1-.786.49l.88,1.326a.447.447,0,0,0,.144.139.37.37,0,0,0,.2.059v.118h-2v-.118a.553.553,0,0,0,.154-.025.1.1,0,0,0,.084-.1.842.842,0,0,0-.1-.247q-.1-.2-.238-.426l-.257-.435q-.124-.208-.154-.267h-.95v1.326a.132.132,0,0,0,.074.139.632.632,0,0,0,.183.04v.118h-1.7v-.118a.566.566,0,0,0,.128-.04q.069-.029.069-.139Zm10.526,3.037a.331.331,0,0,0,.049.178.154.154,0,0,0,.139.079h.089v.118q-.238.119-.543.242a6.061,6.061,0,0,1-.663.223,5.932,5.932,0,0,1-.732.154,5.16,5.16,0,0,1-.752.054,4.454,4.454,0,0,1-1.059-.118,2.1,2.1,0,0,1-.826-.391,1.867,1.867,0,0,1-.534-.691,2.386,2.386,0,0,1-.193-1,2.159,2.159,0,0,1,.218-1,1.976,1.976,0,0,1,.584-.691,2.565,2.565,0,0,1,.836-.406,3.543,3.543,0,0,1,.975-.134,4.906,4.906,0,0,1,.688.044,5.466,5.466,0,0,1,.549.1q.238.059.391.1a.948.948,0,0,0,.2.044.134.134,0,0,0,.1-.03q.025-.03.064-.069l.118.02-.514,1.306-.118-.059v-.069q0-.089-.134-.168a1.656,1.656,0,0,0-.331-.144,2.7,2.7,0,0,0-.421-.1,2.649,2.649,0,0,0-.4-.035,2.4,2.4,0,0,0-.584.069,1.266,1.266,0,0,0-.48.228,1.129,1.129,0,0,0-.321.4,1.33,1.33,0,0,0-.119.588,1.421,1.421,0,0,0,.119.608,1.131,1.131,0,0,0,.326.416,1.392,1.392,0,0,0,.485.242,2.192,2.192,0,0,0,.6.079,3.361,3.361,0,0,0,.485-.035,2.744,2.744,0,0,0,.475-.114v-.642h-.831a.137.137,0,0,0-.148.089.487.487,0,0,0-.03.148h-.118v-1.229h.118a.487.487,0,0,0,.03.148.137.137,0,0,0,.148.089h2.058Zm3.275-3.215a.913.913,0,0,0-.233.039.129.129,0,0,0-.084.139v3.76a.131.131,0,0,0,.084.139.881.881,0,0,0,.233.039v.118H152.2v-.118a.913.913,0,0,0,.233-.039.129.129,0,0,0,.084-.139v-3.76a.131.131,0,0,0-.084-.139.879.879,0,0,0-.233-.039v-.118h1.959Zm6.392,3.937a.131.131,0,0,0,.079.139.72.72,0,0,0,.2.039v.118H159v-.118a1,1,0,0,0,.183-.02q.1-.02.1-.109t-.218-.331q-.218-.242-.559-.6t-.767-.8q-.426-.44-.839-.935l-.02.02v2.592a.133.133,0,0,0,.074.139.634.634,0,0,0,.183.039v.118h-1.693v-.118a.643.643,0,0,0,.173-.039.131.131,0,0,0,.074-.139v-3.76a.131.131,0,0,0-.079-.139.7.7,0,0,0-.188-.039v-.118h2.355v.118a.555.555,0,0,0-.139.039q-.069.03-.069.139a1.109,1.109,0,0,0,.163.2q.164.183.421.46t.574.613l.633.673V437.69a.131.131,0,0,0-.079-.139.721.721,0,0,0-.2-.039v-.118h1.741v.118a.746.746,0,0,0-.2.039.131.131,0,0,0-.079.139Zm6.27-.722a.331.331,0,0,0,.049.178.154.154,0,0,0,.139.079h.089v.118q-.237.119-.543.242a6.065,6.065,0,0,1-.663.223,5.933,5.933,0,0,1-.732.154,5.158,5.158,0,0,1-.752.054,4.453,4.453,0,0,1-1.058-.118,2.081,2.081,0,0,1-.824-.391,1.866,1.866,0,0,1-.534-.691,2.385,2.385,0,0,1-.193-1,2.16,2.16,0,0,1,.218-1,1.975,1.975,0,0,1,.584-.691,2.565,2.565,0,0,1,.836-.406,3.543,3.543,0,0,1,.975-.134,4.906,4.906,0,0,1,.688.044,5.467,5.467,0,0,1,.549.1q.237.059.391.1a.948.948,0,0,0,.2.044.134.134,0,0,0,.1-.03q.025-.03.064-.069l.118.02-.514,1.306-.118-.059v-.069q0-.089-.134-.168a1.655,1.655,0,0,0-.331-.144,2.7,2.7,0,0,0-.421-.1,2.649,2.649,0,0,0-.4-.035,2.4,2.4,0,0,0-.584.069,1.266,1.266,0,0,0-.48.228,1.129,1.129,0,0,0-.321.4,1.332,1.332,0,0,0-.118.589,1.423,1.423,0,0,0,.118.608,1.132,1.132,0,0,0,.326.416,1.392,1.392,0,0,0,.485.242,2.193,2.193,0,0,0,.6.079,3.359,3.359,0,0,0,.485-.035,2.744,2.744,0,0,0,.475-.114v-.642h-.831a.137.137,0,0,0-.148.089.484.484,0,0,0-.03.148h-.118v-1.229h.118a.485.485,0,0,0,.03.148.137.137,0,0,0,.148.089h2.055Zm4.571-.435a.227.227,0,0,1,0,.044.342.342,0,0,0,0,.064q0,.1.188.188a2.67,2.67,0,0,0,.48.163,5.428,5.428,0,0,0,.633.118,4.959,4.959,0,0,0,.638.044q.089,0,.242-.01a1.349,1.349,0,0,0,.3-.054.732.732,0,0,0,.252-.128.281.281,0,0,0,.1-.232q0-.2-.238-.282a2.863,2.863,0,0,0-.6-.129l-.782-.094a3.747,3.747,0,0,1-.782-.173,1.562,1.562,0,0,1-.6-.356.866.866,0,0,1-.238-.648,1.2,1.2,0,0,1,.118-.49,1.374,1.374,0,0,1,.376-.48,2.073,2.073,0,0,1,.678-.361,3.207,3.207,0,0,1,1.024-.144,5.568,5.568,0,0,1,.678.039q.322.039.584.089t.444.089a1.416,1.416,0,0,0,.262.04.185.185,0,0,0,.128-.044q.049-.044.089-.074l.109.039-.593,1.247-.128-.059a.314.314,0,0,1,0-.049.354.354,0,0,0,0-.059q0-.04-.118-.1a2.078,2.078,0,0,0-.312-.118,3.875,3.875,0,0,0-.435-.1,2.771,2.771,0,0,0-.5-.044q-.089,0-.277,0a1.835,1.835,0,0,0-.376.049,1.193,1.193,0,0,0-.331.128.275.275,0,0,0-.144.252q0,.178.238.252a3.49,3.49,0,0,0,.6.118l.782.1a3.745,3.745,0,0,1,.782.183,1.55,1.55,0,0,1,.6.371.927.927,0,0,1,.237.678,1.233,1.233,0,0,1-.128.549,1.271,1.271,0,0,1-.4.46,1.925,1.925,0,0,1-.678.3,3.99,3.99,0,0,1-.975.1,6.007,6.007,0,0,1-.722-.044q-.366-.045-.673-.109t-.51-.118a1.813,1.813,0,0,0-.233-.054.19.19,0,0,0-.123.044,1.4,1.4,0,0,0-.1.094l-.109-.03.406-1.326Zm9.419-2.9a.212.212,0,0,0,.118-.039q.059-.04.059-.178h.118v1.446h-.118q0-.139-.059-.178a.212.212,0,0,0-.118-.04H179.4v3.047a.132.132,0,0,0,.074.139.588.588,0,0,0,.154.04v.118h-1.781v-.118a.624.624,0,0,0,.154-.04.131.131,0,0,0,.074-.139V438.4h-1.415a.212.212,0,0,0-.118.04q-.059.04-.059.178h-.118v-1.445h.118q0,.139.059.178a.212.212,0,0,0,.118.039Zm5.164,4.234a.423.423,0,0,0,.079-.01q.049-.01.049-.079a.372.372,0,0,0-.035-.123q-.034-.084-.079-.183t-.094-.2q-.049-.1-.079-.168h-2.3a1.3,1.3,0,0,1-.064.168q-.044.1-.094.2t-.089.183a.341.341,0,0,0-.039.123q0,.069.049.079a.423.423,0,0,0,.079.01v.118H181.88v-.118A.307.307,0,0,0,182,441.6a.136.136,0,0,0,.079-.074q.3-.691.6-1.37t.549-1.227l.4-.91a2.3,2.3,0,0,0,.159-.411q0-.089-.128-.1v-.118h2.07v.118q-.128.01-.128.1a1.529,1.529,0,0,0,.1.262q.1.233.272.584t.371.777l.406.851q.2.426.381.806t.287.638a.135.135,0,0,0,.079.074.306.306,0,0,0,.118.025v.118h-1.642Zm-1.325-3.2-.811,1.728h1.645Zm7,.868a.83.83,0,0,0,.5-.134.464.464,0,0,0,.183-.4.516.516,0,0,0-.154-.381.862.862,0,0,0-.6-.154h-1.335v1.068ZM189,437.69q0-.109-.069-.139a.523.523,0,0,0-.148-.039v-.118h3.176a1.869,1.869,0,0,1,1.183.346,1.223,1.223,0,0,1,.439,1.031,1.143,1.143,0,0,1-.312.846,2.173,2.173,0,0,1-.786.49l.88,1.326a.448.448,0,0,0,.144.139.37.37,0,0,0,.2.059v.118h-2v-.118a.553.553,0,0,0,.153-.025.1.1,0,0,0,.084-.1.84.84,0,0,0-.1-.247q-.1-.2-.237-.426l-.257-.435q-.123-.208-.154-.267h-.95v1.326a.132.132,0,0,0,.074.139.634.634,0,0,0,.183.04v.118h-1.7v-.118a.575.575,0,0,0,.129-.04q.07-.029.069-.139Zm6.035,2.6a.226.226,0,0,1,0,.044.342.342,0,0,0,0,.064q0,.1.188.188a2.67,2.67,0,0,0,.48.163,5.429,5.429,0,0,0,.633.118,4.959,4.959,0,0,0,.638.044q.089,0,.242-.01a1.349,1.349,0,0,0,.3-.054.732.732,0,0,0,.252-.128.281.281,0,0,0,.1-.232q0-.2-.238-.282a2.848,2.848,0,0,0-.6-.128l-.782-.094a3.747,3.747,0,0,1-.782-.173,1.563,1.563,0,0,1-.6-.356.866.866,0,0,1-.238-.648,1.2,1.2,0,0,1,.118-.49,1.375,1.375,0,0,1,.376-.48,2.072,2.072,0,0,1,.678-.361,3.207,3.207,0,0,1,1.024-.144,5.568,5.568,0,0,1,.678.039q.322.04.584.089t.444.089a1.419,1.419,0,0,0,.262.04.184.184,0,0,0,.128-.044q.049-.044.089-.074l.109.039-.592,1.247-.128-.059a.314.314,0,0,1,0-.049.354.354,0,0,0,0-.059q0-.04-.118-.1a2.077,2.077,0,0,0-.311-.118,3.882,3.882,0,0,0-.436-.1,2.77,2.77,0,0,0-.5-.044q-.089,0-.277,0a1.837,1.837,0,0,0-.376.049,1.193,1.193,0,0,0-.331.128.275.275,0,0,0-.144.252q0,.178.238.252a3.49,3.49,0,0,0,.6.119l.782.1a3.743,3.743,0,0,1,.782.183,1.548,1.548,0,0,1,.6.371.927.927,0,0,1,.237.678,1.229,1.229,0,0,1-.129.549,1.276,1.276,0,0,1-.4.46,1.927,1.927,0,0,1-.678.3,3.991,3.991,0,0,1-.975.1,6,6,0,0,1-.722-.044q-.366-.044-.673-.109t-.51-.119a1.816,1.816,0,0,0-.232-.054.189.189,0,0,0-.123.044,1.394,1.394,0,0,0-.1.094l-.109-.03.406-1.326Z" transform="translate(-110.155 -398.966)" fill="#67352f"/>
      </g>
    </g>
  </g>
</svg>
