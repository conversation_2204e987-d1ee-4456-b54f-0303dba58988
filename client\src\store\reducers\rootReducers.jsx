import { combineReducers } from "redux";

import AlertMasterReducer from "./AlertMasterReducer";
import CommonReducer from "./CommonReducer";
import AppReducer from './RuleReducer/AppReducer';
import RulesetReducer from './RuleReducer/RulesetReducer';

const rootReducer = combineReducers({
    AlertMaster : AlertMasterReducer,
    CommonData : CommonReducer,
    App : AppReducer,
    Ruleset : RulesetReducer
});

export default rootReducer;
