import React from "react";
import {
  GetCommonData, GetCommonspData, PostCommunicationData,ValidateAddLeadToPriorityQueue
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getuser } from '../utility/utility.jsx';
import { ToastContainer, toast } from 'react-toastify';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
//import { If, Then, Else } from 'react-if';
import './customStyling.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
//import { func } from "prop-types";
//import moment from "moment";
import DropDownListMysql from './Common/DropDownListMysql';
import { If, Then, Else } from 'react-if';


class SmeDashBoardTrackLead extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "SME Track Leads",
      SmeQuotes: [],
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      podType: 0,
      IsLoading: true
    };
    //this.AddLeads = this.AddLeads.bind(this);
    this.dtRef = React.createRef();
    this.myInputRef = React.createRef();
    this.AgentType = null;

    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadID",
        searchable: false,
      },
      {
        name: "Customer Name",
        selector: "Name",
      },
      {
        name: "Quote Substatus",
        selector: "QStatusName",
      },
      {
        name: "Last Called",
        selector: "lastCalltime",
        cell: row => (!row.lastCalltime) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.lastCalltime}</Moment>

      },
      {
        name: "CallbackSetFor",
        selector: "Callback",
        cell: row => (!row.Callback) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.Callback}</Moment>
      },
      {
        name: "InsurerName",
        selector: "InsurerName",
      },
      {
        name: "L1 Premium",
        selector: "Price",
      }
    ];

  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetSmeQuotesByAgentId"]) {

        let list = nextProps.CommonData["GetSmeQuotesByAgentId"] || [];
        let QuotesList = [];
        if (list != null && list.length > 0) {
          QuotesList = list[0];
        }

        this.setState({ SmeQuotes: QuotesList, IsLoading: false });
      }
    }
  }

  componentDidMount() {
    this.fetchSmeData();
  }

  fetchSmeData() {
    var userid = getuser().UserID;
    var date = new Date();
   // userid = '90303'
    date.setDate(date.getDate() - 30);

    this.props.GetCommonspData({
      root: 'GetSmeQuotesByAgentId',
      params: [{ agentId: userid, datetime: date }]
    });
  }

  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }

  AddLeads() {
    const { SelectedRows } = this.state;
    for (let index = 0; index < SelectedRows.length; index++) {
      const element = SelectedRows[index];

      var lead = {
        "LeadId": element.LeadID,
        "Name": element.Name,
        "CustomerId": element.CustomerId,
        "UserID": parseInt(getuser().UserID),
        "Priority": 0,
        "ProductId": element.ProductId,
        "Reason": 'Manual added',
        "ReasonId": 33,
        "CallStatus": "",
        "IsAddLeadtoQueue":1,
        "IsNeedToValidate":0
      }
      var reqData = {
        "UserId": parseInt(getuser().UserID),
        "Leads": [lead]
      };

      ValidateAddLeadToPriorityQueue(reqData , function (resultData)
        {
          try{
            if (resultData != null) {
              if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
                  toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
              }
              toast(`${resultData.data.data.message}`, { type: 'error' });
            }
          }catch(e){
            toast(`${e}`, { type: 'error' });
          console.log(e)
        }
        }.bind(this));

      // this.props.PostCommunicationData({
      //   root: 'communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
      //   data: reqData
      // }, function (data) {
      //   toast("Lead (" + element.LeadID + ") Added in Queue", { type: 'success' });
      // });
    }

    // this.setState({ showAssignLeadPopUp: false });

    // setTimeout(function () {
    //   this.fetchCallBackData();
    // }.bind(this), 300);
  }

  clearSelectedRows() {
    this.dtRef.current.handleClearRows();
    this.setState({ SelectedRows: [] });
  }

  render() {
    const columns = this.columnlist;
    const { items, PageTitle, SmeQuotes } = this.state;

    return (
      <>
        <div className="content UserPODLeadsContainer">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={9}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <If condition={this.state.IsLoading}>
                    <Then>
                      <i className="fa fa-spinner fa-spin"></i>
                    </Then>
                    <Else>
                      <DataTable
                        columns={columns}
                        data={SmeQuotes}
                        defaultSortField="ReAssignedon"
                        defaultSortAsc={false}
                        export={false}
                        selectableRows={true}
                        ref={this.dtRef}
                        onSelectedRows={this.onSelectedRows.bind(this)}
                      />
                    </Else>
                  </If>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
        <div style={{ marginLeft : '550px'}}>
          <If condition={this.state.SelectedRows.length > 0}>
            <Then>
              <Button variant="primary" onClick={this.AddLeads.bind(this)}>Add Leads</Button>
            </Then>
          </If>{' '}
          <If condition={this.state.SelectedRows.length > 0}>
            <Then>
              <Button variant="secondary" onClick={this.clearSelectedRows.bind(this)}>Clear</Button>
            </Then>
          </If>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    PostCommunicationData
  }
)(SmeDashBoardTrackLead);