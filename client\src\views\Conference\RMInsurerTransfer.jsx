
import React from "react";
import {
  TransferCallThirdParty,
  MergeCallThirdParty, UnHoldCallThirdParty
} from "../../store/actions/CommonAction";
import {
  addRecord, GetCommonData
} from "../../store/actions/CommonMongoAction";
import moment from "moment";

import { connect } from "react-redux";
import DataTable from '../Common/DataTableWithFilter';
import AlertBox from '../Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getUrlParameter, fnBindRootData, fnDatatableCol } from '../../utility/utility.jsx';
import message from "../../Configs/message.jsx";
import Loader from '../Common/Loader';
import _ from 'underscore';
import {
  Card, CardHeader, CardBody, CardTitle, Row, Col
} from "reactstrap";
import { getuser } from "utility/utility";
import ConfirmDialog from '../Common/ConfirmDialog';
import AlertDialog from '../Common/AlertDialog';
import '../customStyling.css';

class RMInsurerTransfer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      items: [],
      AgentData: [],
      activePage: 1,
      root: "RMInsurerList",
      PageTitle: "RM Insurer List",
      winactive: 0,
      AgentCode: '',
      TransferLoader: false,
      MergeLoader: false,
      UnholdLoader: false,
      CallAgentLoader: false,
      grade: null,
      DIDNo: '',
      SalesAgentIp: '',
      IsConCall: false,
      IsConferenceSuccussful: false,
      ConfirmationOpen: false,
      selectedRow: null,
      highlightTransferBtn: false,
      highlightMergeBtn: false,
      InitialWarningPopup: true,
      lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      RMInsurerList: []
    };

    this.schdular = null;
    this.winactive = 0;

    this.StopAgentListSchedular = false;

    this.columnlist = [

      {
        label: "Area",
        name: "Region",
        sortable: true,
      },
      {
        label: "Name & Empcode",
        name: "EmpCode",
        sortable: true,
        cell: row => row.EmpCode ? row.EmpName + ' (' + row.EmpCode + ')' : "N.A",
      },
      {
        label: "Action",
        cell: row =>
          <div className="moreinfo">{<button id="blockagent" className="btn btn-primary btn-sm" onClick={(e) => this.showConfirmationModal(e, row)}>
            Call
          </button>}
          </div>
      },

    ];


  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    this.savePageLogs();
    this.getRMInsurerList();
  }

  getRMInsurerList() {
    this.props.GetCommonData({
      root: this.state.root,
      c: "MATRIX_DASHBOARD_CLIENT",
    });
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {

      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.setState({ store: nextProps.CommonData });
    }

  }


  savePageLogs() {
    this.props.addRecord({
      root: "DashboardLogs",
      body: {
        module: this.state.root,
        url: window.location.href,
        request: { "querystring": this.props.location?.search },
        response: {},
        on: moment().format("YYYY-MM-DD HH:mm:ss"),
        by: getuser().UserID
      }
    });
  };

  hideConfirmationModal() {
    this.setState({ ConfirmationOpen: false, selectedRow: null });
  }

  showConfirmationModal(e, row) {
    this.setState({ ConfirmationOpen: true, selectedRow: row });
  }

  handleConfirmOk(e) {
    this.clickTransfer();
  }


  clickTransfer() {
    var transfer = document.getElementById("transfer");
    var merge = document.getElementById("merge");
    var unhold = document.getElementById("unhold");
    var grade = getUrlParameter("grade");
    let campaign = getUrlParameter("campaign");
    let transfer_type = getUrlParameter("transfer_type");
    let bookingid = getUrlParameter("bookingid");

    this.setState({ highlightTransferBtn: false });


    this.setState({ TransferLoader: true, grade: grade });
    //transfer.classList.add("disabled");

    let agent = getUrlParameter("agent");
    //let thirdpartynumber = tpn[0].number;
    let thirdpartynumber = this.state.selectedRow['ContactNo']
    let isCreateLead = getUrlParameter("iscreatelead");
    //let bookingid = getUrlParameter("bookingid");
    let dtmf_no = getUrlParameter("dtmf_no");
    let insurer = getUrlParameter("insurer");
    let application_number = getUrlParameter("application_number");
    let transfer_agents = getUrlParameter("transfer_agents");

    let claimid = getUrlParameter("claimid");
    let claim_callid = getUrlParameter("claim_callid");
    let productid = getUrlParameter("productid");
    let salesagent = getUrlParameter("salesagent");
    let isenc = getUrlParameter("isenc");
    let countrycode = getUrlParameter("countrycode");
    let dialercode = getUrlParameter("dialercode");
    var context = getUrlParameter("campaign");

    if (getUrlParameter("customernumber")) {
      thirdpartynumber = getUrlParameter("customernumber");
    }

    let blockedAgentid = this.state.AgentCode;
    if (!grade) {
      grade = '';
    }
    TransferCallThirdParty(agent, thirdpartynumber, campaign, bookingid, transfer_type, dtmf_no, insurer,
      application_number, transfer_agents, grade, blockedAgentid, claimid, claim_callid, productid, salesagent, isenc, countrycode, dialercode, isCreateLead, function (results) {

        if (results && results.data && results.data.status) {
          //debugger;
          this.setState({ TransferLoader: false, ConfirmationOpen : false });

          if (results.data.status == 200) {
            toast("Success, Call is being transferred", { type: 'success' });

            //In case of POD 
            this.setState({ IsConferenceSuccussful: true, highlightMergeBtn: true });

          }
          else {

            let ResponseMessage = _.where(message.status, { status: results.data.status })
            if (ResponseMessage.length > 0) {
              toast(ResponseMessage[0].message, { type: 'error' });
            } else {
              toast(results.data.message, { type: 'error' });
            }

          }

        }
        this.setState({ TransferData: results.data });
      }.bind(this));

  }

  clickMerge() {
    var merge = document.getElementById("merge");
    merge.classList.add("disabled");
    this.setState({ MergeLoader: true, highlightMergeBtn: false });

    let agent = getUrlParameter("agent");
    //var transfer_type = getUrlParameter("transfer_type");
    //var context = getUrlParameter("campaign");

    MergeCallThirdParty(agent, function (results) {
      if (results && results.data && results.data.status) {
        //debugger;
        this.setState({ MergeLoader: false });
        merge.classList.remove("disabled");

        if (results.data.status == 200) {
          toast(results.data.message, { type: 'success' });
        }
        else {
          toast(results.data.message, { type: 'error' });
        }

      }
      this.setState({ MergeData: results.data });

    }.bind(this));

  }

  clickUnHold() {
    var unhold = document.getElementById("unhold");
    var merge = document.getElementById("merge");
    //merge.classList.add("disabled");
    unhold.classList.add("disabled");
    this.setState({ UnholdLoader: true, highlightMergeBtn: false });

    let agent = getUrlParameter("agent");

    UnHoldCallThirdParty(agent, function (results) {
      unhold.classList.remove("disabled");
      this.setState({ UnholdLoader: false });
      if (results && results.data && results.data.status) {
        if (results.data.status == 200) {
          toast('Success, Call is Unhold.', { type: 'success' });
        }
        else {
          toast(results.data.message, { type: 'error' });
        }
      }
      this.setState({ UnHoldData: results.data });
    }.bind(this));
  }

  CheckLoader(action) {
    if (action == 'transfer') {
      if (this.state.TransferLoader)
        return <Loader />;
    } else if (action == 'merge') {
      if (this.state.MergeLoader)
        return <Loader />;
    } else if (action == 'unhold') {
      if (this.state.UnholdLoader)
        return <Loader />;
    } else if (action == 'callagent') {
      if (this.state.CallAgentLoader)
        return <Loader />;
    }

  }

  renderTransferData() {

    var transferheading = 'RM Insurer Transfer'


    var context = getUrlParameter("campaign");
    var colSize = 4;

    return <Row>
      <Col md="12">
        <Card>
          <CardHeader>
            <Row>
              <Col md={12}>
                <CardTitle tag="h5">
                  {/* <center> */}
                  {transferheading}
                  {/* </center> */}
                </CardTitle>
              </Col>
            </Row>
          </CardHeader>
          <CardBody>
            <Row>
              <Col md={colSize}>
                <button id="merge" onClick={(e) => this.clickMerge()} className={this.state.highlightMergeBtn == true ? "btn btn-primary full-width blink" : "btn btn-primary full-width"}>Merge Call {this.CheckLoader('merge')}</button>
              </Col>
              <Col md={colSize}>
                <button id="unhold" onClick={(e) => this.clickUnHold()} className="btn btn-primary full-width">UnHold {this.CheckLoader('unhold')}</button>
              </Col>
            </Row>
            <Row>
              <Col md={8} className="BlockAgentNoticeMsg align-left">
                <br />
                Please brief the new RM Insurer with customer details before merging the call into conference.
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Col>
    </Row>

  }

  handleInitialWarningPopup(e) {
    this.setState({ InitialWarningPopup: false });
  }

  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }



  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>

          {(
            this.state.InitialWarningPopup &&
            <AlertDialog
              onConfirm={(e) => this.handleInitialWarningPopup(e)}
              onCancel={(e) => this.handleInitialWarningPopup(e)}
              buttonLabel="OK"
              title="Confirm"
              message="Please make sure you have informed the customer about merging this call with RM Insurer"
              show={true}
            />
          )}

          {this.renderTransferData()}
          {(
            <Row>
              <Col md="12">
                <Card>

                  <CardHeader>
                    <Row>
                      <Col md={7}>

                        <CardTitle tag="h5">
                          {PageTitle}
                        </CardTitle>
                      </Col>
                    </Row>
                  </CardHeader>

                  <CardBody className="BlockAgentContainer">
                    {(
                      this.state.ConfirmationOpen &&
                      <ConfirmDialog
                        onConfirm={(e) => this.handleConfirmOk(e)}
                        onCancel={() => this.hideConfirmationModal()}
                        title="Confirm"
                        message="Are you sure you wish to transfer call to RM Insurer?"
                        show={true}
                      />
                    )}

                    {
                      <DataTable
                        columns={columns}
                        data={this.state.items}
                        extention={false}
                        pagination={false}
                      />
                    }
                  </CardBody>
                </Card>
              </Col>
            </Row>
          )
          }
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    addRecord
  }
)(RMInsurerTransfer);