
import React from "react";
// javascript plugin used to create scrollbars on windows
import PerfectScrollbar from "perfect-scrollbar";
import { Route, Routes } from "react-router-dom";

import Footer from "components/Footer/Footer.jsx";

import routes from "routes.js";


var ps;

class Gaming extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      backgroundColor: "black",
      activeColor: "info",
      mainclass: "main-panel full",
      inMatrix: true,
      user: {},
      location: '',
    };
    this.mainPanel = React.createRef();
  }

  componentWillMount() {
    // var userid = getCookie("AgentId");
    // if (getUrlParameter("u") != userid) {
    //   this.props.history.push('/client/UnAuthenticated')
    // }

  }



  componentDidMount() {
    if (navigator.platform.indexOf("Win") > -1) {
      ps = new PerfectScrollbar(this.mainPanel.current);
      document.body.classList.toggle("perfect-scrollbar-on");
    }
  }
  componentWillUnmount() {
    if (navigator.platform.indexOf("Win") > -1) {
      try{
        document.body.classList.toggle("perfect-scrollbar-on");
        ps.destroy();

      }
      catch(e){
        console.log(e);
      }
      
    }
  }
  componentDidUpdate(e) {
    if (e.history.action === "PUSH") {
      this.mainPanel.current.scrollTop = 0;
      document.scrollingElement.scrollTop = 0;
    }

  }
  handleActiveClick = color => {
    this.setState({ activeColor: color });
  };
  handleBgClick = color => {
    this.setState({ backgroundColor: color });
  };


  render() {

    return (
      <div className="wrapper">

        <div className='main-panel full' ref={this.mainPanel}>

          <Routes>
            {routes.map((prop, key) => {
              return (
                <Route
                  path={prop.layout + prop.path}
                  element={prop.component}
                  key={key} />);
            })
            }
          </Routes>
          <Footer fluid />
        </div>

      </div>
    );
  }
}

export default Gaming;
