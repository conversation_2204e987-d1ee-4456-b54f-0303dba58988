var express = require("express");
const router = express.Router();

const controller = require("./Controller");
const AthenaController = require("./AthenaController"); 

// router.get("/agentBookings/*", controller.getAgentBookingsdata);
// router.get("/booking/*", controller.getBookingdata);
// router.get("/agentDetails/*", controller.getAgentDetails);
// router.post("/executeQuery", controller.executeHadoopQuery);
router.post("/athenaData", AthenaController.getAthenaData);
router.get("/getCustTicketAthenaData", AthenaController.getCustTicketAthenaData);

// router.get("/getCredentials/*", AthenaController.getCredentials);

module.exports = router;