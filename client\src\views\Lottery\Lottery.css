@font-face {
    font-family: "CircusOfInnocents";
    src: url("../../assets/fonts/CircusOfInnocents.ttf");
    font-weight: normal;
    font-style: normal;
}
body{
    background-color: #063053;
}
.main-panel {
    background-color: #063053;
    background-image: url(../../../../client/public/lottery/StarsBackground.svg);
    background-position: center;
    background-size: cover;
}
.pbLogo {
    margin: 20px 0px 15px;
    width:200px;
}
.lotteryLogo {
    position: absolute;
    top: -68px;
}
.winnerSection {
    position: relative;
}
.winnerList {
    display: inline-grid;
    justify-items: center;
    width: 712px;
    min-height: 485px;
    margin-top: 15px;
    padding: 50px 30px 30px 135px;
    background-image: url(../../../../client/public/lottery/board2.svg);
    background-repeat: no-repeat;
}
.mr-6 {
    margin-right: 6% !important;
}
.winnerList h3 {
    text-align: center;
    font: normal normal 900 40px/30px Roboto;
    letter-spacing: 0px;
    color: #fefefe;
    opacity: 1;
    text-transform: uppercase;
}
.winnerList p {
    text-align: center;
    font: italic normal 600 38px/40px Roboto;
    letter-spacing: 0px;
    color: #fefefe;
    opacity: 1;
}

.winner {
    display: inline-grid;
    justify-items: center;
    width: 604px;
    min-height: 138px;
    margin-top: 15px;
    padding: 8px;
    border: 10px solid transparent;
    /* border-image: url(http://localhost:3000/static/media/board.358020036281bc32d537.svg) 72/33 round; */
    border-image: url(../../../../client/public/lottery/board.svg) 72/33 round;
}
.winner h3 {
    margin-top: 10px;
    margin-bottom: 0px;
    text-align: center;
    font: normal normal 900 28px / 39px Roboto;
    letter-spacing: 0px;
    color: #fefefe;
    opacity: 1;
}

.winner p {
    text-align: center;
    font: italic normal 600 25px / 30px Roboto;
    letter-spacing: 0px;
    color: #fefefe;
    opacity: 1;
    margin-top: 0px;
    margin-bottom: 0px;
}
.resetBtn {
    height: 72px;
    padding: 0;
    position: absolute;
    width: 72px;
    background: transparent linear-gradient(180deg, #f0b145 0%, #ea5b41 100%) 0% 0% no-repeat padding-box;
    font: normal normal bold 25px/32px Roboto;
    border-radius: 8px;
    border: none !important;
    right: 171px !important;
    top: 52px;
}
label {
    text-align: left;
    font: normal normal 600 14px/19px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
}
.winnerprize {
    position: absolute;
    top: -75px;
    left: -31px;
    width: 158px;
}
.btn-primary {
    background-color: #f82;
    border: 1px solid #f82;
    margin: 0 10px;
}

.btn-primary:hover {
    background-color: #f82;
}
.details {
    right: 20px;
    width: 300px;
    margin-bottom: 0px;
    position: absolute;
    top: 38px;
}
.details li {
    background: #e95148 0% 0% no-repeat padding-box;
    margin-bottom: 4px;
    border: 2px solid #ffffff;
    border-radius: 8px;
    text-align: center;
    font: normal normal bold 16px/19px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    width: 100%;
    display: flex;
    justify-content: space-evenly;

}
.details p {
    border-right: 2px solid #fff;
    padding-right: 0px;
    line-height: 28px;
    margin-bottom: 0px;
    text-align: center;
    width: 72%;
}
.details span {
    line-height: 30px;
    width: 28%;
}
.form-control {
    background: transparent linear-gradient(180deg, #f0b145 0%, #ea5b41 100%) 0% 0% no-repeat padding-box;
    border-radius: 8px;
    border: none;
    font: normal normal 600 16px/22px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    height: 56px !important;
}

/* .bgImage {   
    height: 360px;
    margin-bottom: 1em;
   
} */
.humanImage {
    position: absolute;
    left: 15px;
    z-index: 99;
    bottom: 68px;
    height: 255px;
}
.towerImage {
    left: 0;
    right: 0;
    margin: auto;
    top: 21px;
    height: 330px;
    z-index: 0;
    position: absolute;
}
.startBtn {
    background: transparent linear-gradient(180deg, #f0b145 0%, #ea5b41 100%) 0% 0% no-repeat padding-box;
    border-radius: 8px;
    border: none;
    text-align: center;
    font: normal normal bold 22px/32px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    padding: 12px 65px;
    opacity: 1;
    text-transform: capitalize;  
}

.winners{
    width: 100%;
    margin-bottom: 0px;
    display: flex;
    flex-wrap: wrap;
    padding-left: 0px;    
    position: relative;
    top: 70px;
}

.winners li {
    background: #e95148 0% 0% no-repeat padding-box;
    margin-bottom: 4px;
    border: 2px solid #ffffff;
    border-radius: 8px;
    text-align: center;
    font: normal normal bold 16px/19px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    margin-left:0.5%;
    padding: 7px 0px;
    margin-right:0.5%;
    opacity: 1;
    width: 32.1%;
    display: flex;
    justify-content: center;
}
.winners p {
    border-right: 2px solid #fff;
    padding-right: 0px;
    line-height: 24px;
    margin-bottom: 0px;
    text-align: center;
    width: 60%;
}
.winners span {
    line-height: 24px;
    width: 40%;
    padding: 0px 9px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.top0{
    top:0 !important;
}

.contest-name{
    color: #ffffff;
    font-size: 27px;
    font-weight: 700;
    margin-bottom: 18px;
    padding: 12px 0;
}