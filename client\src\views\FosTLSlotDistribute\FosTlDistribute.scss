/* my span booking css */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');


.fosSlotDistributionSection {
    padding-bottom: 0px !important;
    h1 {
        text-align: left;
        font: normal normal bold 24px/32px Roboto;
        letter-spacing: 0px;
        color: #000000;
        opacity: 1;
        margin: 40px 0px 15px 0px;

        img {
            margin-left: 10px;
            cursor: pointer;
            width: 25px;
        }
    }

    .DayTab {
        padding-left: 0px;
        list-style-type: none;
        display: flex;
        width: 340px;
        background: #c6dcff26 0% 0% no-repeat padding-box;
        border: 1px solid #c6dcff26;
        border-radius: 8px;
        height: 41px;
        align-items: center;
        justify-content: space-around;

        li {
            text-align: center;
            font: normal normal 500 14px/19px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 0.6;
            cursor: pointer;
        }

        .activetab {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            box-shadow: 0px 3px 6px #86868629;
            border-radius: 8px;
            opacity: 1;
            font: normal normal bold 14px/19px Roboto;
            letter-spacing: 0px;
            color: #00000099;
            padding: 6px 16px;
        }
    }

    .leadSortDistribution {
        overflow: auto; height: 70vh; 
        table {
            border: 1px solid #0000001F;
            border-radius: 8px;
            border-collapse: separate;
            border-spacing: 0px;

            thead {
                position: sticky; top: 0px; z-index: 1; 
                background-color: #FAFAFA;
                th {
                    background: #FAFAFA 0% 0% no-repeat padding-box;
                    border: 1px solid #0000001F;
                    padding: 20px 10px;
                    text-align: center;
                    font: normal normal bold 12px/16px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 1;
                  
                    &:first-child {
                        border-radius: 8px 0px 0px 0px;                       
                     
                    }

                    &:last-child {
                        border-radius: 0px 8px 0px 0px;                      
                     
                    }
                }

                .activeTimeSlot {
                    color: #0065FF;
                }
            }

            tbody {
                th {
                    background: #f0f0f0 0% 0% no-repeat padding-box;
                    border-bottom: 1px solid #0000001F;
                    text-align: left;
                    font: normal normal bold 16px/21px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    vertical-align: middle;
                    padding: 10px 10px 10px 15px;
                    width:190px;

                    p {
                        margin-bottom: 0px;
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #000000;
                    }
                }

                tr td {
                    border: 1px solid #0000001F;
                    vertical-align: top;
                }

                div {
                    font: normal normal normal 11px/15px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 1;
                    border: 1px solid #0000001F;
                    border-radius: 4px;
                    opacity: 1;
                    padding: 6px 10px;
                    margin-bottom: 4px;
                    position: relative;

                    p {
                        margin-bottom: 0px;
                        margin-top: 0px;
                        display: flex;
                        justify-items: left;
                        align-items: center;

                        img {
                            margin-left: 3px;
                            width: 10px;
                        }
                    }


                }

                .UnassignedLead {
                    background: #F0F0F0 0% 0% no-repeat padding-box;
                    position: relative;
                }

                .journeyStarted {
                    background: #FEF0ED 0% 0% no-repeat padding-box;
                    border: 1px solid #FEF0ED;
                    border-radius: 4px;
                    position: relative;
                }

                // .Completed {
                //     background: #E8F3FC 0% 0% no-repeat padding-box;
                //     border: 1px solid #228CDB80;
                //     border-radius: 4px;
                // }
                .Completed {
                    background: #33B442 0% 0% no-repeat padding-box;
                    border: 1px solid #33B442;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }

                .Confirmed {
                    background: #F78631 0% 0% no-repeat padding-box;
                    border: 1px solid #F78631;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }

                .start {
                    background: #c1ae0b 0% 0% no-repeat padding-box;
                    border: 1px solid #c1ae0b;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }
                .blinkStart{
                    -webkit-animation: flash 3s infinite;
                    animation: flash 3s infinite;
                }
                @-webkit-keyframes flash {

                    from,
                    50%,
                    to {
                        opacity: 1;
                    }
        
                    25%,
                    75% {
                        opacity: 0.2;
                    }
                }
        
                @keyframes flash {
        
                    from,
                    50%,
                    to {
                        opacity: 1;
                    }
        
                    25%,
                    75% {
                        opacity: 0.2;
                    }
                }
                // .Booked {
                //     background: #6EAD5E 0% 0% no-repeat padding-box;
                //     border: 1px solid #6EAD5E;
                //     border-radius: 4px;
                //     color:#fff;
                //     position: relative;
                // }

                .Cancelled {
                    background: #c77b7b 0% 0% no-repeat padding-box;
                    border: 1px solid #c77b7b;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }

                .Re-Scheduled {
                    background: #6f65a3 0% 0% no-repeat padding-box;
                    border: 1px solid #6f65a3;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }

                .StartJourney {
                    background: #0065FF 0% 0% no-repeat padding-box;
                    border: 1px solid #0065FF;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }

                .EndJourney  {
                    background: #0065FF 0% 0% no-repeat padding-box;
                    border: 1px solid #0065FF;
                    border-radius: 4px;
                    color:#fff;
                    position: relative;
                }
              
                .morebtn {
                    background: #C6DCFF66 0% 0% no-repeat padding-box;
                    border: 1px solid #C6DCFF;
                    border-radius: 4px;
                    opacity: 1;
                    text-align: center;
                    font: normal normal 600 10px/13px Roboto;
                    letter-spacing: 0px;
                    color: #0065FF;
                    outline: none;
                    padding: 6px 10px;
                    width: 100%;
                }

                .TogglePopup {
                    border: none;
                    padding: 0px;
                    position: absolute;
                    right: 0px;
                    width: 25px;
                    text-align: center;
                    top: 5px;
                    cursor: pointer;
                }
            }
        
        }
    }

 
    // .scrolling {
    //     overflow-y: auto;
    //     overflow-x: hidden;
    //     height: 190px;
    //     padding: 0px !important;
    //     margin-bottom: 0px !important;
    //     border: none !important;
    // }

    // /* width */
    // ::-webkit-scrollbar {
    //     width: 5px;
    // }

    // /* Track */
    // ::-webkit-scrollbar-track {
    //     box-shadow: inset 0 0 5px grey;
    //     border-radius: 10px;
    // }

    // /* Handle */
    // ::-webkit-scrollbar-thumb {
    //     background: #000;
    //     border-radius: 10px;
    //     color: #767373 !important;
    // }

}
.popover{
    z-index:9999999 !important;
}
.toogleOpen {
    .popover-header {
        font: normal normal bold 10px/13px Roboto;
        letter-spacing: 0px;
        color: #000000;
        text-transform: uppercase;
        opacity: 0.6;
        background-color: transparent;
        border: none;
        padding-bottom: 4px;
    }

    .popover-body {
        padding: 0px;
        width: 135px;

        ul {
            list-style-type: none;
            padding-left: 0px;
            margin-bottom: 0px;
            li {
                padding: 7px 10px;
                text-align: left;
                font: normal normal normal 12px/16px Roboto;
                letter-spacing: 0px;
                color: #000000;
                opacity: 1;
                cursor: pointer;
                img{
                    margin-right: 6px;
                }
            }
        }
        hr{
            margin: 0px;
        }
    }
}

.UnassignedLeadPopup {
    z-index: 9999999 !important;
    .UnassignedLead {
        background: #F0F0F0 0% 0% no-repeat padding-box;
        font: normal normal normal 11px/15px Roboto;
        letter-spacing: 0px;
        color: #000000;
        opacity: 1;
        border: 1px solid #0000001F;
        border-radius: 4px;
        opacity: 1;
        padding: 6px 10px;
        margin-bottom: 10px;
        position: relative;
        width: 42%;
        &:nth-of-type(2n+1){
            float: right;
        }
        p {
            margin-bottom: 0px;
            margin-top: 0px;
            display: flex;
            justify-items: left;
            align-items: center;

            img {
                margin-left: 3px;
                width: 10px;
            }

            b {
                margin-right: 5px;
            }
        }
    
    }
    .TogglePopup {
        border: none;
        padding: 0px;
        position: absolute;
        right: 0px;
        width: 25px;
        text-align: center;
        top: 5px;
        cursor: pointer;
    }
    .modal-content {
        border-radius: 12px;
    }

    .modal-body {       
        height: 320px;
        overflow-y: auto;
        padding-top: 10px;
        
    }

    .modal-header {
        display: block;
        text-align: center;
        padding-bottom: 8px;
        h3 {
            text-align: center;
            font: normal normal 600 24px/32px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
        }

        p {
            text-align: center;
            font: normal normal 600 12px/16px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 0.6;
            padding-bottom: 8px;
        }

        .btn-close {
            position: absolute;
            top: 11px;
            background-color: #f0f0f0;
            right: 12px;
            color: #fff;
            font-size: 10px;
            border-radius: 16px;
        }

        select {
            font: normal normal 600 12px/16px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            border: none;
            text-align: left;
        }
    }

    hr {
        border: 1px solid #707070;
        opacity: 0.12;
        margin: 0px;
    }

    .confrimLead {
        font: normal normal normal 11px/15px Roboto;
        letter-spacing: 0px;
        // color: #000000;
        opacity: 1;
        border: 1px solid #0000001F;
        border-radius: 4px;       
        padding: 6px 10px;
        height: 50px;
        margin-bottom: 10px;
        position: relative;
        width: 42%;
          display: inline-block;
        &:nth-of-type(2n+1){
            float: right;
        }
        p {
            margin-bottom: 0px;
            margin-top: 0px;
            display: flex;
            justify-items: left;
            align-items: center;

            img {
                margin-left: 3px;
                width: 10px;
            }
        }

    }
}

.AssignedLeadPopup {
    z-index: 9999999 !important;
    .modal-header {
        display: block;
        text-align: left;
        padding-bottom: 0px;

        h3 {
            text-align: left;
            font: normal normal 600 24px/32px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            margin-bottom: 20px;
        }

        .btn-close {
            position: absolute;
            top: 11px;
            background-color: #f0f0f0;
            right: 12px;
            color: #fff;
            font-size: 10px;
            border-radius: 16px;
        }

        ul {
            width: 95%;
            list-style-type: none;
            padding-left: 0px;

            li {
                width: 65%;
                float: left;
                text-align: left;
                font: normal normal 500 12px/16px Roboto;
                letter-spacing: 0px;
                color: #00000099;
                margin-bottom: 10px;

                b {
                    color: #000000;
                }

                &:nth-of-type(2n+1) {
                    width: 35%;
                }

                select {
                    font: normal normal 600 12px/16px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 1;
                    border: none;
                    text-align: left;
                }

                .pd-0 {
                    padding: 0px;
                }
            }
            p{
                text-align: left;
                font: normal normal 500 12px/16px Roboto;
                letter-spacing: 0px;
                color: #00000099;
                display:block;
                clear:both;
                margin-bottom: 10px;
            }
        }
    }

    .modal-content {
        border-radius: 12px;
    }

    .modal-body {
        padding: 0px;

        .heading {
            text-align: center;
            font: normal normal 600 12px/16px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            margin: 67px 0px auto;
        }

        .padding15 {
            padding: 15px;
        }
    }

    .form-check {
        border: none;
        padding-left: 0px;

        .form-check-label {
            text-align: left;
            font: normal normal bold 12px/18px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            padding-left: 10px;

            p {
                font-weight: normal;
                margin-bottom: 0px;
                padding-left: 5px;

                b {
                    color: #0065FF;
                }

                img {
                    width: 10px;
                    position: relative;
                    top: -1px;
                    left: -1px;
                }
            }
        }

        .form-check-input {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1.4px solid #0065FF;
        }
    }

    hr {
        border: 1px solid #707070;
        opacity: 0.12;
        margin: 0px;
    }

    .confrimBtn {
        background: #c7dcff80 0% 0% no-repeat padding-box;
        border-radius: 0px 0px 12px 12px;
        border: none;
        text-align: center;
        font: normal normal 500 16px/21px Roboto;
        letter-spacing: 0px;
        color: #0065FF;
        opacity: 1;
        padding: 8px;
        width: 100%;
        margin-top: 20px;
    }

}

.Backdrop{ 
    // width: 100%;
    // height: 100vh;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.7;
    top:0;
    bottom: 0;
    left: 0;
    right:0;
    position: fixed;
    z-index: 9;
}

.flexbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 100%;
    align-items: center;
}

.bt-spinner {
    margin-left: 50vw;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: transparent;
    border: 4px solid #222;
    border-top-color: #009688;
    -webkit-animation: 1s spin linear infinite;
    animation: 1s spin linear infinite;
}
@-webkit-keyframes spin {
    -webkit-from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    -webkit-to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}