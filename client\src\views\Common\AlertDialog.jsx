
import React from "react";
import { But<PERSON>, Modal } from 'react-bootstrap';

class AlertDialog extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            show: this.props.show,
            title: this.props.title,
            message: this.props.message,
            buttonLabel: this.props.buttonLabel,
            isLoading: false,
        }
    }

    handleClose () {
        //this.setState({ show: false });
        this.props.onCancel();
    }
    
    handleConfirm (props) {
        this.setState({ isLoading: true });
        this.props.onConfirm();
    }

    render() {
            return (                
            <>
            <Modal show={this.state.show} onHide={() => this.handleClose()}>
                <Modal.Header>
                    <Modal.Title>{this.state.title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{this.state.message}</Modal.Body>
                <Modal.Footer>
                    <Button variant="primary" onClick={() => this.handleConfirm()}>
                        {this.state.buttonLabel}
                    </Button>
                </Modal.Footer>
            </Modal>
            </>)
    }
}

export default AlertDialog;