import { useEffect, useState } from "react";
import React from "react";

import {
    GenieCreateTicket,
    GetCommonspData, GetUserDetailsBytoken, Ticketing
} from "../store/actions/CommonAction";

import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from './Common/DataTableWithFilter';
import { getUrlParameter, getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import DateRange from "./Common/DateRange"
import 'react-datetime/css/react-datetime.css'
import moment from 'moment';
import {  Button } from 'react-bootstrap';
import config from "../config.jsx";
import { Tooltip } from "@mui/material";
const axios = require('axios');


const CustomerIssues = (props) => {
    
    const PageTitle = "My Tickets"
    const [startDate, setstartDate] = useState(moment().subtract(30, 'days').format("YYYY-MM-DD"));
    const [endDate,setendDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));
    let dateRangeRef = React.createRef();
    const [data, setdata] = useState()
    const [LoadClass, setLoadClass] = useState("")
    const [userData, setuserData] = useState({})
    const [refreshData, setRefreshData ] = useState(false);

    const columnlist = [
        {
            name: 'Ticket ID ',
            selector: 'TicketDetailsID',
            type: "string",
            cell: row => <div style = {{color: '#007BFF', cursor: 'pointer'}}>
                <a onClick ={() => TicketView(row.TicketURL)} target="_blank">{row.TicketDetailsID} </a>

            </div>,
            width: '120px',
            searchable: true
        },
        {
            name: 'BookingID',
            selector: 'BookingID',
            type: "string",
            width: '100px',
            searchable: true
        },
        {
            name: 'Customer Name',
            selector: 'Name',
            width: '150px',
            type: "string",
            searchable: true
        },
        {
            name: 'Product',
            selector: 'ProductName',
            width: '150px',
            type: "string"
        },
        {
            name: 'Issues',
            selector: 'IssueName',
            type: "string",
            cell: row => <div>{row.IssueName ? <div class="fa fa-info-circle" data-toggle="tooltip" title={row.IssueName} aria-hidden="true"> {row.IssueName}</div>   : ""}</div>,
            width: '150px'
        },

        {
            name: 'Sub issue',
            selector: 'SubIssueName',
            type: "string",
            cell: row => <div>{row.SubIssueName ? <div class="fa fa-info-circle" data-toggle="tooltip" title={row.SubIssueName} aria-hidden="true"> {row.SubIssueName}</div>   : ""}</div>,
            width: '120px'
        },
        {
            name: 'Supplier/Insurer',
            selector: 'SupplierShortName',
            type: "string",
            width: '150px'
        },
        {
            name: 'Assigned to (CRT)',
            selector: 'AssignedTo',
            cell: row => <div>{row.AssignToEmpId ? row.AssignToEmpId + "-" + row.AssignToName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
        },
        {
            name: 'Status',
            selector: 'Status',
            type: "string",
            sortable: true,
            width: '100px',
            searchable: true
        },

        {
            name: 'Sub status',
            selector: 'SubStatusName',
            cell: row => <div>{row.SubStatusName ? <div class="fa fa-info-circle" data-toggle="tooltip" title={row.SubStatusName} aria-hidden="true"> {row.SubStatusName}</div>   : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px'
        },
        {
            name: "Escalation",
            selector: 'escalationAllowed',
            sortable: true,
            width: "100px",
            omit: getUrlParameter("showEscalation") === "true" ? false : true,
            cell: row =>  
                row.escalationAllowed == 1 ?
                    <p
                        className="EscalteIssue"
                        onClick={() => 
                            createEscalatedTicket({
                                "BookingID": row.BookingID,
                                "TicketDisplayID": row.TicketDetailsID,
                                "IssueName": row.IssueName,
                                "TicketUrl": row.TicketURL,
                                "ProductName": row.ProductName,
                                "APE": row["APE"]
                            })}
                    >
                        Escalate issue
                    </p>
                :
                row.escalationAllowed == 2 ?
                    <Tooltip
                        title="Your ticket has been escalated"
                        placement="top-start"
                        PopperProps={{
                            className: "custom-tooltip", 
                        }}
                    >
                        <div className="EscalteBtn">
                            Escalated             
                        </div>
                    </Tooltip>
                : 
                row.escalationAllowed == 3 ?
                    <Tooltip
                        title={row.escalationNotAllowedReason}
                        placement="top-start"
                        PopperProps={{                                          
                            className: "custom-tooltip", 
                        }}
                    >
                        <div className="EscalteBtn">
                            Escalate  
                            <img src={"/Images/emergency_home.svg"} alt=" " />             
                        </div>
                    </Tooltip>
                : null
        }
        
    ];

    useEffect(()=>{
        
        setTimeout(function () {
            setuserData(getuser())
          }, 500);
    },[])

    useEffect(()=>{
        if(Object.keys(userData).length > 0 || refreshData){
            GetData(startDate,endDate,userData)
            setRefreshData(false);
        }
    },[userData, refreshData])

    const GetData = (startDate,endDate,user) => {
        setLoadClass('spinner-border spinner-border-sm');
        Ticketing(new Date(startDate).valueOf(), new Date(endDate).valueOf(), user.UserName, user.EmployeeId, function (result) {
            if (result.status === 200) {
                setdata(result.data.Data)
            }
            setLoadClass("");
        })
    }

    const handleStartDate = (StartDateValue) => {
        setstartDate(StartDateValue);

        const date = new Date()
        const startDate= new Date(StartDateValue)

        startDate.setDate(startDate.getDate()+30); 
        if(date-startDate<=0){
            setendDate(moment().format("YYYY-MM-DD 23:59:59"))
        }
        else{
            setendDate(moment(StartDateValue).add(30, 'days').format("YYYY-MM-DD 23:59:59"))
        }
    }
    
    const handleEndDate = (EndDateValue) => {
        setendDate (EndDateValue);
    }

    const createEscalatedTicket = (data) => {
        GetUserDetailsBytoken({ "token": "", "userId": getuser().UserID })
			.then((res) => {
				const request = {
					LeadID: data.BookingID ? data.BookingID : 0,
					CreatedBy: getuser().UserId,
					SourceID: 11,
					IssueID: 57,
					Title: "Service Escalation",
					Comments: `${data.TicketDisplayID} has been escalated by the sales advisor. This ${data.ProductName} booking is of ${data["APE"]} APE. Please help resolve the customer's concern.`,
                    RefTicketID: data.TicketDisplayID,
					FileURL: data.TicketUrl,
					FileName: data.TicketDisplayID,
					ProductID: 0,
					PayID: "",
					OrderID: ""
				}

				GenieCreateTicket(request)
                    .then((result) => {
                        if(result && result.data && result.data.Status > 0) {	
                            toast("Ticket Escalated Successfully.", { type: 'success' });
                            setRefreshData(true);
                        } else {
                            toast("Ticket Escalation Failed! Please try again.", { type: 'error' });
                        }
                    }).catch((e) => {
                        toast("Ticket Escalation Failed! Please try again.", { type: 'error' });
                    })
			}).catch((e) => {
                toast("Ticket Escalation Failed! Please try again.", { type: 'error' });
			})
    }

    const handleChange =() =>{
        GetData(startDate,endDate,userData)
    }

    const TicketView = (TicketURL) => {
        window.open(TicketURL)
    }

    return (
        <>

            <div className="content CustomerIssues" >
                <ToastContainer />

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                <Col md={8}>
                                    <Row>
                                    
                                        <DateRange days={30} FromDate= {"From Date"} ToDate= {"To Date"} EndDateFixed={true}
                                    startDate={startDate} endDate={endDate} LimitStartdate = {90} ref={dateRangeRef} onStartDate={handleStartDate} onEndDate={handleEndDate}>
                                    </DateRange>
                
                                    </Row>
                                </Col>
                                
                                <Col md={4}>
                                <br></br>
                                
                                    <Button variant="primary" onClick={handleChange}>
                                    <span className={LoadClass}></span>
                                    Submit
                                    </Button>
                
                                </Col>
                                </Row>
                                <p style={{marginBottom:'3px', color: 'red'}}><i>*</i> Customer Issues Raised By You</p>
                                <DataTable
                                    columns={columnlist}
                                    data = {data}
                                    printexcel={true}
                                />
                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(CustomerIssues);