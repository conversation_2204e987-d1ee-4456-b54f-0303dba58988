@import './variables.scss';

.header-container {
    background-color: $title-color;
    color: $title-bg-clr;
    display: flex;
    font-family: "Merriweather", serif;
    justify-content: flex-end;
    font-size: 18px;
    padding: 15px;
    box-shadow:0 2px 2px 0 #00000003, 0 3px 1px -2px #00000005, 0 1px 5px 0 #00000005;
}

.page-title {
    display: flex;

    h1 {
        color: $page-title-color;
        font-size: 18px;
        text-transform: capitalize;
        font-family: $title-font-family;
        font-weight: 400;
        margin-top: 5px;
    }
}


.icon-card {
    color: $icon-color;
    font-size: 45px;
    margin-right: 10px;
}


