import React, { useState } from 'react';
import export<PERSON>romJSON from 'export-from-json';

const ExportExcel = (props) => {
  const { data, fileName } = props;

  const handleExcelExport = () => {
    debugger
    const exportType =  exportFromJSON.types.xls;
    exportFromJSON({ data, fileName, exportType });
  }

  return (
    <>
      {<i title='Download' className="fa fa-download" onClick={handleExcelExport}></i>}
    </>
  );
}

export default ExportExcel;