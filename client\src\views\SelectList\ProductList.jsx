
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
// reactstrap components
import DropDownList from '../Common/DropDownList';


import { Row, Col } from 'react-bootstrap';


class ProductList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {

        }

        this.ProductList = {
            config:
            {
                root: "Products",
                cols: ["ID AS Id", "ProductName AS Display"],
                con: [{ "Isactive": 1 }],
                //state: false
            }
        };
    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {

    }


    productchange(e) {
        //return <option key={item.Id} value={item.Id}>{item.Display}</option>
    }



    render() {


        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <div>
                <Form.Group controlId="product_dropdown">
                    <DropDownList firstoption="Select Product"
                        col={this.ProductList}
                        onChange={this.props.productchange}>
                    </DropDownList>
                </Form.Group>
            </div>

        );
    }
}


export default ProductList;

