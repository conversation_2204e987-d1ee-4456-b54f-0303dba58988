import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, ResetTokenDidUpdate, GetCommonspDataV2, GetCommonspData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from './Common/DataTableWithFilter';
import { fnRenderfrmControl, fnBindRootData, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter, getuser, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

class UsersWFH extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      activePage: 1,
      root: "UsersWFH",
      PageTitle: "UsersWFH",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      ResetTokenData:'',
      fields: [],
      condition: {},
      unlockUserAccount: false
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.UnlockAccount = this.UnlockAccount.bind(this);
    this.selectedrow = { "UserID": 0, "UserName": "", "EmployeeId": "" }
    this.callingComp = [{ "Id": "None", "Display": "" },{ "Id": "WFH_NEW", "Display": "WFH_NEW" },{ "Id": "WFH", "Display": "WFH" }, { "Id": "ASWATINDIA", "Display": "ASWATINDIA" }, { "Id": "WEBPHONE", "Display": "WEBPHONE" },{"Id": "TATAMOBILESIP", "Display": "TATAMOBILESIP"}];
    if(["PW52419", "PW24102", "AU00720", "PH47636"].includes(getuser().EmployeeId)){
      this.callingComp.push({"Id": "DIALER_APP", "Display": "DIALER_APP" })
    }

    this.columnlist = [ 
      {
        name: "UserName",
        label: "UserName",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true,
      },
      {
        name: "UserID",
        label: "UserID",
        type: "hidden",
        hide: true,
        sortable: false,
        //searchable: false
      },
      {
        name: "EmployeeId",
        label: "EmployeeId",
        type: "string",
        editable: false,
        sortable: true,
        //searchable: true
      },
      {
        name: "CallingCompany",
        label: "CallingCompany",
        type: "dropdown",
        sortable: true,
        //searchable: true,
        config: {
          root: "CallingCompany",
          //data: [{"Id":"WFH","Display":"WFH"},{"Id":"WEBPHONE","Display":"WEBPHONE"},{"Id":"KNOWLARITY","Display":"KNOWLARITY"},{"Id":"EXOTEL","Display":"EXOTEL"},{"Id":"ASWATINDIA","Display":"ASWATINDIA"},{"Id":"ASWAT","Display":"ASWAT"}],            
          data: this.callingComp,
        }
      },
      {
        name: "DIDNo",
        label: "Mobile No",
        type: "string",
        sortable: true,
        //searchable: true
      },
      {
        name: "DomainUserName",
        label: "DomainUserName",
        type: "string",
        sortable: true,
        //searchable: true
      },
      {
        name: "City +' '+'('+(b.state)+')'",
        label: "City",
        alias:"City",
        type: "string",
        sortable: true,
        //searchable: true,
        hideonmodal: true,
      },
      {
        name: "ud.CityID",
        label: "City Name",
        alias:"CityId",
        type: "autodropdown",
        config: {
          root: "GetStateCitiesList",
          statename: "Cities",
          executionType: 'SP',
          state: true,
          sp:true
        },
        hide: true,
      },
      {
        name: "PinCode",
        label: "PinCode",
        type: "string",
        editable: true,
        cell: row => <div>{row.PinCode ? row.PinCode : "N.A"}</div>,
        maxLength: 6
        //searchable: true
      },
      {
        name: "PBEmailID",
        label: "PBEmailID",
        type: "string",
        editable: true,
        sortable: true,
        //searchable: true
      },
      {
        name: "IsWFH",
        label: "IsWFH",
        type: "bool",
        sortable: true,
        //searchable: true  
      },
      {
        name: "AswatPosition",
        label: "IsAswatRegistered",
        type: "bool",
        cell: row => <div>{row.AswatPosition ? true : false}</div>,
        editable: false
      },
      {
        name: "IsLocked",
        label: "IsLocked",
        type: "bool",
        sortable: true,
        onModal: true,
        isImage: true
      },
      {
        name: "LAT",
        label: "Latitude",
        type: "decimal"
      },
      {
        name: "LONG",
        label: "Longitude",
        type: "decimal"
      },
      {
        name: "ContactNo",
        label: "ContactNo (For OTP)",
        type: "int",
        editable: true,
      },
      {
        name: "Email",
        label: "Email (For OTP)",
        type: "string",
        editable: true,
      }
    ];
  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        if(this.state.condition && this.state.condition.length > 0){
          this.setState({ items: items });
          }else{
            this.setState({ items: [] });
          }    
      }
    }
  }

  componentDidMount() {
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    // this.props.GetCommonData({
    //   limit: 10,
    //   skip: 0,
    //   root: this.state.root,
    //   c: "R",
    //   cols: GetJsonToArray(this.columnlist, "name"),
    //   con: [{ "Isactive": 1 ,"IsBMS": 0}]
    // });
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if(this.state.condition && this.state.condition.length > 0){
        this.setState({ items: nextProps.CommonData[this.state.root] });
      }else{
        this.setState({ items : []})
      }
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }

    // if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
    //   if (nextProps.CommonData.InsertSuccessData.status !== 200)
    //     alert(nextProps.CommonData.InsertSuccessData.error);
    //   else {
    //     this.setState({ showModal: false });
    //   }
    // }

    // if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
    //   if (nextProps.CommonData.UpdateSuccessData.status !== 200)
    //     alert(nextProps.CommonData.UpdateSuccessData.error);
    //   else {
    //     this.setState({ showModal: false });
    //   }
    // }

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        {/* <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button> */}
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }


  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }

  handleClose() {
    this.setState({ showModal: false });
  }


  handleSave() {

    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    //this.fnCleanData(formvalue, true)

    if (this.state.event === "Edit") {
      let od = this.state.od;
      if (formvalue["DomainUserName"] == " " || typeof formvalue["DomainUserName"] == "undefined" || formvalue["DomainUserName"] == null) {
        alert("Please Enter Domain User Name");
        return false;
      }     
      let id = formvalue["UserID"];
      //if(od.DIDNo != formvalue["DIDNo"]){
        this.resetTokenDid(id);
      //}
      // if(formvalue["PinCode"] === "" || formvalue["PinCode"] === "undefined" || formvalue["PinCode"] === null || parseInt(formvalue["PinCode"]) < 0  || (formvalue["PinCode"].toString().length != 6 && parseInt(formvalue["PinCode"]) > 0)){
      //   alert("Please Enter Valid 6 digit Pincode");
      //   return false;
      // }
      if(formvalue["PinCode"]){
        var pat1=/^\d{6}$/;
        if(!pat1.test(formvalue["PinCode"]))
        {
        alert("Pin code should be 6 digits ");
        return false;
        }
      }

      if(formvalue["PBEmailID"] != "" && formvalue["PBEmailID"] != "undefined" & formvalue["PBEmailID"] != null){
        var pat1=/^[a-zA-Z0-9._:$!%-]+@policybazaar.com{1}$/;
        if(!pat1.test(formvalue["PBEmailID"]))
        {
        alert("Please enter correct Policybazaar domain Email ID ");
        return false;
        }
      }

      formvalue["CityId"] = formvalue["ud.CityID"]
      delete formvalue["UserID"]
      delete formvalue["CallingCompany_display"]
      delete formvalue["City"];
      delete formvalue["ud.CityID"];
      //formvalue["IsWFH"] = 1;
      //formvalue["IsProgressive"] = 0;
      
      var pat1=/^[0-9]{10}$/;

      if(formvalue["IsWFH"] && formvalue["IsWFH"] == 1 && (!formvalue["DIDNo"] || formvalue["DIDNo"] == "")) {
          alert("Mobile No is required ");
          return false;
      }
      else if(formvalue["IsWFH"] && formvalue["IsWFH"] == 1 && formvalue["DIDNo"] && !pat1.test(formvalue["DIDNo"])){
        alert("Enter correct mobile no.");
        return false;
      }

      if (formvalue["DIDNo"] == "") {
        formvalue["DIDNo"] = null;
      }
      if (formvalue["CallingCompany"] == "") {
        formvalue["CallingCompany"] = null;
      }
      if (formvalue["CallingCompany"] == "WEBPHONE" && formvalue["DIDNo"] == null) {
        formvalue["DIDNo"] = "WEBPHONE";
      }

      //this.fnCleanData(formvalue, true);
      // if (formvalue["IsWFH"] == false) {
      //   formvalue["CallingCompany"] = null;
      // }

      if(this.state.unlockUserAccount) {
          formvalue["LoginAttempt"] = 1;
          formvalue["IsLocked"] = 0;
      }

      this.props.UpdateData({
        root: "Users",
        body: formvalue,
        c: "L",
        querydata: { "UserID": id }
      }, function (data) {
        toast("Record updated successfully...", { type: 'success' });
        AuditLog();
      });

      const by = getuser().UserID;
      const AuditLog = () => {
        try
        {
          this.setState({ unlockUserAccount: false, ModalValueChanged: false });
          this.props.GetCommonspDataV2({
              root: "UdateUserAuditHistory",
              params: [{ userId: id, comments: 'Account has been Unlocked'}],
              c: "L",
          }, function (data) {
          })
        }
        catch(e){
        }
      }

      this.props.addRecord({
        root: "History",
        body: {
          module: "UsersWFH",
          od: this.state.od,
          nd: formvalue,
          ts: new Date(),
          by: getuser().UserID
        }
      });
      this.setState({ showModal: false });

    } else if (this.state.event === "Copy") {

    } else {

    }
    setTimeout(function () {
      this.props.GetCommonData({
        root: this.state.root,
        cols: GetJsonToArray(this.columnlist, "name"),
        con: this.state.condition,
      });
    }.bind(this), 2000);
    this.setState({ showModal: false });
    return false;
  }

  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;

    if (e.target && e.target.type === "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value === "" ? null : e.target.value;
    }

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  resetTokenDid(UserId){
    ResetTokenDidUpdate(UserId, function (results) {
      this.setState({ ResetTokenData: results.data });
    }.bind(this));
  }

  getUsers(){
    var columns = this.columnlist;
    columns = columns.filter((res) => res.label !== "Product");
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    let condition = [];
    this.setState({ condition : []})
    const inputEmpId = this.state.fields['empid'];
    const inputDIDNo = this.state.fields['DIDno'];
    const inputUserName = this.state.fields['UserName'];
    if(inputEmpId || inputDIDNo || inputUserName){
      condition.push({ "IsBMS": 0 ,"Isactive": 1});
    }
    if (inputEmpId) {
      condition.push({ "EmployeeId":inputEmpId});
    }
    if (inputDIDNo) {
      condition.push({ "DIDNo":inputDIDNo});
    }
    if (inputUserName) {
      condition.push({ "UserName_likesearch":inputUserName});
    }
    this.setState({ condition : condition})    
    if(condition && condition.length > 0){
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(columns, "name"),
      con: condition
    });
  }
  }

  UnlockAccount()
  {
    if(window.confirm("Do you want to unlock User?"))
    {
      this.setState({ unlockUserAccount: true, ModalValueChanged: true });      
    }
  }

  handleChangeFields(field, e){         
    let fields = this.state.fields;
    fields[field] = e.target.value;        
    this.setState({fields});
}

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, event } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={2}>
                    <Form.Control
                        type="text"
                        name="empid" value={this.state.fields['empid']} className="empid" id="empid" onChange={this.handleChangeFields.bind(this, "empid")}
                        placeholder={"Enter EmployeeId"}
                      />
                      </Col>
                      <Col md={2}>
                      <Form.Control
                        type="text"
                        name="UserName" value={this.state.fields['UserName']}  onChange={this.handleChangeFields.bind(this, "UserName")}
                        placeholder={"Enter Username"}
                      />
                    </Col>
                    <Col md={2}>
                    <Form.Control
                        type="text"
                        name="DIDno" value={this.state.fields['DIDno']} className="DIDno" id="DIDno" onChange={this.handleChangeFields.bind(this, "DIDno")}
                        placeholder={"Enter MobileNo"}
                      />
                    </Col>
                    
                    <Col md={1}>
                      <Button variant="primary" onClick={this.getUsers.bind(this)}>
                        Fetch
                      </Button>
                    </Col>

                  </Row>
                </CardHeader>
                <CardBody>
                 
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form>
                <Row>
                  {this.columnlist.map(col =>  {
                    if(!col.onModal || !col.isImage) {
                      return fnRenderfrmControl(col, formvalue, this.handleChange, event)
                    }
                  }
                  )}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <img
                src="/Images/lockimage.jpeg" 
                alt="Unlock Account" 
                onClick={this.UnlockAccount}
                style={{width: "130px"}}
              />
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <Button variant="primary" onClick={this.handleSave}>Save Changes</Button>
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2,
    InsertData,
    UpdateData,
    addRecord
  }
)(UsersWFH);