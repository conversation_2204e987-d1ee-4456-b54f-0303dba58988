<svg xmlns="http://www.w3.org/2000/svg" width="162" height="117.565" viewBox="0 0 162 117.565"><defs><style>.a{fill:#f5f5f5;}.b{fill:#e0e0e0;}.c{fill:none;stroke:#fff;stroke-miterlimit:10;}.d{fill:#e8505b;}.e{opacity:0.5;}.f{fill:#fff;}.g{fill:#263238;}.h{fill:#163560;}.i{fill:#ffb27d;}.j{fill:#aec3ff;}.k{fill:#94b0ed;}.l{fill:#1d3356;}.m{fill:#f2ccaa;}.n{fill:#233862;}.o{fill:#2b478b;}.p{fill:#f78248;}.q{fill:#e8945b;}</style></defs><g transform="translate(-797.189 -195)"><g transform="translate(797.189 195)"><g transform="translate(0 4.66)"><g transform="translate(0 14.992)"><rect class="a" width="41.358" height="60.898" transform="matrix(0.831, -0.556, 0.556, 0.831, 0, 22.993)"/><g transform="translate(32.995 39.606)"><g transform="translate(0 6.151)"><path class="b" d="M232.847,274.086l-.39,2.414-17.089-2.462.39-2.414Z" transform="translate(-215.368 -271.624)"/></g><g transform="translate(6.154)"><path class="b" d="M238.173,251.546l2.452.353-2.719,16.826-2.452-.353Z" transform="translate(-235.454 -251.546)"/></g></g><g transform="translate(37.008 19.216)"><path class="c" d="M228.466,184.995" transform="translate(-228.466 -184.995)"/></g><g transform="translate(10.65 18.22)"><path class="b" d="M157.913,181.744c.025.037-3.421,2.384-7.694,5.242s-7.759,5.147-7.783,5.11,3.42-2.383,7.694-5.242S157.888,181.708,157.913,181.744Z" transform="translate(-142.436 -181.744)"/></g><g transform="translate(13.091 15.046)"><path class="b" d="M176.081,171.386c.025.037-5.7,3.912-12.794,8.653s-12.86,8.558-12.884,8.522,5.7-3.91,12.8-8.654S176.057,171.35,176.081,171.386Z" transform="translate(-150.403 -171.386)"/></g><g transform="translate(15.625 18.835)"><path class="b" d="M184.353,183.752c.025.037-5.7,3.912-12.794,8.654s-12.86,8.558-12.884,8.522,5.7-3.911,12.8-8.654S184.328,183.716,184.353,183.752Z" transform="translate(-158.675 -183.752)"/></g><g transform="translate(18.159 22.624)"><path class="b" d="M192.624,196.119c.025.036-5.7,3.911-12.794,8.653s-12.86,8.558-12.884,8.522,5.7-3.911,12.8-8.654S192.6,196.083,192.624,196.119Z" transform="translate(-166.946 -196.119)"/></g><g transform="translate(20.694 26.413)"><path class="b" d="M200.9,208.485c.025.037-5.7,3.912-12.794,8.653s-12.86,8.558-12.884,8.522,5.7-3.91,12.8-8.654S200.871,208.449,200.9,208.485Z" transform="translate(-175.217 -208.485)"/></g></g><g transform="translate(47.345 0)"><rect class="a" width="35.724" height="52.602" transform="matrix(0.882, 0.472, -0.472, 0.882, 24.807, 0)"/><g transform="translate(15.245 34.698)"><g transform="translate(3.792)"><path class="b" d="M331.413,200.618l-2,.684-5.073-14.024,2-.684Z" transform="translate(-324.341 -186.594)"/></g><g transform="translate(0 3.963)"><path class="b" d="M325.892,199.529l.728,2.012-13.928,4.77-.728-2.012Z" transform="translate(-311.964 -199.529)"/></g></g><g transform="translate(42.735 26.666)"><path class="c" d="M401.689,160.379" transform="translate(-401.689 -160.379)"/></g><g transform="translate(24.887 10.384)"><path class="b" d="M357.616,114.82c-.025.046-3.22-1.614-7.135-3.709s-7.071-3.83-7.047-3.876,3.219,1.614,7.136,3.709S357.641,114.774,357.616,114.82Z" transform="translate(-343.434 -107.234)"/></g><g transform="translate(23.098 13.728)"><path class="b" d="M361.127,130.736c-.025.046-5.312-2.734-11.809-6.208s-11.746-6.33-11.721-6.376,5.311,2.733,11.81,6.209S361.151,130.69,361.127,130.736Z" transform="translate(-337.597 -118.151)"/></g><g transform="translate(21.241 17.2)"><path class="b" d="M355.066,142.067c-.025.046-5.313-2.734-11.809-6.208s-11.745-6.33-11.721-6.376,5.311,2.733,11.81,6.209S355.09,142.021,355.066,142.067Z" transform="translate(-331.536 -129.482)"/></g><g transform="translate(19.385 20.672)"><path class="b" d="M349.006,153.4c-.025.046-5.312-2.734-11.809-6.208s-11.746-6.33-11.721-6.376,5.311,2.733,11.81,6.209S349.03,153.353,349.006,153.4Z" transform="translate(-325.476 -140.814)"/></g><g transform="translate(17.528 24.144)"><path class="b" d="M342.945,164.731c-.025.046-5.313-2.734-11.809-6.208s-11.746-6.33-11.721-6.376,5.311,2.733,11.81,6.209S342.969,164.685,342.945,164.731Z" transform="translate(-319.415 -152.146)"/></g></g></g><g transform="translate(19.884 39.391)"><g transform="translate(0)"><g transform="translate(25.822 10.428)"><path class="d" d="M271.22,226.03l-10.834,55.7-1.418,7.291,72.324-.357c3.246-.016,6.122-2.9,7.167-7.18L351.284,228.9c.793-3.25-.947-6.631-3.411-6.629l-73.2.077C273.056,222.349,271.643,223.857,271.22,226.03Z" transform="translate(-258.968 -222.27)"/></g><g class="e" transform="translate(25.822 10.428)"><path d="M271.22,226.03l-10.834,55.7-1.418,7.291,72.324-.357c3.246-.016,6.122-2.9,7.167-7.18L351.284,228.9c.793-3.25-.947-6.631-3.411-6.629l-73.2.077C273.056,222.349,271.643,223.857,271.22,226.03Z" transform="translate(-258.968 -222.27)"/></g><path class="d" d="M263.854,254.348l-7.61-61.707a4.839,4.839,0,0,0-4.676-4.407l-16.686.055a4.534,4.534,0,0,0-3.3,1.471l-7.923,8.476-46.407.244a4.959,4.959,0,0,0-4.639,5.749l7.065,57.033a4.839,4.839,0,0,0,4.67,4.4l80.878-.161c9.008.188,9.868-2.489,9.868-2.489C264.775,265.318,263.855,254.356,263.854,254.348Z" transform="translate(-172.575 -188.235)"/><g class="e" transform="translate(32.652 34.092)"><path class="f" d="M303.766,320.66l-2.745,2.948L279.88,304.59l2.745-2.948Z" transform="translate(-279.88 -300.988)"/><path class="f" d="M301.822,299.509l3.034,2.729-19.129,20.545-3.034-2.729Z" transform="translate(-281.831 -299.509)"/></g></g></g><g transform="translate(112.856 0)"><g transform="translate(0 0)"><g transform="translate(7.34 5.629)"><path class="d" d="M504.62,84.862a.572.572,0,0,1-.346-.5l-.123-2.844a.572.572,0,0,1,.7-.581,1.777,1.777,0,0,0,1.383-.273,1.6,1.6,0,0,0,.715-1.048,1.638,1.638,0,0,0-.344-1.23,1.825,1.825,0,0,0-1.207-.727,2.142,2.142,0,0,0-2.175,1.543.572.572,0,1,1-1.1-.3,3.294,3.294,0,0,1,3.409-2.38,2.949,2.949,0,0,1,1.988,1.174,2.783,2.783,0,0,1,.56,2.1,2.745,2.745,0,0,1-1.209,1.814,2.994,2.994,0,0,1-1.551.506l.095,2.192a.572.572,0,0,1-.8.55Z" transform="translate(-502.103 -76.504)"/><g transform="translate(2.421 9.085)"><path class="d" d="M510.94,106.591a.468.468,0,1,1-.5-.434A.468.468,0,0,1,510.94,106.591Z" transform="translate(-510.006 -106.155)"/></g></g><path class="g" d="M478.157,68.108a1.177,1.177,0,0,0,.028-.187c.014-.133.034-.315.06-.548a6.455,6.455,0,0,1,.148-.881c.04-.172.061-.362.124-.555s.123-.4.19-.619a10.535,10.535,0,0,1,1.524-2.919,10.274,10.274,0,0,1,9.5-4c.235.022.466.085.7.126a4.815,4.815,0,0,1,.7.17,9.732,9.732,0,0,1,2.74,1.242,10.249,10.249,0,0,1,4.05,5.086,10,10,0,0,1,.6,3.5,9.824,9.824,0,0,1-.641,3.6l-.138.35-.018.046.019.043L500,77.745l.129-.146-4.588-1.763-.057-.022-.044.043a10.258,10.258,0,0,1-4.333,2.5,10.19,10.19,0,0,1-8.272-1.284,10.5,10.5,0,0,1-2.583-2.405,10.16,10.16,0,0,1-1.96-4.7q-.055-.449-.1-.8-.009-.344-.015-.585c0-.15-.009-.269-.012-.359a.622.622,0,0,0-.01-.122.683.683,0,0,0-.007.122c0,.09,0,.208,0,.36s0,.358,0,.589l.081.8a11.167,11.167,0,0,0,.524,2.164,11.28,11.28,0,0,0,1.4,2.6,10.576,10.576,0,0,0,2.6,2.457,10.318,10.318,0,0,0,8.4,1.341,10.427,10.427,0,0,0,4.42-2.537l-.1.021,4.586,1.77.231.089-.1-.236-2.249-5.189v.089l.142-.358a10.033,10.033,0,0,0,.654-3.678,10.217,10.217,0,0,0-.617-3.572,10.433,10.433,0,0,0-4.139-5.18,9.9,9.9,0,0,0-2.793-1.256,4.977,4.977,0,0,0-.718-.17c-.24-.041-.476-.1-.715-.126a10.093,10.093,0,0,0-6.372,1.246,10.307,10.307,0,0,0-3.24,2.864,10.484,10.484,0,0,0-1.509,2.966l-.182.627c-.06.2-.079.388-.116.562a6.065,6.065,0,0,0-.129.889c-.017.234-.03.418-.04.551A1.313,1.313,0,0,0,478.157,68.108Z" transform="translate(-478.144 -58.132)"/></g></g></g><g transform="translate(916.711 216.531)"><path class="h" d="M619.861,948.772c.355-.685-7.755-3.154-7.883,3.611,0,0-15.192-.373-7.938-8.841,2.511-2.932,2.253-5.355,2.178-7.612s1.564-8.815,7.187-8.154a4.188,4.188,0,0,1,3.31.354C620.895,930.3,630.257,951.267,619.861,948.772Z" transform="translate(-589.075 -927.59)"/><path class="i" d="M876.183,1161.552s-.989-1.505-1.786-1.535-2.273-.443-2.155-.148,1.033.325,1.122.5-1.358.443-1.771.472-1.387,0-1.358.207a4.692,4.692,0,0,0,2.974.945,8.259,8.259,0,0,1,2.724.354Z" transform="translate(-870.234 -1134.16)"/><path class="j" d="M755.445,1083.407a14.821,14.821,0,0,1-1.513,2.287,8.853,8.853,0,0,1-5.262,3.508,5.651,5.651,0,0,1-3.126-.507h0a14.255,14.255,0,0,1-4.84-4.047,2.271,2.271,0,0,1,.31-2.229s5.579,1.255,6.11.989,3.8-7.513,4.386-8.72a6.618,6.618,0,0,1,3.235-3.3Z" transform="translate(-735.195 -1055.527)"/><path class="k" d="M761.055,1177.285a8.853,8.853,0,0,1-5.262,3.508,5.649,5.649,0,0,1-3.126-.507c.108.014,2.867.355,3.967-1.613,1.122-2.007,2.335-5.2,4-4.133C761.355,1175,761.324,1176.138,761.055,1177.285Z" transform="translate(-742.317 -1147.118)"/><path class="l" d="M545.149,1720.567s1.7,1.554,1.37,2.657-1.868,1.84-2.076,3.189-1.038,2.085-1.785,2.085h-3.563s-.55-.95.779-1.235,1.83-1.872,1.747-3.344a25.952,25.952,0,0,1,.125-3.434l2.823-.654Z" transform="translate(-504.084 -1632.464)"/><path class="l" d="M690.015,1732.485s.612,6.582-.135,6.95l-9.565-.02s0-1.1,1.536-1.349,4.235-1.145,4.692-3.148S690.015,1732.485,690.015,1732.485Z" transform="translate(-663.359 -1643.722)"/><path class="j" d="M632.442,1072.595a38.083,38.083,0,0,1,0,4.421c-.095,1.6-1.712,3.74-1.941,4.269-.576,1.328-1.225,1.544-1.225,1.544l-5.022,1.231-1.766.432-5.568-1.663s-2.536-7.079-1.518-11.064a30.63,30.63,0,0,1,2.574-6.753s3.775-1.49,10.46-.377Z" transform="translate(-598.429 -1049.155)"/><path class="m" d="M637.965,1173.1a15.632,15.632,0,0,1-.471,3.617c-.576,1.328-1.225,1.544-1.225,1.544l-5.022,1.231-2.181-.641s7.281-1.1,6.474-7.368Z" transform="translate(-605.421 -1144.587)"/><path class="n" d="M672.439,1242.691s-2.758,8.583-2.935,11.506,5.271,41.515,5.491,42.862c.207,1.269,1.783,2.078,1.783,2.078a2.385,2.385,0,0,0,1.76.135,4.35,4.35,0,0,0,1.889-.891s2.146-27.524.818-37.356a155.732,155.732,0,0,1-1.344-18.335Z" transform="translate(-653.593 -1207.942)"/><path class="o" d="M567.843,1240.371s4.206,5.644,4.6,9.459.615,17.217,1.451,20.936,3.905,10.877,3.905,22.478a2.314,2.314,0,0,1-1.3,1.019,10.744,10.744,0,0,1-1.147.317,1.8,1.8,0,0,1-2.017-.957c-2.075-4.178-8.686-17.655-9.025-20.452-.24-1.98-2.327-15.082-6.88-22.77-1.093-1.846.671-9.186.671-9.186Z" transform="translate(-537.313 -1205.878)"/><path class="n" d="M642.084,1230.676a5.781,5.781,0,0,0,3.306.92c2.627.053,9.136-.67,9.136-.67a5.382,5.382,0,0,1,.277,1.661s-7.95,2.005-12.556.293A2.91,2.91,0,0,1,642.084,1230.676Z" transform="translate(-623.679 -1197.252)"/><g transform="translate(19.883 33.894)"><path class="f" d="M724.536,1236.3a1.208,1.208,0,1,0-1.2,1.357A1.287,1.287,0,0,0,724.536,1236.3Zm-.4,0a.814.814,0,1,1-.8-.959A.892.892,0,0,1,724.139,1236.3Z" transform="translate(-722.139 -1234.942)"/></g><path class="p" d="M587.561,1159.254l-6.456,5.621a1.12,1.12,0,0,1-1.58-.109l-8.237-9.46a1.12,1.12,0,0,1,.109-1.58l6.456-5.621a1.12,1.12,0,0,1,1.58.109l8.237,9.46A1.12,1.12,0,0,1,587.561,1159.254Z" transform="translate(-548.999 -1123.542)"/><path class="i" d="M659.4,1244.992c-.092.024-3.429.417-3.43,1.106a2.074,2.074,0,0,0,1.9,1.815,10.952,10.952,0,0,0,2.1-1.6Z" transform="translate(-630.392 -1209.989)"/><path class="i" d="M680.88,1031.9a11.1,11.1,0,0,1-.6-4.06l-.252.05-4.239.839s.1,1.334.1,2.591c0,.01,0,.021,0,.031.005,1.092-.738,2.187,1.013,2.62C677.448,1034.1,681.677,1033.834,680.88,1031.9Z" transform="translate(-653.583 -1016.785)"/><path class="q" d="M684.329,1029.129s.1,1.334.1,2.591c2.372-.067,3.626-2.231,4.14-3.431Z" transform="translate(-662.119 -1017.185)"/><path class="i" d="M680.321,970.281s5.613.093,5.617-3.4.731-5.839-2.853-6.046-4.281,1.127-4.566,2.293S678.861,970.167,680.321,970.281Z" transform="translate(-658.903 -957.147)"/><path class="h" d="M662.086,954.495s1.537,4.1,3.9,4.708,3.381-.025,3.381-.025a6.678,6.678,0,0,1-2.185-3.946S663.245,952.542,662.086,954.495Z" transform="translate(-640.46 -950.895)"/><path class="h" d="M727.46,957.851a3.482,3.482,0,0,0-1.609,1.257,5.652,5.652,0,0,0-.759,3.21s-1.158-2.788.143-4.365C726.616,956.281,727.46,957.851,727.46,957.851Z" transform="translate(-705.453 -953.919)"/><path class="i" d="M663.369,1243.413a3.815,3.815,0,0,0-1.334-.182c-.535.075-.93-.011-.93.139s.69.219.837.291A7.267,7.267,0,0,0,663.369,1243.413Z" transform="translate(-634.359 -1208.41)"/><path class="j" d="M563.759,1067.964a6.8,6.8,0,0,1,5.512,3.992c1.527,3.713,4.425,9.946,4.425,11s-1.619,4.281-10.312,6.14c0,0-.9-.946-.845-1.694,0,0,5.427-4.661,5.412-5.206-.028-1.031-.89-2.391-5.654-7.157C561,1073.739,563.759,1067.964,563.759,1067.964Z" transform="translate(-533.751 -1052.484)"/></g></g></svg>