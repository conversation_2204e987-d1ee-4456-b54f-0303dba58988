import { Button, Modal, Table, Form } from 'react-bootstrap';
import { ModifyType, RemarksTable } from "./Utility";

const AddRemarksModal = (props) => {
  const { data } = props;

  return (
    <>
      <Modal
        {...props}
        //size="lg"
        //aria-labelledby="contained-modal-title-vcenter"
        centered
        onHide={props.onAddRemarksCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            Add Remarks
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
        <Form>
            <Form.Group
              className="mb-3"
              controlId="exampleForm.ControlTextarea1"
            >
              <Form.Label>Add Remarks</Form.Label>
              <Form.Control as="textarea" rows={3} onChange={props.handleRemarkInputChange} />
            </Form.Group>
          </Form>

        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={props.onAddRemarksCancel}>Close</Button>
          <Button variant="primary" onClick={props.handleRemarkSave}>Save</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default AddRemarksModal;