
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, PostCommunicationData,ValidateAddLeadToPriorityQueue
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDown from './Common/DropDown';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func } from "prop-types";
import moment from "moment";
import './customStyling.css';

class QuoteLeadsList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Quote Shared Leads",
      QuoteLeadsList: [],
      showAssignLeadPopUp: false,
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      formvalue: {},
    };
    this.dtRef = React.createRef();
    this.myInputRef = React.createRef();

    this.columnlist = [
      {
        name: "LeadId",
        selector: "leadId",        
      },
      {
        name: "ParentId",
        selector: "ParentId",        
      },
      {
        name: "StatusName",
        selector: "StatusName",        
      },
      {
        name: "Lead Status",
        selector: "QuoteStatus",        
      },
      {
        name: "Quote Updated on",
        selector: "QuoteUpdatedOn",   
        cell: row => <div className="calldate">{row.QuoteUpdatedOn ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.QuoteUpdatedOn}</Moment> : "N.A"}</div>,  
        type: "datetime",
        sortable: true,   
      },
      {
        name: "Last contacted Date",
        selector: "CallDateTime", 
        cell: row => <div className="calldate">{row.CallDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.CallDateTime}</Moment> : "N.A"}</div>,  
        type: "datetime", 
        sortable: true,        
      },
      
    ];
  }
  
  OpenAssignLeadPopUp(row) {
    this.setState({ showAssignLeadPopUp: true });
  }
  
  componentWillReceiveProps(nextProps) {
    
    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetQuoteLeads"]) {
        let CBList = nextProps.CommonData["GetQuoteLeads"];
        this.setState({ QuoteLeadsList: CBList[0] });
      }
    }
  }

  handleClose() {
    this.setState({ showAssignLeadPopUp: false })
  }

  componentDidMount() {
    this.fetchCallBackData();
  }

  fetchCallBackData() {debugger;
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetQuoteLeads",
      params: [{ agentId: getuser().UserID }]
    });

    if (this.state.SelectedRows.length > 0) {
      this.dtRef.current.handleClearRows();
    }

  }

  onSelectedAgent(e) {
    this.setState({ SelectedAgentAssigTo: e.target.value });
  }
  AssignLead() {debugger;
    const { SelectedRows, SelectedAgentAssigTo } = this.state;
    for (let index = 0; index < SelectedRows.length; index++) {
      const element = SelectedRows[index];

      var lead = {
        "LeadId": element.leadId,
        "Name": element.Name,
        "CustomerId": element.CustomerID,
        "UserID": parseInt(getuser().UserID),
        "Priority": 0,
        "ProductId": element.ProductID,
        "Reason": 'Manual added',
        "ReasonId": 33,
        "CallStatus": "",
        "IsAddLeadtoQueue":1,
        "IsNeedToValidate":0
      }
      var reqData = {
        "UserId": parseInt(getuser().UserID),
        "Leads": [lead]
      };

      ValidateAddLeadToPriorityQueue(reqData , function (resultData)
        {
          try{
            if (resultData != null) {
              if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
                  toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
              }
              toast(`${resultData.data.data.message}`, { type: 'error' });
            }
          }catch(e){
            toast(`${e}`, { type: 'error' });
          console.log(e)
        }
        }.bind(this));
      // this.props.PostCommunicationData({
      //   root: 'communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
      //   data: reqData
      // }, function (data) {
      //   toast("Lead (" + element.leadId + ") Added in Call Queue", { type: 'success' });
      // });
    }

    this.setState({ showAssignLeadPopUp: false });

    // setTimeout(function () {
    //   this.fetchCallBackData();
    // }.bind(this), 300);
  }
  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }
  render() {
    const columns = this.columnlist;
    const { items, PageTitle, QuoteLeadsList,showAssignLeadPopUp, showAlert, AlertMsg, AlertVarient, ReportTime, SelectedRows } = this.state;
    console.log(this.state.paymentStatuses);
    let selectedLeads = [];
    SelectedRows.forEach(element => {
      selectedLeads.push(element.leadId);
    });

    return (
      <>
        <div className="content QuoteLeadsListLeadsContainer">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={6}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <CardTitle tag="h5">
                        {ReportTime ? <Moment format="DD/MM/YYYY HH:mm:ss">{ReportTime}</Moment> : null}
                      </CardTitle>

                    </Col>
                    <Col md={2}>
                      {this.state.hideAssign ? null : <button className="btn btn-info btn-sm float-right" onClick={() => this.OpenAssignLeadPopUp()} >Add Lead</button>}
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={(QuoteLeadsList && QuoteLeadsList.length > 0) ? QuoteLeadsList : []}
                    defaultSortField="GracePeriodDate"
                    defaultSortAsc={false}
                    selectableRows={true}
                    export={false}
                    ref={this.dtRef}
                    onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showAssignLeadPopUp} onHide={this.handleClose.bind(this)} >
            <Modal.Header closeButton>
              <Modal.Title>Add Leads</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Row>
                <Col>
                  LeadId : {selectedLeads.join()}
                </Col>
              </Row>
              <Row>
                <Col>
                  {/* <Form.Control as="select" name="products" onChange={this.onSelectedAgent.bind(this)} >
                    <option key={0} value={0}>Select</option>
                    {
                      this.bindAgentDropdown()
                    }
                  </Form.Control> */}
                </Col>
              </Row>

            </Modal.Body>
            <Modal.Footer>

              <If condition={this.state.SelectedRows.length > 0}>
                <Then>
                  <Button variant="primary" onClick={this.AssignLead.bind(this)}>Add Lead</Button>
                </Then>
              </If>
              <Button variant="secondary" onClick={this.handleClose.bind(this)}>
                Close
                </Button>
            </Modal.Footer>
          </Modal>


        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    PostCommunicationData
  }
)(QuoteLeadsList);