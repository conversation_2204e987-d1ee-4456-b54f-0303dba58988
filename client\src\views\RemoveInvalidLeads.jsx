import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardBody,
    CardTitle,
    Row,
    Col
  } from "reactstrap";
  import { GetInvalidLeadsResponse,RemoveInvalidLeadsResponse  } from "../store/actions/CommonAction";
import 'react-toastify/dist/ReactToastify.css';
import {  Button,  Form } from 'react-bootstrap';
import DropDown from "./Common/DropDown";
import { toast } from 'react-toastify';

const RemoveInvalidLeads = () =>{
    const SelectList =
        [{ Id: "1", Display: "LeadId" },{ Id: "2", Display: "Employee Code" } ]

    let [SelectSearch, setSelectSearch] = useState(0);
    let [Value, setValue] = useState(0);
    let [ConsolidatedData, setConsolidatedData] = useState([]);
    let [IsSearched, setIsSearched] = useState(0);

    

    const SearchChange = (e) => { 
        setSelectSearch(e.target.value)
    }

    const GetLeads = () => {
        if(Value)
        {
          if(SelectSearch == 1)
          {
            if(parseInt(Value) > 0){
            var reqData = 
            {
              "LeadId": parseInt(Value),
            };}
            else{
              return;
            }
          }
          else if(SelectSearch == 2)
          {
            var reqData = 
            {
              "EmpCode": Value,
            };
          }
          GetInvalidLeadsResponse(reqData, function (resultData)
          {
            try
            {
              if (resultData != null) 
              {
                if (resultData) 
                  {
                    setConsolidatedData(resultData.data.data)
                  }
              }
              else{
                setConsolidatedData([]);
              }
            }
            catch(e)
            {
              toast(`${e}`, { type: 'error' });
            }
          })
          setIsSearched(1);
        }
      }

    const RemoveLeads = (LeadId, UserId) => {
      var reqData = 
            {
              "UserId": UserId,
              "LeadId" : LeadId
            };
      
      RemoveInvalidLeadsResponse(reqData, function (resultData)
          {
            try
            {
              if (resultData != null) 
              {
                if (resultData.data.data == true) 
                  {
                    window.alert("Lead Successfully Removed!");
                    GetLeads();
                  }
              }
            }
            catch(e)
            {
              toast(`${e}`, { type: 'error' });
            }
          })
    }

    return(
        <div className="content">
            <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">Remove Invalid Leads</CardTitle>
                    </Col>
                    <Col md={1}>

                    </Col>
                  </Row><Row>
                    <Col md={4}>
                        <DropDown firstoptionvalue={true} items={SelectList} onChange={SearchChange}>
                        </DropDown><br></br>
                    </Col>
                    {SelectSearch == 1 && <Col md={2}>
                        <Form.Control
                        type="text"
                        name="LeadId" className="empid" id="LeadId" 
                        placeholder={"Enter LeadId"}
                        onChange={(e)=>{setValue(parseInt(e.target.value))}}
                        />
                    </Col>}
                    {SelectSearch == 2 && <Col md={2}>
                        <Form.Control
                        type="text"
                        name="EmpCode" className="empid" id="EmpCode" 
                        placeholder={"Enter Employee Code"}
                        onChange={(e)=>{setValue(e.target.value)}}
                        />
                    </Col>}
                    {(SelectSearch == 1 || SelectSearch == 2) &&<Col md={1}>
                      <Button variant="primary" onClick={GetLeads}>
                        Fetch
                      </Button>
                    </Col>}
                </Row>
                </CardHeader>
                <CardBody>
                  {IsSearched == 1 && <div className="t1">
                    {ConsolidatedData && ConsolidatedData.length > 0 ? 
                    <table className="table">
                      <thead>
                        <tr>
                            <th>S No</th>
                            <th>LeadId</th>
                            <th>Customer Name</th>
                            <th>Assigned to User</th>
                            <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {
                            ConsolidatedData && ConsolidatedData.length > 0 &&
                            ConsolidatedData.map((data, index) => (
                                <tr>
                                    <td>{index + 1}</td>
                                    <td>{data.LeadId}</td>
                                    <td>{data.CustomerName}</td>
                                    <td><span>{data.UserName} </span><span>({data.EmpCode})</span></td>
                                    <th><Button variant="danger" onClick={()=>{RemoveLeads(data.LeadId,data.UserId)}}>Remove</Button></th>
                                </tr>
                            ))
                        }
                      </tbody>
                    </table> 
                    :
                    <h4>No data Found!</h4>}
                  </div>
                  }
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
        
    )
}

export default RemoveInvalidLeads;