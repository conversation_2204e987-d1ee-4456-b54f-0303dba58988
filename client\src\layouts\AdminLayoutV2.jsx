import React, { useState, useEffect, useRef } from 'react';

import {
  Routes,
  Route
} from "react-router-dom";
import UnAuthenticated from "../views/UnAuthenticated";
// import DemoNavbar from "components/Navbars/DemoNavbar.jsx";
import Footer from "components/Footer/Footer.jsx";
// import Sidebar from "components/Sidebar/Sidebar.jsx";
import { getUrlParameter, getCookie, setCookie, getuser } from '../utility/utility.jsx';
import {
  GetDataDirect
} from "../store/actions/CommonAction";
// import { If } from 'react-if';
import routes from "../routes";
import config from "../config";

const AdminLayoutV2 = (props) => {

  const [user, setUser] = useState({});
  const [Menu, setMenu] = useState(null);
  const [location, setLocation] = useState(null);

  const Logout = () => {
    setCookie("userid", "", -1);
    localStorage.clear();
    window.location.href = config.site_base_url;
  }

  const getLocation = () => {
    var loc = window.location.href;
    let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
    console.log(lastUrl);
    if (lastUrl == "OtpVerification") {
      return false;
    }
    if (lastUrl == "SmsInfo") {
      return false;
    }
    if (lastUrl == "KyaInfoVc") {
      return false;
    }
    return true;
  }

  useEffect(() => {
    // var userid = getCookie("AgentId");
    // if(getUrlParameter("u") != userid){
    //   this.props.history.push('/client/UnAuthenticated')
    // }
    let location = getLocation();
    setLocation(location)
    let userid = null;
    userid = getUrlParameter("u") != "" ? getUrlParameter("u") : getCookie("AgentId");

    if (userid != "") {
      GetDataDirect({
        root: "UserDetails",
        UserID: userid,
        statename: "Users-" + userid,
        state: true
      }, function (result) {
        setUser(result[0]);
      });

      GetDataDirect({
        root: "FetchMenuMaster",
        UserId: userid,
        statename: "Menu-" + userid,
        state: false
      }, function (result) {
        setMenu(result?.[0]);
      });

    }
    else {
      Logout();
    }
  }, []);

  const CheckAuthentication = (prop, key) => {

    const RoleIdCheck = prop?.RoleIdCheck || false;
    const RoleIds = prop?.RoleId || [];
    let IsRoleIdAuthorized = true;
    const { RoleId } = getuser() || {};

    if (RoleIdCheck && !RoleIds.includes(RoleId)) {
      IsRoleIdAuthorized = false;
    }

    if (config.byMenuSecurity) {
      return <Route
        path={prop.path}
        element={prop.component}
        key={key} />
    }
    if (!Menu) {
      return;
    }
    let hasMenu = false;
    for (let index = 0; index < Menu.length; index++) {
      const element  = Menu[index];
      let path = prop.path.replace("/", "");
      if (element.URL.includes(path)) {
        hasMenu = true
        break;
      }
    }

    if (IsRoleIdAuthorized && (hasMenu || !prop.authCheck)) {
      return (<Route
        path={prop.path}
        element={prop.component}
        key={key} />)
    }
    else {
      return (<Route
        path={prop.path}
        element={<UnAuthenticated />}
        key={key} />)

    }
  }


  return (
    <div className="wrapper">
      {/* <If condition={location}>
        <Sidebar
          {...props}
          routes={routes}
          bgColor={this.state.backgroundColor}
          activeColor={this.state.activeColor}
          inMatrix={this.state.inMatrix}
        />
      </If> */}
      <div className={(location) ? "main-panel full" : ""} 
        // ref={this.mainPanel}
      >
        {/* <If condition={location}>
          <DemoNavbar {...this.props} inMatrix={this.state.inMatrix} Logout={this.Logout} />
        </If> */}
        <Routes>
          {
            routes.map((prop, key) => {
              return CheckAuthentication(prop, key)
            })
          }
        </Routes>
        <Footer fluid />
      </div>
      
      {/* <FixedPlugin
      bgColor={this.state.backgroundColor}
      activeColor={this.state.activeColor}
      handleActiveClick={this.handleActiveClick}
      handleBgClick={this.handleBgClick}
    /> */}
    </div>
  );
}

export default AdminLayoutV2;
