const Utility = require("../../Libs/Utility");
const conf = require('../../env_config');
const { POST_JagEmailer } = require('./joi');
const { ObjectID } = require('mongodb');

function getHeaders() {
  return {
    "REQUESTINGSYSTEM": "Matrix",
    "TOKEN": conf.PB_SERVICE_TOKEN
  };
}

async function JagEmailer(req, res) {

  const { error } = POST_JagEmailer.validate(req.body);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {
    let succeeded = [];
    let failed = [];
    let db = matrixdashboarddb;
    const { TriggerName, Collection } = req.body || {};
    let agents = await db.collection(Collection)
      .find({ IsActive: true})
      .toArray();

    for (let i = 0; i < agents.length; i++) {

      let agentDetails = agents[i];
      let id = agentDetails._id;
      const { Email } = agents[i];

      delete agentDetails.Email;

      const body = {
        TriggerName: TriggerName,
        LeadId: 0,
        CommunicationType: 1,
        ProductId: 2,
        To: [
          Email
        ],
        InputData: agentDetails
      };

      const url = conf.BMS_SEND_COMMUNICATION_URL;
      const headers = getHeaders();

      let response = await Utility.API_POST(url, body, headers);

      if (response?.IsSuccess) {
        await db.collection(Collection)
          .updateOne({ _id: ObjectID(id) }, { $set : { IsActive: false }});
          succeeded.push(agentDetails);
      } else {
        failed.push(agentDetails);
      }
    }

    return res.status(200).json({
      status: 200,
      message: 'success',
      succeeded: succeeded.length || 0,
      failed: failed.length || 0
    });
    
  }
  catch (e) {
    return res.status(400).json({
      status: 400,
      data: e.toString()
    })
  }
  finally {
    // return;
  }
}

module.exports = {
  JagEmailer: JagEmailer
};


