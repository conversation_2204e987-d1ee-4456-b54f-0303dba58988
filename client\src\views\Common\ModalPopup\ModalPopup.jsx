import React from "react";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";

const ModalPopup = ({
  show,
  onClose,
  onConfirm,
  title,
  children,
  confirmText = "Confirm",
  cancelText = "Cancel",
  showConfirmButton = true,
  showCancelButton = true,
  isLoading = false,
}) => {
  return (
    <Modal show={show} onHide={onClose} centered>
      {title && (
        <Modal.Header closeButton>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>
      )}

      <Modal.Body>
        {children}
      </Modal.Body>

      {(showConfirmButton || showCancelButton) && (
        <Modal.Footer>
          {showCancelButton && (
            <Button variant="secondary" onClick={onClose} disabled={isLoading}>
              {cancelText}
            </Button>
          )}
          {showConfirmButton && (
            <Button variant="primary" onClick={onConfirm} disabled={isLoading}>
              {isLoading ? "Please wait..." : confirmText}
            </Button>
          )}
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default ModalPopup;
