import Axios from "axios";
import React, { Component } from "react";
import config from "../../config.jsx";
import { ButtonGroup, Button, Modal, Form } from "react-bootstrap";
import { getuser } from "utility/utility";
import {
  GetCommonspData, GetCommonspDataV2
} from "../../store/actions/CommonAction";
import {
  GetCommonData
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import DataTable from "../Common/DataTableWithFilter";
import {
  fnBindRootData,
  fnDatatableCol
} from "../../utility/utility.jsx";
import { CardTitle, Row, Col } from "reactstrap";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Link } from "react-router-dom";
import Moment from "react-moment";

const uuid = require("uuid");
let AxiosInstanceUpload = Axios.create({
  baseURL: "",
  header: {},
  withCredentials: true
});

const BulkLeadUploadType = 3;
const PaymentLinkUploads = 4;
const UpsellLeadCreate = 5;
const BulkBooking = 6;
const LastYearLostCases = 7;
const RenewalMissingLeads = 8;
const CrossSmeUploadType = 9;
const SmeFosBulkUpload = 10;

class DashboardSMEUploads extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      showErrorConsole: false,
      showGrid: true,
      items: [],
      Uploadeditems: [],
      ErrorLogitems: [],
      activePage: 1,
      root: "GetSmeRenewalUploadsData",
      UploadFileDetails: "GetSmeRenewalBulkUploadFileDetailsData",
      ErrorConsoleCollection: "FileProcessingErrorLogs",
      PageTitle: "DashboardSMEUploads",
      DownloadedFile: "",
      selectedFile: null,
      selectedProduct: '131',
      selectedProcess: localStorage.getItem('SmeUploadsProcessId') || "0"
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleErrorConsoleClose = this.handleErrorConsoleClose.bind(this);
    this.columnlist = [
      {
        name: "LeadID",
        label: "LeadID",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
      },
      {
        name: "ExistingLeadID",
        label: "ExistingLeadID",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
      },
      {
        name: "OldPolicyNo",
        label: "OldPolicyNo",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "ErrorMessage",
        label: "StatusMessage",
        type: "string",
        cell: (row) => (
          <div>
            {row.ErrorMessage ? (
              <div data-toggle="tooltip" title={row.ErrorMessage}>
                {" "}
                {row.ErrorMessage}
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        editable: false,
        sortable: true,
      },
      {
        name: "Name",
        label: "Name",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Email",
        label: "Email",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyStartDate",
        label: "PolicyStartDate",
        type: "datetime",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyExpiryDate",
        label: "PolicyExpiryDate",
        type: "datetime",
        editable: false,
        sortable: true,
      },
      {
        name: "UploadedLeadId",
        label: "BookingId",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "MobileNo",
        label: "MobileNo",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "CompanyName",
        label: "CompanyName",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "City",
        label: "City",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "ProductID",
        label: "ProductID",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "SubProductId",
        label: "SubProductId",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "NumberOfEmployees",
        label: "NumberOfEmployees",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "LeadSource",
        label: "LeadSource",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "UTM_Medium",
        label: "UTM_Medium",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Utm_Source",
        label: "Utm_Source",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Utm_Campaign",
        label: "Utm_Campaign",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "AssignToUserID",
        label: "AssignToUserID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "SumInsured",
        label: "SumInsured",
        type: "decimal",
        editable: false,
        sortable: true,
      },
      {
        name: "CustomerID",
        label: "CustomerID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "SupplierID",
        label: "InsurerId",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PlanID",
        label: "PlanID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyTypeName",
        label: "PolicyType",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "InsuredName",
        label: "InsuredName",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Occupancy",
        label: "Occupancy",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyTerm",
        label: "PolicyTerm",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PayTerm",
        label: "PayTerm",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "MasterPolicyNo",
        label: "PolicyNumber",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "BaseSIPremium",
        label: "Premium",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PaymentMode",
        label: "PaymentMode",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "PaymentFrequency",
        label: "PaymentFrequency",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "BankName",
        label: "BankName",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "EmployeeID",
        label: "SalesAgent",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Remarks",
        label: "Remarks",
        type: "string",
        editable: false,
        sortable: true,
      }
    ];
    this.UploadedFilecolumnlist = [
      {
        name: "Filename",
        label: "File Name",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
        width: "250px",
      },
      {
        name: "Status",
        label: "Status",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
        width: "130px",
      },
      {
        name: "createdon",
        label: "UploadedOn",
        type: "datetime",
        editable: false,
        cell: (row) =>
          row.createdon == null ? (
            "N/A"
          ) : (
            <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
              {row.createdon}
            </Moment>
          ),
        format: "DD/MM/YYYY HH:mm:ss",
        sortable: true,
        searchable: true,
        width: "150px",
      },
      {
        name: "UploadedBy",
        label: "UploadedBy",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "250px",
      },
      {
        name: "UniqueId",
        label: "UniqueId",
        type: "string",
        hide: true,
        editable: false,
        sortable: true,
        searchable: true,
      },
      {
        name: "GroupId",
        label: "GroupId",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: true,
      },
      {
        name: "AssignedUser",
        label: "AssignedUser",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: true,
      },
      {
        name: "ProcessType",
        label: "ProcessType",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      },
      {
        name: "SMEProcessType",
        label: "SMEProcessType",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      },
      {
        name: "ProductId",
        label: "ProductId",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      }
    ];
    this.ErrorConsolecolumnlist = [
      {
        name: "TrackingId",
        label: "TrackingId",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "250px",
      },
      {
        name: "UploadedBy",
        label: "UploadedBy",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "130px",
      },
      {
        name: "Createon",
        label: "CreatedOn",
        type: "datetime",
        editable: false,
        cell: (row) =>
          row.Createon == null ? (
            "N/A"
          ) : (
            <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
              {row.Createon}
            </Moment>
          ),
        format: "DD/MM/YYYY HH:mm:ss",
        sortable: true,
        searchable: true,
        width: "150px",
      },
      {
        name: "Exception",
        label: "Error",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "300px",
      }
    ];
     // Define column lists for SmeFosBulkUpload
     this.columnlistSmeFosBulkUpload = [
      {
        name: "ErrorMessage",
        label: "StatusMessage",
        type: "string",
        cell: (row) => (
          <div>
            {row.ErrorMessage ? (
              <div data-toggle="tooltip" title={row.ErrorMessage}>
                {" "}
                {row.ErrorMessage}
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        editable: false,
        sortable: true,
      },
      { name: "LeadID", label: "LeadID",type: "string",editable: false,sortable: true,searchable: true},
      { name: "ExistingLeadID",label: "ExistingLeadID",type: "string",editable: false,sortable: true,searchable: true},
      { name: "CIN", label: "CIN", type: "string", editable: false, sortable: true, cell: (row) => (
        <div>
          {row.CIN ? (
            <div data-toggle="tooltip" title={row.CIN}>
              {" "}
              {row.CIN}
            </div>
          ) : (
            ""
          )}
        </div>
      )},
      { name: "CompanyName", label: "Company Name", type: "string", editable: false, sortable: true },
      { name: "ClientCityName", label: "Client City Name", type: "string", editable: false, sortable: true},
      { name: "ParentCompany", label: "Parent Company", type: "string", editable: false, sortable: true},
      { name: "Name", label: "Name", type: "string", editable: false, sortable: true},
      { name: "MobileNo", label: "Mobile No", type: "string", editable: false, sortable: true},
      { name: "EmailId", label: "Email ID", type: "string", editable: false, sortable: true},
      { name: "ExecutiveRole", label: "Executive Role", type: "string", editable: false, sortable: true},
      { name: "IndustryType", label: "Industry Type", type: "string", editable: false, sortable: true,  cell: (row) => (
        <div>
          {row.IndustryType ? (
            <div data-toggle="tooltip" title={row.IndustryType}>
              {" "}
              {row.IndustryType}
            </div>
          ) : (
            ""
          )}
        </div>
      )},
      { name: "Utm_Source", label: "UTM Source", type: "string", editable: false, sortable: true},
      { name: "PolicyTypeName", label: "Policy Type", type: "string", editable: false, sortable: true},
      { name: "DecisionMakerCity", label: "Decision Maker City Name", type: "string", editable: false, sortable: true},
      { name: "LinkedinLink", label: "LinkedIn Link", type: "string", editable: false, sortable: true},
      { 
        name: "AltMobileNo", 
        label: "Alternate Mobile No", 
        type: "string", 
        cell: (row) => (
          <div>
            {row.AltMobileNo ? (
              <div data-toggle="tooltip" title={row.AltMobileNo}>
                {" "}
                {row.AltMobileNo}
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        editable: false,
        sortable: true
      },
      { 
        name: "AltEmailId", 
        label: "Alternate Email ID", 
        type: "string", 
        cell: (row) => (
          <div>
            {row.AltEmailId ? (
              <div data-toggle="tooltip" title={row.AltEmailId}>
                {" "}
                {row.AltEmailId}
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        editable: false,
        sortable: true
      },
      { name: "Probability", label: "Probability", type: "string", editable: false, sortable: true},
      { name: "Utm_Medium", label: "UTM Medium", type: "string", editable: false, sortable: true},
      { name: "CrossSellSubProductIds", label: "Cross Sell Sub Product IDs", type: "string", editable: false, sortable: true},
      { name: "Discussion", label: "Discussion", type: "string", editable: false, sortable: true},
      { name: "ClaimHistory", label: "Claim History", type: "string", editable: false, sortable: true},
      { name: "ExistingBroker", label: "Existing Broker Name", type: "string", editable: false, sortable: true,  cell: (row) => (
        <div>
          {row.ExistingBroker ? (
            <div data-toggle="tooltip" title={row.ExistingBroker}>
              {" "}
              {row.ExistingBroker}
            </div>
          ) : (
            ""
          )}
        </div>
      )},
      { name: "ExistingInsurer", label: "Existing Insurer Name", type: "string", editable: false, sortable: true, cell: (row) => (
        <div>
          {row.ExistingInsurer ? (
            <div data-toggle="tooltip" title={row.ExistingInsurer}>
              {" "}
              {row.ExistingInsurer}
            </div>
          ) : (
            ""
          )}
        </div>
      )},
      { name: "ExistingTPA", label: "Existing TPA Name", type: "string", editable: false, sortable: true, cell: (row) => (
        <div>
          {row.ExistingTPA ? (
            <div data-toggle="tooltip" title={row.ExistingTPA}>
              {" "}
              {row.ExistingTPA}
            </div>
          ) : (
            ""
          )}
        </div>
      )},
      { name: "SumInsured", label: "Sum Insured", type: "decimal", editable: false, sortable: true},
      { name: "PremiumAtInception", label: "Premium At Inception", type: "decimal", editable: false, sortable: true},
      { name: "PolicyStartDate", label: "Policy Start Date", type: "datetime", editable: false, sortable: true},
      { name: "SubProductId", label: "Sub Product", type: "string", editable: false, sortable: true},
      {
        name: "AssignToUserID",
        label: "AssignToUserID",
        type: "number",
        editable: false,
        sortable: true,
      },

    ];

  }

  onFileChange = (event) => {
    this.setState({ selectedFile: event.target.files[0] });
  };

  handleProcessChange = (event) => {
    this.setState({ selectedProcess: event.target.value });
    if (event.target.value == PaymentLinkUploads) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/PaymentLinkUploadSample.xlsx",
      });
    }
    if (event.target.value == UpsellLeadCreate) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SMEUpsellBulkLeadCreateSample.xlsx",
      });
    }
    if (event.target.value == BulkBooking) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeBulkBookingSample.xlsx",
      });
    }
    if (event.target.value == LastYearLostCases) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeLastYearLostCasesSample.xlsx",
      });
    }
    if (event.target.value == BulkLeadUploadType) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SMEBulkLeadCreateSample.xlsx",
      });
    }
    if (event.target.value == RenewalMissingLeads) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeRenewalMissingLeadsSample.xlsx",
      });
    }
    if (event.target.value == CrossSmeUploadType)
    {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/CrossSmeUploadPanel.xlsx",
      });
    }
    if (event.target.value == SmeFosBulkUpload)
      {
        this.setState({
          DownloadedFile: "../../SampleExcelfiles/SmeFosBulkUpload.xlsx",
        });
      }
    localStorage.setItem('SmeUploadsProcessId', event.target.value)
    this.BindGridOnSaveFile(event.target.value);
  };

  handleFileDownload = (event) => {
    if (this.state.selectedProcess == PaymentLinkUploads || localStorage.getItem('SmeUploadsProcessId') == PaymentLinkUploads) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/PaymentLinkUploadSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == UpsellLeadCreate || localStorage.getItem('SmeUploadsProcessId') == UpsellLeadCreate) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SMEUpsellBulkLeadCreateSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == BulkBooking || localStorage.getItem('SmeUploadsProcessId') == BulkBooking) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeBulkBookingSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == LastYearLostCases || localStorage.getItem('SmeUploadsProcessId') == LastYearLostCases) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeLastYearLostCasesSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == BulkLeadUploadType || localStorage.getItem('SmeUploadsProcessId') == BulkLeadUploadType) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SMEBulkLeadCreateSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == RenewalMissingLeads || localStorage.getItem('SmeUploadsProcessId') == RenewalMissingLeads) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeRenewalMissingLeadsSample.xlsx",
      });
    }
    else if (this.state.selectedProcess == CrossSmeUploadType || localStorage.getItem('SmeUploadsProcessId') == CrossSmeUploadType) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/CrossSmeUploadPanel.xlsx",
      });
    }
    else if (this.state.selectedProcess == SmeFosBulkUpload || localStorage.getItem('SmeUploadsProcessId') == SmeFosBulkUpload) {
      this.setState({
        DownloadedFile: "../../SampleExcelfiles/SmeFosBulkUpload.xlsx",
      });
    }
    else {
      event.preventDefault();
      toast("Please select a valid Process", { type: "error" });
    }
  };

  onFileUpload = () => {
    if (this.state.selectedProcess > 0) {
      if
        (
        this.state.selectedFile &&
        this.isExcel(this.state.selectedFile.name)
      ) {
        var Uploadedby = getuser().UserID;
        var SMEProcessType = this.state.selectedProcess;
        var uniqueId = uuid.v1().toUpperCase();
        const data = {
          FileName: this.state.selectedFile.name.trim(),
          Processtype: 2,
          UniqueId: uniqueId.trim(),
          productId: this.state.selectedProduct.trim(),
          Status: "Saved",
          Uploadedby: Uploadedby.trim(),
          GroupId: -1,
          AssignedUser: -1,
          SMEProcessType: SMEProcessType.trim(),
          UploadedFile: this.state.selectedFile,
          folderName: "SME"
        };

        this.commonuploadFileToUrlService(data, "api/UploadFile/UploadFileToS3BucketPvt")
          .then((result) => {
            if (result.Data == "") {
              toast("Error in uploading file to S3", { type: "error" });
            }
            else {
              localStorage.setItem('SmeUploadsProcessId', SMEProcessType.trim())
              toast("File Saved successfully...", { type: "success" });
              this.BindGridOnSaveFile(SMEProcessType);
              this.setState({ selectedFile: null });
              window.location.reload();
            }
          })
          .catch((err) => {
            toast("Error in saving file", { type: "error" });
          });
      } else {
        toast("Please select a valid excel file", { type: "error" });
      }
    }
    else {
      toast("Please select a valid Process", { type: "error" });
    }
  };

  onFileProcessing = (trackingid, processtype, SMEProcessType, ProductId) => {
    const data = {
      ProcessType: processtype,
      UniqueId: trackingid,
      UploadedBy: getuser().UserID,
      ProductId: ProductId,
      ProcessId: SMEProcessType
    };

    this.ProcessuploadedFile
      (
        data,
        "api/UpdateRenewalLeads/ProcessSmeUploads"
      )
      .then((result) => {
        this.BindGridOnSaveFile(SMEProcessType);
        if (result.includes("Success")) {
          toast(result, { type: "success" });
        } else {
          toast(result, { type: "error" });
        }
      })
      .catch((result) => {
        this.BindGridOnSaveFile(SMEProcessType);
        toast("File not Staged..." + result, { type: "error" });
      });
  };

  componentDidMount() {
    this.BindGridOnSaveFile(this.state.selectedProcess);
  }

  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({
        Uploadeditems: nextProps.CommonData[this.state.UploadFileDetails],
      });
      this.setState({ ErrorLogitems: nextProps.CommonData[this.state.ErrorConsoleCollection] });
    }
  }

  onShowUploadedData = (trackingId) => {
    this.state.showModal = true;
    var processId = localStorage.getItem('SmeUploadsProcessId') ;
    if (processId == SmeFosBulkUpload) {
      this.columnlistSmeFosBulkUpload.map((col) => fnBindRootData(col, this.props));
    }
    else {
      this.columnlist.map((col) => fnBindRootData(col, this.props));
    }
    let UploadedBy = (getuser().UserID).trim();
    let Source = "SmeUpload".trim();
    if (localStorage.getItem('SmeUploadsProcessId') == BulkBooking) {
      Source = "SmeBulkBooking";
    }
    else if (localStorage.getItem('SmeUploadsProcessId') == LastYearLostCases) {
      Source = "LastYearLostCases";
    }
    else if (localStorage.getItem('SmeUploadsProcessId') == BulkLeadUploadType) {
      Source = "SmeBulkLeadUpload";
    }
    else if (localStorage.getItem('SmeUploadsProcessId') == RenewalMissingLeads) {
      Source = "RenewalMissingLeads";
    }
    else if (localStorage.getItem('SmeUploadsProcessId') == CrossSmeUploadType) {
      Source = "CrossSmeUploadType";
    }
    else if (processId == SmeFosBulkUpload) {
      Source = "SmeFosBulkUpload";
    }

    let TrackingId = trackingId.trim();
    this.props.GetCommonspDataV2({
      root: this.state.root,
      c: "R",
      params: [
        { Source: Source, TrackingId: TrackingId },
      ],
    });
  };

  onShowErrorConsole = (trackingId) => {
    this.state.showErrorConsole = true;
    this.ErrorConsolecolumnlist.map((col) => fnBindRootData(col, this.props));
    let TrackingId = trackingId.trim();
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.ErrorConsoleCollection,
      con: { 'TrackingId': TrackingId },
      c: "L"
    });
  };
  BindGridOnSaveFile = (processType) => {
    this.state.showGrid = true;
    this.UploadedFilecolumnlist.map((col) => fnBindRootData(col, this.props));
    this.props.GetCommonspDataV2({
      root: this.state.UploadFileDetails,
      c: "R",
      params: [processType !== "0" ? { ProcessId: processType} : {}],
    });
  };

  fnDatatableCol2() {
    var ErrorLogscolumns = fnDatatableCol(this.ErrorConsolecolumnlist);
    return ErrorLogscolumns;
  }

  fnDatatableCol1() {
    var columns;
    if (this.state.selectedProcess == SmeFosBulkUpload) {
      columns = fnDatatableCol(this.columnlistSmeFosBulkUpload);
    }
    else {
      columns = fnDatatableCol(this.columnlist);
    }
    return columns;
  }

  fnDatatableColsUploaded() {
    var UploadedColumn = fnDatatableCol(this.UploadedFilecolumnlist);
    UploadedColumn.splice(2, 0, {
      name: "Stage Data",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            id="btnstaging"
            className="btn btn-success btnSmall"
            disabled={
              row.Status == "DataStaged" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured" || row.Status == "DateOutOfLimit"
            }
            onClick={() => this.handleEdit(row)}
          >
            Start Staging
          </Button>
        </ButtonGroup>
      ),
    });

    UploadedColumn.splice(3, 0, {
      name: "View Staged Data",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            className="btn btn-primary btnSmall"
            disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured" || row.Status == "DateOutOfLimit"}
            onClick={() => this.handleShowUploaded(row)}
          >
            View Staged Data
          </Button>
        </ButtonGroup>
      ),
    });

    UploadedColumn.splice(4, 0, {
      name: "View Error Console",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            className="btn btn-danger btnSmall"
            disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "DataStaged" || row.Status == "DateOutOfLimit"}
            onClick={() => this.handleShowErrorConsole(row)}
          >
            Error Console
          </Button>
        </ButtonGroup>
      ),
    });

    return UploadedColumn;
  }

  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onFileProcessing(
      row.UniqueId,
      row.ProcessType,
      row.SMEProcessType,
      row.ProductId
    );
  }
  handleShowUploaded(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onShowUploadedData(row.UniqueId);
  }
  handleShowErrorConsole(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onShowErrorConsole(row.UniqueId);
  }

  handleClose() {
    this.setState({ showModal: false });
  }
  handleErrorConsoleClose() {
    this.setState({ showErrorConsole: false });
  }
  getExtension(filename) {
    var parts = filename.split(".");
    return parts[parts.length - 1];
  }

  isExcel(filename) {
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case "xls":
      case "xlsx":
        return true;
    }
    return false;
  }

  commonuploadFileToUrlService = (requestData, url) => {
    const input = {
      url,
      method: "POST",
      service: "commonUploadFileToUrl",
      requestData,
    };
    let a = this.CALL_API(input);
    return a;
  };
  BindGetApiData = (url) => {
    const input = {
      url,
      method: "GET",
      service: "MRSApi",
    };
    let a = this.CALL_API(input);
    return a;
  };
  ProcessuploadedFile = (requestData, url) => {
    const input = {
      url,
      method: "POST",
      service: "ProcessingRenewalFile",
      requestData,
    };
    let a = this.CALL_API(input);
    return a;
  };

  CALL_API = async (input) => {
    let Token = "cG9saWN5 YmF6YWFy";
    input.timeout = input.timeout || 6000;
    input.method = input.method || "GET";
    input.cache = input.cache || false;
    input.service = input.service || "core";
    let URL, Headers;

    switch (input.service) {
      case "commonUploadFileToUrl":
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          ...input.headers,
          "content-Type": "multipart/form-data",
          AgentId: getuser().UserID,
          source: "dashboard",
          "Access-Control-Allow-Origin": "*",
        };

        if (input.requestData) {
          let formData = new FormData();
          Object.keys(input.requestData).forEach((key) => {
            formData.append(key, input.requestData[key]);
          });
          input.formData = formData;
          input.requestData = null;
        }
        break;
      case "ProcessingRenewalFile":
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          ...input.headers,
          "content-Type": "multipart/form-data",
          AgentId: (getuser().UserID).trim(),
          source: "dashboard", //"dialer",
          "Access-Control-Allow-Origin": "*",
        };

        if (input.requestData) {
          let formData = new FormData();
          Object.keys(input.requestData).forEach((key) => {
            formData.append(key, input.requestData[key]);
          });
          input.formData = formData;
          input.requestData = null;
        }
        break;
      case "MRSApi":
        URL = config.api.MRSURL + input.url;
        Headers = {
          ...input.headers,
          Authorization: "cG9saWN5 YmF6YWFy",
        };
        break;

      default:
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          "Content-Type": "application/json",
        };
        break;
    }
    let timeout = 6000;
    if (!timeout) {
      try {
        timeout = !isNaN(parseInt(input.timeout))
          ? parseInt(input.timeout)
          : 3000;
      } catch {
        timeout = 3000;
      }
    }

    var reqData = {
      method: input.method,
      url: URL,
      headers: Headers,
      cache: input.cache,
      timeout,
    };

    if (input.requestData !== undefined) {
      reqData.data = JSON.stringify(input.requestData);
    }
    if (input.formData) {
      reqData.data = input.formData;
    }
    return new Promise((resolve, reject) => {
      AxiosInstanceUpload(reqData)
        .then((res) => {
          if (input.acceptOnly200 === true && res.status !== 200) {
            reject(res.statusText);
            return;
          }
          //console.log(URL, res.data);
          resolve(res.data);
        })
        .catch((error) => {
          if (error.response) {
            reject(error.response);
          } else if (error.request) {
            reject(error.request);
          } else {
            reject(error.message);
          }
          //console.log(error.config);
        });
    });
  };

  fileData = () => {
    if (this.state.selectedFile) {
      return (
        <div>
          <h2>File Details:</h2>
          <p>File Name: {this.state.selectedFile.name}</p>
          <p>File Type: {this.state.selectedFile.type}</p>
          <p>
            Last Modified:{" "}
            {this.state.selectedFile.lastModifiedDate.toDateString()}
          </p>
        </div>
      );
    } else {
      return (
        <div>
          <br />
          <h4>Choose before Pressing the Upload button</h4>
        </div>
      );
    }
  };

  renderDownloadFile() {
    return (
      <Link
        to={this.state.DownloadedFile}
        onClick={this.handleFileDownload}
        download
        target="_blank">
        Download Sample Excel File
      </Link>
    );
  }

  render() {
    const columns = this.fnDatatableCol1();
    const UploadedColumn = this.fnDatatableColsUploaded();
    const ErrorlogsColumn = this.fnDatatableCol2();

    const {
      Uploadeditems,
      items,
      ErrorLogitems,
      showModal,
      showErrorConsole,
      showGrid,
    } = this.state;

    return (
      <div className="content">
        <div class="container-fluid">
          <Row>
            <Col md={3}>
              <CardTitle tag="h4" className="mb-4">Upload Data</CardTitle>
            </Col>
          </Row>
          <Row>

            <Col md={4}>
              <div className="form-group">
                <label htmlFor="criteria" className="form-label">
                  Process
                </label>
                <select
                  className="form-select"
                  value={this.state.selectedProcess}
                  onChange={this.handleProcessChange}
                >
                  <option value="0">Select</option>
                  <option value="3">BulkLeadUpload</option>
                  <option value="4">PaymentLinkUploads</option>
                  <option value="5">UpsellLeadCreate</option>
                  <option value="6">BulkBooking</option>
                  <option value="7">Winback</option>
                  <option value="8">RenewalMissingLeads</option>
                  <option value="9">CrossSmeUpload</option>
                  <option value="10">SmeFosBulkUpload</option>
                </select>
              </div>
            </Col>
            <Col md={5}>
              <label className="form-label"> * Raw Data File :</label>
              <br />
              <input type="file" onChange={this.onFileChange} />
              <button
                type="submit"
                id="btnsave"
                onClick={this.onFileUpload}
                className="btn btn-primary float-end"
              >
                Save
              </button>
            </Col>
            {<div></div>}

            <div className="col-md-4 mt-3 mb-2">{this.renderDownloadFile()}</div>
          </Row>
          <>
            <div show={showGrid} className="content">
              <Form className="uploadRelewalLeadTable">
                <DataTable
                  columns={UploadedColumn}
                  data={
                    Array.isArray(Uploadeditems) && Uploadeditems.length > 0
                      ? Uploadeditems[0]
                      : []
                  }
                />
              </Form>
            </div>
          </>
          <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={showModal}
                onHide={this.handleClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Uploaded Leads</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                    <DataTable
                      columns={columns}
                      data={
                        Array.isArray(items) && items.length > 0 ? items[0] : []
                      }
                    />
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={this.handleClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
          <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={showErrorConsole}
                onHide={this.handleErrorConsoleClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Error Console</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                    <DataTable
                      columns={ErrorlogsColumn}
                      data={
                        Array.isArray(ErrorLogitems) && ErrorLogitems.length > 0 ? ErrorLogitems : []
                      }
                    />
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={this.handleErrorConsoleClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
        </div>
      </div>
    );
  }
}
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}

export default connect(mapStateToProps, {
  GetCommonData,
  GetCommonspData,
  GetCommonspDataV2
})(DashboardSMEUploads);
