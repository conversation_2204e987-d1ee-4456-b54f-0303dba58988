import React, { Component } from 'react';
import RadioButton from '../Common/RadioOptions';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";

class UserDetails extends Component{
    constructor(props) {
        super(props);
    }
     saveAndContinue = (e) => {
        e.preventDefault()
        const { values } = this.props;

         if (this.props.onValidation("part1", values)) {
             if(values.noasset){
                this.props.SaveAssetDetails(values);   
                this.props.nextStep(values);
             }else{              
                 this.props.nextStep(values)
             }
         }
    }

    render(){
        this.QuestionOneAnswer = [["Laptop", "Laptop"], ["Tablet", "Tablet"]]   
        const { values, errors } = this.props;
        return(
            
            <Form>
                <Col md={12} className="pull-left mt-4 mb-4">
                    <Form.Label>Email address <span>*</span></Form.Label>
                    <Form.Control type="text" placeholder="Your email" name="email" value={(values.noasset) ? "" : values.email} className="surveytextfield" id="email"
                    disabled={(values.noasset) ? "disabled" : ""}
                    onChange={this.props.handleChange( "email")} />
                    <span style={{color: "red"}}>{errors.email}</span>
                    </Col>
                    <Col md={12} className="pull-left mt-4">
                    <Form.Label>Employee Name <span>*</span></Form.Label>
                    <Form.Control type="text" placeholder="Your answer" name="empname" value={(values.noasset) ? "" : values.empname} className="surveytextfield" id="empname" 
                    disabled={(values.noasset) ? "disabled" : ""}
                    onChange={this.props.handleChange("empname")} />
                    <span style={{color: "red"}}>{errors.empname}</span>
                    </Col>
                    <Col md={12} className="pull-left mt-4">
                    <Form.Text className="questions" id="question1">
                        <p class="survey-text mb-3">Which device is issued?  <span>*</span></p>
                    </Form.Text>
                    <RadioButton 
                    name= "question1"
                    changed={this.props.handleChange("question1")}
                    items = {this.QuestionOneAnswer}
                    isSelected={ (values.noasset) ? "" :values.question1} 
                    disabled={(values.noasset) ? "disabled" : ""}
                    />
                    <span style={{color: "red"}}>{errors.question1}</span>
                </Col>
 
                <Col md={12} className="pull-left mt-4 mb-4">                                   
                <div className="OrITasset"><h2> OR</h2></div>
                </Col>
                <Col md={12} className="pull-left mt-4">
                <input type="checkbox"
                    onChange={this.props.handleChange("noasset")}
                    checked={values.noasset}
                    name="noasset" id="noasset" />
                    <Form.Label><b>&nbsp; I Don't have any company asset <span>*</span></b></Form.Label>                   
                    <span style={{color: "red"}}>{errors.noasset}</span>
 
                </Col>
                <Button onClick={this.saveAndContinue.bind(this)}>Save And Continue </Button>
            </Form>
          
        )
    }
}

export default UserDetails;