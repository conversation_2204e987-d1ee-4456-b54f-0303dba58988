import React from "react";
import {
  GetCommonData, GetCommonspData, GetRealTimeAgentData, GetRealTimeTotalData
} from "../store/actions/CommonAction";
import { getUrlParameter, hhmmss, getuser } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import { Web } from "sip.js";
import { ToastContainer, toast } from 'react-toastify';
import {
  addRecord, UpdateData, gaEventTracker
} from "../store/actions/CommonMongoAction";
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { Form } from 'react-bootstrap';
import moment from "moment";
import { resetWarned } from "react-bootstrap-typeahead/types/utils/warn";


class RealTimePanel extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "RealTime Panel",
      AgentData: [],
      TotalData: [],
      items: [],
      key: "ALL",
      SelectedSupervisors: [],
      winactive: 0,
      lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      online: true,
      Leads: null,
      body: [],
      AgentCode: "",
      AgentName: "",
      currLeadId: ""

    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    this._handleKeyDown = this._handleKeyDown.bind(this);
    this.saveBargingLogs = this.saveBargingLogs.bind(this);
    //this.bargecall = this.bargecall.bind(this);
    this.userAgent = null;
    this.winactive = 0;
    this.schdular = null;
    this.columnlist = [
      {
        name: "Barging",
        selector: "Barging",
        sortable: true,
        width: "80px",
        cell: row =>
          <div className={["BUSY", "RINGING"].indexOf(row.Status) > -1 ? "" : "hide"} >

            <button onClick={(e) => this.bargecall(e, row)} className={row.Barge ? "hide" : "show"}><i className="fa fa-volume-up" aria-hidden="true"></i></button>


          </div>
      },
      {
        name: "Agent Code",
        selector: "AgentCode",
        sortable: true,
      },
      {
        name: "Agent Name",
        selector: "AgentName",
        sortable: true,
      },
      {
        name: "Status",
        selector: "Status",
        sortable: true,

        cell: row => <div className={this.displayStatus(row) + " RealtimeStatus"}>{this.displayStatus(row)}</div>
      },
      {
        name: "Call Type",
        selector: "CallType",
      },
      {
        name: "Lead Id",
        selector: "LeadId",
        sortable: true,
      },
      // {
      //   name: "Asterisk_Url",
      //   selector: "Asterisk_Url",
      //   width: "120px",
      //   sortable: true,
      // },

      // {
      //   name: "Last Updated On",
      //   selector: "LastUpdatedOn",
      //   width: "130px",
      //   sortable: true,
      //   cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment format="HH:mm:ss A">{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      // },
      {
        name: "Since",
        selector: "LastUpdatedOn",
        sortable: true,
        width: "130px",
        cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment utc={true} from={row.ServerTime}>{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      },
      {
        name: "TL Name",
        selector: "TLName",
        sortable: true,
      },
      {
        name: "Dials",
        selector: "TotalCalls",
        width: "70px",
        sortable: true,
      },
      {
        name: "Unq Dials",
        selector: "UniqueDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "Connected",
        selector: "ConnectedDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "VC Dials",
        selector: "VCCount",
        sortable: true,
      },
      {
        name: "Unq VC",
        selector: "UniqueVCCount",
        sortable: true,
        cell: row => row.UniqueVCCount && row.UniqueVCCount > 0 ? row.UniqueVCCount : 0
      },
      {
        name: "VC Connected",
        selector: "VCConnectCount",
        sortable: true,
      },
      {
        name: "TT",
        selector: "TotalTalkTime",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.TotalTalkTime),
      },
      {
        name: "Calling Via.",
        selector: "CallingCompany",
        sortable: true,
      },
      {
        name: "IsMobile",
        selector: "IsWFH",
        sortable: false,
        cell: row => row.IsWFH ? "Yes" : "No"
      },
      {
        name: "DIDNo",
        selector: "DIDNo",
        sortable: true,
      },

    ];

  }

  componentDidMount() {

    const user = getuser();
    this.setState({ SelectedSupervisors: [getUrlParameter("m") == "" ? user.EmployeeId : getUrlParameter("m")] }, function () {
      this.UserList();
    }.bind(this));
    
    gaEventTracker('NewRealTimePanel', 'OldPanel INIT', getuser().EmployeeId, 'matrixdialerdashboard' );

    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //if (!this.state.onBarge) {
        //if ((new Date()).getHours() >= 21 || (new Date()).getHours() < 9) {
        if (this.state.winactive == 1 || document.hasFocus()) {
          this.UserList();
          //this.totalList();
        }
        // }
        // else {
        //   this.UserList();
        //   this.totalList();
        // }
        //}
        // if (this.userAgent == null) {
        //   sessionStorage.setItem("BargeWith", "");
        // }
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }
  }

  saveBargingLogs(row) {
    try{
    // inserting first record on barge
    let bargeTimestamp = new Date();
    let bargeLog = {
      Agent: row.AgentCode,
      BargedBy: getuser().EmployeeId,
      AgentName: row.AgentName,
      LeadID: row.LeadId,
      Type: "Barge",
      Timestamp: bargeTimestamp,
    }
    
    // setting leadid
    console.log("GA-Start: ",bargeLog)
    sessionStorage.setItem("BargeWithLeadId", row.LeadId);
    
    gaEventTracker("BargeCall", JSON.stringify(bargeLog), getuser().EmployeeId);
    console.log("GA-END")

    this.setState({body: bargeLog})

  }
  catch(e){
    console.log(e);
  }
   
  };

  totalList() {
    let context = getUrlParameter("c");
    if (context != "") {
      GetRealTimeTotalData(context, function (results) {
        this.setState({ TotalData: results.data });
      }.bind(this));
    }
  }


  UserList() {
    let managerid = getUrlParameter("m")
    let context = getUrlParameter("c")
    const user = getuser();
    if (this.state.SelectedSupervisors.length > 0) {
      managerid = this.state.SelectedSupervisors.join()
    }

    if (managerid == "" && context == "") {
      managerid = user.EmployeeId;
      this.setState({ SelectedSupervisors: [managerid] });
    }

    GetRealTimeAgentData(managerid, context, function (results) {
      if (!results.hasOwnProperty('data') && String(results).includes('Error')) {
        this.setState({ online: false })
      } else {
        this.setState({ AgentData: results.data, lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"), online: true });
      }
    }.bind(this));

  }

  handleShow(e) {
    this.setState({ SelectedSupervisors: e.SelectedSupervisors });
  }

  componentWillUnmount() {
    clearInterval(this.schdular);
    if (this.userAgent != null) {
      this.userAgent.hangup();
      sessionStorage.setItem("BargeWith", "");
    }
  }

  displayStatus(row) {
    // var diff = (new Date() - new Date(row.LastUpdatedOn)) / 1000;
    // if (diff > 60 && row.Status == "IDLE") {
    //   return "Away";
    // } else return row.Status;
    let BargeWith = sessionStorage.getItem("BargeWith") ? sessionStorage.getItem("BargeWith") : "";;
    if (BargeWith == row.AgentCode && ["BUSY", "RINGING"].indexOf(row.Status) == -1) {
      this.unbargecall();
    }
    else if (BargeWith == row.AgentCode && row.Status == "BUSY") {
    }
    return row.Status.toUpperCase()
  }

unbargecall(e) {
    if (this.userAgent) {
      try {
        if(this.state.body != []){
        let UnbargeTimestamp = new Date();
        let unbargeLog = {
          Agent: this.state.body.Agent,
          BargedBy: getuser().EmployeeId,
          AgentName: this.state.body.AgentName,
          LeadID: sessionStorage.getItem("BargeWithLeadId"),
          Type: "Unbarge",
          Timestamp: UnbargeTimestamp,
        }
        gaEventTracker("BargeCall", JSON.stringify(unbargeLog), getuser().EmployeeId);
      }
      
      }
      catch (e) {
        console.log(e)
      }
      this.userAgent.hangup();
      this.userAgent = null;


    }
    sessionStorage.setItem("BargeWith", "");
  }


  bargecall(e, row) {
    
    try {
      
      console.log("clicked with : " + row.AgentCode);

      if (this.userAgent) {
        this.userAgent.hangup();
        this.userAgent = null;
        sessionStorage.setItem("BargeWith", "");
      }
      sessionStorage.setItem("BargeWith", row.AgentCode);
      

      //if (!this.state.onBarge) {
      let user = {
        Display: getuser().EmployeeId,
        User: getuser().EmployeeId,
        Pass: getuser().EmployeeId,
        Realm: row.Asterisk_Url,
        WSServer: "wss://" + row.Asterisk_Url + ":8089/ws"
      }
      this.state.AgentName = row.AgentName
      this.state.AgentCode = row.AgentCode
      this.LoginAsteriskServer(user, function () {
        //setTimeout(function () {
        console.log("Barge with : " + row.AgentCode);
        console.log("sessionStorage with : " + sessionStorage.getItem("BargeWith"));

        if (sessionStorage.getItem("BargeWith") == row.AgentCode) {
          if (this.userAgent) {
            let target = "*222" + row.AgentCode;
            if (row.IsWFH || (row.CallingCompany == "WFH" || row.CallingCompany == "WFH_NEW")) {
              target = "*222" + row.DIDNo;
            }

            // if (row.CallingCompany == "WFH" || row.CallingCompany == "KNOWLARITY") {
            //   target = "*222" + row.DIDNo;
            // }

            this.userAgent.call(target);
          }
        }
        

        this.saveBargingLogs(row)

        this.setState({ onBarge: true, BargeWith: row.AgentCode, BargeWithAgent: row });



        // setTimeout(function () {
        //   //this.forceUpdate();

        // }.bind(this), 500);

        //}.bind(this), 1000);
      }.bind(this), function () {
        if (document.getElementById(row.AgentCode)) {
          document.getElementById(row.AgentCode).checked = false;
        }
        sessionStorage.setItem("BargeWith", "");
      }.bind(this));

      
    


      // }
      // else {
      //   toast("Close previous call barging", { type: 'error' });
      //   e.target.checked = false;
      //   return false;
      // }

      // this.bodyLeads = setInterval(function () {
      //   this.insertBargeLogs(row)
      // }.bind(this), 5000)

    } catch (e) {

    }

  }


  LoginAsteriskServer(user, onsuccess, onerror) {

    if (user) {
      var config = {
        media: {
          remote: {
            //video: document.getElementById('remoteVideo'),
            // This is necessary to do an audio/video call as opposed to just a video call
            audio: document.getElementById('audioRemote')
          }
        },
        ua: {
          uri: user.User + '@' + user.Realm,
          wsServers: [user.WSServer],
          authorizationUser: user.Display,
          password: user.Pass
        }
      }

      if (Web) {
        this.userAgent = new Web.Simple(config);
        // this.userAgent = new Web.SimpleUser(config);

        //let remoteElem = document.getElementById('audioRemote');
        //let localElem = document.getElementById('audioLocal');
        this.userAgent.on('connected', function (e) {
          toast("Barging Connected!", { type: 'success' });
        });
        this.userAgent.on('disconnected', function (e) {

        });
        this.userAgent.on('registered', function (e) {
          if (onsuccess) {
            onsuccess();
          }

        });
        this.userAgent.on('registrationFailed', function (e) {
          gaEventTracker("BargingVpnError", JSON.stringify(config.ua), getuser().EmployeeId);
          toast("Make sure your VPN is connected!", { type: 'error' });
        });
        this.userAgent.on('unregistered', function (e) {
          gaEventTracker("BargingDialerError", JSON.stringify(config.ua), getuser().EmployeeId);
          toast("Dialer issue please contact administrator!", { type: 'error' });
          if (onerror) {
            onerror();
          }

        });
        this.userAgent.on('userMediaFailed', function (e) {

        });
        this.userAgent.on('userMediaRequest', function (e) {

        });
        this.userAgent.on('userMedia', function (e) {

        });
        this.userAgent.on('invite', function (e) {

        });
        this.userAgent.on('addStream', function (stream) {

        });
        this.userAgent.on('ended', function (stream) {

        });
      }
    }

    setTimeout(function () {
      if (this.userAgent && this.userAgent.ua && this.userAgent.ua.isRegistered() == false) {
        toast("Make sure your VPN is connected!!", { type: 'error' });
        gaEventTracker("BargingConnectFailed", getuser().EmployeeId, getuser().EmployeeId);
      }
    }.bind(this), 10000);
    return this.userAgent;
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {

    }
  }

  statuschange(e) {
    this.setState({ key: e.target.value });

  }

  filterdata(e) {

    let alldata = this.state.AgentData
    let that = this;
    if (this.state.key === "ALL") {
      return alldata;
    }
    if (this.state.key === "AWAY") {
      let AgentData = [];
      alldata.forEach(element => {
        var diff = (new Date() - new Date(element.LastUpdatedOn)) / 1000;
        if (diff > 60 && element.Status === "IDLE") {
          AgentData.push(element);
        }
      });
      return AgentData;
    }
 
    /// If status is neither "ALL" nor "AWAY" then split status key into a array
    /// and compare with status of every element
    let conditionArray = this.state.key.split(',');
    let agentData = [];
    alldata.forEach(element => {
      if(conditionArray.includes(element.Status.toUpperCase())){
        agentData.push(element);
      }
    })
    return agentData;
  }
  _handleKeyDown(e) {

    if (e.key === 'Enter') {
      this.setState({ SelectedSupervisors: [e.target.value] });
    }
  }

  _handleOnClick(e) {


    this.setState({ SelectedSupervisors: [document.getElementById("EmpId").value] });

  }

  renderTotalData() {
    const context = getUrlParameter("c");
    let TotalData = this.state.TotalData;
    if (context != "") {
      return <div>

        <div className="totaldata">
          <Row></Row>
          <Row>
            <Col md={2}><span className="totaltext">Context : {TotalData.context}</span></Col>
            <Col md={2}><span className="totaltext">Answered : {TotalData.answered}</span></Col>
            <Col md={2}><span className="totaltext">Unanswered : {TotalData.unanswered}</span></Col>
            <Col md={2}><span className="totaltext"># Agents : {TotalData.totalAgents}</span></Col>
            <Col md={2}><span className="totaltext">Waiting Calls : {TotalData.waitingCalls}</span></Col>
            <Col md={2}><span className="totaltext"># Avail. Agents : {TotalData.totalAvailableAgents}</span></Col>
          </Row>
        </div>
        <br />
      </div>
    }
    else {
      return null;
    }
  }


  render() {
    const columns = this.columnlist;
    const totalcolumns = this.totalcolumnlist;
    const data = this.filterdata();
    const managerid = getUrlParameter("m");
    const context = getUrlParameter("c");

    const { items, PageTitle, online } = this.state;
    const BargeWith = sessionStorage.getItem("BargeWith") ? sessionStorage.getItem("BargeWith") : "";

    return (
      <>
        <div className="panelMigText">
          <span>Important Note:</span> A new Realtime Panel has been introduced, offering enhanced insights into bucket utilization & other metrics.<br/><b>This Panel will no longer be available after March 31st</b>. Click here to access the <a href={document.referrer+"PGV/Administration/dialerdashboard.aspx?t=RealTimePanel"} target="_top" onClick={() => gaEventTracker('NewRealTimePanel', 'Click to redirect to New Panel', getuser().EmployeeId, 'matrixdialerdashboard' ) }>New RealTime Panel</a>
        </div>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <CardTitle tag="h6">{this.state.SelectedSupervisors.join()}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <div className="input-group">

                        <Form.Control required type="text" name="EmpId" id="EmpId" onKeyDown={this._handleKeyDown} onChange={(e) => this.setState({ username: e.target.value })} value={this.state.username} placeholder={"Enter Supervisor Id"} />
                        <div className="input-group-append">
                          <button onClick={(e) => this._handleOnClick(e)} className="btn btn-primary input-group-button"><i className="fa fa-search" aria-hidden="true"></i></button>

                        </div>
                      </div>
                    </Col>
                    <Col md={2}>
                      <div className="form-group">
                        <select className="form-control" onChange={this.statuschange}>
                          <option value="ALL">ALL</option>
                          <option value="IDLE">IDLE</option>
                          <option value="AWAY">AWAY</option>
                          <option value="BUSY">BUSY</option>
                          <option value="UNAVAILABLE">UNAVAILABLE</option>
                          <option value="LUNCH,TEA,TRAINING,MEETING,DAY END,BIO BREAK">BREAK</option>
                          <option value="PAUSE">PAUSE</option>
                          <option value="AUTO LOGOUT,LOGOUT">LOGOUT</option>
                        </select>
                      </div>
                      {
                        (managerid == '' && context == '') ? <ManagerHierarchy handleShow={this.handleShow} value={/EmployeeId/g} ></ManagerHierarchy> : null
                      }
                      <button id="BargeWith" onClick={(e) => this.unbargecall(e)} className={BargeWith == "" ? "hide" : "btn btn-primary hangupwith show"} ><i className="fa fa-volume-off" aria-hidden="true"></i> Hang Up With: {BargeWith}</button>
                    </Col>

                  </Row>
                  <Row>
                    <Col md={5}>
                      <div>
                        Last Refresh Time : {this.state.lastRefreshTime}
                      </div>
                    </Col>
                    <Col md={4}>
                      <div style={(online) ? { display: "none" } : { display: "block", color: "red" }}>Internet is not working</div>
                    </Col>
                  </Row>
                </CardHeader>

                <CardBody>
                  {/* {this.renderTotalData()} */}
                  <div className="statusdata">
                    <DataTable
                      columns={columns}
                      data={data}
                      pagination={false}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense

                    />

                  </div>
                </CardBody>

              </Card>
            </Col>
            <audio id="audioRemote"></audio>
            <audio id="audioLocal"></audio>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    addRecord,
    UpdateData,
    gaEventTracker
  }
)(RealTimePanel);



