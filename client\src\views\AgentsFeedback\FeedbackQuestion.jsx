import React, { Component } from 'react';
import RadioButton from '../Common/RadioOptions';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";

class FeedbackQuestion extends Component {
    constructor(props) {
        super(props);
    }
    saveAndContinue = (e) => {
        e.preventDefault()
        const { values } = this.props;
        if (this.props.onValidation("part1", values)) {
            this.props.SaveFeedbackDetails(values);
            this.props.nextStep()
        }
    }

    render() {
        const { values, errors } = this.props;

        return (

            <Form>
                <Col md={12} className="pull-left mt-4 mb-4">
                <iframe
                src='/PB-Incentive-Policy-V1.1.pdf'
                width="500"
                height="678"
                />
                </Col>

                <Col md={12} className="pull-left mt-4">
                <input type="checkbox"
                    onChange={this.props.handleChange("termsconditions")}
                    checked={values.termsconditions}
                    name="termsconditions" id="termsconditions" />
                    <Form.Label><b>&nbsp; I agree to terms and conditions <span>*</span></b></Form.Label>                   
                    <span style={{color: "red"}}>{errors.termsconditions}</span>
                </Col>

                <Col md={12} className="pull-left mt-4">
                    <Button onClick={this.saveAndContinue.bind(this)}>Submit Details </Button>
                </Col>
            </Form>

        )
    }
}

export default FeedbackQuestion;