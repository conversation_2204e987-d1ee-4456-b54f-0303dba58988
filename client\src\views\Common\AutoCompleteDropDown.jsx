import React from 'react';
import Autosuggest from 'react-autosuggest';
import { defaultTheme } from 'react-autosuggest/dist/theme';

class AutoCompleteDropDown extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: '',
      name: this.props.name,
      suggestions: []
    };
  }

  onChange = (event, { newValue }) => {
    this.setState({
      value: newValue
    });
  };

  // Autosuggest will call this function every time you need to update suggestions.
  // You already implemented this logic above, so just use it.
  onSuggestionsFetchRequested = ({ value }) => {
    this.setState({
      suggestions: this.getSuggestions(value)
    });
  };

  // Autosuggest will call this function every time you need to clear suggestions.
  onSuggestionsClearRequested = () => {
    this.setState({
      suggestions: []
    });
  };

    getSuggestions = value => {
        const inputValue = value.trim().toLowerCase();
        const inputLength = inputValue.length;
        const data = this.props.data;
        return inputLength === 0 ? [] : data.filter(item =>
            item.name && item.name.toLowerCase().slice(0, inputLength) === inputValue
        );
    };

    // When suggestion is clicked, Autosuggest needs to populate the input
    // based on the clicked suggestion. Teach Autosuggest how to calculate the
    // input value for every given suggestion.
    getSuggestionValue = (suggestion, event) => { 
      this.props.onChange({target: { "name": this.state.name, "value": suggestion.value }}, this.state.name);
      return suggestion.name;
    }

    // Use your imagination to render suggestions.
    renderSuggestion = suggestion => (
            <div>
                {suggestion.name}
            </div>
        );

  render() {
    const { value, suggestions } = this.state;

    // Autosuggest will pass through all these props to the input.
    const inputProps = {
      name: this.state.name,
      placeholder: 'Enter ' + this.state.name,
      class: 'form-control',
      value,
      autoComplete:"none",
      onChange: this.onChange
    };

    // Finally, render it!
    return (
      <Autosuggest
        suggestions={suggestions}
        onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
        onSuggestionsClearRequested={this.onSuggestionsClearRequested}
        getSuggestionValue={this.getSuggestionValue}
        renderSuggestion={this.renderSuggestion}
        inputProps={inputProps}
        theme={defaultTheme}
      />
    );
  }
}

export default AutoCompleteDropDown;