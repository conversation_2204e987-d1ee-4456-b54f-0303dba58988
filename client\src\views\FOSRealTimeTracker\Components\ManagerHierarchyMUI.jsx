import React, { useEffect, useState } from 'react';
import { getuser, getUserDetails } from '../../../utility/utility';
import { GetDataDirect } from 'store/actions/CommonAction';
import { <PERSON><PERSON>,  Drawer, Grid, IconButton  } from '@mui/material';
import CheckboxTree from 'react-checkbox-tree';
import axios from 'axios';
import config from '../../../config';
import { MY_BOOKINGS, FOS_RTT } from '../../../variables/constants';
import "react-checkbox-tree/lib/react-checkbox-tree.css";
import CloseIcon from "@mui/icons-material/Close";
import CachedIcon from "@mui/icons-material/Cached";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ExpandMoreTwoToneIcon from "@mui/icons-material/ExpandMoreTwoTone";
import ChevronRightTwoToneIcon from "@mui/icons-material/ChevronRightTwoTone";
import AddTwoToneIcon from "@mui/icons-material/AddTwoTone";
import RemoveTwoToneIcon from "@mui/icons-material/RemoveTwoTone";
import FolderTwoToneIcon from "@mui/icons-material/FolderTwoTone";
import FolderOpenTwoToneIcon from "@mui/icons-material/FolderOpenTwoTone";
import DescriptionTwoToneIcon from "@mui/icons-material/DescriptionTwoTone";
import "../../../assets/scss/ManagerHierarchyMUI.scss";
import CropSquareIcon from '@mui/icons-material/CropSquare';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import FullscreenIcon from '@mui/icons-material/Fullscreen';

const GetCheckedValues = ({checkedOptions, module, state}) => {
    debugger
      let options = checkedOptions;
      let errorStatus = 0;
      switch (module) {
        case MY_BOOKINGS :
        case FOS_RTT:
            if(checkedOptions.length > 2) {
                errorStatus = 1;
                break;
            } 
    
          if(checkedOptions.length === 2){
            const opt = checkedOptions?.filter(opt => opt !== state[0]);
            options = opt;
            
          }
            break;
      
        default:
          break;
      }
      return { errorStatus, data: options }
    }


const ManagerHierarchyMUI=(props) => {
	const { handleShow, selectAll, value, noCascade, module, message, FetchOtsData } = props;
    const [nodes, setNodes] = useState([]);
    const [checked, setChecked] = useState([]);
    const [EmployeeIds, setEmployeeIds] = useState([]);
    const [showManagerHierarchy, setShowManagerHierarchy] = useState(false);
    const [IsLoading, setIsLoading] = useState(false);
    const [expanded, setExpanded] = useState([]);
    

    let selectedNodes = [];
    let TLnodes = [];

  

    useEffect(() => {
        let UserId =  parseInt(getUserDetails('UserId'));
    if(!UserId)
    {
    UserId = getuser().UserID;
    }

    if(UserId)
    {
    let Value= value;

    GetDataDirect({
        root: "Hierarchy2",
        ManagerId: UserId,
        statename: "Hierarchy-" + UserId,
        value: Value,
        state: true
    }, function (result) {
      debugger
       let expired=false;
        if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
        {
        expired=true;
        }
        // result.pop();
        let str = JSON.stringify(result);
        var res = str.replace(/UserName/g, "label");
        res = res.replace(Value, "value");
        let nodes= JSON.parse(res);

        let TLs =[];
        
        let childNodes= new Map();

        let levelWiseNodes = {};

        for(let i=0;i<nodes.length;i++)
        {
          // console.log("What is the object ", nodes[i])
          TLnodes.push(nodes[i].value);
          
          if(nodes[i].hasOwnProperty("children"))
          {
               searchChildNodes(nodes[i].children, TLs, childNodes, nodes[i].value, levelWiseNodes,1);
               
          }
          else{
               TLs.push(parseInt(nodes[i].value));
          }
        }

        let highestLevel = 0;

        Object.keys(levelWiseNodes).forEach((key)=>{
          if(highestLevel<key)
          {
            highestLevel=key;
          }
        })
        
     
        FetchOtsData(TLs, childNodes, levelWiseNodes, highestLevel);
        // setTLs(TLs);
        setNodes(nodes);
        handleShow({
          SelectedSupervisors: TLnodes,
          nodesData: selectedNodes,
        });
        
        if(selectAll===true){
         
          setChecked(TLnodes);
        }
        else{
          setChecked([getuser().UserID]);
        }
     
        if(expired)
        {
          axios
          .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
          .then(function(response){
            if(response.data.status===200)
            {
               return;
            }
          })
        }
    });
  }

    }, []);

    const searchChildNodes = (node,TLs, childNodes, val, levelWiseNodes, level) => {
      
        for (let i = 0; i < node.length; i++) {
  
            childNodes.set(parseInt(node[i].value),parseInt(val));


            levelWiseNodes[level] = levelWiseNodes[level] || [];

            levelWiseNodes[level].push(parseInt(node[i].value));

            

            // let nodesAtThisLevel = levelWiseNodes.get(level) || [];

            // levelWiseNodes.set(level, nodesAtThisLevel.push(node[i].value));
          
            // console.log("The childNode is ",childNodes);
            TLnodes.push(node[i].value);
            if (node[i].hasOwnProperty('children')) {
                searchChildNodes(node[i].children,TLs, childNodes,node[i].value, levelWiseNodes, level+1);
            }
            else{
                TLs.push(parseInt(node[i].value));
            }
        }
        return;
    }

    const getNodesData = (nodesData, SelectedSupervisors, checkedUsers, pushedVal ) => {
      
        for (let i = 0; i < nodesData.length; i++) {
            let element = nodesData[i];
            let copyPushedVal = pushedVal;
            if (SelectedSupervisors.indexOf(element.value) > -1) {
              
                if(!copyPushedVal)
                {
                checkedUsers.push(parseInt(element.value));
                }
                copyPushedVal=true
                selectedNodes.push(element);
            }
            if (element.children) {
                getNodesData(element.children, SelectedSupervisors,checkedUsers, copyPushedVal);
                
            }
        }
    }

    const onRefreshHierarchy = () => {
        let UserId = parseInt(getUserDetails('UserId'));
        if (!UserId) {
            UserId = getuser().UserID;
        }

        if (UserId) {
            let Value = value;
            setIsLoading(true);
            GetDataDirect({
                root: "Hierarchy2",
                ManagerId: UserId,
                statename: "Hierarchy-" + UserId,
                value: Value,
                state: true,
                ClearLocalStorage: true
            }, function (result) {
                let expired=false;
                if(result[result.length-1].hasOwnProperty('expired') && result[result.length-1].expired==true)
                {
                expired=true;
                }
                // result.pop();
                let str = JSON.stringify(result);
                var res = str.replace(/UserName/g, "label");
                res = res.replace(Value, "value");
                let nodes= JSON.parse(res);
                setNodes(nodes);
                setIsLoading(false);
                if(expired)
                {
                  axios
                  .post(config.api.base_url + "/Common/UpdateHierarchy", {ManagerId:75})
                  .then(function(response){
                    if(response.data.status===200)
                    {
                      return;
                    }
                  })
                }
            });
        }
    }

    const handleShow1 = () => {
        let checkedUsers=[];
        getNodesData(nodes, checked,checkedUsers, false);
        
        let arr = [];
        for (let i = 0; i < selectedNodes.length; i++) {
            arr.push(selectedNodes[i].EmployeeId)
        }
        setEmployeeIds(arr);
        setShowManagerHierarchy(false);
        handleShow({
            SelectedSupervisors: checked,
            nodesData: selectedNodes,
            FetchedViaClick: true,
            CheckedUsers: checkedUsers
        });
    }

    const handleOpenHierarchy = () => {
        setShowManagerHierarchy(true);
    }

    const handleCloseHierarchy = () => {
        setShowManagerHierarchy(false);
    }

    const checkFunction = (values) => {
        if (selectAll === false) {
            let difference = values.filter(x => !checked.includes(x));
            setChecked(difference)
        } else {
            debugger
            const { data, errorStatus }= GetCheckedValues({checkedOptions:values, module, state: checked})
            if(errorStatus) {
              // toast(HIERARCHY_MESSAGE_NOTE, { type: 'error' });
              return;
            }
            setChecked(data)
        }
    }

	return (
		<>
    
			{
				nodes.length != 0 &&
        
				<div className="ShowHistoryBTn">
					<Button variant="contained" onClick={handleOpenHierarchy}>
						Filter
					</Button>
          {
            props.page=='FosRealTimeTracker' &&
            <Button className='fullscreen' onClick={()=>props.FullView()}>
              {props.fullview?
                // 'Minimize':'Expand'
                // <FullscreenIcon /> : <FullscreenExitIcon /> 
                <FullscreenExitIcon/> :<FullscreenIcon />
              }
            </Button>
          }

					<Drawer anchor="right" className="ManagerHierachyPopop" open={showManagerHierarchy} onClose={handleCloseHierarchy}>
						<div className="topHeader">
							<h3> Select Supervisors</h3>
							<Button variant="contained" onClick={handleShow1} className="ShowBtn">
								Show
							</Button>
							<IconButton onClick={onRefreshHierarchy}>
								<CachedIcon color="primary" />
							</IconButton>
							<IconButton onClick={handleCloseHierarchy}>
								<CloseIcon />
							</IconButton>
						</div>
						{IsLoading ?
							<h5 className="hierarchyLoading">Loading...</h5> :
							<div className="hierarchyScrolling">
								<Grid container spacing={2}>
									<Grid item xs={12} md={12} lg={12}>
										<div className="SupervisorListing">
											<CheckboxTree
												icons={{
													check       : <CheckBoxIcon className="checked" />,
													uncheck     : <CheckBoxOutlineBlankIcon />,
													halfCheck   : <CheckBoxIcon />,
													expandClose : <ChevronRightTwoToneIcon className="downArrow"/>,
													expandOpen  : <ExpandMoreTwoToneIcon className="downArrow"/>,
													expandAll   : <AddTwoToneIcon />,
													collapseAll : <RemoveTwoToneIcon />,
													parentClose : <FolderTwoToneIcon />,
													parentOpen  : <FolderOpenTwoToneIcon />,
													leaf        : <DescriptionTwoToneIcon />
												}}
												nodes={nodes}
												checked={checked}
												expanded={expanded}
												checkModel="all"
												name="UserName"
												showNodeIcon={false}
                                                onCheck={checkFunction}
												// onCheck={(_checked) => setChecked(_checked)}
												onExpand={(_expanded) => setExpanded(_expanded)}
												showExpandAll
											/>
										</div>

									</Grid>
									<Grid item>
										<span className="managerEmployees">{EmployeeIds && Array.isArray(EmployeeIds) && EmployeeIds.join(" , ")}</span>
									</Grid>
                  <Grid item>
                    {module && module=='FOS_RTT' ? <span className="note" style={{color:"#ff1744", fontSize:"12px"}}>** Can select upto 1 TL at a time</span> :null}
                  </Grid>
								</Grid>
							</div>
						}
					</Drawer>
				</div>
        
			}
			{
				nodes.length == 0 && null
			}
     
		</>
	);

};

export default ManagerHierarchyMUI;
