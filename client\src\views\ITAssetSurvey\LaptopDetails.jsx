import React, { Component } from 'react';

import RadioButton from '../Common/RadioOptions';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {InsertData} from "../../store/actions/CommonAction";
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import moment from 'moment';
import { connect } from "react-redux";

class AssetDetails extends Component{

    constructor(props) {
        super(props);
    }

    saveAndContinue = (e) => {
        e.preventDefault();
        const { values } = this.props;

        if (this.props.onValidation("part2", values)) {
            this.props.SaveAssetDetails(values);                 
            this.props.nextStep(values);
         }
    }


    back  = (e) => {
        e.preventDefault();
        this.props.prevStep();
    }

    render(){

        const { values, errors } = this.props
        return(
        <Form color='blue' >
            <Col md={12} className="pull-left mt-4">
            <Form.Label>Enter Serial Number <span>* </span> <span style={{color: "blue"}}><a onClick={this.props.showdetails.bind(this)} >
            What's this ?</a></span></Form.Label>
            <Form.Control type="text" placeholder="Your answer" value={values.serialno} name="serialno" className="surveytextfield" id="serialno" onChange={this.props.handleChange("serialno")} />
            <span style={{color: "red"}}>{errors.serialno}</span>
            </Col>                                                                            
            <Col md={12} className="pull-left mt-4">
            <Form.Label>Enter Model Number <span>*</span></Form.Label>
            <Form.Control type="text" placeholder="Your answer" value={values.question2} name="modelno" className="surveytextfield" id="modelno" onChange={this.props.handleChange("question2")} />
            <span style={{color: "red"}}>{errors.question2}</span>
            </Col>
            
            <Col md={4} className="pull-left mt-4">
            <Button onClick={this.back}>Back</Button>
            </Col>
            <Col md={6} className="pull-left mt-4">
            <Button onClick={this.saveAndContinue}>Submit Details </Button>
            </Col>
        </Form>
        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        InsertData,
    }
)(AssetDetails)