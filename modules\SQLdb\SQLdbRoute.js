var express = require("express");
const router = express.Router();
const controller = require("./SQLdbController");
const {ACLlayer} = require("../ACLlayer");

router.post("/insert/*", controller.insert);
router.post("/update/*", controller.update);
router.post("/UploadIncentiveFile", controller.UploadIncentiveFile);
router.post("/UploadMonthlyIncentiveFile", controller.UploadMonthlyIncentiveFile)
router.post("/UploadVideoFile",ACLlayer ,controller.UploadVideoFile);
router.post("/UploadStoryFile", controller.UploadStoryFile);
router.post("/UploadChatAgentFile", controller.UploadChatAgentFile);
router.post("/HniLogicMaster", controller.HniLogicMaster);
router.post("/UploadMatrixFiles", controller.UploadMatrixFile);
router.get("/list/*", controller.getdata);
router.get("/HealthCheck", controller.HealthCheck);
router.post("/delete", controller.delete);

router.get("/listsp/:root", controller.getdatasp);
router.get("/listspV2/:root*", controller.getdataspV2);

router.get("/agentidletime", controller.agentidletime);
router.get("/agentstats", controller.agentstats);
router.get("/awsfile", controller.awsfile);
router.get("/getawsfile", controller.getawsfile);
//router.post("/login", controller.login);
router.post("/rejectLeads", controller.rejectLeads);
router.post("/uploadUserGrades", controller.uploadUserGrades);
router.get("/ProcessUploadIncentivefile", controller.ProcessUploadIncentivefile);
router.post("/UploadAllocationFile", controller.UploadAllocationFile);
router.post("/AgentSurvey", controller.AgentSurvey);
router.post("/FileUpload", controller.FileUpload);
router.get("/openSalesView", controller.openSalesView);
router.post("/getUserlistbyManagerId", controller.getUserlistbyManagerId);
router.post("/getDuplicateLeadsData", controller.getDuplicateLeadsData);
router.post("/getIsAllowedDuplicateSearch", controller.getIsAllowedDuplicateSearch);
router.post("/UploadAdultChildScoreFile", controller.UploadAdultChildScoreFile);
router.post("/UploadCityScoreFile", controller.UploadCityScoreFile);
router.post("/getPaymentOverDueCountByManagerId", controller.getPaymentOverDueCountByManagerId);
router.post("/getDuplicateLeadsDatabyProductId", controller.getDuplicateLeadsDatabyProductId);
router.post("/UploadPayUScoreRankFile", controller.UploadPayUScoreRankFile);
router.post("/IsUserAttributesAuthorized", controller.IsUserAttributesAuthorized);
router.post("/GetAdditionalLDBulkUploadData", controller.GetAdditionalLDBulkUploadData);
router.post("/FetchHealthRenewalPaymentLink", controller.FetchHealthRenewalPaymentLink);
router.post("/SetHealthRenewalPaymentLink", controller.SetHealthRenewalPaymentLink);
router.post("/UpdateRenewalFileStatus", controller.UpdateRenewalFileStatus);
router.post("/GetStagedFileData", controller.GetStagedFileData);
router.post("/GetUniqueGroups", controller.GetUniqueGroups);
module.exports = router;
