*{font-family: '<PERSON><PERSON>',sans-serif !important;}
.fos-advisor-views{
    padding: 24px 16px 0px;
    font-family: '<PERSON><PERSON>',sans-serif;

    &.map-view{
        display: flex;
        flex-direction: column;
        width: 100%;
        box-shadow: 0px 3px 12px 0px #00000029;
        -webkit-box-shadow: 0px 3px 12px 0px #00000029;
        
        // visibility: hidden;
        // height: 100%;
        // max-height: 100vh;
    }

   h3{
    font-size: 24px;
    font-weight: 500;
    color: #253858e3;
    margin-bottom: 2px;
   }

   .switch-view{
    font-size: 13px;
    font-weight: 500;
    color: #253858e3;
    margin-bottom: 10px;
    cursor: pointer;

        span{
            font-size: 13px;
            color: #0065FF !important;
            font-weight: 500;
            text-decoration: underline !important;
            margin-left: 2px;


        }
   }    

   .header-list{
    gap: 16px
   }

    ul{
        list-style: none;
        padding: 0px 16px 0px 0px;
        margin: 0px 0px 20px;
        border-right: 1px solid #0000003d;

       li{
            border-radius: 20px;
            background: #FFF;
            box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.06);
            padding: 8px 8px 8px 16px;
            font-size: 14px;
            font-weight: 600;
            color: #253858e3;
            cursor: pointer;
            max-width: 140px;

            &:last-child{
                margin-left: 16px;
            }
       }

       &.main-listing{
        padding-right: 0;
        gap: 6px;
        border-right: 0;

        &::-webkit-scrollbar, &::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track{
            width: 0px;
            background: transparent;
            box-shadow: none;
            border-radius: 0;
            margin-right: 0;
            color: inherit;
        }
    

        li{
            padding: 6px 42px 6px 10px;
            border-radius: 24px;
            background: #F5F5F5;
            gap: 0px;
            display: flex;
            align-items: center;
            border: 1px solid transparent;
            box-shadow: none;
            -webkit-box-shadow: none;
            max-width: inherit;
            margin-left: 0;

            .MuiBadge-badge{
                min-width: inherit;
                width: 24px;
                background:#DDECFF;
                border-radius: 32px;
                padding: 5px 8px 5px 9px;
                height: 24px;
                font-size: 12px;
                color: #0065FF;
                font-weight: 600;
                right: auto;
            }

            &.active{
                background: #0065FF;
                border-color: #0065FF;
                cursor: pointer;
                pointer-events: all;
                
                .MuiBadge-badge{
                   background: #F7FBFF;
                }

                img{
                    filter: brightness(0) invert(1);
                }
            }

            img{
               padding-right: 8px;
            }
        }

       }

    }

    .advisor-table-list{

       .MuiTableContainer-root{
            // max-height: 480px;
            overflow-x: hidden;
            border: 0;
            box-shadow: none;
            border-radius: 0;
            max-height: 75vh;
            height: auto;
            scrollbar-width: thin;

            // @media(max-width: 1280px){
            //     height: 400px;
            // }

       }
        
       thead{
        position: relative;
       }

        tr{
            vertical-align: top;
            position: relative;

            &:nth-child(n):after{
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                right: 0;
                background: #0000001F;
                width: 100%;
                height: 1px;
            }

            &:first-child::after, &:last-child::after{
                display: none;
            }

        }

        th, td{
            background: #F0F0F0;
            border-bottom: 0;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 600;
            color: #25385899;
            white-space: nowrap;
        }

        .green-bg{
            background: #D9F8FD;
            
        }
        .green-bg th{
            background: #D9F8FD;
            padding: 3px 16px 0px;
            width: 100%;
            min-width: 174px;

            @media(max-width: 1280px){
                // padding: 3px 25px 0px;
            }
        }

        td{
            background: #fff;
            padding: 12px 16px;
            color: #253858e3;
            text-overflow: ellipsis;
            overflow: hidden;
            font-weight: 500;
            height: 100px;
            font-size: 16px;

            p{
                color: #25385861;
            }

            span, small{
                font-size: 13px;
                color: #253858E3;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 3px;
                font-weight: 400;
                gap: 5px;
            }

            small{
                position: relative;
                font-weight: 400;
                color: #25385899;
                gap: 16px;

                &.text-right{
                    font-weight: 600;
                    margin: 0;
                }

                &.text{
                    color: #AF5F14;
                    line-height: normal;
                    font-weight: 400;
                    padding-bottom: 6px;
                }
            }

            em{
                font-style: normal;
                font-weight: 500;
                font-size: 16px;
            }

            strong{
                color: #0065FF;
                font-weight: 600;
                font-size: 13px;
                display: block;
                margin-top: 3px;
                
            }

            .icon{
                background: #DDECFF;
                padding: 6px 10px;
                border-radius: 16px;
                gap: 6px;
                font-size: 14px;
                font-weight: 500;
                display: inline-flex;
                margin: 0;
                line-height: normal;

                &.meeting{
                    background: #FFEFD6;
                }

                &.loggedout{
                    background: #FDF1EF;
                }
                &.lunch{
                    background: #FDF1EF;
                    color: #CB2AD7;
                }

                img{
                    width: 16px;
                }
            }
        }

        ::-webkit-scrollbar, ::-webkit-scrollbar-thumb, ::-webkit-scrollbar-track{
            width: 0px;
            background: transparent;
            box-shadow: none;
            border-radius: 0;
        }

        .work-done{
            background: #02A0B6;
            color: #fff;
            font-weight: 400;
            font-size: 12px;
            z-index: 11;
            text-align: center;
            width: 50%;
            height: 17px;
            line-height: 17px;
            padding: 0;

            // @media(max-width: 1280px){
            //     top: 175px;
            // }
        }

        .status-view {

            td.status{
                position: absolute;
                top: -50px;
                left: 0;
                right: 0;
                background: #ddecff4d;
                z-index: 11;
                display: flex;
                padding: 3px 4px;
                border-radius: 32px;
                gap: 66px;
                align-items: center;
                height: auto;
                z-index: inherit;

                .icon{
                    margin-right: 75px;
                    width: auto;
                }

                .icon.meeting{
                    margin-right: 90px;
                }

                .icon.calling{
                    margin-right: 98px;
                }

                .icon.idle{
                    margin-right: 114px;
                    background: #f6b1a74d;
                }

                .icon.calling{
                    background: #aee9f3
                }

                small.green-text, .green-text .text-right{
                    color: #028601;
                    font-weight: 600;

                    img{
                      position: relative;
                      left: 8px;
                      animation: fade 1000ms ease infinite;
                      -webkit-animation: fade 1000ms ease infinite;
                      -moz-animation: fade 1000ms ease infinite;
                      opacity: 1;

                      @keyframes fade {

                        0%{
                            opacity: 0.6;
                        }
                        
                        100%{
                            opacity: 1;
                        }

                      }

                    }

                }
            }

            .meeting-text{
                height: auto;
                display: none;
            }

            &.meeting td{
                background: #ffefd64d;

                &.meeting-text{
                    display: block;
                    background: transparent;

                    &::after{
                        display: none;
                    }
                }
            }

            &.loggedoff td{
                background: #ffdbd64d;
            }

            &.idle td{
                background: #f6b1a74d;
            }

            &.calling td{
                background: #D9F8FD;
            }

        }

        .inner-details{
            padding: 12px 0px;

            td{
                min-width: 174px;
                width: 100%;
            }
        }

    }
  
    .ryt-view {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        z-index: 10001;
    }
}