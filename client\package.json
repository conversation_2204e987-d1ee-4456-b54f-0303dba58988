{"name": "MatrixDashboard", "version": "2.0.0", "private": true, "homepage": "/", "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.18", "@mui/material": "^5.15.18", "@react-google-maps/api": "^2.19.3", "amazon-quicksight-embedding-sdk": "^2.3.0", "axios": "^1.4.0", "bootstrap": "^5.3.1", "core-js": "^3.29.1", "excel-date-to-js": "^1.1.5", "exceljs": "^4.4.0", "export-from-json": "^1.7.3", "file-saver": "^2.0.5", "history": "^5.3.0", "lottie-react": "^2.4.0", "moment": "^2.29.4", "multiselect-react-dropdown": "^2.0.25", "node-sass": "^9.0.0", "perfect-scrollbar": "^1.5.5", "postcss-loader": "^7.3.3", "primereact": "^10.0.2", "react": "^18.2.0", "react-bootstrap": "^2.8.0", "react-bootstrap-sweetalert": "^5.2.0", "react-bootstrap-typeahead": "^6.2.3", "react-checkbox-tree": "^1.8.0", "react-confetti": "^6.1.0", "react-csv": "^2.2.2", "react-custom-roulette": "^1.4.1", "react-data-table-component": "7.5.3", "react-datetime": "^3.2.0", "react-dom": "^18.2.0", "react-if": "^4.1.4", "react-json-to-table": "^0.1.7", "react-moment": "^1.1.3", "react-multi-select-component": "^4.3.4", "react-otp-input": "^3.0.2", "react-paginate": "^8.2.0", "react-player": "^2.12.0", "react-redux": "^8.1.2", "react-router": "^6.15.0", "react-router-dom": "^6.15.0", "react-scripts": "^5.0.1", "react-select": "^5.7.4", "react-spinners": "^0.13.8", "react-toastify": "^9.1.3", "reactstrap": "^9.2.0", "read-excel-file": "^5.6.1", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "sip.js": "^0.15.10", "socket.io-client": "^4.7.5", "styled-components": "^5.3.11", "underscore": "^1.13.6", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/parser": "^7.22.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@fortawesome/fontawesome-svg-core": "^6.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "compile-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.css", "minify-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.min.css --output-style compressed", "map-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.css --source-map true", "startHttps": "set HTTPS=true&&set PORT=3000&&set HOST=svlocalhost.policybazaar.com&&react-scripts start"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "optionalDependencies": {"jquery": "3.6.0", "typescript": "4.6.2"}}