import { Button, Modal, Form } from 'react-bootstrap';

const AddVerificationRemark = (props) => {
  const { data } = props;

  return (
    <>
      <Modal
        {...props}
        //size="lg"
        //aria-labelledby="contained-modal-title-vcenter"
        centered
        onHide={props.onAddResolvedCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            Add Verification Remark
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
        <Form>
            <Form.Group
              className="mb-3"
              controlId="exampleForm.ControlTextarea1"
            >
              <Form.Label>Add Verification Remark</Form.Label>
              <Form.Control as="textarea" rows={3} onChange={props.handleResolvedInputChange} />
            </Form.Group>
          </Form>

        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={props.onAddResolvedCancel}>Close</Button>
          <Button variant="primary" onClick={props.handleResolvedSave}>Save</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default AddVerificationRemark;