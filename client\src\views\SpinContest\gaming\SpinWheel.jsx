import React, { useEffect, useState, useRef } from "react";
import { connect } from "react-redux";
import { ToastContainer } from 'react-toastify';
import { GetCommonData, UpdateDataV2 } from "../../../store/actions/CommonActionPbIncentive";
import SweetAlert from "react-bootstrap-sweetalert";
import Confetti from 'react-confetti';
import { getuser } from "../../../utility/utility.jsx";
import { getUrlParameter } from '../../../utility/utility.jsx';
import './Spinwheel.css';
import { Wheel } from 'react-custom-roulette';
import Spinner from '../../../components/Loaders/Spinner';
import './Wheel.css';
import 'react-toastify/dist/ReactToastify.css';
import '../../../assets/scss/spinner.scss';

const SpinWheel = (props) => {
    const [mustSpin, setMustSpin] = useState(false);
    const [prizeNumber, setPrizeNumber] = useState(0);

    const [IsLoaded, setIsLoaded] = useState(false);
    const contestRewards = useRef([]);
    const RewardId = useRef([]);
    const ContestId = useRef("");
    const user = useRef({});
    const spinCount = useRef(0);
    const prizetype= useRef(null);
    const [update,setUpdate]=useState(false);
    const [attempted,setAttempted]=useState(false);
    const [contestName,setContestName]= useState("");
    const [ContestDescription,setContestDescription]= useState("");
    const [alert,setAlert]= useState(null);
    const [confetti,setConfetti]= useState(false);
    

    const data2 = [
        { option: '', style: { backgroundColor: '#FC4F00', textColor: 'black' } },
        { option: '', style: { backgroundColor: '#8B1874' } },
        { option: '', style: { backgroundColor: '#B71375', textColor: 'black' } },
        { option: '', style: { backgroundColor: '#F79540' } },
        { option: '', style: { backgroundColor: '#FF8DC7', textColor: 'black' } },
        { option: '', style: { backgroundColor: '#D27685' } },
        { option: '', style: { backgroundColor: '#9384D1', textColor: 'black' } },
        { option: '', style: { backgroundColor: '#D864A9' } },
    ]

    useEffect(() => {
        ContestId.current = getUrlParameter("cid");
        user.current = getuser();

        const isCompleted=localStorage.getItem('SpinContest' + ContestId?.current + '-' + user.current?.UserID);
        if(isCompleted === 'completed')
        {
            setAttempted(true);
        }
        
        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ContestMaster',
            con: [{ ContestId: parseInt(ContestId.current) }],
            c: "R",
        }, function (data) {
            if (Array.isArray(data[0]) && data[0].length>0)
            {
                setContestName(data[0][0]?.ContestName);
                setContestDescription(data[0][0]?.Description)
            }
        });

        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ContestOptionMaster',
            order: 'ContentId',
            con: [{ ContestId: parseInt(ContestId.current), IsActive: 1 }],
            c: "R",
        }, function (data) {
            if (Array.isArray(data[0]) && data[0].length > 0) {
                // setContestRewards(data[0]);
                contestRewards.current=[];
                RewardId.current=[];
                for (let index = 0; index < data[0].length; index++) {
                    const reward = data[0][index] || {};
                    contestRewards.current.push({ option: reward?.ContentText, style: data2[index%8].style, PrizeType: reward?.PrizeType || 0 });
                    RewardId.current.push(reward?.ContentId);
                }
                setIsLoaded(true);
            }
        });

    }, [])

    const handleSpinClick = () => {
        if (!mustSpin) {
            const newPrizeNumber = Math.floor(Math.random() * RewardId.current.length);

            spinCount.current = spinCount.current + 1;
            setPrizeNumber(newPrizeNumber);
            prizetype.current= contestRewards.current[newPrizeNumber].PrizeType;
       
            //   setPrizeNumber({RewardId: RewardId.current[newPrizeNumber], RewardText: contestRewards.current[newPrizeNumber].option});
            if(localStorage.getItem('SpinContest' + ContestId.current + '-' + user.current.UserID)!=='completed')
            {
            setMustSpin(true);
            }
            else{
                setMustSpin(false);
                setAttempted(true);
            }
        }
    }


    useEffect(() => {
        if (update && spinCount.current == 1) {
     
            callUpdateResult();
        }

    }, [update])


    const stopSpin=()=>{
        setMustSpin(false);   
        setUpdate(true);
        setAlert(getAlert());
        setConfetti(true);
        setTimeout(() => {
            setConfetti(false);
        },5000)
     
    }
   
    const getAlert=()=>(
        <SweetAlert 
        success 
        title={(
            contestRewards.current) && contestRewards.current[prizeNumber].option } 
        onConfirm={() => hideAlert()}
       
      >
      </SweetAlert>
    )

    const hideAlert=()=>{
        setAttempted(true);
          setAlert(null);
    }

    const callUpdateResult = () => {
        let UserId = user.current.UserID;
        let UpdatedOn = new Date();
        props.UpdateDataV2({
            root: 'ContestAgentMapping',
            querydata: { ContestId: ContestId.current },
            body: {
                RewardText: contestRewards.current[prizeNumber].option,
                IsCompleted: 1,
                RewardId: parseInt(RewardId.current[prizeNumber]),
                UpdatedOn: UpdatedOn,
                PrizeType: contestRewards.current[prizeNumber].PrizeType
            }

        }, (result) => {
            localStorage.setItem('SpinContest' + ContestId.current + '-' + user.current.UserID, 'completed');
            if (Array.isArray(result?.data?.data) && result?.data?.data?.length > 0 && result.data.data[0].status != 200) {
                setMustSpin(true);
                spinCount.current=0;
            }
            else {
                setMustSpin(false);
            }
        })

    }

    return (
        <>
        <ToastContainer/>
        <div className="content">
            { attempted ? <div  style={{justifyContent: 'center',alignItems: 'center'}}>
                    {confetti && <h1 className="AttemptsMsg">You have attempted the spin wheel</h1>}
                    {/* {confetti && prizetype.current && <h1 className="AttemptsMsg">You have attempted the SpinWheel</h1>} */}
                    {!confetti && <h1 className="AttemptsMsg">You have already attempted the SpinWheel.</h1>}
                </div> : <>
                    {IsLoaded ? <>
                    <div className="ContestCard">
                        <div>
                        <h2>{contestName.length>0 && contestName}</h2>

                        </div>
                    <img src="/lottery/mobile.png "/>

                    </div>
                        <div className="wheelOfFortuneWapper">
                            <img src="/lottery/WheelStand.png " className="towerImage" />
                            <Wheel
                                mustStartSpinning={mustSpin}
                                prizeNumber={prizeNumber}
                                data={contestRewards.current}
                                backgroundColors={['#3e3e3e', '#df3428']}
                                textColors={['#ffffff']}
                                outerBorderColor={'gold'}
                                innerBorderColor={'black'}
                                onStopSpinning={stopSpin}
                                radiusLineWidth={1}
                                fontSize={14}
                            />
                        </div>
                        {confetti && prizetype.current && <Confetti numberOfPieces={200} />}
                    
                        {alert}
                        <div className="text-center"> <button onClick={handleSpinClick} className="SpinWheelBtn">SPIN</button></div> </> : <Spinner />
                       
                    }</>
                }
            </div>
          
        </>
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    { GetCommonData, UpdateDataV2 }
)(SpinWheel);