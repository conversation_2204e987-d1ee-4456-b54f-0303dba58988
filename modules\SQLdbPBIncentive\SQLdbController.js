const tblList = require("../constants");
const tblListWithParams = require("../constants");
const methods = require("./SQLdbMethods");
const sqlHelper = require("../../LibsPBIncentive/sqlHelper");
const UploadMatrixFiles = require("./UploadMatrixFiles");
const newrelic = require('newrelic');
const { isInputSafe } = require("../common/CommonMethods");

async function insert(req, res) {
    try {


        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'INSERT INTO ' + tblList[endpoint] + '(';
        var columns = [];
        var VALUES = [];
        let sqlparams = [];
        // for (var key in inputdata) {
        //     columns.push(key);
        //     VALUES.push(inputdata[key]);
        // }
        for (var key in inputdata) {
            columns.push(`${key}`);
            VALUES.push(`@${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + columns.join() + ") VALUES ( ";
        query = query + VALUES.join() + ")";

        if (req.body.data.scope)
            query = query + "; SELECT SCOPE_IDENTITY()";
        // console.log(inputdata);
        // console.log(query);
        // console.log(sqlparams);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);
        // console.log(result);
        let e = JSON.stringify(result);
        var myArr = JSON.parse(e);

        if (typeof (myArr[0]) !== 'undefined') {
            if (typeof (myArr[0].error) !== 'undefined') {
                res.send({
                    status: 500,
                    error: myArr[0].error
                });
            }
        }

        res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function update(req, res) {
    try {

        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        let querydata = req.body.data.querydata;
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
        var updatedata = [];
        var updatequery = [];
        let sqlparams = [];

        for (var key in inputdata) {
            updatedata.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + updatedata.join();
        query = query + ` WHERE `;

        for (var key in querydata) {
            updatequery.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: querydata[key] });
        }
        query = query + updatequery.join(' and ');
        // console.log(query);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);

        res.send({
            status: 200,
            data: result,
            message: "Success"
        });

    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        await sql.close();
    }
}

async function updatev2(req, res) {
    try {
        let { userId } = req.user || {}
        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        let querydata = req.body.data.querydata;
        querydata = { ...querydata, UserId: userId}
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
        var updatedata = [];
        var updatequery = [];
        let sqlparams = [];

        for (var key in inputdata) {
            updatedata.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + updatedata.join();
        query = query + ` WHERE `;

        for (var key in querydata) {
            updatequery.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: querydata[key] });
        }
        query = query + updatequery.join(' and ');
        // console.log(query);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);

        res.send({
            status: 200,
            data: result,
            message: "Success"
        });

    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        await sql.close();
    }
}

async function getdata(req, res) {
    try {

        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        if (methods.FindRoot(req, res)) {
            return;
        }

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        // let skip = req.query.skip ? parseInt(req.query.skip) : 0,
        //     limit = req.query.limit ? parseInt(req.query.limit) : 10;

        let tablename = tblList[endpoint];
        if (tablename?.toUpperCase().indexOf("NOLOCK") == -1) {
            tablename = tablename + " (NOLOCK) "
        }

        var query = 'SELECT  * FROM ' + tablename;// + ' (NOLOCK) ';


        if (req.query.cols && req.query.cols.length > 0) {
            for(let i=0;i<req.query.cols.length;i++)
                {
                    if(isInputSafe(req.query.cols[i]))
                    {
                        continue;
                    }
                    else{
                        return res.send({
                            status: 500,
                            error: "Please enter correct inputs."
                        });
                    }
                }
            query = 'SELECT  ' + req.query.cols.join() + ' FROM ' + tablename;// + ' (NOLOCK) ';
        }
        var whereCondition = [];
        let sqlparams = [];
        if (req.query.con) {
            query = query + ` WHERE `;
            for (var key in req.query.con) {
                let json = req.query.con[key];
                for (var obj in json) {
                    // whereCondition.push(`${obj} = '${json[obj]}'`);
                    var objparam = obj;
                    if (obj.indexOf('.') !== -1) {
                        var objparam = obj.replace('.', '')
                    }
                    //console.log(objparam);
                    if (obj.toLowerCase().indexOf('receivedon') > -1 || obj.toLowerCase().indexOf('date') > -1 || obj.toLowerCase().indexOf('time') > -1) {
                        whereCondition.push(`CAST(${obj} AS DATE) = @${objparam}`);
                    }
                    else if (obj.toLowerCase().indexOf('likesearch') > -1) {
                        whereCondition.push(`${obj.substr(0, obj.indexOf('_'))} like @${objparam.substr(0, objparam.indexOf('_'))}`);
                    }
                    else {
                        whereCondition.push(`${obj} = @${objparam}`);
                    }
                    if (objparam.toLowerCase().indexOf('likesearch') > -1) {
                        sqlparams.push({ key: objparam.substr(0, objparam.indexOf('_')), value: '%' + json[obj] + '%' });

                    } else {
                        sqlparams.push({ key: objparam, value: json[obj] });
                    }
                }
            }
            query = query + whereCondition.join(' and ');
        }
        if (req.query.order) {
            if (!req.query.direction)
                query = query + " ORDER BY " + req.query.order + " DESC";
            else
                query = query + " ORDER BY " + req.query.order + " " + req.query.direction;
        } else {
            query = query + " ORDER BY 2 DESC";
        }
        // query =
        //     query +
        //     ` ORDER BY 1
        //     OFFSET ${skip} ROWS
        //     FETCH NEXT ${limit} ROWS ONLY`;

        // console.log(query);
        //console.log(sqlparams);

        // let result = await sql.query(query);
        // await sql.close();
        let result = await sqlHelper.sqlquery(c, query, sqlparams);
        //console.log(result.recordsets);
        return res.send({
            status: 200,
            data: result.recordsets,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function getdatasp(req, res) {
    try {
        // console.log(req.query);
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        var query = tblList[endpoint];
        let sqlparam = [];
        //const request = new sql.Request();
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                const obj = params[key]
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        // console.log(sqlparam);
        let result = await sqlHelper.sqlProcedure(c, query, sqlparam);

        return res.send({
            status: 200,
            data: result.recordsets,
            message: "Success"
        });



    } catch (err) {
        console.log('catch error', err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}

const GetSqlParams = ({ required, user, params }) => {
    try {
        const parameters = params && params.length > 0 && params[0] || '{}';
        const obj = parameters;
        let sqlParams = [];

        for (let itr = 0; itr < required.length; itr++) {
            const item = required[itr];
            const { element, from, userKey } = item;

            if (from != 'user') {
                if (!(element in obj)) {
                    return [];
                } else {
                    sqlParams.push({
                        key: element,
                        value: obj[element]
                    });
                }

            } else if (from == 'user' && userKey) {
                const info = user[userKey] || null;
                sqlParams.push({
                    key: element,
                    value: info
                });
            }
        }
        return sqlParams;
    } catch (err) {
        console.log('Inside GetSqlParams : ', err);
        return [];
    }
}

async function getdataspV2(req, res) {
    try {
        let endpoint = req.query.root;
        let c = req.query.c ? req.query.c : "R";
        let params = req.query.params;

        if (!tblListWithParams[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        const query = tblListWithParams[endpoint].proc || "";
        const requiredParameters = tblListWithParams[endpoint].params || [];
        let sqlParams = GetSqlParams({ required: requiredParameters, user: req.user, params: params });

        // console.log('List SP V2:: ', sqlParams);
        let result = await sqlHelper.sqlProcedure(c, query, sqlParams);
        newrelic.recordMetric(`Booking/${endpoint}`, result);

        return res.send({
            status: 200,
            data: result?.recordsets || [],
            info: req.user,
            message: "Success"
        });

    } catch (err) {
        console.log('catch error', err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function deleteRow(req, res) {
    try {
        // console.log('---------');
        let endpoint = req.body.data.root;
        let sqlParams = [];
        // var patharray = req.url.split("/");
        // var endpoint = patharray[patharray.length - 1];
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'DELETE FROM ' + tblList[endpoint];
        var updatequery = [];
        if (req.body.data.query) {
            query = query + ` WHERE `;
            for (var key in req.body.data.query) {
                updatequery.push(`${key} = @${key}`);
                sqlParams.push({ key: key, value: req.body.data.query[key] });
            }
            query = query + updatequery.join();
        }
        // console.log(query, '-------');
        let result;
        if (query.indexOf("WHERE") > -1) {
            result = await sqlHelper.sqlquery("L", query, sqlParams);
        }
        //let result = await sql.query(query);
        res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        await sql.close();
    }
}


async function login(req, res) {
    try {

        let inputdata = req.body;

        let query = "Select userid from CRM.UserDetails (NOLOCK) where EmployeeId = @EmployeeId and CAST(Password AS Varchar) = @Password";
        let sqlparams = [];
        sqlparams.push({ key: "EmployeeId", value: inputdata.EmpId });
        sqlparams.push({ key: "Password", value: inputdata.password });
        let result = await sqlHelper.sqlquery("R", query, sqlparams);

        if (result && result.recordset.length > 0) {
            res.cookie("userid", result.recordset[0].userid);
            res.cookie("AgentId", result.recordset[0].userid);
            res.redirect(req.headers.origin + "/admin/Users");
        } else {
            res.redirect(req.headers.origin + "?m=User Not found.");
        }
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}


async function ContestMapping(req, res) {
    try {
        UploadMatrixFiles.ContestMapping(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

module.exports = {
    insert: insert,
    update: update,
    updatev2: updatev2,
    getdata: getdata,
    delete: deleteRow,
    getdatasp: getdatasp,
    getdataspV2: getdataspV2,
    login: login,
    ContestMapping: ContestMapping,
};