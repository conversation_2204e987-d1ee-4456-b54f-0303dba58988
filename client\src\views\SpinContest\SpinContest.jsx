import React, { useEffect } from "react";
import { useState } from "react";
import { ButtonGroup, Button, Modal, Form, FormGroup } from 'react-bootstrap';
import moment from 'moment';
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData } from "../../store/actions/CommonActionPbIncentive";
import { If, Then } from 'react-if';
import { connect } from "react-redux";
import DataTable from '../Common/DataTableWithFilter';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SweetAlert from "react-bootstrap-sweetalert";
import { fnRenderfrmControl, fnDatatableCol, GetJsonToArray, getuser } from '../../utility/utility.jsx';
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'

import {
  <PERSON>,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
// import "../../../assets/scss/Agent.scss";
import Spinner from '../../components/Loaders/Spinner';


const SpinContest = (props) => {
  let selectedRow = {
    ContestName: "",
    IsActive: true,
    Description: "",
    ApplicationId: 0,
    modalValueChanged: false,
    StartDate: new Date(),
    EndDate: new Date(),
    change: false
  };

  const [isLoaded, setIsLoaded] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState([]);
  const [store, setStore] = useState([]);
  const [activePage, setActivePage] = useState(1);
  const [root, setRoot] = useState("ContestMaster");
  const [rootTable, setRootTable] = useState("ContestApplicationMaster");
  const [departmentId, setDepartmentId] = useState("0");
  const [pageTitle, setPageTitle] = useState("Contest List");
  const [formTitle, setFormTitle] = useState("");
  const [ColumnValues, setColumnValues] = useState(selectedRow);
  const [event, setEvent] = useState("");
  const [user, setUser] = useState({});
  const [modalValueChanged, setModalValueChanged] = useState(false);
  const [alert, setAlert] = useState(null);
  const [Od, setOd] = useState({});
  const [ApplicationMaster, setApplicationMaster] = useState([]);
  const [ContestData, setContestData] = useState([]);
  const [options, setOptions] = useState([]);
  const [deletedOptions, setDeletedOptions] = useState([]);
  const [copyAlert, setCopyAlert] = useState(null);
  const [save, setSave] = useState(false);
  const [contestId, setContestId]= useState(null);
  const [copyId,setCopyId]= useState(null);
  

  let columnlist = [
    {
      name: "ContestId",
      label: "ContestId",
      type: "number",
      searchable: true,
      editable: false
    },
    {
      name: "ContestName",
      label: "ContestName",
      type: "string",
      searchable: true,
      required: true

      // editable: false
    },
    {
      name: "Description",
      label: "Description",
      type: "textarea",
      searchable: true,
      // editable: false
    },
    {
      name: "ApplicationId",
      label: "Application",
      type: "textarea"
      // searchable: true,
    },
    {
      name: "IsActive",
      label: "IsActive",
      type: "bool"
      // searchable: true,
    },
    {
      name: "StartDate",
      label: "StartDate",

    },
    {
      name: "EndDate",
      label: "EndDate"
    }

  ];

  useEffect(() => {
  //  window.location.reload();
    // setIsLoaded(true);
    setUser(getuser());
    
    props.GetCommonData({
      limit: 10,
      skip: 0,
      root: root,
      cols: GetJsonToArray(columnlist, "name"),
      order: 'ContestId',
      c: "R",
    }, function (data) {
      setIsLoaded(true);
      if (Array.isArray(data) && data.length > 0) {

        for (let i = 0; i < data[0].length; i++) {
          data[0][i].StartDate = moment(data[0][i].StartDate).format("YYYY/MM/DD");
          data[0][i].EndDate = moment(data[0][i].EndDate).format("YYYY/MM/DD");
        }

        setContestData(data[0]);
      }
    });

    props.GetCommonData({
      limit: 10,
      skip: 0,
      root: rootTable,
      c: "R",
    }, (result) => {
      if (Array.isArray(result) && result[0][0] || null) {
        setApplicationMaster(result[0]);
      }
    });

  }, []);

  const fnDatatableCol1 = () => {
    let columns = fnDatatableCol(columnlist);
    columns.push({
      name: "Action",
      cell: row =>
        <ButtonGroup aria-label="Basic example">
          {/* <Button title="Copy" variant="secondary" onClick={(e) => handleCopy(e, row)}><i className="fa fa-copy" aria-hidden="true"></i></Button>{copyAlert} */}
          <Button title="Edit" variant="secondary" onClick={() => handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
          <Button title="Add Content" variant="secondary" onClick={() => window.location.href = "/admin/ContestContent?cid=" + row.ContestId + "&ContestName=" + row.ContestName}><i className="fa fa-plus" aria-hidden="true"></i></Button>
          {/* <Button variant="secondary" onClick={(e) => handleDelete(e, row)}><i className="fa fa-trash" aria-hidden="true"></i></Button>{alert} */}
        </ButtonGroup>
    });
    return columns;
  }

  // const handleDelete = (e, row) => {
  //   e.preventDefault();
  //   const getAlert = (row) => (
  //     <SweetAlert
  //       warning
  //       showCancel
  //       confirmBtnText="Yes!"
  //       // confirmBtnBsStyle="danger"
  //       // cancelBtnBsStyle="default"
  //       title="Are you sure you want to delete this Contest?"
  //       onConfirm={() => handleRemove(row)}
  //       onCancel={() => onCancelDelete()}
  //     >

  //     </SweetAlert>
  //   );
  //   setAlert(getAlert(row));
  // }


  // const onCancelDelete = () => {
  //   setAlert(null);
  // }


  // const handleRemove = (row) => {
  //   setAlert(null);
  //   props.DeleteData({
  //     root: root,
  //     query: { ContestId: row.ContestId }

  //   }, (data) => {
  //     if (data)
  //       toast("Contest deleted Successfully!", { type: 'success' });
  //   });

  //   setTimeout(() => {
  //     props.GetCommonData({
  //       limit: 10,
  //       skip: 0,
  //       root: root,
  //       cols: GetJsonToArray(columnlist, "name"),
  //       order: 'ContestId',
  //       c: "R",
  //     }, (data) => {
  //       if (data && Array.isArray(data[0]) && data[0].length > 0)
  //         for (let i = 0; i < data[0].length; i++) {
  //           data[0][i].StartDate = moment(data[0][i].StartDate).format("YYYY/MM/DD");
  //           data[0][i].EndDate = moment(data[0][i].EndDate).format("YYYY/MM/DD");
  //         }
  //       setContestData(data[0]);
  //     });
  //   }, 1000);
  // }

  const handleCopy = (e, row) => {
    e.preventDefault();
    const CopyAlert = (row) => (
      <SweetAlert
        warning
        showCancel
        confirmBtnText="Yes!"
        // confirmBtnBsStyle="danger"
        // cancelBtnBsStyle="default"
        title="Are you sure you want to copy this Contest?"
        onConfirm={() => CopyContest(row)}
        onCancel={() => onCancelCopy()}
      >

      </SweetAlert>
    );
    setCopyAlert(CopyAlert(row));
  }

  const CopyContest = (row) => {
    setCopyAlert(null);
    let { ApplicationId, ContestId, ContestName, Description, EndDate, StartDate, IsActive } = row;

    setColumnValues(prev => ({
      ...prev,
      ApplicationId: ApplicationId,
      ContestName: ContestName + "Copy",
      Description: Description,
      EndDate: EndDate,
      StartDate: StartDate,
      IsActive: IsActive,
      change: true
    }));
    setCopyId(ContestId);
  }

  useEffect(() => {
    if (ColumnValues.change == true) {
      handleSave();
    }
  }, [ColumnValues.change])

  const onCancelCopy = () => {
    setCopyAlert(null);
  }

  const handleEdit = (row) => {
    setColumnValues(Object.assign({}, row, {}));
    setEvent("Edit");
    setShowModal(true);
    setFormTitle("Edit Contest");
  }

  const handleClose = () => {
    if (event == "Edit") {
      setColumnValues(selectedRow);
      fnCleanData();
    }
    setShowModal(false);
  }

  const handleShow = () => {
    setColumnValues(ColumnValues);
    setEvent("Add");
    setShowModal(true);
    setFormTitle("Add New Contest");
    // this.setState({ formvalue: this.state.formvalue, event: "Add", showModal: true, FormTitle: "Add New Contest" });
  }


  const handleSave = () => {
    if (ColumnValues?.ApplicationId == 0) {
      toast.error("Please select an application");
      return;
    }
    if (ColumnValues?.ContestName.length > 30) {
      toast.error("Contest Name should be less than 30 characters");
      return;
    }
    if (ColumnValues?.Description.length > 200) {
      toast.error("Desription should be less than 200 characters");
      return;
    }
    let formvalue = JSON.parse(JSON.stringify(ColumnValues));
    setColumnValues(selectedRow);

    if (formvalue.EndDate < formvalue.StartDate || formvalue.EndDate < new Date()) {

      toast.error(
        "Select an EndDate on/after " + moment(formvalue.StartDate).format('YYYY/MM/DD')
      );
      setShowModal(false);
      return;
    }
    formvalue.StartDate = moment(formvalue.StartDate).format('YYYY/MM/DD 00:00:00')
    formvalue.EndDate = moment(formvalue.EndDate).format('YYYY/MM/DD 00:00:00')
    fnCleanData();

    let cid = formvalue.ContestId;
    delete formvalue["ContestId"];
    delete formvalue["modalValueChanged"];
    delete formvalue["change"];

    if (event == "Edit") {
      formvalue.UpdatedOn=moment().format("YYYY-MM-DD HH:mm:ss");
      formvalue.UpdatedBy=user.UserID;
      let res = props.UpdateData({
        root: root,
        body: formvalue,
        querydata: { ContestId: cid },
        c: "L",

      }, (data) => {
        if (data)
          toast("Contest Saved Successfully!", { type: 'success' });
      });

    } else if(event=="Add") {
      formvalue.CreatedOn = moment().format("YYYY-MM-DD HH:mm:ss");
      formvalue.CreatedBy= user.UserID;
      props.InsertData({
        root: root,
        body: formvalue
      }, (data) => {

        if (data?.data?.status != 200) {
          toast.error(data?.data?.message);
        } else
       
          toast("Contest Added Successfully!", { type: 'success' });
      });
    }
    else{
      formvalue.CreatedOn = moment().format("YYYY-MM-DD HH:mm:ss");
      formvalue.CreatedBy = user.UserID;
      props.InsertData({
        root: root,
        body: formvalue,
        scope:true
      }, (data) => {

        if (data?.data?.status != 200) {
          toast.error(data?.data?.message);
        } else
        {
          
          setContestId(data.data.data.recordset[0][""]);
          toast("Contest Added Successfully!", { type: 'success' });
        }
      });
    }
    setTimeout(() => {
      props.GetCommonData({
        limit: 10,
        skip: 0,
        root: root,
        cols: GetJsonToArray(columnlist, "name"),
        order: 'ContestId ',
        c: "R",
      }, (data) => {
        debugger
        if (data && Array.isArray(data[0]) && data[0].length > 0)
          for (let i = 0; i < data[0].length; i++) {
            data[0][i].StartDate = moment(data[0][i].StartDate).format("YYYY/MM/DD");
            data[0][i].EndDate = moment(data[0][i].EndDate).format("YYYY/MM/DD");
          }
        setContestData(data[0]);
      });
    }, 1000);

    //   this.setState({ showModal: false });
    setShowModal(false);

    return false;
  }




  // useEffect(()=>{
  //      if(contestId)
  //      {
  //       props.GetCommonspData({
  //         root: 'CopyContestOptions',
  //         params: [{ CreatedContestId: contestId, UserId: user.UserID, ContestId: copyId}],
  //         c: "L",
  //     }, function (data) {
  //        console.log(data);
  //     });
  //      }
  // },[contestId])

  const fnCleanData = () => {
    //
  }

  const columns = fnDatatableCol1();

  const handleStartChange = (e) => {

    if (moment(new Date).format("YYYY/MM/DD") > moment(e._d).format("YYYY/MM/DD")) {
      toast.error('Select a date on/after ' + moment(new Date()).format("YYYY/MM/DD"));
      return;
    }
    setColumnValues(prev => ({
      ...prev,
      StartDate: moment(e._d).format("YYYY/MM/DD"),
      modalValueChanged: true
    }))

  }

  const handleEndChange = (e) => {

    if (ColumnValues.StartDate > e._d) {
      toast.error(
        "Select a date after " + moment(ColumnValues.StartDate).format("YYYY/MM/DD")
      );
      return;
    }

    setColumnValues(prev => ({
      ...prev,
      EndDate: moment(e._d).format("YYYY/MM/DD"),
      modalValueChanged: true
    }))
  }




  return (
    <>
      {isLoaded == false ? <div className="Backdrop"> <Spinner></Spinner></div> :
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h4">{pageTitle}</CardTitle>
                    </Col>
                    <Col md={2}>
                      <Button variant="primary" onClick={handleShow}>Add New Contest</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={ContestData}
                    extention={true}
                    export={false}
                    print={false}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{formTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmChatAgentConfigure">
                <Row>

                  <Form.Group as={Col} md={4} >
                    <Form.Label>Contest Name</Form.Label>
                    <Form.Control required

                      type="text" onChange={(e) => setColumnValues(prev => ({
                        ...prev,
                        ContestName: e.target.value,
                        modalValueChanged: true
                      }))} placeholder="Enter Contest Name"
                      value={ColumnValues.ContestName} name="ContestName" />
                  </Form.Group>
                  <Form.Group as={Col} md={6}>
                    <Form.Label>Description</Form.Label>
                    <Form.Control required
                      type="text" onChange={(e) => setColumnValues(prev => ({
                        ...prev,
                        Description: e.target.value,
                        modalValueChanged: true
                      }))} as="textarea" placeholder="Enter Description"
                      value={ColumnValues.Description} name="Description" />
                  </Form.Group>
                  <Form.Group as={Col} md={2} key={ColumnValues.ApplicationId}>
                    <Form.Label>Application</Form.Label>
                    <Form.Control as="select"
                      onChange={(e) => setColumnValues(prev => ({
                        ...prev,
                        ApplicationId: e.target.value,
                        modalValueChanged: true
                      }))}
                      // defaultValue={ColumnValues.ApplicationId}

                      value={ColumnValues.ApplicationId || 0}
                      placeholder="Select"
                      required>
                      <option key={0} value={0} disabled selected>Select Option</option>
                      {ApplicationMaster && ApplicationMaster.map((item, idx) =>
                        <option key={item.ApplicationId} value={item.ApplicationId}>{item.ApplicationName}</option>
                      )}
                    </Form.Control>
                  </Form.Group>

                  <Row>
                    <Form.Group as={Col} md={3}>
                      <Form.Label>Start Date</Form.Label>
                      <Datetime timeFormat={false} id="StartDate" value={ColumnValues.StartDate} name="StartDate" onChange={handleStartChange} />
                    </Form.Group>
                    <Form.Group as={Col} md={3}>
                      <Form.Label>End Date</Form.Label>

                      <Datetime timeFormat={false} id="EndDate" value={ColumnValues.EndDate} name="EndDate" onChange={handleEndChange} />
                    </Form.Group>
                    <Form.Group as={Col} md={3} >
                      <Form.Label>IsActive &nbsp;</Form.Label>
                      <input type="checkbox"
                        label=""
                        onChange={(e) => setColumnValues(prev => ({
                          ...prev,
                          IsActive: e.target.checked,
                          modalValueChanged: true
                        }))}
                        checked={ColumnValues.IsActive}
                        name="IsActive" id="IsActive" />
                    </Form.Group>
                  </Row>

                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={handleClose}>
                Close
              </Button>
              <If condition={ColumnValues.modalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      }
    </>
  );

}

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    InsertData,
    UpdateData,
    DeleteData
  }
)(SpinContest);
