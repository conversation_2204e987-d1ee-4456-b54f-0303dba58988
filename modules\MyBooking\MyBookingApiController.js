

const { MY_BOOKINGS_BMS_API, MY_BOOKINGS_BMS_API2 } = require("../common/ActionTypes");
const CommonController = require("../common/CommonController");
const MyBookingApiMethods = require("./MyBookingApiMethods");


const getBookingByBookingId = async (req, res) => {
    let IsAllowed=0; BookingId = 0;
    const { SearchText, SearchByField, BookingType } = req.body;
    const UserId = req?.user?.userId;

    if (!SearchText) {
        return res.status(400).send({
            isError: true,
            errorMessage: "BookingId or Application Number is Mandatory"
        });
    }
    // ----------  check if lead is from user's span   ------------- 
    const SearchAllowedResponse = await MyBookingApiMethods.IsBookingSearchAllowed({ SearchText, UserId, SearchByField, BookingType })
    
    if(SearchAllowedResponse){
        IsAllowed=SearchAllowedResponse.IsAllowed || 0;
        // IsAllowed=1;
    }
    const ErrorCodeMsg = {
        "-1": "Please enter a Valid Booking ID",
        "-2": "Please enter a Valid Application Number",
        "-3": "Please enter SearchByField and search value",
        "0": "Booking ID doesn't belongs to your Span"
    }

    if (IsAllowed <= 0) {
        return res.status(400).send({
            isError: true,
            errorMessage: ErrorCodeMsg[IsAllowed.toString()]
        });
    }else{
        BookingId = (SearchAllowedResponse && SearchAllowedResponse.BookingId) ? parseInt(SearchAllowedResponse.BookingId):0;
       
    }
    // --------------------------------------- 

    // ------------------ Get Booking Details from bms---------------------
    req.body.Type=MY_BOOKINGS_BMS_API;
    req.body.BookingId= BookingId ;
    req.body.Data={
        "AgentID": UserId,
        "BookingType": BookingType,
        "BookingMonth": "1970-01-01", // any date works, but not null
        "ManagerIds": UserId.toString(),
        // "ProductIds": null,
        // "FilterProduct": null,
        "ProductPresent": 1,
        "LeadIDs": [
            BookingId
        ],
        "SkipFetchLead": true
    }

    await CommonController.PostCommonApi(req, res);
    
}

module.exports = {
    getBookingByBookingId: getBookingByBookingId
};