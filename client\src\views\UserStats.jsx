import React from "react";
import {
  GetCommonData, GetCommonspData, GetComunicationData
} from "../store/actions/CommonAction";
import { getUrlParameter, hhmmss, getuser } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";


class UserStats extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "User Stats",
      items: {},
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      key: "-1",
      Performancekey: "-1",
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    this.columnlist = [

      {
        name: "EmployeeId",
        selector: "EmployeeId",

        sortable: true,
      },
      {
        name: "UserName",
        selector: "UserName",

        sortable: true,
      },
      {
        name: "GroupName",
        selector: "GroupName",

        sortable: true,
      },
      {
        name: "LoginHours (hrs)",
        selector: "LoginHours",
        sortable: true,
        cell: row => <span >{(row.LoginHours / 60).toFixed(1)} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "LoginHours")}</span>
      },
      {
        name: "IdleTime (min)",
        selector: "IdleTime",
        sortable: true,
        cell: row => <span >{(row.IdleTime)} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "IdleTime", true)}</span>
      },
      {
        name: "Talktime (min)",
        selector: "TalkTime",
        sortable: true,
        cell: row => <span >{(row.TalkTime / 60).toFixed(0)} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "TalkTime")}</span>
      },
      {
        name: "Attempts",
        selector: "Attempts",
        sortable: true,
        cell: row => <span >{row.Attempts} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "Attempts")}</span>
      },
      {
        name: "UniqueDials",
        selector: "UniqueDials",
        sortable: true,
        cell: row => <span >{row.UniqueDials} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "UniqueDials")}</span>
      },

      {
        name: "TotalBreaks",
        selector: "TotalBreaks",
        sortable: true,
        cell: row => <span >{row.TotalBreaks} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "TotalBreaks", true)}</span>
      },
      {
        name: "MissedCB",
        selector: "MissedCB",
        sortable: true,
        cell: row => <span >{row.MissedCB} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "MissedCB", true)}</span>
      },
      {
        name: "APE (mtd)",
        selector: "APE",
        sortable: true,
        cell: row => <span >{row.APE} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "APE")}</span>
      },
      {
        name: "BKGS (mtd)",
        selector: "BKGS",
        sortable: true,
        cell: row => <span >{row.BKGS} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "BKGS")}</span>
      },
      {
        name: "Missed Attempts",
        selector: "MissedAttempts",
        sortable: true,
        cell: row => <span >{row.MissedAttempts} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "MissedAttempts", true)}</span>
      },
      {
        name: "Cancel Attempts",
        selector: "CancelAttempts",
        sortable: true,
        cell: row => <span >{row.CancelAttempts} {this.PerformanceCalculation(row, this.state.items.AvgGroupList, "CancelAttempts", true)}</span>
      },
    ];
  }
  handleShow(e) {

    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);

  }
  statuschange(e) {
    this.setState({ key: e.target.value });
  }

  CheckPerformance(e) {
    this.setState({ Performancekey: e.target.value });
  }
  filterdata(e) {

    let alldata = this.state.items.UsersList
    let that = this;
    if (this.state.key === "-1" && this.state.Performancekey === "-1") {
      return alldata;
    }

    let AgentData = [];
    alldata.forEach(element => {
      if (this.state.key != -1 && this.state.key.indexOf(element.GroupId) > -1) {
        AgentData.push(element);
      }      
    });

    if(AgentData && AgentData.length == 0){
      
    }
    



    return AgentData;
  }
  PerformanceCalculation(row, AvgGroupList, field, MissedCB) {
    let groupid = row.GroupId;
    let grpavg = {}
    if (AvgGroupList.length > 1) {
      AvgGroupList.forEach(item => {
        if (item.GroupId == groupid) {
          grpavg = item;
        }
      });

    }
    else {
      grpavg = AvgGroupList[0];
    }

    let data = ((row[field] / (grpavg[field] == 0 ? 1 : grpavg[field])) - 1) * 100;
    let color;
    if (MissedCB == true) {
      if (data > 0) {
        color = "fa fa-long-arrow-up userstatred";
      }
      else {
        color = "fa fa-long-arrow-down userstatgreen";
      }
    }
    else {
      if (data < 0) {
        color = "fa fa-long-arrow-down userstatred";
      }
      else {
        color = "fa fa-long-arrow-up userstatgreen";
      }
    }

    if (data < 0) {
      data = data * -1;
    }

    return <>
      <i className={color}> {data.toFixed(0)}%</i>
    </>
  }

  componentDidMount() {

    var that = this;
    const user = getuser();
    this.setState({ ReportTime: new Date(), SelectedSupervisors: [user.UserID] });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);

  }

  fetchData() {
    let that = this;
    var SelectedSupervisors = this.state.SelectedSupervisors;
    this.props.GetCommonspData({
      root: "GetUserStats",
      params: [{ ManagerIds: SelectedSupervisors.join() }, { UserId: 0 }],
    }, function (data) {
      if (data && data.data && data.data.data) {
        let items = { UsersList: data.data.data[0], AvgGroupList: data.data.data[1] }
        that.setState({ items: items });
      }
    });

    if (that.state.SelectedRows.length > 0) {
      that.dtRef.current.handleClearRows();
    }

  }


  componentWillUnmount() {

  }


  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {

    }
  }


  render() {
    const columns = this.columnlist;

    const { items, PageTitle } = this.state;
    console.log(items);
    const data = this.filterdata();
    let ddl = [];
    if (items && items.AvgGroupList) {
      items.AvgGroupList.forEach(item => {
        ddl.push(<option value={item.GroupId}>{item.GroupName}</option>)
      });
    }


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={5}>

                    </Col>
                    <Col md={2}>
                      <div className="form-group">
                        <select className="form-control" onChange={this.CheckPerformance}>
                          <option value="-1">ALL</option>
                          <option value="80">Less than 80</option>
                          <option value="60">Less than 60</option>
                          <option value="50">Less than 50</option>
                          <option value="40">Less than 40</option>

                        </select>
                      </div>
                    </Col>
                    <Col md={2}>
                      <div className="form-group">
                        <select className="form-control" onChange={this.statuschange}>
                          <option value="-1">ALL</option>
                          {ddl}
                        </select>
                      </div>
                      <ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>
                    </Col>

                  </Row>

                </CardHeader>

                <CardBody>

                  <div className="statusdata">
                    <DataTable
                      columns={columns}
                      data={data}
                      pagination={false}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense
                      ref={this.dtRef}
                    />

                  </div>
                </CardBody>

              </Card>
            </Col>

          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetComunicationData
  }
)(UserStats);




