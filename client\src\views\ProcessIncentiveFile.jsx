import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, ProcessUploadIncentiveFile, GetCommonspData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from './Common/DataTableWithFilter';
import { fnRenderfrmControl, fnBindRootData, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter, getuser, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ConfirmDialog from './Common/ConfirmDialog';
import moment from 'moment';
import Moment from 'react-moment';
import result from "lodash/result";

class ProcessIncentiveFile extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      activePage: 1,
      root: "ProcessIncentiveFile",
      PageTitle: "ProcessIncentiveFile",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      ResetTokenData: '',
      ConfirmationOpen: false,
      processedFileModal: false,
      ProcessInfoData: [],
      confirm: "no",
      IncentiveFileData: [],
    };
    this.handleClose = this.handleClose.bind(this);

    this.selectedrow = { "UserID": 0, "UserName": "", "EmployeeId": "" }
    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );

    // this.processfilecolumns = [
    //   {
    //     name: "LeadID",
    //     label: "LeadID",
    //     sortable: false,
    //   },  
    //   {
    //     name: "IsSI",
    //     label: "IsSI",
    //   },
    //   {
    //     label: "Status",
    //     name: "message",
    //     cell: row => <div className="status">{row.message ? row.message : "N.A"}</div>,
    //     searchable: true
    //   },


    // ]
    this.columnlist = [
      {
        name: "Id",
        label: "Id",
        type: "hidden",
        hide: true,
        sortable: false,
        searchable: false
      },
      {
        name: "FileName",
        label: "FileName",
        type: "string",
        sortable: true,
        searchable: true,
        cell: row => <Cell v={row.FileName} />,
      },
      {
        label: "IncentiveMonth",
        name: "IncentiveMonth",
        cell: row => <div className="calldate">{row.IncentiveMonth ? <Moment format="D MMM YYYY" utc={true}
        >{row.IncentiveMonth}</Moment> : "N.A"}</div>,
        "sortable": true,
        width: "150px",
      },
      {
        name: "UserName",
        label: "UserName",
        type: "string",
        sortable: true,
        cell: row => <Cell v={row.UserName} />,
      },
      {
        name: "TypeName",
        label: "TypeName",
        type: "string",
        sortable: true,
        cell: row => <Cell v={row.TypeName} />,
      },
      {
        label: "Process File",
        name: "StatusId",
        cell: row =>
          <div className="moreinfo">{row.StatusId == 1 ? <button id="processfile" className="btn btn-primary btn-sm" onClick={(e) => this.showConfirmationModal(e, row)}>
            Start Processing
          </button> : row.StatusId == 2 ? "In Process"
            : "Processed"}
          </div>,
        width: "180px"
      },
    ];

  }



  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentDidMount() {
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    const user = getuser();
    var managerid = user.UserID;
    console.log('managerid', managerid, this.props, this.props.StartDate);
    this.GetIncentiveFiles();

  }

  componentDidUpdate(prevProps) {
    debugger;
    if ((prevProps.ProductId !== this.props.ProductId) || (prevProps.TypeId !== this.props.TypeId)
      || (prevProps.IncentiveMonth !== this.props.IncentiveMonth) || (prevProps.isUpload !== this.props.isUpload)) {
      this.GetIncentiveFiles();
    }
  }

  GetIncentiveFiles() {
    this.props.GetCommonspData({
      root: 'GetIncentiveFiles',
      c: "L",
      params: [{ "ProductId": this.props.ProductId, "TypeId": this.props.TypeId, "IncentiveMonth": moment(this.props.IncentiveMonth).format("YYYY-MM-DD") }],
    }, function (result) {
      if (result.data && result.data.data[0]) {//debugger;
        console.log(result.data.data[0]);
        this.setState({ IncentiveFileData: result.data.data[0] });
      }
    }.bind(this));
  }

  hideConfirmationModal() {
    if ((this.state.ProcessInfoData && this.state.ProcessInfoData.length > 0 && this.state.confirm == "yes") ||
      this.state.confirm == "no")
      this.setState({ ConfirmationOpen: false, selectedRow: null });
  }

  showConfirmationModal(e, row) {
    //this.setState({ConfirmationOpen : true, onConfirmFunction: () => this.blockAgent(e, row)});
    console.log('rowwww', row);
    this.setState({ ConfirmationOpen: true, selectedRow: row });
  }

  handleConfirmOk(e) {
    this.setState({ confirm: "yes" });
    this.processFile(e, this.state.selectedRow)
  }

  handleProcessedFilePopup() {
    debugger;
    this.setState({ processedFileModal: false, ConfirmationOpen: false })
    this.GetIncentiveFiles();


  }

  processFile(e, row) {
    ProcessUploadIncentiveFile(row, function (results) {
      debugger;
      console.log(results);
      if (results.data && results.data.data && results.data.data.length > 0)
        this.setState({ ProcessInfoData: results.data.data, processedFileModal: true, confirm: "no" });
      else {
        toast('Some error occured', { type: 'error' });

        this.setState({ processedFileModal: false, ConfirmationOpen: false });
        this.GetIncentiveFiles();

      }
    }.bind(this));

  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }


  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }

  handleClose() {
    this.setState({ showModal: false });
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  renderModalProcessFile() {
    debugger;
    if (this.state.ProcessInfoData && this.state.ProcessInfoData.length > 0) {
      let ProcessStatusColumns = [];
      let ProcessInfoData = this.state.ProcessInfoData;

      Object.entries(ProcessInfoData[0]).map(([key, value]) => {
        if (key == 'status') {
          ProcessStatusColumns.push({
            label: "Status",
            name: "status",
            cell: row => <div className="Status">{row.status ? row.status : "N.A"}</div>,
          })

        } else if (key == 'message') {
          ProcessStatusColumns.push({
            label: "message",
            name: "message",
            cell: row => <div className="Status">{row.message ? row.message : "N.A"}</div>,
            searchable: true,

          })

        } else if (key == 'FirstPremPaid' || key == 'FLC' || key == 'IsSI' || key == 'IsE2E') {
          ProcessStatusColumns.push({
            label: key.toString(),
            name: key.toString(),
            type: "bool",
          })
        } else {
          ProcessStatusColumns.push({
            "name": key.toString(),
            "label": key.toString(),
            searchable: true,
          })
        }
      })

      const processfilecolumns = fnDatatableCol(ProcessStatusColumns);
      console.log(processfilecolumns, ProcessInfoData)
      return <DataTable
        columns={processfilecolumns}
        data={(ProcessInfoData && ProcessInfoData.length > 0) ? ProcessInfoData : []}
        striped={true}
        noHeader={true}
        highlightOnHover
        dense

      />
    }

    return null;

  }


  render() {
    const columns = this.fnDatatableCol();
    // var processfilecolumns = fnDatatableCol(this.processfilecolumns);

    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, event, ProcessInfoData, IncentiveFileData } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={8}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <Col md={12}>
                  {(
                    this.state.ConfirmationOpen && <ConfirmDialog onConfirm={(e) => this.handleConfirmOk(e)} onCancel={() => this.hideConfirmationModal()} show={this.state.ConfirmationOpen}
                      message="Do You want to process the file ?" />
                  )}
                  <CardBody>
                    <div class="inc" style={{ height: "fit-content" }}>

                      {(IncentiveFileData && IncentiveFileData.length > 0 && <DataTable
                        columns={columns}
                        data={IncentiveFileData}
                        pagination={false}
                      />)}
                    </div>

                  </CardBody>
                </Col>
              </Card>
            </Col>
          </Row>

          <Modal show={this.state.processedFileModal} onHide={this.handleProcessedFilePopup.bind(this)} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                {this.renderModalProcessFile()}


              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord,
    GetCommonspData
  }
)(ProcessIncentiveFile);