
import React from "react";
import {
  GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";

import DataTable from './Common/DataTableWithFilter';
import { fnDatatableCol, getuser } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";

import  Datetime from 'react-datetime';
import moment from 'moment';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

class FosAssignedDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      FOSData: [],
      activePage: 1,
      root: "",
      PageTitle: "Appointment History ",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      startdate: moment().format("YYYY-MM-DD"),
      enddate: moment().add(7, "days").format("YYYY-MM-DD"),
      IsLoading: true,
    };
    
    this.columnlist = [
      {
        name: "Leadid",
        label: "LeadId",
        searchable: true,
        sortable: true,
      },
      {
        name: "Custname",
        label: "Customer Name",
        type: "string",
        sortable: true
      },
      {
        name: "AppointmentSubStatus",
        label: "Appointment SubStatus",
        type: "string",
        cell: row => <div>{row.AppointmentSubStatus ? row.AppointmentSubStatus : "N.A"}</div>,
        sortable: true
      },
      {
        name: "AppointmentDateTime",
        label: "Appointment Date and Time",
        type: "datetime",
        sortable: true
      },
      {
        name: "Appointment_Created_on",
        label: "Appointment Created On",
        type: "datetime",
        width : "200px",
        sortable: true
      },
    ];

  }



  componentDidMount() {
    this.fetchFOSData();

  } 

  componentWillReceiveProps(nextProps) {

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }

  handleStartDateChange = (e, props) => {
    if (e._isAMomentObject) {
      //this.props.onStartDate(e.format("YYYY-MM-DD"));
      this.setState({ startdate: e.format("YYYY-MM-DD"), enddate: e.add(30, 'days').format("YYYY-MM-DD") }, function () {
      });
    }
  }

  handleEndDateChange = (e, props) => {
    if (e._isAMomentObject) {
      //this.props.onEndDate(e.format("YYYY-MM-DD"));
      this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
        //this.fetchCallBackData();
      });
    }
  }

  validation = (currentDate) => {
    if (currentDate.isBefore(moment().subtract(15, "days").format("YYYY-MM-DD"))) {
      return false;
    }
    return currentDate.isBefore(moment());

  };

  validationEndDate = (currentDate) => {


    if (!currentDate.isBefore(moment().add(30, "days").format("YYYY-MM-DD"))) {
      return false;
    }

    if (currentDate.isBefore(moment(this.state.startdate))) {
      return false;
    }

    return true;

  };

  fetchFOSData(){
    this.setState({ IsLoading : true})
  this.props.GetCommonspData({
    root: "GetAgentFOSLeadAssignDetails",
    c: "L",
    //params: [{ StartDate: '2021-08-01' , EndDate : '2022-01-01' , UserId: '8219'}],
    params: [{ StartDate: this.state.startdate , EndDate : this.state.enddate , UserId: getuser().UserID}],

  }, function (result) {debugger;
        this.setState({ IsLoading : false});
      if (result && result.data && result.data.data ) {
        this.setState({ FOSData: result.data.data[0]});
      }
     
    }.bind(this)
  
  )
  }

  render() {
    const columns = this.fnDatatableCol();
    const {  PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, FOSData } = this.state;

    return (
      <>
        <div className="content">
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={2}>
                    <Form.Label>Start Date </Form.Label>

                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.startdate}
                        isValidDate={this.validation}
                        onChange={moment => this.handleStartDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    <Col md={2}>
                    <Form.Label>End Date </Form.Label>

                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.enddate}
                        isValidDate={this.validationEndDate}
                        onChange={moment => this.handleEndDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    <Col md={1}>
                  <Form.Label>Fetch </Form.Label>
                      <Button variant="primary" onClick={() => this.fetchFOSData()}>Fetch</Button>
                  </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  {this.state.IsLoading ? <i class="fa fa-spinner fa-spin"></i> : ''}
                  <DataTable
                    columns={columns}
                    data={(FOSData && FOSData.length > 0) ? FOSData : []}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}
function mapStateToProps(state) {
    return {
      CommonData: state.CommonData
    };
  }

export default connect(
  mapStateToProps,
  {
    GetCommonspData
  }
)(FosAssignedDetails);