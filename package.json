{"name": "matrixdashboard", "version": "2.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "local:qa": "set NODE_ENV=qa&& node app.js", "local:prod": "set NODE_ENV=prod&& node app.js", "dev:qa": "set NODE_ENV=qa&& nodemon app.js", "dev:prod": "set NODE_ENV=prod&& nodemon app.js"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"athena-express": "^7.1.5", "aws-sdk": "^2.680.0", "axios": "^1.4.0", "bcrypt": "^5.1.1", "compression": "^1.7.4", "content-security-policy": "^0.3.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dont-sniff-mimetype": "^1.1.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "feature-policy": "^0.6.0", "hive-driver": "^0.2.0", "joi": "^17.9.2", "json-rules-engine": "^6.3.1", "jsontoxml": "^1.0.1", "kafkajs": "^2.2.4", "mathjs": "^9.4.4", "memory-cache": "^0.2.0", "moment": "^2.29.4", "mongodb": "^3.1.13", "mssql": "^9.1.1", "mysql": "^2.18.1", "newrelic": "^11.5.0", "nodemailer": "^6.9.4", "read-excel-file": "^5.6.1", "referrer-policy": "^1.2.0", "request": "^2.88.0", "socket.io": "^4.7.5", "strict-transport-security": "^0.2.2", "underscore": "^1.13.6", "x-xss-protection": "^2.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^2.0.20"}}