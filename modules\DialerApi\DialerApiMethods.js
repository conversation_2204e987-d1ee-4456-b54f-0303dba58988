const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");
const { createLog } = require('../../auth');

exports.setAgentStatus = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "setAgentStatus.php"
    const body = {
        emp_id: req.query.agentCode,
        requested_by: req.query.managerid,
        leadid: req.query.bookingid,
        grade: req.query.grade,
        status: "blocked"
    }
    // console.log("setAgentStatus", url);
    let response = await axios.get(url, {params:body});
    //console.log("setAgentStatus", response);
    try {
        
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

exports.multi_conference = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "multi_conference.php"
    const body = req.query;
    try {
        let response = await axios.get(url, { params: body });
        createLog(
            body?.agent || '',
            'multi_conference',
            url,
            JSON.stringify(body),
            JSON.stringify(response?.data || {}),
            '',
            ''
        );
        return res.send(response.data);
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.mob2mobcall = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "mob2mobcall.php"
    const body = req.query;

    // console.log("mob2mobcall", url);
    let response = await axios.get(url, {params:body});
    //console.log("mob2mobcall", response);
    try {
        
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

//https://dialerapi.policybazaar.com/api/v2/Dialer/getQueuesValue?type=sales&product=4


exports.getQueuesValue = async function (req, res) {
    const url = conf.dialerApiUrlV2 + "Dialer/getQueuesValue"
    const body = req.query;
    const config = {
        params:body,
        headers:{
            clientKey:conf.Dialer_clientKey,
            source:conf.Dialer_source,
            authKey:conf.Dialer_authKey
        }
      };
    // console.log("getQueuesValue", url);
    //let response = await axios.get(url, {params:body});
    //console.log("mob2mobcall", response);
    try {
        let response = await axios.get(url, config);
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}


//https://dialerapi.policybazaar.com/api/v2/Dialer/getQueues?type=sales&product=4&queuename=bdayunansweredpd,bajajunansweredpd


exports.getQueues = async function (req, res) {
    const url = conf.dialerApiUrlV2 + "Dialer/getQueues"
    const body = req.query;
    const config = {
        params:body,
        headers:{
            clientKey:conf.Dialer_clientKey,
            source:conf.Dialer_source,
            authKey:conf.Dialer_authKey
        }
      };
    // console.log("getQueues", url);
    
    //console.log("mob2mobcall", response);
    try {
        let response = await axios.get(url, config);
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

//https://dialerapi.policybazaar.com/api/v2/Dialer/getQueuesByIvrProduct?type=sales&product=4,2,1



exports.getQueuesByIvrProduct = async function (req, res) {
    const url = conf.dialerApiUrlV2 + "Dialer/getQueuesByIvrProduct"
    const body = req.query;
    const config = {
        params:body,
        headers:{
            clientKey:conf.Dialer_clientKey,
            source:conf.Dialer_source,
            authKey:conf.Dialer_authKey
        }
      };
    // console.log("getQueuesByIvrProduct", url, config);
    try {
        let response = await axios.get(url, config);
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}

// https://dialerapi.policybazaar.com/api/v2/dialer/unansweredCallList

// {
//  "queuename":"termretaineribmobile,web",
//  "date":"2022-07-18"
// }


exports.unansweredCallList = async function (req, res) {
    const url = conf.dialerApiUrlV2 + "Dialer/unansweredCallList"
    const body = req.body;
    const config = {
        headers:{
            clientKey:conf.Dialer_clientKey,
            source:conf.Dialer_source,
            authKey:conf.Dialer_authKey
        }
      };
    // console.log("unansweredCallList", url);
    
    //console.log("mob2mobcall", response);
    try {
        let response = await axios.post(url, {params:body}, config);
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}


// POST API
// https://dialerapi.policybazaar.com/api/v2/dialer/answeredCallList

// {
//  "queuename":"termretaineribmobile,web",
//  "date":"2022-07-18"
// }

exports.answeredCallList = async function (req, res) {
    const url = conf.dialerApiUrlV2 + "Dialer/answeredCallList"
    const body = req.body;
    const config = {
        headers:{
            clientKey:conf.Dialer_clientKey,
            source:conf.Dialer_source,
            authKey:conf.Dialer_authKey
        }
      };
    // console.log("answeredCallList", url);
    //let response = await axios.post(url, {params:body});
    //console.log("mob2mobcall", response);
    try {
        let response = await axios.post(url, {params:body}, config);
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}