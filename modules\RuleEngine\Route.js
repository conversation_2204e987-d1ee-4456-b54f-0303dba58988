var express = require("express");
const router = express.Router();

const controller = require("./Controller");
const Incentive = require("./Incentive");

router.get("/getRules/", controller.getRules);
router.get("/getRulebyId/", controller.getRulebyId);
router.post("/SaveCondition", controller.SaveCondition);
router.post("/SaveRule", controller.SaveRule);
router.post("/factCheck", controller.factCheck);
router.post("/resolveRules", controller.resolveRules);
router.get("/facts", controller.getFacts);
router.post("/addRule", controller.addRule);
router.patch("/editRule/:id", controller.editRule);
router.patch("/editRuleDetails/:id", controller.editRuleDetails);
router.get("/getRuleParams", controller.getRuleParams);
router.get("/getFactsData", controller.getFactsData);
router.get("/agentBookings/*", Incentive.getAgentBookings);
router.get("/booking/*", Incentive.getBooking);
router.get("/bookingIncentive/*", Incentive.CalculateBookingIncentive);
router.get("/AgentIncentive/*", Incentive.CalculateAgentIncentive);
router.post("/toggleFactIsActive/", controller.toggleFactIsActive);
router.get("/AgentCTC/*", Incentive.getAgentCTC);
router.get("/AgentDetails/*", Incentive.getAgentDetails);
router.post("/evaluateRule", controller.evaluateRule);

module.exports = router;