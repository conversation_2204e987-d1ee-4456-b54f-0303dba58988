
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, GetCommonspData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import { fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'
import moment from 'moment';
import Loader from './Common/Loader';


class SuperGroupCriteriaHtml extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "SuperGroupCriteriaHtml",
      PageTitle: "SuperGroupCriteriaHtml",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      ProId: '',
      agentranklist: '',
      groupnamelist: '',
      startdate: moment().format("YYYY-MM-DD"),
      IncentiveLoader: false,
      InsertLoader: false,
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);
    //this.GroupNamelistchange = this.GroupNamelistchange.bind(this);
    //this.agentranklistchange = this.agentranklistchange.bind(this);
    this.selectedrow = { "Id": 0, "ProductID": null, "Validfrom": new Date(), "IsActive": true }
    this.columnlist = [
      {
        name: "ProductId",
        label: "Product",
        type: "dropdown",
        config: {
          root: "Products",
          cols: ["ID AS Id", "ProductName AS Display"],
          con: [{ "Isactive": 1 }]
        },
        searchable: true,
        editable: false,
        required: true,
      },
      {
        name: "Id",
        label: "Id",
        type: "hidden",
        hide: true,
      },

     


      {
        name: "CriteriaMonth",
        label: "CriteriaMonth",
        type: "datetime",
        editable: false,
        timeFormat: false,
      },
      {
        name: "IsActive",
        label: "IsActive",
        type: "bool"
      },
      {
        name: "CreatedOn",
        label: "CreatedOn",
        type: "datetime",
        hide: true,
      },

      {
        name: "CriteriaHtml",
        label: "CriteriaHtml",
        type: "textarea"
      },

    ];
    let count = 0;
    this.ProductList = {
      config:
      {
        root: "Products",
        cols: ["ID AS Id", "ProductName AS Display"],
        con: [{ "Isactive": 1 }],
        //state: false
      }
    };
    // this.AgentRankList = {
    //   config:
    //   {
    //     root: "LeadAgentRankMapping",
    //     cols: ['DISTINCT AgentRank AS Id', 'AgentRank AS Display'],
    //     statename: "agentrankmapping",
    //     state: true
    //   }
    // };
    // this.GroupNameList = {
    //   config:
    //   {
    //     root: "vwUserGroup",
    //     cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
    //     statenema: "usergroupmapping",
    //     state: true
    //   }
    // };
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    // this.props.GetCommonData({
    //   limit: 10,
    //   skip: 0,
    //   root: this.state.root,
    //   cols: GetJsonToArray(this.columnlist, "name"),
    //   c: "L", // L for live connection, R for Replica
    // });
    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        debugger;
        items = joinObject(this.state.IncentiveData, nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200) {
        //alert(nextProps.CommonData.InsertSuccessData.error);
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      }
      else {
        //this.setState({ showModal: false });
        //this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
      }
    }

  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
  handleCopy(row) {
    this.setState({ formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  CheckLoader(action) {
    if (action == 'IncentiveFetch') {
      if (this.state.IncentiveLoader)
        return <Loader />;
    } else if (action == 'IncentiveInsert') {
      if (this.state.InsertLoader)
        return <Loader />;
    }

  }

  async handleSave() {
    if (document.getElementsByName("frmSuperGroupCriteriaHtml").length > 0 &&
      document.getElementsByName("frmSuperGroupCriteriaHtml")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      console.log('formvalue/////////////////', formvalue)

      let id = formvalue["Id"];
      delete formvalue["Id"]
      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);
       

        await this.props.UpdateData({
          root: 'SuperGroupCriteriaHtml',
          body: formvalue,
          querydata: { "Id": id },
          c: "L",
        }, function (result) {
          if (result.data) {//debugger;
            console.log(result.data);
            alert(result.data.message);
          }
        }.bind(this));
        let by = getuser().UserID;
        this.props.addRecord({
          root: "History",
          body: {
            module: "SuperGroupCriteriaHtml",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: by
          }
        });
      } else if (this.state.event == "Copy") {
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        formvalue["CreatedOn"] = new Date();
        this.fnCleanData(formvalue);
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
      } else {
        // if (formvalue['MaxRange'] < formvalue['MinRange']) {
        //   alert('MinRange should be less than MaxRange')
        //   return false;
        // }
        // this.setState({ 'InsertLoader': true })
        // await this.props.GetCommonspData({
        //   root: 'SuperGroupCriteriaHtml',
        //   c: "L",
        //   params: [{
        //     "SuperGroupID": formvalue['SuperGroupID'], "SuperGroupTypeId": formvalue['SuperGroupTypeId'],
        //     "IncentiveMonth": moment(formvalue['Validfrom']).format("YYYY-MM-DD"), "ProductId": formvalue['ProductID'], "MinRange": formvalue['MinRange']
        //     , "MaxRange": formvalue['MaxRange'], "IncValue": formvalue['Value'], "IsActive": formvalue['IsActive'], "userId": getuser().UserID,
        //   }],
        // }, function (result) {
        //   if (result.data) {//debugger;
        //     console.log(result.data);
        //     alert(result.data.message);
        //     this.setState({ InsertLoader: false });
        //   }
        // }.bind(this));
      }
      let productid = formvalue["ProductID"];

      setTimeout(function () {

        if (productid) {
          this.fetchIncentiveData();
        }
      }.bind(this), 2000);
      this.setState({ showModal: false });
    }
    return false;
  }
  handleChange = (e, props) => {
    debugger;
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    if (e.type == 'editor') {
      formvalue[e.id] = e.html;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }
  productchange(e, props) {
    this.setState({ ProId: e.target.value });
  }

  fnRenderfrmControl(col, formvalue, handleChange, event) {
    return fnRenderfrmControl(col, formvalue, handleChange, event)

  }

  fetchIncentiveData() {
    this.setState({ 'IncentiveLoader': true });
    debugger;
    this.props.GetCommonData({
      root: 'SuperGroupCriteriaHtml',
      c: "L",
      order: 1,
      con: [{ "ProductId": this.state.ProId, "CriteriaMonth": this.state.startdate }],
    }, function (result) {
      this.setState({ 'IncentiveLoader': false });
      if (result.data && result.data.data[0]) {//debugger;
        console.log(result.data.data[0]);
        this.setState({ IncentiveData: result.data.data[0] });
      }
    }.bind(this));
    console.log('///////////', this.state.user)
  }

  validation = (currentDate) => {
    return currentDate.isBefore(moment());
  };

  handleStartDateChange = (e, props) => {
    if (e._isAMomentObject) {
      //this.props.onStartDate(e.format("YYYY-MM-DD"));
      this.setState({ startdate: e.format("YYYY-MM-DD"), enddate: e.add(60, 'days').format("YYYY-MM-DD") }, function () {
      });
    }
  }

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event, IncentiveData } = this.state;
    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={4}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="product_dropdown">
                        <DropDown firstoption="Select Product" col={this.ProductList} onChange={this.productchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.startdate}
                        isValidDate={this.validation}
                        onChange={moment => this.handleStartDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={() => this.fetchIncentiveData()}>Fetch {this.CheckLoader('IncentiveFetch')}</Button>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={IncentiveData}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmSuperGroupCriteriaHtml">
                <Row>
                  {this.columnlist.map(col => (
                    this.fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value={"Save Changes", this.CheckLoader('IncentiveInsert')} className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord,
    GetCommonspData
  }
)(SuperGroupCriteriaHtml);