import React, { Component } from 'react';

import { <PERSON>ton<PERSON>roup, Button, Modal, Form } from 'react-bootstrap';
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";

import OtpInput from "react-otp-input";
import { If, Then, Else } from 'react-if';


class OtpDetails extends Component{

    constructor(props) {
        super(props);
        this.state = {
            bgImg: '/Graphic.svg' ,
            advImg: '/Logo.svg' ,
            enterSmallImg:'/enterSmall.svg',
            verifybtniconImg:'/verifybtnicon.svg',
    }
    }

    componentDidMount(){debugger;
        setTimeout(function () {
            document.getElementById('central-login-module-content').classList.add('central-login-module-content-popup-close-nd');
            document.getElementById('central-login-module-trigger').click()
        }.bind(this), 2000);

    }

    renderButtonCallLog(){

            return <div className="seperateBox"><Col md={3} xs={3} lg={2} className="text-center"><span><img src={this.state.enterSmallImg}/> </span> </Col>
            <Col md={9} xs={9} lg={10}><Button variant="primary" id="central-login-module-trigger" className="clHistoryBtn">Sign In</Button>
            <p>Sign in to know more about your advisor</p></Col></div>

        
    }

    render(){

        const { bgImg, advImg, enterImg } = this.props
        return(
            <Row>
            <Col md={6} xs={12}> <div className="verifybgImg"><img src={this.state.bgImg}/>  </div>  </Col>
            <Col md={6} xs={12}>
            <Row>                                                                       
        <Col md={12} xs={12} lg={9} className=" mt-2">
        <Form.Text className="text-muted">
        <h2 class="otp-title">Know your advisor</h2>  
       
        <p class="otp-caption">Wish to learn more about your Insurance Advisor from Policybazaar? </p>
        {/* <p><a onClick={this.props.howitworks.bind(this)} className="knowMore">Know More </a></p>   */}

        </Form.Text> 
        </Col> </Row>  
        {}
         <Row >
         <Col  md={12} xs={12} lg={9}>
        <div className="clHistoryCheck mt-4">
        <Row>
        
         
            {this.renderButtonCallLog()}
            
        
            </Row>
            {}
            </div>

        </Col>

            </Row>
            </Col>
            </Row>

        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default OtpDetails;