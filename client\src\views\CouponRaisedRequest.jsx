import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { GetCouponRaiseRequest, UpdateCouponRaiseRequest,GetCouponDataByLeadId } from "../store/actions/CommonAction";
import { Button, Form } from 'react-bootstrap';
import DropDown from "./Common/DropDown";

import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


const CouponRaisedRequest = () => {

  const SelectList =
    [{ Id: "1", Display: "LeadId" }, { Id: "2", Display: "Employee Code" }]

  let [SelectSearch, setSelectSearch] = useState(0);
  let [Value, setValue] = useState(0);
  let [ConsolidatedData, setConsolidatedData] = useState([]);
  let [FilteredData, setFilteredData] = useState([]);
  let [IsFilerted, setIsFilerted] = useState(false);

  const SearchChange = (e) => {
    setSelectSearch(e.target.value)
    if (e.target.value == 0) {
      setIsFilerted(false);
    }
  }

  const CouponRaiseRequest = () => {
    GetCouponRaiseRequest(function (resultData) {
      try {
        if (resultData != null) {
          if (resultData) {
            setConsolidatedData(resultData.data.data);
          }
        }
        else {
          setConsolidatedData([]);
        }
      } catch (e) {
        toast(`${e}`, { type: 'error' });
      }
    })
  }

  const ReleaseCouponRaiseRequest = (LeadId, CustomerId) => {
    if (CustomerId > 0) {
      var reqData =
      {
        "CustomerId": parseInt(CustomerId),
        "LeadId": parseInt(LeadId)
      };
      UpdateCouponRaiseRequest(reqData, function (resultData) {
        try {
          if (resultData != null) {
            if (resultData.data.data == true) {
              window.alert("Lead Released Succeessfully!");
              CouponRaiseRequest();
              if (SelectSearch == 1) {
                document.getElementById("LeadId").value = '';
                setIsFilerted(false);
                setValue(0);
              }
            }
          }
        } catch (e) {
          toast(`${e}`, { type: 'error' });
        }
      })

    }
  }

  const SearchFilter = () => {
    setIsFilerted(true);
    if (SelectSearch == 1) {
      if(Value > 0){
        GetCouponDataByLeadId(Value, function (resultData) {
          try {
            if (resultData && resultData.data && resultData.data.data && Object.keys(resultData.data.data).length > 0) {
              setFilteredData([resultData.data.data]);
            }
            else {
              setFilteredData([]);
            }
          } catch (e) {
            toast(`${e}`, { type: 'error' });
          }
        })
      }
      else{
        window.alert("Enter Valid Leadid");
        setIsFilerted(false);
      }
    }
    else if (SelectSearch == 2) {
      setFilteredData(ConsolidatedData.filter((data) => data.EmpCode === Value))
    }
  };

  useEffect(() => {
    CouponRaiseRequest()
  }, []);

  useEffect(() => {
    if (SelectSearch == 2) {
      SearchFilter()
    }
  }, [ConsolidatedData])

  return (
    <div className="content">
      <Row>
        <Col md="12">
          <Card>
            <CardHeader>
              <Row>
                <Col md={11}>
                  <CardTitle tag="h4">Release Coupon Requests</CardTitle>
                </Col>
                <Col md={1}>

                </Col>
              </Row><Row>
                <Col md={4}>
                  <DropDown firstoptionvalue={true} items={SelectList} onChange={SearchChange}>
                  </DropDown><br></br>
                </Col>
                {SelectSearch == 1 && <Col md={2}>
                  <Form.Control
                    type="text"
                    name="LeadId" className="empid" id="LeadId"
                    placeholder={"Enter LeadId"}
                    onChange={(e) => { setValue(parseInt(e.target.value)) }}
                  />
                </Col>}
                {SelectSearch == 2 && <Col md={2}>
                  <Form.Control
                    type="text"
                    name="EmpCode" className="empid" id="EmpCode"
                    placeholder={"Enter Employee Code"}
                    onChange={(e) => { setValue(e.target.value) }}
                  />
                </Col>}
                {(SelectSearch == 1 || SelectSearch == 2) && <Col md={1}>
                  <Button variant="primary" onClick={() => { SearchFilter(); }}>
                    Fetch
                  </Button>
                </Col>}
              </Row>
            </CardHeader>
            <CardBody>
              <div className="t1">
                {(IsFilerted == true ? FilteredData && FilteredData.length > 0 : ConsolidatedData && ConsolidatedData.length > 0) ?
                  <table className="table">
                    <thead>
                      <tr>
                        <th>S No</th>
                        <th>LeadId</th>
                        <th>ParentId</th>
                        <th>Customer Name</th>
                        <th>Assigned to User</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {IsFilerted == true ?
                        FilteredData && FilteredData.length > 0 ?
                          FilteredData.map((data, index) => (
                            <tr>
                              <td>{index + 1}</td>
                              <td>{data.LeadId > 0 ? data.LeadId : 0}</td>
                              <td>{data.ParentId > 0 ? data.ParentId : 0}</td>
                              <td>{data.CustomerName ? data.CustomerName : ""}</td>
                              <td><span>{data.UserName ? data.UserName : ""} </span><span>({data.EmpCode ? data.EmpCode : ""})</span></td>
                              <th><Button variant="danger" onClick={() => { ReleaseCouponRaiseRequest(data.LeadId, data.CustomerId) }}>Release</Button></th>
                            </tr>
                          ))
                          : <h4>No data Found!</h4>
                        :
                        ConsolidatedData && ConsolidatedData.length > 0 ?
                          ConsolidatedData.map((data, index) => (
                            <tr>
                              <td>{index + 1}</td>
                              <td>{data.LeadId > 0 ? data.LeadId : 0}</td>
                              <td>{data.ParentId > 0 ? data.ParentId : 0}</td>
                              <td>{data.CustomerName ? data.CustomerName : ""}</td>
                              <td><span>{data.UserName ? data.UserName : ""} </span><span>({data.EmpCode ? data.EmpCode : ""})</span></td>
                              <th><Button variant="danger" onClick={() => { ReleaseCouponRaiseRequest(data.LeadId, data.CustomerId) }}>Release</Button></th>
                            </tr>
                          ))
                          : <h4>No data Found!</h4>
                      }
                    </tbody>
                  </table>
                  :
                  <h4>No data Found!</h4>}
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row>
    </div>

  )
}

export default CouponRaisedRequest;