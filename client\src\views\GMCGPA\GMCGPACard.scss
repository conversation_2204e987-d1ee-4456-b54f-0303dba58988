.GMCGPACardSection {
    background-color: #f5f5f5;
    margin-top: 0px !important;
    padding: 0px !important;
    overflow: hidden;
    .content {
        padding: 0px;
    }
    .profile {
        //background-image:url("../../../public/GMC/profile_img.png");
        text-align: center;
        background-color: #fff;
        padding-bottom: 1px;
        background-repeat: no-repeat;
        img {
            border: 1px dashed #2a63f6;
            border-radius: 50px;
            padding: 3px;
            margin-top: 25px;
            width: 96px;
        }
        h4 {
            text-align: center;
            font: normal normal 600 18px/18px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            margin-top: 12px;
        }
        p {
            text-align: center;
            font: normal normal 600 16px/18px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 0.6;
        }
        ul {
            list-style-type: none;
            display: flex;
            justify-content: space-between;
            padding: 0px 20px;
            li {
                text-align: center;
                font: normal normal 600 18px/18px Roboto;
                letter-spacing: 0px;
                color: #000000;
                opacity: 1;
                p {
                    text-align: center;
                    font: normal normal normal 14px/18px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 1;
                    margin-bottom: 0px;
                    margin-top: 5px;
                }
            }
        }
    }
    .GMCcardView {
        background-color: #f5f5f5;
        padding: 15px 10px 10px 10px;
        span {
            text-align: left;
            font: normal normal 600 14px/20px Roboto;
            letter-spacing: 0px;
            color: #000000;
        }
        p {
            text-align: left;
            font: normal normal 600 18px/24px Roboto;
            letter-spacing: 0px;
            color: #000000;
            word-break: break-word;
            margin: 10px 0px 10px 0px;
        }
        .GMCCard{
            margin-bottom: 15px;
        }
        .AccidentInsurCard {
            background: linear-gradient(118deg, #6b9d6d 0%, #46887c 100%),
                url("../../../public/GMC/profilebanner.png");
            height: auto;
            box-shadow: 0px 3px 20px #00000029;
            background-blend-mode: multiply;
            border-radius: 12px;
            opacity: 1;
            padding: 15px 0px 0px;
            min-height: 220px;
            position: relative;
            ul {
                display: flex;
                list-style-type: none;
                flex-wrap: wrap;
                justify-content: flex-start;
                width: 100%;
                padding-left: 0px;
                margin-bottom: 0px;
                li {
                    text-align: left;
                    font: normal normal normal 10px/16px Roboto;
                    letter-spacing: 0px;
                    color: #ffffff;
                    opacity: 1;
                    width: calc(100% - 66.7%);
                    word-break: break-word;
                    padding: 0px 12px;
                    h4 {
                        text-align: left;
                        font: normal normal 500 11px/24px Roboto;
                        letter-spacing: 0px;
                        color: #ffffff;
                        opacity: 1;
                        margin-bottom: 3px;
                    }
                }
            }
            .brandlogo {
                float: right;
                margin-right: 12px;
                position: absolute;
                right: 5px;
            }
            .logoProfileName {
                padding: 0px 12px;
                width: 100%;
                float: left;
                margin-bottom: 15px;

                h4 {
                    text-align: left;
                    font: normal normal 600 20px/24px Roboto;
                    letter-spacing: 0.7px;
                    color: #ffffff;
                    opacity: 1;
                    margin-bottom: 0px;
                    p {
                        text-align: left;
                        font: normal normal 500 14px/15px Roboto;
                        letter-spacing: 0.7px;
                        color: #ffffff;
                        opacity: 1;
                        margin-bottom: 0px;
                        margin-top: 7px;
                    }
                }
                span {
                    text-align: left;
                    font: normal normal normal 12px/24px Roboto;
                    letter-spacing: 0px;
                    color: #ffffff;
                }
                .groupname {
                    text-align: left;
                    font: normal normal normal 11px/12px Roboto;
                    letter-spacing: 0px;
                    color: #ffffff;
                    opacity: 1;
                    margin-top: 15px;
                    margin-bottom: 0px;
                    h4 {
                        text-align: left;
                        font: normal normal 500 13px/24px Roboto;
                        letter-spacing: 0px;
                        color: #ffffff;
                        opacity: 1;
                    }
                }
            }
            .FooterCard {
                background: #598367 0% 0% no-repeat padding-box;
                border-radius: 0px 0px 12px 12px;
                opacity: 1;
                padding-top: 10px;
                ul {
                    display: flex;
                    width: 100%;
                    justify-content: space-between;

                    li {
                        text-align: left;
                        font: normal normal normal 10px/12px Roboto;
                        letter-spacing: 0px;
                        color: #ffffff;
                        opacity: 1;
                        width: 70%;
                        padding: 0px 12px 0px 12px;
                        &:last-child {
                            width: 30%;
                            padding: 0px 15px 0px 12px;
                            text-align: right;
                            h4 {
                                text-align: right;
                            }
                        }
                        h4 {
                            text-align: left;
                            font: normal normal 600 10px/24px Roboto;
                            letter-spacing: 0.4px;
                            color: #ffffff;
                            opacity: 1;
                        }
                    }
                }
            }
            .errorMessage p{
                
                width: 100%;
                color: #ffffff;
                padding: 10px 20px;
                font: normal normal 500 18px/24px Roboto;
                word-break: break-word;
            }
        }
        .GroupHealthCard {
            background: linear-gradient(124deg, #756b9d 0%, #467988 100%),
                url("../../../public/GMC/profilebanner.png");
                

            .FooterCard {
                background: #4f5d7e 0% 0% no-repeat padding-box;
                border-radius: 0px 0px 12px 12px;
                opacity: 1;
            }
        }
        .HealthdownloadPolicy {
            display: flex;
            background: #ffffff 0% 0% no-repeat padding-box;
            box-shadow: 0px 3px 20px #00000029;
            border: 1px solid #726b97;
            border-radius: 0px 0px 12px 12px;
            opacity: 1;
            width: 88%;
            padding: 5px;
            margin: auto;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            p {
                text-align: left;
                font: normal normal 600 14px/18px Roboto;
                letter-spacing: 0px;
                color: #736b9a;
                opacity: 1;
                margin-left: 12px;
                margin-bottom: 0px;
            }
        }
        .downloadPolicy{
            width: 100%;
            float: left;
            height: 45px;
            position: relative;
        .downloadPolicybtn {
            display: flex;
            background: #ffffff 0% 0% no-repeat padding-box;
            box-shadow: 0px 3px 20px #00000029;
            border: 1px solid #5b897a;
            border-radius: 0px 0px 12px 12px;
            opacity: 1;
            width: 300px;
            padding: 5px;
            margin: auto;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            margin-bottom: 10px;
            p {
                text-align: left;
                font: normal normal 600 14px/18px Roboto;
                letter-spacing: 0px;
                color: #5c9d4b;
                opacity: 1;                
                margin: 0px 0px 0px 12px;
            }
        }
    }
    }
}
.footer {
    display: none;
}

@media screen and (max-width: 480px) {
    .GMCcardView {
        padding: 10px;
        .AccidentInsurCard {
            
            ul {
                li {
                    h4 {
                        font: normal normal 500 12px/24px Roboto !important;
                    }
                }
            }
            .FooterCard {
                ul {
                    li {
                        padding: 0px 8px 0px 15px !important;
                        h4 {
                            font: normal normal 500 10px/24px Roboto !important;
                        }
                        &:last-child {
                            padding: 0px 15px 0px 10px !important;
                        }
                    }
                }
            }
            .logoProfileName {
                width: 72% !important;
            }
        }
    }
}
