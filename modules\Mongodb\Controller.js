const tblList = require("../constants");
let fs = require('fs');
const path = require("path");
const bcrypt = require('bcrypt');
const crypto = require("crypto");
const HEALTH_DEPARTMENTS = ["Health", "Health_WA_Facebook", "Health_Regional", "Health_Renewal", "Health_Retention", "HealthRenewal"];
const MOTOR_DEPARTMENTS = ["NewCar", "NewCar_WA", "NewCar_Renewal", "Retainers"];
const userMadetoryFeilds = [{
  type: 'string',
  key: 'name',
}, {
  type: 'string',
  key: 'username',
}, {
  type: 'string',
  key: 'domainusername',
},
{
  type: 'string',
  key: 'emails',
},
{
  type: 'string',
  key: 'password',
},
{
  type: 'array',
  key: 'department',
},
{
  type: 'array',
  key: 'role',
},
{
  type: 'boolean',
  key: 'verified'
}
]
const userMadetoryFeildsforUpdate = [{
  type: 'string',
  key: 'name',
}, {
  type: 'string',
  key: 'username',
},
{
  type: 'string',
  key: 'emails',
},
{
  type: 'array',
  key: 'department',
},
{
  type: 'array',
  key: 'role',
},
{
  type: 'boolean',
  key: 'verified'
}
]

const { ObjectId } = require('mongodb');

async function getdata(req, res) {
  try { 
    // console.log("mongo get data")
    // console.log(req.query);
    var endpoint = req.body.root;
    var c = req.body.c ? req.body.c : "R";
    var query = req.body.con || {}
    //var cols = req.body.cols ?? JSON.parse(req.body.cols);

    
    var db = commondb;
    //var db;
    if (c === "M") {
      db = matrixdb;
    } else  if (c === "L") {
      // db = loggerdb;
      // console.log('endpoint log', c)
      if(endpoint === 'FileProcessingErrorLogs') {
        db = matrixdb;
      }
    } else if (c === "FL") {
      db = loggerdb;
    } else if(c === "MATRIX_DASHBOARD_CLIENT") {
      db = matrixdashboarddb;
    }
    //console.log(endpoint,c);
    if (!tblList[endpoint]) {
      return res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }

    let data = await db.collection(tblList[endpoint])
      .find(query)
      .toArray();
    return res.send({
      status: 200,
      data: data,
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    return res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {
    //await sql.close();
  }
}

async function getCount(req, res) {
  try {
    // console.log("-------------")
    // console.log(req.body);
    var endpoint = req.body.root;
    var c = req.body.c ? req.body.c : "R";
    var query = req.body.con || {};
    //var cols = req.body.cols ?? JSON.parse(req.body.cols);

    var db = commondb;
    if (c === "M") {
      db = matrixdb;
    }
    if (c === "L") {
       db = loggerdb;
    }
    if (c === "FL") {
       db = loggerdb;
    }
    //console.log(endpoint,c);
    if (!tblList[endpoint]) {
      return res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }

    let data = await db.collection(tblList[endpoint])
      .count(query)
    return res.send({
      status: 200,
      data: data,
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
  finally {
    //await sql.close();
  }
}

async function update(req, res) {
  try {
    let endpoint = req.body.data.root;
    let inputdata = req.body.data.body;
    let querydata = req.body.data.querydata;
    var c = req.body.data.c ? req.body.data.c : "R";
    var unset = req.body.data.unset;
    var db = commondb;
    if (c === "M") {
      db = matrixdb;
    }
    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }
    if (endpoint === 'users') {

      inputdata = filterMultiselectValues(inputdata);
      const validateDataRes = validateData(inputdata, userMadetoryFeildsforUpdate);
      if (validateDataRes.isValid == false) {
        return res.send({
          status: 400,
          message: validateDataRes.messege
        });
      }
      const depArray = inputdata.department
      const department_db = await commondb.collection('rocketchat_livechat_department_agents').find({ 'username': inputdata.username }).toArray();
      const departmentname_db = department_db.map((e) => {
        return e.departmentId;
      })
      const previous_department = await commondb.collection('rocketchat_livechat_department').find({ '_id': { $in: departmentname_db } }).toArray()
      const pre_depname = previous_department.map((e) => { return e.name });
      const depname = await commondb.collection('rocketchat_livechat_department').find({ '_id': { $in: depArray } }).toArray()
      let dep = depname.map((e) => {
        return e.name;
      });
      const commondepartment = getArraysIntersection(pre_depname, dep);
      let currentdep = dep
      const deleted_department = pre_depname.filter(n => !commondepartment.includes(n))
      const deletedhealthdep = getArraysIntersection(deleted_department, HEALTH_DEPARTMENTS);
      const prsenthealthdep = getArraysIntersection(commondepartment, HEALTH_DEPARTMENTS);
      if (deletedhealthdep.length > 0 && prsenthealthdep.length == 0) {
        await healthdb.collection("users").deleteOne({ "username": inputdata.username });
        await healthdb.collection("rocketchat_livechat_department_agents").deleteMany({ "username": inputdata.username })
      }
      const deletedmotordep = getArraysIntersection(deleted_department, MOTOR_DEPARTMENTS);
      const prsentmotordep = getArraysIntersection(commondepartment, HEALTH_DEPARTMENTS);
      if (deletedmotordep.length > 0 && prsentmotordep.length == 0) {
        await cardb.collection("users").deleteOne({ "username": inputdata.username });
        await cardb.collection("rocketchat_livechat_department_agents").deleteMany({ "username": inputdata.username })
      }



      currentdep = currentdep.filter(n => !commondepartment.includes(n))
      const promiseArray = [];
      const healthdep = getArraysIntersection(dep, HEALTH_DEPARTMENTS);
      if (healthdep.length > 0) {

        promiseArray.push(updateUser(healthdb, inputdata, querydata, "health"));
      }

      dep = dep.filter(n => !healthdep.includes(n))
      const motordep = getArraysIntersection(dep, MOTOR_DEPARTMENTS);
      if (motordep.length > 0) {

        promiseArray.push(updateUser(cardb, inputdata, querydata, "motor"));
      }

      dep = dep.filter(n => !motordep.includes(n))
      if (dep) {
        promiseArray.push(updateUser(commondb, inputdata, querydata, "common"));
      }
      const promisUpdateResult = await Promise.all(promiseArray);
      let apiUpdateResponse = {};
      promisUpdateResult.forEach(e => {
        if (e.department == 'common') {
          apiUpdateResponse['commonUpdate_response'] = e;
        }
        if (e.department == 'health') {
          apiUpdateResponse['healthUpdate_response'] = e;
        }
        if (e.department == 'motor') {
          apiUpdateResponse['motorUpdate_response'] = e;
        }
      })

      const healthinsertdep = getArraysIntersection(currentdep, HEALTH_DEPARTMENTS);
      if (healthinsertdep.length > 0) {
        await updateDepartment_userInsertion(healthdb, inputdata);
      }
      currentdep = currentdep.filter(n => !healthinsertdep.includes(n))
      const motorinsertdep = getArraysIntersection(currentdep, MOTOR_DEPARTMENTS);
      if (motorinsertdep.length > 0) {
        await updateDepartment_userInsertion(cardb, inputdata);
      }
      currentdep = currentdep.filter(n => !motorinsertdep.includes(n))
      if (currentdep.length > 0) {
        updateDepartment_userInsertion(commondb, inputdata);
      }


      return res.send(apiUpdateResponse);

    } 
    if(unset == 'unset') {
      var collection = db.collection(tblList[endpoint]);

      if (querydata['_id']) {
        querydata['_id'] = ObjectId(querydata['_id']);
      }
      collection.updateOne(querydata, {
        $unset: inputdata
      }, function (err, results) {
        //console.log(results.result);
        res.send({
          status: 200,
          data: results.result,
          message: "Success"
        });
      });
    }
    else {
      var collection = db.collection(tblList[endpoint]);

      if (querydata['_id']) {
        querydata['_id'] = ObjectId(querydata['_id']);
      }
      collection.updateOne(querydata, {
        $set: inputdata
      }, function (err, results) {
        //console.log(results.result);
        res.send({
          status: 200,
          data: results.result,
          message: "Success"
        });
      });
    }

  } catch (err) {
    // console.log(err);
    res.send({
      status: 500,
      error: "backend error",

    });
  }
  finally {
    // await commondb.close();
  }
}

const GetDataCount = async function (req, res){
  try {
    
    
    let endpoint = req.query.root;
    // if (!tblList[endpoint]) {
    //   res.send({
    //     status: 500,
    //     error: "endpoint not exist."
    //   });
    // }
    var count = await loggerdb.collection(endpoint).count();
    
    res.send({
      status: 200,
      data: count,
      message: "Success"
    });

  }
  catch(e){
    console.log(e)
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
}

const addRecord = async function (req, res) {
  try {
    //console.log(req);

    let endpoint = req.body.data.root;
    let inputdata = req.body.data.body;
    var c = req.body.data.c ? req.body.data.c : "R";
    var db = commondb;
    if (c === "M") {
      db = matrixdb;
    }

    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }
    if (endpoint === 'livechat_department_agents') {
      let user = await commondb.collection('users').findOne({ 'username': inputdata.username });
      let departmentagent = await commondb.collection(tblList[endpoint]).findOne({ username: inputdata.username, departmentId: inputdata.departmentId });
      if (user && departmentagent) {
        res.send({
          status: 400,
          message: 'Agent already added'
        });
        return;
      } else if (user) {
        inputdata['agentId'] = user._id;
        inputdata["_id"] = ((new Date()).getTime() + (new Date()).getTime()).toString(36).toLocaleUpperCase() + Math.random().toString(36).slice(2);
        await commondb.collection(tblList[endpoint]).insert(inputdata);
      } else {
        res.send({
          status: 400,
          message: "User does not exist"
        });
        return;
      }
    } else if (endpoint === 'users') {
      const formData = req.body && req.body.data && req.body.data.body ? req.body.data.body.nd : {};
      const depArray = formData.department;
      const depname = await commondb.collection('rocketchat_livechat_department').find({ '_id': { $in: depArray } }).toArray()
      let dep = depname.map((e) => {
        return e.name;
      })
      const validationData = validateData(formData, userMadetoryFeilds);
      if (validationData.isValid == false) {
        return res.send({
          status: 400,
          message: validationData.messege
        });
      }
      const promiseArray = [];
      const healthdep = getArraysIntersection(dep, HEALTH_DEPARTMENTS);
      if (healthdep.length > 0) {
        promiseArray.push(insertUser(healthdb, formData, endpoint, "health"));
      }

      dep = dep.filter(n => !healthdep.includes(n))
      const motordep = getArraysIntersection(dep, MOTOR_DEPARTMENTS);
      if (motordep.length > 0) {
        promiseArray.push(insertUser(cardb, formData, endpoint, "motor"));
      }

      dep = dep.filter(n => !motordep.includes(n))
      if (dep) {
        promiseArray.push(insertUser(commondb, formData, endpoint, "common"));
      }
      const promisResult = await Promise.all(promiseArray);
      let apiResponse = {};
      promisResult.forEach(e => {
        if (e.department == 'common') {
          apiResponse['common_response'] = e;
        }
        if (e.department == 'health') {
          apiResponse['health_response'] = e;
        }
        if (e.department == 'motor') {
          apiResponse['motor_response'] = e;
        }
      })

      return res.send(apiResponse);
    } else {
      await matrixdb.collection(tblList[endpoint]).insertOne(inputdata);
    }
    res.send({
      status: 200,
      message: "Success"
    });

  } catch (e) {
    console.log(e);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

const WinFrmSettings = async function (req, res) {
  try {

    // let settings = await matrixdb.collection("WinFrmSettings").findOne({});
    // res.send(settings);

    fs.readFile(path.join(appRoot, "public/json", "winfrm.json")

      , null, function (error, data) {
        if (error) {
          res.writeHead(404);
          res.write('Whoops! File not found!');
        } else {
          try {
            res.write(data);

          }
          catch (e) {
            res.write(e.toString());
          }
        }
        res.end();
      });

  } catch (e) {
    console.log(e);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
};

async function deleteRecord(req, res) {
  try {
    let endpoint = req.body.data.root;
    let querydata = req.body.data.body;
    var c = req.body.data.c ? req.body.data.c : "R";
    var db = commondb;
    if (c === "M") {
      db = matrixdb;
    }
    if (!tblList[endpoint]) {
      res.send({
        status: 500,
        error: "endpoint not exist."
      });
    }
    var collection = db.collection(tblList[endpoint]);

    collection.deleteOne(querydata, function (err, results) {
      //console.log(results.result);
      res.send({
        status: 200,
        data: results.result,
        message: "Success"
      });
    });


  } catch (err) {
    // console.log(err);
    res.send({
      status: 500,
      error: err
    });
  }
}

function validateData(data, feilds) {
  // console.log("formdata:::",data)
  let isValid = true;
  let messege = '';
  feilds.forEach(e => {
    switch (e.type) {
      case 'string':
        if (e.key == 'emails') {
          var filter = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i
          if (!data[e.key]) {
            messege = `${e.key} is blank,`;
            isValid = false;
          } else if (!filter.test(data[e.key])) {
            messege = `${e.key} is not valid,`;
            isValid = false;
          }
        } else {
          if (!data[e.key]) {
            messege = `${e.key} is blank,`;
            isValid = false;
          }
        }
        break;
      case 'int':
        if (!data[e.key]) {
          messege += `${e.key} is invalid value,`;
          isValid = false;
        }
        break;
      case 'boolean':
        if (typeof data[e.key] != "boolean") {
          // console.log("dddddd",data[e.key]);
          messege += `${e.key} must have boolean,`;
          isValid = false;
        }
        break;
      case 'array':
        if (!data[e.key] || !data[e.key].length) {
          messege += `${e.key} is invalid value,`;
          isValid = false;
        }
        break
      default:
    }
  });
  messege = messege.replace(/,+$/, '');
  return {
    messege,
    isValid
  }
}
async function getUserObject(userData) {
  let returnData = {
    _id: ((new Date()).getTime() + (new Date()).getTime()).toString(36).toLocaleUpperCase() + Math.random().toString(36).slice(2),
    name: userData.name,
    username: userData.username,
    emails: [{
      address: userData.emails,
      verified: userData.verified || false
    }],
    manager: userData.manager,
    employeeId: userData.employeeId,
    requirePasswordChange: userData.requirePasswordChange || false,
    domainusername: userData.domainusername
  }
  if (userData.password) {
    const salt = await bcrypt.genSalt(10);
    const password = crypto.Hash('sha256').update(userData.password).digest('hex')
    var hash = await bcrypt.hash(password, salt);
    returnData.services = {
      password: {
        bcrypt: hash
      }
    }
  }
  if (userData.role) {
    returnData.roles = userData.role;
  }
  return returnData;
}
async function updateUserDepartment(db, inputdata, agentId) {
  await db.collection("rocketchat_livechat_department_agents").deleteMany({ "username": inputdata.username })

  const depUpdatetData = inputdata && inputdata.department && inputdata.department.length ? inputdata.department.map(e => {
    return {
      _id: ((new Date()).getTime() + (new Date()).getTime()).toString(36).toLocaleUpperCase() + Math.random().toString(36).slice(2),
      agentId: agentId,
      departmentId: e,
      username: inputdata.username,
    }
  }) : [];
  if (depUpdatetData && depUpdatetData.length) {
    await db.collection("rocketchat_livechat_department_agents").insertMany(depUpdatetData);
  }
}
function filterMultiselectValues(inputData) {
  if (inputData.department && inputData.department.length) {
    inputData.department = inputData.department.map(e => {
      return e.value || e;
    });
  }
  if (inputData.role && inputData.role.length) {
    inputData.role = inputData.role.map(e => {
      return e.value || e;
    });
  }
  return inputData;
}
async function insertUser(db, formData, endpoint, department) {
  const returnObj = {
    success: false,
    department: department,
    message: ''
  }
  const user = await db.collection('users').find({ 'username': formData.username }).toArray();
  if (user.length > 0) {
    returnObj.message = 'Duplicate username';
    return returnObj;
  }
  const domainuser = await db.collection('users').find({ 'domainusername': formData.domainusername }).toArray();
  if (domainuser.length > 0) {
    returnObj.message = 'Duplicate Domain_username'
    return returnObj;
  }
  const emails = await db.collection('users').find({ "emails": { $elemMatch: { "address": formData.emails } } }).toArray();
  if (emails.length > 0) {
    returnObj.message = 'Emails already exist'
    return returnObj;
  }
  try {
    const userInserData = await getUserObject(formData);
    var InsertedUser = await db.collection(tblList[endpoint]).insertOne(userInserData);
  } catch (e) {
    returnObj.message = 'backend error'
    return returnObj;
  }
  if (InsertedUser.insertedId) {
    const depInsertData = formData && formData.department && formData.department.map(e => {
      return {
        _id: ((new Date()).getTime() + (new Date()).getTime()).toString(36).toLocaleUpperCase() + Math.random().toString(36).slice(2),
        agentId: InsertedUser.insertedId,
        departmentId: e,
        username: formData.username,
      }
    });
    await db.collection("rocketchat_livechat_department_agents").insertMany(depInsertData);
    returnObj.message = 'user inserted.'
    returnObj.success = true
    return returnObj;
  } else {
    returnObj.message = 'backend error'
    return returnObj;
  }
}
async function updateUser(db, inputdata, querydata, department) {
  const returnObj = {
    success: false,
    department: department,
    message: ''
  }
  const userName = await db.collection('users').findOne({ "username": inputdata.username });
  if (userName) {
    const id = { "_id": userName._id }
    querydata = id;
  }
  const emails = await db.collection('users').findOne({ "emails": { $elemMatch: { "address": inputdata.emails } }, "username": { $ne: inputdata.username } });
  if (emails) {
    returnObj.message = 'email already exist';
    return returnObj;
  }
  await updateUserDepartment(db, inputdata, querydata._id);
  const userUpdateData = await getUserObject(inputdata);
  delete userUpdateData['_id'];
  delete userUpdateData['username'];
  delete userUpdateData['domainusername'];
  delete userUpdateData['password'];
  try {
    await db.collection('users').updateOne(querydata, { $set: userUpdateData });
    returnObj.success = true;
    return returnObj;
  } catch (e) {
    returnObj.message = 'backend error';
    return returnObj;
  }
}
async function updateDepartment_userInsertion(db, inputdata) {
  Data = await commondb.collection('users').findOne({ 'username': inputdata.username })
  delete Data._id;
  const user = await db.collection('users').find({ 'username': inputdata.username }).toArray();

  if (user.length == 0) {
    Data["_id"] = ((new Date()).getTime() + (new Date()).getTime()).toString(36).toLocaleUpperCase() + Math.random().toString(36).slice(2)
    const result = await db.collection("users").insertOne(Data);
    await updateUserDepartment(db, inputdata, Data._id);
    //  console.log("result",result);
  }
}
function getArraysIntersection(a1, a2) {
  return a1.filter(function (n) { return a2.indexOf(n) !== -1; });
}

// async function MongoTestData(req, res) {
//   try {
//    let common = await commondb.collection('WhatsAppMessage').find({}).limit(1).toArray();
//    let health = await healthdb.collection('rocketchat_settings').find({}).limit(1).toArray();
//    let car = await cardb.collection('rocketchat_settings').find({}).limit(1).toArray();
//    let matrix = await matrixdb.collection('FactsLeadRanking').find({}).limit(1).toArray();
//    let matrixdash = await matrixdashboarddb.collection('MissSellData').find({}).limit(1).toArray();
//    let rule = await ruleenginedb.collection('FactsLeadRanking').find({}).limit(1).toArray();
//    let logger = await loggerdb.collection('MatrixDashboard_logs').find({}).limit(1).toArray();


//    return res.json({
//     status: 200,
//     data: {common, health, car, matrix, matrixdash, rule, logger}
//    })
//   } catch (err) {
//     return res.json({
//       status: 500,
//       err: err.toString()
//     })
//   }
// }


module.exports = {
  addRecord: addRecord,
  update: update,
  WinFrmSettings: WinFrmSettings,
  getdata: getdata,
  deleteRecord: deleteRecord,
  GetDataCount: GetDataCount,
  getCount: getCount,
  // MongoTestData: MongoTestData
};