
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
import {MultiSelect} from "react-multi-select-component";
import moment from 'moment';
import _, { bind } from 'underscore';


// reactstrap components

import {
    GetMoodleQuiz
} from "../../store/actions/CommonAction";

import { Row, Col } from 'react-bootstrap';

import DropDown from '../Common/DropDown';

class QuizList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            QuizData: [],
            selectedValue: [{ label: "Select Quiz", value: "0", courseId: "0" }],
        };
        this.onSelect = this.onSelect.bind(this);
    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {//debugger;
        if ((nextProps.ProductId && nextProps.ProductId != this.props.ProductId) || (nextProps.StartDate != this.props.StartDate) || (nextProps.EndDate != this.props.EndDate)) {
            this.fetchQuizData(nextProps.ProductId, nextProps.StartDate, nextProps.EndDate);
            this.setState({ selectedValue: [{ label: "Select Quiz", value: "0", courseId: "0" }] });
        }
       
    }
    fetchQuizData(ProductId, StartDate, EndDate) {//debugger;
        GetMoodleQuiz(ProductId, function (results) {
            var data = results.data.data;
            let AllDates = this.enumerateDaysBetweenDates(StartDate, EndDate);
            const resultdata = data.filter(val => _.contains( AllDates, val.date));
            this.setState({ QuizData: resultdata });
        }.bind(this));

    }

    enumerateDaysBetweenDates(startDate, endDate) {

        var dates = [];
        dates.push(startDate);

        var currDate = moment(startDate).startOf('day');
        var lastDate = moment(endDate).startOf('day');
        while (currDate.add(1, 'days').diff(lastDate) < 0) {
            dates.push(currDate.format('YYYY-MM-DD'));
        }

        dates.push(endDate);

        return dates;
    }

    onSelect(selectedList, selectedItem) {

        console.log(selectedList);
        const newselectedList = selectedList.filter(task => task.label !== 'Select Quiz');
        this.setState({ selectedValue: newselectedList });
        this.props.onSelectQuiz(newselectedList);
        console.log(this.props);
    }

    render() {

        let { visible } = this.props;
        console.log("this.props.disabled", this.props.disabled);
        if (visible == false) {
            return null;
        }
        return (

            <div>

                <Form.Group controlId="quiz_dropdown">
                    <MultiSelect
                        options={this.state.QuizData} // Options to display in the dropdown
                        value={this.props.disabled ? [] : this.state.selectedValue} // Preselected value to persist in dropdown
                        onChange={this.onSelect} // Function will trigger on select event                                                
                        // labelledBy={"Select Course"}
                        disabled={this.props.disabled}
                        selectAllLabel={"Select ALL Quiz List"}
                        className={this.props.disabled ? "quizlist disable" : "quizlist"}
                    />
                </Form.Group>
            </div>

        );
    }
}


export default QuizList;

