import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetCommonData, InsertData, UpdateData, GetCommonspData
} from "../../store/actions/CommonAction";
import {
    addRecord
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from '../Common/DataTableWithFilter';
import { OpenNewSalesView, GetJsonToArray, getUrlParameter, getuser } from '../../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
const FOSRealTimePanel = (props) => {
 
    const [root] = useState(["FOS Real-Time Dashboard"])
    const [PageTitle] = useState(["FOS Real-Time Dashboard"])
    let [rows, setRows] = useState([]);
    const [FOSRealTimeDetails, setFOSRealTimeDetails] = useState([]);
    const user=getuser();
    const userName=user? user.UserName :'';
    let dtRef = React.createRef();
  
    const columnlist = [

        {

            name: 'Agent',
            selector: 'Agent',
            type: "string",
            editable: false,
            sortable: true,
            width: '200px',
            //searchable: true
        },
        {

            name: 'Total Appointments',
            selector: 'Total Appointments',
            //type: "string",
            editable: false,
            sortable: true,
            width: '150px',
            //searchable: true
        },
        {

            name: 'Confirmed- visit pending',
            selector: 'Appointment Confirmed',
            //type: "string",
            width: '200px',
            //sortable: true,
            //searchable: true
        },

        {

            name: 'Completed',
            selector: 'Appointment Completed',
            //type: "datetime",
           // cell: row => <div>{row.CreatedOn ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.CreatedOn}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            //searchable: true
        },
        {

            name: 'Rescheduled',
            selector: 'Appointment Postponed',
            //type: "datetime",
            //cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            //searchable: true
        },
        {

            name: 'Cancelled',
            selector: 'Appointment Cancelled',
            //type: "datetime",
            //cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            //searchable: true
        },
        {

            name: 'Pending',
            selector: 'Pending',
            //type: "datetime",
            //cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '180px',
            //searchable: true
        },

        {

            name: 'Agent Current Status',
            selector: 'Agent Current Status',
            type: "string",
            width: '180px',
            //sortable: true,
            //searchable: true
        },
        // {

        //     name: 'Active Marked',
        //     selector: 'Active Marked',
        //     //cell: row => <div>{row.LastAssignedTo ? row.LastAssignedTo + "/" + row.UserName : ""}</div>,
        //     sortable: true,
        //     type: "string",
        //     sortable: false,
        //     width: '150px',
        //     //searchable: true,
        // },
        {

            name: 'Total Assigned Leads',
            selector: 'Total Assigned Leads',
            //type: "datetime",
            //cell: row => <div>{row.LastAssignedDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.LastAssignedDate}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            //searchable: true
        },
        
        
    ];

    useEffect(() => {
           
       if (!props.CommonData.isError) {

            GetData();
        }

    }, [])

    
    const GetData = () => {

        const user = getuser();
       
        if (user) {
       
            props.GetCommonspData({
                root: 'GetRealTimeDashboardDetails',
                c: "R",

                params: [{ ManagerIds: user.UserID }],

            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data[0]
                    setFOSRealTimeDetails(item)
                }
            })
        }

    }
   
  

    return (
        <>

            <div className="content fosAgent">
                <ToastContainer />
                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                        <p>TL's Name:   {userName}</p>
                                        <button className="btn btn-primary" onClick={()=>GetData()}>
                                            Refresh
                                        </button>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
        
                                <DataTable

                                    columns={columnlist}
                                    data={FOSRealTimeDetails}
                                    printexcel={false}
                                    ref={dtRef}
                                    
                                />

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(FOSRealTimePanel);