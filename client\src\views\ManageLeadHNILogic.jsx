import { useEffect, useState } from "react";
import React from "react";

import {
    GetCommonData, GetCommonspData, HniLogicMaster
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from './Common/DataTableWithFilter';
import {  getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'
import 'react-datetime/css/react-datetime.css'
import * as XLSX from 'xlsx';


const ManageLeadHNILogic = (props) => {    
    
    const PageTitle = "Manage Lead HNI Logics"
    let [HniLeads, setHniLeads] = useState([])
    const [selectedFile,setselectedFile] = useState(null)
    const [addClass,setaddClass] = useState('')
    const DownloadedFile ='/SampleExcelfiles/HealthHNI.xlsx'
    const [PreviewedData, setPreviewedData] = useState([]);
    const [ErrorArray, setErrorArray] = useState([]);

    const columnlist = [

        {
            name: 'PostCode',
            selector: 'PostCode',
            type: "string",
            width: '100px',
        },

        {
            name: 'CityID',
            selector: 'CityID',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },
        {
            name: 'MinAPE',
            selector: 'MinAPE',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },
        {
            name: 'MaxAPE',
            selector: 'MaxAPE',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },
        {
            name: 'SupplierID',
            selector: 'SupplierID',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },

        {
            name: 'GroupID',
            selector: 'GroupID',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },
        {
            name: 'PaymentPeriodicity',
            selector: 'PaymentPeriodicity',
            cell: row => <div>{row.PaymentPeriodicity ? row.PaymentPeriodicity : "N.A"}</div>,
            type: "string",
            width: '100px',
        },
        {
            name: 'NumberOfDays',
            selector: 'NumberOfDays',
            cell: row => <div>{row.NumberOfDays ? row.NumberOfDays : "N.A"}</div>,
            type: "int",
            width: '100px',
        },
        {
            name: 'IsRenewal',
            selector: 'IsRenewal',
            cell: row => <div>{row.IsRenewal === true ? 1 :0}</div>,
            type: "bool",
            width: '100px',
        },
        {
            name: 'Created On',
            selector: 'CreatedOn',
            type: "datetime",
            cell: row => <div>{row.CreatedOn ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.CreatedOn}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            //width: '200px',
            format:'YYYY-MM-DD HH:mm:ss',
            //exportCell: ((row) => {row.Createdon ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.Createdon}</Moment> : "-"}),
            //searchable: true
        },
        {
            name: 'CreatedBy',
            selector: 'CreatedBy',
            type: "string",
            width: '100px',
            //sortable: true,
            //searchable: true
        },       
    
    ];


    useEffect(() => {
        if (!props.CommonData.isError) {
           GetData();
        }
    }, [])

    const GetData = () => {
        
            props.GetCommonData({
                root:'healthhnilogic',
                con: [{ "IsActive": 1 }],
                c: "R",
            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data[0]
                    setHniLeads(item)
                }
            })       

    }
    
    const onFileChange = (event) => {
        // Update the state 
        setPreviewedData([]);
        setErrorArray([]);

        let HNILogicFile = event.target.files[0];
        if (HNILogicFile && HNILogicFile.name && HNILogicFile.name.split('.').pop() != 'xlsx') {
            toast("Please choose valid Excel File of type xlsx", { type: 'error' });
            setaddClass('');
            setselectedFile(null);
            return;
        }
        setselectedFile(event.target.files[0]);

        if (event.target.files) {
      
            const reader = new FileReader();
            let dataArray = [];

            reader.onload = (e) => {
              const workbook = XLSX.read(e.target.result, { type: 'array' });
              const sheetName = workbook.SheetNames[0]; // Assuming we want the first sheet
              const sheet = workbook.Sheets[sheetName];
              const parsedData = XLSX.utils.sheet_to_json(sheet);
              for (let i = 0; i < parsedData.length; i++) {
                dataArray.push({ PostCode: parsedData[i].PostCode, CityID: parsedData[i].CityID,
                    MinAPE: parsedData[i].MinAPE, MaxAPE: parsedData[i].MaxAPE,
                    SupplierID: parsedData[i].SupplierID, GroupID: parsedData[i].GroupID, 
                    PaymentPeriodicity: parsedData[i].PaymentPeriodicity, NumberOfDays: parsedData[i].NumberOfDays,
                    IsRenewal: parsedData[i].IsRenewal === 1 ? true : false })
               };
            setPreviewedData(dataArray);

            }
            reader.readAsArrayBuffer(event.target.files[0]);

        }
    };

    const onFileUpload = (e) => {
      
        e.preventDefault();
        setaddClass('fa fa-spinner fa-spin');

        if (selectedFile == null) {
            toast("Please choose Excel File", { type: 'error' });
            setaddClass('');
            return;
        }
       
        // Create an object of formData
        const User = getuser(); 
        var type =  getType();
        const formData = new FormData(); 
        // Update the formData object 
        formData.append( 
            "myFile",
            selectedFile,
            selectedFile.name,
        ); 
        formData.append('type', type);         
        formData.append('UserId', User.UserID); 

        
        // Details of the uploaded file 
        // Request made to the backend api 
        // Send formData object 
        HniLogicMaster(formData, function (results) {
            
            document.getElementById('files-upload').value = null;
            if (results && results.data && results.data.data && results.data.status == 200) {
                const hniLeadsNotPushed = results.data.data.filter(hniLeads => hniLeads.status === 2);
                hniLeadsNotPushed && setErrorArray(hniLeadsNotPushed);
                alert('File uploaded');
                setaddClass('');
                setselectedFile(null);
                setPreviewedData([]);
                GetData() 
            } else {
                setselectedFile(null);
                toast(results.data.message, { type: 'error' });
                return;
            }
        });
    }


    const getType =() => {
        var loc = window.location.href;
        let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
        
        lastUrl = lastUrl.split('?');
        //console.log('lasturl',lastUrl[0]);
        return lastUrl[0];    
    }

    const renderDownloadFile = () => {
        if (DownloadedFile) {
            return  <Link style={{ fontSize: '14px' }} to={DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    const handleExport = () => {

        const newArray = Array.isArray(HniLeads) && HniLeads.map(obj => {
            const { PostCode, CityID, MinAPE, MaxAPE, SupplierID, GroupID, PaymentPeriodicity, NumberOfDays, IsRenewal} = obj; // Use destructuring to remove extra keys
            return { PostCode, CityID, MinAPE, MaxAPE, SupplierID, GroupID, PaymentPeriodicity, NumberOfDays, IsRenewal: IsRenewal ? 1 : 0 // Change IsRenewal value from false/true to 0/1
                 };
        });
        if (Array.isArray(newArray) && newArray.length > 0) {
            const sheet = XLSX.utils.json_to_sheet(newArray);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
            XLSX.writeFile(workbook, 'HNILeads.xlsx');
        }
    };


    return (
        <>

            <div className="content fosAgent" >
                <ToastContainer />

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>                                                               
                                <Row>
                                    <Col md = {10}>
                                        <form onSubmit={onFileUpload}>                                           
                                                <label for="files-upload">Upload Excel To Lead HNI Master</label>&ensp;
                                                <input type="file" id="files-upload" onChange={onFileChange} />                                           
                                                <button type="submit" id="uploadbutton"  className="btn btn-primary">Upload! <i class={addClass} aria-hidden="true"></i></button>                                                               
                                        </form>
                                    </Col>
                                    
                                </Row>
                                        
                               
                                {renderDownloadFile()} 
                                {PreviewedData && (PreviewedData.length > 0) && <>
                                <h3>Preview Data Before Upload </h3>
                                <DataTable
                                columns={columnlist}
                                data = {PreviewedData}
                                printexcel={false}
                                /></>}

                                {ErrorArray && (ErrorArray.length > 0) && <><h3>Details of Data Not Pushed</h3>
                                <ul>
                                    { ErrorArray.map(hniLead => (
                                        <li>
                                            <span style={{ color: 'red' }}>Payment Periodicity not correct for {hniLead.PaymentPeriodicity} </span>
                                        </li>
                                    ))}
                                </ul></>
                                }

                                {HniLeads && (HniLeads.length > 0) && <><h3>HNI Logics Data</h3>

                                <span onClick={handleExport} class="downloadExcel"></span>

                                <DataTable
                                    columns={columnlist}
                                    data = {HniLeads}
                                    printexcel={false}
                                /></>}

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData
    }
)(ManageLeadHNILogic);