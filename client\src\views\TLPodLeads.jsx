import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
//import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import './customStyling.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func } from "prop-types";
import moment from "moment";
import { If, Then, Else } from 'react-if';
import DropDownListMysql from './Common/DropDownListMysql';
import DateRange from "./Common/DateRangeTLPodLead"


class TLPodLeads extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "POD Transfer/Booked leads",
      TLPodLeads: [],
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      IsLoading: true,
      podType: 0,
      StartDate: moment().subtract(2, 'days').format("YYYY-MM-DD"),
      EndDate: moment().format("YYYY-MM-DD"),
      range: 15,
      colWidth: 3,
    };
    this.dtRef = React.createRef();
    this.AgentType = null;
    this.dateRangeRef = React.createRef();

    this.PodTypeList = {
      config:
      {
          root: "Podtypelist",
          data: [ { Id: 1, Display: "Booked leads" }],
      }
    };
    const Cell = ({v}) => (
      <span title={v}>{(v)?v.substring(0,25):''}{(v && v.length>25)?'...':''}</span>
    );
    this.columnlistTransferLeads = [
      {
        name: "LeadId",
        selector: "LeadID",        
      },
      {
        name: "POD Transfer Date",
        selector: "ReAssignedon",        
        sortable: true,
        cell: row => (row.ReAssignedon == null) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.ReAssignedon}</Moment>
      },
      {
        name: "Transferred By",
        selector: "TransferBy",  
        cell: row => <Cell v={row.TransferBy} />,      
        searchable: true,
      },
      {
        name: "Transferred To",
        selector: "TransferTo",  
        cell: row => <Cell v={row.TransferTo} />,            
        searchable: true,
      },
      {
        name: "Status",
        selector: "StatusName",
        sortable: false,
      },
      {
        name: "CallBack Date",
        selector: "EventDate",
        sortable: true,
        cell: row => (row.EventDate == null) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.EventDate}</Moment>
      }

    ];

    this.columnlistBookedLeads = [
      {
        name: "BookedLeadId",
        selector: "BookedLeadId",        
      },
      {
        name: "Parent Lead ID",
        selector: "ParentID",        
      },
      {
        name: "Transferred Date",
        selector: "TransferredDate",        
        sortable: true,
        cell: row => (row.TransferredDate == null) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.TransferredDate}</Moment>
      },
      {
        name: "Assigned by",
        selector: "TransferBy",        
        searchable: true,
      },
      {
        name: "Assigned to ",
        selector: "TransferTo",        
        searchable: true,
      },      
      {
        name: "Booking Date",
        selector: "BookingDate",
        sortable: true,
        cell: row => (row.BookingDate == null) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.BookingDate}</Moment>
      },
      {
        name: "Status",
        selector: "StatusName",
        sortable: false,
      }

    ];


  }

  handleStartDate = (StartDateValue) => {
    this.setState({ StartDate: StartDateValue });
  }

  handleEndDate = (EndDateValue) => {
      this.setState({ EndDate: EndDateValue });
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
        if (nextProps.CommonData["GetPODLeadsData"]) {
            this.setState({ TLPodLeads: nextProps.CommonData["GetPODLeadsData"], IsLoading: false });
        }
    }
  }

  componentDidMount() {
    this.fetchCallBackData(0);
  }

  fetchCallBackData(IsPODBooking) {debugger;
    console.log('useridd',getuser().UserID);
    this.setState({ IsLoading : true})
        var lastDate = moment(this.state.StartDate, "YYYY-MM-DD");
        var currDate = moment().startOf('day');

        if (currDate.diff(lastDate,'days') > 45) {
          toast('Data can be fetched within 45 days range only',{type : 'error'});
          this.setState({ IsLoading : false})
          return;
        }
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetPODLeadsData",
      params: [{ManagerIds: getuser().UserID},{IsPODBooking : IsPODBooking},{fromdate : this.state.StartDate},
      {todate : this.state.EndDate}]//getuser().UserID

    });

  }

  podtypechange(e, props){
    let podType = e.target.value;
    this.setState({podType: e.target.value, IsLoading: true, TLPodLeads: []})
   if(Number(e.target.value) == 0){
    this.setState({StartDate: moment().subtract(2, 'days').format("YYYY-MM-DD"),EndDate: moment().format("YYYY-MM-DD")}, () => this.fetchCallBackData(podType))
    }
    else if (Number(e.target.value) == 1){
      this.setState({StartDate: moment().subtract(7, 'days').format("YYYY-MM-DD"),EndDate: moment().format("YYYY-MM-DD")}, () => this.fetchCallBackData(podType))
    }
    

  }

  render() {
    const columns = (this.state.podType == 0)?this.columnlistTransferLeads : this.columnlistBookedLeads;
    console.log('columns',columns);
    const { items, PageTitle, TLPodLeads, showAlert, AlertMsg, AlertVarient, ReportTime, SelectedRows } = this.state;

    return (
      <>
        <div className="content TLPodLeadsContainer">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={2}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    {/* <Col md={2}>
                      <CardTitle tag="h6">
                      </CardTitle>
                    </Col> */}
                    <Col md={2}>
                    <Form.Label><i>*</i> POD Type </Form.Label>
                    <Form.Group controlId="podType_dropdown" >
                        <DropDownListMysql firstoption="Transfer leads" value={this.state.podType} col={this.PodTypeList} onChange={this.podtypechange.bind(this)}>
                        </DropDownListMysql>
                    </Form.Group>
                  </Col>
                  <DateRange colWidth={this.state.colWidth} range={this.state.range} podType = {this.state.podType}
                  startDate={this.state.StartDate} endDate={this.state.EndDate} ref={this.dateRangeRef} onStartDate={this.handleStartDate.bind(this)} onEndDate={this.handleEndDate.bind(this)}>
                  </DateRange>
                  <Col md={1}>
                  <Form.Label>Fetch </Form.Label>
                      <Button variant="primary" onClick={() => this.fetchCallBackData(this.state.podType)}>Fetch</Button>
                  </Col>
                  </Row>

                </CardHeader>

                <CardBody>
               
                <If condition={this.state.IsLoading}>
                <Then>
                  <i class="fa fa-spinner fa-spin"></i>
                </Then>
                <Else>
                  <DataTable
                    columns={columns}
                    data={(TLPodLeads && TLPodLeads.length > 0) ? TLPodLeads[0] : []}
                    defaultSortAsc={false}
                    export={false}               
                  />
                 </Else>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(TLPodLeads);