const config = require("../../env_config");
const AthenaExpress = require("athena-express");
const AWS = require("aws-sdk");
const { queryAthenaData } = require("./Helper");
const axios = require("axios");
const logger = require("../../modules/Loggerdb/Controller");
const athenaQueries = require("./AthenaQueries");

async function getAthenaData(req, res) {
  let LogData = {
    Application: "MatrixDashboard",
    Method: "getAthenaData",
    Console: [],
    CreatedOn: new Date(),
    StartTime: new Date(),
    EndTime: new Date(),
  };
  let consoles = [];

  try {
    let { queryString, queryName } = req.body;

    if(queryName && athenaQueries[queryName]){
      queryString=athenaQueries[queryName];
    }

    consoles.push({ query: queryString });
    LogData.StartTime = new Date();

    let response = await queryAthenaData(String(queryString), queryName);

    LogData.EndTime = new Date();
    if (response.errorStatus === 0) {
      return res.send({
        status: 200,
        message: "Success",
        data: response.results || [],
      });
    } else {
      consoles.push({ error: response.message });
      return res.send({
        status: 500,
        message: response.message,
      });
    }
  } catch (err) {
    consoles.push({ error: err.toString });
    return res.send({
      status: 500,
      message: err.toString(),
    });
  } finally {
    LogData.Console = consoles;
    //logger.LogConsole(LogData);
  }
}

async function getCredentials(req, res) {
  try {
    let data = await axios.get(config.AWS_CREDENTIALS_URL);

    if (data !== null) {
      res.send({
        status: 200,
        message: "Success",
        data: data,
      });
    } else {
      res.send({
        status: 500,
        message: null,
        data: data,
      });
    }
  } catch (err) {
    res.send({
      status: 500,
      message: err.toString(),
    });
  }
}

async function getCustTicketAthenaData(req, res) {
  try {
    let query = "";

    // Type = 1 (count of tickets product wise)
    // Type = 2 (count of open tickets based on timeline)
    // Type other than 1,2 (raw dump of tickets)

    if (req.body?.Type == 1) {
		query = `SELECT CASE WHEN PRD.ID = 2 AND lower(LD.LeadSource) = 'renewal' THEN 'Health Renewal' WHEN PRD.ID = 2 THEN 'Health' WHEN PRD.ID = 7 THEN 'Term' WHEN PRD.ID = 115 THEN 'Savings' WHEN PRD.ID = 117 THEN 'Motor' ELSE 'Others' END AS Product, COUNT(DISTINCT CASE WHEN TD.StatusId IN (1,2,5) THEN TD.TicketID ELSE NULL END) AS Open, COUNT(DISTINCT CASE WHEN TD.StatusId IN (3,4) THEN TD.TicketID ELSE NULL END) AS Closed,COUNT(DISTINCT TD.TicketID) AS Total FROM sqldb_athena.PBCROMA_CARE_TicketDetails TD INNER JOIN sqldb_athena.Matrix_CRM_LeadDetails LD ON LD.LeadID = TD.LeadID INNER JOIN sqldb_athena.PBCROMA_CRM_UserGroupRoleMapNew UGRMN ON UGRMN.UserId = TD.CreatedBy LEFT JOIN (SELECT GroupID, ProductID, MAX(ProcessID) AS ProcessID, MAX(IsRenewal) AS IsRenewal FROM ((SELECT GroupID ,ProductID, ProcessID, IsRenewal FROM sqldb_athena.PBCROMA_MTX_ProductGrpMapping_Allocation WHERE ProductID IN (2,7)) UNION ALL (SELECT GroupID,ProductID, 0 AS ProcessID, 0 AS IsRenewal FROM sqldb_athena.PBCROMA_CRM_ProductGroupMapping WHERE ProductID IN (2,7)))A GROUP BY GroupID, ProductID) PGM ON PGM.GroupId=UGRMN.GroupId INNER JOIN sqldb_athena.PBCROMA_DBO_Products PRD ON PRD.ID=TD.ProductID WHERE TD.createdby IN (SELECT UserID  FROM sqldb_athena.PBCROMA_crm_UserDetails WHERE COALESCE(IsBMS,0)=0 AND IsActive=1) AND CAST(TD.CreatedOn AS TIMESTAMP) BETWEEN date_add('DAY', -30, current_date) AND date_add('DAY', 0, current_date) AND TD.AutoClosure=1 AND TD.ProductID in (2,114,117,7,115) AND (COALESCE(lower(TD.Source),'') IN ('matrix') OR (COALESCE(lower(TD.Source),'') IN ('pb manual') AND COALESCE(lower(TD.subsource),'') IN ('matrix'))) AND (UGRMN.GroupId IN (${req.body.UserGroups}) OR PGM.ProductId IN (2,7)) GROUP BY CASE WHEN PRD.ID = 2 AND lower(LD.LeadSource) = 'renewal' THEN 'Health Renewal' WHEN PRD.ID = 2 THEN 'Health' WHEN PRD.ID = 7 THEN 'Term' WHEN PRD.ID = 115 THEN 'Savings' WHEN PRD.ID = 117 THEN 'Motor' ELSE 'Others' END`;
    }
    else if (req.body?.Type == 2) {      
      query = `SELECT CASE WHEN PRD.ID = 2 AND lower(LD.LeadSource) = 'renewal' THEN 'Health Renewal' WHEN PRD.ID = 2 THEN 'Health' WHEN PRD.ID = 7 THEN 'Term' WHEN PRD.ID = 115 THEN 'Savings' WHEN PRD.ID = 117 THEN 'Motor' ELSE 'Others' END AS Product, COUNT(DISTINCT CASE WHEN CAST(TD.CreatedOn AS TIMESTAMP) >= date_add('DAY', -2, current_date) THEN TD.TicketID ELSE NULL END) AS "0-2days", COUNT(DISTINCT CASE WHEN CAST(TD.CreatedOn AS TIMESTAMP) >= date_add('DAY', -5, current_date) AND CAST(TD.CreatedOn AS TIMESTAMP) < date_add('DAY', -2, current_date) THEN TD.TicketID ELSE NULL END) AS "3-5days", COUNT(DISTINCT CASE WHEN CAST(TD.CreatedOn AS TIMESTAMP) >= date_add('DAY', -10, current_date) AND CAST(TD.CreatedOn AS TIMESTAMP) < date_add('DAY', -5, current_date) THEN TD.TicketID ELSE NULL END)AS "6-10days", COUNT(DISTINCT CASE WHEN CAST(TD.CreatedOn AS TIMESTAMP) < date_add('DAY', -10, current_date) THEN TD.TicketID ELSE NULL END) AS ">11days" FROM sqldb_athena.PBCROMA_CARE_TicketDetails TD INNER JOIN sqldb_athena.Matrix_CRM_LeadDetails LD ON LD.LeadID = TD.LeadID INNER JOIN sqldb_athena.PBCROMA_CRM_UserGroupRoleMapNew UGRMN ON UGRMN.UserId = TD.CreatedBy LEFT JOIN (SELECT GroupID, ProductID, MAX(ProcessID) AS ProcessID, MAX(IsRenewal) AS IsRenewal FROM ((SELECT GroupID ,ProductID, ProcessID, IsRenewal FROM sqldb_athena.PBCROMA_MTX_ProductGrpMapping_Allocation WHERE ProductID IN (2,7)) UNION ALL (SELECT GroupID,ProductID, 0 AS ProcessID, 0 AS IsRenewal FROM sqldb_athena.PBCROMA_CRM_ProductGroupMapping WHERE ProductID IN (2,7)))A GROUP BY GroupID, ProductID) PGM ON PGM.GroupId=UGRMN.GroupId INNER JOIN	sqldb_athena.PBCROMA_DBO_Products PRD ON PRD.ID=TD.ProductID WHERE TD.createdby IN (SELECT UserID  FROM sqldb_athena.PBCROMA_crm_UserDetails WHERE COALESCE(IsBMS,0)=0 AND IsActive=1) AND CAST(TD.CreatedOn AS TIMESTAMP) BETWEEN date_add('DAY', -30, current_date) AND date_add('DAY', 0, current_date) AND TD.AutoClosure=1 AND TD.StatusID IN (1,2,5) AND TD.ProductID in (2,114,117,7,115) AND (COALESCE(lower(TD.Source),'') IN ('matrix') OR (COALESCE(lower(TD.Source),'') IN ('pb manual') AND COALESCE(lower(TD.subsource),'') IN ('matrix'))) AND (UGRMN.GroupId IN (${req.body.UserGroups}) OR PGM.ProductId IN (2,7)) GROUP BY CASE WHEN PRD.ID = 2 AND lower(LD.LeadSource) = 'renewal' THEN 'Health Renewal' WHEN PRD.ID = 2 THEN 'Health' WHEN PRD.ID = 7 THEN 'Term' WHEN PRD.ID = 115 THEN 'Savings' WHEN PRD.ID = 117 THEN 'Motor' ELSE 'Others' END`;
    }
    else {              
      query = `SELECT DISTINCT CASE WHEN TD.ProductID=2 AND lower(LD.LeadSource) = 'renewal' THEN 'Health Renewal' ELSE COALESCE(PRD.ProductDisplayName,'') END AS ProductName,COALESCE(TD.TicketDetailsID,'') AS TicketDetailsID,COALESCE(CAST(TD.LeadID AS VARCHAR),'') AS LeadID,COALESCE(ATI.IssueName,'') AS IssueName,COALESCE(ATI.SubIssueName,'') AS SubIssueName,COALESCE(BD.SupplierName,'') AS SupplierName,COALESCE(UD.EmployeeId,'') AS CreatedBy, CASE WHEN PGM.ProductId=2 AND (PGM.IsRenewal=1 OR PGM.ProcessID = 4) THEN 'Health Renewal' ELSE COALESCE(PRD1.ProductDisplayName,'') END AS CreatedByBU,COALESCE(TSM.Status,'') AS Status,COALESCE(SM.StatusName,'') AS BookingStatus, COALESCE(CAST(CAST(TD.createdon as timestamp) AS DATE),CAST(current_date AS DATE))CreatedOn FROM sqldb_athena.PBCROMA_CARE_TicketDetails TD INNER JOIN sqldb_athena.Matrix_CRM_LeadDetails LD ON Ld.LeadId = TD.leadID INNER JOIN sqldb_athena.PBCROMA_CARE_TicketStatusMaster TSM on TSM.StatusID=TD.statusid INNER JOIN sqldb_athena.Pbcroma_crm_bookingdetails BD on BD.leadid=TD.LeadID INNER JOIN sqldb_athena.PBCROMA_CRM_UserGroupRoleMapNew UGRMN ON UGRMN.UserId = TD.CreatedBy INNER JOIN sqldb_athena.PBCroma_CRM_LeadStatus LS ON LS.LeadID = BD.LEADID AND LS.IsLastStatus = 1 LEFT JOIN sqldb_athena.PBCroma_CRM_StatusMaster SM ON SM.StatusId = LS.StatusID LEFT JOIN (SELECT GroupID, ProductID, MAX(ProcessID) AS ProcessID, MAX(IsRenewal) AS IsRenewal FROM ((SELECT GroupID ,ProductID, ProcessID, IsRenewal FROM sqldb_athena.PBCROMA_MTX_ProductGrpMapping_Allocation WHERE ProductID IN (2,7)) UNION ALL (SELECT GroupID,ProductID, 0 AS ProcessID, 0 AS IsRenewal FROM sqldb_athena.PBCROMA_CRM_ProductGroupMapping WHERE ProductID IN (2,7)))A GROUP BY GroupID, ProductID) PGM ON PGM.GroupId=UGRMN.GroupId AND PGM.ProductId NOT IN (130,118,106,147) LEFT JOIN sqldb_athena.PBCROMA_DBO_Products PRD ON PRD.ID=TD.ProductID LEFT JOIN sqldb_athena.PBCROMA_DBO_Products PRD1 ON PRD1.ID = PGM.ProductId LEFT JOIN sqldb_athena.PBCROMA_CARE_AllTicketIssueMaster ATI ON TD.NewIssueTypeID=ATI.SubIssueID  LEFT JOIN sqldb_athena.PBCROMA_crm_userdetails UD  on UD.UserID = TD.CreatedBy where TD.createdby IN (SELECT UserID  FROM sqldb_athena.PBCROMA_crm_UserDetails WHERE COALESCE(IsBMS,0)=0 AND IsActive=1) AND CAST(TD.CreatedOn AS TIMESTAMP) BETWEEN date_add('DAY', -30, current_date) AND date_add('DAY', 0, current_date) AND TD.AutoClosure=1 AND TD.ProductID in (2,114,117,7,115) and TD.StatusID NOT IN (3,4) AND (COALESCE(lower(TD.Source),'') IN ('matrix') OR (COALESCE(lower(TD.Source),'') IN ('pb manual') AND COALESCE(lower(TD.subsource),'') IN ('matrix'))) AND (UGRMN.GroupId IN (${req.body.UserGroups}) OR PGM.ProductId IN (2,7))`;
    }

    let response = await queryAthenaData(query);

    if (response.errorStatus === 0) {
      if (![1,2].includes(req.body?.Type)) {
        const requiredFields = [
          "ProductName",
          "TicketDetailsID",
          "LeadID",
          "IssueName",
          "SubIssueName",
          "SupplierName",
          "CreatedBy",
          "CreatedByBU",
          "Status",
          "BookingStatus",
          "CreatedOn"
        ];
        const formattedData = response.results[0]?.Items.map((record) => {
        let formattedRecord = {};
        requiredFields.forEach((field) => {
          formattedRecord[field] = record[field] || "";
        });
        return formattedRecord;
        });

			return res.send({
			status: 200,
			message: "Success",
			data: formattedData,
			});
		} else {
			return res.send({
			status: 200,
			message: "Success",
			data: response.results[0]?.Items || [],
			});
     	}
    } else {
		return res.send({
			status: 500,
			message: response.message,
		});
    }
  } catch (err) {
		return res.send({
		status: 500,
		message: err.toString(),
		});
  }
}

module.exports = {
	getAthenaData: getAthenaData,
	getCredentials: getCredentials,
	getCustTicketAthenaData: getCustTicketAthenaData,
};
