/* my span booking css */

.MySpanBooking {
    .heading {
        text-align: left;
        font: normal normal 600 24px/35px Poppins;
        letter-spacing: 0px;
        color: #363636;
        opacity: 1;
        margin-right: 15px;
    }

    nav {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #70707033;
        margin-bottom: 3px;

        .slick-track {
            margin-bottom: 10px;

            div {
                margin-right: 3px;

                button {
                    margin-right: 0px;
                }
            }
        }

        button {
            font: normal normal 500 12px/21px Poppins;
            letter-spacing: 0px;
            color: #0065ff99;
            opacity: 1;
            border: none;
            padding: 3px 10px;
            margin-right: 10px;
            background: #0065ff1f 0% 0% no-repeat padding-box;
            border-radius: 50px;
            margin-bottom: 8px;
            height: 30px;
            outline: none;
            cursor: pointer;
            width: auto;
        }

        .activeBtn {
            background: #0065ff 0% 0% no-repeat padding-box;
            color: #fff;
            font-weight: 500;

            .badge {
                background-color: #fff !important;
                color: #0065ff;
            }

        }

        .red {
            background: #f550501f;
            color: #f5505099;

            .badge {
                background-color: #F55050 !important;

            }
        }

        .RedActiveBtn {
            background: #F55050;
            color: #fff;

            .badge {
                color: #F55050;
                background-color: #fff !important;
            }
        }

        .yellow {
            background: #e4ac051f;
            color: #776f0099;

            .badge {
                background-color: #D99D00 !important;
            }
        }

        .yellowActiveBtn {
            background: #D99D00;
            color: #fff;

            .badge {
                color: #e2cc05;
                background-color: #fff !important;
            }
        }

        .orange {
            background: #d4a1051f;
            color: #99570099;

            .badge {
                background-color: #e2802f !important;

            }
        }

        .orangeActiveBtn {
            background: #e78505;
            color: #fff;

            .badge {
                color: #e2802f;
                background-color: #fff !important;
            }
        }

        hr {
            border: 1px solid #000000;
            opacity: 0.12;
            width: 1px;
            height: 25px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 0px;
            position: relative;
            left: -7px;
        }

        .dropdownBtn {
            float: right;
            margin-bottom: 0px;
            font: normal normal 600 14px/21px Poppins;
            letter-spacing: 0px;
            color: #0065ff;
            opacity: 1;
            margin-top: 13px;
            border: 1px solid #0065ff;

        }

        .slick-slider {
            top: 14px;

            .slick-arrow {
                visibility: hidden;
                padding: 0px;
                margin-right: 0px;
                margin-left: 0px;
                z-index: 999;
            }

            .slick-prev,
            .slick-next {
                &::before {
                    visibility: visible;
                    color: #0065ff;
                }
            }
        }

        .badge {
            background-color: #6198e9 !important;
            border-radius: 10px;
            margin-left: 5px;
        }

        .FirstCol {
            flex: 0 0 auto;
            width: 569px;
            display: flex;
        }

        .secondCol {
            width: 100%;
            flex: 0 0 auto;
            margin-top: 5px;
        }

        .thirdCol {
            width: 108px;
            flex: 0 0 auto;
        }

        .iconBtn {
            display: flex;
            width: 70px;
            justify-content: space-evenly;
            cursor: pointer;

            i {
                font-size: 20px;
                color: #5b5b5b;
            }
        }

        &.topbar-list {
            justify-content: space-between;

            .FirstCol {
                width: 100%;
            }

            h3 {
                &.heading {
                    font-size: 18px;
                }
            }

            .iconBtn {
                width: auto;
                margin: 0px 6px 0;
                height: 36px;
                display: inline-block;

                i {
                    font-size: 22px;
                    background: #ccc;
                    padding: 7px;
                    color: #5b5b5b;
                    border-radius: 6px;
                    margin: 0 6px;
                }
            }

            button {
                &.refresh-btn {
                    border-radius: 6px;
                    margin: 0;
                    padding: 2px 12px;
                    background: #0065ff;
                    height: 36px;

                    &:hover {
                        background: #0065ff;
                        opacity: 1;
                    }

                    cursor: pointer;

                    i {
                        color: #fff;
                        font-size: 20px;
                    }
                }
            }
        }
    }

    .NavBarMObileView {
        display: none;
    }

    .SpanBookingDataTable {
        width: 100%;

        .FilterSelection {
            background: #f7f7f7 0% 0% no-repeat padding-box;
            border-radius: 4px;
            padding: 3px 18px 10px;
            margin-bottom: 7px;

            .total-ape {
                background-color: #ffffff;
                border-radius: 4px;
                border: none;
                color: #363636;
                outline: none;
                padding: 4px 8px;
                height: 35px;
            }

            label {
                text-align: left;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                opacity: 1;
                margin-bottom: 5px;
            }

            select {
                background-color: #ffffff;
                border-radius: 4px;
                border: none;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                outline: none;
                height: 30px;
            }

            .has-search .form-control {
                padding: 6px 6px 6px 40px;
                border: none;
                height: 31px;
                font: normal normal 600 12px/18px Poppins;
                color: #363636;

                &::placeholder {
                    font: normal normal normal 10px/13px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 0.4;
                }
            }

            .has-search .form-control-feedback {
                position: absolute;
                z-index: 2;
                display: block;
                width: 1.375rem;
                height: 1.375rem;
                line-height: 2.375rem;
                text-align: center;
                pointer-events: none;
                color: #000;
                opacity: 0.5;
                top: -4px;
                font-size: 14px;
                left: 10px;
            }

            .heading {
                text-align: left;
                font: normal normal 500 11px/14px Poppins;
                letter-spacing: 0px;
                color: #363636;
                opacity: 1;
                margin-bottom: 0px;
                margin-top: 7px;
            }

            .HierarchyBtn {
                padding: 5px 17px;
                text-transform: capitalize;
                margin-top: 5px;
                float: right;
                z-index: 999;
            }

            .pera {
                font: normal normal 500 11px/14px Poppins;
                margin-bottom: 0px;
                color: #da2525;
            }

            .btn:disabled {
                background-color: #cccccc;
                border: none;
            }
            .SearchBtn {
                font: normal normal 500 14px/19px Poppins;
                letter-spacing: 0px;
                color: #fff;
                opacity: 1;
                border: none;
                padding: 5px 10px;
                margin-right: 10px;
                background: #0065ff 0% 0% no-repeat padding-box;
                border-radius: 6px;
                margin-top: 5px;
                outline: none;
                text-transform: capitalize;
                cursor: pointer;
                width: auto;
                height: 30px;
            }
        }

        .FilterSelectionDuplicate {
            background: #f7f7f7 0% 0% no-repeat padding-box;
            border-radius: 4px;
            padding: 3px 18px 10px;
            margin-bottom: 7px;

            .total-ape {
                background-color: #ffffff;
                border-radius: 4px;
                border: none;
                color: #363636;
                outline: none;
                padding: 4px 8px;
                height: 35px;
            }

            label {
                text-align: left;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                opacity: 1;
                margin-bottom: 5px;
            }

            select {
                background-color: #ffffff;
                border-radius: 4px;
                border: none;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                outline: none;
                height: 30px;
            }

            .has-search .form-control {
                padding: 6px 6px 6px 40px;
                border: none;
                height: 31px;
                font: normal normal 600 12px/18px Poppins;
                color: #363636;

                &::placeholder {
                    font: normal normal normal 10px/13px Roboto;
                    letter-spacing: 0px;
                    color: #000000;
                    opacity: 0.4;
                }
            }

            .has-search .form-control-feedback {
                position: absolute;
                z-index: 2;
                display: block;
                width: 1.375rem;
                height: 1.375rem;
                line-height: 2.375rem;
                text-align: center;
                pointer-events: none;
                color: #000;
                opacity: 0.5;
                top: -4px;
                font-size: 14px;
                left: 10px;
            }

            .heading {
                text-align: left;
                font: normal normal 500 11px/14px Poppins;
                letter-spacing: 0px;
                color: #363636;
                opacity: 1;
                margin-bottom: 0px;
                margin-top: 7px;
            }

            .HierarchyBtn {
                padding: 5px 17px;
                text-transform: capitalize;
                margin-top: 5px;
                float: right;
                z-index: 999;
            }

            .pera {
                font: normal normal 500 11px/14px Poppins;
                margin-bottom: 0px;
                color: #da2525;
            }

            .btn:disabled {
                background-color: #cccccc;
                border: none;
            }
            .SearchBtn {
                font: normal normal 500 14px/19px Poppins;
                letter-spacing: 0px;
                color: #fff;
                opacity: 1;
                border: none;
                padding: 5px 10px;
                margin-right: 10px;
                background: #0065ff 0% 0% no-repeat padding-box;
                border-radius: 6px;
                margin-top: 5px;
                outline: none;
                text-transform: capitalize;
                cursor: pointer;
                width: auto;
                height: 30px;
            }
        }

        .bottomPagination {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            align-items: center;

            select {
                width: 155px;
                margin-right: 10px;
                font: normal normal 600 12px/16px Roboto;
            }

            div {
                display: flex;
            }

            p {
                font-size: 13px;
                margin-bottom: 0px;
            }

            .pagination {
                margin-bottom: 0px;

                .page-link {
                    font: normal normal 600 12px/16px Roboto;
                }

                .active {
                    background-color: transparent;
                }

                .page-item.active .page-link {
                    background-color: #0065ff;
                    border-color: #0065ff;
                }
            }
        }
    }

    #floating {
        position: absolute;
        z-index: 99999;
        right: 0;
        background: #f7f7f7 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        padding: 15px;
        -webkit-animation-name: fadeIn;
        animation-name: fadeIn;
        animation-duration: 0.5s;
        top: 47px;

        .managers {
            background-color: #f7f7f7;

            li {
                margin-bottom: 5px;

                button {
                    color: #707070;
                    background-color: transparent;
                    margin: 0;
                    padding: 0;
                    font-size: 12px;
                }

                label {
                    text-align: left;
                    font: normal normal 600 14px/21px Poppins;
                    letter-spacing: 0px;
                    color: #707070;
                    opacity: 1;
                    display: flex;

                    &:hover {
                        background-color: transparent !important;
                    }

                    .rct-icon-uncheck {
                        font-size: 17px;
                        color: #707070;
                        border-radius: 4px;
                    }
                }
            }
        }
    }

    @-webkit-keyframes fadeIn {
        from {
            opacity: 0.5;
        }

        to {
            opacity: 1;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0.5;
        }

        to {
            opacity: 1;
        }
    }
}

.handle {
    transform: none;
}

// .ps__rail-x,
// .ps__rail-y {
//     display: none !important;
// }
footer {
    display: none;
}

/*End  myspan booking css */

.spanbooking {
    width: 100%;
    overflow-x: scroll;
    overflow-y: visible;
    padding: 0;
    height: 320px;

    table {
        border-collapse: collapse;
        width: 100%;

        thead {
            position: sticky;
            top: 0px;
            z-index: 9;
        }

        thead tr {
            background-color: #f5f9fb;
            height: 35px;
        }

        tr th:first-child {
            position: sticky;
            left: 0;
            background-color: #f5f9fb;
            min-width: 100px;
        }

        tr th:nth-child(2) {
            position: sticky;
            left: 100px;
            background-color: #f5f9fb;
            min-width: 150px;
        }

        tr th:nth-child(3) {
            position: sticky;
            left: 250px;
            background-color: #f5f9fb;
            min-width: 150px;
        }

        tr th:nth-child(4) {
            position: sticky;
            left: 400px;
            background-color: #f5f9fb;
            min-width: 150px;
        }

        tr th:last-child {
            position: sticky;
            right: 0;
            background-color: #f5f9fb;
            min-width: 120px;
        }

        td,
        th {
            text-align: left;
            padding: 3px 10px;
            font: normal normal 600 12px/16px Roboto;
            letter-spacing: 0px;
            color: #666;
            min-width: 180px;
        }

        tbody {

            tr {
                background-color: #f5f9fb;

                td:first-child {
                    background-color: #f5f9fb;
                }

                td:nth-child(2) {
                    background-color: #f5f9fb;
                }

                td:nth-child(3) {
                    background-color: #f5f9fb;
                }

                td:nth-child(4) {
                    background-color: #f5f9fb;
                }

                td:last-child {
                    background-color: #f5f9fb;
                }
            }

            tr:nth-of-type(odd) {
                background-color: #fff;

                td:first-child {
                    background-color: #fff;
                }

                td:nth-child(2) {
                    background-color: #fff;
                }

                td:nth-child(3) {
                    background-color: #fff;
                }

                td:nth-child(4) {
                    background-color: #fff;
                }

                td:last-child {
                    background-color: #fff;
                }
            }

            tr td:first-child {
                position: sticky;
                left: 0;
                min-width: 100px;
            }

            tr td:nth-child(2) {
                position: sticky;
                left: 100px;
                min-width: 150px;
            }

            tr td:nth-child(3) {
                position: sticky;
                left: 250px;
                min-width: 150px;
            }

            tr td:nth-child(4) {
                position: sticky;
                left: 400px;
                min-width: 150px;
            }

            tr td:last-child {
                position: sticky;
                right: 0;
                min-width: 120px;
            }
        }

        .uploadDoc {
            border: 1px solid #0065ff;
            border-radius: 19px;
            text-decoration: none;
            padding: 3px 12px;
            font: normal normal 600 10px/16px Poppins;
            letter-spacing: 0px;
            color: #0065ff;
            opacity: 1;
            display: inline-flex;
            justify-content: space-around;
            min-width: 120px;
            align-items: center;

            i {
                font-size: 14px;
                margin-right: 10px;
            }
        }
    }
}

.sidebarMobileView {
    display: none;
}

.more {
    display: none;
}

.ViewMoreBtn {
    display: none;
}

.hierarchyScrolling {
    overflow-y: auto;
    height: 90%;
}

.hierarchyLoading {
    text-align: center;
}

.managerEmployees {
    word-break: break-word;
}

.refreshIcon {
    color: #0065ff;
    font-size: 25px;
    cursor: pointer
}

@media only screen and (max-width: 767px) {
    .ViewMoreBtn {
        display: flex;
        width: 100%;
        float: left;
        text-align: center;
        top: -15px;
        position: relative;
        justify-content: center;

        button {
            font: normal normal 600 12px/18px Poppins;
            letter-spacing: 0px;
            color: #2A63F6;
            width: 90%;
            opacity: 1;
            border: 1px solid #d9d9d9;
            float: right;
            display: flex;
            background-color: #fcfcfc;
            align-items: center;
            padding: 7px;
            text-align: center;
            justify-content: center;
            border-radius: 0px 0px 8px 8px;

            i {
                font-size: 16px;
                font-weight: 600;
                margin-left: 5px;
            }
        }
    }

    .hideBtn {
        display: none;
    }

    .spanbooking {
        width: 100%;
        overflow-x: inherit;
        height: 57vh;
        overflow-y: auto;

        table {
            thead {
                display: none;
            }

            tbody {
                display: block;

                tr {
                    margin-bottom: 15px;
                    display: flex;
                    flex-wrap: wrap;
                    float: left;
                    padding: 10px;
                    border-radius: 8px;
                    background-color: #fcfcfc;
                    border: 1px solid #d9d9d9;
                }

                tr:nth-child(odd) {
                    background-color: #fcfcfc;
                }

                tr td {
                    width: 50%;
                    float: left;
                    line-height: 20px;
                    color: #273a59;
                    font-weight: 600;
                    font-size: 12.6px;
                    background-color: #fcfcfc !important;
                    min-width: auto !important;
                    position: static !important;

                    &:first-child {
                        position: static;
                        width: 50%;
                        background-color: transparent;
                        display: none;
                    }

                    &:nth-child(2n + 1) {
                        text-align: right;
                    }
                }

                tr td::before {
                    content: attr(data-title);
                    display: block;
                    color: #aaa;
                }
            }
        }
    }

    nav {
        display: none !important;
    }

    .NavBarMObileView {
        display: block !important;

        .form-select {
            font: normal normal 600 12px/18px Poppins;
            letter-spacing: 0px;
            opacity: 1;
            background-color: #e0ecff;
            border-radius: 4px;
            border: none;
            height: 35px;
            color: #0065ff;
            margin: 10px 0px 10px;
            background-image: url(../../../../public/spanBooking/caret-down-fill.svg);

            option {
                background: #f7f7f7 0% 0% no-repeat padding-box;
                box-shadow: 0px 3px 12px #00000029;
                border-radius: 4px;
                opacity: 1;
                color: #363636;
                font: normal normal 500 12px/18px Poppins;
            }
        }

        .has-search .form-control {
            padding: 6px 6px 6px 40px;
            border: none;
            height: 36px;
            font: normal normal 600 12px/18px Poppins;
            color: #363636;
            background: #f7f7f7 0% 0% no-repeat padding-box;
            border-radius: 4px;

            &::placeholder {
                font: normal normal normal 10px/13px Roboto;
                letter-spacing: 0px;
                color: #000000;
                opacity: 0.4;
            }
        }

        .has-search .form-control-feedback {
            position: absolute;
            z-index: 2;
            display: block;
            width: 2.375rem;
            height: 2.375rem;
            line-height: 2.375rem;
            text-align: center;
            pointer-events: none;
            color: #000;
            opacity: 0.5;
            top: -1px;
            font-size: 14px;
            left: 4px;
        }
    }

    .sidebarMobileView {
        display: block;

        .dropdownBtn {
            background: #ffffff 0% 0% no-repeat padding-box;
            border: 1px solid #eaeaea;
            font: normal normal 600 14px/21px Poppins;
            letter-spacing: 0px;
            color: #707070;
            opacity: 1;
            width: 100%;
            text-transform: capitalize;
            margin-bottom: 12px;
        }
    }

    .offcanvas {
        border: none;
    }

    .offcanvas-body {
        padding-top: 0px;
        overflow-y: auto !important;
        height: 525px !important;

        .FilterSelection {
            display: block !important;

            label {
                text-align: left;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                opacity: 1;
                margin-bottom: 5px;
                margin-top: 15px;
            }

            select {
                background-color: #f7f7f7;
                border-radius: 4px;
                border: none;
                font: normal normal 600 12px/18px Poppins;
                letter-spacing: 0px;
                color: #363636;
                outline: none;
                height: 35px;
            }
        }

        .filterFooter {
            position: fixed;
            bottom: 8px;
            // background: #fff 0% 0% no-repeat padding-box;
            // box-shadow: 0px 3px 12px #00000029;
            opacity: 1;
            height: 45px;
            width: 99%;
            left: 0px;
            right: 0px;
            display: flex;
            margin: auto;
            justify-content: end;
            align-items: center;

            .cancelBtn {
                text-align: left;
                font: normal normal 600 14px/21px Poppins;
                letter-spacing: 0px;
                color: #aaaaaa;
                opacity: 1;
                background-color: #fff;
                border: none;
                outline: none;
                margin-right: 15px;
            }

            .save {
                background: #2963f6 0% 0% no-repeat padding-box;
                border: 1px solid #2963f6;
                border-radius: 8px;
                outline: none;
                padding: 5px 25px;
                font: normal normal 600 14px/21px Poppins;
                letter-spacing: 0px;
                color: #ffffff;
                opacity: 1;
                margin-right: 15px;
                float: right;
            }
        }
    }

    .FilterSelection {
        display: none !important;
    }

    .mobileViewToggle {
        overflow-y: hidden;
        height: 145px;

    }

}

.ShowHistoryBTn {
    position: relative;

    button {
        position: fixed;
        right: -10px;
        top: 10px;
    }
}

@media (min-width: 320px) and (max-width: 767px) {
    .MySpanBooking .SpanBookingDataTable .bottomPagination {
        margin-top: 10px;
    }

    .spanbooking {
        height: 67vh;
    }

    .main-panel>.content {
        padding: 0 5px;
    }
}

.duplicateLead {

    .FilterSelection {
        padding-bottom: 6px 18px !important;

        .heading {
            font-size: 13px !important;
            margin-bottom: 0px !important;
            font-family: 'Roboto' !important;
            padding-top: 4px !important;
        }
    }

    .FilterSelectionDuplicate {
        padding-bottom: 6px 18px !important;

        .heading {
            font-size: 13px !important;
            margin-bottom: 0px !important;
            font-family: 'Roboto' !important;
            padding-top: 4px !important;
        }
    }

    .mb-0 {
        margin-bottom: 0px;
    }

    .searchBtn {
        font: normal normal 500 14px/21px Poppins;
        letter-spacing: 0px;
        color: #fff;
        opacity: 1;
        border: none;
        padding: 6px 10px;
        margin-right: 10px;
        background: #0065ff 0% 0% no-repeat padding-box;
        border-radius: 6px;
        margin-top: 5px;
        outline: none;
        cursor: pointer;
        width: auto;
    }

    table {
        border-collapse: collapse;
        width: 100%;
    
        thead {
            position: sticky;
            top: 0;
            z-index: 9;
    
            tr {
                background-color: #f5f9fb;
                height: 45px;
    
                th {
                    text-align: left;
                    padding: 10px;
                    font: 600 12px/16px 'Roboto', sans-serif;
                    color: #666;
                    min-width: 100px;
                    background-color: #f5f9fb;
                }
    
                th:first-child {
                    position: sticky;
                    left: 0;
                    min-width: 60px;
                }
    
                th:nth-child(2) {
                    position: sticky;
                    left: 60px;
                    min-width: 150px;
                    cursor: pointer;
                }
    
                th:nth-child(3) {
                    position: sticky;
                    left: 210px;
                    min-width: 95px;
                }
    
                th:nth-child(4) {
                    position: static;
                    // left: 330px;
                    min-width: 120px;
                }
    
                th:last-child {
                    position: static;
                }
            }
        }
            tbody {
            tr {
                background-color: #f5f9fb;    
                &:nth-of-type(odd) {
                    background-color: #fff;
                }    
                td {
                    text-align: left;
                    padding: 6px 10px;
                    font: 600 12px/16px 'Roboto', sans-serif;
                    color: #666;
                    min-width: 100px;
    
                    &:first-child {
                        position: sticky;
                        left: 0;
                        min-width: 60px;                        
                    }
    
                    &:nth-child(2) {
                        position: sticky;
                        left: 60px;
                        min-width: 150px;                       
                    }
    
                    &:nth-child(3) {
                        position: sticky;
                        left: 210px;
                    min-width: 95px;                      
                    }
    
                    &:nth-child(4) {
                        position: static;
                        left: 330px;
                        min-width: 120px;                      
                    }
    
                    &:last-child {
                        position: static;
                    }
                }
            }
        }
    }
    
}
.text-ellipsis{
    width:120px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 0px;
}

.iframe-modal-header{
    height: 0px;
    padding: 10px 15px;
    border-bottom: none;
}