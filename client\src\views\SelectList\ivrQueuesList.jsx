
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
// reactstrap components

import {
    GetMySqlData
} from "../../store/actions/CommonAction";

import { Row, Col } from 'react-bootstrap';

import {MultiSelect} from "react-multi-select-component";

class ivrQueuesList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            ivrQueues: [],
            selectedValue: []
        }


    }
    componentDidMount() {
    }
    componentWillReceiveProps(nextProps) {

    }
    fetchQueueData() {
        this.props.GetMySqlData({
            root: "getIvrQueue",
            con: [{ "ivrtype": this.props.ivrType, "product": this.props.Product }],
        }, function (result) {
            this.setState({ ivrQueues: result.data.data[0] });
        }.bind(this));

    }




    render() {


        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <div>

                <Form.Group controlId="ivr_dropdown">
                    <MultiSelect
                        options={this.state.ivrQueues} // Options to display in the dropdown
                        value={this.state.selectedValue} // Preselected value to persist in dropdown
                        onChange={this.onSelect} // Function will trigger on select event                                                
                        labelledBy={"Select IVR Queues"}
                        selectAllLabel={"Select ALL IVR Queues"}
                    />
                </Form.Group>
            </div>

        );
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData
    }
)(ivrQueuesList);



