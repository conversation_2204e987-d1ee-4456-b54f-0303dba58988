
// import React from "react";
// import {
//   GetMySqlData
// } from "../store/actions/CommonMysqlAction";
// import {
//   GetCommonData, GetCommonspData, GetDataDirect
// } from "../store/actions/CommonAction";
// import { connect } from "react-redux";
// import DataTable from './Common/DataTableWithFilter';
// import DropDown from './Common/DropDownList';
// import moment from 'moment';
// import AlertBox from './Common/AlertBox';
// import { ToastContainer, toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';

// import Datetime from 'react-datetime';
// import 'react-datetime/css/react-datetime.css'

// import { Chart } from "react-google-charts";
// import _ from 'underscore';

// // reactstrap components
// import {
//   Card,
//   CardHeader,
//   CardBody,
//   CardTitle,
//   Table,
//   Row,
//   Col
// } from "reactstrap";
// import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
// import { func } from "prop-types";

// class UnRegisteredChart extends React.Component {
//   constructor(props) {
//     super(props);
//     this.state = {
//       PageTitle: "UnRegistered Chart",
//       UnRegisteredChart: [],
//       ProductId: 117,
//       startdate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
//       enddate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
//       maxdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
//       ReportDate: moment().subtract(0, 'days').format("YYYY-MM-DD"),
//       ReportTime: null,
//       SelectedSupervisors: [],
//       ChartData: [],
//       charttype: 'ColumnChart',

//     };
    
//     this.chartchange = this.chartchange.bind(this);
//     this.handleShow = this.handleShow.bind(this);
//     this.columnlist = [{ "name": "calltime", "selector": "calltime",width: "150px" }, { "name": "callid", "selector": "callid" }, { "name": "agentname", "selector": "agentname" }, { "name": "entertime", "selector": "entertime", width: "150px" }, { "name": "answertime", "selector": "answertime", width: "150px" }, { "name": "hanguptime", "selector": "hanguptime", width: "150px" }, { "name": "callstatus", "selector": "callstatus" }, { "name": "transferedCallid", "selector": "transferedCallid" }, { "name": "product", "selector": "product" }, { "name": "leadid", "selector": "leadid" }, { "name": "mainQueue", "selector": "mainQueue" }, { "name": "mainQueueAgent", "selector": "mainQueueAgent" }, { "name": "mainQueueEnterTime", "selector": "mainQueueEnterTime", width: "150px" }, { "name": "mainQueueAnswerTime", "selector": "mainQueueAnswerTime", width: "150px" }, { "name": "mainQueueHangupTime", "selector": "mainQueueHangupTime", width: "150px" }, { "name": "mainQueueCallStatus", "selector": "mainQueueCallStatus" }];

//     this.ProductList = {
//       config:
//       {
//         root: "Products",
//         cols: ['id AS Id', 'ProductName AS Display'],
//         con: [{ "ISActive": true }]
//       }
//     }
//     this.chartList = {
//       config:
//       {
//         root: "chartList",
//         data: [
//           { Id: 'BarChart', Display: "BarChart" },
//           { Id: 'ColumnChart', Display: "ColumnChart" },
//           { Id: 'AreaChart', Display: "AreaChart" },
//           { Id: 'LineChart', Display: "LineChart" },
//           { Id: 'Bar', Display: "Bar" },
//           { Id: 'PieChart', Display: "PieChart" }
//         ],
//       }
//     }

//     //"AnnotationChart" | "AreaChart" | "BarChart" | "BubbleChart" | "Calendar" | "CandlestickChart" | "ColumnChart" | "ComboChart" | "DiffChart" | "DonutChart" | "Gantt" | "Gauge" | "GeoChart" | "Histogram" | "LineChart" | "Line" | "Bar" | "Map" | "OrgChart" 
//     //| "PieChart" | "Sankey" | "ScatterChart" | "SteppedAreaChart" | "Table" | "Timeline" | "TreeMap" | "WaterfallChart" | "WordTree"
//   }


//   componentWillMount() {
//     setTimeout(function () {
//       this.fetchCallBackData();
//     }.bind(this), 500);
//   }

//   componentWillReceiveProps(nextProps) {
//     if (!nextProps.CommonData.isError) {
//       this.setState({ UnRegisteredChart: nextProps.CommonData["unregistered"] });

//       this.BindChartData(nextProps.CommonData["unregistered"]);
//     }
//   }
//   handleShow(e) {

//     this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
//     setTimeout(function () {
//       this.fetchCallBackData();
//     }.bind(this), 500);

//   }



//   fetchCallBackData() {
//     var SelectedSupervisors = this.state.SelectedSupervisors;
//     this.props.GetMySqlData({
//       root: "unregistered",
//       ProductId: this.state.ProductId,
//       ReportDate: this.state.ReportDate,
//     });
//   }
//   fetchSQLCallBackData() {
//     var SelectedSupervisors = this.state.SelectedSupervisors;
//     this.props.GetCommonData({
//       root: "unregistered",
//       ProductId: this.state.ProductId,
//       startdate: this.state.startdate,
//       enddate: this.state.enddate,
//     });
//   }
  
//   chartchange(e, props) {
//     this.setState({
//       charttype: e.target.value
//     });


//   }
//   handleChange = (e, props) => {

//     if (e._isAMomentObject) {
//       this.setState({ ReportDate: e.format("YYYY-MM-DD") }, function () {
//         this.fetchCallBackData();
//       });
//     }


//   }

//   BindChartData(ReportData) {

//     if (ReportData) {

//       let data = [];
//       let TransferToAgentData = [];
//       let ProductWiseTransfer = [];

//       var hourlydata = _.groupBy(ReportData, 'hour');
//       data.push(["", "Total", { role: 'annotation' }, "Answered", { role: 'annotation' }, "ABANDON", { role: 'annotation' }]);
//       TransferToAgentData.push(["", "Answered in UnRgsterd Q", { role: 'annotation' }, "Transferred to Product", { role: 'annotation' }, "Abandon in Product Queue", { role: 'annotation' }]);

//       var ProductWisedata = _.groupBy(ReportData, 'product');
//       let columns = [''];
//       Object.keys(ProductWisedata).forEach(function (key) {
//         if (key != '') {
//           columns.push(key);
//           columns.push({ role: 'annotation' });
//         }
//       });
//       ProductWiseTransfer.push(columns);

//       Object.keys(hourlydata).forEach(function (key) {
//         let Answered = _.countBy(hourlydata[key], function (item) {
//           return item.callstatus != "ABANDON";
//         });
//         let ABANDON = _.countBy(hourlydata[key], function (item) {
//           return item.callstatus == "ABANDON";
//         })
//         data.push([key, hourlydata[key].length, hourlydata[key].length, Answered.true ?? 0, Answered.true ?? 0, ABANDON.true ?? 0, ABANDON.true ?? 0]);

//         let TransToProduct = _.countBy(hourlydata[key], function (item) {
//           return item.product != '';
//         });

//         let TransToProductABANDON = _.countBy(hourlydata[key], function (item) {
//           return item.mainQueueCallStatus == "ABANDON" && item.product != '';
//         });

//         TransferToAgentData.push([key, Answered.true ?? 0, Answered.true ?? 0, TransToProduct.true ?? 0, TransToProduct.true ?? 0, TransToProductABANDON.true ?? 0, TransToProductABANDON.true ?? 0]);

//         let values = [key];
//         for (let index = 1; index < columns.length; index++) {
//           values.push(0);
//         }
//         ProductWiseTransfer.push(values);
//       });

//       // ProductWiseTransfer.push(["", "Total",
//       //   { role: "style" },
//       //   { role: 'annotation' }]);


//       // Object.keys(ProductWisedata).forEach(function (key) {
//       //   if (key == 'noinput' || key == '' || key == 'null' || key == null) {

//       //   }
//       //   else {
//       //     ProductWiseTransfer.push([key, ProductWisedata[key].length, "#ffeb3b", ProductWisedata[key].length]);
//       //   }
//       // });





//       Object.keys(ProductWisedata).forEach(function (key) {
//         if (key != '') {

//           var P_hourlydata = _.groupBy(ProductWisedata[key], 'hour');
//           Object.keys(P_hourlydata).forEach(function (item) {

//             // let TransToProductABANDON = _.countBy(ProductWisedata[item], function (item) {
//             //   return item.callstatus == "ABANDON";
//             // });

//             for (let i = 1; i < ProductWiseTransfer.length; i++) {
//               //for (let j = 0; j < columns.length; j++) {
//               if (ProductWiseTransfer[i][0] == item) {
//                 let j = columns.indexOf(key);
//                 //if (columns[j] == key || columns[j] > 0) {

//                 ProductWiseTransfer[i][j] = P_hourlydata[item].length;
//                 ProductWiseTransfer[i][j + 1] = P_hourlydata[item].length;
//                 //}
//               }
//               //}

//             }
//             //ProductWiseTransfer.push(values);

//           });
//         }
//       });


//       debugger;


//       this.setState({ ChartData: data, TransferToAgent: TransferToAgentData, FinalData: ProductWiseTransfer });


//     }
//   }
//   CreateChart(ChartData, title) {
//     let lineChart = null;
//     if (ChartData && ChartData.length > 0) {

//       lineChart = (

//         <Chart
//           chartType={this.state.charttype}
//           data={ChartData}
//           loader={<div>Loading Chart</div>}
//           width="100%"
//           height="200px"
//           options={{
//             title: title,
//             is3D: true,
//             //isStacked: true,
//             chartArea: { width: '80%' },
//             hAxis: {
//               title: ChartData[0][0],
//               minValue: 0,
//             },
//             vAxis: {
//               title: 'Total',
//               minValue: 0,
//             },
//             bar: { groupWidth: '80%' },
//             legend: { position: 'bottom' },
//             animation: {
//               startup: true,
//               easing: 'linear',
//               duration: 1000,
//             }
//           }}
//           legendToggle
//         />

//       )
//       return lineChart;
//     }
//   }
//   handleStartDateChange = (e, props) => {
//     if (e._isAMomentObject) {
//       this.setState({ startdate: e.format("YYYY-MM-DD") }, function () {
        
//       });
//     }

//   }
//   handleEndDateChange = (e, props) => {
//     if (e._isAMomentObject) {
//       this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
        
//       });
//     }
//   }
//   validation = (currentDate) => {
//     return !currentDate.isBefore(moment(this.state.maxdate));
//   };

//   validationEndDate = (currentDate) => {
//     return currentDate.isBefore(moment(new Date()));
//   };

//   render() {
//     const columns = this.columnlist;
//     const { items, PageTitle, UnRegisteredChart, showAlert, AlertMsg, AlertVarient, ReportTime } = this.state;

//     let selectedLeads = [];

//     let AutoAndAnswerChart = this.CreateChart(this.state.ChartData, 'Hourly Call Flow - UnRegistered Queue');
//     let TransferToAgent = this.CreateChart(this.state.TransferToAgent, 'Call Transfer');
//     console.log(this.state.FinalData);
//     let finalChart = this.CreateChart(this.state.FinalData, 'Product Wise - Transfer');
//     let TicketData;//= this.CreateChart(this.state.TicketData, 'Ticket Report');

//     return (
//       <>
//         {/* <div className="content">
//           <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
//           <ToastContainer />
//           <Row>
//             <Col md="12">
//               <Card>
//                 <CardHeader>
//                   <Row>
//                     <Col md={3}>
//                       <CardTitle tag="h4">{PageTitle}</CardTitle>
//                     </Col>
                    
//                     <Col md={2}>

//                       <DropDown
//                         value={this.state.charttype}
//                         col={this.chartList}
//                         onChange={this.chartchange}>
//                       </DropDown>


//                     </Col>
//                     <Col md={2}>
//                       <Datetime value={new Date()}
//                         dateFormat="YYYY-MM-DD"
//                         value={this.state.startdate}
//                         isValidDate={this.validation}
//                         onChange={moment => this.handleStartDateChange(moment)}
//                         utc={true}
//                         timeFormat={false}
//                       />
//                     </Col>
//                     <Col md={2}>
//                       <Datetime value={new Date()}
//                         dateFormat="YYYY-MM-DD"
//                         value={this.state.enddate}
//                         isValidDate={this.validationEndDate}
//                         onChange={moment => this.handleEndDateChange(moment)}
//                         utc={true}
//                         timeFormat={false}
//                       />
//                     </Col>
//                     <Col md={3}>
//                     <Button variant="primary" onClick={() => this.fetchSQLCallBackData()}>Fetch</Button>
//                     &nbsp;
//                     <Button variant="primary" onClick={() => this.fetchCallBackData()}>Today</Button>
//                     </Col>
//                   </Row>

//                 </CardHeader>
//                 <CardBody>
//                   <Row>
//                     <Col md="6">
//                       {AutoAndAnswerChart}
//                     </Col>
//                     <Col md="6">
//                       {TransferToAgent}
//                     </Col>
//                     <Col md="12">
//                       {finalChart}
//                     </Col>

//                   </Row>

//                   <DataTable
//                     columns={columns}
//                     data={(UnRegisteredChart && UnRegisteredChart.length > 0) ? UnRegisteredChart : []}


//                   />
//                 </CardBody>
//               </Card>
//             </Col>
//           </Row>





//         </div> */}
//         <div className="container mt-5">
//           <h2 style={{textAlign: 'center'}}>
//             ( This report is closed. If you still need to use this report, please connect with the dialer team once. )
//           </h2>
//         </div>
//       </>
//     );
//   }
// }


// function mapStateToProps(state) {
//   return {
//     CommonData: state.CommonData
//   };
// }

// export default connect(
//   mapStateToProps,
//   {
//     GetMySqlData,
//     GetCommonData,
//     GetCommonspData
//   }
// )(UnRegisteredChart);