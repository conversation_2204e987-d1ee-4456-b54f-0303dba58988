
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTableV1 from './Common/DataTableWithFilterV1';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import { CompareJson, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject, getAgentId } from '../utility/utility.jsx';
// reactstrap components
import {
  <PERSON>,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  CardTitle,
  Table,
  <PERSON>,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import Moment from "react-moment";

class ProductGrpMapping_Allocation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "ProductGrpMapping",
      PageTitle: "Product Group Mapping",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      prolist: '',
      agentranklist: '',
      groupnamelist: '',
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);
    //this.GroupNamelistchange = this.GroupNamelistchange.bind(this);
    //this.agentranklistchange = this.agentranklistchange.bind(this);
    this.selectedrow = { "Id": 0, "ProductID": null, "NonSelectionAllocation": false, "AllocationStartTime": null, "AllocationEndTime": null}
    this.dateFormatNA = "1970-01-01T00:00:00.000Z";
    this.columnlist = [    
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        config: {
          root: "UserProducts",
          cols: ["DISTINCT PDT.ID AS Id", "PDT.ProductName AS Display"],
          con: [{ "PDT.Isactive": 1 },{ "UBM.IsActive" : 1 },{ "UBM.UserId": getAgentId() }]
        },
        editable: false
      },
      {
        name: "Id",
        label: "Id",
        type: "hidden",
        hide: true,
      },
      {
        name: "Description",
        label: "Description",
        type: "string",
        searchable: true
      },
      {
        name: "GroupID",
        label: "Group",
        type: "autodropdown",
        searchable: true,
        distinct: true,
        config: {
          root: "vwUserGroup",
          cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
          // state: true,
          // statename: "vwUserGroup_product"
        }
      },
      {
        name: "GradeRuleID",
        label: "Grade Rule",
        type: "dropdown",
        searchable: true,
        distinct: true,
        config: {
          root: "AgentRules",
          cols: ["DISTINCT RuleID AS Id", "RuleName AS Display"],
          // state: true,
          // statename: "AgentRules_product"
        }
      },
      {
        name: "NonSelectionAllocation",
        label: "NSA",
        type: "bool"
      },
      {
        name: "AllocationStartTime",
        label: "Start Time",
        type: "allocationTime",
        cell: row => row.AllocationStartTime === this.dateFormatNA && row.AllocationEndTime === this.dateFormatNA ? 'N.A' : <Moment subtract={{hours: 5, minutes: 30}} format="HH:mm:ss">{row.AllocationStartTime}</Moment>
      },
      {
        name: "AllocationEndTime",
        label: "End Time",
        type: "allocationTime",
        cell: row => row.AllocationStartTime === this.dateFormatNA && row.AllocationEndTime === this.dateFormatNA ? 'N.A' : <Moment subtract={{hours: 5, minutes: 30}} format="HH:mm:ss">{row.AllocationEndTime}</Moment>
      },
      {
        name: "CreatedOn",
        label: "CreatedOn",
        type: "datetime",
        hide: true,
      }

    ];
    this.ProductList = {
      config: {
        root: "UserProducts",
        cols: ["DISTINCT PDT.ID AS Id", "PDT.ProductName AS Display"],
        con: [{ "PDT.Isactive": 1 },{ "UBM.IsActive" : 1 },{ "UBM.UserId": getAgentId() }]
      }
    };
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    // this.props.GetCommonData({
    //   limit: 10,
    //   skip: 0,
    //   root: this.state.root,
    //   cols: GetJsonToArray(this.columnlist, "name"),
    //   c: "L", // L for live connection, R for Replica
    // });
    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown" || col.type == "autodropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status && nextProps.CommonData.InsertSuccessData != this.props.CommonData.InsertSuccessData) {
      if (nextProps.CommonData.InsertSuccessData.status != 200) {
        //alert(nextProps.CommonData.InsertSuccessData.error);
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      }
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status && nextProps.CommonData.UpdateSuccessData != this.props.CommonData.UpdateSuccessData) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
      }
    }

    setTimeout(function () {
      this.setState({ showAlert: false, AlertMsg: "", AlertVarient: "" });
    }.bind(this), 2000);
  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
  handleCopy(row) {
    this.setState({ formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {
    if (document.getElementsByName("frmProductGrpMapping_Allocation").length > 0 &&
      document.getElementsByName("frmProductGrpMapping_Allocation")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      // if ( !('InvTypeID' in formvalue) ){
      //   formvalue["InvTypeID"] = 1;
      // }
      if(formvalue.AllocationStartTime === this.dateFormatNA && formvalue.AllocationEndTime === this.dateFormatNA){
        formvalue.AllocationStartTime = null;
        formvalue.AllocationEndTime = null;
      }

      if(formvalue.AllocationStartTime || formvalue.AllocationEndTime){
        if(!formvalue.AllocationStartTime || !formvalue.AllocationEndTime){
          alert("Please provide Start and End time both.");
          return;
        }
        if(formvalue.AllocationStartTime > formvalue.AllocationEndTime) {
          alert("Start Time cannot be greater than End Time");
          return;
        } else if(formvalue.AllocationStartTime === formvalue.AllocationEndTime) {
          alert("Start and End Time cannot be same");
          return;
        }
      }
      formvalue['NonSelectionAllocation'] = true;
      formvalue['InvTypeID'] = 1;
      formvalue['InsurerID'] = 0;

      let id = formvalue["Id"];
      delete formvalue["Id"]
      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);
        if(!formvalue.AllocationStartTime && !formvalue.AllocationEndTime){
          formvalue.AllocationStartTime = null;
          formvalue.AllocationEndTime = null;
        }
        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { "Id": id },
          c: "L",
        });
        let by  = getuser().UserID;
        this.props.addRecord({
          root: "History",
          body: {
            module: "ProductGrpMapping_Allocation",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: by
          }
        });
      } else if (this.state.event == "Copy") {
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        //formvalue["CreatedOn"] = new Date();
        this.fnCleanData(formvalue);
        if(!formvalue.AllocationStartTime && !formvalue.AllocationEndTime){
          formvalue.AllocationStartTime = null;
          formvalue.AllocationEndTime = null;
        }
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
      } else {
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        //formvalue["CreatedOn"] = new Date();

        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
      }
      let productid = formvalue["ProductID"];

      setTimeout(function () {

        if (productid) {
          this.props.GetCommonData({
            root: this.state.root,
            cols: GetJsonToArray(this.columnlist, "name"),
            c: "L",
            con: [{ "ProductID": productid }],
          });
        }
        else {
          this.props.GetCommonData({
            limit: 10,
            skip: 0,
            root: this.state.root,
            cols: GetJsonToArray(this.columnlist, "name"),
            c: "L",
          });
        }
      }.bind(this), 2000);
      this.setState({ showModal: false });
    }
    return false;
  }
  handleChange = (e, props) => {
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    // else if (e._isAMomentObject) {
    //   formvalue[props] = e.format()
    // }
    else {
      formvalue[e.target.id] = e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }
  productchange(e, props) {
    this.setState({ prolist: e.target.value });

    // this.props.GetCommonData({
    //   root: "vwUserGroup",
    //   cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
    //   con: [{ "ProductId": e.target.value }]
    // });

    // this.props.GetCommonData({
    //   root: "AgentRules",
    //   cols: ["RuleID AS Id", "RuleName AS Display"],
    //   con: [{ "ProductId": e.target.value }]
    // });
    // setTimeout(function () {
      this.props.GetCommonData({
        root: this.state.root,
        cols: GetJsonToArray(this.columnlist, "name"),
        c: "L",
        con: [{ "ProductID": e.target.value }],
      });
    //}.bind(this), 1000);


  }
  // GroupNamelistchange(e, props) {
  //   this.setState({ groupnamelist: e.target.value });
  //   this.props.GetCommonData({
  //     limit: 10,
  //     skip: 0,
  //     root: this.state.root,
  //     cols: GetJsonToArray(this.columnlist, "name"),
  //     c: "L",
  //     con: [{ "GroupID": e.target.value }, { "ProductID": this.state.prolist }],
  //   });
  // }
  //   agentranklistchange(e, props) {
  //     console.log(e.target.value);
  //     this.setState({ agentranklist: e.target.value });
  //     console.log(this.state.leadranklist + this.state.agentranklist + this.state.prolist);
  //     this.props.GetCommonData({
  //       limit: 10,
  //       skip: 0,
  //       root: this.state.root,
  //       cols: GetJsonToArray(this.columnlist, "name"),
  //       c: "L",
  //       con: [{ "ProductID": this.state.prolist }, { "AgentRank": e.target.value }, { "LeadRank": this.state.leadranklist }],
  //     });
  //   }

  fnRenderfrmControl(col, formvalue, handleChange, event) {

    // if (col.name == "GroupID") {
    //   if (formvalue && formvalue.ProductID) {
    //     col.config = {
    //       root: "vwUserGroup",
    //       cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
    //       con: [{ "ProductId": formvalue.ProductID }]
    //     }
    //   }else{
    //     col.config = {
    //       root: "vwUserGroup",
    //       cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display"],
    //     }
    //   }
    // }

    // if (col.name == "GradeRuleID") {
    //   if (formvalue && formvalue.ProductID) {
    //     col.config = {
    //       root: "AgentRules",
    //       cols: ["RuleID AS Id", "RuleName AS Display"],
    //       con: [{ "ProductId": formvalue.ProductID }]
    //     }
    //   }else{
    //     col.config = {
    //       root: "AgentRules",
    //       cols: ["RuleID AS Id", "RuleName AS Display"],
    //     }
    //   }
    // }


    return fnRenderfrmControl(col, formvalue, handleChange, event)

  }



  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event } = this.state;
    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={7}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="product_dropdown">
                        <DropDown firstoption="Select Product" col={this.ProductList} onChange={this.productchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>

                    <Col md={2}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTableV1
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmProductGrpMapping_Allocation">
                <Row>
                  {this.columnlist.map(col => {
                    if(col.name === "NonSelectionAllocation"){ return null; }
                    return this.fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  })}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
  }
)(ProductGrpMapping_Allocation);