var express = require("express");
const router = express.Router();
const controller = require("./ApiCommController");
const { ACLlayer } = require("../ACLlayer");


router.get('/AddLeadValidation', ACLlayer,controller.AddLeadValidation);
router.post('/ValidateAddLeadToPriorityQueue', ACLlayer,controller.ValidateAddLeadToPriorityQueue);
router.post("/GetInvalidLeads", ACLlayer,controller.GetInvalidLeads);
router.post("/RemoveInvalidLeads", ACLlayer ,controller.RemoveInvalidLeads);

module.exports = router;
