import React from 'react';
import PropTypes from 'prop-types';
import DataTable from "react-data-table-component";

const ViewAttributes = ({items}) => {
    
    return (Object.keys(items).map(key => 
            (<div key={key} className="view-attribute">
                <div>
                    {key}
                </div>
                <div>
                    {items[key]}
                </div>
            </div>))
    );
};

ViewAttributes.defaultProps = {
    items: {},
};

ViewAttributes.propTypes = {
    items: PropTypes.object,
};



export const ViewOutcomes = (outcomes) => {
        let events = [];
    
        if(outcomes !== null && outcomes.items !== undefined &&    
                outcomes.items !== null && outcomes.items.events !== undefined) {    
            events = outcomes.items.events;    
        }
    
    
        let data = [];   
        for (let index = 0; index < events.length; index++) {    
            const item = events[index];    
            let obj = {    
                type : item.type,    
                expression : item.params.EXPRESSION,    
                calculated : item.params.value    
            }
    
            data.push(obj);    
        }
    
    
        let OutcomeColumns = [    
            {    
                name: 'Type',    
                selector: "type",    
            },
    
            {    
                name: 'Expression',    
                selector: "expression",    
            },
    
            {    
                name: 'Calculated Result',    
                selector: "calculated",    
            }    
        ];
    
    return (
        <DataTable

        columns={OutcomeColumns}

        data={data}

        striped

        highlightOnHover

        progressPending={false}

        title={"Resolved Results"}

    />
    );
};

ViewOutcomes.defautProps = {
    outcomes: [],
};

ViewOutcomes.propTypes = {
    outcomes: PropTypes.array,
};

export default ViewAttributes;
