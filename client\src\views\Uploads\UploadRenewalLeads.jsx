import Axios from "axios";
import React from "react";
import config from "../../config.jsx";
import { ButtonGroup, Button, Modal, Form } from "react-bootstrap";
import { getuser } from "utility/utility";
import { GetCommonspDataV2} from "../../store/actions/CommonAction";
import { GetCommonData } from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import DataTable from "../Common/DataTableWithFilter";
import { fnBindRootData, fnDatatableCol } from "../../utility/utility.jsx";
import { CardTitle, Row, Col } from "reactstrap";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Link } from "react-router-dom";
import Moment from "react-moment";

const uuid = require("uuid");
let AxiosInstanceUpload = Axios.create({
  baseURL: "",
  header: {},
  withCredentials: true
});

class UploadRenewalLeads extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      showErrorConsole: false,
      showGrid: true,
      items: [],
      Uploadeditems: [],
      ErrorLogitems: [],
      root: "GetRenewalUploadsData",
      UploadFileDetails: "GetRenewalBulkUploadFileDetailsData",
      ErrorConsoleCollection: "FileProcessingErrorLogs",
      PageTitle: "UploadRenewalLeads",
      formvalue: {},
      event: "",
      DownloadedFile: "",
      ModalValueChanged: false,
      ResetTokenData: "",
      selectedFile: null,
      selectedProduct: localStorage.getItem('UploadRenewalLeadsPrdId') || '2',
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleErrorConsoleClose = this.handleErrorConsoleClose.bind(this);
    this.columnlist = [
      {
        name: "LeadID",
        label: "LeadID",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
      },
      {
        name: "ErrorMessage",
        label: "StatusMessage",
        type: "string",
        cell: (row) => (
          <div>
            {row.ErrorMessage ? (
              <div data-toggle="tooltip" title={row.ErrorMessage}>
                {" "}
                {row.ErrorMessage}
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        editable: false,
        sortable: true,
      },
      {
        name: "Name",
        label: "Name",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Email",
        label: "Email",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "AltEmailID",
        label: "AltEmailID",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "MobileNo",
        label: "MobileNo",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "PlanID",
        label: "PlanID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PlanName",
        label: "PlanName",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "OldPolicyNo",
        label: "OldPolicyNo",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "SupplierID",
        label: "SupplierID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "Insurer",
        label: "Insurer",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "FirstYrPremium",
        label: "FirstYrPremium",
        type: "decimal",
        editable: false,
        sortable: true,
      },
      {
        name: "SumInsured",
        label: "SumInsured",
        type: "decimal",
        editable: false,
        sortable: true,
      },
      {
        name: "IsPreviousClaimsTaken",
        label: "IsPreviousClaimsTaken",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyStartDate",
        label: "PolicyStartDate",
        type: "datetime",
        editable: false,
        sortable: true,
      },
      {
        name: "PolicyExpiryDate",
        label: "PolicyExpiryDate",
        type: "datetime",
        editable: false,
        sortable: true,
      },
      {
        name: "GracePeriod",
        label: "GracePeriod",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "AltContatNumber",
        label: "AltContactNumber",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "Address",
        label: "Address",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "CustomerID",
        label: "CustomerID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PlanTerm",
        label: "PlanTerm",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "UPSellSI",
        label: "UPSellSI",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "UpSellSIPremium",
        label: "UpSellSIPremium",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "AddOnProduct",
        label: "AddOnProduct",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "AssignToUserID",
        label: "AssignToUserID",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "NCB",
        label: "NCB",
        type: "decimal",
        editable: false,
        sortable: true,
      },
      {
        name: "FreeHealthCheckup",
        label: "FreeHealthCheckup",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "AdditionalBenefits",
        label: "AdditionalBenefits",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "SpecificDiseaseWaitingPeriod",
        label: "SpecificDiseaseWaitingPeriod",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "PreExistingDiseaseWaitingPeriod",
        label: "PreExistingDiseaseWaitingPeriod",
        type: "number",
        editable: false,
        sortable: true,
      },
      {
        name: "BatchID",
        label: "BatchID",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "ComboProduct",
        label: "ComboProduct",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "Remarks",
        label: "Remarks",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "WasPort",
        label: "WasPort",
        type: "bool",
        editable: false,
        sortable: true,
      },
      {
        name: "OverrideRecords",
        label: "OverrideRecords",
        type: "int",
        editable: false,
        sortable: true,
      },
      {
        name: "PremiumInflationReason",
        label: "PremiumInflationReason",
        type: "string",
        editable: false,
        sortable: true,
      },
      {
        name: "EnrollmentDate",
        label: "EnrollmentDate",
        type: "datetime",
        editable: false,
        sortable: true,
      },
      {
        name: "UpsellFreshLead",
        label: "UpsellFreshLead",
        type: "string",
        editable: false,
        sortable: true,
      }
    ];
    this.UploadedFilecolumnlist = [
      {
        name: "Filename",
        label: "File Name",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
        width: "250px",
      },
      {
        name: "Status",
        label: "Status",
        type: "string",
        editable: false,
        sortable: true,
        searchable: true,
        width: "130px",
      },
      {
        name: "createdon",
        label: "UploadedOn",
        type: "datetime",
        editable: false,
        cell: (row) =>
          row.createdon == null ? (
            "N/A"
          ) : (
            <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
              {row.createdon}
            </Moment>
          ),
        format: "DD/MM/YYYY HH:mm:ss",
        sortable: true,
        searchable: true,
        width: "150px",
      },
      {
        name: "UploadedBy",
        label: "UploadedBy",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "250px",
      },
      {
        name: "UniqueId",
        label: "UniqueId",
        type: "string",
        hide: true,
        editable: false,
        sortable: true,
        searchable: true,
      },
      {
        name: "GroupId",
        label: "GroupId",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: true,
      },
      {
        name: "AssignedUser",
        label: "AssignedUser",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: true,
      },
      {
        name: "ProcessType",
        label: "ProcessType",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      },
      {
        name: "SMEProcessType",
        label: "SMEProcessType",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      },
      {
        name: "ProductId",
        label: "ProductId",
        type: "string",
        hide: true,
        editable: false,
        sortable: false,
        searchable: false,
      }
    ];
    this.ErrorConsolecolumnlist = [
      {
        name: "TrackingId",
        label: "TrackingId",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "250px",
      },
      {
        name: "Createon",
        label: "CreatedOn",
        type: "datetime",
        editable: false,
        cell: (row) =>
          row.Createon == null ? (
            "N/A"
          ) : (
            <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
              {row.Createon}
            </Moment>
          ),
        format: "DD/MM/YYYY HH:mm:ss",
        sortable: true,
        searchable: true,
        width: "150px",
      },
      {
        name: "Exception",
        label: "Error",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "500px",
      },
      {
        name: "PrimaryColumn",
        label: "PrimaryColumn",
        type: "string",
        editable: false,
        sortable: false,
        searchable: true,
        width: "150px",
      }
    ];
    //document.getElementById("btnsave").disabled = 'false';
  }
  // On file select (from the pop up)	// Update the state
  onFileChange = (event) => {
    this.setState({ selectedFile: event.target.files[0] });
    //this.setState({uniqueId :uuid.v1().toUpperCase()});
  };
  //On Product Change
  handleProductChange = (event) => {
    this.setState({ selectedProduct: event.target.value });
    this.BindGridOnSaveFile(event.target.value);
    var type = "";
    if (event.target.value == "2") {
      type = "HealthRenewalsWithCJDataSample";
    }
    if (event.target.value == "117") {
      type = "MotorBulkLeadCreateSample";
    }
    if (event.target.value == "106") {
      type = "healthRenewalsSample";
    }
    if (event.target.value == "118") {
      type = "healthRenewalsSample";
    }
    if (event.target.value == "130") {
      type = "healthRenewalsSample";
    }
    if (event.target.value == "101") {
      type = "renewalhome";
    }
    this.setState({
      DownloadedFile: "../../SampleExcelfiles/" + type + ".xlsx",
    });
    this.setState({ selectedFile: null });
    localStorage.setItem('UploadRenewalLeadsPrdId', event.target.value);
  };
  // On file upload
  onFileUpload = () => {
    //document.getElementById("btnsave").disabled = 'true';
    if (
      this.state.selectedFile &&
      this.isExcel(this.state.selectedFile.name)
    ) {

      var uniqueId = uuid.v1().toUpperCase();
      var Uploadedby = getuser().UserID;
      var processtype = 2; //Create/Upload
      if (document.getElementById("chkupdate") && document.getElementById("chkupdate").checked) {
        processtype = 1; //Update
      }
      const data = {
        FileName: this.state.selectedFile.name.trim(),
        Processtype: processtype,
        UniqueId: uniqueId.trim(),
        productId: this.state.selectedProduct.trim(),
        Status: "Saved",
        Uploadedby: Uploadedby.trim(),
        GroupId: "",
        AssignedUser: "",
        SMEProcessType: "",
        UploadedFile: this.state.selectedFile,
        folderName: "Renewal"
      };

      var url = "api/UploadFile/UploadFileToS3BucketPvt"
      this.commonuploadFileToUrlService(data, url)
        .then((result) => {
          if (result.Data == "") {
            toast("Error in uploading file to S3", { type: "error" });
          }
          else {
            toast("File Saved successfully...", { type: "success" });
            this.BindGridOnSaveFile(this.state.selectedProduct);
            this.setState({ selectedFile: null });
            window.location.reload();
          }
        })
        .catch((err) => {
          toast("Error in saving file", { type: "error" });
        });
    } else {
      toast("Please select a valid excel file", { type: "error" });
    }
  };
  

  onFileProcessing = (
    trackingid,
    processtype,
    GroupId,
    AssignedUser,
    ProductId
  ) => {
    const data = {
      ProcessType: processtype,
      UniqueId: trackingid,
      UploadedBy: getuser().UserID,
      ProductId: ProductId,
      AssignedGroupId: GroupId,
      AssignedUserId: AssignedUser,
      ProcessId: "0"
    };
    if (this.state.selectedFile !== "") {
      this.ProcessuploadedFile(
        data,
        "api/UpdateRenewalLeads/ProcessRenewalLeads"
      )
        .then((result) => {
          this.BindGridOnSaveFile(ProductId);
          if (result.includes("Success")) {
            toast(result, { type: "success" });
          } else {
            toast(result, { type: "error" });
          }
        })
        .catch((result) => {
          this.BindGridOnSaveFile(ProductId);
          toast("File not Staged..." + result, { type: "error" });
        });
    } else {
      toast("File not selected", { type: "error" });
    }
  };
  componentDidMount() {
    var type = "HealthRenewalsWithCJDataSample";
    if (this.state.selectedProduct && this.state.selectedProduct == "117") {
      type = "MotorBulkLeadCreateSample";
    }
    this.setState({
      DownloadedFile: "../../SampleExcelfiles/" + type + ".xlsx",
    });
    this.BindGridOnSaveFile(this.state.selectedProduct);
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({
        Uploadeditems: nextProps.CommonData[this.state.UploadFileDetails],
      });
      this.setState({ ErrorLogitems: nextProps.CommonData[this.state.ErrorConsoleCollection] });
    }
  }
  onShowUploadedData = (trackingId) => {
    this.state.showModal = true;
    this.columnlist.map((col) => fnBindRootData(col, this.props));
    let UploadedBy = (getuser().UserID).trim();
    //let UploadedBy = 90368;
    let Source = "DumpUpload".trim();
    let TrackingId = trackingId.trim();
    this.props.GetCommonspDataV2({
      root: this.state.root,
      c: "L",
      params: [
        { Source: Source, TrackingId: TrackingId },
      ],
    });
  };
  onShowErrorConsole = (trackingId) => {
    this.state.showErrorConsole = true;
    this.ErrorConsolecolumnlist.map((col) => fnBindRootData(col, this.props));
    let TrackingId = trackingId.trim();
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.ErrorConsoleCollection,
      con: { 'TrackingId': TrackingId },
      c: "L"
    });
  };
  BindGridOnSaveFile = (Productid) => {
    this.state.showGrid = true;
    this.UploadedFilecolumnlist.map((col) => fnBindRootData(col, this.props));
    let UploadedBy = (getuser().UserID).trim();
    //let UploadedBy = 90368;
    let ProductId = (Productid).trim();
    this.props.GetCommonspDataV2({
      root: this.state.UploadFileDetails,
      c: "L",
      params: [{ ProductId: ProductId }],
    });
  };
  fnDatatableCol2() {
    var ErrorLogscolumns = fnDatatableCol(this.ErrorConsolecolumnlist);
    return ErrorLogscolumns;
  }
  fnDatatableCol1() {
    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }
  fnDatatableColsUploaded() {
    var UploadedColumn = fnDatatableCol(this.UploadedFilecolumnlist);
    UploadedColumn.splice(2, 0, {
      name: "Stage Data",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            id="btnstaging"
            className="btn btn-success btnSmall"
            disabled={
              row.Status == "DataStaged" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured"|| row.Status == "DateoutofLimit"
            }
            onClick={() => this.handleEdit(row)}
          >
            Start Staging
          </Button>
        </ButtonGroup>
      ),
    });
    UploadedColumn.splice(3, 0, {
      name: "View Staged Data",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            className="btn btn-primary btnSmall"
            disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "ErrorOccured"|| row.Status == "DateoutofLimit"}
            onClick={() => this.handleShowUploaded(row)}
          >
            View Staged Data
          </Button>
        </ButtonGroup>
      ),
    });
    UploadedColumn.splice(4, 0, {
      name: "View Error Console",
      width: "150px",
      cell: (row) => (
        <ButtonGroup aria-label="Basic example">
          <Button
            className="btn btn-danger btnSmall"
            disabled={row.Status == "Saved" || row.Status == "DataUnderProcessing" || row.Status == "DataStaged"|| row.Status == "DateoutofLimit"}
            onClick={() => this.handleShowErrorConsole(row)}
          >
            Error Console
          </Button>
        </ButtonGroup>
      ),
    });

    return UploadedColumn;
  }

  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onFileProcessing(
      row.UniqueId,
      row.ProcessType,
      row.GroupId,
      row.AssignedUser,
      row.ProductId
    );
  }
  handleShowUploaded(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onShowUploadedData(row.UniqueId);
  }
  handleShowErrorConsole(row) {
    this.setState({ od: Object.assign({}, row, {}) });
    this.onShowErrorConsole(row.UniqueId);
  }

  handleClose() {
    this.setState({ showModal: false });
  }
  handleErrorConsoleClose() {
    this.setState({ showErrorConsole: false });
  }
  getExtension(filename) {
    var parts = filename.split(".");
    return parts[parts.length - 1];
  }

  isExcel(filename) {
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case "xls":
      case "xlsx":
        return true;
      default:
        return true;
    }
  }

  commonuploadFileToUrlService = (requestData, url) => {
    const input = {
      url,
      method: "POST",
      service: "commonUploadFileToUrl",
      requestData,
    };
    let a = this.CALL_API(input);
    return a;
  };
  ProcessuploadedFile = (requestData, url) => {
    const input = {
      url,
      method: "POST",
      service: "ProcessingRenewalFile",
      requestData,
    };
    let a = this.CALL_API(input);
    return a;
  };

  CALL_API = async (input) => {
    let Token = "cG9saWN5 YmF6YWFy";

    // Set defaults
    input.timeout = input.timeout || 6000; // timeout s/m/l/(number of millisec)
    input.method = input.method || "GET";
    input.cache = input.cache || false;
    input.service = input.service || "core";
    let URL, Headers;
    //Set service and url
    debugger;
    switch (input.service) {
      case "commonUploadFileToUrl":
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          ...input.headers,
          "content-Type": "multipart/form-data",
          AgentId: getuser().UserID,
          //AgentId: 90368,
          source: "dashboard",
          "Access-Control-Allow-Origin": "*",
        };

        if (input.requestData) {
          let formData = new FormData();
          Object.keys(input.requestData).forEach((key) => {
            formData.append(key, input.requestData[key]);
          });
          input.formData = formData;
          input.requestData = null;
        }
        break;
      case "ProcessingRenewalFile":
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          ...input.headers,
          "content-Type": "multipart/form-data",
          //AgentId: 90368,
          AgentId: (getuser().UserID).trim(),
          source: "dashboard", //"dialer",
          "Access-Control-Allow-Origin": "*",
        };
        if (input.requestData) {
          let formData = new FormData();
          Object.keys(input.requestData).forEach((key) => {
            formData.append(key, input.requestData[key]);
          });
          input.formData = formData;
          input.requestData = null;
        }
        break;
      default:
        URL = config.api.MatrixCoreURL + input.url;
        Headers = {
          "Content-Type": "application/json",
        };
        break;
    }
    let timeout = 6000;
    if (!timeout) {
      try {
        timeout = !isNaN(parseInt(input.timeout))
          ? parseInt(input.timeout)
          : 3000;
      } catch {
        timeout = 3000;
      }
    }

    // Prepare reqData
    var reqData = {
      method: input.method,
      url: URL,
      headers: Headers,
      cache: input.cache,
      timeout,
    };
    //// ;
    // Add payload if provided
    if (input.requestData !== undefined) {
      reqData.data = JSON.stringify(input.requestData);
    }
    if (input.formData) {
      reqData.data = input.formData;
    }
    return new Promise((resolve, reject) => {
      AxiosInstanceUpload(reqData)
        .then((res) => {
          // ;
          if (input.acceptOnly200 === true && res.status !== 200) {
            reject(res.statusText);
            return;
          }
          //console.log(URL, res.data);
          resolve(res.data);
        })
        .catch((error) => {
          // ;
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            reject(error.response);
          } else if (error.request) {
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest
            reject(error.request);
          } else {
            // Something happened in setting up the request that triggered an Error
            //console.log('Error', error.message);
            reject(error.message);
          }
          //console.log(error.config);
        });
    });
  };
  fileData = () => {
    if (this.state.selectedFile) {
      return (
        <div>
          <h2>File Details:</h2>
          <p>File Name: {this.state.selectedFile.name}</p>
          <p>File Type: {this.state.selectedFile.type}</p>
          <p>
            Last Modified:{" "}
            {this.state.selectedFile.lastModifiedDate.toDateString()}
          </p>
        </div>
      );
    } else {
      return (
        <div>
          <br />
          <h4>Choose before Pressing the Upload button</h4>
        </div>
      );
    }
  };
  renderDownloadFile() {
    if (this.state.DownloadedFile) {
      return (
        <Link to={this.state.DownloadedFile} target="_blank" download>
          Download Sample Excel File
        </Link>
      );
    }
  }

  render() {
    const columns = this.fnDatatableCol1();
    const UploadedColumn = this.fnDatatableColsUploaded();
    const ErrorlogsColumn = this.fnDatatableCol2();
    const {
      Uploadeditems,
      items,
      ErrorLogitems,
      showModal,
      showErrorConsole,
      showGrid,
    } = this.state;
    return (
      <div className="content">
        <div class="container-fluid">
          <Row>
            <Col md={3}>
              <CardTitle tag="h4" className="mb-4">Upload Data</CardTitle>
            </Col>
          </Row>
          <Row>
            <Col md={4}>
              <div className="form-group">
                <label htmlFor="criteria" className="form-label">
                  *Product
                </label>
                <select
                  className="form-select"
                  value={this.state.selectedProduct}
                  onChange={this.handleProductChange}
                >
                  <option value="2">Health</option>
                  <option value="117">Motor</option>
                  <option value="106">CriticalIllness</option>
                  <option value="118">PersonalAccident</option>
                  <option value="130">SuperTopUp</option>
                  <option value="101">Home</option>
                </select>
              </div>
            </Col>
            <Col md={(this.state.selectedProduct != 117) ? 4 : 8}>
              <label className="form-label"> * Raw Data File :</label>
              <br />
              <input type="file" onChange={this.onFileChange} />
            </Col>
            {
              (this.state.selectedProduct != 117) &&
              <div className="form-group col-md-4 mt-4">
                <label className="form-label" htmlFor="chkupdate">
                  {" "}
                  Update Lead data &nbsp;{" "}
                </label>
                <input
                  type="checkbox"
                  id="chkupdate"
                  name="chkupdate"
                  value=" Update Lead data"
                />
              </div>
            }

            <div className="col-md-4 mt-3 mb-2">{this.renderDownloadFile()}</div>
            <Col md={4}>
              <button
                type="submit"
                id="btnsave"
                onClick={this.onFileUpload}
                className="btn btn-primary"
              >
                Save
              </button>
            </Col>
          </Row>
          <>
            <div show={showGrid} className="content">
              <Form className="uploadRelewalLeadTable">
                <DataTable
                  columns={UploadedColumn}
                  data={
                    Array.isArray(Uploadeditems) && Uploadeditems.length > 0
                      ? Uploadeditems[0]
                      : []
                  }
                />
              </Form>
            </div>
          </>
          <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={showModal}
                onHide={this.handleClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Uploaded Leads</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                    <DataTable
                      columns={columns}
                      data={
                        Array.isArray(items) && items.length > 0 ? items[0] : []
                      }
                    />
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={this.handleClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
          <>
            <div className="content">
              <ToastContainer />
              <Modal
                show={showErrorConsole}
                onHide={this.handleErrorConsoleClose}
                dialogClassName="modal-90w"
              >
                <Modal.Header closeButton>
                  <Modal.Title>Error Console</Modal.Title>
                </Modal.Header>
                <Modal.Body className="uploadLeadPopup">
                  <Form>
                    <DataTable
                      columns={ErrorlogsColumn}
                      data={
                        Array.isArray(ErrorLogitems) && ErrorLogitems.length > 0 ? ErrorLogitems : []
                      }
                    />
                  </Form>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="secondary" onClick={this.handleErrorConsoleClose}>
                    Close
                  </Button>
                </Modal.Footer>
              </Modal>
            </div>
          </>
        </div>
      </div>
    );
  }
}
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}
//export default UploadRenewalLeads;
export default connect(mapStateToProps, {
  GetCommonData,
  GetCommonspDataV2
})(UploadRenewalLeads);