import React, { useState } from 'react'
import { Form } from 'react-bootstrap'
import { Col, Row } from 'reactstrap'
import { ALL, SECONDARY, ISADMIN, NavBarButtonsSlider } from './Utility';

function NavbarMobileView(props) {

  const [FilterSelected, setFilterSelected] = useState(0)
  const { MobileViewProps } = props;
  const {
    AllBookingsCount,
    AllBookingsCountSecondary,
    ActiveTabParent,
    BadgeCounter,
    BadgeCounterSecondary,
    FilterProductId
  } = MobileViewProps;

  let SliderProductkey = "", SliderFilters = [];

  const HandleBookingsFilterChange = (e) => {
    const value = parseInt(e.target.value);
    if(value === ALL || value === SECONDARY) {
      setFilterSelected(0)
    } else {
      setFilterSelected(value)
    }
     
    props.toggleNavButtons(value, '');
  }

  return (
    <div className="NavBarMObileView">
      <Row>
        <Col md={6} sm={6} xs={6}>
          <Form.Select aria-label="Default select" onClick={HandleBookingsFilterChange}>
            <option key={'primary'} value={ALL}>My Booking ({AllBookingsCount})</option>
            <option key={'secondary'} value={SECONDARY}>Secondary Booking ({AllBookingsCountSecondary})</option>
          </Form.Select>
        </Col>
        <Col md={6} sm={6} xs={6}>
          <Form.Select aria-label="Default select" onChange={HandleBookingsFilterChange} value={FilterSelected}>
          <option key={'select'} value={0}>Select</option>
            {
              SliderFilters && SliderFilters.length > 0 && SliderFilters.map((item, index) => {
                let statusType = item.BookingStatusType;
                let BadgeCounterCurrent = ActiveTabParent === 'Primary' ? BadgeCounter : BadgeCounterSecondary;
                let Badge = BadgeCounterCurrent && BadgeCounterCurrent.length > 0 && BadgeCounterCurrent.filter(item => item.BookingStatusType == statusType) || {};
                if (statusType != 100) {
                  return<>  <option key={index} value={item.id}> {item.text} ({Badge.length > 0 && Badge[0].Count || 0}) </option> <span className="badge bg-primary"> {Badge.length > 0 && Badge[0].Count || 0}</span> </>
                }

              })
            }
          </Form.Select>
        </Col>

        <Col xs={12} sm={12}>
          <div className="form-group has-search">
            <span className="fa fa-search form-control-feedback"></span>
            <input
              type="text"
              className="form-control"
              placeholder="Search BookingID, Customer Name …"
              value={props.value}
              onChange={props.handleChangeDebounceSearchText}
            />
          </div>
        </Col>
      </Row>

    </div>
  )
}

export default NavbarMobileView
