const {Kafka}= require('kafkajs');

const produceMessage=async (producer, topic, message, timeout)=>{
    await producer.connect();
    const result= await producer.send({
        topic,
        messages: [message],
        timeout: timeout
      });
    
    // console.log("The result is ", result);

    result
    .filter((record) => record.hasError)
    .forEach((record) => {
      // console.error(`Error for ${record.topic}:${record.partition} - ${record.value}`);
      });
    await producer.disconnect();

}

const LogKafka=async (data)=>{

    const AWSSecretConfig = global.SecretConfig;
   
    if(AWSSecretConfig && data){
      let KafkaConfig= AWSSecretConfig.Kafka_Server;
      let KafkaLogger= AWSSecretConfig.Kafka_Logger;
      if(KafkaConfig)
      {
        let kafkaBrokers= KafkaConfig.KafkaBootstrapServers.split(',');

        try{
        // console.log("KafkaBrokers ", kafkaBrokers);
        // console.log("KafkaUserName ", KafkaConfig.KafkaUserName);
        // console.log("KafkaPassword ", KafkaConfig.KafkaPassword);
        const kafka= new Kafka({
    
          brokers: kafkaBrokers,
          sasl:{
            mechanism:'plain',
            username: KafkaConfig.KafkaUserName,
            password: KafkaConfig.KafkaPassword
          },
          retry:{
            retries:2
          }
        });
        const producer= kafka.producer();
        
        const topic=KafkaLogger.Topic;
    
      
    
        const payload={
          ConnectionName:KafkaLogger.ConnectionName,
          DbName:KafkaLogger.DbName,
          CollectionName:KafkaLogger.CollectionName,
          RequestPayload: JSON.stringify(data)
        }
    
        // console.log("The payload is ", payload);
    
        const message={
          value: JSON.stringify(payload)
        }
    
        await produceMessage(producer, topic, message, 5000);
        return {
          errorStatus: 0
        }
    
        }
        
        
        catch(e){
          console.log("Kafka log error: ",e);
          return {
            errorStatus:1
          }
        }
        
      }

      else{
        console.log("Unable To Fetch Kafka Broker Credentials");
        return {
          errorStatus:1
        }
      }
    }
    else{
      console.log("Unable to fetch AWS Secrets");
      return {
        errorStatus: 1
      }
    }
  

}

module.exports={
    LogKafka: LogKafka
}