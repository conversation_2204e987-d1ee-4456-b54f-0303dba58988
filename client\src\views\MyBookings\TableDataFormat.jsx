import React from "react";
import { getDifferenceInMinFromNow } from "utility/utility";
import { VERIFICATION_ADDED_LEADS } from "./Utility";
import { ListItemAvatar } from "@mui/material";
import { NoteAddOutlined, WysiwygOutlined } from "@mui/icons-material";

const VERIFICATION_BUTTON_DISABLE_TIME = 60; // in minutes
const VERIFICATION_QUEUE_PRODUCTS = [2, 118, 106, 130]; // health products

const CheckLeadAddedToVerificationQueue = (booking, data) => {
  try {
    if (
      booking.ProductID &&
      !VERIFICATION_QUEUE_PRODUCTS.includes(booking.ProductID)
    ) {
      return false;
    }
    const foundElement = data.find(
      (element) => element.LeadId === booking.BookingID
    );
    let diff = getDifferenceInMinFromNow(foundElement.createdAt);
    if (diff < VERIFICATION_BUTTON_DISABLE_TIME) {
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
};
const queueLeadsLocal =
  JSON.parse(localStorage.getItem(VERIFICATION_ADDED_LEADS)) || [];

const TableDataFormat = (props) => {
  let {
    item,
    ModifiedColumnData,
    data,
    index,
    Index,
    RoleId,
    tableDataStates,
    ProcessID
  } = props;
  const dependents = item.dependents || [];

  const leadsAddedToQueue =
    tableDataStates?.verificationQueueInfo || queueLeadsLocal;
  const IsLeadAddedToVerificationQueue = CheckLeadAddedToVerificationQueue(
    data,
    leadsAddedToQueue
  );

  let dependentRow =
    (Array.isArray(dependents) && dependents.length > 0) || false;
  let isValidRow = true;
  for (let i = 0; i < dependents.length; i++) {
    const dependent = dependents[i];
    const key = dependent.key || "";
    const value = dependent.value || "";
    if (data[key] !== value) {
      isValidRow = false;
    }
  }

  if (index === 0) {
    return <td data-title={item.name}>{Index}</td>;
  }

  if (RoleId === 13 && item.name === "SalesAgent") {
    return null;
  } else {
    let value = "-",
      text = "-";
    switch (item.type) {
      case "link":
        value = data[item.accessor] || null;

        // if (!isValidRow) {
        //   return <td data-title={item.name}>{item.show ? item.text: "-"}</td>;
        // }
        text = IsLeadAddedToVerificationQueue ? item.successText : item.text;
        let disabled = IsLeadAddedToVerificationQueue;

        return (
          <td data-title={item.name}>
            {dependentRow && (
              <>
               {disabled && <>{item.show ? value : text} </>}
                {!disabled && (
                  <>
                    {isValidRow ? (
                      <a
                        href="#"
                        onClick={(e) => {
                          props.handleLinkClick(e, data, item);
                        }}
                      >
                        {item.show ? value : text}
                      </a>
                    ) : (
                      <>{'-'} </>
                    )}
                  </>
                )}
              </>
            )}

            {!dependentRow && (
              <a
                href="#"
                onClick={(e) => {
                  props.handleLinkClick(e, data, item);
                }}
              >
                {item.show ? value : text}
              </a>
            )}
          </td>
        );

      case "iframe":
        if (item.subtype === "upload") {
          return (
            <td data-title={item.name}>
              <a
                href="#"
                className="uploadDoc"
                onClick={(e) => {
                  props.handlePopUp(e, data, item);
                }}
              >
                <i className="fa fa-cloud-upload" aria-hidden="true"></i>{" "}
                {item.name}
              </a>
            </td>
          );
        }
        else if( item.subtype==="optForWA"){
         
       
          return(
            <td data-title={item.name}>
              <a
              href="#"
              className="uploadDoc"
              // id={`fos_appointment|${data.BookingID}|${data.CustomerID}`}
              onClick={(e)=>props.handleOptWAClick(e,data)}
              >
              <i className="fa fa-whatsapp" aria-hidden="true"></i>
              {/* <i className="fa fa-cloud-upload" aria-hidden="true"></i>{" "} */}
              {item.name}
              </a>
            </td>
          )
            
            
        }
        else if( item.subtype==="AddMom"){
          return (
            <td data-title={item.name}>
              <a
                href="#"
                className="uploadDoc"
                onClick={(e) => props.handleOpenAddMomModal(e, data)}
              >
                <NoteAddOutlined style={{ fontSize: "16px", marginRight: "5px" }} /> {/* Add the icon */}
                {item.name}
              </a>
            </td>
          );
        }
        else if( item.subtype==="ViewMom"){
          return (
            <td data-title={item.name}>
              <a
                href="#"
                className="uploadDoc"
                onClick={(e) => props.handleOpenViewMomModal(e, data)}
              >
                <WysiwygOutlined style={{ fontSize: "16px", marginRight: "5px" }} /> {/* Add the icon */}
                {item.name}
              </a>
            </td>
          );
        }
        // else if(item.subtype==="TempButton"){
        //   // if(parseInt(ProcessID)==10)
        //   // {
        //   return(
        //     <td data-title={item.name} style={{display:'none'}}>
        //       <a
        //       href="#"
             
        //       className="uploadDoc"
        //       id={`fos_appointment|${data.BookingID}|${data.CustomerID}`}
        //       onClick={(e)=>props.handleOptWAClick(e,data.CustomerID)}
        //       >
        //       <i className="fa fa-whatsapp" aria-hidden="true"></i>
        //       {/* <i className="fa fa-cloud-upload" aria-hidden="true"></i>{" "} */}
        //       {item.name}
        //       </a>
        //     </td>
        //   )
        //     // }
        //     // else{
        //     //   return(
        //     //     null
        //     //   )
        //     // }
        // }
         else {
          value = data[item.accessor] || null;
          // if (!value) {
          //   return <td data-title={item.name}>{item.show ? "" : item.text}</td>;
          // } 
           if (isValidRow) {
            return (
              <td data-title={item.name}>
                <a
                  href="#"
                  onClick={(e) => {
                    props.handlePopUp(e, data, item);
                  }}
                >
                  {item.show ? value : item.text}
                </a>
              </td>
            );
          } else if (item.disable) {
            return (
              <td data-title={item.name}>{item.show ? value : item.text}</td>
            );
          } else {
            return (
              <td data-title={item.name}>
                <a
                  href="#"
                  onClick={(e) => {
                    props.handlePopUp(e, data, item);
                  }}
                >
                  {item.show ? value : item.text}
                </a>
              </td>
            );
          }
        }

      case "call":
        value = data[item.accessor] || null;   
        if(data['ProductID']==115 && new Date()- new Date(data['BookingDate']) >= 180*24*60*60*1000 && [41,42,43,44].includes(data['StatusID']))
        {
              return <td data-title={item.name}>-</td>;
        }
        if (value && RoleId && ![13].includes(RoleId)) {
          return <td data-title={item.name}>{value?value:'-'}</td>;
        }
        return (
          <td data-title={item.name}>
            {
              value?
            <a
              href="#"
              onClick={(e) => {
                props.handleLinkClick(e, data, item);
              }}
            >
              <i className="fa fa-phone" aria-hidden="true"></i>
              {" " + value}{" "}
            </a>:null
    }
          </td>
        );

      case "boolean":
        return (
          <td data-title={item.name}>{data[item.accessor] ? "Yes" : "No"}</td>
        );

      case "int":
        value = parseInt(data[item.accessor]) || 0;
        value = new Intl.NumberFormat('en-IN', {}).format(value)
        return(
          <td data-title={item.name}>{value}</td>
        );

      case "popup":
        text = data[item.accessor] || item.text;
        const overflow = item.overflow || false;
        if (overflow && text.length > 0) {
          text = text.substring(0, 20) + "...";
        }

        if (text === "Active") {
          return <td data-title={item.name}> {text}</td>;
        } else if (item.show && text) {
          return (
            <td data-title={item.name}>
              <a
                href="#"
                onClick={(e) => {
                  props.handlePopUp(e, data, item);
                }}
              >
                {" "}
                {text}
              </a>
            </td>
          );
        } else if (isValidRow && text) {
          return (
            <td data-title={item.name}>
              <a
                href="#"
                onClick={(e) => {
                  props.handlePopUp(e, data, item);
                }}
              >
                {" "}
                {text}
              </a>
            </td>
          );
        } else {
          return <td data-title={item.name}> {"-"}</td>;
        }
      
      case "active":
        return <td data-title={item.name}>{data[item.accessor] ? data[item.accessor] : "Inactive"}</td>
      
      case "prompt":
        if(RoleId && [13].includes(RoleId))
        {
        if(data[item.accessor])
        {
          return <td>-</td>
        }
        else{
          
          return(
          <td data-title={item.name}>
              {/* <a
              href="#"
              // className="uploadDoc"
              onClick={(e)=>props.handleOptWAClick(e,data)}
              > */}
               <i class='fas fa-location-arrow' onClick={(e)=>{props.handleAutoDebitPrompt(e,data)}} style={{fontSize:'14px', color:'#0065ff'}} aria-hidden="true"></i>
              {/* {item.name} */}
              {/* </a> */}
            </td>
          )
        }
        }
        else{
         
          return <td>-</td>
        }

      case "rupees":
        if(data[item.accessor])
        {
          return <td>{parseInt(data[item.accessor]).toLocaleString('en-IN')}</td>
        }
        else{
          return <td>-</td>
        }
      default:
        return <td data-title={item.name}>{ModifiedColumnData}</td>;
    }
  }
};

export default TableDataFormat;
