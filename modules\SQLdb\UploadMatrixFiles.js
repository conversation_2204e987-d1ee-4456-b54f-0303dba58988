
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var _ = require('underscore');
var fs = require('fs');
var path = require('path');
const moment = require("moment");
const xlsxFile = require('read-excel-file/node');


exports.UploadChatAgentFile = async function (req, res) {
    try {
        // console.log(req.body);
        // console.log(req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {
            var _result = [];
            var JsonExcel = {};
            for (var i = 1; i in rows; i++) {
                var obj = {};
                for (j in rows[i]) {
                    obj[rows[0][j]] = rows[i][j];
                }
                var innerObj = {};
                _result.push(obj);
            }
            //console.log("result", _result);

            if (req.body.type == "UserMessages") {

                let response =  UploadMessageData(_result, req.body.UserId);
                response.then(function (result) {
                    // console.log('res',result);    

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UserMessages UploadChatAgentFile', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                     
                })
              
            }
            else if(req.body.type == "LeadCredit"){
                let response =  UploadLeadCreditData(_result, req.body.UserId);
                response.then(function (result) {
                    //console.log('res',result);    

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside LeadCredit UploadChatAgentFile', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
                 
            }
            else if (req.body.type == "FosAllocationPanel") {
                let response =  UploadAllocationData(_result, req.body.UserId);
                response.then(function (result) {  

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside FosAllocationPanel UploadChatAgentFile', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
              
            }
            else if (req.body.type == "FosAllocationPanelNew") {
                let response =  UploadAllocationDataNew(_result, req.body.UserId, req.body.ProcessList, req.body.ProductList);
                response.then(function (result) {  

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside FosAllocationPanel UploadChatAgentFile', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
              
            }
            else if (req.body.type == "ManageLeadHNILogic") {
                let response =  UploadHniData(_result, req.body.UserId);
                response.then(function (result) {  

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UploadHniData', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
              
            }
            else if (req.body.type == "AdultChildScoreMaster") {
                let response =  UploadAdultChildScoreData(_result,req.body.UserProductAccess);
                response.then(function (result) {  
                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UploadAdultChildScoreData', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
              
            }
            else if (req.body.type == "CityScoreMaster") {
                let response =  UploadCityScoreData(_result, req.body.UserProductAccess);
                response.then(function (result) {  
                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UploadCityScoreData', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
              
            }
            else if (req.body.type == "PayUScoreRankMapping") {
                let response =  UploadPayUScoreRankMapping(_result);
                response.then(function (result) {  
                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UploadPayUScoreRankMapping', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
            }
            else if(req.body.type == "UploadUsersLimit"){
                if(_result && _result.length > 500){
                    return res.status(400).send({
                          isError: true,
                          errorMessage: "Uploaded File has exceeded limit. Maximum 500 records can be uploaded at once."
                        });
                }
                let response =  UploadUsersLimit(req,_result);
                response.then(function (result) {
                    //console.log('res',result);    

                    res.send({
                        status: 200,
                        data: result
                    });
                }).catch(err => {
                    console.log('Inside UploadMatrixFiles UploadChatAgentFile', err);
                    return res.send({
                        status: 500,
                        data: err
                    });
                })
                 
            }
            else {
                res.send({
                    status: 200,
                    data: _result
                });
            }

        })

    }
    catch (e) {
        console.log('UploadChatAgentFile', e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UploadMessageData(res, UserId) {
    try {
        // console.log(res.length);

        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                // console.log(res[index].Ecode);
                let sqlparam = [];
                sqlparam.push({ key: "Ecode", value: res[index].Ecode });
                sqlparam.push({ key: "displaymessage", value: res[index].Message });
                sqlparam.push({ key: "Description", value: res[index].Description });
                sqlparam.push({ key: "CreatedBy", value: UserId });
                //sqlparams.push("@" + k);
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertUserMessages]", sqlparam);
                // console.log(result);
                results.push(result.recordset[0]);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}

async function UploadLeadCreditData(res, UserId) {
    try {
        // console.log(res.length);

        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                // console.log(res[index].Ecode);
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeID", value: res[index].EmployeeID });
                sqlparam.push({ key: "LeadID", value: res[index].LeadID });
                sqlparam.push({ key: "UpdatedBy", value: UserId });
                //sqlparams.push("@" + k);
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[Insert_SecondaryUserLeadCredit]", sqlparam);
                // console.log(result);
                results.push(result.recordset[0]);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}



async function UploadAllocationData(res, UserId) {
    try {
        
        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeId", value: res[index].AssignedTo });
                sqlparam.push({ key: "LeadId", value: res[index].LeadID });
                sqlparam.push({ key: "AssignedBy", value: parseInt(UserId) });
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[Fos_UploadLeadAllocation]", sqlparam);
                results.push(result);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return null;

    }
    finally {

    }
}

async function UploadAllocationDataNew(res, UserId, processList, productList) {
    try {
        
        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeId", value: res[index].AssignedTo });
                sqlparam.push({ key: "LeadId", value: res[index].LeadID });
                sqlparam.push({ key: "AssignedBy", value: parseInt(UserId) });
                sqlparam.push({ key: "ProductList", value: productList });
                sqlparam.push({ key: "ProcessList", value: processList });
                console.log("sqlparam", sqlparam);
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[Fos_UploadLeadAllocationv2]", sqlparam);
                results.push(result);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return null;

    }
    finally {

    }
}



exports.AgentSurvey = async function (req, res) {
    try {
        
        let results = [];
        let data = req.body;

        if (data) {
            for (let index = 0; index < data.length; index++) {
                //console.log( "StartDate",new Date());
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
                sqlparam.push({ key: "SurveyId", value: data[index].SurveyId });
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertSurveyAgentMapping]", sqlparam);

                results.push(result.recordset[0]);
                //console.log( "EndDate",new Date());
            }
 
               await res.send({
                    status: 200,
                    data: results
                });
           
        }
        
    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}

async function UploadHniData(res, UserId) {
    try {
        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                let sqlparam = [];
                if(index == 0){
                    sqlparam.push({ key: "DisabledIsActive", value: 1 });
                }
                sqlparam.push({ key: "PostCode", value: res[index].PostCode });
                sqlparam.push({ key: "CityID", value: res[index].CityID });
                sqlparam.push({ key: "MinAPE", value: res[index].MinAPE });
                sqlparam.push({ key: "MaxAPE", value: res[index].MaxAPE });
                sqlparam.push({ key: "SupplierID", value: res[index].SupplierID });
                sqlparam.push({ key: "GroupID", value: res[index].GroupID });
                sqlparam.push({ key: "CreatedBy", value: parseInt(UserId) });
                sqlparam.push({ key: "PaymentPeriodicity", value: res[index].PaymentPeriodicity });
                sqlparam.push({ key: "NumberOfDays", value: res[index].NumberOfDays });
                sqlparam.push({ key: "IsRenewal", value: res[index].IsRenewal });
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertLeadHNIData]", sqlparam);   
                results.push(result?.recordset?.length > 0 && result.recordset[0] || {});
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return null;

    }
    finally {

    }
}

async function UploadAdultChildScoreData(res, UserProductAccess) {
    try {
        let results = [];
        let reslength = res.length > 50 ? 50 : res.length;

        if (res) {
            for (let index = 0; index < reslength; index++) {
                if(UserProductAccess.includes(res[index].ProductID))
                {
                    let sqlparam = [];
                    sqlparam.push({ key: "AdultCount", value: res[index].AdultCount });
                    sqlparam.push({ key: "ChildCount", value: res[index].ChildCount });
                    sqlparam.push({ key: "ProductID", value: res[index].ProductID });
                    sqlparam.push({ key: "ProcessName", value: res[index].ProcessName });
                    sqlparam.push({ key: "Score", value: res[index].Score });
                    sqlparam.push({ key: "IsActive", value: res[index].IsActive });
    
                    let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertAdultChildScoreData]", sqlparam);
                    results.push(result?.recordset?.length > 0 && result.recordset[0] || {});
                }
            }
            return results;
        }
    }
    catch (e) {
        console.log(e);
        return null;
    }
    finally {
    }
}

async function UploadCityScoreData(res, UserProductAccess) {
    try {
        let results = [];
        let reslength = res.length > 50 ? 50 : res.length;

        if (res) {
            for (let index = 0; index < reslength; index++) {
                if(UserProductAccess.includes(res[index].ProductID))
                {
                    let sqlparam = [];
                    sqlparam.push({ key: "CityID", value: res[index].CityID });
                    sqlparam.push({ key: "ProductID", value: res[index].ProductID });
                    sqlparam.push({ key: "ProcessName", value: res[index].ProcessName });
                    sqlparam.push({ key: "Score", value: res[index].Score });
                    sqlparam.push({ key: "IsActive", value: res[index].IsActive });

                    let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertCityScoreData]", sqlparam);
                    results.push(result?.recordset?.length > 0 && result.recordset[0] || {});
                }
            }
            return results;
        }
    }
    catch (e) {
        console.log(e);
        return null;
    }
    finally {
    }
}

async function UploadPayUScoreRankMapping(res) {
    try {
        let results = [];
        let reslength = res.length > 500 ? 500 : res.length;

        if (res) {
            for (let index = 0; index < reslength; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "AffluenceScore", value: res[index].AffluenceScore });
                sqlparam.push({ key: "PropensityScore", value: res[index].PropensityScore });
                sqlparam.push({ key: "PbRank", value: res[index].PbRank });
                sqlparam.push({ key: "FinalRank", value: res[index].FinalRank });

                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertPayUScoreRankMapping]", sqlparam);
                results.push(result?.recordset?.length > 0 && result.recordset[0] || {});
            }
            return results;
        }
    }
    catch (e) {
        console.log(e);
        return null;
    }
    finally {
    }
}

async function UploadUsersLimit(req,res) {
    try {
        
        let results = [];
        let userId = req.user?.userId ? req.user.userId : null;  

        if (res) {
            for (let index = 0; index < res.length; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "Ecode", value: res[index].Ecode });
                sqlparam.push({ key: "Limit", value: res[index].Limit });
                sqlparam.push({ key: "UpdatedBy", value: userId });
                sqlparam.push({ key: "RowNo", value: (index+2) });
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertUserLimit]", sqlparam);
                results.push(result?.recordset?.length > 0 && result.recordset[0] || {});
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return null;

    }
    finally {

    }
}


