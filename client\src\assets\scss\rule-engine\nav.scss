@import './variables.scss';

.nav-container {
    background-color: $title-bg-clr;
    font-family: $title-font-family;
    height: 100%;
    left: 0;
    margin-top: -70px;
    overflow-y: auto;
    position: fixed;
    padding: 0 10px 25px;

    &.closed {
      width: 40px;
    }

    &.open {
      width: $sideNavWidth;
    }
    .sidebar-navigation {
      display: flex;
      justify-content: space-between;
    }
    .ruleslist-link{
      display: flex;
      justify-content: space-around;
    }
}

.menu-bar {
  cursor: pointer;
  font-size: 19px;
  margin-top: 30px;
  margin-bottom: 25px;

  a {
    color: $title-color;
  }
}


.links-section {
    display: flex;
    height: 90vh;
    flex-direction: column;
    // justify-content: space-between;
} 

.link-container {
  list-style-type: none;
  margin-top: 5px;
  padding: 0;

  li {
    color: $btn-color;
    font-family: $btn-font-family;
    font-size: 14px;
    height: 48px;
    line-height: 40px;
    padding: 5px 10px;
  }

  li:hover {
    background: $btn-bg-clr;
  }

  a {
    color: $btn-color;
  }

  .link-heading {
    font-size: 16px;

    a {
      color: $btn-color;
  
      .rules-icon {
        background: url('../../icons/rules.svg') no-repeat;        
        background-size: contain;
        margin-top: 8px;
        padding: 0;
        padding-bottom: 10%;
        position: absolute;
        width: 90%;
      }
  
      span.text {
        margin-left: 30px;
      }
  
    }

    a::after {
      content: '>';
      float: right;
      transition: transform 0.5s;
    }
  
    a.active::after {
      transform: rotate(90deg);
      transition: transform 0.5s;
    }
  }

  .navmenu {
    cursor: pointer;
    padding-left: 30px;

    a {
      color: $btn-color;
      font-size: 14px;
      text-decoration: none;
    }

    .icon {
      font-size: 14px;
    }

    span.text {
      margin-left: 10px;
    }
  }

  .sublink-container {
    display: none;
    list-style-type: none;
    top: 10px;

    &.visible {
      display: block;
    }

    li {
      border-left: 3px solid $border-left-color;
      cursor: pointer;
      line-height: 15px;
      visibility: hidden;
      height: auto;

      &.visible {
        visibility: visible;
        padding: 10px;
      }

      &.active a{
        color: $link-active-color;
      }
    }

    a {
      span.text {
        margin-left: 10px;
      }
    }

    a::after {
      content: '';
    }
  }
}