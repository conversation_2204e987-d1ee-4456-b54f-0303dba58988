
import React, { useEffect, useState } from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { GetCommonData, InsertData, UpdateData } from "../store/actions/CommonAction";
import { addRecord } from "../store/actions/CommonMongoAction";
import { If, Then } from 'react-if';
import { connect } from "react-redux";
import DataTableV1 from './Common/DataTableWithFilterV1';
import AlertBox from './Common/AlertBox';
import { fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, joinObject, getAgentId } from '../utility/utility.jsx';

// reactstrap components
import { Card, CardHeader, CardBody, CardTitle, Row, Col } from "reactstrap";


const CMAgeBucketScoreMaster = (props) => {  
    
    const [ state, setState ] = useState({
        showModal: false,
        items: [],
        FormTitle: "",
        formvalue: {},
        event: "",
        ModalValueChanged: false,
    });

    const selectedrow = { "Id": 0, "Score": "", "IsActive": false, "CreatedOn": new Date(), "ProductID": "", "ProcessName": "" }
    const root = "CMAgeBucketScoreMaster";
    const PageTitle = "CM Age Bucket Score";

    const columnlist = [
        {
            name: "Id",
            label: "Id",
            type: "hidden",
            hide: true,
        },
        {
            name: "AgeGroup",
            label: "AgeGroup",
            type: "number"
        },
        {
            name: "Score",
            label: "Score",
            type: "decimal"
        },
        {
            name: "ProductID",
            label: "Product",
            type: "dropdown",
            config: {
                root: "UserProducts",
                cols: ["DISTINCT PDT.ID AS Id", "PDT.ProductName AS Display"],
                con: [{ "PDT.Isactive": 1 },{ "UBM.IsActive" : 1 },{ "UBM.UserId": getAgentId() }]
            },
            searchable: true,
            editable: false
        },
        {
            name: "ProcessName",
            label: "ProcessName",
            type: "dropdown",
            config: {
                root: "AllocationProcessMaster",
                cols: ["ProcessName AS Id", "ProcessName AS Display"],
                con: [{ "Isactive": 1 }]
            },
            searchable: true,        
        },
        {
            name: "CreatedOn",
            label: "CreatedOn",
            type: "datetime",
            hide: true,        
        },     
        {
            name: "IsActive",
            label: "IsActive",
            type: "bool"
        }
    ];

    useEffect(() => {
        columnlist.map(col => (
            fnBindRootData(col, props)
        ));

        props.GetCommonData(    
            {
                limit: 10,
                skip: 0,
                root: root,
                cols: GetJsonToArray(columnlist, "name"),
                c: "L"
            }
        );
    }, []); 

    const fnBindStore = (col, nextProps) => {
        if (col.type === "dropdown") {
            let items;
            if (nextProps.CommonData && nextProps.CommonData[root] && nextProps.CommonData[col.config.root]) {
                items = joinObject(nextProps.CommonData[root], nextProps.CommonData[col.config.root], col.name)
                setState(prev => ({...prev, items: items }));
            }
        }
    }

    useEffect(() => {
        setState(prev => ({...prev, items: props.CommonData[root] }));

        columnlist.map(col => (
            fnBindStore(col, props)
        ));

        if (props.CommonData && props.CommonData.InsertSuccessData && props.CommonData.InsertSuccessData.status) {
            if (props.CommonData.InsertSuccessData.status != 200) {
                setState(prev => ({...prev, showAlert: true, AlertMsg: props.CommonData.InsertSuccessData.error, AlertVarient: "danger" }));
            } else {
                setState(prev => ({...prev, showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" }));
            }
        }
    
        if (props.CommonData && props.CommonData.UpdateSuccessData && props.CommonData.UpdateSuccessData.status) {
            if (props.CommonData.UpdateSuccessData.status != 200)
                setState(prev => ({...prev, showAlert: true, AlertMsg: props.CommonData.InsertSuccessData.error, AlertVarient: "danger" }));
            else {
                setState(prev => ({...prev, showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" }));
            }
        }

        setTimeout(function () {
            setState(prev => ({...prev, showAlert: false, AlertMsg: "", AlertVarient: "" }));
        }, 2000);

    }, [props]);

    const fnDatatableColumn = () => {

        var columns = fnDatatableCol(columnlist);

        columns.push({
            name: "Action",
            cell: row => <ButtonGroup aria-label="Basic example">
                <Button variant="secondary" onClick={() => handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
                <Button variant="secondary" onClick={() => handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
            </ButtonGroup>
        });
        return columns;
    }

    const handleCopy = (row) => {
        setState(prev => ({...prev, formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" }));
    }

    const handleEdit = (row) => {
        setState(prev => ({...prev, od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" }));
    }

    const handleClose = () => {
        setState(prev => ({...prev, showModal: false }));
    }

    const handleShow = () => {
        setState(prev => ({...prev, formvalue: selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" }));
    }

    const handleSave = () => {
        if (document.getElementsByName("frmCMAgeBucketScoreMaster").length > 0 &&
        document.getElementsByName("frmCMAgeBucketScoreMaster")[0].reportValidity()) {

            let formvalue = JSON.parse(JSON.stringify(state.formvalue));
            funcCleanData(formvalue);
            let id = formvalue["Id"];
                delete formvalue["Id"]
            if (state.event === "Edit") {
                funcCleanData(formvalue);

                props.UpdateData({
                    root: root,
                    body: formvalue,
                    querydata: { "Id": id },
                    c: "L"
                });
               
                props.addRecord({
                    root: "History",
                    body: {
                        module: root,
                        od: state.od,
                        nd: formvalue,
                        ts: new Date(),
                        by: getAgentId()
                    }
                });
            } else if (state.event === "Copy") {
                formvalue["CreatedOn"] = new Date();
                funcCleanData(formvalue);

                props.InsertData({
                    root: root,
                    body: formvalue,
                    c: "L"
                });
            } else {
                formvalue["CreatedOn"] = new Date();
                
                props.InsertData({
                    root: root,
                    body: formvalue,
                    c: "L"
                });
            }
            setTimeout(function () {
                props.GetCommonData({
                    root: root,
                    cols: GetJsonToArray(columnlist, "name"),
                    c: "L"
                });
            }, 2000);
            setState(prev => ({...prev, showModal: false }));
        }
        return false;
    }

    const handleChange = (e, props) => {
        let formvalue = state.formvalue;

        if (e.target && e.target.type === "checkbox") {
            formvalue[e.target.id] = e.target.checked;
        }
        else if (e._isAMomentObject) {
            formvalue[props] = e.format()
        }
        else {
            formvalue[e.target.id] = e.target.value;
        }
        setState(prev => ({...prev, formvalue: formvalue, ModalValueChanged: true }));
    }

    const funcCleanData = (formvalue) => {
        formvalue = fnCleanData(columnlist, formvalue);
        setState(prev => ({...prev, formvalue: formvalue }));
    }

    const columns = fnDatatableColumn();
    const { items, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event} = state;

    return (
        <div className="content">
            <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
            <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={11}>
                                    <CardTitle tag="h4">{PageTitle}</CardTitle>
                                </Col>
                                <Col md={1}>
                                    <Button variant="primary" onClick={handleShow}>ADD</Button>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTableV1
                                columns={columns}
                                data={items}
                                getDataOnProdSearch={true}
                            />
                        </CardBody>
                    </Card>
                </Col>
            </Row>

            <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
                <Modal.Header closeButton>
                    <Modal.Title>{FormTitle}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form name="frmCMAgeBucketScoreMaster">
                        <Row>
                            {columnlist.map(col => (
                                fnRenderfrmControl(col, formvalue, handleChange, event)
                            ))}
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <If condition={ModalValueChanged}>
                        <Then>
                            <input type="submit" value="Save Changes" className="btn btn-primary" onClick={handleSave} />
                        </Then>
                    </If>
                </Modal.Footer>
            </Modal>
        </div>
    );
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
  }
  
export default connect(
mapStateToProps,
{
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
}
)(CMAgeBucketScoreMaster);