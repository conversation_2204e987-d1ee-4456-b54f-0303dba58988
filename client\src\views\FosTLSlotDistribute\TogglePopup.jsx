import {useState, useRef, useEffect} from 'react';
import { Dropdown, OverlayTrigger, Popover } from 'react-bootstrap';
import AssignedLeadPopup from './AssignedLeadPopup';
import 'react-toastify/dist/ReactToastify.css';
import { OpenNewSalesView, getuser } from "../../utility/utility.jsx";

const TogglePopup = (props) => {
  const { Details, AppointmentSlots, activeTab, IsSuccess, EmployeeFlag } = props;
  
  const [tooltipShow, setToolTipShow] = useState(false);
  const [modalShow, setModalShow] = useState(false);
  const [Rescheduled, setRescheduled] = useState(false);
  const [AssignagentLead, setAssignagentLead] = useState(false);

  let target = useRef(null);
  // console.log("Toggle", props)

  // useEffect (()=>{
  //   ref.handleHide()
  // },[])

  const openSalesView = (CustomerId, LeadId, ProductId) => { 
    let url = OpenNewSalesView(CustomerId, LeadId, ProductId);
    window.open(url)
  }


  // const openSalesView = (Leadid) => {
  //  e.preventDefault();
 

    // SalesView(Leadid, function (results) {
    //   if (results.data.status == 200) {
    //       console.log(results.data.data[0])
    //       let val = results.data.data[0]
    //       // console.log(val[0].CustomerID)
    //       // console.log(val[0].ProductID)

    //       let url = LeadView(val[0].CustomerId, Leadid, val[0].ProductId, getuser().UserID);
    //       window.open(url)
           
    //   } else {
    //       toast(results.data.message, { type: 'error' });
    //       return;
    //   }
    // });

  // }

  const RescheduleAppointment = () => {
    setModalShow(true);
    setRescheduled(true);
    document.getElementById("popover-positioned-bottom").style.visibility = "hidden";
  }
  const AssignLead = () => {
    setModalShow(true);
    setRescheduled(false);
    setAssignagentLead(true)
    document.getElementById("popover-positioned-bottom").style.visibility = "hidden";
  }

  // const popup = (e) => {
  //   console.log(e)
  //   setTrig(true)
  // }

  return (
    <>

      {['bottom'].map((placement) => (
        <OverlayTrigger rootClose
          ref={target}
          trigger={['click']}
          // trigger="focus" 
          //key={placement}
          //show={tooltipShow}
          placement={placement}
          overlay={
            <Popover id={`popover-positioned-${placement}`} className="toogleOpen">
              <Popover.Header as="h3">OPTIONS</Popover.Header>
              <Popover.Body>
                <ul>
                  <li onClick = {() => openSalesView(Details.CustomerId, Details.LeadId, Details.ProductId)}> <img src="/fosTlDashboard/eye.svg" /> Lead View</li>
                  <hr />
                  {/* EmployeeFlag==2 &&  */}
                  { Details && Details.EmployeeId  && [2002, 2005, 2088, 2194].indexOf(Details.SubstatusId) > -1 && <li onClick={RescheduleAppointment}> <img src="/fosTlDashboard/AssignLead.svg" /> Re-Schedule</li> }

                  <hr />
                  {/* && [2002, 2005, 2088, 2194].indexOf(Details.SubstatusId) > -1 */}
                  {/* EmployeeFlag==1 &&  */}
                  { EmployeeFlag==1 && Details && [2002, 2005, 2088, 2194].indexOf(Details.SubstatusId) > -1 && <li onClick={AssignLead}> <img src="/fosTlDashboard/AssignLead.svg" /> Assign Lead</li>}
                </ul>
              </Popover.Body>
            </Popover>
          }
        >
          <div className="TogglePopup"><img src="/fosTlDashboard/three-dots.svg" onClick={()=> setToolTipShow(!tooltipShow)} /></div>
          {/* <button variant="secondary">Popover on {placement}</button> */}
        </OverlayTrigger>
      ))}
      
      {modalShow && <AssignedLeadPopup
        show={modalShow}
        onHide={() => {setModalShow(false); setRescheduled(false)}}
        Details = {Details}
        AppointmentSlots = {AppointmentSlots}
        activeTab = {activeTab}
        Rescheduled = {Rescheduled}
        IsSuccess = {IsSuccess}
        AssignagentLead = {AssignagentLead}
      />}
    </>
  )

}

export default TogglePopup;