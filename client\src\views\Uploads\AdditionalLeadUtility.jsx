const ColumnList =
[   { Id: [1,2,3], Column: "LeadId", ProductId : 117, key : "LeadID"},
    { Id: [1], Column: "AlternateNumber", ProductId : 117, key : "AltContatNumber" },
    { Id: [2], Column: "Remarks", ProductId : 117, key : "Remarks" },
    { Id: [3], Column: "EmployeeId", ProductId : 117, key : "EmployeeID" }
]

export const FilterColumnList = (FeatureId) =>{
    let columns = ColumnList.filter((item) => (item.Id.includes(parseInt(FeatureId))));
    return{
        Columns : columns
    }
}

export const ProductList =
[
    { Id: 117, Display: "Motor" },
    { Id: 2, Display: "Health" }
];

export const FeatureList =
[
    { Id: 1, Display: "Alternate Number", ProductId : [117], Name : "AlternateNumber" },
    { Id: 2, Display: "Re-Open", ProductId : [117,2], Name : "ReOpen" },
    { Id: 3, Display: "Assignment", ProductId : [117,2], Name : "Assignment" },    
];

export const ErrorDataColumns = 
[
    {Column : "Tracking Id"},
    {Column : "Created On"},
    {Column : "Error"},
    {Column : "Primary Column"}
];