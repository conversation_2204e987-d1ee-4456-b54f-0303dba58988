import React, { Suspense, useEffect, useState } from "react";
import { Button } from 'react-bootstrap';

import { LotteryData } from "../../store/actions/CommonAction";

import { connect } from "react-redux";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import './Lottery.css';

import Wheel from './Wheel.js';

import Confetti from 'react-confetti'

import { Form, Modal } from 'react-bootstrap';


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
    Table
} from "reactstrap";
//import { getuser } from "utility/utility";
import _ from 'underscore';

let sectors = [
    { color: "#f82", label: "0" },
    { color: "#0bf", label: "1" },
    { color: "#fb0", label: "2" },
    { color: "#0fb", label: "3" },
    { color: "#b0f", label: "4" },
    { color: "#f0b", label: "5" },
    { color: "#bf0", label: "6" },
    { color: "#f82", label: "7" },
    { color: "#0bf", label: "8" },
    { color: "#fb0", label: "9" }
];

function Lottery() {

    const [mustSpin, setMustSpin] = useState(false);
    const [prize, setPrize] = useState('');

    const [ticketCount, setTicketCount] = useState(0);
    const [agentCount, setAgentCount] = useState(0);
    const [eligibleAgentCount, setEligibleAgentCount] = useState(0);

    const [wheelCount, setWheelCount] = useState(0);
    const [firstWheel, setFirstWheel] = useState([]);
    const [WheelResult, setWheelResult] = useState([]);
    const [BuList, setBuList] = useState([]);
    const [RewardsList, setRewardsList] = useState([]);
    const [RewardsDropdownList, setRewardsDropdownList] = useState([]);
    const [SelectedProduct, setSelectedProduct] = useState(0);
    const [SelectedBuId, setSelectedBuId] = useState(0);
    const [SelectedRewardId, setSelectedRewardId] = useState(0);
    const [RewardCurrentStatus, setRewardCurrentStatus] = useState({});
    const [WinnerData, setWinnerData] = useState(null);
    const [Winners, setWinners] = useState([]);
    const [showModal, setShowModal] = useState(false);


    const handleClose = () => {
        setShowModal(false);
    }

    useEffect(() => {
        if (ticketCount > 99) {
            setWheelCount(3)
        }
        else if (ticketCount > 9) {
            setWheelCount(2)
        }
        else {
            setWheelCount(1)
        }

        let tc = ticketCount.toString()
        let tc1 = tc.split('')[0];
        tc1 = parseInt(tc1);
        let fw = [];
        for (let index = 0; index <= tc1; index++) {
            const element = sectors[index];
            fw.push(element)
        }

        setFirstWheel(fw)

    }, [ticketCount]);

    useEffect(() => {
        //getBuListData();
    }, [])

    useEffect(() => {
        if (SelectedRewardId) {
            var rewardStatus = _.filter(RewardsList, function (item) { return item.RewardId == SelectedRewardId; });
            console.log(rewardStatus);
            setRewardCurrentStatus(rewardStatus[0]);
        }
    }, [SelectedRewardId, RewardsList])

    const onBUChange = (e) => {
        setRewardsList([]);
        setSelectedRewardId(0);

        setSelectedBuId(e.target.value);
        getRewardsList(e.target.value);
        setRewardsDropdownList([]);
        setWheelCount(0);
    }

    const onProductChange = (e) => {
        setRewardsList([]);
        setSelectedProduct(e.target.value);
        setSelectedRewardId(0);

        setSelectedBuId(0);
        getBuListData(e.target.value);
        setRewardsDropdownList([]);
        setWheelCount(0);
    }

    const onRewardChange = (e) => {
        setWheelCount(0);
        setSelectedRewardId(e.target.value);

        getTicketCount(e.target.value);
        getWinners(e.target.value);
        //showRewardsCurrentStatus();
    }

    const getTicketCount = (RewardId) => {
        LotteryData({
            root: "getTicketCount",
            params: { BUId: SelectedBuId, RewardId: RewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                setTicketCount(result.data.data[0][0].TicketCount);
                setAgentCount(result.data.data[0][0].AgentCount);
                setEligibleAgentCount(result.data.data[0][0].EligibleAgents);
            }
        });
    }

    const getBuListData = (ProductId) => {
        LotteryData({
            root: "getBuList",
            params: { ProductId: ProductId }
        }, (result) => {
            //console.log(result.data);
            if (result.data && result.data.status == 200) {
                setBuList(result.data.data[0]);
            }
            return;
        });
    }

    const getRewardsList = (BuId) => {
        LotteryData({
            root: "getRewardsList",
            params: { BUId: BuId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let resData = [];
                result.data.data[0] && result.data.data[0].map((value) => {
                    resData.push({ Id: value.RewardId, Display: value.RewardTitle });
                })
                setRewardsDropdownList(resData);
                //debugger;
                setRewardsList(result.data.data[0]);



            }
            return;
        });
    }
    const getRewardDetails = (BuId, RewardId) => {
        LotteryData({
            root: "GetRewardDetails",
            params: { BUId: BuId, RewardId: RewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let resData = result.data.data[0][0];
                if (resData.PendingRewards > 0) {
                    setTimeout(function () {
                        onClickDraw();
                    }, 1000)

                }
                else {
                    setShowModal(true);
                }


            }
            return;
        });
    }

    const getWinnerDetail = (prize, cb) => {
        LotteryData({
            root: "GetWinnerDetail",
            method: "post",
            params: { BUId: SelectedBuId, RewardId: SelectedRewardId, TicketNumber: prize }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                let resData = result.data.data[0][0];
                setWinnerData(resData);
                if (cb) {
                    cb(resData);
                }
            }
            return;
        }, 'post');
    }
    const getWinners = (SelectedRewardId) => {
        LotteryData({
            root: "GetWinners",
            params: { BUId: SelectedBuId, RewardId: SelectedRewardId }
        }, (result) => {
            console.log(result.data);
            if (result.data && result.data.status == 200) {
                //debugger;
                let resData = result.data.data[0];
                setWinners(resData);
            }
            return;
        }, 'get');
    }

    const bindWheels = () => {
        let result = <></>;


        if (wheelCount == 1) {
            // result = <Col md="12">
            //     <Wheel spin={mustSpin} sectors={sectors} onResult={onResult} id="0" />
            // </Col>
        }
        if (wheelCount == 2) {
            result = <>
                <Col md="4">
                    <Wheel spin={mustSpin} sectors={firstWheel} onResult={onResult} id="0" />
                </Col>
                <Col md="4">
                    <Wheel spin={mustSpin} sectors={sectors} onResult={onResult} id="1" />
                </Col>
                <Col md="4">
                </Col>
            </>
        }
        if (wheelCount == 3) {
            result = <>
                <Col md="4">
                    <Wheel spin={mustSpin} sectors={firstWheel} onResult={onResult} id="0" />
                </Col>
                <Col md="4">
                    <Wheel spin={mustSpin} sectors={sectors} onResult={onResult} id="1" />
                </Col>
                <Col md="4">
                    <Wheel spin={mustSpin} sectors={sectors} onResult={onResult} id="2" />
                </Col>
            </>
        }


        if (SelectedBuId == 4 && SelectedRewardId == 2) {

            result = <Col md="auto" md="7" className="mr-6">
                <div className="winnerList">
                    <h3>Winners</h3>
                    <p>Firdous Ahmad - PW07423</p>
                    <p>Chand Tanwar - PW13165</p>
                    <p>Gavendra Kumar Jha - PW17814</p>
                </div>
            </Col>

        }
        if (SelectedBuId == 4 && SelectedRewardId == 3) {

            result = <Col md="auto" md="7" className="mr-6">
                <div className="winnerList">
                    <h3>Winners</h3>
                    <p>Monika - PW02305</p>
                    <p>Deepak Kumar - PW03219</p>
                    <p>Ravi Rajput - PW09898</p>
                </div>
            </Col>

        }
        return result;
    }


    // useEffect(() => {
    //     debugger;
    //     let _p = prize;
    //     for (let index = 0; index < WheelResult.length; index++) {
    //         const e = WheelResult[index];
    //         _p[e.id] = e.label;
    //     }
    //     setPrize(_p);

    // }, [WheelResult, prize]);


    const onResult = (e) => {
        console.log(e)
        //debugger;

        // let wr = WheelResult
        // wr.push(e);
        // setWheelResult(wr);

    }

    const onFinalResult = (e) => {
        let res = '';
        let EL_spin = document.getElementsByClassName("spin");
        for (let index = 0; index < EL_spin.length; index++) {
            const element = EL_spin[index];
            res += element.innerHTML;
        }

        //let _p = prize;
        //_p[e.id] = e.label;
        setPrize(res);
        getWinnerDetail(res, (data) => {
            if (data.RETVAL == 1) {
                setTimeout(function () {
                    onClickReset();
                }, 5000)
            }
            else{
                setTimeout(function () {
                    onClickReset();
                }, 2000)
            }
        });



    }

    const onClickReset = () => {
        setPrize('');
        // setMustSpin(!mustSpin);
        setWheelResult([]);
        setTicketCount(0);
        setAgentCount(0);

        setWheelCount(0);
        getTicketCount(SelectedRewardId);
        getRewardsList(SelectedBuId);
        getWinners(SelectedRewardId)
        setWinnerData(null);
        console.log(RewardCurrentStatus);

        getRewardDetails(SelectedBuId, SelectedRewardId);
        //window.location.reload();
    }
    const onClickDraw = () => {
        //setMustSpin(true)
        //debugger;
        try {
            setPrize('');
            let EL_spin = document.getElementsByClassName("spin");
            for (let index = 0; index < EL_spin.length; index++) {
                const element = EL_spin[index];
                element.click();
            }
            setWheelResult([]);
            setTimeout(function () {
                onFinalResult();
                // setTimeout(function () {
                //     getWinnerDetail();
                // }, 1000);
            }, 12000)
        }
        catch (e) {
            console.log(e)
        }
    }

    const showDrawBtn = () => {
        let result = true;

        if (prize) {
            result = false
        }
        if (SelectedBuId == 4 && SelectedRewardId == 2) {
            result = false
        }
        if (SelectedBuId == 4 && SelectedRewardId == 3) {
            result = false
        }

        return result;
    }

    return (
        <>
            <div className="content">
                <ToastContainer />
                {WinnerData && WinnerData.RETVAL == 1 && <Confetti />}
                <Row>
                    <Col md="12" className="text-center"><img src="/lottery/pblogo.png" className="pbLogo" /></Col>
                    <Col md="3">
                    </Col>
                    <Col md="2">
                        <label>Product</label>
                        <Form.Control as="select" name="Product" onChange={(e) => onProductChange(e)}>
                            <option key={0} value=''>Select Product</option>
                            <option key={1} value='2'>Health</option>
                            <option key={2} value='7'>Term</option>
                            <option key={3} value='115'>Saving</option>
                        </Form.Control>
                    </Col>
                    <Col md="2">
                        <label>Process</label>
                        <Form.Control as="select" name="BU" onChange={(e) => onBUChange(e)}>
                            <option key={0} value=''>Select Process</option>
                            {
                                BuList.map(item => (
                                    <option key={item.Id} value={item.Id}>{item.Display}</option>
                                ))
                            }
                        </Form.Control>
                    </Col>
                    <Col md="2">
                        <label>Prize</label>
                        <Form.Control as="select" name="Reward" onChange={(e) => onRewardChange(e)}>
                            <option key={0} value=''>Select Reward</option>
                            {
                                RewardsDropdownList.map(item => (
                                    <option key={item.Id} value={item.Id}>{item.Display}</option>
                                ))
                            }
                        </Form.Control>
                    </Col>
                    <Col md="3">
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("ACTIVA") > -1 && <img src="/lottery/activa.png" className="winnerprize" />}
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("LED") > -1 && <img src="/lottery/led.png" className="winnerprize" />}
                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle && RewardCurrentStatus.RewardTitle.indexOf("CAR") > -1 && <img src="/lottery/car.svg" className="winnerprize" />}

                        {RewardCurrentStatus && RewardCurrentStatus.RewardTitle &&
                            // <div className={"details"}>

                            //     <p>
                            //         {RewardCurrentStatus.RewardTitle}

                            //     </p>
                            //     <span>{RewardCurrentStatus.PendingRewards}/{RewardCurrentStatus.MaxRewards}</span>
                            // </div>
                            <ul className={"details"}>
                                <li onClick={() => setShowModal(true)}><p> {RewardCurrentStatus.RewardTitle}</p><span>{RewardCurrentStatus.PendingRewards}/{RewardCurrentStatus.MaxRewards}</span></li>
                                <li><p>Total Agents</p><span>{agentCount}</span></li>
                                <li><p>Eligible Agents</p><span>{eligibleAgentCount}</span></li>
                                <li><p>Total tickets</p><span>{ticketCount}</span></li>
                            </ul>
                        }

                    </Col>
                </Row>
                <br />
                <Row className="justify-content-md-center mt-1">

                    {bindWheels()}


                </Row>


                {RewardCurrentStatus && RewardCurrentStatus.PendingRewards > 0 &&
                    <Row className="justify-content-md-center mt-5 winnerSection">
                        <Col md="auto" className="mt-1">
                            {prize &&
                                <div className="winner">
                                    <h3>Winner Ticket No. {prize}</h3>
                                    {WinnerData && WinnerData.RETVAL == 0 && <p>Invalid Ticket</p>}
                                    {WinnerData && WinnerData.RETVAL == 1 && <p>{WinnerData.EmployeId} {WinnerData.EmployeName}</p>}
                                </div>
                            }
                            {/* {prize && <Button onClick={() => getWinnerDetail()}>Search Ticket</Button>} */}

                            {showDrawBtn() && <Button onClick={() => onClickDraw()} className="startBtn">Start Draw</Button>}
                        </Col>
                        {prize && <Button onClick={() => onClickReset()} className="resetBtn"><i class="fas fa-undo"></i></Button>}
                    </Row>
                }

                <Row>
                    <Col>

                        <ul className={"winners"}>
                            {Winners.map(item => (
                                <li><p> Winner {item.winno} <br />{item.TicketNumber}</p><span>{item.EmployeeId}<br />{item.UserName}</span></li>

                            ))}
                        </ul>
                    </Col>
                </Row>
                <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
                    <Modal.Header closeButton>
                        <Modal.Title>Winners</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>

                        <Row>

                            <ul className={"winners top0"}>
                                {Winners.map(item => (
                                    <li><p>
                                        Winner {item.winno}<br />
                                        {item.TicketNumber}</p><span>{item.EmployeeId}<br />{item.UserName}</span></li>

                                ))}
                            </ul>
                        </Row>

                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleClose}>
                            Close
                        </Button>

                    </Modal.Footer>
                </Modal>
            </div>
        </>
    );

}

export default Lottery;

