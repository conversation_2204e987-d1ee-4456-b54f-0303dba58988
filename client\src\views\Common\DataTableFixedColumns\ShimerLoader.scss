.floatLeft {
  // float: left;
  padding-top: 2px;
  position: relative;
  height: auto;
  width: 100%;
}

.floatLeft:after,
.floatLeft:before {
  display: table;
  content: " ";
}

.floatLeft:after {
  clear: both;
}

.line {
  display: inline-block;
  height: 10px;
  margin-top: 7px;
  margin-bottom: 7px;
  width: 100%;
  background-color: #ede7dc;
  float: left;
  clear: both;
}

.line--trunc {
  max-width: 240px;
  width: 100%;
}

.shimmer {
  background-image: linear-gradient(90deg, #f6f7f9 0, #e9ebee 20%, #f6f7f9 40%, #f6f7f9);
  background-size: 99% 100%;
  background-repeat: no-repeat;
  animation: shimmer 1s linear 1ms infinite backwards;
}

@keyframes shimmer {
  0% {
    background-position: 100% * 5 100%;
  }

  100% {
    background-position: 100% * 100 100%;
  }
}