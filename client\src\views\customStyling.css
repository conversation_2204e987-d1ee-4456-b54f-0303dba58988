.noExportButton .downloadExcel, .UserPODLeadsContainer .downloadExcel, .EmiPaymentFailedLeadsContainer .downloadExcel {
  display: none;
}

.wrap{
  white-space: unset !important;
}

.EmiPaymentFailedLeadsContainer .currentPaymentStatus {
  background-color: #fbc658;
  color: #FFFFFF;
  padding: 0.2em;
  border-radius: 1.5px;
}

.UserPODLeadsContainer .rdt_TableHead div {
  font-weight: 600;
  font-size: 13px;
}

.BlockAgentNoticeMsg {
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  color: red;
}

.align-left{
  text-align: left !important;
}

.blink {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    -moz-transition: all 0.5s ease-in;
    -o-transition: all 0.5s ease-in;
    -webkit-transition: all 0.5s ease-in;
    transition: all 0.5s ease-in;
    background-color: green;    
  }
}


.CallGallery .iconsDefault { color: #252422 }
.CallGallery .likedAudio { color: #0065ff }
.CallGallery .dislikedAudio { color: #dc3545 }
.CallGallery caption { min-width: 180px }
.CallGallery .icons { font-size: 18px !important }
.CallGallery .clickable { cursor: pointer }
.CallGallery .table>tbody>tr>td { vertical-align: top !important }
.table-responsive{
  overflow: scroll;
  padding-bottom: 10px;
  height: 439px;
}
.CallGallery{
  padding-bottom: 0px !important;
}
.footer{
  padding:0px !important
}