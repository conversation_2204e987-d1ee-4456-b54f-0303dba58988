import React from "react";
import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';

const SlotDisplay = (props) => {

    const {handleChange, chosenSlot}= props;
    
    return (
        <FormControl className="MapControl">

            <Select
                id="demo-simple-select"
                onChange={handleChange}
                value={chosenSlot}
            >
                <MenuItem value={1}>08:00 - 10:00</MenuItem>
                <MenuItem value={2}>10:00 - 12:00</MenuItem>
                <MenuItem value={3}>12:00 - 14:00</MenuItem>
                <MenuItem value={4}>14:00 - 16:00</MenuItem>
                <MenuItem value={5}>16:00 - 18:00</MenuItem>
                <MenuItem value={6}>18:00 - 20:00</MenuItem>
                <MenuItem value={7}>20:00 - 22:00</MenuItem>
            </Select>
        </FormControl>
    )

}

export default SlotDisplay;