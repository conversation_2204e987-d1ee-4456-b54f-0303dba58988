import React from "react";

import Dashboard from "views/Dashboard.jsx";

import Notifications from "views/Notifications.jsx";
import Icons from "views/Icons.jsx";
import Typography from "views/Typography.jsx";
import TableList from "views/Tables.jsx";

import UserPage from "views/User.jsx";
import AlertMaster from "views/AlertMaster.jsx";
import INVProduct from "views/INVProduct.jsx";



const AgentSurvey = React.lazy(() => import('views/AgentSurvey.jsx'));
const AgentSurveyQuestions = React.lazy(() => import('views/AgentSurveyQuestions.jsx'));
const QuickSightVisualization = React.lazy(() => import('views/QuickSightVisualization.jsx'));
const QuickSightTL = React.lazy(() => import('views/QuickSightTL.jsx'));
const QuickSightTLDashboard = React.lazy(() => import('views/QuickSightTLDashboard.jsx'));
const QuickSightAMDashboard = React.lazy(() => import('views/QuickSightAMDashboard.jsx'));
const QuickSightMGRDashboard = React.lazy(() => import('views/QuickSightMGRDashboard.jsx'));
const chatRoomHistory = React.lazy(() => import('views/chatRoomHistory.jsx'));
const SurveyForm = React.lazy(() => import('views/SurveyForm.jsx'));
const AgentGradeRules_Allocation = React.lazy(() => import('views/AgentGradeRules_Allocation.jsx'));
const GradeRulesMapping_Allocation = React.lazy(() => import('views/GradeRulesMapping_Allocation.jsx'));
const Grade_LimitMapping_Allocation = React.lazy(() => import('views/Grade_LimitMapping_Allocation.jsx'));
const HealthAgeGrpMapping_Allocation = React.lazy(() => import('views/HealthAgeGrpMapping_Allocation.jsx'));
const TermProductGrpMapping_Allocation = React.lazy(() => import('views/TermProductGrpMapping_Allocation.jsx'));
const ProductGrpMapping_Allocation = React.lazy(() => import('views/ProductGrpMapping_Allocation.jsx'));
const PayTermBucketScore = React.lazy(() => import('views/PayTermBucketScore.jsx'));
const AnnualIncomeScore = React.lazy(() => import('views/AnnualIncomeScore.jsx'));
const AgeBucketScoreMaster = React.lazy(() => import('views/AgeBucketScoreMaster.jsx'));
const CustomUtmScore = React.lazy(() => import('views/CustomUtmScore.jsx'));
const BrandScore = React.lazy(() => import('views/BrandScore.jsx'));
const AllocationProcessMaster = React.lazy(() => import('views/AllocationProcessMaster.jsx'));
const LeadSourceScore = React.lazy(() => import('views/LeadSourceScore.jsx'));
const LeadScoreRankMapping = React.lazy(() => import('views/LeadScoreRankMapping.jsx'));
const LeadAgentRankMapping_NewApp = React.lazy(() => import('views/LeadAgentRankMapping_NewApp.jsx'));
const MoodleReport = React.lazy(() => import('views/MoodleReport.jsx'));
const UserCallDetails = React.lazy(() => import('views/UserCallDetails.jsx'));
const ConferenceDetails = React.lazy(() => import('views/ConferenceDetails.jsx'));
const SupplierScore = React.lazy(() => import('views/SupplierScore.jsx'));
const AgentLoginDashboard = React.lazy(() => import('views/AgentLoginDashboard.jsx'));
const CallBackTracker = React.lazy(() => import('views/CallBackTracker.jsx'));
const CallBackAgentWise = React.lazy(() => import('views/CallBackAgentWise.jsx'));
// const IVRActivityChart = React.lazy(() => import('views/IVRActivityChart.jsx'));
// const UnRegisteredChart = React.lazy(() => import('views/UnRegisteredChart.jsx'));
const RealTimePanel = React.lazy(() => import('views/RealTimePanel.jsx'));
const RealTimePanelQueuewise = React.lazy(() => import('views/RealTimePanelQueuewise.jsx'));
//const RealTimePanelQueuewiseTwo = React.lazy(() => import('views/RealTimePanelQueuewiseTwo.jsx'));
const RealTimePanelQueuewiseTwo = React.lazy(() => import('views/RealTimePanel/RealTimePanelQueuewiseTwo.jsx'));
const AgentLeadsNotCalled = React.lazy(() => import('views/AgentLeadsNotCalled.jsx'));
const MobileCallingReport = React.lazy(() => import('views/MobileCallingReport.jsx'));
const InboundQueueReport = React.lazy(() => import('views/InboundQueueReport.jsx'));
const BlockAgent = React.lazy(() => import('views/BlockAgent.jsx'));
const RealTimeDashboard = React.lazy(() => import('views/RealTimeDashboard.jsx'));
const UploadIncentiveData = React.lazy(() => import('views/UploadIncentiveData.jsx'));
const UploadAgentChatData = React.lazy(() => import('views/UploadAgentChatData.jsx'));
const LoginLogoutHistory = React.lazy(() => import('views/LoginLogoutHistory.jsx'));
const LoginLogoutDetails = React.lazy(() => import('views/LoginLogoutDetails.jsx'));
const OtpVerification = React.lazy(() => import('views/OtpVerification.jsx'));
// const PbSurvey = React.lazy(() => import('views/PbSurvey.jsx'));
const BajajUpsellData = React.lazy(() => import('views/BajajUpsellData.jsx'));
const ITAssetDetailSurvey = React.lazy(() => import('views/ITAssetDetailSurvey.jsx'));
const QcReport = React.lazy(() => import('views/QcReport.jsx'));
// const TCSSMEFeedback = React.lazy(() => import('views/TCSSMEFeedback.jsx'));
const JagStoryBoard = React.lazy(() => import('views/JagStoryBoard.jsx'));
const UploadMonthlyIncentive = React.lazy(() => import('views/MonthlyIncentive/UploadMonthlyIncentive.jsx'));
const FileUpload = React.lazy(() => import('views/FileUpload/FileUpload.jsx'));
// import UsersWFH = React.lazy(() => import('views/UsersWFH.jsx'));
// import UserStats = React.lazy(() => import('views/UserStats.jsx'));
// import ScoreMaster = React.lazy(() => import('views/ScoreMaster.jsx'));
// import PremiumBucketScore = React.lazy(() => import('views/PremiumBucketScore.jsx'));
// import ChatAgentConfigure = React.lazy(() => import('views/ChatAgentConfigure.jsx'));


const Users = React.lazy(() =>  import('./views/Users'));
const UsersWFH = React.lazy(() => import('views/UsersWFH.jsx'));
const UserStats = React.lazy(() => import('views/UserStats.jsx'));
const ScoreMaster = React.lazy(() => import('views/ScoreMaster.jsx'));
const PremiumBucketScore = React.lazy(() => import('views/PremiumBucketScore.jsx'));
const ChatAgentConfigure = React.lazy(() => import('views/ChatAgentConfigure.jsx'));
const UploadVideo = React.lazy(() => import('views/UploadVideo.jsx'));
const PaymentFailedNotification = React.lazy(() => import('views/PaymentFailedNotification.jsx'));
//const RuleEngine = React.lazy(() => import('views/RuleEngine.jsx'));
//const ITAssetDetailSurveyTwo = React.lazy(() => import('views/ITAssetDetailSurveyTwo.jsx'));
const RejectLeads = React.lazy(() => import('views/RejectLeads.jsx'));
const UnAuthenticated = React.lazy(() => import('views/UnAuthenticated.jsx'));
const AgentsIncentiveCriteria = React.lazy(() => import('views/AgentsIncentiveCriteria.jsx'));
const SuperGroupCriteriaHtml = React.lazy(() => import('views/SuperGroupCriteriaHtml.jsx'));
const C2CTrackingDataService = React.lazy(() => import('views/C2CTrackingDataService.jsx'));
const RenewalPaymentFailed = React.lazy(() => import('views/RenewalPaymentFailed.jsx'));
const MyPODLeads = React.lazy(() => import('views/MyPodLeads.jsx'));
const TLPodLeads = React.lazy(() => import('views/TLPodLeads.jsx'));
const ManageIBQueueMaster = React.lazy(() => import('views/ManageIBQueueMaster.jsx'));
const UploadUserGrades = React.lazy(() => import('views/UploadUserGrades.jsx'));
const RejectedLeadsList = React.lazy(() => import('views/RejectedLeadsList.jsx'));
const QuoteLeadsList = React.lazy(() => import('views/QuoteLeadsList.jsx'));
const EmiPaymentFailed = React.lazy(() => import('views/EmiPaymentFailed.jsx'));
const SosAgentDocListing = React.lazy(() => import('views/SosAgentDocListing.jsx'));
const BulkUpload = React.lazy(() => import('views/BulkUpload.jsx'));
const ChatHistory=React.lazy(()=> import('views/Chat/ChatHistory.jsx'))
const ChatUsers =React.lazy(()=> import('views/Chat/ChatUsers.jsx'))
const AdvisorInfo = React.lazy(() => import("views/Common/OtpVerification/AdvisorInfo"));
// const DBQuery = React.lazy(() => import ('views/RuleEngine/TableQuery/Query.jsx'));
// const RuleEngineApp = React.lazy(() => import("views/RuleEngine/App/RuleEngineApp.jsx"));
const Lottery = React.lazy(() => import("views/Lottery/Lottery.jsx"));
const Lottery_v1 = React.lazy(() => import("views/Lottery/Lottery_v1.jsx"));
const JagWelcome = React.lazy(() => import("views/Lottery/JagWelcome"));
// const SmsInfo = React.lazy(() => import("views/Common/SmsInfoData"));
const CallGallery = React.lazy(() => import('views/CallGallery.jsx'));
const KyaInfoVc = React.lazy(() => import('views/KyaInfoVc.jsx'));
const FosAppointmentData = React.lazy(() => import('views/FosAppointmentData.jsx'));
const AgentDashboard = React.lazy(() => import('views/AgentDashboard.jsx'));
const FosAssignedDetails = React.lazy(() => import('views/FosAssignedDetails.jsx'));
const AgentStories = React.lazy(() => import('views/AgentStories.jsx'));
const FosAllocationPanel = React.lazy(() => import('views/FosAllocationPanel.jsx'));
const AgentSurveyMapping = React.lazy(() => import('views/AgentSurveyMapping.jsx'));
const DkdLottery = React.lazy(() => import('views/DkdLottery.jsx'));
const UploadRenewalLeads = React.lazy(() => import('views/Uploads/UploadRenewalLeads.jsx'));
const DashboardSMEUploads = React.lazy(() => import('views/Uploads/DashboardSMEUploads.jsx'));
const FOSAgentDashboard = React.lazy(() => import('views/FOS/FOSAgentDashboard.jsx'));
const SmeDashboardTrackLead = React.lazy(() => import('views/SmeDashboardTrackLead.jsx'));
const AgentCSATScore = React.lazy(() => import('views/Agents/AgentCSATScore/AgentCSATScore.jsx'));
const FOSRealTimePanel = React.lazy(() => import('views/FOS/FOSRealTimePanel.jsx'));
const CustomerIssues = React.lazy(() => import('views/CustomerIssues.jsx'));
const CreateLead = React.lazy(() => import('views/CreateLead/Index.jsx'));
const FOSSelfiePanel = React.lazy(() => import('views/FOSSelfiePanel.jsx'));
const FOSCityPanel = React.lazy(() => import('views/FOS/FOSCityPanel.jsx'));
const FOSTLDashboard = React.lazy(() => import('views/FOSTLDashboard.jsx'));
const GMCGPACardView = React.lazy(() => import('views/GMCGPA/GMCGPACardView.jsx'));
const MyBookings = React.lazy(() => import('views/MyBookings/MyBookings'));
const AgentLeadRankMapping = React.lazy(() => import('views/AgentLeadRankMapping.jsx'));
const SpinContest = React.lazy(() => import('views/SpinContest/SpinContest'));
const ContestContent= React.lazy(()=>import('views/SpinContest/ContestContent'));
const ContestAgentMapping= React.lazy(()=>import('views/SpinContest/ContestAgentMapping'));
const DuplicateLeads = React.lazy(() => import('views/DuplicateLeads/DuplicateLeads'));
const PfPending = React.lazy(() => import('views/Leads/PfPending.jsx'));
const PfPending_v2 = React.lazy(() => import('views/Leads/PfPending_v2.jsx'));
const PfPending_new_v2 = React.lazy(() => import('views/Leads/PfPending_new_v2.jsx'));
const AMSummaryReport= React.lazy(()=>import('views/AMSummaryReport/AMSummaryReport') );
const FosTLSlotDistribute= React.lazy(()=> import('views/FosTLSlotDistribute/FosTLSlotDistribute'));
const SpinWheel= React.lazy(() => import('views/SpinContest/gaming/SpinWheel'));
const ContestReward= React.lazy(()=>import('views/SpinContest/ContestReward'));
const AgentCrossSellLead= React.lazy(()=> import('views/Agents/AgentCrossSellLead'));
const RMInsurerTransfer= React.lazy(()=> import('views/Conference/RMInsurerTransfer'));
const PaymentOverDueSupervisor= React.lazy(()=> import('views/PaymentOverDueSupervisor'));
const ManageLeadHNILogic = React.lazy(() => import('views/ManageLeadHNILogic.jsx'));
// const AdminLayoutV2 = React.lazy(() => import('./layouts/AdminLayoutV2'));
// const GamingLayoutV2 = React.lazy(() => import('./layouts/GamingLayoutV2'));
// const ClientLayoutV2= React.lazy(()=>import('./layouts/ClientLayoutV2'));
// const AdminLayoutLatest= React.lazy(()=>import('./layouts/AdminLayoutLatest'));
// const ClientLayoutLatest= React.lazy(()=>import('./layouts/ClientLayoutLatest'));
// const GamingLayoutLatest= React.lazy(()=>import('./layouts/GamingLayoutLatest'));
const NotFound= React.lazy(()=> import('views/Common/NotFound/NotFound'));
const PaymentAttemptLeadsSupervisor = React.lazy(() => import('views/PaymentAttemptLeadsSupervisor.jsx'));
const FOSRealTimeTracker = React.lazy(()=>import("views/FOSRealTimeTracker/FOSRealTimeTracker")); 
const SpouseOpportunity = React.lazy(() => import('views/SpouseOpportunity/index.js'));
const RemoveInvalidLeads = React.lazy(() => import('views/RemoveInvalidLeads.jsx'))
const CouponRaisedRequest = React.lazy(() => import('views/CouponRaisedRequest.jsx'));
const CMAgeBucketScoreMaster = React.lazy(() => import('views/CMAgeBucketScoreMaster.jsx'));
const AdultChildScoreMaster = React.lazy(() => import('views/AdultChildScoreMaster.jsx'));
const CityScoreMaster = React.lazy(() => import('views/CityScoreMaster.jsx'));
const MyBookings_V2 = React.lazy(()=>import('views/MyBookings/MyBookings_V2.jsx'));
const PayUScoreRankMapping = React.lazy(() => import('views/PayUScoreRankMapping.jsx'));
const MenuDataAccess = React.lazy(()=> import('views/MenuDataAccess.jsx'));
const UploadUserLimit = React.lazy(()=> import('views/UploadUserLimit.jsx'));
const IncentiveUpload = React.lazy(()=> import('views/IncentiveUpload.jsx'));
const ChurnAssignment = React.lazy(()=> import('views/HealthRenewal/ChurnAssignment.jsx'));
const MISPaymentLinkRequest = React.lazy(()=> import('views/HealthRenewal/MISPaymentLinkRequest.jsx'));
const QuickSightAccess = React.lazy(()=> import('views/QuickSightAccess.jsx'));
const ViewPaymentLinkRequests = React.lazy(()=> import('views/HealthRenewal/ViewPaymentLinkRequests.jsx'));

const UploadAdditionalLeadDetails = React.lazy(()=> import('views/Uploads/UploadAdditionalLeadDetails.jsx'));
const UsersGroup = React.lazy(() => import('views/Agents/UsersGroup.jsx'));
const FosAllocationPanelNew = React.lazy(() => import('views/FosAllocationPanelNew.jsx'));

let routes = [
    
    {
        path: "/dashboard",
        name: "Dashboard",
        icon: "nc-icon nc-bank",
        // component:  <AdminLayoutLatest element={<Dashboard/>}/>,
        component: <Dashboard/>,
        layout: "/admin",
        hide: true,
        authCheck: true,
    },
    {
        path: "/icons",
        name: "Icons",
        icon: "nc-icon nc-diamond",
        component:  <Icons/>,
        layout: "/admin",
        hide: true,
        authCheck: false,
    },
    // {
    //     path: "/notifications",
    //     name: "Notifications",
    //     icon: "nc-icon nc-bell-55",
    //     component: <Notifications />,
    //     layout: "/admin",
    //     hide: true,
    //     authCheck: false,
    // },
    {
        path: "/user-page",
        name: "User Profile",
        icon: "nc-icon nc-single-02",
        // component:  <AdminLayoutLatest element={<UserPage />}/>,
        component: <UserPage />,
        layout: "/admin",
        hide: true,
        authCheck: false,
    },
    {
        path: "/tables",
        name: "Table List",
        icon: "nc-icon nc-tile-56",
        component:  <TableList />,
        layout: "/admin",
        hide: true,
        authCheck: false,
    },
    {
        path: "/typography",
        name: "Typography",
        icon: "nc-icon nc-caps-small",
        component:  <Typography />,
        layout: "/admin",
        hide: true,
        authCheck: false,
    },
    {
        path: "/Users",
        name: "Users",
        icon: "nc-icon nc-circle-10",
        component: <Users />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/alertmaster",
        name: "AlertMaster",
        icon: "nc-icon nc-time-alarm",
        component:  <AlertMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/UsersWFH",
        name: "UsersWFH",
        icon: "nc-icon nc-circle-10",
        // component: <AdminLayoutLatest element={<UsersWFH />}/>,
        component: <UsersWFH />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/UserStats",
        name: "UserStats",
        icon: "nc-icon nc-circle-10",
        component:  <UserStats />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/INVProduct",
        name: "INVProduct",
        icon: "nc-icon nc-minimal-right",
        component: <INVProduct />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    
    {
        path: "/ScoreMaster",
        name: "Score Master",
        icon: "nc-icon nc-minimal-right",
        component:  <ScoreMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/PremiumBucketScore",
        name: "Premium Bucket Score",
        icon: "nc-icon nc-minimal-right",
        component:  <PremiumBucketScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/ChatAgentConfigure",
        name: "Chat Agent Configuration",
        icon: "nc-icon nc-minimal-right",
        component:  <ChatAgentConfigure />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentSurvey",
        name: "Agent Survey",
        icon: "nc-icon nc-minimal-right",
        component:<AgentSurvey />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuickSightVisualization",
        name: "QuickSight Visualization",
        icon: "nc-icon nc-minimal-right",
        component: <QuickSightVisualization />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuickSightTL",
        name: "QuickSight TL",
        icon: "nc-icon nc-minimal-right",
        component: <QuickSightTL />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuickSightTLDashboard",
        name: "QuickSight TL",
        icon: "nc-icon nc-minimal-right",
        component: <QuickSightTLDashboard />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuickSightAMDashboard",
        name: "QuickSight AM",
        icon: "nc-icon nc-minimal-right",
        component: <QuickSightAMDashboard/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuickSightMGRDashboard",
        name: "QuickSight AM",
        icon: "nc-icon nc-minimal-right",
        component: <QuickSightMGRDashboard />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AgentSurveyQuestions",
        name: "Agent Survey Questions",
        icon: "nc-icon nc-minimal-right",
        component: <AgentSurveyQuestions />,
        layout: "/admin",
        hide: true,
        authCheck: true,
    },
    {
        path: "/SurveyForm",
        name: "Survey Form",
        icon: "nc-icon nc-minimal-right",
        component:<SurveyForm />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentGradeRules_Allocation",
        name: "Agent Grade Rules ",
        icon: "nc-icon nc-minimal-right",
        component: <AgentGradeRules_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/GradeRulesMapping_Allocation",
        name: "Grade Rules Mapping ",
        icon: "nc-icon nc-minimal-right",
        component: <GradeRulesMapping_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/Grade_LimitMapping_Allocation",
        name: "Grade Limit Mapping ",
        icon: "nc-icon nc-minimal-right",
        component: <Grade_LimitMapping_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/TermProductGrpMapping_Allocation",
        name: "Term Product Mapping ",
        icon: "nc-icon nc-minimal-right",
        component:<TermProductGrpMapping_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/ProductGrpMapping_Allocation",
        name: "Product Group Mapping ",
        icon: "nc-icon nc-minimal-right",
        component:<ProductGrpMapping_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/HealthAgeGrpMapping_Allocation",
        name: "Health Age Mapping ",
        icon: "nc-icon nc-minimal-right",
        component: <HealthAgeGrpMapping_Allocation />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/PayTermBucketScore",
        name: "Pay Term Bucket Score",
        icon: "nc-icon nc-minimal-right",
        component: <PayTermBucketScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/SupplierScore",
        name: "Supplier Score",
        icon: "nc-icon nc-minimal-right",
        component: <SupplierScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AgentLoginDashboard",
        name: "Agent Login Dashboard",
        icon: "nc-icon nc-minimal-right",
        component: <AgentLoginDashboard />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AnnualIncomeScore",
        name: "Annual Income Score",
        icon: "nc-icon nc-minimal-right",
        component:<AnnualIncomeScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AgeBucketScoreMaster",
        name: "Age Bucket Score",
        icon: "nc-icon nc-minimal-right",
        component: <AgeBucketScoreMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/CustomUtmScore",
        name: "Custom Utm Score",
        icon: "nc-icon nc-minimal-right",
        component: <CustomUtmScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/BrandScore",
        name: "Brand Score",
        icon: "nc-icon nc-minimal-right",
        component:<BrandScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AllocationProcessMaster",
        name: "Allocation ProcessMaster",
        icon: "nc-icon nc-minimal-right",
        component:<AllocationProcessMaster/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/LeadSourceScore",
        name: "Lead Source Score",
        icon: "nc-icon nc-minimal-right",
        component: <LeadSourceScore />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/LeadScoreRankMapping",
        name: "Lead Score Rank Mapping",
        icon: "nc-icon nc-minimal-right",
        component: <LeadScoreRankMapping />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/LeadAgentRankMapping_NewApp",
        name: "Lead Agent Rank Mapping",
        icon: "nc-icon nc-minimal-right",
        component: <LeadAgentRankMapping_NewApp />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/MoodleReport",
        name: "LMS Report",
        icon: "nc-icon nc-chart-bar-32",
        component:<MoodleReport />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/UserCallDetails",
        name: "User Call Details",
        icon: "nc-icon nc-minimal-right",
        component: <UserCallDetails />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/ConferenceDetails",
        name: "Conference Details",
        icon: "nc-icon nc-minimal-right",
        component: <ConferenceDetails />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/CallBackTracker",
        name: "CallBackTracker",
        icon: "nc-icon nc-headphones",
        component: <CallBackTracker />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/CallBackAgentWise",
        name: "CallBackAgentWise",
        icon: "nc-icon nc-headphones",
        component: <CallBackAgentWise />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,, 19, 20],
        authCheck: false,
    },

    // {
    //     path: "/IVRActivityChart",
    //     name: "IVRActivityChart",
    //     icon: "nc-icon nc-headphones",
    //     component:<IVRActivityChart />,
    //     layout: "/admin",
    //     RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },
    // {
    //     path: "/UnRegisteredChart",
    //     name: "UnRegisteredChart",
    //     icon: "nc-icon nc-headphones",
    //     component: <UnRegisteredChart />,
    //     layout: "/admin",
    //     RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },
    {
        path: "/RealTimePanel",
        name: "RealTime Panel",
        icon: "nc-icon nc-chart-bar-32",
        component: <RealTimePanel />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path: "/RealTimePanelQueuewise",
        name: "RealTime Panel Queuewise",
        icon: "nc-icon nc-chart-bar-32",
        component: <RealTimePanelQueuewise />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/chatRoomHistory",
        name: "Chat Room History",
        icon: "nc-icon nc-chart-bar-32",
        component: <chatRoomHistory />,
        layout: "/client",
        hide: true,
        authCheck: false,
    },
    {
        path: "/UnAuthenticated",
        name: "Chat Room History",
        icon: "nc-icon nc-chart-bar-32",
        component: <UnAuthenticated />,
        layout: "/client",
        hide: true,
        authCheck: false,
    },
    {
        path: "/RealTimePanelQueuewiseTwo",
        name: "RealTime Panel Queuewise Two",
        icon: "nc-icon nc-chart-bar-32",
        component: <RealTimePanelQueuewiseTwo />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/UploadIncentiveData",
        name: "Upload Incentive Data",
        icon: "nc-icon nc-chart-bar-32",
        component: <UploadIncentiveData />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20],
        authCheck: true,
    },
    {
        path: "/UploadMonthlyIncentive",
        name: "Upload Monthly Incentive",
        icon: "nc-icon nc-chart-bar-32",
        component: <UploadMonthlyIncentive />,
        layout: "/admin",
        RoleId: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path: "/AgentLeadsNotCalled",
        name: "AgentLeadsNotCalled",
        icon: "nc-icon nc-chart-bar-32",
        component: <AgentLeadsNotCalled />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },    
    {
        path: "/MobileCallingReport",
        name: "Mobile Calling Report",
        icon: "nc-icon nc-chart-bar-32",
        component: <MobileCallingReport />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },    
    {
        path: "/InboundQueueReport",
        name: "Inbound Queue Report",
        icon: "nc-icon nc-chart-bar-32",
        component: <InboundQueueReport />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },    
    {
        path: "/BlockAgent",
        name: "Transfer Call",
        icon: "nc-icon nc-chart-bar-32",
        component: <BlockAgent />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },     
    {
        path: "/RealTimeDashboard",
        name: "Real Time Dashboard",
        icon: "nc-icon nc-chart-bar-32",
        component: <RealTimeDashboard />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false
    },      
    {
        path: "/UploadAgentChatData",
        name: "Upload Agent Chat Data",
        icon: "nc-icon nc-chart-bar-32",
        component: <UploadAgentChatData />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    }, 
    {
        path: "/LoginLogoutHistory",
        name: "Login Logout History",
        icon: "nc-icon nc-chart-bar-32",
        component: <LoginLogoutHistory />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },  
    // {
    //     path: "/OtpVerification",
    //     name: "Otp Verification",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: <AdminLayoutLatest element={<OtpVerification />}/>,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },  
    // {
    //     path: "/OtpVerification",
    //     name: "Otp Verification",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: <ClientLayoutV2 element={<OtpVerification/>}/>,
    //     layout: "/client",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // }, 
    {
        path: "/LoginLogoutDetails",
        name: "Health Login Logout Details",
        icon: "nc-icon nc-chart-bar-32",
        component:<LoginLogoutDetails />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },  
    // {
    //     path: "/PbSurvey",
    //     name: "Policy Bazaar Survey",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: PbSurvey,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },  
    {
        path: "/BajajUpsellData",
        name: "Bajaj Upsell Data",
        icon: "nc-icon nc-chart-bar-32",
        component: <BajajUpsellData />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/ITAssetDetailSurvey",
        name: "IT Asset Detail Survey",
        icon: "nc-icon nc-chart-bar-32",
        component:  <ITAssetDetailSurvey />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },  
    // {
    //     path: "/QcReport",
    //     name: "Qc Report",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component:  <QcReport/>,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },  
    {
        path: "/JagStoryBoard",
        name: "Jag Story Board",
        icon: "nc-icon nc-chart-bar-32",
        component:  <JagStoryBoard />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/UploadVideo",
        name: "Upload Video",
        icon: "nc-icon nc-chart-bar-32",
        component: <UploadVideo />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/PaymentFailedNotification",
        name: "Payment Failed Notification",
        icon: "nc-icon nc-chart-bar-32",
        component: <PaymentFailedNotification />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },      
    {
        path: "/RejectLeads",
        name: "Reject Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <RejectLeads/>,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,

    },      
    {
        path: "/AgentsIncentiveCriteria",
        name: "Agents Incentive Criteria",
        icon: "nc-icon nc-chart-bar-32",
        component:  <AgentsIncentiveCriteria />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/SuperGroupCriteriaHtml",
        name: "SuperGroup CriteriaHtml",
        icon: "nc-icon nc-chart-bar-32",
        component:  <SuperGroupCriteriaHtml />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },      
    {
        path: "/C2CTrackingDataService",
        name: "C2C Tracking Data Service",
        icon: "nc-icon nc-chart-bar-32",
        component:  <C2CTrackingDataService />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },      
    {
        path: "/RenewalPaymentFailed",
        name: "Renewal Payment Failed",
        icon: "nc-icon nc-chart-bar-32",
        component: <RenewalPaymentFailed />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },      
    {
        path: "/MyPodLeads",
        name: "My POD Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <MyPODLeads/>,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },      
    {
        path: "/TLPodLeads",
        name: "TL POD Leads",
        icon: "nc-icon nc-chart-bar-32",
        component: <TLPodLeads />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
 
    },
    {
        path: "/ManageIBQueueMaster",
        name: "ManageIBQueueMaster",
        icon: "nc-icon nc-minimal-right",
        component: <ManageIBQueueMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },      
    {
        path: "/UploadUserGrades",
        name: "Upload User Grades",
        icon: "nc-icon nc-chart-bar-32",
        component:  <UploadUserGrades />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/RejectedLeads",
        name: "Rejected Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <RejectedLeadsList />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/QuoteLeads",
        name: "Quote Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <QuoteLeadsList />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/EmiPaymentFailed",
        name: "EMI Payment Failed Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <EmiPaymentFailed />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },      
    {
        path: "/SosAgentDocListing",
        name: "SosAgentDocListing",
        icon: "nc-icon nc-headphones",
        component:  <SosAgentDocListing />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/BulkUpload",
        name: "Bulk Upload",
        icon: "nc-icon nc-chart-bar-32",
        component:  <BulkUpload />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },  
    {
        path: "/UserMessages",
        name: "User Messages",
        icon: "nc-icon nc-chart-bar-32",
        component:  <BulkUpload />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        name: "ChatHisory",
        icon: "nc-icon nc-chart-bar-32",
        component:  <ChatHistory />,
        path: "/Chat/ChatHistory",
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/Chat/Users",
        name: "ChatUsers",
        icon: "nc-icon nc-chart-bar-32",
        component: <ChatUsers />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/LeadCredit",
        name: "Lead Credit",
        icon: "nc-icon nc-chart-bar-32",
        component:  <BulkUpload/>,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    }, 
    {
        path: "/AdvisorInfo",
        name: "AdvisorInfo",
        icon: "nc-icon nc-chart-bar-32",
        component:  <AdvisorInfo />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    }, 
    // {
    //     path: "/SmsInfo",
    //     name: "SmsInfo",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: SmsInfo,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },
    // {
    //     path: "/SmsInfo",
    //     name: "SmsInfo",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: SmsInfo,
    //     layout: "/client",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },  
    // {
    //     path: "/Query",
    //     name: "DB Query UI",
    //     component: DBQuery,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },
    // {
    //     path: "/RuleEngine",
    //     name: "Rule Engine App",
    //     icon: "nc-icon nc-chart-bar-32",
    //     component: RuleEngineApp,
    //     layout: "/admin",
    //     RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     authCheck: false,
    // },
    {
        path: "/Lottery",
        name: "Lottery",
        icon: "nc-icon nc-chart-bar-32",
        component:  <Lottery />,
        layout: "/gaming",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/Lottery_v1",
        name: "Lottery_v1",
        icon: "nc-icon nc-chart-bar-32",
        component: <Lottery_v1 />,
        layout: "/gaming",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/CallGallery",
        name: "Call Gallery",
        icon: "nc-icon nc-chart-bar-32",
        component:  <CallGallery />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
   },   
    {
        path: "/KyaInfoVc",
        name: "Kya Info Vc",
        icon: "nc-icon nc-chart-bar-32",
        component: <KyaInfoVc />,
        layout: "/client",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    }, 
    {
        path: "/FosAppointmentData",
        name: "FosAppointmentData",
        icon: "nc-icon nc-chart-bar-32",
        component: <FosAppointmentData />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },     

        
    {
        path: "/AgentDashboard",
        name: "Agent Dashboard",
        icon: "nc-icon nc-chart-bar-32",
        component: <AgentDashboard/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path: "/FosAssignedDetails",
        name: "FosAssignedDetails",
        icon: "nc-icon nc-chart-bar-32",
        component: <FosAssignedDetails/>,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentStories",
        name: "AgentStories",
        icon: "nc-icon nc-chart-bar-32",
        component:  <AgentStories />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/FosAllocationPanel",
        name: "FosAllocationPanel",
        icon: "nc-icon nc-chart-bar-32",
        component:  <FosAllocationPanel />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/FosAllocationPanelNew",
        name: "FosAllocationPanelNew",
        icon: "nc-icon nc-chart-bar-32",
        component:  <FosAllocationPanelNew />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentSurveyMapping",
        name: "AgentSurveyMapping",
        icon: "nc-icon nc-chart-bar-32",
        component: <AgentSurveyMapping />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/DkdLottery",
        name: "DkdLottery",
        icon: "nc-icon nc-chart-bar-32",
        component:  <DkdLottery />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentDashboard",
        name: "Agent Dashboard",
        icon: "nc-icon nc-chart-bar-32",
        component: <AgentDashboard />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/Uploads/UploadRenewalLeads",
        name: "UploadRenewalLeads",
        icon: "nc-icon nc-chart-bar-32",
        component: <UploadRenewalLeads />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    }, 
    {
        path: "/Uploads/DashboardSMEUploads",
        name: "DashboardSMEUploads",
        icon: "nc-icon nc-chart-bar-32",
        component: <DashboardSMEUploads />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },     
    {
        path: "/FOS/FOSAgentDashboard",
        name: "FOSAgentDashboard",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSAgentDashboard />,
        layout: "/admin",
        // RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        //RoleId:[13],
        authCheck: false,
    },
    { 
        path: "/SmeDashboardTrackLead",
        name: "Sme Track leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <SmeDashboardTrackLead />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },  
    { 
        path: "/AgentCSATScore",
        name: "AgentCSATScore",
        icon: "nc-icon nc-chart-bar-32",
        component:  <AgentCSATScore />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },  
    {
        path: "/FOS/FOSRealTimePanel",
        name: "FOSRealTimePanel",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSRealTimePanel />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false
    },
    {
        path: "/CustomerIssues",
        name: "CustomerIssues",
        icon: "nc-icon nc-chart-bar-32",
        component:  <CustomerIssues />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/CreateLead",
        name: "Create Lead",
        icon: "nc-icon nc-chart-bar-32",
        component: <CreateLead />,
        layout: "/matrix",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/FOSSelfiePanel",
        name: "FOSSelfiePanel",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSSelfiePanel />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/FOS/FOSCityPanel",
        name: "FOSCityPanel",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSCityPanel/>,
        layout: "/admin",
        // RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        RoleId:[13],
        authCheck: true,
    },
    {
        path: "/FOSTLDashboard",
        name: "FOSTLDashboard",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSTLDashboard />,
        layout: "/admin",
        authCheck: false,
    },
    {
        path: "/GMCGPACardView",
        name: "GMCGPACardView",
        icon: "nc-icon nc-chart-bar-32",
        component: <GMCGPACardView />,
        layout: "/admin",
        // RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        RoleId:[13],
        authCheck: false,
    },
    {
        path: "/FileUpload",
        name: "FileUpload",
        icon: "nc-icon nc-chart-bar-32",
        component:  <FileUpload/>,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/MyBookings",
        name: "MyBookings",
        icon: "nc-icon nc-minimal-right",
        component: <MyBookings />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: false,
    },
    {
        path: "/MyBooking_V2",
        name: "MyBookings",
        icon: "nc-icon nc-minimal-right",
        component: <MyBookings_V2 />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: false,
    },
    {
        path: "/AgentLeadRankMapping",
        name:"  AgentLeadRankMapping",
        icon: "nc-icon nc-chart-bar-32",
        component: <AgentLeadRankMapping />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true
    },
    {
        path: "/SpinContest",
        name:"  SpinContest",
        icon: "nc-icon nc-chart-bar-32",
        component: <SpinContest />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path:"/ContestContent",
        name: "ContestContent",
        icon: "nc-icon nc-chart-bar-32",
        component: <ContestContent />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path:"/ContestAgentMapping",
        name: "ContestAgentMapping",
        icon: "nc-icon nc-chart-bar-32",
        component: <ContestAgentMapping />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
        RoleIdCheck: true
    },
    {
        path: "/FosTLSlotDistribute",
        name:"  FosTLSlotDistribute",
        icon: "nc-icon nc-chart-bar-32",
        component: <FosTLSlotDistribute />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
    },
    {
        path: "/DuplicateLeads",
        name: "Duplicate Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <DuplicateLeads type = {"DuplicateLeads"}/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: true,
    },
    {
        path: "/PfPending",
        name: "Pf Doc Pending",
        icon: "nc-icon nc-chart-bar-32",
        component:  <PfPending />,
        layout: "/admin",
        authCheck: false,
    }, 
    {
        path: "/PfPending_v2",
        name: "Pf Doc Pending",
        icon: "nc-icon nc-chart-bar-32",
        component:  <PfPending_v2 />,
        layout: "/admin",
        authCheck: false,
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        RoleIdCheck: true
    },
    {
        path: "/PfPending_new_v2",
        name: "Pf Doc Pending",
        icon: "nc-icon nc-chart-bar-32",
        component:  <PfPending_new_v2 />,
        layout: "/admin",
        authCheck: false,
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        RoleIdCheck: true
    },  
    {
        path: "/SpinWheel",
        name:"SpinWheel",
        icon: "nc-icon nc-chart-bar-32",
        component: <SpinWheel />,
        layout: "/gaming",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: false,
        RoleIdCheck: true
    },
    {
        path:"/ContestReward",
        name:"ContestReward",
        icon:"nc-icon nc-chart-bar-32",
        component: <ContestReward />,
        layout:"/gaming",
        RoleId:[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: false,
        RoleIdCheck: true
    },
    {
        path: "/AgentCrossSellLead",
        name: "Agent CrossSell Lead",
        icon: "nc-icon nc-chart-bar-32",
        component:  <AgentCrossSellLead />,
        layout: "/admin",
        authCheck: false,
    },
    {
        path:"/AMSummaryReport",
        name: "AMSummaryReport",
        icon: "nc-icon nc-chart-bar-32",
        component: <AMSummaryReport />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path:"/RMInsurerTransfer",
        name: "RMInsurerTransfer",
        icon: "nc-icon nc-chart-bar-32",
        component:  <RMInsurerTransfer />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/LotteryJag2024",
        name: "LotteryJag2024",
        icon: "nc-icon nc-chart-bar-32",
        component: <JagWelcome />,
        layout: "/gaming",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path:"*",
        name:"NotFound",
        icon: "nc-icon nc-chart-bar-32",
        component:  <NotFound />,
        layout: "/",
    },
    {
        path:"/PaymentOverDueSupervisor",
        name: "PaymentOverDueSupervisor",
        icon: "nc-icon nc-chart-bar-32",
        component:  <PaymentOverDueSupervisor />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path:"/PaymentAttemptLeadsSupervisor",
        name: "PaymentAttemptLeadsSupervisor",
        icon: "nc-icon nc-chart-bar-32",
        component:  <PaymentAttemptLeadsSupervisor />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path: "/ManageLeadHNILogic",
        name: "ManageLeadHNILogic",
        icon: "nc-icon nc-chart-bar-32",
        component:  <ManageLeadHNILogic />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/UploadUserLimit",
        name: "UploadUserLimit",
        icon: "nc-icon nc-chart-bar-32",
        component:  <UploadUserLimit />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },{
        path: "/RemoveInvalidLeads",
        name: "RemoveInvalidLeads",
        icon: "nc-icon nc-chart-bar-32",
        component: <RemoveInvalidLeads />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },   

    {
        path:"/CouponRaisedRequest",
        name: "CouponRaisedRequest",
        icon: "nc-icon nc-chart-bar-32",
        component:  <CouponRaisedRequest />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    {
        path:"/FOSRealTimeTracker",
        name: "FOSRealTimeTracker",
        icon: "nc-icon nc-chart-bar-32",
        component: <FOSRealTimeTracker/>,
        layout: "/adminFOS",
        RoleId: [0,1,2,3,4,5,6,7,8,9,10,11,12,19,20],
        authCheck: false,
    },
    {
        path: "/SpouseOpportunity",
        name: "SpouseOpportunity",
        icon: "nc-icon nc-circle-10",
        component: <SpouseOpportunity />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: false,
    },
    {
        path: "/CMAgeBucketScoreMaster",
        name: "CMAgeBucketScoreMaster",
        icon: "nc-icon nc-minimal-right",
        component: <CMAgeBucketScoreMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/AdultChildScoreMaster",
        name: "AdultChildScoreMaster",
        icon: "nc-icon nc-minimal-right",
        component: <AdultChildScoreMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/CityScoreMaster",
        name: "CityScoreMaster",
        icon: "nc-icon nc-minimal-right",
        component: <CityScoreMaster />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/PayUScoreRankMapping",
        name: "PayU Score Rank Mapping",
        icon: "nc-icon nc-minimal-right",
        component: <PayUScoreRankMapping />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/MenuDataAccess",
        name: "MenuDataAccess",
        icon: "nc-icon nc-minimal-right",
        component: <MenuDataAccess />,
        layout: "/adminFOS",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false
        // authCheck: true,
    },
    {
        path: "/IncentiveUpload",
        name: "Upload Incentive Data",
        icon: "nc-icon nc-chart-bar-32",
        component: <IncentiveUpload />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20],
        authCheck: true,
    },
    {
        path: "/SearchLeads",
        name: "Search Leads",
        icon: "nc-icon nc-chart-bar-32",
        component:  <DuplicateLeads type = {"SearchLeads"}/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 19, 20],
        authCheck: true,
    },
    {
        path:"/UploadAdditionalLeadDetails",
        name: "UploadAdditionalLeadDetails",
        icon: "nc-icon nc-chart-bar-32",
        component:  <UploadAdditionalLeadDetails />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: true,
    },
    {
        path: "/HealthRenewal/ChurnAssignment",
        name: "ChurnAssignment",
        icon: "nc-icon nc-chart-bar-32",
        component: <ChurnAssignment />,
        layout: "/admin",
        authCheck: true,
    },
    {
        path: "/UsersGroup",
        name: "UsersGroup",
        icon: "nc-icon nc-chart-bar-32",
        component: <UsersGroup />,
        layout: "/admin",
        RoleId: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 19, 20],
        authCheck: false,
    },
    { 
        path: "/QuickSightAccess",
        name: "QuickSightAccess",
        icon: "nc-icon nc-chart-bar-32",
        component: <QuickSightAccess />,
        layout: "/admin",
        authCheck: true,
    },
    {
        path: "/HealthRenewal/L2PaymentLinkRequests",
        name: "L2 - Manager",
        icon: "nc-icon nc-chart-bar-32",
        component: <ViewPaymentLinkRequests menuaccess ="L2"/>,
        layout: "/admin",
        authCheck: true,
    },
    {
        path: "/HealthRenewal/SupervisorPaymentLinkRequests",
        name: "L1 - Team Leader",
        icon: "nc-icon nc-chart-bar-32",
        component: <ViewPaymentLinkRequests menuaccess = "Supervisor"/>,
        layout: "/admin",
        authCheck: true,
    },
    {
        path: "/HealthRenewal/MISPaymentLinkRequest",
        name: "MISPaymentLinkRequest",
        icon: "nc-icon nc-chart-bar-32",
        component: <MISPaymentLinkRequest />,
        layout: "/admin",
        authCheck: false,
    },
];

export default routes;