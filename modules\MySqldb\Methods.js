const mysqlHelper = require("../../Libs/mysqlHelper");
const conf = require("../../env_config");
const moment = require("moment");
var http = require('https');
var url = require('url');
var request = require('request');
const cache = require('memory-cache');
const axios = require("axios");

exports.FindRoot = function (req, res) {
  let result = false;
  switch (req.query.root) {
    case "unregistered":
      unregistered(req, res);
      result = true;
      break;
    case "getqueue":
      getqueue(req, res);
      result = true;
      break;
    case "unanswered":
      unanswered(req, res);
      result = true;
      break;
    case "answered":
      answered(req, res);
      result = true;
      break;
    case "moreinfo":
      moreinfo(req, res);
      result = true;
      break;
    case "conference":
      conference(req, res);
      result = true;
      break; 
    case "getProduct":
      getProduct(req, res);
      result = true;
      break; 
    case "getIvrQueue":
      getIvrQueue(req, res);
      result = true;
      break;
    case "queueAbandonData":
      queueAbandonData(req, res);
    result = true;
    break; 
    case "getConferenceType":
      getConferenceType(req, res);
    result = true;
    break;  
    case "getProductByIvrType":
      getProductByIvrType(req, res);
    result = true;
    break;  
    case "getQueuesByIvrProduct":
      getQueuesByIvrProduct(req, res);
    result = true;
    break; 
    case "getqueuetwo":
      getqueuetwo(req, res);
    result = true;
    break;
    case "UploadAWSfiles":
      UploadAWSfiles(req, res);
    result = true;
    break;  
    case "getOtherInfoFromCdr":
      getOtherInfoFromCdr(req, res);
      result = true;
      break;    
    
  }

  return result;
}

const DEFAULT_TIMEOUT = 5000;


async function unregistered(req, res) {
  try {
    const AWSSecretConfig = global.SecretConfig;

    let connection = AWSSecretConfig.MYSQL_821_URL;
    //if (moment().diff(req.query.ReportDate, 'days') > 0) {
    //connection = conf.MYSQL_3096_URL;
    //}

    //MYSQL_3096_URL

    let query = `SELECT DATE_FORMAT(n.calltime, "%h") AS hour, 
                DATE_FORMAT(n.calltime, "%Y-%m-%d %h:%i:%s") AS calltime, 
                IFNULL(n.callid,0) callid, 
                IFNULL(n.agentname,'') agentname, 
                IFNULL(DATE_FORMAT(n.entertime, "%Y-%m-%d %h:%i:%s"),'') AS entertime, 
                IFNULL(DATE_FORMAT(n.answertime, "%Y-%m-%d %h:%i:%s"),'') AS answertime, 
                IFNULL(DATE_FORMAT(n.hanguptime, "%Y-%m-%d %h:%i:%s"),'') AS hanguptime, 
                IFNULL(n.callstatus,'') AS callstatus, 
                IFNULL(t.callid,'') AS transferedCallid, 
                IFNULL(t.product,'') AS product, 
                CAST( IFNULL(t.leadid,'') AS CHAR( 50 ) ) AS leadid, 
                IFNULL(t.queuename,'') AS mainQueue, 
                IFNULL(t.agentname,'') AS mainQueueAgent, 
                IFNULL(DATE_FORMAT(t.entertime, "%Y-%m-%d %h:%i:%s"),'') AS mainQueueEnterTime, 
                IFNULL(DATE_FORMAT(t.answertime, "%Y-%m-%d %h:%i:%s"),'') AS mainQueueAnswerTime, 
                IFNULL(DATE_FORMAT(t.hanguptime, "%Y-%m-%d %h:%i:%s"),'') AS mainQueueHangupTime, 
                IFNULL(t.callstatus,'') AS mainQueueCallStatus 
                FROM ncallers n LEFT JOIN ncallers_transfer t ON n.callid=t.orig_callid WHERE n.calltime BETWEEN '` + req.query.ReportDate + ` 10:00:00' AND '` + req.query.ReportDate + ` 19:00:00' AND n.queuename = 'unregistered';`;
    mysqlHelper.mysqlquery(connection, query, function (error, result) {
      if (!error) {
        res.send({
          status: 200,
          data: [result.rows]
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });

  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {

  }
}
async function getqueue(req, res) {
  try {
    const AWSSecretConfig = global.SecretConfig;
    let connection = AWSSecretConfig.MYSQL_AWS_URL;
    let query = `SELECT n.queuename,n.server_ip FROM inbound_queue_master n  WHERE n.type='` + req.query.ProductType + `' AND n.mdProduct=` + req.query.ProductId + ` AND n.IsMobile=` + req.query.IsMobile + ` AND n.IsClaim=` + req.query.IsClaim;
    // console.log(query);

    let cachedgetqueue = cache.get('getqueue');
    if (cachedgetqueue) {
      res.send({
        status: 200,
        data: cachedgetqueue
      });
      return cachedgetqueue;
    } 


    mysqlHelper.mysqlquery(connection, query, function (error, result) {
      if (!error) {
        cache.put('getqueue', [result.rows], (5 * 60 * 1000));
        res.send({
          status: 200,
          data: [result.rows]
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });

  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {

  }


}

async function getqueuetwo(req, res) {
  try {
    const AWSSecretConfig = global.SecretConfig;
    
    let connection = AWSSecretConfig.MYSQL_AWS_URL;
    let query = `SELECT n.queuename,n.server_ip,n.isGroupMasterQueue as isMaster,n.queue_wrap_time as wraptime, A.child 
FROM inbound_queue_master n 
LEFT JOIN
(
	SELECT n.queuename,GROUP_CONCAT(nw.queuename ) AS child
	FROM inbound_queue_master n
	INNER JOIN inbound_queue_master nw ON n.queuename=nw.master_queuename
	WHERE n.isGroupMasterQueue=1
	GROUP BY n.queuename    
) A ON n.queuename=A.queuename  WHERE n.type='` + req.query.ProductType + `' AND n.Product=` + req.query.ProductId ;
    if (req.query.queues != 'Select IVR Queues' && req.query.queues != 'Select') {
      query = query + ` AND n.queuename IN ('` + req.query.queues+ `') `;
    }
  query = query + "order by n.queuename asc";  
    // console.log(query);
    mysqlHelper.mysqlquery(connection, query, function (error, result) {
      if (!error) {
        res.send({
          status: 200,
          data: [result.rows]
        });
      }
      else {
        res.send({
          status: 500,
          error: result
        });
      }
    });

  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {

  }


}

async function unanswered(req, res){
  try {
    const url = conf.dialerApiUrlV2 + 'dialer/unansweredCallList';
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    const data = await axios.post(
      url,
      {
        queuename: req.query.queues
      },
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );
   
    res.status(200).json({
      status: 200,
      message: "success",
      data: data?.data?.data || [],
    });
  } catch (err) {
    if(err.response?.status == 500){
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });  
    }
  }
}

// async function unanswered(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = AWSSecretConfig.MYSQL_821_URL;
//     let query = `SELECT * FROM
//     (
//         (
//             SELECT RAND() as row_num,
//             CASE 
//                 WHEN n.ivrsubtype = 'ctc' THEN 'CTC'
//                 ELSE 'IB'
//             END as call_type,
//             n.callid as orig_callid, n.newcallid AS callid, n.queuename,n.calltime,n.answertime,n.hanguptime,n.entertime,n.mainqueueentertime,
//             TIMESTAMPDIFF(SECOND,n.entertime,n.hanguptime) as waittime,
//             TIMESTAMPDIFF(SECOND,n.calltime,n.entertime) as ivrtime,
//             CASE 
//               WHEN queuename LIKE '%claim%' THEN claimid 
//               ELSE CAST( leadid AS CHAR( 50 ) ) 
//             END AS leadid,
//             calltoagent as assignedagent
//             FROM ncallers n
//             WHERE 1=1
//             AND n.calltime > CURDATE()
//             AND n.answertime IS NULL
//             AND n.hanguptime IS NOT NULL
//             AND n.queuename IS NOT NULL
//             ORDER BY n.calltime DESC 
//         )
//         UNION
//         (
//           SELECT RAND() as row_num, 
//           CASE 
//               WHEN n.ivrsubtype = 'ctc' THEN 'CTC'
//               ELSE 'Transfer'
//           END
//           as call_type,
//           n.callid as orig_callid,
//           CASE 
//               WHEN IFNULL(n.newcallid,'')<>'' THEN n.newcallid
//               ELSE n.callid
//           END
//           AS callid,n.queuename,n.calltime,n.answertime,n.hanguptime,n.entertime,n.mainqueueentertime,
//           TIMESTAMPDIFF(SECOND,n.entertime,n.hanguptime) as waittime,
//           TIMESTAMPDIFF(SECOND,n.calltime,n.entertime) as ivrtime,
//           CAST( leadid AS CHAR( 50 ) ) ,
//           calltoagent as assignedagent
//           FROM ncallers_transfer n
//           WHERE 1=1
//           AND n.calltime > CURDATE()
//           AND n.answertime IS NULL
//           AND n.hanguptime IS NOT NULL
//           AND n.queuename IS NOT NULL
//           ORDER BY n.calltime DESC
//         )
        
//     )T
//     WHERE queuename IN ('` + req.query.queues + `')`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function answered(req, res) {
  try {
    const currentdate = new Date();
    const datetime =
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate();

      const url = conf.dialerApiUrlV2 + 'dialer/answeredCallList';
      const headers = {
        clientKey: conf.Dialer_clientKey,
        source: conf.Dialer_source,
        authKey: conf.Dialer_authKey,
      };
  

    const data = await axios.post(
      url,
      {
        queuename: req.query.queues,
        data: datetime,
      },
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );

    res.status(200).json({
      status: 200,
      message: "success",
      data: data && data.data && data.data.data,
    });
  } catch (err) {

    if(err.response.data.data == null) {
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });
    }
  }
}

// async function answered(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = AWSSecretConfig.MYSQL_821_URL;
//     let query = `SELECT * FROM
//     (
//         (
//             SELECT RAND() as row_num,
//             CASE 
//                 WHEN n.ivrsubtype = 'ctc' THEN 'CTC'
//                 ELSE 'IB'
//             END as call_type,
//             n.callid as orig_callid, n.newcallid AS callid, n.queuename,n.calltime,n.answertime,n.hanguptime,n.entertime,n.mainqueueentertime,
//             TIMESTAMPDIFF(SECOND,n.entertime,n.answertime) as waittime,
//             TIMESTAMPDIFF(SECOND,n.calltime,n.entertime) as ivrtime,
//             CASE 
//               WHEN queuename LIKE '%claim%' THEN claimid 
//               ELSE CAST( leadid AS CHAR( 50 ) ) 
//             END AS leadid
//             FROM ncallers n
//             WHERE 1=1
//             AND n.calltime > CURDATE()
//             AND n.answertime IS NOT NULL
//             AND n.hanguptime IS NOT NULL
//             AND n.queuename IS NOT NULL
//             ORDER BY n.calltime DESC 
//         )
//         UNION
//         (
//             SELECT RAND() as row_num, 
//             CASE 
//                 WHEN n.ivrsubtype = 'ctc' THEN 'CTC'
//                 ELSE 'Transfer'
//             END
//             as call_type,
//             n.callid as orig_callid, n.callid as callid, n.queuename,n.calltime,n.answertime,n.hanguptime,n.entertime,n.mainqueueentertime,
//             TIMESTAMPDIFF(SECOND,n.entertime,n.answertime) as waittime,
//             TIMESTAMPDIFF(SECOND,n.calltime,n.entertime) as ivrtime,
//             CAST( leadid AS CHAR( 50 ) ) 
//             FROM ncallers_transfer n
//             WHERE 1=1
//             AND n.calltime > CURDATE()
//             AND n.answertime IS NOT NULL
//             AND n.hanguptime IS NOT NULL
//             AND n.queuename IS NOT NULL
//             ORDER BY n.calltime DESC  
//         )
        
//     )T
//     WHERE queuename IN ('` + req.query.queues + `')`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function moreinfo(req, res){
  try {
    const url = conf.dialerApiUrlV2 + 'matrix/moreinfodata?debug=1';
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    let body = {
      uniqueid: req.query.uniqueid,
      server: req.query.queueServerIp
    }

    if(req.query.conftype !== ""){
      body.conftype=req.query.conftype;
    }

    const data = await axios.post(
      url,
      body,
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );
   
    res.status(200).json({

      status: 200,
      message: "success",
      data: data?.data?.data || [],
    });
  } catch (err) {
    if(err.response?.status == 500){
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });  
    }
  }
}

// async function moreinfo(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;
//     let server_ip = req.query.queueServerIp;
//     console.log(server_ip);
//     let connection = '';
//     connection = AWSSecretConfig.MYSQL_COMMON_URL;
//     connection.host = req.query.queueServerIp;
//     // switch (req.query.queueServerIp) {
//     //   case "**********":
//     //     connection = conf.MYSQL_9134_URL;
//     //     break;
//     //   case "**********":
//     //     connection = conf.MYSQL_9132_URL;
//     //     break;
//     //   case "************":
//     //     connection = conf.MYSQL_106106_URL;
//     //     break;
//     //   case "**********":
//     //     connection = conf.MYSQL_3046_URL;
//     //     break;
//     //   case "************":
//     //     connection = conf.MYSQL_149106_URL;
//     //     break;
//     //   case "************":
//     //     connection = conf.MYSQL_106105_URL;
//     //     break;
//     // }
//     console.log(connection);
//     //let connection = con;
//     //if (moment().diff(req.query.ReportDate, 'days') > 0) {
//     //connection = conf.MYSQL_3096_URL;
//     //}

//     //MYSQL_3096_URL

//     let query = `select RAND() as row_num,CASE
//     WHEN lastapp = 'Playback' THEN 'Playback'
//     WHEN lastapp = 'Hangup' THEN 'Hangup'
//     WHEN dstchannel = '' THEN 'Playback'
//     ELSE disposition
//     END as custom_disposition,
//     CASE  WHEN lastapp = 'Queue' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1)
//     ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'/',-1),'-',2),'-',-1) END as agentid,
// 	  CASE
//     WHEN lastapp = 'Queue' THEN ''
//     ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1)
//     END as agentno,  calldate,duration as ringtime,dstchannel,accountcode as leadid,uniqueid as callid,disposition,userfield, IFNULL(isassignedagent,"") AS isassignedagent from cdr 
//     where dcontext in ('claimInboundCallHandler','inboundCallHandlerMTM','inboundCallHandler','transferCallHandlerMTM',
//     'multiconference_os_queue','dynamic-nway-main','dynamic-nway','at_transfer','ibhandler','phones') and IFNULL(lastapp,'') IN ('Dial','Playback','Hangup','MeetMe','Queue')`;
    
//     if(req.query.startdate){
//       query = query + `and calldate BETWEEN '` + req.query.startdate + ` 00:00:00' AND '` + req.query.enddate + ` 23:59:59'`;
//     }else{
//       query = query + `and calldate >= curdate()`;
//     } 
//     query = query + `and SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'-',-1) != 'BPW00000' and
//     SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1) != '**********'
//     and uniqueid='` + req.query.uniqueid + `' order by calldate desc`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {

//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: error
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function conference(req, res){
  try {
    const url = conf.dialerApiUrlV2 + 'matrix/conferencedata?debug=1';
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    let body = {
      startdate: req.query.startdate,
      enddate: req.query.enddate,
      conferencetype: req.query.conftype,
    }


    const data = await axios.post(
      url,
      body,
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );
   
    res.status(200).json({

      status: 200,
      message: "success",
      data: data?.data?.data || [],
    });
  } catch (err) {
    if(err.response?.status == 404){
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });  
    }
  }
}

// async function conference(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = AWSSecretConfig.MYSQL_AWS_URL;
//     const today = moment(Date.now()).format('YYYY-MM-DD');
//     const startdate = req.query.startdate;
//     const table = (today == startdate) ? 'ibobconference' : 'ibobconference_new';
//     console.log(table);
//     let query = `SELECT newcallid AS callid,callid as main_callid ,calltime AS calldate,insurer_name,blocked_agent_call,
//     CASE
//       WHEN LENGTH(accountcode) > 1
//       THEN accountcode
//      ELSE bookingid END AS LeadId,
//      p.product_label AS ProductName,
//      q.mdproduct AS ProductId,
//       bookingid AS BookingId,campaign AS Campaign,
//     CASE
//     WHEN initiated_at != ''
//     THEN TIMESTAMPDIFF(SECOND,calltime,initiated_at)
//     ELSE 'N.A' END  AS TTBeforeTransfer,
//   IFNULL(agentid,"") AS TransferedBy,
//   IFNULL(initiated_at,"") AS TransferInitiateTime, 
//   IFNULL(transfertime,"") AS ServiceAgentAnswerTime,
//   CASE
//     WHEN customerdisconnect <= conferencetime
//     THEN ''
//    ELSE IFNULL(conferencetime,"") END AS CallConferenceTime,
//   CASE
//     WHEN customerdisconnect <= conferencetime
//     THEN ''
//    ELSE IFNULL(customerdisconnect,"") END  AS CustomerDisconnectTime,

//   IFNULL(agentdisconnect,"") AS SalesAgentDisconnect,
//   IFNULL(thirdpartydisconnect,"") AS ServiceAgentDisconnect ,
//   IFNULL(tp_disposition,"") AS ThirdPartyDialStatus,
//   CASE
//    WHEN customerdisconnect <= conferencetime
//   THEN IFNULL(customerdisconnect,"")
//   ELSE '' END  AS CustDisconnectBeforeTransfer,
//   IFNULL(tagentid,"") AS TransferedToAgent , 
//   IFNULL(hanguptime,"") AS Hanguptime, 
//   IFNULL(callid,"") AS CallId, 
//   IFNULL(channel,"") AS Channel, 
//   IFNULL(extrachannel,"") AS ExtraChannel, 
//   IFNULL(calltype,"") AS CallType, 
//   IFNULL(serverip,"") AS ServerIp,
//   IFNULL(cancel_at,"") AS ConferenceCancelTime,
//   IFNULL(conference_type,"") AS conference_type FROM  ` + table + ` i
//   LEFT JOIN inbound_queue_master q on i.campaign=q.queuename
//   LEFT JOIN inbound_product_master p on q.product=p.id
//   WHERE  calltime BETWEEN '` + req.query.startdate + ` 00:00:00' AND '` + req.query.enddate + ` 23:59:59' AND conference_type = '` + req.query.conftype + `' AND campaign<>'maxcon'`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function getProduct(req,res) {
  try {
    let query = req.query && req.query.ivrtype;
    let ivrType = query.toLowerCase();

    const url = conf.dialerApiUrlV2+"dialer/getQueuesValue?type=" + ivrType;
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };
    
    const data = await axios.get(url,{ headers: headers, timeout: DEFAULT_TIMEOUT},);
    
    res.status(200).json({
      status: 200,
      message: "success",
      data: data && data.data && data.data.data,
    });
  } catch (err) {
    res.status(500).json({
      status: 500,
      data: err.toString(),
    });
  }
}

// async function getProduct(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = AWSSecretConfig.MYSQL_9135_URL;
//     var con = JSON.parse(req.query.con);
//     console.log(con);
//     var query = 'SELECT DISTINCT product as Id , product as Display FROM asterisk_prog_queues';
//     var whereCondition = [];
//     let mysqlparams = [];
//     if (req.query.con) {
//         query = query + ` WHERE `;
//     for (var key in req.query.con) {
//       let json = JSON.parse(req.query.con[key]);
//       for (var obj in json) {
//            whereCondition.push(`${obj} = '${json[obj]}'`);
//            mysqlparams.push({ key: obj, value: json[obj] });
//       }
//   }
//   query = query + whereCondition.join(' and ');
// }
// query = query + " GROUP BY `product` ORDER BY 1 DESC";
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function getIvrQueue(req,res) {
  try {
    const url =
      conf.dialerApiUrlV2 + "Dialer/getQueuesByIvrProduct?type=" +
      req.query.ivrType.toLowerCase() +
      "&product=" +
      req.query.ivrProduct;
    // console.log("url is ", url);
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };
    const data = await axios.get(url, { headers: headers, timeout: DEFAULT_TIMEOUT},);

    res.status(200).json({
      status: 200,
      message: "success",
      data: data && data.data && data.data.data,
    });
  } catch (err) {
    res.status(500).json({
      status: 500,
      data: err.toString(),
    });
  }
}

// async function getIvrQueue(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;
//     let connection = AWSSecretConfig.MYSQL_9135_URL;
//     var con = JSON.parse(req.query.con);
//     console.log(con);
//     var query = 'SELECT queuename as label,queuename as value,product,serverip FROM asterisk_prog_queues';
//     var whereCondition = [];
//     let mysqlparams = [];
//     if (req.query.con) {
//         query = query + ` WHERE `;
//     for (var key in req.query.con) {
//       let json = JSON.parse(req.query.con[key]);
//       for (var obj in json) {
//            whereCondition.push(`${obj} = '${json[obj]}'`);
//            mysqlparams.push({ key: obj, value: json[obj] });
//       }
//   }
//   query = query + whereCondition.join(' and ');
// }
// query = query + " ORDER BY 1 DESC";
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

// async function queueAbandonData(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;
    
//     let server_ip = req.query.queueServerIp;
//     console.log(server_ip);
//     let connection = '';
//     connection = AWSSecretConfig.MYSQL_COMMON_URL;
//     connection.host = req.query.queueServerIp;

//     // switch (req.query.queueServerIp) {
//     //   case "**********":
//     //     connection = conf.MYSQL_9134_URL;
//     //     break;
//     //   case "**********":
//     //     connection = conf.MYSQL_9132_URL;
//     //     break;
//     //   case "************":
//     //     connection = conf.MYSQL_106106_URL;
//     //     break;
//     //   case "**********":
//     //     connection = conf.MYSQL_3046_URL;
//     //     break;
//     //   case "************":
//     //     connection = conf.MYSQL_149106_URL;
//     //     break;
//     // }
//     console.log(connection);
//     console.log(req.query.conftype);

//     //let connection = con;
//     //if (moment().diff(req.query.ReportDate, 'days') > 0) {
//     //connection = conf.MYSQL_3096_URL;
//     //}

//     //MYSQL_3096_URL

async function queueAbandonData(req, res){
  try {
    const url = conf.dialerApiUrlV2 + 'matrix/queueAbandonData?debug=1';
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    let body = {
      startdata: req.query.startdate,
      enddata: req.query.enddate,
      queues: "'"+req.query.queues+"'",
      server: req.query.queueServerIp,
    }

    if(req.query.conftype !== ""){
      body.conftype=req.query.conftype;
    }

    const data = await axios.post(
      url,
      body,
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );
   
    res.status(200).json({

      status: 200,
      message: "success",
      data: data?.data?.data || [],
    });
  } catch (err) {
    if(err.response?.status == 500){
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });  
    }
  }
}

//     let query = `select RAND() as row_num,queuename,dstchannel,userfield,conference_type,CASE
//     WHEN lastapp = 'Playback' THEN 'Playback'
//     WHEN lastapp = 'Hangup' THEN 'Hangup'
//     WHEN dstchannel = '' THEN 'Playback'
//     ELSE disposition
//     END as custom_disposition,
//     SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1) AS agentno,
//     SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'-',-1) AS agentid,
//     calldate,duration as ringtime,dstchannel,accountcode as leadid,uniqueid as callid,disposition,userfield from cdr where 
//     dcontext in ('claimInboundCallHandler','inboundCallHandlerMTM','inboundCallHandler','transferCallHandlerMTM','multiconference_os_queue','dynamic-nway-main','dynamic-nway','at_transfer') and IFNULL(lastapp,'') IN ('Dial','Playback','Hangup','MeetMe') 
//     and calldate BETWEEN '` + req.query.startdate + ` 00:00:00' AND '` + req.query.enddate + ` 23:59:59' 
//     and SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'-',-1) != 'BPW00000' and
//     SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1) != '**********'
//     and queuename in('` + req.query.queues + `')`;
//     if (req.query.conftype != 0) {
//       query = query + ` and conference_type ='` + req.query.conftype + `'`;
//     }
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function getConferenceType(req, res) {
  try {
    const url = conf.dialerApiUrlV2+"matrix/getConferenceType?type=";
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    const data = await axios.post(url,
      {
        status: "active"
      },
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    )

    const newData = data?.data?.data;

    let item = []

    if(newData){
      for (let i = 0; i < newData.length; i++) {
        let obj = {};
        obj['Id'] = newData[i].conference_type;
        obj['Display'] = newData[i].label;
        item.push(obj);
      }
    }
    
    res.send({
      status: 200,
      data: item,
      message: "Success"
    });
  } catch (err) {
    res.status(500).json({
      status: 500,
      data: err.toString(),
    });
  }
}

// async function getConferenceType(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;
    
//     let connection = AWSSecretConfig.MYSQL_AWS_URL;
//     let query = `SELECT conference_type as Id, label as Display FROM conference_type WHERE is_active=1`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }

// }

async function getProductByIvrType(req, res) {
  try {
    let query = req.query && req.query.ProductType;
    let ProductType = query.toLowerCase();

    const url = conf.dialerApiUrlV2+"dialer/getQueuesValue?type=" + ProductType;
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };
    
    const data = await axios.get(url, { headers: headers, timeout: DEFAULT_TIMEOUT });
    
    res.status(200).json({
      status: 200,
      message: "success",
      data: data && data.data && data.data.data,
    });
  } catch (err) {
    res.status(500).json({
      status: 500,
      data: err.toString(),
    });
  }
}

// async function getProductByIvrType(req, res) {
//   try {

//     let cachedgetqueue = cache.get('getProductByIvrType');
    
//     // if (cachedgetqueue) {
//     //   console.log(cachedgetqueue)
//     //   res.send({
//     //     status: 200,
//     //     data: cachedgetqueue
//     //   });
//     //   return cachedgetqueue;
//     // } 

//     let connection = conf.MYSQL_AWS_URL;
//     let query = `SELECT distinct n.id as Id,n.product_label as Display FROM inbound_product_master n join inbound_queue_master i where i.product=n.id and i.type='` + req.query.ProductType  + `'`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         //cache.put('getProductByIvrType', [result.rows], (5 * 60 * 1000));
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function getQueuesByIvrProduct(req, res) {
  try {
    const url =
      conf.dialerApiUrlV2 + "Dialer/getQueuesByIvrProduct?type=" +
      req.query.ProductType.toLowerCase() +
      "&product=" +
      req.query.ProductId;
    // console.log("url is ", url);
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };
    const data = await axios.get(url, { headers: headers, timeout: DEFAULT_TIMEOUT},);

    res.status(200).json({
      status: 200,
      message: "success",
      buildServer: 321,
      data: data && data.data && data.data.data,
    });
  } catch (err) {
    res.status(500).json({
      status: 500,
      data: err.toString(),
    });
  }
}

// async function getQueuesByIvrProduct(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = AWSSecretConfig.MYSQL_AWS_URL;
//     console.log(req.query);
//     let query = `SELECT distinct queuename as label,queuename as value, server_ip as serverip FROM inbound_queue_master n WHERE n.type='` + req.query.ProductType + `' AND n.product=` + req.query.ProductId
//     + ` order by queuename asc`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {

//   }
// }

async function UploadAWSfiles(req, res) {
  // console.log('here',__filename);
try {
  const AWS = require('aws-sdk');
  // Create S3 service object
  const s3 = new AWS.S3();
  // Set the region 
  AWS.config.update({ region: 'ap-south-1' });
  // **DO THIS**:
  //   Replace BUCKET_NAME with the name of the bucket,  FILE_NAME with the name of the file you want to upload (including relative page), and EXPIRATION with the duration in validaty in seconds (e.g 60 *5)
  const myBucket = 'cctecbuckt'
  const signedUrlExpireSeconds = 3600

  const fileName = __filename//req.query['file-name'];
  const fileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"//req.query['file-type'];
  const s3Params = {
    Bucket: "asterisk-log",
    Key: 'mykey',
    Expires: 60,
    ContentType: fileType,
    ACL: 'public-read'
  };

  var signedUrlPut = '';
  s3.getSignedUrl('putObject', s3Params, (err, data) => {
    if(err){
      console.log(err);
      res.send({
        status: 500,
        message: err
      });
    }
    signedUrlPut = data;
    const returnData = {
      signedRequest: data,
      url: `https://asterisk-log.s3.ap-south-1.amazonaws.com/${fileName}`
    };
     res.write(JSON.stringify(returnData));
  });
  debugger;
  var signedUrlRead = s3.getSignedUrl('getObject', s3Params);
  // console.log('READ', signedUrlRead);

  var remoteUrl = 'https://asterisk-log.s3.ap-south-1.amazonaws.com';
  var sourceRequest = request.get(remoteUrl);
  sourceRequest.on('response', onResponse);

    res.end();
}
catch (e) {
  console.log(e);
  res.send({
    status: 500,
    message: e
  });
}
finally {

}}

function onResponse(res) {
  var contentLength = res.headers['content-length'];
  // console.log(contentLength);
  // console.log('On response');

  res.pipe(getUploadStream(signedUrlPut, contentLength));
}

function getUploadStream(repoteUrl, contentLength) {
  var opts = url.parse(repoteUrl);
  opts.method = 'PUT';
  opts.headers = {
      'Content-Length': contentLength
}
var req = http.request(opts, onRequest);

}

function onRequest(res) {
  // console.log('STATUS: ' + res.statusCode);
  // console.log('HEADERS: ' + JSON.stringify(res.headers));
  res.setEncoding('utf8');
  res.on('data', function(chunk) {
      // console.log('BODY: ' + chunk);
  });
  res.on('end', function() {
      // console.log('No more data in response.')
  });
}

async function getOtherInfoFromCdr(req, res){
  try {
    const url = conf.dialerApiUrlV2 + 'matrix/getotherinfofromcdr?debug=1';
    const headers = {
      clientKey: conf.Dialer_clientKey,
      source: conf.Dialer_source,
      authKey: conf.Dialer_authKey,
    };

    let body = {
      callid: req.query.callId,
      server: req.query.connectionIp
    }

    const data = await axios.post(
      url,
      body,
      { headers: headers, timeout: DEFAULT_TIMEOUT},
    );
   
    res.status(200).json({

      status: 200,
      message: "success",
      data: data?.data?.data || [],
    });
  } catch (err) {
    if(err.response?.status == 500){
      res.status(200).json({
        status: 200,
        data: []
      })
    } else {
      res.status(500).json({
        status: 500,
        data: err.toString(),
      });  
    }
  }
}

// async function getOtherInfoFromCdr(req, res) {
//   try {
//     const AWSSecretConfig = global.SecretConfig;

//     let connection = '';
//     connection = AWSSecretConfig.MYSQL_COMMON_URL;
//     connection.host = req.query.connectionIp;

//     let query = `select RAND() as row_num,CASE
//     WHEN lastapp = 'Playback' THEN 'Playback'
//     WHEN lastapp = 'Hangup' THEN 'Hangup'
//     WHEN dstchannel = '' THEN 'Playback'
//     ELSE disposition
//     END as custom_disposition,
//     CASE
//     WHEN lastapp = 'Queue' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1) 
//     ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'-',-1)
//     END as agentid,
// 	  CASE
//     WHEN lastapp = 'Queue' THEN ''
//     ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1)
//     END as agentno,  calldate,duration as ringtime,dstchannel,accountcode as leadid,uniqueid as callid,disposition,userfield, IFNULL(isassignedagent,"") AS isassignedagent from cdr 
//     where dst='tryblocked' and dcontext in ('claimInboundCallHandler','inboundCallHandlerMTM','inboundCallHandler','transferCallHandlerMTM',
//     'multiconference_os_queue','dynamic-nway-main','dynamic-nway','at_transfer','ibhandler','phones') and IFNULL(lastapp,'') IN ('Dial','Playback','Hangup','MeetMe','Queue')`;
    
//     query = query + `and SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'@',1),'-',-1) != 'BPW00000' and
//     SUBSTRING_INDEX(SUBSTRING_INDEX(dstchannel,'-',1),'/',-1) != '**********'
//     and uniqueid='` + req.query.callId + `'
//     order by calldate desc`;
//     console.log(query);
//     mysqlHelper.mysqlquery(connection, query, function (error, result) {
//       if (!error) {
//         res.send({
//           status: 200,
//           data: [result.rows]
//         });
//       }
//       else {
//         res.send({
//           status: 500,
//           error: result
//         });
//       }
//     });

//   }
//   catch (e) {
//     console.log(e);
//     res.send({
//       status: 500,
//       message: e
//     });
//   }
//   finally {
//     //console.log('1234');
//   }
// }
