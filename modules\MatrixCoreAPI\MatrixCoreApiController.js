const tblList = require("../constants");
const methods = require("./MatrixCoreApiMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");

async function UpdateFOSCityAssignment(req, res) {
    try {
       await methods.UpdateFOSCityAssignment(req, res);
    } catch (err) {
        console.log("Error in UpdateFOSCityAssignment: ",err);
        res.send({
            status: 500,
            error: "Error"
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function RejectLead(req, res) {
    try {
       await methods.RejectLead(req, res);
    } catch (err) {
        console.log("Error in RejectLead: ",err);
        res.send({
            status: 500,
            error: "Error"
        });
    }
}

async function CreateLead(req, res) {
    try {
       await methods.CreateLead(req, res);
    } catch (err) {
        console.log("Error in CreateLead: ",err);
        res.send({
            status: 500,
            error: "Error"
        });
    }
}
async function GetCouponRaiseRequest(req, result){
    try{
        methods.GetCouponRaiseRequest(req, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}
async function FetchAgentPredefinedUrl(req, result){
    try{
        methods.FetchAgentPredefinedUrl(req, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}
async function GetCouponDataByLeadId(req, result){
    try{
        methods.GetCouponDataByLeadId(req, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: err
        });
    }
}
async function UpdateCouponRaiseRequest(request, result){
    try{
        methods.UpdateCouponRaiseRequest(request, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}
async function SendDuplicateOtp(request, result){
    try{
        methods.SendDuplicateOtp(request, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}
async function VerifyDuplicateOtp(request, result){
    try{
        methods.VerifyDuplicateOtp(request, result)
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}
async function GetLeadOnlyURL(req, result){
    try{
        methods.GetLeadOnlyURL(req, result);
    }
    catch(err){
        result.send({
            status: 500,
            error: "Error"
        });
    }
}

async function UploadFileToS3BucketPvt(req, res) {
    try {
        methods.UploadFileToS3BucketPvt(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        
    }
}

module.exports = {
    UpdateFOSCityAssignment: UpdateFOSCityAssignment,
    RejectLead: RejectLead,
    CreateLead: CreateLead,
    GetCouponRaiseRequest : GetCouponRaiseRequest,
    FetchAgentPredefinedUrl : FetchAgentPredefinedUrl,
    UpdateCouponRaiseRequest : UpdateCouponRaiseRequest,
    GetCouponDataByLeadId:GetCouponDataByLeadId,
    SendDuplicateOtp : SendDuplicateOtp,
    VerifyDuplicateOtp : VerifyDuplicateOtp, 
    GetLeadOnlyURL : GetLeadOnlyURL,
    UploadFileToS3BucketPvt : UploadFileToS3BucketPvt
};