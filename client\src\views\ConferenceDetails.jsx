
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, GetFileExists, GetAwsRecordingUrl
} from "../store/actions/CommonAction";
import {
  GetMySqlData
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser, fnDatatableCol, hhmmss } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDown';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import DateRange from "./Common/DateRange"
import 'react-datetime/css/react-datetime.css'



// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class ConferenceDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      root: "conference",
      PageTitle: "ConferenceDetails",
      ConferenceDetails: [],
      ProductId: 0,
      ReportDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
      ReportTime: null,
      SelectedSupervisors: [],
      ConfType: '',
      startdate: moment().subtract(6, 'days').format("YYYY-MM-DD 00:00:00"),
      // enddate: moment().format("YYYY-MM-DD"),
      //startdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
      enddate: moment().format("YYYY-MM-DD hh:mm:ss"),
      // maxdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
      showMoreInfoModal: false,
      MoreInfoData: [],
      addClass: "fa fa-play-circle",
      confTypes: [],
    };
    this.conftypechange = this.conftypechange.bind(this);
    this.columnlist = [
      {
        label: "CallId",
        name: "callid",
        width: "150px",
        type: "string",
        cell: row => <div className="callid">{row.callid ? row.callid : "N.A"}</div>,
      },
      {
        label: "ServerIp",
        name: "ServerIp",
        hide: true,
        cell: row => <div className="serverip">{row.ServerIp ? row.ServerIp : "N.A"}</div>,
      },
      {
        label: "LeadId",
        name: "LeadId",
        searchable: true
      },
      {
        label: "Product",
        name: "ProductName",
      },
      {
        label: "BookingId",
        "name": "BookingId",
        searchable: true,
      },
      {
        label: "Campaign",
        name: "Campaign",
      },
      {
        label: "TP Call Type",
        name: "blocked_agent_call",
        cell: row => <div className="insurer_name">{row.blocked_agent_call ? row.blocked_agent_call : "Queue"}</div>,
      },
      {
        label: "Insurer",
        name: "insurer_name",
        cell: row => <div className="insurer_name">{row.insurer_name ? row.insurer_name : "N.A"}</div>,
      },
      {
        label: "TTBeforeTransfer",
        name: "TTBeforeTransfer",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.TTBeforeTransfer),
      },
      {
        label: "Transfered By",
        name: "TransferedBy",
        searchable: true
      },
      {
        label: "TransferInitiateTime",
        name: "TransferInitiateTime",
        cell: row => <div className="calldate">{row.TransferInitiateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.TransferInitiateTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",
      },
      {
        label: "TP Answer Time",
        name: "ServiceAgentAnswerTime",
        cell: row => <div className="calldate">{row.ServiceAgentAnswerTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.ServiceAgentAnswerTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",
      },
      {
        label: "CallConferenceTime",
        name: "CallConferenceTime",
        cell: row => <div className="calldate">{row.CallConferenceTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.CallConferenceTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "ConferenceCancelTime",
        name: "ConferenceCancelTime",
        cell: row => <div className="calldate">{row.ConferenceCancelTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.ConferenceCancelTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "CustomerDisconnectTime",
        name: "CustomerDisconnectTime",
        cell: row => <div className="calldate">{row.CustomerDisconnectTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.CustomerDisconnectTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "Disconnect(First Agent)",
        name: "SalesAgentDisconnect",
        cell: row => <div className="calldate">{row.SalesAgentDisconnect ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.SalesAgentDisconnect}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "Disconnect(TP)",
        name: "ServiceAgentDisconnect",
        cell: row => <div className="calldate">{row.ServiceAgentDisconnect ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.ServiceAgentDisconnect}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        name: "TransferedToAgent",
        label: "TP Agent",
        type: "string",
        cell: row => <div className="transferedagent">{row.TransferedToAgent ? row.TransferedToAgent : "N.A"}</div>,
      },
      {
        name: "ThirdPartyDialStatus",
        label: "TP CallStatus",
        type: "string",
        cell: row => <div className="ThirdPartyDialStatus">{row.ThirdPartyDialStatus ? row.ThirdPartyDialStatus : "N.A"}</div>,
      },
      {
        label: "Hanguptime",
        name: "Hanguptime",
        cell: row => <div className="calldate">{row.Hanguptime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.Hanguptime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "More Info",
        name: "More Info", cell: row => <div className="moreinfo"><a onClick={(e) => this.clickMoreinfo(e, row)} className="detailsinfo">
          <i className="fa fa-eye"></i></a>
        </div>
      },

    ];

    this.ConfTypeList = {
      config:
      {
        root: "Products",
        data: [{ Id: "sales_to_service", Display: "Life Hot Call Transfer" }, { Id: "telimedical", Display: "Teli-Medical [BMS Transfer]" }, { Id: "telemax", Display: "Tele-Max" }, { Id: "telehdfc", Display: "Tele-Hdfc" }, { Id: "teleipru", Display: "Tele-Ipru" }, { Id: "teletata", Display: "Tele-Tata" }, { Id: "tltransfer", Display: "TLTransfer" }
          , { Id: "transfer_salesservice", Display: "Service Agent Transfer" }, { Id: 0, Display: "Other" }],
      }
    };

    this.moreinfolist = [
      { name: "callid", selector: "callid" },
      { name: "leadid", selector: "leadid" }, { name: "Calldate", cell: row => <div className="calldate">{row.calldate ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss">{row.calldate}</Moment> : "N.A"}</div> },
      { name: "agentno", selector: "agentno" }, { name: "agentid", selector: "agentid" },
      { name: "duration", selector: "ringtime" }, { name: "disposition", selector: "custom_disposition" },
      {
        name: "Listen", cell: row =>(['Playback','Hangup'].indexOf(row.custom_disposition) > -1)?'No file found':
        <div className="listen">
            {this.getHtmlListenMoreInfo(row)}
        </div>
      },
      // {
      //   name: "Listen", cell: row =>
      //     <div className="listen">
      //       {this.CreateRecordingURL(row)}
      //     </div>
      // }
    ];
  }

  componentDidMount() {

    this.props.GetMySqlData({
      root: "getConferenceType",
    }, function (result) {
      this.setState({ confTypes: result?.data?.data });
    }.bind(this));

  }

  componentWillMount() {
    // this.props.GetMySqlData({
    //   root: this.state.root,
    //   startdate: this.state.startdate,
    //   enddate: this.state.enddate,
    //   conftype: 'sales_to_service',
    // }, function (result) {
    //   this.setState({ ConferenceDetails: result.data.data[0] });
    // }.bind(this));

  }

  componentWillReceiveProps(nextProps) {
    //debugger;
    if (!nextProps.CommonData.isError) {
      //this.setState({ ConferenceDetails: nextProps.CommonData[this.state.root] });
      console.log(nextProps.CommonData[this.state.root]);
    }
  }

  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }

  clickMoreinfo(e, row) {
    var queueip = "**********";
    var uniqueid = row.callid;
    if (row.Campaign == 'telemax') {
      queueip = "**********";
    }
    if (this.state.ConfType == 'telimedical') {
      queueip = row.ServerIp;
    }
    /*if (row.callid == null || row.callid == "") {
      uniqueid = row.main_callid;
      queueip = row.ServerIp;
    }*/
    if (row.blocked_agent_call) {
      queueip = row.ServerIp;
    }
    this.props.GetMySqlData({
      root: "moreinfo",
      uniqueid: uniqueid,
      queueServerIp: queueip,
      startdate: this.state.startdate,
      enddate: this.state.enddate,
    }, function (result) {//debugger;
      this.setState({ showMoreInfoModal: true, MoreInfoData: result?.data?.data});
    }.bind(this));

  }

  fetchConferenceData() {
    //debugger;
    if(this.state.ConfType == "" || this.state.ConfType == "0"){
      toast("Please enter conference type", {type: 'error'});
      return;
    }

    this.props.GetMySqlData({
      root: this.state.root,
      startdate: this.state.startdate,
      enddate: this.state.enddate,
      conftype: this.state.ConfType,
    }, function (result) {
      this.setState({ ConferenceDetails: result?.data?.data });
    }.bind(this));

  }

  play(number) {
    var audio = document.getElementById('audio_' + number);
    var icon = document.getElementById("play" + number);
    if (audio.paused) {
      audio.play();
      icon.classList.remove("fa-play-circle");
      icon.classList.add("fa-stop-circle");
    } else {
      audio.pause();
      audio.currentTime = 0
      icon.classList.remove("fa-stop-circle");
      icon.classList.add("fa-play-circle");
    }
  }

   // CreateRecordingURL(row) {

  //   let userfield = row.userfield;
  //   let dstchannel = row.dstchannel;
  //   let date = moment(new Date(userfield)).format("DD-MM-YYYY");
  //   let hour = moment(new Date(userfield)).format("H");
  //   let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
  //   let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
  //   let callid = row.callid;
  //   console.log(date, hour, datetime, phoneNo, callid);
  //   let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";


  //   if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
  //     return <span>File not found</span>
  //   }

  //   GetFileExists(url, function (params) {
  //     //debugger;
  //     if (params && params.status && params.status != 404) {
  //     }
  //     else {
  //       document.getElementById("span_" + row.row_num).innerHTML = "File not found";
  //     }
  //   });

  //   //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/12-05-2020/10/1589258668.10883-20200512101428-07509883158.wav";
  //   return <span id={"span_" + row.row_num}><audio src={url} id={"audio" + "_" + row.row_num}></audio>
  //     <i className={this.state.addClass} id={"play" + row.row_num} onClick={(e) => this.play(row.row_num, e)}></i>
  //   </span>;
  // }

  getHtmlListenMoreInfo(row){
    return  (<span id={"span_" + row.row_num} onClick={(e) => this.CreateMoreInfoRecordingURL(e, row)}>
      <i class="fa fa-play-circle listen"></i>
      </span>)
  }

  CreateMoreInfoRecordingURL(e, row){
    var audio = document.getElementById('audio2');
    var number = row.row_num;
    if (audio.paused) {
      document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      let userfield = row.userfield;
      let dstchannel = row.dstchannel;
      let date = moment(new Date(userfield)).format("DD-MM-YYYY");
      let hour = moment(new Date(userfield)).format("H");
      let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
      let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
      let callid = row.callid;
      console.log(date, hour, datetime, phoneNo, callid);
      let recfilename = "recording/"+ date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
      //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
  
  
      if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
        document.getElementById('span_'+ number).innerHTML = 'No File Found';
        return;
      }
     
      //debugger;
          GetAwsRecordingUrl(recfilename, 'asterisk-log' ,function (results) {debugger;
            //debugger;

            console.log("results", results);
            if (results.data.status == 200) {
              let url = results.data.data;
              audio.src = results.data.data;
              document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
              GetFileExists(url, function (params) {
                //debugger;
                if (params && params.status && params.status != 404) {
                  audio.onloadedmetadata = function() {
                    console.log(audio.duration)
                    //setTimeout(function () {
                      audio.play();
                      console.log(audio.duration);
                      if(audio.paused == false && audio.duration > 0 && audio.duration != 'Infinity' && audio.duration != 'NaN'){
                      document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-stop-circle listen"></i>';
                     
                      audio.onended = function() {
                        document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
                      };
                     
                      }else{
                      document.getElementById('span_'+ number).innerHTML = 'No File Found'; 
                      }
                   // }.bind(this), 500);
                  };
                } else {
                  try {
                    document.getElementById("span_" + row.row_num).innerHTML = "File not found";
                  } catch (e) {
                    //console.log('error', e);        
                  }
                }
              });
            
              
            } else {
              document.getElementById('span_'+ number).innerHTML = 'No File Found';
            }
          }.bind(this));
       


    } else {
      //debugger;
      audio.pause();
      audio.currentTime = 0;
      document.getElementById('span_'+ number).innerHTML = '<i class="fa fa-play-circle listen"></i>';

    }

  }
  conftypechange(e, props) {
    this.setState({
      ConfType: e.target.value
    }, function () {
      //this.fetchConferenceData();
    });

  }
  handleChange = (e, props) => {

    if (e._isAMomentObject) {
      this.setState({ ReportDate: e.format("YYYY-MM-DD") }, function () {
        this.fetchCallBackData();
      });
    }


  }

  // handleStartDateChange = (e, props) => {

  //   if (e._isAMomentObject) {
  //     this.setState({ startdate: e.format("YYYY-MM-DD") }, function () {
  //       //this.fetchCallBackData();
  //     });
  //   }


  // }

  // handleEndDateChange = (e, props) => {

  //   if (e._isAMomentObject) {
  //     this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
  //       //this.fetchCallBackData();
  //     });
  //   }


  // }

  handleStartDateChange = (StartDateValue) => {
    const date = new Date();
    const startDate = new Date(StartDateValue);

    startDate.setDate(startDate.getDate()+6);

    if(date-startDate<=0){
        this.setState({
            startdate: StartDateValue,
            enddate: moment().format("YYYY-MM-DD 23:59:59")
        })
    } else {
        this.setState({
            startdate: StartDateValue,
            enddate: moment(StartDateValue).add(6, 'days').format("YYYY-MM-DD 23:59:59")
        })
    }
  }

  handleEndDateChange = (currentDate) => {
    this.setState({ enddate: currentDate});
  }


  render() {
    //const columns = this.columnlist;
    const columns = this.fnDatatableCol();
    const moreinfocolumns = this.moreinfolist;
    const { items, PageTitle, ConferenceDetails, showAlert, AlertMsg, AlertVarient, ReportTime, MoreInfoData } = this.state;
    console.log(ConferenceDetails);
    let selectedLeads = [];


    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="conftype_dropdown">
                        <DropDown firstoption="Select Conference Type" items={this.state.confTypes} onChange={this.conftypechange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    {/* <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.startdate}
                        isValidDate={this.validation}
                        onChange={moment => this.handleStartDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.enddate}
                        isValidDate={this.validationEndDate}
                        onChange={moment => this.handleEndDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col> */}
                    <Col md={4} style={{display: 'flex', gap: '1rem', marginTop: '-28px'}}>
                        <DateRange 
                            days={6}
                            FromDate= {" "} 
                            ToDate= {" "}
                            startDate={this.state.startdate}
                            endDate={this.state.enddate}
                            EndDateFixed={true}
                            onStartDate={this.handleStartDateChange}
                            onEndDate={this.handleEndDateChange}
                            >
                        </DateRange>
                    </Col>
                                        
                    <Col md={1}>
                      <Button variant="primary" onClick={() => this.fetchConferenceData()}>Fetch</Button>
                    </Col>
                    <Col md={2}>
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={(ConferenceDetails && ConferenceDetails.length > 0) ? ConferenceDetails : []}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
          <audio src="" id={"audio2"}></audio>
          <Modal show={this.state.showMoreInfoModal} onHide={() => this.setState({ showMoreInfoModal: false })} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                <DataTable
                  columns={moreinfocolumns}
                  data={(MoreInfoData && MoreInfoData.length > 0) ? MoreInfoData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense

                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>



        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetMySqlData
  }
)(ConferenceDetails);