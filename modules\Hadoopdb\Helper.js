const tblList = require("../constants");
let fs = require('fs');
const path = require("path");

const conf = require("../../env_config");
const AthenaExpress = require("athena-express");
const AWS = require("aws-sdk");
const axios = require("axios");
const { GetParsedConfigFromCache } = require("../../modules/common/CommonMethods");

const hive = require('hive-driver');
const { TCLIService, TCLIService_types } = hive.thrift;
const client = new hive.HiveClient(
  TCLIService,
  TCLIService_types
);
const utils = new hive.HiveUtils(
  TCLIService_types
);

async function getHadoopData(queryString) {
  const AWSSecretConfig = global.SecretConfig;

  let data = client.connect(
    AWSSecretConfig?.HadoopConnection?.connect,
    new hive.connections.TcpConnection(),
    new hive.auth.PlainTcpAuthentication(AWSSecretConfig?.HadoopConnection?.cred)
  ).then(async client => {
    const session = await client.openSession({
      client_protocol: TCLIService_types.TProtocolVersion.HIVE_CLI_SERVICE_PROTOCOL_V10
    });
    const operation = await session.executeStatement(queryString);
    await utils.waitUntilReady(operation, false, () => { });
    let selectDataOperation = await utils.fetchAll(operation);

    let errorStatus = 0;
    let data = await utils.getResult(selectDataOperation).getValue();
    await operation.close();
    await session.close();
    return { data, errorStatus };

  }).catch(error => {
    errorStatus = 1;
    return { error, errorStatus };
  });

  return data;
}


async function queryAthenaData(queryString, queryName) {
  try {
    let errorStatus = 0;
    //let secret = await axios.get(conf.AWS_CREDENTIALS_URL);

    // const awsCredentials = {
    //   region: conf.AWS_REGION,
    //   accessKeyId: secret.AccessKeyId,
    //   secretAccessKey: secret.SecretAccessKey,
    //   sessionToken: secret.Token
    // };

    // Set the region 
    AWS.config.update({ region: 'ap-south-1' });

    // Create S3 service object
    //const athenaExpressConfig = { aws: AWS     }; //configuring athena-express with aws sdk object
    //const athenaExpress = new AthenaExpress(athenaExpressConfig);

    // const awsCredentials = {
    //   region: 'ap-south-1',
    //   accessKeyId: '********************',
    //   secretAccessKey:  'u1p5RfSh38x4/0FpKWjjl/2RroNl8/rA6z6z8W+S',
    //   sessionToken: 'IQoJb3JpZ2luX2VjEKn//////////wEaCmFwLXNvdXRoLTEiRzBFAiA4ypU+tGeZnp/RYQiMWOg7oQ3pRN1ck/zB+NPb1Vuu5QIhAJUwJKzb2OkY8zjLgKDZ9mZclqVk4QRDBylDff2RVA7SKvwDCBIQAxoMNzIxNTM3NDY3OTQ5IgxXcn5vDKCwBHTID2oq2QPKmgbWzweoAOrWLJiQ/L6yQ+LcVGoz1xzlm4l/KLcLanFLCfKvVDlN90/kDemPFhfkg3Cw6saNetXBv9c51FiX4OcgjKc+j8IwL7apEUpL8/3wy8RL9aQ1Rz6vL+ZPyk1LL5tQMf7FgdCBPnW4h6XLWaC/t31zVJxhttanmbhKhKRFZ8oCIFa4EBqgziBUKJBc36Ci8aMBuPWFt1f9YpX3p6UhGxhO0hGuryF7D2tx2K85FF0DRsPRPXVVTQaN5mDQQ6eR4z5UkgT4vVVxAjbYJ3Qd35bw/Uw4yyxRorUqlu8RGDVnyxpq/JxjJKdsWxN0IgjqUHe7hedkAdU7DxcTYPPwB9IEV0l6zNLC6now0CGoaphvEtUJAorFNkAGoBr9TEgNldvnw3OoEJvk/9XY/1X0SYG4hYNZXhMog6dl7fsEtCyt2eQjtcTyeWjBiAbWGJbtQuq2pdhGQbSx7bKRLpiDT0FKcHI3iKioTtm8UWF0a1JcPgb6fdyiyZsElGoae6zLie1ibHD5wJT/PUKqQjFeRiv8/hlnMxWJAeYmSZgJhFcRJfHcTy1Pi6v0dTJJH0CEugaouv5Sn0F39kQ92eaR/LsbrOMukH+WXnBsdKeJNlF9vC7SAjCC46uKBjqlAcNMJU78Vs7YvogisI2wB27P4rLxtD9iH87qvRLJpQUhfvh5X/VLVICdMcc1rod5gcAKJp4rI4kPv/4a8RshRVNAusCVvwRpid/KCmNj9h1CPob6Kj8nrGxyLsYnOtQ+VtVUmevP1ZZPHX6P6jfJOrU+jEzjBSHfIQf9jaIBp5knsJPzV8pl/nNR9w8q1/YvfnLp590HCCX5pWFQTaoC8FX+B3AHZQ=='
    // };

    //AWS.config.update(awsCredentials);

    const athenaExpressConfig = {
      aws: AWS,
      s3: "s3://aws-athena-query-results-721537467949-ap-south-1",
      getStats: true
    };

    const athenaExpress = new AthenaExpress(athenaExpressConfig);
    let queries=[];

    if (!["GET_HW_LEADS_REOPEN", "GET_HW_LEADS_REOPEN_LAST_YR", "GET_HW_LEADS_REOPEN_2_YR","GET_HW_LEADS_REOPEN_3_YR"].includes(queryName)) {
      queries = queryString.split('|')
    }
    else {
      queries.push(queryString);
    }

    let results = []
    for (let index = 0; index < queries.length; index++) {
      const query = queries[index];
      
      const myQuery = {
        sql: query,
      };
  
      let result = await athenaExpress.query(myQuery);
      
      const formattedResult = JSON.parse(JSON.stringify(result, (key,value) =>
          typeof value === "bigint" ? value.toString() : value
      ));

      results.push(formattedResult);
    }

   
    

    return { errorStatus, results };

  } catch (err) {
    let errorStatus = 1;
    return { errorStatus, message: err.toString() };
  }

}

module.exports = {
  getHadoopData: getHadoopData,
  queryAthenaData: queryAthenaData
}