var express = require("express");
const router = express.Router();
const controller = require("./DialerApiController");
const { ACLlayer } = require("../ACLlayer");


router.get("/setAgentStatus", controller.setAgentStatus);
router.get("/multi_conference",controller.multi_conference);
router.get("/mob2mobcall",controller.mob2mobcall);
router.get("/getQueuesValue",controller.getQueuesValue);
router.get("/getQueues",controller.getQueues);
router.get("/getQueuesByIvrProduct",controller.getQueuesByIvrProduct);
router.post("/unansweredCallList",controller.unansweredCallList);
router.post("/answeredCallList",controller.answeredCallList);
module.exports = router;
