
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

/*import {
  GetCommonData
} from "../store/actions/CommonAction";*/

import { GetMySqlData, InsertData, UpdateData } from "../store/actions/CommonMysqlAction";
import { addRecord, GetCommonData } from "../store/actions/CommonMongoAction";

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import { fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject, getuser } from '../utility/utility.jsx';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";


class ManageIBQueueMaster extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "InboundQueueMaster",
      PageTitle: "Inbound Queue Master",
      FormTitle: "",
      formvalue: {},
      event: "",
      errors: {},
      fields: {},
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleValidation = this.handleValidation.bind(this);
    this.selectedrow = { 
      "id": 0, 
      "queuename" : "",
      "queue_ring_time" : "",
      "ctc_queue_ring_time" : "",
      "master_queuename" : "",
      "master_queue_ring_time" : "",
      "product" : "",
      "mdProduct" : "",
      "group_name" : "",
      "type" : "",
      "is_ctc" : "",
      "IsMobile" : "",
      "IsClaim" : "",
      "ctc_process_type" : "",
      "server_ip" : "",
      "callWaitTime" : "",
      "dialRingTime" : "",
      "startTime" : "",
      "endTime" : "",
      "agentsDial" : "",
      "isGroupMasterQueue" : "",
      //"CreatedOn": new Date() 
    }
    this.columnlist = [
      { name: "id", label: "ID", type: "hidden", hide: true },
      { 
        name: "queuename", 
        label: "Queue Name", 
        searchable: false, 
        required: true,
        type: "string",
        editable: false
      },
      { name: "master_queuename", label: "Master Queue Name", searchable: false, type: "string" },
      {
        name: "product",
        label: "Product",
        type: "dropdown",
        config: {
          root: "Products",
          cols: ["ID AS Id", "ProductName AS Display"],
          con: [{ "Isactive": 1 }]
        },
        required: true,
        searchable: true,  
        //editable: false      
      },
      { 
        name: "mdProduct", 
        label: "Md Product", 
        type: "dropdown",
        config: {
          root: "Products",
          cols: ["ID AS Id", "ProductName AS Display"],
          con: [{ "Isactive": 1 }]
        },
        searchable: false, 
        //type: "string" 
      },
      { name: "group_name", 
        label: "Group Name", 
        type: "dropdown",
        searchable: true,
        config: {
          root: "groupname",
          data: [{ Id: 'inbound', Display: "inbound" }, { Id: 'predictive', Display: "predictive" }],
        } 
      },
      { name: "type", 
        label: "Type", 
        type: "dropdown",
        searchable: true,
        config: {
          root: "type",
          data: [{ Id: 'sales', Display: "sales" }, { Id: 'service', Display: "service" }, { Id: 'claim', Display: "claim" }],
        },
        required: true
      },
      { name: "ctc_process_type", 
        label: "Ctc Process Type", 
        type: "dropdown",
        searchable: true,
        config: {
          root: "ctc_process_type",
          data: [{ Id: 'old', Display: "old" }, { Id: 'new', Display: "new" }],
        },
        required: true
      },
      { name: "server_ip", label: "Server IP", searchable: false, required: true, type: "string" },
      { name: "queue_ring_time", label: "Queue Ring Time", searchable: false, type: "string" },
      { name: "ctc_queue_ring_time", label: "Ctc Queue Ring Time", searchable: false, type: "string" },
      { name: "master_queue_ring_time", label: "Master Queue Ring Time", searchable: false, type: "string" },
      { name: "callWaitTime", label: "Call Wait Time", searchable: false, type: "string" },
      { name: "dialRingTime", label: "Dial Ring Time", searchable: false, type: "string" },
      { name: "startTime", label: "Start Time", searchable: false, type: "string" },
      { name: "endTime", label: "End Time", searchable: false, type: "string" },
      { name: "agentsDial", label: "Agents Dial", searchable: false, type: "string" },
      { name: "is_ctc", label: "IsCtc", searchable: false, type: "bool" },
      { name: "IsMobile", label: "IsMobile", searchable: false, type: "bool" },
      { name: "IsClaim", label: "IsClaim", searchable: false, type: "bool" },
      //{ name: "isActive", label: "IsActive", searchable: false, type: "bool" },
      { name: "isGroupMasterQueue", label: "IsGroupMasterQueue", searchable: false, type: "bool" },
    ];
    let count = 0;
  }


  componentDidMount() {
    this.columnlist.map(col => (
      fnBindRootData(col,this.props)
    ));

    /*this.props.GetMySqlData({
      limit: 10,
      skip: 0,
      c: "L",
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name")
    });*/

    this.props.GetMySqlData({
      root: this.state.root,
      //     cols: {},
      //     c: "L",
      order: "id",
      con: {"isActive": 1 }
    }, function (result) {//debugger;
      //this.setState({ showMoreInfoModal: true, MoreInfoData: result.data.data[0] });
    }.bind(this));

  }
  fnBindStore(col,nextProps){
    if(col.type == "dropdown"){
      let items;
      
      if(nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]){
        
        items = joinObject(nextProps.CommonData[this.state.root],nextProps.CommonData[col.config.root],col.name)
        this.setState({ items: items });
      }
    }
  }
  componentWillReceiveProps(nextProps) {
    

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      
      //setTimeout(function(){
        this.columnlist.map(col => (
          this.fnBindStore(col,nextProps)
        ));
      //}.bind(this),2000);
      
    }

    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200)
        alert(nextProps.CommonData.InsertSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        alert(nextProps.CommonData.UpdateSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "InvType",
    //   selector: "InvTypeID_display",
    //   sortable: true,
    // });

    columns.unshift({
      name: "Action",
      width: "150px",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => { if (window.confirm('Are you sure you wish to delete this queue?')) this.handleDelete(row)}}><i className="fa fa-trash" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }



  /*
  handleCopy(row) {
    this.setState({ formvalue: row, event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  */

  handleEdit(row) {
    //this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
    this.setState({ od: Object.assign({}, row, {}), formvalue: row, event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }

  handleClose() {
    this.setState({ showModal: false });
  }

  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleDelete(row){
      this.setState({ od: Object.assign({}, row, {}), formvalue: row });
      this.props.UpdateData({
        root: this.state.root,
        body: {'isActive' : 0},
        querydata: { "id": row.id }
      }, function (data) {
        if(data.data.status === 200) {
          toast("Queue removed successfully!", { type: 'success' });
        }
        else 
          toast.error("Queue could not be removed"); 
      });
        
      this.props.addRecord({
        root: "History",
        body: {
          module: "InboundQueueMaster",
          action:"Delete",
          od: this.state.od,
          nd: {'isActive' : 0},
          ts: new Date(),
          by: getuser().UserID
        }
      });

    /*this.props.DeleteData({
      root: this.state.root,
      query: {QuestionID : row.QuestionID}
    }, function (data) {
      if(data.data.status === 200) 
        toast("Question removed!", { type: 'success' });
      else 
        toast.error("Question could not be removed"); 
    });*/
  }

  handleSave() {
    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    
    if(this.handleValidation(formvalue)){
      this.fnCleanData(formvalue, true)
      if (this.state.event == "Edit") {
        let id = formvalue["id"];
        delete formvalue["id"]
        this.fnCleanData(formvalue, true);
        this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { "id": id }
        }, function (data) {
          if(data.data.status === 200) 
            toast("Queue updated successfully!", { type: 'success' });
          else 
            toast.error("Queue could not be updated"); 
        });
        
        this.props.addRecord({
          root: "History",
          body: {
            module: "InboundQueueMaster",
            action:"Edit",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserID
          }
        });

        this.props.UpdateData({
          root: 'CampaignRoute',
          body: { "server_ip": formvalue["server_ip"] },
          querydata: { "campaign_name": formvalue["queuename"] }
        });
        this.props.UpdateData({
          root: 'QueueRoute',
          body: { "serverip": formvalue["server_ip"], "isMobile":formvalue["IsMobile"] },
          querydata: { "queuename": formvalue["queuename"] }
        });

      } else {
        let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
        //formvalue["CreatedOn"] = new Date();
        delete formvalue["id"]
        this.fnCleanData(formvalue, false);
        this.props.InsertData({
          root: this.state.root,
          body: formvalue
        }, function (data) {
          if(data.data.status === 200) 
            toast("Queue added successfully!", { type: 'success' });
          else 
            toast.error("Queue could not be added"); 
        });
        
        this.props.addRecord({
          root: "History",
          body: {
            module: "InboundQueueMaster",
            action:"Add",
            od: {},
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserID
          }
        });

        this.props.InsertData({
          root: 'CampaignRoute',
          body: { "campaign_name": formvalue["queuename"], "server_ip": formvalue["server_ip"], "context":formvalue["queuename"] }
        });
        this.props.InsertData({
          root: 'QueueRoute',
          body: { "queuename": formvalue["queuename"], "serverip": formvalue["server_ip"], "isMobile":formvalue["IsMobile"] }
        });
      }
      this.setState({ showModal: false });
    }
  }
  handleChange = (e,props) => {
    let formvalue = this.state.formvalue;
    
    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if(e._isAMomentObject){
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }

    this.setState({ formvalue: formvalue });
  }

  handleValidation(formvalue){
      let errors = {};
      let formIsValid = true;
      
      if(!formvalue["queuename"]){
          formIsValid = false;
          //errors["queuename"] = "This field is required";
          toast.error("Queue name field is required"); 
      }
      
      if(!formvalue["product"]){
          formIsValid = false;
          toast.error("Product field is required"); 
      }
      
      if(!formvalue["group_name"]){
          formIsValid = false;
          toast.error("Group name field is required"); 
      }
      
      if(!formvalue["type"]){
          formIsValid = false;
          toast.error("Type field is required"); 
      }
      
      if(!formvalue["ctc_process_type"]){
          formIsValid = false;
          toast.error("CTC process type field is required"); 
      }
      
      if(!formvalue["server_ip"]){
          formIsValid = false;
          toast.error("Server IP field is required"); 
      }
    
      //this.setState({errors: errors});
      return formIsValid;
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue,event } = this.state;
    
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>

                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form>
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange,event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <Button variant="primary" onClick={this.handleSave}>
                Save Changes
            </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetMySqlData,
    InsertData,
    UpdateData,
    addRecord,
    //GetCommonDataMongo
  }
)(ManageIBQueueMaster);