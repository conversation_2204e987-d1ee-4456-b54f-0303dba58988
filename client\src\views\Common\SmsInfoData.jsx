import React, { Component } from 'react';
import {GetCommonData,InsertData, GetCommonspData, UpdateData, GetAgentCallLogs } from "../../store/actions/CommonAction";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    
    Row,
    Col
  } from "reactstrap";
  import StarRating from "../Common/StarRating";
  import { getUrlParameter, getuser, getDifferenceInMinFromNow } from '../../utility/utility.jsx';
  import { connect } from "react-redux";
  import moment from 'moment';
import { getCookie, setCookie } from 'utility/utility';
import { Redirect } from 'react-router';
import OtpDetails  from './SmsInfo/OtpDetails';
import SmsAdvisorDetails from './SmsInfo/SmsAdvisorDetails';

class SmsInfoData extends Component {
  constructor(props) {
    super(props);
    this.state = {
        customerId : null,
        agentId : null,   
        bgImg: '/Graphic.svg' ,
        advImg: '/Logo.svg' ,
        enterSmallImg:'/enterSmall.svg',
        verifybtniconImg:'/verifybtnicon.svg',
        happystar:'/happy-star.png',
        CertifiedBadge:'/CertifiedBadge.svg',
        pointerImg:'/Pointer.svg',
        advisorData: [],
        advisorDataRating: null,
        StarRating : 0,
        rating:'',
        currentRating: "0",
        kycinfo: null,
        step: null,
        smsId : null,
        IsLoading: true,
    };
  }

  async componentDidMount() {//debugger;
    var body = document.body;

    body.classList.add("otpLayoutSection");

    document.addEventListener("centralLogin", () => { // arrow function
          
      this.onLoginSuccess(); // now you can use this
 
    });
    let triggerid = getUrlParameter('triggerId');
    this.props.GetCommonData({
      root: 'AesDecryption',
      uid: triggerid,
      }, function (results) {
          if (results.data.status == 200) {
          this.setState({ smsId: results.data.data }, () =>
          this.CheckKYCTriggerInfo(this.state.smsId));
      }else{
          this.setState({step : 3, IsLoading: false})
      }
      }.bind(this));
    
  }

  async CheckKYCTriggerInfo(triggerid){
  await this.props.GetCommonspData({
    root: 'GetKYCTriggerInfo',
    c: "L",
    params: [{"triggerId": triggerid}],
      }, function (result) {
      if (result.data && result.data.data[0] ) {//debugger;
          let data = result.data.data[0][0]
          if(data.AgentID){
          this.setState({ kycinfo : result.data.data[0][0], customerId: data.CustomerId, agentId : data.AgentID},
          () =>  this.getadvisorData())
          this.getLoginInfo(data);   
          }else{
            this.setState({ step : 3 , IsLoading: false})
          }      

      }else{
        this.setState({step : 3,  IsLoading: false})
     
      }
     }.bind(this));  
  } 

  getLoginInfo(data){
    let diff = getDifferenceInMinFromNow(data.linkSendTime);
    console.log(getuser().UserID);
    if (diff < 15)   {
      this.setState({ step : 2});
    }else if(getCookie("pbcentrallogin")){
     this.checkCustomerAfterLogin();

    }else{
     this.setState({ step : 1}); 
    }
  
  }

  async getadvisorData(){
      let triggerid = this.state.smsId;
    if(this.state.kycinfo.SmsLinkOpenTime === null){
      this.props.UpdateData({
        root: 'smsTriggerInfo',
        body: {"SmsLinkOpenTime" : moment().format("YYYY-MM-DD HH:mm:ss")},
        querydata: { "TriggerId": triggerid},
        c: "L",
        });
    }
    await this.props.GetCommonData({
        limit: 10,
        skip: 0,
        root: 'AdvisorInfoStarRating',
        con: [{ "UserID": this.state.agentId , "CustomerId" : this.state.customerId , "IsActive" : 1}]

      }, function (results) {
        if (results.data.status == 200 && results.data.data && results.data.data[0]) {
            console.log('advisorstarrating',results.data.data[0]);
        this.setState({ advisorDataRating: results.data.data[0], StarRating : results.data.data[0][0].StarRating });
        }
    }.bind(this));
    // }.bind(this), 10000);

     await this.props.GetCommonData({
        limit: 10,
        skip: 0,
        root: 'AdvisorInfo',
        con: [{ "UserID": this.state.agentId, "IsActive" : 1 }]

      }, function (results) {
        if (results.data.status == 200 && results.data.data && results.data.data[0]) {
            console.log('advisorinfo',results.data.data[0] );
        this.setState({ advisorData: results.data.data[0], IsLoading: false });
        }
    }.bind(this));
    
  }

  onLoginSuccess(){
     this.checkCustomerAfterLogin();

  }

  checkCustomerAfterLogin(){debugger;
    var json ={
      "pbcentrallogin": getCookie("pbcentrallogin"),         
      }

      GetAgentCallLogs(json, function (results) {  
      if(results && results.data){ 
          if(Number(results.data.CustomerId) == Number(this.state.customerId)){
            this.setState({ step :2});
          }else{       
            this.setState({ step : 5});
          }
        }else{       
          this.setState({ step : 5});
        }
      }.bind(this));
  }

    renderSwitch(step) {
      switch (step) {
        case 1:
          
            return  <OtpDetails />
        case 2:
          return <SmsAdvisorDetails
          agentid= {this.state.agentId}
          advisorData= {this.state.advisorData}
          StarRatingValue={this.state.StarRating}
          customerId={this.state.customerId}
          kycinfo={this.state.kycinfo}
          IsLoading={this.state.IsLoading}
          />  
        case 3:
          return <p class="survey-response invalid text-center"><b>Invalid Action</b></p>   
        case 4:
          return <i class="fa fa-spinner fa-spin"></i>  
        case 5:
          return <p class="survey-response invalid text-center"><b>Unauthorised User</b></p>   
      
        default:
          return <></>
      }
    
    }

  render() {
    const { step } = this.state;

    return (
      this.renderSwitch(step)
    );
  }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      UpdateData,
      InsertData
    }
  )(SmsInfoData);