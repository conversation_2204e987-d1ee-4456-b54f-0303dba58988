/* my span booking css */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');


.AMSummaryReport{
    .ManagerHierarchy{
      padding-right: 60px;
     
    }

    .NewTab{
      display: flex;
      justify-content: right;
      right: 62px;
      position: relative;
      top: 2px;
    }

    .tlName{
      text-align: center;
      color: #FA1A1A;
      text-decoration: underline;
      font: normal normal 600 20px/23px Roboto;
      margin-bottom: 20px;
    }

    .t1heading{
         text-align: center;
         font: normal normal 600 23px/23px Roboto;
         color: #09409b;
         margin-bottom: 20px;
         .textBox1{
          font: normal normal 600 23px/23px Roboto;
          width: 35px;
          text-align: center;
          border-radius: 5px;
          border: 1px solid #ccc;
         }
        //  .textBox2{
        //   font: normal normal 600 23px/23px Roboto;
        //   width: 55px;
        //   text-align: center;
        //   border-radius: 5px;
        //   border: 1px solid #ccc;
        //  }
    }

    .t1{
      overflow: auto;
      
      table{
        border: none;
        box-sizing: border-box;
        border-collapse: collapse;
        border-spacing: 0px;
        background-color:#E0E0E0 ;
        border-color: grey;
        width:100%;
        margin:auto;
        thead{
          position: sticky; top: 0px; z-index: 1; 
          background-color: #FAFAFA;
          
          th{
            background: #e8eefa;
            // white-space: nowrap;
            padding: 3px 7px;
            text-align: center;
            color: #09409b;
            text-transform: capitalize;
            font: normal normal 600 12px/21px Roboto;
          }
        }
        tbody{
          border: 1px solid #ddd;
          td{
            text-align: center;
            white-space: nowrap;
            font: normal normal 600 12px/21px Roboto;
            padding: 3px 7px;
          }
          tr:nth-child(even){
                background-color: #e8eefa;
                border-bottom: 1px solid #ccc;
                .t2{
                  width:98%;
                  tr{
                    background-color: #e8eefa;
                    border-bottom: none;
                    td{
                      width:33%;
                    }
                  }
                }
          }
          tr:nth-child(odd){
            background-color: white;
            border-bottom: 1px solid #ccc;
            .t2{
              width:98%;
              tr{
                background-color: white;
                border-bottom: none;
                td{
                  width:33%;
                }
              }
            }
          }
          .total_row > td {
            font-weight: 900;
          }
        }
      }
    }
    .bottomPagination {
      // position: relative;
      // right: 10px;
      // bottom: 10px;
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;

      select {
          width: 155px;
          margin-right: 20px;
          margin-top:10px;
          font: normal normal 600 12px/16px Roboto;
      }

      .pagination {
          margin-bottom: 0px;
          margin-right: 40px;
          margin-top: 10px;
          .page-link {
              font: normal normal 600 12px/16px Roboto;
          }

          .active {
              background-color: transparent;
          }

          .page-item.active .page-link {
              background-color: #0065ff;
              border-color: #0065ff;
          }
      }
  }
   .ShowHistoryBTn{
    position: relative;
    button{
      position: fixed;
      right:1px;
      top:10px;
    }
   }
}