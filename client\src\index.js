import React, { Suspense, lazy, useEffect } from "react";
import ReactD<PERSON> from "react-dom";
import { createBrowserHistory } from "history";
import { BrowserRouter, Routes, Route } from "react-router-dom";

import "assets/scss/paper-dashboard.scss?v=1.1.0";
import "assets/demo/demo.css";
import "perfect-scrollbar/css/perfect-scrollbar.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "react-toastify/dist/ReactToastify.css";
import { createRoot } from "react-dom/client";
// import 'font-awesome/css/font-awesome.min.css';

// import AdminLayout from "layouts/Admin.jsx";
// import ClientLayout from "layouts/Client.jsx";
// import Login from "views/Login.jsx";

import { createStore, applyMiddleware, compose } from "redux";
import { Provider } from "react-redux";
import thunk from "redux-thunk";
import rootReducer from "./store/reducers/rootReducers.jsx";
import { getUrlParameter } from "utility/utility.jsx";
import { getCookie } from "utility/utility.jsx";
import config from "config";
import Loading from "Loading.jsx";
const store = createStore(rootReducer, compose(applyMiddleware(thunk)));

const hist = createBrowserHistory();

// const AdminLayout = lazy(() => import("./layouts/Admin.jsx"));
// const MatrixLayout = lazy(() => import("./layouts/Matrix.jsx"));
// const ClientLayout = lazy(() => import("./layouts/Client.jsx"));
// const GamingLayout = lazy(() => import("./layouts/Gaming.jsx"));

const AdminLayoutV2 = lazy(() => import("./layouts/AdminLayoutV2.jsx"));
const MatrixLayoutV2 = lazy(() => import("./layouts/MatrixLayoutV2.jsx"));
const ClientLayoutV2 = lazy(() => import("./layouts/ClientLayoutV2.jsx"));
const MuiAdminLayout = lazy(()=> import('./layouts/MuiAdminLayout.jsx'));
const GamingLayoutV2 = lazy(() => import("./layouts/GamingLayoutV2.jsx"));

//const Login = lazy(() => import('views/Login.jsx'));
//const OtpVerification = React.lazy(() => import('views/OtpVerification.jsx'));
const UnAuthenticated = React.lazy(() => import("views/UnAuthenticated.jsx"));
const Unauthorized = React.lazy(()=>import("views/MUICommon/Unauthorized.jsx"))

let DefaultComponent = UnAuthenticated;
// let DefaultAdminLayout = AdminLayout;
// let DefaultClientLayout = ClientLayout;
let DefaultGamingLayout = GamingLayoutV2;
let DefaultAdminLayout = AdminLayoutV2;
let DefaultClientLayout = ClientLayoutV2;
let DefaultMatrixLayout = MatrixLayoutV2;
let DefaultMuiAdminLayout = MuiAdminLayout;
// let loader = <div className="PageLoading">
//   <span>
//     Loading...
//   </span>
// </div>;
let loader = <Loading show={true} />;

if (!config.byPassSecurity) {
  var flag = false;
  //auth check iframe and domain check
  console.log("document.referrer", document.referrer);
  if (document.referrer != "") {
    let origin = document.referrer;
    let allowedOrigins = [".policybazaar.com", ".policybazaar.ae"];
    allowedOrigins.forEach(async function (val, key) {
      if (origin.includes(val)) {
        flag = true;
        console.log("flag", flag);
      }
    });
  }
  //cookie check if cookie exist need to improve this check
  var userid = getCookie("AgentId");
  // if (getUrlParameter("u") != userid) {
  //   flag = true;
  // }
  if (userid) {
    flag = true;
  }

  //By pass page level check from cookies
  for (let index = 0; index < config.byPassPages.length; index++) {
    const element = config.byPassPages[index];
    let pageurl = document.location.href.toLowerCase();
    if (pageurl.includes(element.toLowerCase())) {
      flag = true;
    }
  }

  console.log("after allowedOrigins check flag", flag);
  if (!flag) {
    DefaultComponent = UnAuthenticated;
    DefaultAdminLayout = UnAuthenticated;
    DefaultClientLayout = UnAuthenticated;
    DefaultGamingLayout = UnAuthenticated;
    DefaultMatrixLayout = UnAuthenticated;
    DefaultMuiAdminLayout = Unauthorized;
  }
}

// var userid = getCookie("AgentId");
// if(getUrlParameter("u") != userid){
//   DefaultComponent = UnAuthenticated
// }
const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <Provider store={store}>
    {/* <Router history={hist}> */}
    <BrowserRouter history={hist}>
      <Suspense fallback={loader}>
        {/* <Switch>
          <Route path="/admin" render={props => <DefaultAdminLayout {...props} />} />
          <Route path="/client" render={props => <DefaultClientLayout {...props} />} />
          <Route path="/matrix" render={props => <DefaultMatrixLayout {...props} />} />
          <Route path="/gaming" render={props => <DefaultGamingLayout {...props} />} />
          <Route exact path="" render={props => <DefaultComponent {...props} />} />
        </Switch> */}
        <Routes>
          {/* <Route
            exact
            path=""
            render={(props) => <DefaultComponent {...props} />}
          /> */}
          <Route exact path="/admin/*" element={<DefaultAdminLayout />} />
          <Route exact path="/client/*" element={<DefaultClientLayout />} />
          <Route exact path="/matrix/*" element={<DefaultMatrixLayout />} />
          <Route exact path="/gaming/*" element={<DefaultGamingLayout />} />
          <Route exact path="adminFOS/*" element={<DefaultMuiAdminLayout/>}/>
          <Route exact path="/*" element={<DefaultComponent />} />
        </Routes>
      </Suspense>
      {/* </Router> */}
    </BrowserRouter>
  </Provider>
);
