const athenaQueries = {
    "GET_HW_LEADS_REOPEN_3_YR":"with books as ( select distinct bd.leadid, cast(bd.OfferCreatedOn as timestamp) Bkg_DT,ld.customerid, abd.InsuredName,abd.ApplicationNo,abd.PolicyNo,ldp.leadid as <PERSON><PERSON><PERSON>,ldp.LeadRank as Leadrank, abd.SalesAgent, ud.EmployeeID, ud.UserName, spd.planname,spd.suppliershortname, a.age,a.suminsured,prod.term as policy_term,prod.payterm as pay_term, case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end as SI_Buckets,a.issue_flag,cast (a.issue_DT as timestamp) as Issue_Date, case when prod.payterm <=15 then 1 else 0 end as LP_15 ,pd.pincode, case when coalesce(ldp.country,'0') not in ('392','91','999','INDIA','0','NULL') or coalesce(ld.country,'0') not in ('392','91','999','INDIA','0','NULL') then 1 else 0 end as IsNRI, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end as leadsource, sum((CASE WHEN bd.PaymentPeriodicity IS null THEN 1 WHEN bd.PaymentPeriodicity IN ('Half Yearly','HalfYearly','Half-Yearly') THEN 2 WHEN bd.PaymentPeriodicity IN ('SINGLE','Single Premium','Single Pay','Single','Annually','Yearly','5','4','3','2','1','0') THEN 1 WHEN bd.PaymentPeriodicity IN ('Monthly') THEN 12 WHEN bd.PaymentPeriodicity IN ('Quarterly') THEN 4 END)/(case when abd.InstallmentsPaid is null Or abd.InstallmentsPaid=0 then 1 else abd.InstallmentsPaid end)*bd.TotalPremium) APE,a.annualincome, CASE WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) > 2500000 THEN '25L+' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 1500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) <= 2500000 THEN '15-25L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 1500000 THEN '5-15L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 200000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 500000 THEN '2-5L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 0 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 200000 THEN 'LT2L' ELSE 'Others' END AS AI_Category, CASE WHEN pd.gender IN ('2', 'Female', 'M/S', 'MS', 'Mrs.', 'Ms.', 'Ms') THEN 'Female' ELSE 'Male' END AS Gender_Group, case when b.statename is null then pd.state else b.statename end as updated_state, CASE WHEN a.occupation = 'Armed Forces' THEN 'Salaried' WHEN a.occupation = 'Army/Navy/Police' THEN 'Salaried' WHEN a.occupation = 'Business' THEN 'Self-Employed' WHEN a.occupation = 'Business / Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Business/Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Defence (Army/Navy/Other Paramilitiary Forces)' THEN 'Salaried' WHEN a.occupation = 'House Wife' THEN 'Housewife' WHEN a.occupation = 'Housewife' THEN 'Housewife' WHEN a.occupation = 'Police' THEN 'Salaried' WHEN a.occupation = 'Professional' THEN 'Self-Employed' WHEN a.occupation = 'Professional (Salaried)' THEN 'Salaried' WHEN a.occupation = 'Professional(Salaried)' THEN 'Salaried' WHEN a.occupation = 'Salaried' THEN 'Salaried' WHEN a.occupation = 'Salaried (Govt Employee)' THEN 'Salaried' WHEN a.occupation = 'Salaried (Other than Govt)' THEN 'Salaried' WHEN a.occupation = 'Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed / Business' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed/Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Business)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Professional)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed/Professional' THEN 'Self-Employed' WHEN a.occupation = 'Service' THEN 'Salaried' WHEN a.occupation = 'Service- Government Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Private Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Public Sector' THEN 'Salaried' ELSE 'Others' END as Profession_Category, a.education, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end as final_maritial_status, case when pob.ComboTypeId=5 then 1 else 0 end as is_Working_Spouse , case when pob.ComboTypeId=6 then 1 else 0 end as is_HW_Spouse, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end as is_SpouseBkg,pob.combotypeid from sqldb_athena.pbcroma_crm_bookingdetails bd left join (select leadid,applicationno,policytype,salesagent,installmentspaid,pb_date,InsuredName,PolicyNo from sqldb_athena.pbcroma_crm_AdditionalBookingDetails where coalesce(PolicyType,0)=0 and pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) abd on bd.leadid=abd.leadid left join (select bookingid,combotypeid from sqldb_athena.bms_bms_postbookingdetails where pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) pob on pob.bookingid=bd.leadid left join (select planid, planname, supplierid, suppliername,suppliershortname,productid from sqldb_athena.pbcroma_dbo_vwSupplierPlanDetailsAllProduct where productid=7) spd on spd.PlanID=bd.PlanId and spd.SupplierID = bd.SupplierID left join (select leadid,customerid,parentid,dob,annualincome,postcode,utm_source,utm_campaign,leadrank,cityid,leadsource,productid ,pb_date,country from sqldb_athena.pbcroma_crm_Leaddetails where pb_date>=substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7) and productid=7) ld on ld.leadid=bd.LEADID left join (select leadid,productid,IsNRI,education,country,annualincome,occupation,age,planname,suppliershortname,suminsured,issue_flag,issue_DT from sqldb_athena.Analyticslife_dbo_Term_PureBookingData_CRT where productid=7) a on a.leadid=bd.leadid left join (select leadid,city, statename, productid, nationality from sqldb_athena.Analyticslife_dbo_Term_Booking_OtherInfo_CRT where productid=7) b on b.leadid=bd.leadid left join (select leadid,parentid,leadrank,leadsource,productid,cityid,pb_date,country,utm_medium,utm_source,utm_content,utm_campaign,utm_term from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) ldp on ldp.leadid=(case when ld.parentid is null then ld.leadid else ld.parentid end) left join (select leadid,city,state,pincode,gender,maritalstatus from sqldb_athena.bms_bms_ProposalDetails) pd on bd.leadid = pd.leadid left join sqldb_athena.pbcroma_crm_UserDetails ud on abd.SalesAgent=ud.userid left join (select leadid, term, payterm from sqldb_athena.PBCROMA_CRM_productdetails where pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) prod on prod.leadid = bd.leadid left join (Select countryid, country from sqldb_athena.productdb_master_country) c on cast(c.countryid as varchar) = case when cast(ldp.country as varchar)='INDIA' then '392' else ldp.country end where bd.paymentstatus in (300,3002,4002,5002,6002,7002,8002) and bd.pb_date>=substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and bd.pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7) and bd.productid=7 and cast(cast(a.issue_DT as Timestamp) as date) = date_add('DAY', -1094, current_date) and Lower(ldp.leadSource) not in ('reopen') and Lower(ld.leadSource) not in ('reopen') and coalesce(lower(bd.bookingsource),'abcd') not like '%retargeting%' group by bd.leadid, cast(bd.OfferCreatedOn as timestamp),ld.customerid, ldp.leadid, ldp.Leadrank,abd.InsuredName,abd.ApplicationNo,abd.PolicyNo, case WHEN Lower(ldp.leadSource) = 'inbound' THEN '15. Inbound' when Lower(ldp.leadSource) like '%referral%' THEN '17. referral' when ((Lower(ldp.UTM_Medium) in ('term special lead','nri-reopen','term nri lead')) or (Lower(ldp.leadSource) like '%reopen%')) then '13. Reopen' WHEN Lower(ldp.Utm_source) LIKE '%offline%affiliate%' THEN '14. Offline Affiliate' When Lower(ldp.Utm_SOURCE) like 'whatsapp_crm_sales' THEN '16. Whatsapp' When Lower(ldp.Utm_SOURCE) LIKE '%retainer%' or Lower(ldp.UTM_Source) like '%pais%' OR Lower(ldp.Utm_term) like '%singlepagereturn%' THEN '11. Retainer' WHEN Lower(ldp.Utm_source) LIKE '%crm%' THEN '10. CRM' WHEN Lower(ldp.LeadSource) like '%pb%app%' and (Lower(ldp.UTM_Source) like '%organic%' OR coalesce(ldp.UTM_Source,'abc') = 'abc' OR Lower(ldp.Utm_source) = '' OR Lower(ldp.Utm_source) = 'null' or Lower(ldp.Utm_source) = 'none' or Lower(ldp.Utm_source) like '%cj%' OR Lower(ldp.Utm_source) in ('myaccount','twowheeler') ) then '00. APP Organic' when Lower(ldp.LeadSource) like '%pb%app%' and Lower(ldp.UTM_Source) not like '%organic%' then '05. APP Non Organic' WHEN Lower(ldp.Utm_source) IN ('organic','pb_articles','pb_business','providers','pb_news') THEN '02. SEO' WHEN Lower(ldp.Utm_source) IN ('google_brand','yahoo_brand','bing_brand', 'hotstar') THEN '03. Brand Paid' WHEN Lower(ldp.UTM_Source) in ('google','google_','bing','yahoo') and ((Lower(ldp.UTM_Medium) like '%ppc%' or Lower(ldp.UTM_Medium) like '%cpc%') AND ( Lower(ldp.UTM_Medium) NOT like '%remarketing%' and Lower(ldp.UTM_Medium) not like '%disp%')) THEN '04. Non Brand Search' WHEN Lower(ldp.Utm_source) IN ('google') and ( Lower(ldp.UTM_Medium) like '%remarketing%' or Lower(ldp.UTM_Medium) like '%disp%' or Lower(ldp.UTM_Medium) like '%video%') then '06. GDN' WHEN Lower(ldp.UTM_Source) like '%facebook%' then '07. FB Display' WHEN Lower(ldp.Utm_source) IN ('outbrain','taboola','quora') or Lower(ldp.UTM_Medium) like '%native%' or Lower(ldp.UTM_Medium) like '%roadblock%' then '08. Other Display' WHEN Lower(ldp.UTM_Source) = 'commercex_sms' or Lower(ldp.UTM_Medium) like '%affiliate%' then '09. Affiliate' when Lower(ldp.Utm_source) = '' or coalesce(ldp.UTM_Source,'abc') = 'abc'OR Lower(ldp.Utm_source) = 'null' then '01. Direct' else '12.Other' END, ldp.Utm_source,ldp.utm_campaign,ldp.utm_term, abd.SalesAgent, ud.EmployeeID, ud.UserName, ld.DOB, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end, ldp.country, ld.country, b.statename,pd.state,a.issue_flag,cast(a.issue_DT as timestamp), bd.PaymentPeriodicity, abd.InstallmentsPaid, bd.TotalPremium, a.annualincome, a.education, a.occupation, pd.Gender,spd.planname,spd.suppliershortname,a.age,a.suminsured,prod.term,prod.payterm,pd.pincode,case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end, case when pob.ComboTypeId=5 then 1 else 0 end, case when pob.ComboTypeId=6 then 1 else 0 end, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end,pob.combotypeid ), usergroup as ( select distinct bd.salesagent, bd.Bkg_DT,max(ldh.id) as MaxID from books bd left join (select userid,CreatedOn,id from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) ldh on ldh.UserID = bd.SalesAgent and cast(ldh.CreatedOn as timestamp) < bd.Bkg_DT Group by bd.salesagent,bd.Bkg_DT ), book2 as ( select a.*,ldh.GroupID,ugm.UsergroupName as usergroupnameBkg, case when cast(vc.MaritalStatus as varchar) in ('1','s','u','single','unmarried') then 'Single' when cast(vc.MaritalStatus as varchar) in ('2','married','m') then 'Married' when cast(vc.MaritalStatus as varchar) in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when cast(vc.MaritalStatus as varchar) in ('4','widower','w','widowed','widow(er)') then 'Widower' when cast(vc.MaritalStatus as varchar) in ('5','widow') then 'Widow' else 'NA' end as maritial_statusv2, vc.policy_status from Books a inner join Usergroup u on u.salesagent = a.salesagent and u.Bkg_DT= a.Bkg_DT left join (select userid,id,groupid from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) ldh on ldh.userID = u.salesagent and ldh.ID = u.MaxID left join sqldb_athena.pbcroma_crm_UsergroupMaster ugm on ugm.UsergroupID = ldh.GroupID left join voice.life_bu_fraud_framework_2020 vc on vc.leadid=a.leadid ), /*hus_wife as ( select pb.bookingid as Wifeid, be.Comboleadid as husbandID from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) )*/ hus_wife as ( select pb.bookingid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) union select be.Comboleadid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) ), Active_leads as ( Select bk.CustomerID, ld.CreatedON,ld.ProductID, ls.CreatedOn as statusDT,sm.StatusName from books bk inner join (select customerid,leadid, createdon, productid from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) ld on ld.CustomerID=bk.CustomerID and productid=7 inner join (select leadid,createdon, islaststatus, statusid from sqldb_athena.pbcroma_crm_LeadStatus where pb_date >= substring(cast(date_add('Month', -38, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -33, current_date) as varchar),1,7)) ls on ls.LeadID=ld.leadid and IsLastStatus=1 and cast(ls.CreatedOn as timestamp) >= cast(bk.Issue_Date as timestamp) and ls.StatusID in (1,2,3,4,11) left join sqldb_athena.pbcroma_crm_StatusMaster sm on sm.StatusId=ls.StatusID ), dump as ( select b2.*, case when hw.bkg_id is null then 0 else 1 end as spouse_booking_F,case when al.CustomerID is null then 0 else 1 end as is_lead_active from book2 b2 left join hus_wife hw on hw.Bkg_id=b2.leadid left join Active_leads al on al.customerid=b2.customerid ), CID as (select dp.customerid,proposer_gender,proposer_occupation_name from dump dp inner join (select leadid,customerid,proposer_gender,proposer_occupation_name from voice.life_bu_fraud_framework_2020 where ProductID=7)vi on vi.customerid=dp.customerid where proposer_occupation_name='Housewife' or proposer_gender = 2 ) select 'Reopen' as LeadSource, 'Term_Wife_upsell' as Utm_Source,'Term_Wife_upsell' as Utm_Source, 'Wife Upsell | ' || COALESCE(suppliershortname, '') || ' - ' || COALESCE(CAST(suminsured / 10000000 AS VARCHAR), '') || ' Cr | ' || COALESCE(CAST(employeeid AS VARCHAR), '') || ' ( ' || COALESCE(username, '') || ' )' AS Utm_Campaign, 'Term-4' as Utm_Term,dp.leadid as Utm_Medium, 'TermLife' AS ProductName, 7 AS ProductID, 0 as GroupID, dp.leadid as LeadID, 0 AS MobileNo, dp.customerid AS CustomerID, dp.InsuredName AS CustomerName, 510 AS LeadRank, 392 AS Country from dump dp LEFT join CID cid on cid.customerid=dp.customerid where Gender_Group like ('%Male%') and maritial_statusv2 in ('Married') and coalesce(try(cast(cast(Annualincome AS double) AS Integer)), null) >= 500000 and issue_flag=1 and profession_category not in ('Housewife') and IsNRI=0 and spouse_booking_F =0 and is_lead_active = 0 and policy_status='Inforce' and cid.customerid is null",
    "GET_HW_LEADS_REOPEN_2_YR":"with books as ( select distinct bd.leadid, cast(bd.OfferCreatedOn as timestamp) Bkg_DT,ld.customerid, abd.InsuredName,abd.ApplicationNo,abd.PolicyNo,ldp.leadid as Parentid,ldp.LeadRank as Leadrank, abd.SalesAgent, ud.EmployeeID, ud.UserName, spd.planname,spd.suppliershortname, a.age,a.suminsured,prod.term as policy_term,prod.payterm as pay_term, case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end as SI_Buckets,a.issue_flag,cast (a.issue_DT as timestamp) as Issue_Date, case when prod.payterm <=15 then 1 else 0 end as LP_15 ,pd.pincode, case when coalesce(ldp.country,'0') not in ('392','91','999','INDIA','0','NULL') or coalesce(ld.country,'0') not in ('392','91','999','INDIA','0','NULL') then 1 else 0 end as IsNRI, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end as leadsource, sum((CASE WHEN bd.PaymentPeriodicity IS null THEN 1 WHEN bd.PaymentPeriodicity IN ('Half Yearly','HalfYearly','Half-Yearly') THEN 2 WHEN bd.PaymentPeriodicity IN ('SINGLE','Single Premium','Single Pay','Single','Annually','Yearly','5','4','3','2','1','0') THEN 1 WHEN bd.PaymentPeriodicity IN ('Monthly') THEN 12 WHEN bd.PaymentPeriodicity IN ('Quarterly') THEN 4 END)/(case when abd.InstallmentsPaid is null Or abd.InstallmentsPaid=0 then 1 else abd.InstallmentsPaid end)*bd.TotalPremium) APE,a.annualincome, CASE WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) > 2500000 THEN '25L+' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 1500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) <= 2500000 THEN '15-25L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 1500000 THEN '5-15L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 200000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 500000 THEN '2-5L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 0 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 200000 THEN 'LT2L' ELSE 'Others' END AS AI_Category, CASE WHEN pd.gender IN ('2', 'Female', 'M/S', 'MS', 'Mrs.', 'Ms.', 'Ms') THEN 'Female' ELSE 'Male' END AS Gender_Group, case when b.statename is null then pd.state else b.statename end as updated_state, CASE WHEN a.occupation = 'Armed Forces' THEN 'Salaried' WHEN a.occupation = 'Army/Navy/Police' THEN 'Salaried' WHEN a.occupation = 'Business' THEN 'Self-Employed' WHEN a.occupation = 'Business / Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Business/Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Defence (Army/Navy/Other Paramilitiary Forces)' THEN 'Salaried' WHEN a.occupation = 'House Wife' THEN 'Housewife' WHEN a.occupation = 'Housewife' THEN 'Housewife' WHEN a.occupation = 'Police' THEN 'Salaried' WHEN a.occupation = 'Professional' THEN 'Self-Employed' WHEN a.occupation = 'Professional (Salaried)' THEN 'Salaried' WHEN a.occupation = 'Professional(Salaried)' THEN 'Salaried' WHEN a.occupation = 'Salaried' THEN 'Salaried' WHEN a.occupation = 'Salaried (Govt Employee)' THEN 'Salaried' WHEN a.occupation = 'Salaried (Other than Govt)' THEN 'Salaried' WHEN a.occupation = 'Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed / Business' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed/Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Business)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Professional)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed/Professional' THEN 'Self-Employed' WHEN a.occupation = 'Service' THEN 'Salaried' WHEN a.occupation = 'Service- Government Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Private Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Public Sector' THEN 'Salaried' ELSE 'Others' END as Profession_Category, a.education, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end as final_maritial_status, case when pob.ComboTypeId=5 then 1 else 0 end as is_Working_Spouse , case when pob.ComboTypeId=6 then 1 else 0 end as is_HW_Spouse, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end as is_SpouseBkg,pob.combotypeid from sqldb_athena.pbcroma_crm_bookingdetails bd left join (select leadid,applicationno,policytype,salesagent,installmentspaid,pb_date,InsuredName,PolicyNo from sqldb_athena.pbcroma_crm_AdditionalBookingDetails where coalesce(PolicyType,0)=0 and pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) abd on bd.leadid=abd.leadid left join (select bookingid,combotypeid from sqldb_athena.bms_bms_postbookingdetails where pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) pob on pob.bookingid=bd.leadid left join (select planid, planname, supplierid, suppliername,suppliershortname,productid from sqldb_athena.pbcroma_dbo_vwSupplierPlanDetailsAllProduct where productid=7) spd on spd.PlanID=bd.PlanId and spd.SupplierID = bd.SupplierID left join (select leadid,customerid,parentid,dob,annualincome,postcode,utm_source,utm_campaign,leadrank,cityid,leadsource,productid ,pb_date,country from sqldb_athena.pbcroma_crm_Leaddetails where pb_date>=substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7) and productid=7) ld on ld.leadid=bd.LEADID left join (select leadid,productid,IsNRI,education,country,annualincome,occupation,age,planname,suppliershortname,suminsured,issue_flag,issue_DT from sqldb_athena.Analyticslife_dbo_Term_PureBookingData_CRT where productid=7) a on a.leadid=bd.leadid left join (select leadid,city, statename, productid, nationality from sqldb_athena.Analyticslife_dbo_Term_Booking_OtherInfo_CRT where productid=7) b on b.leadid=bd.leadid left join (select leadid,parentid,leadrank,leadsource,productid,cityid,pb_date,country,utm_medium,utm_source,utm_content,utm_campaign,utm_term from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) ldp on ldp.leadid=(case when ld.parentid is null then ld.leadid else ld.parentid end) left join (select leadid,city,state,pincode,gender,maritalstatus from sqldb_athena.bms_bms_ProposalDetails) pd on bd.leadid = pd.leadid left join sqldb_athena.pbcroma_crm_UserDetails ud on abd.SalesAgent=ud.userid left join (select leadid, term, payterm from sqldb_athena.PBCROMA_CRM_productdetails where pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) prod on prod.leadid = bd.leadid left join (Select countryid, country from sqldb_athena.productdb_master_country) c on cast(c.countryid as varchar) = case when cast(ldp.country as varchar)='INDIA' then '392' else ldp.country end where bd.paymentstatus in (300,3002,4002,5002,6002,7002,8002) and bd.pb_date>=substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and bd.pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7) and bd.productid=7 and cast(cast(a.issue_DT as Timestamp) as date) = date_add('DAY', -729, current_date) and Lower(ldp.leadSource) not in ('reopen') and Lower(ld.leadSource) not in ('reopen') and coalesce(lower(bd.bookingsource),'abcd') not like '%retargeting%' group by bd.leadid, cast(bd.OfferCreatedOn as timestamp),ld.customerid, ldp.leadid, ldp.Leadrank,abd.InsuredName,abd.ApplicationNo,abd.PolicyNo, case WHEN Lower(ldp.leadSource) = 'inbound' THEN '15. Inbound' when Lower(ldp.leadSource) like '%referral%' THEN '17. referral' when ((Lower(ldp.UTM_Medium) in ('term special lead','nri-reopen','term nri lead')) or (Lower(ldp.leadSource) like '%reopen%')) then '13. Reopen' WHEN Lower(ldp.Utm_source) LIKE '%offline%affiliate%' THEN '14. Offline Affiliate' When Lower(ldp.Utm_SOURCE) like 'whatsapp_crm_sales' THEN '16. Whatsapp' When Lower(ldp.Utm_SOURCE) LIKE '%retainer%' or Lower(ldp.UTM_Source) like '%pais%' OR Lower(ldp.Utm_term) like '%singlepagereturn%' THEN '11. Retainer' WHEN Lower(ldp.Utm_source) LIKE '%crm%' THEN '10. CRM' WHEN Lower(ldp.LeadSource) like '%pb%app%' and (Lower(ldp.UTM_Source) like '%organic%' OR coalesce(ldp.UTM_Source,'abc') = 'abc' OR Lower(ldp.Utm_source) = '' OR Lower(ldp.Utm_source) = 'null' or Lower(ldp.Utm_source) = 'none' or Lower(ldp.Utm_source) like '%cj%' OR Lower(ldp.Utm_source) in ('myaccount','twowheeler') ) then '00. APP Organic' when Lower(ldp.LeadSource) like '%pb%app%' and Lower(ldp.UTM_Source) not like '%organic%' then '05. APP Non Organic' WHEN Lower(ldp.Utm_source) IN ('organic','pb_articles','pb_business','providers','pb_news') THEN '02. SEO' WHEN Lower(ldp.Utm_source) IN ('google_brand','yahoo_brand','bing_brand', 'hotstar') THEN '03. Brand Paid' WHEN Lower(ldp.UTM_Source) in ('google','google_','bing','yahoo') and ((Lower(ldp.UTM_Medium) like '%ppc%' or Lower(ldp.UTM_Medium) like '%cpc%') AND ( Lower(ldp.UTM_Medium) NOT like '%remarketing%' and Lower(ldp.UTM_Medium) not like '%disp%')) THEN '04. Non Brand Search' WHEN Lower(ldp.Utm_source) IN ('google') and ( Lower(ldp.UTM_Medium) like '%remarketing%' or Lower(ldp.UTM_Medium) like '%disp%' or Lower(ldp.UTM_Medium) like '%video%') then '06. GDN' WHEN Lower(ldp.UTM_Source) like '%facebook%' then '07. FB Display' WHEN Lower(ldp.Utm_source) IN ('outbrain','taboola','quora') or Lower(ldp.UTM_Medium) like '%native%' or Lower(ldp.UTM_Medium) like '%roadblock%' then '08. Other Display' WHEN Lower(ldp.UTM_Source) = 'commercex_sms' or Lower(ldp.UTM_Medium) like '%affiliate%' then '09. Affiliate' when Lower(ldp.Utm_source) = '' or coalesce(ldp.UTM_Source,'abc') = 'abc'OR Lower(ldp.Utm_source) = 'null' then '01. Direct' else '12.Other' END, ldp.Utm_source,ldp.utm_campaign,ldp.utm_term, abd.SalesAgent, ud.EmployeeID, ud.UserName, ld.DOB, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end, ldp.country, ld.country, b.statename,pd.state,a.issue_flag,cast(a.issue_DT as timestamp), bd.PaymentPeriodicity, abd.InstallmentsPaid, bd.TotalPremium, a.annualincome, a.education, a.occupation, pd.Gender,spd.planname,spd.suppliershortname,a.age,a.suminsured,prod.term,prod.payterm,pd.pincode,case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end, case when pob.ComboTypeId=5 then 1 else 0 end, case when pob.ComboTypeId=6 then 1 else 0 end, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end,pob.combotypeid ), usergroup as ( select distinct bd.salesagent, bd.Bkg_DT,max(ldh.id) as MaxID from books bd left join (select userid,CreatedOn,id from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) ldh on ldh.UserID = bd.SalesAgent and cast(ldh.CreatedOn as timestamp) < bd.Bkg_DT Group by bd.salesagent,bd.Bkg_DT ), book2 as ( select a.*,ldh.GroupID,ugm.UsergroupName as usergroupnameBkg, case when cast(vc.MaritalStatus as varchar) in ('1','s','u','single','unmarried') then 'Single' when cast(vc.MaritalStatus as varchar) in ('2','married','m') then 'Married' when cast(vc.MaritalStatus as varchar) in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when cast(vc.MaritalStatus as varchar) in ('4','widower','w','widowed','widow(er)') then 'Widower' when cast(vc.MaritalStatus as varchar) in ('5','widow') then 'Widow' else 'NA' end as maritial_statusv2, vc.policy_status from Books a inner join Usergroup u on u.salesagent = a.salesagent and u.Bkg_DT= a.Bkg_DT left join (select userid,id,groupid from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) ldh on ldh.userID = u.salesagent and ldh.ID = u.MaxID left join sqldb_athena.pbcroma_crm_UsergroupMaster ugm on ugm.UsergroupID = ldh.GroupID left join voice.life_bu_fraud_framework_2020 vc on vc.leadid=a.leadid ), /*hus_wife as ( select pb.bookingid as Wifeid, be.Comboleadid as husbandID from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) )*/ hus_wife as ( select pb.bookingid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) union select be.Comboleadid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) ), Active_leads as ( Select bk.CustomerID, ld.CreatedON,ld.ProductID, ls.CreatedOn as statusDT,sm.StatusName from books bk inner join (select customerid,leadid, createdon, productid from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) ld on ld.CustomerID=bk.CustomerID and productid=7 inner join (select leadid,createdon, islaststatus, statusid from sqldb_athena.pbcroma_crm_LeadStatus where pb_date >= substring(cast(date_add('Month', -26, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -22, current_date) as varchar),1,7)) ls on ls.LeadID=ld.leadid and IsLastStatus=1 and cast(ls.CreatedOn as timestamp) >= cast(bk.Issue_Date as timestamp) and ls.StatusID in (1,2,3,4,11) left join sqldb_athena.pbcroma_crm_StatusMaster sm on sm.StatusId=ls.StatusID ), dump as ( select b2.*, case when hw.bkg_id is null then 0 else 1 end as spouse_booking_F,case when al.CustomerID is null then 0 else 1 end as is_lead_active from book2 b2 left join hus_wife hw on hw.Bkg_id=b2.leadid left join Active_leads al on al.customerid=b2.customerid ), CID as (select dp.customerid,proposer_gender,proposer_occupation_name from dump dp inner join (select leadid,customerid,proposer_gender,proposer_occupation_name from voice.life_bu_fraud_framework_2020 where ProductID=7)vi on vi.customerid=dp.customerid where proposer_occupation_name='Housewife' or proposer_gender = 2 ) select 'Reopen' as LeadSource, 'Term_Wife_upsell' as Utm_Source, 'Wife Upsell | ' || COALESCE(suppliershortname, '') || ' - ' || COALESCE(CAST(suminsured / 10000000 AS VARCHAR), '') || ' Cr | ' || COALESCE(CAST(employeeid AS VARCHAR), '') || ' ( ' || COALESCE(username, '') || ' )' AS Utm_Campaign, 'Term-3' as Utm_Term,dp.leadid as Utm_Medium, 'TermLife' AS ProductName, 7 AS ProductID, 0 as GroupID, dp.leadid as LeadID, 0 AS MobileNo, dp.customerid AS CustomerID, dp.InsuredName AS CustomerName, 509 AS LeadRank, 392 AS Country from dump dp LEFT join CID cid on cid.customerid=dp.customerid where Gender_Group like ('%Male%') and maritial_statusv2 in ('Married') and coalesce(try(cast(cast(Annualincome AS double) AS Integer)), null) >= 500000 and issue_flag=1 and profession_category not in ('Housewife') and IsNRI=0 and spouse_booking_F =0 and is_lead_active = 0 and policy_status='Inforce' and cid.customerid is null",
    "GET_HW_LEADS_REOPEN_LAST_YR":"with books as ( select distinct bd.leadid, cast(bd.OfferCreatedOn as timestamp) Bkg_DT,ld.customerid, abd.InsuredName,abd.ApplicationNo,abd.PolicyNo,ldp.leadid as Parentid,ldp.LeadRank as Leadrank, abd.SalesAgent, ud.EmployeeID, ud.UserName, spd.planname,spd.suppliershortname, a.age,a.suminsured,prod.term as policy_term,prod.payterm as pay_term, case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end as SI_Buckets,a.issue_flag,cast (a.issue_DT as timestamp) as Issue_Date, case when prod.payterm <=15 then 1 else 0 end as LP_15 ,pd.pincode, case when coalesce(ldp.country,'0') not in ('392','91','999','INDIA','0','NULL') or coalesce(ld.country,'0') not in ('392','91','999','INDIA','0','NULL') then 1 else 0 end as IsNRI, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end as leadsource, sum((CASE WHEN bd.PaymentPeriodicity IS null THEN 1 WHEN bd.PaymentPeriodicity IN ('Half Yearly','HalfYearly','Half-Yearly') THEN 2 WHEN bd.PaymentPeriodicity IN ('SINGLE','Single Premium','Single Pay','Single','Annually','Yearly','5','4','3','2','1','0') THEN 1 WHEN bd.PaymentPeriodicity IN ('Monthly') THEN 12 WHEN bd.PaymentPeriodicity IN ('Quarterly') THEN 4 END)/(case when abd.InstallmentsPaid is null Or abd.InstallmentsPaid=0 then 1 else abd.InstallmentsPaid end)*bd.TotalPremium) APE,a.annualincome, CASE WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) > 2500000 THEN '25L+' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 1500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) <= 2500000 THEN '15-25L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 1500000 THEN '5-15L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 200000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 500000 THEN '2-5L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 0 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 200000 THEN 'LT2L' ELSE 'Others' END AS AI_Category, CASE WHEN pd.gender IN ('2', 'Female', 'M/S', 'MS', 'Mrs.', 'Ms.', 'Ms') THEN 'Female' ELSE 'Male' END AS Gender_Group, case when b.statename is null then pd.state else b.statename end as updated_state, CASE WHEN a.occupation = 'Armed Forces' THEN 'Salaried' WHEN a.occupation = 'Army/Navy/Police' THEN 'Salaried' WHEN a.occupation = 'Business' THEN 'Self-Employed' WHEN a.occupation = 'Business / Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Business/Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Defence (Army/Navy/Other Paramilitiary Forces)' THEN 'Salaried' WHEN a.occupation = 'House Wife' THEN 'Housewife' WHEN a.occupation = 'Housewife' THEN 'Housewife' WHEN a.occupation = 'Police' THEN 'Salaried' WHEN a.occupation = 'Professional' THEN 'Self-Employed' WHEN a.occupation = 'Professional (Salaried)' THEN 'Salaried' WHEN a.occupation = 'Professional(Salaried)' THEN 'Salaried' WHEN a.occupation = 'Salaried' THEN 'Salaried' WHEN a.occupation = 'Salaried (Govt Employee)' THEN 'Salaried' WHEN a.occupation = 'Salaried (Other than Govt)' THEN 'Salaried' WHEN a.occupation = 'Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed / Business' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed/Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Business)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Professional)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed/Professional' THEN 'Self-Employed' WHEN a.occupation = 'Service' THEN 'Salaried' WHEN a.occupation = 'Service- Government Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Private Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Public Sector' THEN 'Salaried' ELSE 'Others' END as Profession_Category, a.education, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end as final_maritial_status, case when pob.ComboTypeId=5 then 1 else 0 end as is_Working_Spouse , case when pob.ComboTypeId=6 then 1 else 0 end as is_HW_Spouse, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end as is_SpouseBkg,pob.combotypeid from sqldb_athena.pbcroma_crm_bookingdetails bd left join (select leadid,applicationno,policytype,salesagent,installmentspaid,pb_date,InsuredName,PolicyNo from sqldb_athena.pbcroma_crm_AdditionalBookingDetails where coalesce(PolicyType,0)=0 and pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) abd on bd.leadid=abd.leadid left join (select bookingid,combotypeid from sqldb_athena.bms_bms_postbookingdetails where pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) pob on pob.bookingid=bd.leadid left join (select planid, planname, supplierid, suppliername,suppliershortname,productid from sqldb_athena.pbcroma_dbo_vwSupplierPlanDetailsAllProduct where productid=7) spd on spd.PlanID=bd.PlanId and spd.SupplierID = bd.SupplierID left join (select leadid,customerid,parentid,dob,annualincome,postcode,utm_source,utm_campaign,leadrank,cityid,leadsource,productid ,pb_date,country from sqldb_athena.pbcroma_crm_Leaddetails where pb_date>=substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7) and productid=7) ld on ld.leadid=bd.LEADID left join (select leadid,productid,IsNRI,education,country,annualincome,occupation,age,planname,suppliershortname,suminsured,issue_flag,issue_DT from sqldb_athena.Analyticslife_dbo_Term_PureBookingData_CRT where productid=7) a on a.leadid=bd.leadid left join (select leadid,city, statename, productid, nationality from sqldb_athena.Analyticslife_dbo_Term_Booking_OtherInfo_CRT where productid=7) b on b.leadid=bd.leadid left join (select leadid,parentid,leadrank,leadsource,productid,cityid,pb_date,country,utm_medium,utm_source,utm_content,utm_campaign,utm_term from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) ldp on ldp.leadid=(case when ld.parentid is null then ld.leadid else ld.parentid end) left join (select leadid,city,state,pincode,gender,maritalstatus from sqldb_athena.bms_bms_ProposalDetails) pd on bd.leadid = pd.leadid left join sqldb_athena.pbcroma_crm_UserDetails ud on abd.SalesAgent=ud.userid left join (select leadid, term, payterm from sqldb_athena.PBCROMA_CRM_productdetails where pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) prod on prod.leadid = bd.leadid left join (Select countryid, country from sqldb_athena.productdb_master_country) c on cast(c.countryid as varchar) = case when cast(ldp.country as varchar)='INDIA' then '392' else ldp.country end where bd.paymentstatus in (300,3002,4002,5002,6002,7002,8002) and bd.pb_date>=substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and bd.pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7) and bd.productid=7 and cast(cast(a.issue_DT as Timestamp) as date) = date_add('DAY', -364, current_date) and Lower(ldp.leadSource) not in ('reopen') and Lower(ld.leadSource) not in ('reopen') and coalesce(lower(bd.bookingsource),'abcd') not like '%retargeting%' group by bd.leadid, cast(bd.OfferCreatedOn as timestamp),ld.customerid, ldp.leadid, ldp.Leadrank,abd.InsuredName,abd.ApplicationNo,abd.PolicyNo, case WHEN Lower(ldp.leadSource) = 'inbound' THEN '15. Inbound' when Lower(ldp.leadSource) like '%referral%' THEN '17. referral' when ((Lower(ldp.UTM_Medium) in ('term special lead','nri-reopen','term nri lead')) or (Lower(ldp.leadSource) like '%reopen%')) then '13. Reopen' WHEN Lower(ldp.Utm_source) LIKE '%offline%affiliate%' THEN '14. Offline Affiliate' When Lower(ldp.Utm_SOURCE) like 'whatsapp_crm_sales' THEN '16. Whatsapp' When Lower(ldp.Utm_SOURCE) LIKE '%retainer%' or Lower(ldp.UTM_Source) like '%pais%' OR Lower(ldp.Utm_term) like '%singlepagereturn%' THEN '11. Retainer' WHEN Lower(ldp.Utm_source) LIKE '%crm%' THEN '10. CRM' WHEN Lower(ldp.LeadSource) like '%pb%app%' and (Lower(ldp.UTM_Source) like '%organic%' OR coalesce(ldp.UTM_Source,'abc') = 'abc' OR Lower(ldp.Utm_source) = '' OR Lower(ldp.Utm_source) = 'null' or Lower(ldp.Utm_source) = 'none' or Lower(ldp.Utm_source) like '%cj%' OR Lower(ldp.Utm_source) in ('myaccount','twowheeler') ) then '00. APP Organic' when Lower(ldp.LeadSource) like '%pb%app%' and Lower(ldp.UTM_Source) not like '%organic%' then '05. APP Non Organic' WHEN Lower(ldp.Utm_source) IN ('organic','pb_articles','pb_business','providers','pb_news') THEN '02. SEO' WHEN Lower(ldp.Utm_source) IN ('google_brand','yahoo_brand','bing_brand', 'hotstar') THEN '03. Brand Paid' WHEN Lower(ldp.UTM_Source) in ('google','google_','bing','yahoo') and ((Lower(ldp.UTM_Medium) like '%ppc%' or Lower(ldp.UTM_Medium) like '%cpc%') AND ( Lower(ldp.UTM_Medium) NOT like '%remarketing%' and Lower(ldp.UTM_Medium) not like '%disp%')) THEN '04. Non Brand Search' WHEN Lower(ldp.Utm_source) IN ('google') and ( Lower(ldp.UTM_Medium) like '%remarketing%' or Lower(ldp.UTM_Medium) like '%disp%' or Lower(ldp.UTM_Medium) like '%video%') then '06. GDN' WHEN Lower(ldp.UTM_Source) like '%facebook%' then '07. FB Display' WHEN Lower(ldp.Utm_source) IN ('outbrain','taboola','quora') or Lower(ldp.UTM_Medium) like '%native%' or Lower(ldp.UTM_Medium) like '%roadblock%' then '08. Other Display' WHEN Lower(ldp.UTM_Source) = 'commercex_sms' or Lower(ldp.UTM_Medium) like '%affiliate%' then '09. Affiliate' when Lower(ldp.Utm_source) = '' or coalesce(ldp.UTM_Source,'abc') = 'abc'OR Lower(ldp.Utm_source) = 'null' then '01. Direct' else '12.Other' END, ldp.Utm_source,ldp.utm_campaign,ldp.utm_term, abd.SalesAgent, ud.EmployeeID, ud.UserName, ld.DOB, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end, ldp.country, ld.country, b.statename,pd.state,a.issue_flag,cast(a.issue_DT as timestamp), bd.PaymentPeriodicity, abd.InstallmentsPaid, bd.TotalPremium, a.annualincome, a.education, a.occupation, pd.Gender,spd.planname,spd.suppliershortname,a.age,a.suminsured,prod.term,prod.payterm,pd.pincode,case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end, case when pob.ComboTypeId=5 then 1 else 0 end, case when pob.ComboTypeId=6 then 1 else 0 end, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end,pob.combotypeid ), usergroup as ( select distinct bd.salesagent, bd.Bkg_DT,max(ldh.id) as MaxID from books bd left join (select userid,CreatedOn,id from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) ldh on ldh.UserID = bd.SalesAgent and cast(ldh.CreatedOn as timestamp) < bd.Bkg_DT Group by bd.salesagent,bd.Bkg_DT ), book2 as ( select a.*,ldh.GroupID,ugm.UsergroupName as usergroupnameBkg, case when cast(vc.MaritalStatus as varchar) in ('1','s','u','single','unmarried') then 'Single' when cast(vc.MaritalStatus as varchar) in ('2','married','m') then 'Married' when cast(vc.MaritalStatus as varchar) in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when cast(vc.MaritalStatus as varchar) in ('4','widower','w','widowed','widow(er)') then 'Widower' when cast(vc.MaritalStatus as varchar) in ('5','widow') then 'Widow' else 'NA' end as maritial_statusv2, vc.policy_status from Books a inner join Usergroup u on u.salesagent = a.salesagent and u.Bkg_DT= a.Bkg_DT left join (select userid,id,groupid from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) ldh on ldh.userID = u.salesagent and ldh.ID = u.MaxID left join sqldb_athena.pbcroma_crm_UsergroupMaster ugm on ugm.UsergroupID = ldh.GroupID left join voice.life_bu_fraud_framework_2020 vc on vc.leadid=a.leadid ), /*hus_wife as ( select pb.bookingid as Wifeid, be.Comboleadid as husbandID from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) )*/ hus_wife as ( select pb.bookingid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) union select be.Comboleadid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) ), Active_leads as ( Select bk.CustomerID, ld.CreatedON,ld.ProductID, ls.CreatedOn as statusDT,sm.StatusName from books bk inner join (select customerid,leadid, createdon, productid from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) ld on ld.CustomerID=bk.CustomerID and productid=7 inner join (select leadid,createdon, islaststatus, statusid from sqldb_athena.pbcroma_crm_LeadStatus where pb_date >= substring(cast(date_add('Month', -14, current_date) as varchar),1,7) and pb_date <= substring(cast(date_add('Month', -10, current_date) as varchar),1,7)) ls on ls.LeadID=ld.leadid and IsLastStatus=1 and cast(ls.CreatedOn as timestamp) >= cast(bk.Issue_Date as timestamp) and ls.StatusID in (1,2,3,4,11) left join sqldb_athena.pbcroma_crm_StatusMaster sm on sm.StatusId=ls.StatusID ), dump as ( select b2.*, case when hw.bkg_id is null then 0 else 1 end as spouse_booking_F,case when al.CustomerID is null then 0 else 1 end as is_lead_active from book2 b2 left join hus_wife hw on hw.Bkg_id=b2.leadid left join Active_leads al on al.customerid=b2.customerid ), CID as (select dp.customerid,proposer_gender,proposer_occupation_name from dump dp inner join (select leadid,customerid,proposer_gender,proposer_occupation_name from voice.life_bu_fraud_framework_2020 where ProductID=7)vi on vi.customerid=dp.customerid where proposer_occupation_name='Housewife' or proposer_gender = 2 ) select 'Reopen' as LeadSource, 'Term_Wife_upsell' as Utm_Source,'Term_Wife_upsell' as Utm_Source, 'Wife Upsell | ' || COALESCE(suppliershortname, '') || ' - ' || COALESCE(CAST(suminsured / 10000000 AS VARCHAR), '') || ' Cr | ' || COALESCE(CAST(employeeid AS VARCHAR), '') || ' ( ' || COALESCE(username, '') || ' )' AS Utm_Campaign, 'Term-2' as Utm_Term,dp.leadid as Utm_Medium, 'TermLife' AS ProductName, 7 AS ProductID, 0 as GroupID, dp.leadid as LeadID, 0 AS MobileNo, dp.customerid AS CustomerID, dp.InsuredName AS CustomerName, 508 AS LeadRank, 392 AS Country from dump dp LEFT join CID cid on cid.customerid=dp.customerid where Gender_Group like ('%Male%') and maritial_statusv2 in ('Married') and coalesce(try(cast(cast(Annualincome AS double) AS Integer)), null) >= 500000 and issue_flag=1 and profession_category not in ('Housewife') and IsNRI=0 and spouse_booking_F =0 and is_lead_active = 0 and policy_status='Inforce' and cid.customerid is null",
    "GET_HW_LEADS_REOPEN" : "with books as ( select distinct bd.leadid, cast(bd.OfferCreatedOn as timestamp) Bkg_DT,ld.customerid, abd.InsuredName,abd.ApplicationNo,abd.PolicyNo,ldp.leadid as Parentid,ldp.LeadRank as Leadrank, abd.SalesAgent, ud.EmployeeID, ud.UserName, spd.planname,spd.suppliershortname, a.age,a.suminsured,prod.term as policy_term,prod.payterm as pay_term, case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end as SI_Buckets,a.issue_flag,cast (a.issue_DT as timestamp) as Issue_Date, case when prod.payterm <=15 then 1 else 0 end as LP_15 ,pd.pincode, case when coalesce(ldp.country,'0') not in ('392','91','999','INDIA','0','NULL') or coalesce(ld.country,'0') not in ('392','91','999','INDIA','0','NULL') then 1 else 0 end as IsNRI, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end as leadsource, sum((CASE WHEN bd.PaymentPeriodicity IS null THEN 1 WHEN bd.PaymentPeriodicity IN ('Half Yearly','HalfYearly','Half-Yearly') THEN 2 WHEN bd.PaymentPeriodicity IN ('SINGLE','Single Premium','Single Pay','Single','Annually','Yearly','5','4','3','2','1','0') THEN 1 WHEN bd.PaymentPeriodicity IN ('Monthly') THEN 12 WHEN bd.PaymentPeriodicity IN ('Quarterly') THEN 4 END)/(case when abd.InstallmentsPaid is null Or abd.InstallmentsPaid=0 then 1 else abd.InstallmentsPaid end)*bd.TotalPremium) APE,a.annualincome, CASE WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) > 2500000 THEN '25L+' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 1500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) <= 2500000 THEN '15-25L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 500000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 1500000 THEN '5-15L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 200000 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 500000 THEN '2-5L' WHEN coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) >= 0 AND coalesce(try(cast(cast(a.Annualincome AS double) AS Integer)), null) < 200000 THEN 'LT2L' ELSE 'Others' END AS AI_Category, CASE WHEN pd.gender IN ('2', 'Female', 'M/S', 'MS', 'Mrs.', 'Ms.', 'Ms') THEN 'Female' ELSE 'Male' END AS Gender_Group, case when b.statename is null then pd.state else b.statename end as updated_state, CASE WHEN a.occupation = 'Armed Forces' THEN 'Salaried' WHEN a.occupation = 'Army/Navy/Police' THEN 'Salaried' WHEN a.occupation = 'Business' THEN 'Self-Employed' WHEN a.occupation = 'Business / Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Business/Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Defence (Army/Navy/Other Paramilitiary Forces)' THEN 'Salaried' WHEN a.occupation = 'House Wife' THEN 'Housewife' WHEN a.occupation = 'Housewife' THEN 'Housewife' WHEN a.occupation = 'Police' THEN 'Salaried' WHEN a.occupation = 'Professional' THEN 'Self-Employed' WHEN a.occupation = 'Professional (Salaried)' THEN 'Salaried' WHEN a.occupation = 'Professional(Salaried)' THEN 'Salaried' WHEN a.occupation = 'Salaried' THEN 'Salaried' WHEN a.occupation = 'Salaried (Govt Employee)' THEN 'Salaried' WHEN a.occupation = 'Salaried (Other than Govt)' THEN 'Salaried' WHEN a.occupation = 'Self Employed' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed / Business' THEN 'Self-Employed' WHEN a.occupation = 'Self Employed/Business Owner' THEN 'Self-Employed' WHEN a.occupation = 'Self employed' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Business)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed(Professional)' THEN 'Self-Employed' WHEN a.occupation = 'Self employed/Professional' THEN 'Self-Employed' WHEN a.occupation = 'Service' THEN 'Salaried' WHEN a.occupation = 'Service- Government Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Private Sector' THEN 'Salaried' WHEN a.occupation = 'Service- Public Sector' THEN 'Salaried' ELSE 'Others' END as Profession_Category, a.education, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end as final_maritial_status, case when pob.ComboTypeId=5 then 1 else 0 end as is_Working_Spouse , case when pob.ComboTypeId=6 then 1 else 0 end as is_HW_Spouse, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end as is_SpouseBkg,pob.combotypeid from sqldb_athena.pbcroma_crm_bookingdetails bd left join (select leadid,applicationno,policytype,salesagent,installmentspaid,pb_date,InsuredName,PolicyNo from sqldb_athena.pbcroma_crm_AdditionalBookingDetails where coalesce(PolicyType,0)=0 and pb_date >= substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) abd on bd.leadid=abd.leadid left join (select bookingid,combotypeid from sqldb_athena.bms_bms_postbookingdetails where pb_date>=substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) pob on pob.bookingid=bd.leadid left join (select planid, planname, supplierid, suppliername,suppliershortname,productid from sqldb_athena.pbcroma_dbo_vwSupplierPlanDetailsAllProduct where productid=7) spd on spd.PlanID=bd.PlanId and spd.SupplierID = bd.SupplierID left join (select leadid,customerid,parentid,dob,annualincome,postcode,utm_source,utm_campaign,leadrank,cityid,leadsource,productid ,pb_date,country from sqldb_athena.pbcroma_crm_Leaddetails where pb_date>=substring(cast(date_add('Month', -2, current_date) as varchar),1,7) and productid=7) ld on ld.leadid=bd.LEADID left join (select leadid,productid,IsNRI,education,country,annualincome,occupation,age,planname,suppliershortname,suminsured,issue_flag,issue_DT from sqldb_athena.Analyticslife_dbo_Term_PureBookingData_CRT where productid=7) a on a.leadid=bd.leadid left join (select leadid,city, statename, productid, nationality from sqldb_athena.Analyticslife_dbo_Term_Booking_OtherInfo_CRT where productid=7) b on b.leadid=bd.leadid left join (select leadid,parentid,leadrank,leadsource,productid,cityid,pb_date,country,utm_medium,utm_source,utm_content,utm_campaign,utm_term from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date >= substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) ldp on ldp.leadid=(case when ld.parentid is null then ld.leadid else ld.parentid end) left join (select leadid,city,state,pincode,gender,maritalstatus from sqldb_athena.bms_bms_ProposalDetails) pd on bd.leadid = pd.leadid left join sqldb_athena.pbcroma_crm_UserDetails ud on abd.SalesAgent=ud.userid left join (select leadid, term, payterm from sqldb_athena.PBCROMA_CRM_productdetails where pb_date >= substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) prod on prod.leadid = bd.leadid left join (Select countryid, country from sqldb_athena.productdb_master_country) c on cast(c.countryid as varchar) = case when cast(ldp.country as varchar)='INDIA' then '392' else ldp.country end where bd.paymentstatus in (300,3002,4002,5002,6002,7002,8002) and bd.pb_date>=substring(cast(date_add('Month', -2, current_date) as varchar),1,7) and bd.productid=7 and cast(cast(a.issue_DT as Timestamp) as date)=date_add('DAY', -30, current_date) and Lower(ldp.leadSource) not in ('reopen') and Lower(ld.leadSource) not in ('reopen') and coalesce(lower(bd.bookingsource),'abcd') not like '%retargeting%' group by bd.leadid, cast(bd.OfferCreatedOn as timestamp),ld.customerid, ldp.leadid, ldp.Leadrank,abd.InsuredName,abd.ApplicationNo,abd.PolicyNo, case WHEN Lower(ldp.leadSource) = 'inbound' THEN '15. Inbound' when Lower(ldp.leadSource) like '%referral%' THEN '17. referral' when ((Lower(ldp.UTM_Medium) in ('term special lead','nri-reopen','term nri lead')) or (Lower(ldp.leadSource) like '%reopen%')) then '13. Reopen' WHEN Lower(ldp.Utm_source) LIKE '%offline%affiliate%' THEN '14. Offline Affiliate' When Lower(ldp.Utm_SOURCE) like 'whatsapp_crm_sales' THEN '16. Whatsapp' When Lower(ldp.Utm_SOURCE) LIKE '%retainer%' or Lower(ldp.UTM_Source) like '%pais%' OR Lower(ldp.Utm_term) like '%singlepagereturn%' THEN '11. Retainer' WHEN Lower(ldp.Utm_source) LIKE '%crm%' THEN '10. CRM' WHEN Lower(ldp.LeadSource) like '%pb%app%' and (Lower(ldp.UTM_Source) like '%organic%' OR coalesce(ldp.UTM_Source,'abc') = 'abc' OR Lower(ldp.Utm_source) = '' OR Lower(ldp.Utm_source) = 'null' or Lower(ldp.Utm_source) = 'none' or Lower(ldp.Utm_source) like '%cj%' OR Lower(ldp.Utm_source) in ('myaccount','twowheeler') ) then '00. APP Organic' when Lower(ldp.LeadSource) like '%pb%app%' and Lower(ldp.UTM_Source) not like '%organic%' then '05. APP Non Organic' WHEN Lower(ldp.Utm_source) IN ('organic','pb_articles','pb_business','providers','pb_news') THEN '02. SEO' WHEN Lower(ldp.Utm_source) IN ('google_brand','yahoo_brand','bing_brand', 'hotstar') THEN '03. Brand Paid' WHEN Lower(ldp.UTM_Source) in ('google','google_','bing','yahoo') and ((Lower(ldp.UTM_Medium) like '%ppc%' or Lower(ldp.UTM_Medium) like '%cpc%') AND ( Lower(ldp.UTM_Medium) NOT like '%remarketing%' and Lower(ldp.UTM_Medium) not like '%disp%')) THEN '04. Non Brand Search' WHEN Lower(ldp.Utm_source) IN ('google') and ( Lower(ldp.UTM_Medium) like '%remarketing%' or Lower(ldp.UTM_Medium) like '%disp%' or Lower(ldp.UTM_Medium) like '%video%') then '06. GDN' WHEN Lower(ldp.UTM_Source) like '%facebook%' then '07. FB Display' WHEN Lower(ldp.Utm_source) IN ('outbrain','taboola','quora') or Lower(ldp.UTM_Medium) like '%native%' or Lower(ldp.UTM_Medium) like '%roadblock%' then '08. Other Display' WHEN Lower(ldp.UTM_Source) = 'commercex_sms' or Lower(ldp.UTM_Medium) like '%affiliate%' then '09. Affiliate' when Lower(ldp.Utm_source) = '' or coalesce(ldp.UTM_Source,'abc') = 'abc'OR Lower(ldp.Utm_source) = 'null' then '01. Direct' else '12.Other' END, ldp.Utm_source,ldp.utm_campaign,ldp.utm_term, abd.SalesAgent, ud.EmployeeID, ud.UserName, ld.DOB, case when lower(ld.LeadSource) in ('acaff','acaffapp') then ld.leadsource else ldp.leadsource end, ldp.country, ld.country, b.statename,pd.state,a.issue_flag,cast(a.issue_DT as timestamp), bd.PaymentPeriodicity, abd.InstallmentsPaid, bd.TotalPremium, a.annualincome, a.education, a.occupation, pd.Gender,spd.planname,spd.suppliershortname,a.age,a.suminsured,prod.term,prod.payterm,pd.pincode,case when a.SumInsured<5000000 then '01. <50L' when a.SumInsured<10000000 then '02. 50L-0.99Cr' when a.SumInsured<20000000 then '03. 1Cr-1.99Cr' else '04. >=2Cr' end, case when pd.MaritalStatus in ('1','s','u','single','unmarried') then 'Single' when pd.MaritalStatus in ('2','married','m') then 'Married' when pd.MaritalStatus in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when pd.MaritalStatus in ('4','widower','w','widowed','widow(er)') then 'Widower' when pd.MaritalStatus in ('5','widow') then 'Widow' else 'NA' end, case when pob.ComboTypeId=5 then 1 else 0 end, case when pob.ComboTypeId=6 then 1 else 0 end, case when pob.combotypeid=6 or pob.combotypeid=5 then 1 else 0 end,pob.combotypeid ), usergroup as ( select distinct bd.salesagent, bd.Bkg_DT,max(ldh.id) as MaxID from books bd left join (select userid,CreatedOn,id from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) ldh on ldh.UserID = bd.SalesAgent and cast(ldh.CreatedOn as timestamp) < bd.Bkg_DT Group by bd.salesagent,bd.Bkg_DT ), book2 as ( select a.*,ldh.GroupID,ugm.UsergroupName as usergroupnameBkg, case when cast(vc.MaritalStatus as varchar) in ('1','s','u','single','unmarried') then 'Single' when cast(vc.MaritalStatus as varchar) in ('2','married','m') then 'Married' when cast(vc.MaritalStatus as varchar) in ('3','divorced/separated','d','divorcee','divorced') then 'Divorced/Separated' when cast(vc.MaritalStatus as varchar) in ('4','widower','w','widowed','widow(er)') then 'Widower' when cast(vc.MaritalStatus as varchar) in ('5','widow') then 'Widow' else 'NA' end as maritial_statusv2, vc.policy_status from Books a inner join Usergroup u on u.salesagent = a.salesagent and u.Bkg_DT= a.Bkg_DT left join (select userid,id,groupid from sqldb_athena.pbcroma_crm_Logindetailshistory where pb_date >= substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) ldh on ldh.userID = u.salesagent and ldh.ID = u.MaxID left join sqldb_athena.pbcroma_crm_UsergroupMaster ugm on ugm.UsergroupID = ldh.GroupID left join voice.life_bu_fraud_framework_2020 vc on vc.leadid=a.leadid ), /*hus_wife as ( select pb.bookingid as Wifeid, be.Comboleadid as husbandID from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) )*/ hus_wife as ( select pb.bookingid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) union select be.Comboleadid Bkg_id from sqldb_athena.bms_bms_postbookingdetails pb left join sqldb_athena.bms_bms_bookinge2eflag be on be.leadid=pb.bookingid where pb.Combotypeid in (5,6) ), Active_leads as ( Select bk.CustomerID, ld.CreatedON,ld.ProductID, ls.CreatedOn as statusDT,sm.StatusName from books bk inner join (select customerid,leadid, createdon, productid from sqldb_athena.pbcroma_crm_Leaddetails where productid=7 and pb_date>=substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) ld on ld.CustomerID=bk.CustomerID and productid=7 inner join (select leadid,createdon, islaststatus, statusid from sqldb_athena.pbcroma_crm_LeadStatus where pb_date>=substring(cast(date_add('Month', -2, current_date) as varchar),1,7)) ls on ls.LeadID=ld.leadid and IsLastStatus=1 and cast(ls.CreatedOn as timestamp) >= cast(bk.Issue_Date as timestamp) and ls.StatusID in (1,2,3,4,11) left join sqldb_athena.pbcroma_crm_StatusMaster sm on sm.StatusId=ls.StatusID ), dump as ( select b2.*, case when hw.bkg_id is null then 0 else 1 end as spouse_booking_F,case when al.CustomerID is null then 0 else 1 end as is_lead_active from book2 b2 left join hus_wife hw on hw.Bkg_id=b2.leadid left join Active_leads al on al.customerid=b2.customerid ), CID as (select dp.customerid,proposer_gender,proposer_occupation_name from dump dp inner join (select leadid,customerid,proposer_gender,proposer_occupation_name from voice.life_bu_fraud_framework_2020 where ProductID=7)vi on vi.customerid=dp.customerid where proposer_occupation_name='Housewife' or proposer_gender = 2) select 'Reopen' as LeadSource, 'Term_Wife_upsell' as Utm_Source, 'Wife Upsell | ' || COALESCE(suppliershortname, '') || ' - ' || COALESCE(CAST(suminsured / 10000000 AS VARCHAR), '') || ' Cr | ' || COALESCE(CAST(employeeid AS VARCHAR), '') || ' ( ' || COALESCE(username, '') || ' )' AS Utm_Campaign, 'Term-1' as Utm_Term, dp.leadid as Utm_Medium, 'TermLife' AS ProductName, 7 AS ProductID, 0 as GroupID, dp.leadid as LeadID, 0 AS MobileNo, dp.customerid AS CustomerID, dp.InsuredName AS CustomerName, 505 AS LeadRank, 392 AS Country from dump dp LEFT join CID cid on cid.customerid=dp.customerid where Gender_Group like ('%Male%') and maritial_statusv2 in ('Married') and coalesce(try(cast(cast(Annualincome AS double) AS Integer)), null) >= 500000 and issue_flag=1 and LP_15=1 and profession_category not in ('Housewife') and IsNRI=0 and spouse_booking_F =0 and is_lead_active = 0 and policy_status='Inforce' and cid.customerid is null"
}

module.exports = athenaQueries 