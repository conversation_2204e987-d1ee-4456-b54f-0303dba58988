@use "sass:map";
@import './variables.scss';

.attributes-header {
    color: $title-bg-clr;
    display: flex;
    flex-wrap: wrap;
    font-family: $tab-font-family;
    justify-content: flex-end;
    margin: 0;
    width: 100%;
    align-items: baseline;

    .attr-link {
        cursor: pointer;
        margin: {
            left: 25px;
            right: 25px;
            top: 10px;
        }

        .text {
            margin-left: 30px;
        }
    }

    .attr-link:hover {
        text-decoration: underline;
    }

    .plus-icon {
        background: url('../../icons/plus-icon-rounded.svg') no-repeat;
        background-size: contain;
        margin: 0 0px 0 0;
        padding: 0;
        padding-bottom: 35px;
        position: absolute;
        width: 35px;
    }

    .submit-icon {
        background: url('../../icons/submit-icon-green-rounded.svg') no-repeat;
        background-size: contain;
        margin: 0 0px 0 0;
        padding: 0;
        padding-bottom: 25px;
        position: absolute;
        width: 25px;
    }

    .reset-icon {
        background: url('../../icons/reset-icon-red-rounded.svg') no-repeat;
        background-size: contain;
        margin: 0 0px 0 0;
        padding: 0;
        padding-bottom: 25px;
        position: absolute;
        width: 25px;
    }

    .btn-container {
        margin-top: 0px;
    }
}

td.attributes-header {
    justify-content: flex-start;
}

@mixin attr-linktype($color) {
    background-color: $color;
    border-radius: 15px;
    color: $btn-color;
    padding: 5px 18px;
    font-size: 12px;
    text-transform: uppercase;
}

.string {
    @include attr-linktype(map-get($attribute-type, "string"));
}

.boolean {
    @include attr-linktype(map-get($attribute-type, "boolean"));
}

.array {
    @include attr-linktype(map-get($attribute-type, "array"));
    
}

.object {
    @include attr-linktype(map-get($attribute-type, "object"));
}

.number {
    @include attr-linktype(map-get($attribute-type, "number"));
}

.date {
    @include attr-linktype(map-get($attribute-type, "date"));
}

.view-attribute {
    border-bottom: 2px solid #ddd;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px;
    width: 60%
}

.view-params-container {
    width: 100%;
    color: $dark-color;
    h5{
        font-size: 20px;
        text-align: right;
    }
}
