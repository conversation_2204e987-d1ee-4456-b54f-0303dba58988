
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, GetCommonspDataV2
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import {OpenNewSalesView, getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func } from "prop-types";

class CallBackTracker extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "CallBack Tracker",
      CallBackTracker: [],
      showAssignLeadPopUp: false,
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: true,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false
    };
    this.dtRef = React.createRef();
    this.handleShow = this.handleShow.bind(this);
    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadId",
        sortable: true,
        cell: row => <a className="pointer" onClick = {() => this.openSalesView(row.CustomerID, row.LeadId, row.ProductId)}>
          {row.LeadId}
        </a>
      },
      {
        name: "Customer Name",
        selector: "CustName",
        sortable: true,
      },
      {
        name: "Emp Id",
        selector: "EmployeeId",
        sortable: true,
        searchable: true,
      },
      {
        name: "Agent Name",
        selector: "AgentName",
        sortable: true,
        searchable: true,
      },
      {
        name: "IsAvailable",
        selector: "AgentAvailibilityStatus",
        sortable: true,
        searchable: true,
      },
      {
        name: "Current Callback",
        selector: "NextCallBackTime",
        sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.NextCallBackTime}</Moment>
      },
      {
        name: "Payment Callback",
        selector: "PaymentCallback",
        cell: row => (
          <div>
           {
            row.PaymentCallback === true
              ? "Yes"
              : row.PaymentCallback === false
              ? "No"
              : "N.A"
           }
          </div>
        )
      },
      {
        name: "Lead Status",
        selector: "LeadStatus",
        sortable: true,
      },
      {
        name: "Last Call Time",
        selector: "LastCallTime",
        sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.LastCallTime}</Moment>
      },
      // {
      //   name: "AgentStatus",
      //   selector: "AgentStatus",
      //   sortable: true,
      // },
      {
        name: "Flag",
        selector: "Flag",
        sortable: true,
        type: "dropdown",
        searchable: true,
        config: {
          root: "Flag",
          data: [{ Id: 'Missed CB', Display: "Missed CB" }, { Id: 'Call Done, No CB Set', Display: "Call Done, No CB Set" }, { Id: 'Future CB', Display: "Future CB" }],
        },
        cell: row => <div className={this.getStatusFlagClass(row.Flag)}>
          {row.Flag}
        </div>,
        width: "150px"
      }

    ];

    // GetDataDirect({
    //   root: "UserMenu",
    //   con: [{ "UserId": getuser().UserID }, { "MenuId": 263 }],
    // }, function (result) {
    //   if (result && result[0] && result[0].length == 0) {
    //     this.state.hideAssign = true
    //   }
    //   else {
    //     this.state.hideAssign = false
    //   }
    // }.bind(this))

    this.props.GetCommonspDataV2({
      root: "GetFollowUpPermissionsByUserId",
      c: 'R'
    }, function (data) {
      if (data?.data?.data && data.data.data.length > 0 && data.data.data[0].length > 0) {
        let userMenuMap = data.data.data[0]
        const keyExists = userMenuMap.some(obj => obj['FollowUpMenuID'] === 5);
        (keyExists && !(getuser().RoleID == 13))?this.state.hideAssign = false: this.state.hideAssign = true       
      }
      else {
        this.state.hideAssign = true
      }
    }.bind(this))
  }

  OpenAssignLeadPopUp(row) {
    this.setState({ showAssignLeadPopUp: true });
  }
  openSalesView = (CustomerId, LeadId, ProductId) => { 
    let url = OpenNewSalesView(CustomerId, LeadId, ProductId);
    window.open(url)
  }

  getStatusFlagClass(flag) {
    if(flag == 'Missed CB'){
      return 'RED'
    }else if(flag == 'Call Done, No CB Set'){
      return 'GREY'
    }else if(flag == 'Future CB'){
      return 'GREEN'
    }
  }


  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError &&  nextProps.CommonData["CallBackTracker"] != undefined) {
      this.setState({ CallBackTracker: nextProps.CommonData["CallBackTracker"] });
  
      if (nextProps.CommonData["AssignedToAgent"]) {
        //this.setState({ showAlert: true, AlertMsg: "Lead Assigned Successfully.", AlertVarient: "success" });

      }
      else {
        this.setState({CallBackTracker : []})
      }
    }
  }
  handleShow(e) {

    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchCallBackData();
    }.bind(this), 500);

  }

  handleClose() {
    this.setState({ showAssignLeadPopUp: false })
  }

  fetchCallBackData() {
    var SelectedSupervisors = this.state.SelectedSupervisors;//[48,282,288,1362,1606,1985,2349,2640,3269,3584,4119,4386,4474,4583,5126,5712,5947,6052,6358,7008,7014,7171,7172,7175,8177,8492,9020,9087,9144,9541,9870,10242,10455,10491,10492,11176,11193,11284,11693,11762,11854,12088,12705,13394,13807,14365,14421,14541,14750,15620,16591,16775,17251,17345,19216,19336,19738,20777,21018,21019,21302,21456,21585,21586,21822,21825,21911,22271,22541,22734,23087,23288,23538,23548,23627,23652,23656,24295,24664,24767,26160,26224,26728,26957,27139,28051,29508,30060,30956,31002,31415,31813,31975,32564,32664,32670,33385,35917,36003,36155,36977,37608,38326,38327,38721,38742,39031,39052,39098,39138,39412,40253,40308,40336,40568,40615,40801,41056,41075,41094,41257,41518,42062,42098,42114,42393,42394,43092,43276,43277,44026,44438,44680,45128,45233,45234,45332,45455,45460,46056,46083,46291,46340,46433,47074,47186,47600,47633,47645,47713,48032,48065,48299,48788,48999,49222,49418,49525,49588]
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "CallBackTracker",
      params: [{ ManagerIds:  SelectedSupervisors.join()}]
    });

    if (this.state.SelectedRows.length > 0) {
      this.dtRef.current.handleClearRows();
    }

  }

  bindAgentDropdown() {

    const { CallBackTracker } = this.props.CommonData || {};
    let option = [];
    if (CallBackTracker && CallBackTracker.length > 0) {
      CallBackTracker[1].map(item => (
        option.push(<option key={item.UserID} value={item.UserID}>{item.UserName}</option>)
      ))
    }
    return option;
  }
  onSelectedAgent(e) {
    this.setState({ SelectedAgentAssigTo: e.target.value });
  }
  AssignLead() {
    const { SelectedRows, SelectedAgentAssigTo } = this.state;

    for (let index = 0; index < SelectedRows.length; index++) {
      const element = SelectedRows[index];
      //toast("Lead (" + element.LeadId + ") assign successfully", { type: 'success' });

      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "AssignedToAgent",
        c: "L",
        params: [{
          AssignedTo_AgentId: SelectedAgentAssigTo,
          AssignedBy_AgentId: getuser().UserID,
          ProductId: element.ProductId,
          LeadId: element.LeadId,
          GroupId: 0,
          flag: 1,
          JobId: 94
        }]
      }, function (data) {
        toast("Lead (" + element.LeadId + ") assign successfully", { type: 'success' });
      });
    }

    this.setState({ showAssignLeadPopUp: false });

    setTimeout(function () {
      this.fetchCallBackData();
    }.bind(this), 300);
  }
  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }
  render() {
    const columns = this.columnlist;
    const { items, PageTitle, showAssignLeadPopUp, showAlert, AlertMsg, AlertVarient, ReportTime, SelectedRows } = this.state;

    let selectedLeads = [];
    SelectedRows.forEach(element => {
      selectedLeads.push(element.LeadId);
    });

   const { CallBackTracker } = this.props.CommonData || {};
    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={6}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <CardTitle tag="h5">
                        {ReportTime ? <Moment format="DD/MM/YYYY HH:mm:ss">{ReportTime}</Moment> : null}
                      </CardTitle>

                    </Col>
                    <Col md={2}>
                      <ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>
                      {this.state.hideAssign ? null : <button className="btn btn-info btn-sm" onClick={() => this.OpenAssignLeadPopUp()} >Assign Lead</button>}
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={(CallBackTracker && CallBackTracker.length > 0) ? CallBackTracker[0] : []}
                    defaultSortField="Flag"
                    defaultSortAsc={false}
                    selectableRows={true}
                    ref={this.dtRef}
                    onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showAssignLeadPopUp} onHide={this.handleClose.bind(this)} >
            <Modal.Header closeButton>
              <Modal.Title>Assign Leads</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Row>
                <Col>
                  LeadId : {selectedLeads.join()}
                </Col>
              </Row>
              <Row>
                <Col>
                  <Form.Control as="select" name="products" onChange={this.onSelectedAgent.bind(this)} >
                    <option key={0} value={0}>Select</option>
                    {
                      this.bindAgentDropdown()
                    }
                  </Form.Control>
                </Col>
              </Row>

            </Modal.Body>
            <Modal.Footer>

              <If condition={this.state.SelectedAgentAssigTo != 0}>
                <Then>
                  <Button variant="primary" onClick={this.AssignLead.bind(this)}>Assign Lead</Button>
                </Then>
              </If>
              <Button variant="secondary" onClick={this.handleClose.bind(this)}>
                Close
                </Button>
            </Modal.Footer>
          </Modal>


        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(CallBackTracker);