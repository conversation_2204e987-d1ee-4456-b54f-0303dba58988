
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
import Select from 'react-select';

// reactstrap components

import {
    GetCommonData, GetCommonspData
} from "../../store/actions/CommonAction";

import _ from 'underscore';

class DropDownList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            items: [],
            selectedItem:{}
        }
    }
    componentDidMount() {
        if (this.state.items && this.state.items.length === 0) {
            if(this.props.col.config.sp){
                //console.log('cellll',this.props.col.cell)
                this.props.GetCommonspData({
                    root: this.props.col.config.root,
                    params: this.props.col.config.con,
                    c: 'R'
                  }, function (result) {
                    if(result && result.data && result.data.data){
                        let str = JSON.stringify(result.data.data[0]);
                        var res = str.replace(this.props.col.config.I<PERSON>field, "Id");
                        res = res.replace(this.props.col.config.Displayfield, "Display");      
                        this.setState({ items: JSON.parse(res) });
                    }
                    }.bind(this));
            }else{
            this.props.GetCommonData({
                root: this.props.col.config.root,
                cols: this.props.col.config.cols,
                con: this.props.col.config.con,
                data: this.props.col.config.data,
                statename: this.props.col.config.statename,
                state: this.props.col.config.state == false ? false : true,
            });
        }
        }
    }
    componentWillReceiveProps(nextProps) {

        if (!nextProps.CommonData.isError) {
            let items = (this.props.col.config.sp)?this.state.items:nextProps.CommonData[this.props.col.config.statename ?? this.props.col.config.root];
            //console.log('itemsss',items)
            if(items && nextProps.valueTobefiltered){
                items = items.filter(function(n) {
                    return (nextProps.valueTobefiltered.indexOf(n.Id) > -1);
                  });
            }
            if (nextProps.col.distinct) {
                items = _.uniq(items, function (x) {
                    return x.Id;
                });
            }
            //console.log('itemsss after',items)

            this.setState({ items: items });
            this.getSelectedOption();
        }
    }

    displayoption(item, name) {
        if(Array.isArray(item[this.props.filterkey])){
            if (item[this.props.filterkey].indexOf(Number(this.props.filtervalue)) > -1  ) {                                                     
                return {value:item.Id, label:item.Display}
            }
        }
        if (this.props.filterkey && this.props.filtervalue) {
            if (this.props.filterkey.toLowerCase() == "productid") {
                if (this.props.filtervalue == 7) {
                    if (item[this.props.filterkey] == 7 || item[this.props.filterkey] == 1000) {                    
                        return {value:item.Id, label:item.Display}
                    }
                }
            }
            if (item[this.props.filterkey] == this.props.filtervalue) {                                                     
                return {value:item.Id, label:item.Display}
            }
            return true;
        }
        else{
            return {value: item.Id, label: item.Display}
        }
    }

    getSelectedOption(){
        let { items } = this.state;
        let that = this;
        if(this.state.selectedItem && !this.state.selectedItem.label && that.props.value){
        items.forEach(item => {
            if(item.Id.toString().toUpperCase() == that.props.value.toString().toUpperCase()){
                that.setState({
                    selectedItem:{value:item.Id, label:item.Display}
                });
               return;
            }
        })
    }

      
    }

    onChange(e){
        console.log(e)
        let that = this;
        this.props.onChange(
            {
                target:{
                    id: that.props.col.name,
                    value: e.value,
                    text: e.label
                }
            }
        )
        this.setState({selectedItem:e});
        this.getSelectedOption();
        this.forceUpdate();
    }

    render() {

        let { items } = this.state;
        let { selectedItem  } = this.state;
        const { value, visible } = this.props;
        if (!items) {
            items = [];
        }
        if (visible == false) {
            return null;
        }
        
        return (
            
            <div>
                {/* {JSON.stringify(selectedItem)} */}
                <Select 
                disabled={this.props.disabled} 
                value={Object.keys(selectedItem).length > 0 ? selectedItem : { value: '', label: (this.props.firstoption ? this.props.firstoption : "Select")}} 
                //inputValue={selectedItem.label}
                
                name={this.props.col.name} 
                onChange={this.onChange.bind(this)} 
                required={this.props.required} 
                options ={
                    [
                        {value: '', label: this.props.firstoption ? this.props.firstoption : "Select"},
                        ...items.map(item => ( this.displayoption(item,this.props.col.name)
                        ))
                    ]}
                />
            </div>

        );
    }
}
function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData
    }
)(DropDownList);

