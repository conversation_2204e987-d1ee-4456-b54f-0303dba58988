
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, PostCommunicationData, AddLeadValidation , ValidateAddLeadToPriorityQueue
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func } from "prop-types";
import moment from "moment";

class CallBackAgentWise extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "CallBack Tracker",
      CallBackAgentWise: [],
      showAssignLeadPopUp: false,
      SelectedAgentAssigTo: 0,
      SelectedRow: null,
      hideAssign: false,
      ReportTime: null,
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false
    };
    this.dtRef = React.createRef();

    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 18) : ''}{(v && v.length > 19) ? '...' : ''}</span>
    );
    
    this.columnlist = [
      {
        name: "LeadID",
        selector: "LeadId",
        width: "100px"
      },
      {
        name: "Customer Name",
        selector: "CustName",
        cell: row => <Cell v={row.CustName} />,
        sortable: true,
      },
      {
        name: "Callback Time",
        sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.EventDate}</Moment>,
        width: "150px"
      },
      {
        name: "Status",
        selector: "StatusName",
        sortable: true,
        width: "100px"
      },
      {
        name: "Callback Type",
        selector: "Description",
        cell: row => <Cell v={row.Description} />,
        sortable: true,
        width: "180px"
      },
      {
        name: "Flag",
        selector: "Flag",
        sortable: true,
        type: "dropdown",
        searchable: true,
        config: {
          root: "Flag",
          data: [{ Id: 'RED', Display: "MISSED" }, { Id: 'GREY', Display: "FUTURE" }],
        },
        cell: row => <div className={row.Flag}>
          {row.Flag == "RED" ? "MISSED" : "FUTURE"}
        </div>,
        width: "150px"
      }

    ];


  }

  OpenAssignLeadPopUp(row) {
    this.setState({ showAssignLeadPopUp: true });

  }


  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["CallBackAgentWise"]) {
        debugger
        let CBList = nextProps.CommonData["CallBackAgentWise"];

        if(CBList && CBList[0]){
        for (let index = 0; index < CBList[0].length; index++) {
          const element = CBList[0][index];

          var compareTo = element.EventDate;
          //var compareTo = "2021-05-07T15:00:00.000Z";
          var date = new Date().toISOString();
          var now = moment.utc(date);
          now.add(5, 'hour')
          now.add(30, 'minute')
          //var then = moment.utc(compareTo);


          var isAfter = moment(now).isAfter(compareTo);

          if (isAfter) {
            element['Flag'] = "RED";
          }
          else {
            element['Flag'] = "GREEN";
          }
        }
      }
        this.setState({ CallBackAgentWise: CBList });
      }



    }
  }


  handleClose() {
    this.setState({ showAssignLeadPopUp: false })
  }

  componentDidMount() {
    this.fetchCallBackData();
  }

  fetchCallBackData() {
    var SelectedSupervisors = this.state.SelectedSupervisors;
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "CallBackAgentWise",
      params: [{ UserId: getuser().UserID }]
    });

    if (this.state.SelectedRows.length > 0) {
      this.dtRef.current.handleClearRows();
    }

  }


  onSelectedAgent(e) {
    this.setState({ SelectedAgentAssigTo: e.target.value });
  }
  AssignLead() {
    const { SelectedRows, SelectedAgentAssigTo } = this.state;
    debugger
    for (let index = 0; index < SelectedRows.length; index++) {
      const element = SelectedRows[index];
      //toast("Lead (" + element.LeadId + ") assign successfully", { type: 'success' });

      var lead = {
        "LeadId": element.LeadId,
        "Name": element.CustName,
        "CustomerId": element.CustomerID,
        "UserID": parseInt(getuser().UserID),
        "Priority": 0,
        "ProductId": element.ProductId,
        "Reason": 'Manual added',
        "ReasonId": 33,
        "CallStatus": "",
        "IsAddLeadtoQueue":1,
        "IsNeedToValidate":1
      }
      var reqData = {
        "UserId": parseInt(getuser().UserID),
        "Leads": [lead]
      };
      ValidateAddLeadToPriorityQueue(reqData , function (resultData)
        {
          try{
            if (resultData != null) {
              if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
                  toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
              }
              toast(`${resultData.data.data.message}`, { type: 'error' });
            }
          }catch(e){
            toast(`${e}`, { type: 'error' });
          console.log(e)
        }
        }.bind(this));

      // AddLeadValidation(element.LeadId , function (results) {
      //   try{
      //     if (results.data.status == 200) {
      //       if(results.data.data.status == 1 && results.data.data.message == 'Success') {
      //         this.props.PostCommunicationData({
      //           root: 'communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
      //           data: reqData
      //         }, function (data) {
      //           toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
      //         });
      //       }
      //       else{
      //         toast(`${results.data.data.message}`, { type: 'error' });
      //       }  
      //     }
      //   }
      //   catch(e){
      //     console.log(e)
      //   }
      // }.bind(this));

      // this.props.PostCommunicationData({
      //   root: 'communication/LeadPrioritization.svc/AddLeadToPriorityQueue',
      //   data: reqData
      // }, function (data) {
      //   toast("Lead (" + element.LeadId + ") Added in Call Queue", { type: 'success' });
      // });
    }

    this.setState({ showAssignLeadPopUp: false });

    // setTimeout(function () {
    //   this.fetchCallBackData();
    // }.bind(this), 300);
  }
  onSelectedRows(SelectedRows) {
    this.setState({ SelectedRows: SelectedRows });
  }
  render() {
    const columns = this.columnlist;
    const { items, PageTitle, CallBackAgentWise, showAssignLeadPopUp, showAlert, AlertMsg, AlertVarient, ReportTime, SelectedRows } = this.state;

    let selectedLeads = [];
    SelectedRows.forEach(element => {
      selectedLeads.push(element.LeadId);
    });

    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={6}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <CardTitle tag="h5">
                        {ReportTime ? <Moment format="DD/MM/YYYY HH:mm:ss">{ReportTime}</Moment> : null}
                      </CardTitle>

                    </Col>
                    <Col md={2}>
                      {this.state.hideAssign ? null : <button className="btn btn-info btn-sm" onClick={() => this.OpenAssignLeadPopUp()} >Add Lead</button>}
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody  className="CallBackAgent">
                  <DataTable
                    columns={columns}
                    data={(CallBackAgentWise && CallBackAgentWise.length > 0) ? CallBackAgentWise[0] : []}
                    defaultSortField="Flag"
                    defaultSortAsc={false}
                    selectableRows={true}
                    ref={this.dtRef}
                    onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showAssignLeadPopUp} onHide={this.handleClose.bind(this)} >
            <Modal.Header closeButton>
              <Modal.Title>Add Leads</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Row>
                <Col>
                  LeadId : {selectedLeads.join()}
                </Col>
              </Row>
              <Row>
                <Col>
                  {/* <Form.Control as="select" name="products" onChange={this.onSelectedAgent.bind(this)} >
                    <option key={0} value={0}>Select</option>
                    {
                      this.bindAgentDropdown()
                    }
                  </Form.Control> */}
                </Col>
              </Row>

            </Modal.Body>
            <Modal.Footer>

              <If condition={this.state.SelectedRows.length > 0}>
                <Then>
                  <Button variant="primary" onClick={this.AssignLead.bind(this)}>Add Lead</Button>
                </Then>
              </If>
              <Button variant="secondary" onClick={this.handleClose.bind(this)}>
                Close
                </Button>
            </Modal.Footer>
          </Modal>


        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    PostCommunicationData
  }
)(CallBackAgentWise);