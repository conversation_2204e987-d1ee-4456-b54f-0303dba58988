.ManagerHierachyPopop {
    .MuiDrawer-paperAnchorRight {
        width: 360px;
        padding: 10px 20px;
    }

    .topHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .ShowBtn {
        background-color: #0065FF;
        text-transform: capitalize;
    }

    h3 {
        color: rgba(37, 56, 88, 0.60) !important;
        font-family: Roboto !important;
        font-size: 17px !important;
        font-style: normal;
        font-weight: 600 !important;
    }

    .SupervisorListing {
        .rct-node {
            .rct-checkbox {
                height: 22px;
                display: inline-block;

                svg {
                    width: 20px;
                }
            }

            .rct-text {
                margin-bottom: 2px;

                label {
                    display: flex;
                    align-items: center;
                    margin-bottom: 5px;
                    background-color: transparent;
                    color: rgba(37, 56, 88, 0.60);
                    font-family: Roboto;
                    font-size: 14px;
                    font-weight: 500;
                }
            }
        }

        .checked {
            color: #0065FF;
        }

        .downArrow {
            color: rgba(37, 56, 88, 0.60);
        }
    }

    .managerEmployees {
        word-break: break-word;
        font-size: 14px;
        font-family: 'Roboto';
        font-weight: 500;
        color: rgba(37, 56, 88, 0.60);
    }


}

.ShowHistoryBTn {
    position: relative;

    button {
        position: fixed;
        right: 10px;
        top: 10px;
        background-color: #0065FF !important;
        z-index: 99;
    }


    button.fullscreen {
        // top: 55px;
        // color: #fff;
        // right: -4px;
        top: 75%;
        transform: translate(-50%, 0%);
        -webkit-transform: translate(-50%, 0%);
        color: #343434;
        right: -10px;
        background: #fff !important;
        padding: 0;
        width: 40px;
        height: 40px;
        margin: 0;
        min-width: 40px;
        box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 4px -1px;
        -webkit-box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 4px -1px;
        border-radius: 2px;
    }


}