import TogglePopup from './TogglePopup'
import moment from 'moment';

const SlotTableView = (props) => {

    const {UserData, AppointmentSlots , handleMore, GetData, activeTab , EmployeeFlag, blink } = props

    // class="fas fa-phone-volume"
    return <>
      {AppointmentSlots.map((slot) => (
        UserData && Object.keys(UserData).indexOf(slot.Id.toString()) > -1 ?
          Object.values(UserData).map((AgData) => (
            slot.Id == AgData[0].SlotId &&
            <td>
              {
                AgData.slice(0, 3).map((data) => (
                  // <div className={data.SubStatusName != 'Start Journey' ? "Completed" : "journeyStarted"} >
                  <>
                  {data.SubstatusId == 2124 && blink ?
                  <div className="start blinkStart">
                    <b>Lead ID :</b>{data.LeadId} {data.AssignmentId == 1 && <b>(Store)</b>}
                  
                    {data.SubstatusId == 2124 && <p><b>Status :</b>{"Appt. Started"} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={GetData} EmployeeFlag={EmployeeFlag} />
                    {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}</p>}
                    
                  </div>
                    :
                  <div className={data.SubStatusName == 'Start Journey' ? "StartJourney" : data.SubStatusName == 'End Journey' ? "EndJourney" :
                    data.SubStatusName.replace("Appointment", " ")} >
                    <b>Lead ID :</b>{data.LeadId} {data.AssignmentId == 1 && <b>(Store)</b>}
                    {data.SubstatusId == 2002 && <p><b>Status :</b>{"Appt. Book"} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={GetData} EmployeeFlag={EmployeeFlag} />
                    {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}</p>}

                    {data.SubstatusId == 2124 && <p><b>Status :</b>{"Appt. Started"} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={GetData} EmployeeFlag={EmployeeFlag} />
                    {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}</p>}

                    {data.SubstatusId != 2002 && data.SubstatusId != 2124 && <p><b>Status :</b>{data.SubStatusName.replace("Appointment", " ")} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={GetData} EmployeeFlag={EmployeeFlag} />
                    {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}</p>}
                    {/* <p><b>Status :</b> {data.SubStatusName} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab}/></p> */}
                  </div>
                  }
                  </>
                ))
              }
              {AgData && AgData.length > 3 && <button className="morebtn" variant="primary" onClick={() => handleMore(AgData)} >+{AgData.length - 3} more</button>}

            </td>
          ))
          : <td></td>

      ))
      }
    </>
  }


  export default SlotTableView;