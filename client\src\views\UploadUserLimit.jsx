import {  useState } from "react";
import React from "react";

import {
    GetCommonData, GetCommonspData,
    UploadMatrixFiles
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import DataTable from './Common/DataTableWithFilter';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'
import 'react-datetime/css/react-datetime.css'
import * as XLSX from 'xlsx';

const UploadUserLimit = (props) => {    
    
    const PageTitle = "Manage Upload User Limit"
    const [selectedFile,setselectedFile] = useState(null)
    const [addClass,setaddClass] = useState('')
    const DownloadedFile ='/SampleExcelfiles/UserLimits.xlsx'
    const [PreviewedData, setPreviewedData] = useState([]);
    const [ErrorArray, setErrorArray] = useState([]);

    const columnlist = [

        {
            name: 'EmpCode',
            selector: 'ecode',
            type: "string",
            width: '100px',
        },
        {
            name: 'Assign Lead Limit',
            selector: 'Limit',
            cell: row => <div>{row.Limit ? row.Limit : "N.A"}</div>,
            type: "int",
            width: '100px',
        }   
    
    ];
    
    const onFileChange = (event) => {
        // Update the state 
        setPreviewedData([]);
        setErrorArray([]);

        let UserLimitFile = event.target.files[0];
        if (UserLimitFile && UserLimitFile.name && UserLimitFile.name.split('.').pop() != 'xlsx') {
            toast("Please choose valid Excel File of type xlsx", { type: 'error' });
            setaddClass('');
            setselectedFile(null);
            return;
        }
        setselectedFile(event.target.files[0]);

        if (event.target.files) {
      
            const reader = new FileReader();
            let dataArray = [];

            reader.onload = (e) => {
              const workbook = XLSX.read(e.target.result, { type: 'array' });
              const sheetName = workbook.SheetNames[0]; // Assuming we want the first sheet
              const sheet = workbook.Sheets[sheetName];
              const parsedData = XLSX.utils.sheet_to_json(sheet);
              for (let i = 0; i < parsedData.length; i++) {
                dataArray.push({ ecode: parsedData[i].Ecode, Limit: parsedData[i].Limit})
               };
            setPreviewedData(dataArray);

            }
            reader.readAsArrayBuffer(event.target.files[0]);

        }
    };

    const onFileUpload = (e) => {
      
        e.preventDefault();
        setaddClass('fa fa-spinner fa-spin');

        if (selectedFile == null) {
            toast("Please choose Excel File", { type: 'error' });
            setaddClass('');
            return;
        }
       
        // Create an object of formData
        const formData = new FormData(); 
        // Update the formData object 
        formData.append( 
            "myFile",
            selectedFile,
            selectedFile.name,
        ); 
        formData.append( 
            "type",
            "UploadUsersLimit"
        ); 
        
        // Details of the uploaded file 
        // Request made to the backend api 
        // Send formData object 
        UploadMatrixFiles(formData, function (results) {
            
            document.getElementById('files-upload').value = null;
            if (results?.data?.data && results.data.status == 200) {
                const usersLimitNotUpdated = results.data.data.filter(usersLimitUpload => usersLimitUpload.status === 0);
                usersLimitNotUpdated && setErrorArray(usersLimitNotUpdated);
                alert('File uploaded');
                setaddClass('');
                setselectedFile(null);
                setPreviewedData([]);
                //GetData() 
            }
            else if(results?.response?.data?.isError && results.response?.status == 400){
                toast(results?.response?.data.errorMessage, { type: 'error' });
                setaddClass('');
                setselectedFile(null);
                setPreviewedData([]);
                return;
            } else {
                setselectedFile(null);
                toast(results.data.message, { type: 'error' });
                return;
            }
        });
    }

    const renderDownloadFile = () => {
        if (DownloadedFile) {
            return  <Link style={{ fontSize: '14px' }} to={DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    return (
        <>

            <div className="content fosAgent" >
                <ToastContainer />

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>                                                               
                                <Row>
                                    <Col md = {10}>
                                        <form onSubmit={onFileUpload}>                                           
                                                <label for="files-upload">Upload Excel To Update User Limit</label>&ensp;
                                                <input type="file" id="files-upload" onChange={onFileChange} />                                           
                                                <button type="submit" id="uploadbutton"  className="btn btn-primary">Upload! <i class={addClass} aria-hidden="true"></i></button>                                                               
                                        </form>
                                    </Col>
                                    
                                </Row>
                                        
                               
                                {renderDownloadFile()} 
                                {PreviewedData && (PreviewedData.length > 0) && <>
                                <h3>Preview Data Before Upload </h3>
                                <DataTable
                                columns={columnlist}
                                data = {PreviewedData}
                                printexcel={false}
                                /></>}

                                {ErrorArray && (ErrorArray.length > 0) && <><h3>Details of Data Not Pushed</h3>
                                <ul>
                                    { ErrorArray.map(users => (
                                        <li>
                                            <span style={{ color: 'red' }}>{(users.message)?  users.message + ' , ' : '' } RowNo {users.RowNo} not updated for {(users.ecode)? 'Employee : ' + users.ecode : '' }{(users.Limit)? ' Limit : ' + users.Limit : '' } </span>
                                        </li>
                                    ))}
                                </ul></>
                                }

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData
    }
)(UploadUserLimit);