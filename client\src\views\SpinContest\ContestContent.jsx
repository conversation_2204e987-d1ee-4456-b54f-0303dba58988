import React from "react";
import { <PERSON>ton<PERSON><PERSON>, <PERSON>ton, Modal, Form, FormGroup } from 'react-bootstrap';
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData } from "../../store/actions/CommonActionPbIncentive";
import { connect } from "react-redux";
import { useState, useEffect } from "react";
import DataTable from '../Common/DataTableWithFilter';
import { fnDatatableCol, getUrlParameter } from '../../utility/utility.jsx';
import SweetAlert from "react-bootstrap-sweetalert";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import Spinner from '../../components/Loaders/Spinner';

const ColumnList = [
    {
        name: "SerialNo",
        label: "SerialNo",
        type: "number",
        searchable: true,
        required: true
    },
    {
        name: "ContestId",
        label: "ContestId",
        type: "number",
        searchable: true,
        editable: false
    },
    {
        name: "ContentText",
        label: "ContentText",
        type: "string",
        searchable: true,
        required: true
    },
    {
        name: "PrizeType",
        label: "PrizeType",
        type: "string",
        required: true
    },
    {
        name:"IsActive",
        label:"IsActive",
        type: "bool",
        require:true
    }
];

const ContestContent = (props) => {
    let selectedRow = {
        SerialNo: '',
        ContentText: "",
        modalValueChanged: false,
        PrizeType: null,
        PrizeId: null,
        IsActive: 1
    };
    const ContestName = getUrlParameter('ContestName');
    const ContestId = getUrlParameter('cid');
    const [options, setOptions] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [formTitle, setFormTitle] = useState("");
    const [ColumnValues, setColumnValues] = useState(selectedRow);
    const [event, setEvent] = useState("");
    const [ContestData, setContestData] = useState([]);
    const [IsLoaded, setIsLoaded] = useState(false);
    const [IsDeleteAlert, setIsDeleteAlert] = useState(false);
    const [PrizeType, setPrizeType] = useState([{ PrizeId: 0, PrizeName: 'No Reward' }, { PrizeId: 1, PrizeName: 'Reward' }]);

    const AddContestContent = () => {

        const newOptions = options.concat({ OptionText: '', SerialNo: '' });
        setOptions(newOptions);
        setColumnValues(ColumnValues);
        setEvent("Add");
        setShowModal(true);
        setFormTitle("Add Contest Content");
    }

    const handleEdit = (row) => {
        if (row.PrizeType == "Reward") {
            row.PrizeId = 1;
        }
        else {
            row.PrizeId = 0;
        }
        setColumnValues(Object.assign({}, row, {}));
        setEvent("Edit");
        setShowModal(true);
        setFormTitle("Edit Content");
    }

    const handleClose = () => {
        if (event == "Edit") {
            setColumnValues(selectedRow);
        }
        setShowModal(false);
    }

    const GetContentOptions = () => {
        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ContestOptionMaster',
            order: 'ContentId',
            con: [{ ContestId: parseInt(ContestId) }],
            c: "R",
        }, function (data) {
            setIsLoaded(true);

            if (Array.isArray(data) && data.length > 0) {
                for (let i = 0; i < data[0].length; i++) {
                    // data[0][i].isActive= data[0][i].IsActive==1 ? "Yes":"No"
                    if (!data[0][i].PrizeType) {
                        data[0][i].PrizeType = "No Reward";
                    }
                    else {
                        data[0][i].PrizeType = "Reward";
                    }
                }
                setContestData(data[0]);

            }
        });
    }

    const handleSave = () => {

        const { SerialNo, ContentText, PrizeType , PrizeId, IsActive} = ColumnValues;
        let isnum = /^\d+$/.test(SerialNo);
        if (SerialNo == "" || ContentText == "") {
            toast.error("Please enter both SerialNo and ContentText properly");
            return;
        }
        if (!PrizeType) {
            toast.error("Please Select a Reward Type");
            return;
        }
        if (!isnum) {
            toast.error("SerialNo can only be a numeric value");
            return;
        }

        if (ContentText.length > 25) {
            toast.error("Length of ContentText should be less than 25 characters");
            setColumnValues(selectedRow);
            return;
        }
        
        switch (event) {
            case 'Add':
                setShowModal(false);
                props.InsertData({
                    root: 'ContestOptionMaster',
                    body: {
                        SerialNo: parseInt(SerialNo),
                        ContentText,
                        ContestId: parseInt(ContestId),
                        PrizeType,
                        IsActive
                    }
                }, (data) => {
                    if (data?.data?.status != 200) {
                        toast.error(data?.data?.message);
                    } else {
                        GetContentOptions();
                        toast("Content Added Successfully!", { type: 'success' });
                    }

                    setColumnValues(selectedRow);
                });
                return
            case 'Edit':
                setShowModal(false);
                props.UpdateData({
                    root: 'ContestOptionMaster',
                    querydata: { ContentId: parseInt(ColumnValues?.ContentId) },
                    body: {
                        SerialNo: parseInt(SerialNo),
                        ContentText,
                        ContestId: parseInt(ContestId),
                        PrizeType: PrizeId,
                        IsActive
                    }
                }, (result) => {
                    debugger
                    if (Array.isArray(result?.data?.data) && result?.data?.data?.length > 0 && result.data.data[0].status != 200) {
                        toast.error('Error Occured!!');
                    } else {
                        GetContentOptions();
                        toast("Content Updated Successfully!", { type: 'success' });
                    }
                    setColumnValues(selectedRow);
                });
                return
            case 'Delete':
                setShowModal(false);
                props.DeleteData({
                    root: 'ContestOptionMaster',
                    querydata: { ContentId: parseInt(ColumnValues?.ContentId) },

                }, (data) => {
                    if (data?.data?.status != 200) {
                        toast.error(data?.data?.message);
                    } else {
                        GetContentOptions();
                        toast("Content Updated Successfully!", { type: 'success' });
                    }

                    setColumnValues(selectedRow);
                });
                return;
            default:
                return;
        }
    }

    const onCancelDelete = () => {
        setIsDeleteAlert(false);
    }

    const handleRemove = (row) => {
        setIsDeleteAlert(false);
        props.DeleteData({
            root: 'ContestOptionMaster',
            query: { ContentId: row.ContentId }

        }, (data) => {
            setColumnValues(selectedRow);
            if (data?.data?.status != 200) {

                toast.error(data?.data?.message);
            } else {
                GetContentOptions();
                toast("Content Deleted Successfully!", { type: 'success' });
            }
        });
    }

    const GetDeleteAlert = (row) => (
        <SweetAlert
            warning
            showCancel
            confirmBtnText="Yes!"
            title="Are you sure you want to delete this Content?"
            onConfirm={() => handleRemove(row)}
            onCancel={() => onCancelDelete()}
        >
        </SweetAlert>
    );

    const handleDelete = (e, row) => {
        e.preventDefault();
        setIsDeleteAlert(true);
        setColumnValues(row);
    }

    const fnDatatableColActions = () => {
        let columns = fnDatatableCol(ColumnList);
        columns.push({
            name: "Action",
            cell: row =>
                <ButtonGroup aria-label="Basic example">
                    <Button title="Edit" variant="secondary" onClick={() => handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
                    {/* <Button variant="secondary" onClick={(e) => handleDelete(e, row)}><i className="fa fa-trash" aria-hidden="true"></i></Button>{alert} */}
                </ButtonGroup>
        });
        return columns;
    }

    useEffect(() => {
        GetContentOptions();
    }, []);

    const TableColumns = fnDatatableColActions();

    return (
        <>{!IsLoaded ? <div className="Backdrop"> <Spinner></Spinner></div> :
            <div className="content">
                <ToastContainer />
                {IsDeleteAlert && GetDeleteAlert(ColumnValues)}
                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={9}>
                                        <CardTitle tag="h4">ADD CONTENT ({ContestName})</CardTitle>
                                    </Col>
                                    <Col md={3}>
                                        <Button variant="primary" onClick={AddContestContent} className="mt-4" name="addOptions">ADD CONTENT</Button>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <DataTable
                                    columns={TableColumns}
                                    data={ContestData}
                                    extention={true}
                                    export={false}
                                    print={false}
                                />
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
                <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
                    <Modal.Header closeButton>
                        <Modal.Title>{formTitle}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form name="frmChatAgentConfigure">
                            <Row>
                                <Form.Group as={Col} md={2} >
                                    <Form.Label>SerialNo</Form.Label>
                                    <Form.Control required

                                        type="text" onChange={(e) => setColumnValues(prev => ({
                                            ...prev,
                                            SerialNo: e.target.value,
                                            modalValueChanged: true
                                        }))} placeholder="Enter Serial No"
                                        value={ColumnValues.SerialNo} name="SerialNo" />
                                </Form.Group>
                                <Form.Group as={Col} md={3} >
                                    <Form.Label>Contest Name</Form.Label>
                                    <Form.Control required
                                        type="text"
                                        disabled
                                        value={ContestName} name="ContestName" />
                                </Form.Group>
                                <Form.Group as={Col} md={2} key={ColumnValues.PrizeId}>
                                    <Form.Label>PrizeType</Form.Label>
                                    <Form.Control required as="select"
                                        onChange={(e) => setColumnValues(prev => ({
                                            ...prev,
                                            PrizeType: e.target.value,
                                            PrizeId: e.target.value,
                                            modalValueChanged: true
                                        }))}
                                        // defaultValue={ColumnValues.ApplicationId}

                                        value={ColumnValues.PrizeId}
                                        placeholder="Select"
                                    >
                                        <option disabled selected>Select Option</option>
                                        {PrizeType && PrizeType.map((item, idx) =>
                                            <option key={item.PrizeId} value={item.PrizeId}>{item.PrizeName}</option>
                                        )}
                                    </Form.Control>
                                </Form.Group>
                                <Form.Group as={Col} md={5} >
                                    <Form.Label>Content Text</Form.Label>
                                    <Form.Control required
                                        type="text" onChange={(e) => setColumnValues(prev => ({
                                            ...prev,
                                            ContentText: e.target.value,
                                            modalValueChanged: true
                                        }))}
                                        value={ColumnValues.ContentText} name="ContentText" />
                                </Form.Group>
                                <Form.Group as={Col} >
                                    <Form.Label>IsActive &nbsp;</Form.Label>
                                    <input type="checkbox"
                                        label=""
                                        onChange={(e) => setColumnValues(prev => ({
                                            ...prev,
                                            IsActive: e.target.checked,
                                            modalValueChanged: true
                                        }))}
                                        checked={ColumnValues.IsActive}
                                        name="IsActive" id="IsActive" />
                                </Form.Group>

                            </Row>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleClose}>
                            Close
                        </Button>
                        <If condition={ColumnValues.modalValueChanged}>
                            <Then>
                                <input type="submit" value="Save Changes" className="btn btn-primary" onClick={handleSave} />
                            </Then>
                        </If>
                    </Modal.Footer>
                </Modal>
            </div>
        }
        </>
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        InsertData,
        UpdateData,
        DeleteData
    }
)(ContestContent);