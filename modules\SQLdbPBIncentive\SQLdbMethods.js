const sqlHelper = require("../../LibsPBIncentive/sqlHelper");
const Utility = require("../../LibsPBIncentive/Utility");
const UserStatsToManager = require("../SQLdb/UserStatsToManager");
//AWS.config.update(conf.s3);
var cache = require('memory-cache');
var _ = require('underscore');
const conf = require("../../env_config");
exports.FindRoot = function (req, res) {
    let result = false;
    switch (req.query.root) {
        case "Hierarchy":
            FetchManagerHierarchy(req, res);
            result = true;
            break;
        case "UserDetails":
            GetUserDetails(req, res);
            result = true;
            break;
        case "UserStatsToManager":
            UserStatsToManager.UserStatsToManager(req, res);
            result = true;
            break;
        case "GetAgentsUnderManagers":
            GetAgentsUnderManagers(req, res);
            result = true;
            break;
        case "FetchMenuMaster":
            FetchMenuMaster(req, res);
            result = true;
            break;
        
    }

    return result;
}



async function GetUserDetails(req, res) {
    try {
        if (isNaN(req.query.UserID)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }

        let query = `SELECT distinct RSM.RoleId,UD.UserID, UD.EmployeeId, UD.UserName, UD.ManagerId, ISNULL(RM.RoleName,'ADMIN') AS RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  UD.IsActive=1 and UD.UserId = @UserId`
        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: req.query.UserID });
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordset
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function FetchManagerHierarchy(req, res) {
    let result = {};
    if (req.query.ManagerId == 75) {
        result = cache.get('jerry');
        if (result === null) {
            result = await GetTree(req.query.ManagerId);
            cache.put('jerry', result, (60 * 60 * 1000), function (key, value) {
                // console.log(key + ' did ' + "EMPTY");
            });
        }
    }
    else {
        result = await GetTree(req.query.ManagerId);
    }


    res.send({
        status: 200,
        data: result
    });
}

async function GetTree(ManagerId) {
    let result = await GetUnderManagers(ManagerId);
    if (result) {
        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            let childrens = await GetTree(element.UserID);
            if (childrens && childrens.length > 0) {
                element["children"] = childrens;
            }

        }
    }

    return result;
}



async function GetUnderManagers(ManagerId) {
    try {

        let query = `SELECT distinct UD.UserID, 12 AS RoleId, UD.EmployeeId, UD.UserName, UD.ManagerId, 'Supervisor' AS RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  UD.IsActive=1 AND RSM.RoleId NOT IN(13) and UD.ManagerId = @ManagerId`
        // console.log(query);
        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        let res = result.recordset;
        return res;
    }
    catch (e) {
        console.log(e);
    }
    finally {

    }
}



/**
 * Call SP and set parameters
 * @param {*} xlsData 
 * @param {*} fixedParams 
 * @param {*} batchId 
 * @returns 
 */


async function GetAgentsUnderManagers(req, res) {
    try {

        if (isNaN(req.query.ManagerId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }


        let query = `SELECT distinct RSM.RoleId,UD.UserID, UD.EmployeeId, UD.UserName, UD.ManagerId, RM.RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE   UD.IsActive=1 AND RSM.RoleId IN (13) and UD.ManagerId IN (SELECT item from dbo.fnSplit(@ManagerId, ','))`;


        // console.log(query);

        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: req.query.ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);


        res.send({
            status: 200,
            data: result.recordset
        });
        //let res = result.recordset;
        //return res;
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function FetchMenuMaster(req, res) {
    try {

        // console.log(req.body)
        // console.log(req.query)

        if (isNaN(req.query.UserId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }

        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: req.query.UserId });

        let query = `SELECT FM.* FROM [CRM].[UserMenuMap] UMM  JOIN CRM.FunctionsMaster FM ON FM.MenuId = UMM.MenuId  where UserId = @UserId and URL LIKE '%matrixdashboard%'`
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}