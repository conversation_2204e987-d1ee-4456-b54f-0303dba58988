
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import { GetCommonData, GetCommonspData, RegisterCustomer } from "../../store/actions/CommonAction";
import { addR<PERSON>ord, GetCommonData as GetCommonMongoData } from "../../store/actions/CommonMongoAction";

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import LeadCommonFields from './LeadCommonFields';
import { fnBindRootData, fnRenderfrmControl, fnCleanData, GetJsonToArray, getMax, joinObject, getuser, getUrlParameter, getCookie } from '../../utility/utility.jsx';
import { ToastContainer, toast } from 'react-toastify';
import DropDownList from "./../Common/DropDownList.jsx"
import 'react-toastify/dist/ReactToastify.css';
import moment from "moment";
import './../customStyling.css';
import Loader from './../Common/Loader';
import _, { bind } from 'underscore';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col,
  Input,
} from "reactstrap";
import ListItem from './ListItem'
import config from "../../config.jsx";

class CreateLead extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      items: [],
      store: [],
      activePage: 1,
      root: "CreateLead",
      PageTitle: "Create Lead",
      FormTitle: "",
      formvalue: {},
      event: "",
      errors: {},
      fields: {},
      LeadSource: 'Offline',
      //LeadSourceId: 35,
      MandatoryFields: ["LeadSource", "ProductId", "Name", "EmailId", "MobileNo", "Gender", "DateOfBirth", "City", "Country"],
      showReferralFields: false,
      LeadDetails: {},
      isReferralLoading: false,
      IsReferral: false,
      options: {},
      selections: [],
      questions: {},
      questionId: 0,
      showModal: false,
      proname: '',
      setDisabled: [],
      NewLeadId: 0,
      useExistingMobile: false,
    };
    this.handleSave = this.handleSave.bind(this);
    //this.handleChange = this.handleChange.bind(this);
    this.handleValidation = this.handleValidation.bind(this);
    this.getReferralDetails = this.getReferralDetails.bind(this);
    this.handleGetDetails = this.handleGetDetails.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.createAnotherReferral = this.createAnotherReferral.bind(this);
    this.createAppointment = this.createAppointment.bind(this);

    this.selectedrow = {
      //"LeadSourceId": this.state.LeadSourceId, 
      "LeadSource": getUrlParameter("source") && getUrlParameter("source") == 'FOS_referral' ? 'Referral' :
        getUrlParameter("source") && getUrlParameter("source") == 'FOS_crosssell' ? 'CrossSell' :
          this.state.LeadSource,
      "ProductId": getUrlParameter("source") && getUrlParameter("source") == 'FOS_referral' && getUrlParameter("product") ? getUrlParameter("product") : '',
      "ReferralId": getUrlParameter("referralId") ? getUrlParameter("referralId") : '',
      "AssignToUser": getuser().UserID,
      "UTMSource": getUrlParameter("source") && getUrlParameter("source") == 'FOS_referral' ? 'FOS_referral' :
        getUrlParameter("source") && getUrlParameter("source") == 'FOS_crosssell' ? 'FOS_CrossSell' :
          this.state.LeadSource,
      DateOfBirth: moment().subtract(0, 'years').format("DD-MM-YYYY"),
      Status: 4,
    };

    let count = 0;
  }


  componentDidMount() {
    if (this.selectedrow.LeadSource == 'Referral' || this.selectedrow.LeadSource == 'CrossSell') {
      this.setState({ showReferralFields: true, IsReferral: true })
      // this.props.GetCommonData({
      //   root: 'AesDecryption',
      //   uid: getUrlParameter("referralId"),
      //   Source: 'Fos'
      //     }, function (results) {
      //         if (results.data.status == 200) {debugger;
      //           let referralId = results.data.data;
      let referralId = getUrlParameter("referralId");
      this.selectedrow.ReferralId = referralId;

      this.setState({
        formvalue: this.selectedrow
      });

      // }else{
      //     //this.setState({IsLoading: false, validUser : false})
      // }
      // }.bind(this));
    }
    this.setState({
      formvalue: this.selectedrow
    })
    setTimeout(function () {
      var sel = document.getElementById("ProductId");
      if (sel)
        this.setState({ proname: sel.options[sel.selectedIndex].text })
    }.bind(this), 2000)
    this.getHealthQuestionnaire();
    this.updateMandatoryFields();
  }

  updateMandatoryFields() {
    if (this.selectedrow.ProductId == 2) {
      this.setState({ MandatoryFields: ["LeadSource", "ProductId", "Name", "MobileNo", "Gender", "DateOfBirth", "City", "PostalCode"] })
    } else if (this.selectedrow.ProductId == 7) {
      this.setState({ MandatoryFields: ["LeadSource", "ProductId", "Name", "MobileNo", "Gender", "DateOfBirth", "ProductTypeID"] })
    } else if (["115", "117"].includes(this.selectedrow.ProductId)) {
      this.setState({ MandatoryFields: ["LeadSource", "ProductId", "Name", "MobileNo", "ProductTypeID"] })
    }
  }

  getHealthQuestionnaire() {
    this.props.GetCommonspData({
      root: "GetCustQuesAns",
    }, function (result) {
      if (result.data.status === 200 && result.data.data) {
        let questions = result.data.data[0];
        //console.log('===========', questions)
        this.setState({ questions: questions, questionId: questions[0].QuestionID });

        // var output = [];

        // questions.forEach(function(item) {
        //   var existing = output.filter(function(v, i) {
        //     return v.QuestionID == item.QuestionID;
        //   });
        //   if (existing.length) {
        //     var existingIndex = output.indexOf(existing[0]);
        //     output[existingIndex].optionHeading = output[existingIndex].optionHeading.concat(item.optionHeading);
        //     output[existingIndex].optiondescription = output[existingIndex].optiondescription.concat(item.optiondescription);
        //     output[existingIndex].OptionsID = output[existingIndex].OptionsID.concat(item.OptionsID);

        //   } else {debugger;
        //       item.optionHeading = [item.optionHeading];
        //       item.optiondescription = [item.optiondescription];
        //       item.OptionsID = [item.OptionsID];

        //     output.push(item);
        //   }

        // });

        // console.log('op',output);
        // this.setState({options : output});

      }
    }.bind(this)

    )
  }

  fnBindStore(col, nextProps) {
    /*if(col.type == "dropdown"){
      let items;
      
      if(nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]){
        
        items = joinObject(nextProps.CommonData[this.state.root],nextProps.CommonData[col.config.root],col.name)
        this.setState({ items: items });
      }
    }*/
  }
  componentWillReceiveProps(nextProps) {


    if (!nextProps.CommonData.isError) {
      //this.setState({ items: nextProps.CommonData[this.state.root] });
      //this.setState({ store: nextProps.CommonData });

      //setTimeout(function(){
      /*this.columnlist.map(col => (
        this.fnBindStore(col,nextProps)
      ));*/
      //}.bind(this),2000);

    }

    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200)
        alert(nextProps.CommonData.InsertSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        alert(nextProps.CommonData.UpdateSuccessData.error);
      else {
        //this.setState({ showModal: false });
      }
    }

  }

  leadSourcechange(e, props) {
    this.setState({
      ivrType: e.target.value
    }, function () {
      //this.fetchProductData();
    });
  }

  handleSave() {
    this.setState({ isLoading: true });
    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    if(this.handleValidation(formvalue)){
      //formvalue["CreatedOn"] = new Date();
      //this.fnCleanData(formvalue, false);
      RegisterCustomer(formvalue, function (data) {
        if (data.status === 200) {
          debugger
          //toast("Customer registered successfully!", { type: 'success' });
          let customer = data.data.customer;
          if (customer) {
            this.saveLeadDetails(customer.CustomerId);
          } else {
            toast.error("Customer could not be registered");
            this.setState({ isLoading: false });
          }
        } else {
          toast.error(data.error);
          this.setState({ isLoading: false });
        }
      }.bind(this));

    } else {
      this.setState({ isLoading: false });
    }
  }

  saveLeadDetails(customerId) {
    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    formvalue["CustomerId"] = customerId;
    this.setState({
      formvalue: formvalue
    })
    let dataToSend = this.setParameters(formvalue);
    //this.fnCleanData(formvalue, false);
    this.props.GetCommonspData({
      root: "CreateLead",
      c: "L",
      //body: dataToSend
      params: [dataToSend],
    }, function (data) {
      if(data.data.status === 200 && data.data.data && data.data.data.length > 0) {
        if(data.data.data[0][0].LeadId != 0){
        toast("Lead created successfully!", { type: 'success' });
          this.setState({ NewLeadId: data.data.data[0][0].LeadId, showModal : true, isLoading: false})
        }else{
          toast(data.data.data[0][0].outputmessage, { type: 'error' });
        }
      } else {
        toast.error("Lead could not be created");
        this.setState({ isLoading: false });
      }
      this.setState({ isLoading: false });
    }.bind(this));
  }

  handleValidation(formvalue) {
    let formIsValid = true;
    //let ErrorMsg = "";
    let errorsList = {};
    let errorMesg = 'Required fields ';
    let mobileErrorMsg = "";
    let i = 0;
    this.state.MandatoryFields.forEach((element) => {
      if (!formvalue[element]) {
        formIsValid = false;
        errorsList[element] = `This ${element} is required`;


        //ErrorMsg +=  `This ${element} is required \n`;
        if (element == 'ProductTypeID') {
          if (i > 0) errorMesg += `, `
          errorMesg += `Category`
          i++;
          //toast.error(`This Category is required`); 
        } else {
          if (i > 0) errorMesg += `, `
          errorMesg += `${element}`
          i++;
          //toast.error(`This ${element} is required`); 
        }
      } else if(element === 'MobileNo' && formvalue[element].length < 5 ) {
        formIsValid = false;
        mobileErrorMsg += "MobileNo should have minimum 5 digits ";
      }
      else if (element == 'EmailId' && (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formvalue[element]))) {
        formIsValid = false;
        errorsList[element] = `Invalid email address`;
        toast.error(`Invalid email address`);
      }
    });

    if (this.selectedrow.ProductId == 2 && this.state.selections.length == 0) {
      formIsValid = false;
      errorsList['HealthQuestionaire'] = `This HealthQuestionaire is required`;
      if (i > 0) errorMesg += `, `
      errorMesg += `HealthQuestionaire `
      //toast.error(`This HealthQuestionaire is required`); 
    }
   
    if (!formIsValid) {
      toast.error(`${errorMesg} are missing`);
      if(mobileErrorMsg) {
        toast.error(mobileErrorMsg);
      }
    }
    return formIsValid;
  }

  setParameters(params) {
    return {
      "Name": params.Name || null,
      "CustId": params.CustomerId || null,
      "Gender": params.Gender || null,
      "DOB": params.DateOfBirth || null,
      "MobileNo": params.MobileNo || null,
      "AltPhoneNo": params.AltPhoneNo || null,
      "EmailId": params.EmailId || null,
      "Address": params.Address || null,
      "CityID": params.City || null,
      "StateID": params.StateID || "2",
      "Country": params.Country || 392,
      "PostCode": params.PostalCode || null,
      "MaritalStatus": params.MaritalStatus || null,
      "AnnualIncome": params.AnnualIncome || null,
      "ProfessionID": params.ProfessionID || null,
      "LeadSource": params.LeadSource || null,
      "UTMMedium": params.UTMMedium || null,
      "ProductId": params.ProductId || null,
      "SA": params.SA || null,
      "Premium": params.Premium || null,
      "Paymode": params.PaymentSource || null,
      "Bank": params.BranchName || null,
      "Cheque": params.Cheque || null,
      "Insurer": params.Supplier || null,
      "Plan": params.PlanName || null,
      "UserID": getuser().UserID,
      "Filter": params.Filter || null,
      "StatusID": params.Status || null,
      "SubStatusID": params.SubStatus || null,
      "OfferNo": params.OfferNo || null,
      "GroupId": params.GroupId || null,
      "SmsId": params.SmsId || null,
      "PolicyTypeId": params.PolicyTypeId || null,
      "PolicyTypeName": params.PolicyTypeName || null,
      "ReferralID": params.ReferralId || null,
      "TwoYrPremium": params.TwoYrPremium || null,
      "IsPreviousClaimsTaken": params.IsPreviousClaimsTaken || null,
      "Bonus": params.Bonus || null,
      "OldPolicyNo": params.OldPolicyNo || null,
      "GracePeriod": params.GracePeriod || null,
      "PolicyExpiryDate": params.PolicyExpiryDate || null,
      "NoticePremium": params.NoticePremium || null,
      "RenewalYear": params.RenewalYear || null,
      "DOBofInsured01": params.DOBofInsured01 || null,
      "DOBofInsured02": params.DOBofInsured02 || null,
      "DOBofInsured03": params.DOBofInsured03 || null,
      "DOBofInsured04": params.DOBofInsured04 || null,
      "DOBofInsured05": params.DOBofInsured05 || null,
      "ProductTypeID": params.ProductTypeID || null,
      "VehicleModelID": params.Model || null,
      "PropertyType": params.PropertyType || null,
      "PropertyRisk": params.PropertyRisk || null,
      "UTMSource": params.UTMSource || null,
      "PlanId": params.PlanName || null,
      "AssignLead": params.AssignLead || null,
      "AssignToUserId": params.AssignToUser || null,
      "questionid": (params.ProductId == 2) ? this.state.questionId : null || null,
      "optionlist": (params.ProductId == 2) ? this.state.selections.toString() : null || null
    };
  }

  handleChange = (e, props) => {

    try {
      let formvalue = this.state.formvalue;

      if (e.target && e.target.type == "checkbox") {
        formvalue[e.target.id] = e.target.checked;
      } else if (e._isAMomentObject) {
        formvalue[props] = e.format("DD-MM-YYYY");
      } else if (e.target && e.target.id == 'City') {
        formvalue['City'] = e.target.value;
      } else if (e == 'Country') {
        formvalue['Country'] = props.value;
        formvalue['CountryName'] = props.label;
      } else {
        if (e.target && e.target.name)
          formvalue[e.target.name] = e.target.value;
      }
      this.setState({ formvalue: formvalue });
    } catch (e) {
      console.log(e)
    }
  }

  handleChangeForm = (field, e) => {
    //this.props.onChange(e, field);
    let formvalue = this.state.formvalue;
    if (e._isAMomentObject) {
      formvalue[field] = e.format("DD-MM-YYYY");
    } else {
      formvalue[field] = e.target.value;
    }
    this.setState({ formvalue: formvalue, LeadDetails: {} });
    console.log(this.state.formvalue);
    // if(field == 'Make') {
    //   this.getCarMakeModels(e.target.value);
    // } else if(field == 'Supplier') {
    //   this.getPlansList(e.target.value);
    // }
    if (field == 'LeadSource') {
      this.setState({ showReferralFields: false, IsReferral: false })
      if (e.target.value == "Referral") {
        this.setState({ showReferralFields: true })
      }
    }
    if (field == 'ProductId') {
      this.updateMandatoryFields();
    }
  }

  fnCleanData(formvalue, IsUpdate) {
    formvalue = fnCleanData(this.columnlist, formvalue, IsUpdate);
    this.setState({ formvalue: formvalue });
  }

  handleReset() {
    //let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    //this.fnCleanData(formvalue, true);
    //this.componentDidMount();
    window.scrollTo(0, 0);
    window.location.reload();
  }

  renderErrorMessages() {
    return (
      <>
        <div className="errorsContainer">
          {
            this.state.errors.map((err) =>
              <p>this is testing</p>
            )}
        </div>
      </>
    );
  }

  getReferralDetails(leadid) {
    this.props.GetCommonspData({
      root: "GetAllLeads",
      params: [{ LeadID: leadid }],
    }, function (result) {
      if (result.data.status === 200 && result.data.data) {
        var LeadDetails = result.data.data;
        if (LeadDetails && LeadDetails[0] && LeadDetails[0].length > 0) {
          this.setState({ LeadDetails: LeadDetails[0][0], useExistingMobile: true })
          var resultDetails = LeadDetails[0][0];
          let formvalue = this.state.formvalue;
          formvalue['Name'] = resultDetails.Name;
          formvalue['EmailId'] = resultDetails.EmailId;
          formvalue['MobileNo'] = resultDetails.MobileNo;
          formvalue['DateOfBirth'] = moment(resultDetails.DateOfBirth, 'DD-MM-YYYY').format('DD-MM-YYYY');
          formvalue['Gender'] = resultDetails.Gender;
          formvalue['PostalCode'] = resultDetails.PostalCode;
          formvalue['City'] = resultDetails.CityID;
          formvalue['Country'] = parseInt(resultDetails.Country);

          this.setState({ formvalue: formvalue })
        } else {
          toast.error("Referral Lead could not be found");
          this.setState({ LeadDetails: {} })
        }
      }
      this.setState({ isReferralLoading: false });
    }.bind(this));

  }

  handleGetDetails(formvalue) {
    if (formvalue['ReferralId']) {
      this.setState({ isReferralLoading: true, LeadDetails: {}, IsReferral: true })
      this.getReferralDetails(formvalue['ReferralId'])
    } else {
      toast("Enter ReferralId!", { type: 'error' });
    }
  }

  checkboxItems() {
    return (
      <React.Fragment>
        <Form.Label>{this.state.questions[0].question}<i>*</i></Form.Label>
        <p>{this.state.questions[0].QuestionDescription}</p>
        {Object.values(this.state.questions).map((option) => (
          <ListItem
            key={option.OptionsID}
            id={option.OptionsID}
            text={option}
            handleOnChange={() => this.handleCheckboxChange(option.OptionsID, option.optionHeading)}
            selected={this.state.selections.includes(option.OptionsID)}
            disabled={(this.state.setDisabled && this.state.setDisabled.length > 0 && !this.state.setDisabled.includes(option.OptionsID))}
          ></ListItem>
        ))}
      </React.Fragment>
    )
  }

  handleCheckboxChange(key,heading) {debugger;
    let sel = this.state.selections
    let find = sel.indexOf(key)
    if (find > -1) {
      this.setState({ setDisabled: [] })
      sel.splice(find, 1)
    } else {
      if (heading == 'None of these') {
        sel = [4];
        this.setState({ setDisabled: [4] })
      } else {
        sel.push(key)
      }
    }

    this.setState({
      selections: sel,
    })
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  createAnotherReferral() {
    window.scrollTo(0, 0);
    window.location.reload()
  }

  getValueFiltered(row){
    var valueArray = [2,115,7]
    if(row.LeadSource == 'Referral')
    return valueArray;
    if(row.LeadSource == 'CrossSell'){
      let proId = parseInt(getUrlParameter("product"))
      let result = valueArray.filter(function(item){
        return item !== proId;
      })
      return result;
    }
    return valueArray
  }

  createAppointment(){
    let formvalue = this.state.formvalue
    let encryptURL = window.btoa(formvalue['CustomerId'] + '/' + parseInt(formvalue['ProductId']) + '/' + parseInt(this.state.NewLeadId) + '/' + getuser().UserID)
    let FOSurl = config.api.newSalesviewUrl+"FOS/"+encryptURL+"?src=referralPage&referralId="+getUrlParameter('referralId')+"&referralProduct="+getUrlParameter('product')
    console.log('FOSurl',FOSurl)
    let user ={UserId:getuser().UserID, AsteriskToken:getCookie("AsteriskToken")}
		let u = btoa(JSON.stringify(user));
    let t = window.btoa(unescape(encodeURIComponent(FOSurl)))
    console.log('user',user,'u',u,'t',t)
    window.open(config.api.newSalesviewUrl+"auth?u="+u+"&t="+t,"_self")
  }

  render() {
    const selectedRowData = this.selectedrow;
    const { isLoading, PageTitle, FormTitle, formvalue, event, errors, showReferralFields, LeadDetails, isReferralLoading, showModal } = this.state;

    return (
      <>
        <div className="CreateLeadContainer">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>

                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1} className="hide">
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <Form>
                    <Row>
                      <Col md={4}>
                        <Form.Group>
                          <Form.Label>Lead Source <span>*</span></Form.Label>
                          <DropDownList
                            disabled={true}
                            visible={true} 
                            col={{ 
                              name: "LeadSource", 
                              label: "Lead Source",
                              type: "dropdown",
                              config: {
                                root: "LeadSource",
                                data: [
                                  //{ Id: 3, Display: "Referral" }, 
                                  { Id: "Referral", Display: "Referral" }, 
                                  { Id: "CrossSell", Display: "CrossSell" }, 
                                  { Id: 6, Display: "Renewal" },
                                  { Id: 18, Display: "NRI" },
                                  { Id: 35, Display: "Offline" },
                                ],
                              }
                            }}
                            value={selectedRowData.LeadSource}
                            filterkey={null}
                            filtervalue={null}
                            //onChange={(e) => this.handleChange()}
                            onChange={ this.handleChangeForm.bind(this, "LeadSource")}
                            required= {true} 
                          />
                        </Form.Group>
                      </Col>
                      <Col md={4}>
                        <Form.Group>
                          <Form.Label>Select Product <span>*</span></Form.Label>
                          <DropDownList
                            visible={true} 
                            valueTobefiltered={this.getValueFiltered(selectedRowData)}
                            col={{ 
                              name: "ProductId", 
                              label: "ProductId",
                              type: "dropdown",
                              config: {
                                root: "Products",
                                cols: ["ID AS Id", "ProductName AS Display"],
                                con: [{ "Isactive": 1 }],
                              },
                              //value: {selectedRowData.ProductId}
                            }}
                            value={selectedRowData.ProductId}
                            filterkey={null}
                            filtervalue={null}
                            //onChange={(e) => this.handleChange()}
                            onChange={ this.handleChangeForm.bind(this, "ProductId")}
                            required= {true} 
                            disabled= {(selectedRowData.LeadSource == 'Referral')?true:false}
                          />
                        </Form.Group>
                      </Col>
                      {showReferralFields && (
                      <>
                      <Col md={4}>
                        <Form.Group>
                          <Form.Label>Referral Id <span>*</span></Form.Label>
                          <Input 
                            placeholder="Enter ReferralId" 
                            type="text" 
                            name="ReferralId" 
                            maxLength={30} 
                            onChange={this.handleChangeForm.bind(this, "ReferralId")}
                            value={selectedRowData.ReferralId}
                            disabled={true}
                          />

                        </Form.Group>
                      </Col>
                      <Col md={1}>
                          <Button variant="primary" onClick={() => this.handleGetDetails(formvalue)}>
                            Get Details
                          </Button>

                          {isReferralLoading && 
                            <Loader />
                          }
                      </Col>
                      </>)
                      }
                    </Row>

                    <LeadCommonFields
                      onChange={this.handleChange}
                      useExistingMobile={this.state.useExistingMobile}
                      LeadSource={selectedRowData.LeadSource}
                      ProductId={selectedRowData.ProductId}
                      LeadDetails={LeadDetails}
                      isReferral={this.state.IsReferral}
                      Country={this.state.formvalue.Country || null}
                      CountryName={this.state.formvalue.CountryName || null}
                    />

                    <Row>
                      {/* <Col md={4}>
                        <Form.Group>
                          <Form.Label>Additional Details</Form.Label>
                          <Form.Control 
                            disabled={false}
                            type="text" 
                            //onChange={(e) => this.handleChange()} 
                            onChange={ this.handleChangeForm.bind(this, "AdditionalFields")}
                            as="textarea" 
                            placeholder={""} 
                            value={selectedRowData.AdditionalFields}
                          />
                        </Form.Group>
                      </Col> */}
                      <Col md={4}>
                        <Form.Group>
                          <Form.Label>UTM Source</Form.Label>
                          {(selectedRowData.LeadSource && selectedRowData.LeadSource == 'Referral') ? <Input value="FOS_Referral" type="text" name="UTMSource" disabled={true} /> :
                            (selectedRowData.LeadSource && selectedRowData.LeadSource == 'CrossSell') ? <Input value="FOS_CrossSell" type="text" name="UTMSource" disabled={true} /> : ''}
                        </Form.Group>
                      </Col>
                      <Col md={4}>
                        <div className="medicalHistoryForm">
                          {(selectedRowData.ProductId == 2) && this.state.questions && this.state.questions.length > 0 &&

                            this.checkboxItems()}
                          {/* <p>{this.state.selections.toString()}</p> */}
                        </div>
                      </Col>
                      <Col md={4}>
                        <Form.Group>
                          {/* <Form.Label>Assign To User</Form.Label> */}
                          <Input value={getuser().UserID} type="hidden" name="AssignToUser" disabled={true} />

                          {/* <AutoCompleteDropDown name={"AssignToUser"} data={this.Cities} onChange={this.handleChange.bind(this, "AssignToUser")} /> */}
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row>
                      <Col md={1} xs={6} className="text-center">
                        <Button variant="primary" onClick={this.handleSave} disabled={this.state.isLoading}>
                          Submit
                        </Button>

                        {isLoading &&
                          <Loader />
                        }
                      </Col>

                      <Col md={1} xs={6} className="text-center">
                        <Button variant="primary" onClick={(e) => this.handleReset()}>
                          Reset
                        </Button>
                      </Col>
                    </Row>
                  </Form>

                  <Modal show={showModal} onHide={this.handleClose} enforceFocus={false} dialogClassName="modal-50w">
                    {/* <Modal.Header closeButton>
                  <Modal.Title>{FormTitle}</Modal.Title>
                </Modal.Header> */}
                    <Modal.Body>
                      <Form name="frmUploadStory" className="leadCreatedSuesses">
                        <p>Lead created successfully</p>
                        <h4>{formvalue['Name']}</h4>
                        <p className="ReferalID">{this.state.NewLeadId} | {this.state.proname}</p>
                        <div className="text-center">
                          <Button className="AppointmentBTN" variant="secondary" onClick={this.createAppointment}>
                            Create Appointment
                          </Button>
                          <Button className="ReferalBTN" variant="secondary" id={"Fos_referral"} onClick={this.createAnotherReferral}>
                            Create another Referral
                          </Button>
                          <Button className="GoHomeBtn" variant="secondary" id="fos_home">
                            Go to Home
                          </Button>
                        </div>
                      </Form>
                    </Modal.Body>
                    {/* <Modal.Footer>

                  <Button variant="secondary" onClick={this.handleClose}>
                    Close
                  </Button>
                  </Modal.Footer> */}
                  </Modal>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonMongoData
  }
)(CreateLead);