
import React from "react";
import {GetCommonspData} from "../../store/actions/CommonAction"
import { connect } from "react-redux";

class CheckboxItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      selections: []
    }
  }

  componentDidMount(){
    this.props.GetCommonspData({
      root: this.props.root
    }, function (result) {
      if(result && result.data && result.data.data){
          this.setState({ items: result.data.data[0] });
      }
      }.bind(this));
  }

  handleCheckboxChange(key,heading) {
    let sel = this.state.selections
    let find = sel.indexOf(key)
    if (find > -1) {
      sel.splice(find, 1)
    } else {
        sel.push(key)      
    }
    this.props.setCheckboxValue(sel)
    this.setState({
      selections: sel,
    })
  }

  render() {
    let  items  = this.state.items;
    //console.log('checkeditems',this.props.checkedItems)
    return (
      <div>
          
           {items && (items.length > 0) && Object.values(items).map((option) => (
            <label class="container"> {option.optionHeading}
             <input type="checkbox"
              key={option.OptionsID}
              id={option.OptionsID}
              onChange={() => this.handleCheckboxChange(option.OptionsID,option.optionHeading)}
              checked={(this.props.checkedItems && this.props.checkedItems.length > 0)?this.props.checkedItems.includes(option.OptionsID):this.state.selections.includes(option.OptionsID)}
              disabled = {this.props.disabled}
              />
                  <span class="checkmark"></span>
             </label>
          ))}

    </div>
    );
  }
}

function mapStateToProps(state) {
  return {
      CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
      GetCommonspData
  }
)(CheckboxItem);
