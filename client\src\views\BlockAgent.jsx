
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import { GetCommonspData } from "../store/actions/CommonAction";

import {
  GetRealTimeAgentData, InsertData, UpdateData, SetAgentStatusBlocked, TransferCallThirdParty,
  MergeCallThirdParty, UnHoldCallThirdParty, GetRealTimeSingleAgentData, AgentToAgentCall, ReAssignedPODLead,
  SendReassignNotification,
  RejectLead,
  CreateLead
} from "../store/actions/CommonAction";
import {
  addRecord, GetCommonData
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
import moment from "moment";
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getUrlParameter, fnBindRootData, fnDatatableCol } from '../utility/utility.jsx';

import thirdPartyNumbers from "../Configs/thirdpartynumbers.jsx";
import message from "../Configs/message.jsx";
import Loader from './Common/Loader';
import _ from 'underscore';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import ConfirmDialog from './Common/ConfirmDialog';
import AlertDialog from './Common/AlertDialog';
import './customStyling.css';
import ModalPopup from "./Common/ModalPopup/ModalPopup";

class BlockAgent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      items: [],
      AgentData: [],
      activePage: 1,
      root: ["engageinsurerrm"].indexOf(getUrlParameter("transfer_type")) == -1 ? "BlockAgent" : "RMInsurerList",
      PageTitle: ["engageinsurerrm"].indexOf(getUrlParameter("transfer_type")) == -1 ? "Agents List" : "RM Insurer List",
      winactive: 0,
      AgentCode: '',
      TransferLoader: false,
      MergeLoader: false,
      UnholdLoader: false,
      CallAgentLoader: false,
      grade: null,
      IsBlocked: false,
      DIDNo: '',
      SalesAgentIp: '',
      IsConCall: false,
      IsConferenceSuccussful: false,
      ConfirmationOpen: false,
      selectedRow: null,
      highlightTransferBtn: false,
      highlightMergeBtn: false,
      InitialWarningPopup: (getUrlParameter('productid') != 219) ? true : false,
      isRegionalTransfer: false,
      lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      RMInsurerList: [],
      IsRMInsurer: ["engageinsurerrm"].indexOf(getUrlParameter("transfer_type")) == -1 ? 0 : 1,
      TransferType: '',
      TransferSteps: (getUrlParameter('productid') == 219) ? true : false,
      CallTransferImage: '/Images/CallTransferSteps.png',
      languageQueue: null,
      IsAgentBlockedToTransferCall: false
    };

    this.schdular = null;
    this.winactive = 0;

    this.StopAgentListSchedular = false;

    this.RMInsurerColumnlist = [

      {
        label: "Area",
        name: "Region",
        sortable: true,
      },
      {
        label: "Name & Empcode",
        name: "EmpCode",
        sortable: true,
        cell: row => row.EmpCode ? row.EmpName + ' (' + row.EmpCode + ')' : "N.A",
      },
      {
        label: "Action",
        cell: row =>
          <div className="moreinfo">{<button id="blockagent" className="btn btn-primary btn-sm" onClick={(e) => this.showConfirmationModal(e, row)}>
            Call
          </button>}
          </div>
      },

    ];

    this.columnlist = [

      {
        label: "Agent Code",
        name: "AgentCode",
        sortable: true,
      },
      {
        label: "Status",
        name: "Status",
        sortable: true,
        cell: row => <div className={row.Status.toUpperCase() + " RealtimeStatus"}>{row.Status.toUpperCase()}</div>
      },
      {
        label: "Agent Name",
        name: "AgentName",
        sortable: true,
      },
      // {
      //   label: "Grade",
      //   name: "Grade",
      //   sortable: true,
      // },
      {
        label: "Block",
        cell: row =>
          <div className="moreinfo">{this.showBlockButton(row) ? <button id="blockagent" className="btn btn-primary btn-sm" onClick={(e) => this.showConfirmationModal(e, row)}>
            Block
          </button> : ""}
          </div>
      },
      {
        label: "CallAgent",
        cell: row =>
          <div className="moreinfo">{(row.Status.toUpperCase() == 'IDLE' && getUrlParameter("customernumber")) ? <a href="#" onClick={(e) => this.callAgent(e, row)} id="agenttoagent" className="callAgentbtn">
            <i className="fa fa-phone"></i> {this.CheckLoader('callagent')} </a> : ""}
          </div>
      },

    ];


  }


  componentWillUnmount() {
    clearInterval(this.schdular);
  }


  componentDidMount() {
    this.setState({ TransferType: getUrlParameter('transfer_type') })
    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    this.savePageLogs();

    if (getUrlParameter('transfer_type') == 'engageinsurerrm') {
      this.getRMInsurerList();
      return;
    } else {
      this.getRealTimeData();
    }
    if (getUrlParameter('languageId') && getUrlParameter('transfer_type') == 'crttosalescrosssell') {
      var postData = {
        LeadID: getUrlParameter('bookingid'),
        LanguageID: getUrlParameter('languageId'),
        ProductID: getUrlParameter('productid'),
      };

      this.props.GetCommonspData(
        {
          root: "GetQueueName",
          c: "L",
          params: [postData],
        },
        (result) => {
          this.setState({
            languageQueue: (result && result.data &&
              Array.isArray(result.data.data) &&
              result.data.data.length > 0 &&
              result.data.data[0] &&
              Array.isArray(result.data.data[0]) &&
              result.data.data[0].length > 0 &&
              result.data.data[0][0] &&
              result.data.data[0][0].queuename) || undefined
          });
        }
      );
    }

    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //console.log(this.StopAgentListSchedular);
        if (this.StopAgentListSchedular) {
          this.getRealTimeData();
        }
        //if (this.state.winactive == 1 || document.hasFocus()) {
        //}
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }
  }

  getRMInsurerList() {
    // this.props.GetCommonData({
    //   root: this.state.root,
    //   c: "MATRIX_DASHBOARD_CLIENT",
    // });
    try {
      this.props.GetCommonData({
        root: this.state.root,
        con: { EmpGroupID: parseInt(getUrlParameter('groupid')) },
        c: "MATRIX_DASHBOARD_CLIENT",
      })
    }
    catch (ex) {
      console.log(ex)
    }
    this.setState({ isLoaded: true })
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {

      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.setState({ store: nextProps.CommonData });
    }

  }

  showBlockButton(row) {
    let campaign = getUrlParameter('campaign');
    if (row.Status.toUpperCase() == 'IDLE') {
      return true;
    } else if (row.Status.toUpperCase() == 'PAUSE' && (["construction", "cyber_risk", "directors", "erection", "errors", "gen_liability",
      "g_p_accident", "g_term_life", "smegmcsalesib", "smesalesib", "plant", "indemnity", "wcm_assignment", "marine", "fire"].indexOf(campaign) > -1)) {
      return true;
    }
    else {
      return false;
    }
  }

  savePageLogs() {
    this.props.addRecord({
      root: "DashboardLogs",
      body: {
        module: this.state.root,
        url: window.location.href,
        request: { "querystring": this.props.location?.search },
        response: {},
        on: new Date(),
        by: getuser().UserID
      }
    });
  };

  /*recheckAgentStatusBeforeCall(element) {
    if(this.state.selectedRow 
      && this.state.selectedRow.AgentCode == element.AgentCode 
      && ['RINGING', 'BUSY', 'CALLINITIATED'].indexOf(element.Status.toUpperCase()) > -1
    ){
      toast.remove();
      toast("Selected agent is on another call, please block other agent.", { type: 'error' });
    }
  };*/

  getRealTimeData() {
    this.StopAgentListSchedular = false;

    let context = this.state.languageQueue ? this.state.languageQueue : getUrlParameter("campaign");
    let transfer_type = getUrlParameter("transfer_type");
    let grade = getUrlParameter("grade");


    let salesagent = '';
    let assignedagent = '';
    if (["service_to_sales", "POD", "service_assigned_agent", "rmhealthweb", "sales_assigned", "Sales_to_RM", "Claim_to_RM", "RMSmeTransfer", "IssuanceAgentTransfer"].indexOf(transfer_type) > -1) {
      assignedagent = getUrlParameter("assignedagent");
      salesagent = getUrlParameter("salesagent");
    }
    if (["service_to_salesagent"].indexOf(transfer_type) > -1) {
      salesagent = getUrlParameter("salesagent");
    }
    //if(["claim_transfer"].indexOf(transfer_type) == -1){

    if (["POD"].indexOf(transfer_type) > -1) {

      GetRealTimeAgentData('', context, function (results) {
        this.StopAgentListSchedular = true;
        let AgentData = [];
        let gradelist = (grade) ? grade.split(',').map(Number) : '';
        results.data.forEach(element => {
          if (gradelist && gradelist.indexOf(parseInt(element.Grade)) > -1) {
            AgentData.push(element);
            //this.recheckAgentStatusBeforeCall(element);
          }
        });
        this.setState({
          isLoaded: true,
          AgentData: _.sortBy(AgentData, function (item) {
            return parseInt(item.Grade);
          }),
          lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss")
        });

      }.bind(this));

    } else {

      if (assignedagent) {
        GetRealTimeSingleAgentData(assignedagent, function (results) {
          //this.recheckAgentStatusBeforeCall(results.data[0]);
          this.StopAgentListSchedular = true;
          this.setState({ isLoaded: true, AgentData: results.data });
        }.bind(this));
      }
      else if (salesagent) {
        GetRealTimeSingleAgentData(salesagent, function (results) {
          //this.recheckAgentStatusBeforeCall(results.data[0]);
          this.StopAgentListSchedular = true;
          this.setState({ isLoaded: true, AgentData: results.data });
          if (results.data) {
            //console.log(results.data[0],results.data[0].DIDNo,results.data[0].AgentIP,'9999999999999999999999')
            this.setState({ 'DIDNo': results.data[0].DIDNo, 'SalesAgentIp': results.data[0].AgentIP })
          }
        }.bind(this));
      }
      else if (context) {
        GetRealTimeAgentData('', context, function (results) {
          this.StopAgentListSchedular = true;
          let campaign = this.state.languageQueue ? this.state.languageQueue : getUrlParameter("campaign");
          let grade = getUrlParameter("grade");

          if (["transfer_salesservice", "sales_to_service"].indexOf(transfer_type) > -1 && (["termmax"].indexOf(campaign) > -1 || ["termtata"].indexOf(campaign) > -1 || ["hdfclife"].indexOf(campaign) > -1)) {
            let AgentData = [];

            if (grade == "null" || grade == "" || grade == 0) {
              this.setState({ isLoaded: true, AgentData: results.data, lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss") });
            }
            else {
              results.data.forEach(element => {
                if (element.Grade == grade) {
                  AgentData.push(element);
                  //this.recheckAgentStatusBeforeCall(element);
                }
              });
              this.setState({ isLoaded: true, AgentData: AgentData, lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss") });
            }
          }
          else {
            this.setState({ isLoaded: true, AgentData: results.data, lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss") });
          }

        }.bind(this));
      }
    }

    //}

  }

  callAgent(e, row) {

    this.setState({ CallAgentLoader: true });
    var agenttoagent = document.getElementById("agenttoagent");
    agenttoagent.classList.add("disabled");
    let leadid = getUrlParameter("bookingid");
    let serviceagent = getUrlParameter("agent");
    let salesagent = getUrlParameter("salesagent");
    let sericeagentphn = getUrlParameter("serviceagentphn");
    let salesagentphn = (this.state.DIDNo) ? this.state.DIDNo : 0;//getUrlParameter("salesagentphn");
    let uid = getUrlParameter("uid");
    let serviceserverip = getUrlParameter("serviceserverip");
    let salesserverip = this.state.SalesAgentIp;//getUrlParameter("salesserverip");

    AgentToAgentCall(leadid, serviceagent, salesagent, sericeagentphn, salesagentphn,
      uid, serviceserverip, salesserverip, function (results) {
        agenttoagent.classList.remove("disabled");
        this.setState({ CallAgentLoader: false });
        if (results && results.data && results.data.status == 200) {
          toast(results.data.message, { type: 'success' });
        } else {
          toast(results.data.message, { type: 'error' });
        }
      }.bind(this));
  }

  scheduleCallback(e) {
    if (["POD"].indexOf(getUrlParameter("transfer_type")) > -1) {
      localStorage.setItem('ReassignedLead', getUrlParameter("bookingid"));
    }
    window.parent.postMessage({ "action": "ScheduleCallback" }, '*');
  }

  hideConfirmationModal() {
    this.setState({ ConfirmationOpen: false, selectedRow: null });
  }

  showConfirmationModal(e, row) {
    this.setState({ ConfirmationOpen: true, selectedRow: row });
  }

  handleConfirmOk(e) {
    if (!this.state.IsRMInsurer) {
      this.blockAgent(e, this.state.selectedRow);
      this.setState({IsAgentBlockedToTransferCall: true});
    } else {
      this.clickTransfer();
    }
  }

  blockAgent(e, row) {
    //clearInterval(this.schdular);
    var blockagent = document.getElementById("transfer");
    blockagent.classList.add("disabled");
    var merge = document.getElementById("merge");
    let AgentCode = row.AgentCode;
    let grade = row.grade;
    let managerid = getUrlParameter("agent");
    let bookingid = getUrlParameter("bookingid");
    if (!grade) {
      grade = '';
    }
    if (AgentCode && managerid && bookingid) {
      SetAgentStatusBlocked(managerid, AgentCode, bookingid, grade, function (results) {
        blockagent.classList.remove("disabled");
        if (results && results.data && results.data.status == 200) {
          this.setState({ AgentCode: AgentCode, highlightTransferBtn: true });
          merge.classList.add("disabled");
          toast(results.data.message, { type: 'success' });
        } else {
          this.setState({ AgentCode: '', selectedRow: null });
          toast(results.data.message, { type: 'error' });
        }
        this.setState({ IsBlocked: true, ConfirmationOpen: false });
        this.setState({IsAgentBlockedToTransferCall:false})
      }.bind(this));

      /*GetRealTimeSingleAgentData(AgentCode, function (resp) {
        if(resp.data[0] && resp.data[0].Status.toUpperCase() == 'IDLE') {
          //SetAgentStatusBlocked(managerid, AgentCode, bookingid, grade, function (results) {
          let params = {
            "agentId" : managerid, 
            "transferAgent" : AgentCode
          }
          SetAgentStatusDirect(params, function (results) {
            blockagent.classList.remove("disabled");
            //if (results && results.data && results.data.status == 200) {
            if (results && results.data && results.data.status == true) {
              this.setState({AgentCode : AgentCode});
              toast("Agent " + AgentCode + " is blocked successfully!!", { type: 'success' });
            } else {
              this.setState({AgentCode : '', selectedRow: null});
              toast(results.data.message, { type: 'error' });
            }
            this.setState({ IsBlocked: true, ConfirmationOpen : false });
            
          }.bind(this));
        } else {
            this.setState({AgentCode : '', selectedRow: null, IsBlocked: true, ConfirmationOpen : false});
            toast("Agent is not available at the moment, please block other agent", { type: 'error' });
        }
      }.bind(this));*/


    }

  }

  transferCall() {
    if (getUrlParameter("transfer_type") === 'SmeInterTeamTransfer') {
      if ((getUrlParameter("subProductId") !== getUrlParameter("initialLeadSubProductId")) && (getUrlParameter("isSmeRenewal") === "true")) {
        this.CreateLead().then((res) => {
          if (res && res.data && res.data.data && res.data.data.LeadId) {
            console.log('Call transferred to LeadId: ' + res.data.data.LeadId);
            toast('Call transferred to LeadId: ' + res.data.data.LeadId, { type: 'success' });

            this.clickTransfer(res.data.data.LeadId);
          }
        })
      }
      else if ((getUrlParameter("subProductId") === getUrlParameter("initialLeadSubProductId")) || (getUrlParameter("isSmeRenewal") === "true")) {
        this.clickTransfer();
      }
      else {
        this.RejectLeads().then((result) => {
          if (result && result.data) {
            this.CreateLead().then((res) => {
              if (res && res.data && res.data.data && res.data.data.LeadId) {
                console.log('Call transferred to LeadId: ' + res.data.data.LeadId);
                toast('Call transferred to LeadId: ' + res.data.data.LeadId, { type: 'success' });

                this.clickTransfer(res.data.data.LeadId);
              }
            })
          }
        })
      }
    }
    else if(getUrlParameter("transfer_type") === 'renewaltofreshreferrals'){
      this.CreateHealthReferralLead().then((res) => {
        if (res && res.data && res.data.data && res.data.data.LeadId) {
          console.log('Call transferred to LeadId: ' + res.data.data.LeadId);
          toast('Call transferred to LeadId: ' + res.data.data.LeadId, { type: 'success' });
          this.clickTransfer(res.data.data.LeadId);
        }
      })
    }
    else {
      this.clickTransfer()
    }

    if (getUrlParameter("transfer_type") === 'regional_transfer') {
      this.setState({ isRegionalTransfer : true });
    }

  }

  CreateLead = () => {
    var request = {
      ReferralLead: getUrlParameter("bookingid"),
      ProductId: 131,
      UserId: getuser().UserID,
      CallTransferType: getUrlParameter("transfer_type"),
      SubProductId: getUrlParameter("subProductId"),
      AssignLead: false
    };
    return CreateLead(request);
  }
  
  CreateHealthReferralLead = () => {
    var request = {
      ReferralLead: getUrlParameter("bookingid"),
      ProductId: 2,
      UserId: getuser().UserID,
      CallTransferType: getUrlParameter("transfer_type"),
      AssignLead: true,
      UtmMedium : 'Health_Renewal'
    };
    return CreateLead(request);
  }

  RejectLeads = () => {
    var request = {
      Leads: getUrlParameter("bookingid"),
      SubStatusId: 2398,
      ProductId: 131,
      UserID: getuser().UserID,
      FutureProsDate: 0
    };
    return RejectLead(request);
  }

  clickTransfer(leadId = 0) {
    var transfer = document.getElementById("transfer");
    var merge = document.getElementById("merge");
    var unhold = document.getElementById("unhold");
    var grade = getUrlParameter("grade");
    let campaign = this.state.languageQueue ? this.state.languageQueue : getUrlParameter("campaign");
    let transfer_type = getUrlParameter("transfer_type");
    let bookingid = leadId > 0 ? leadId : getUrlParameter("bookingid");
    let pushrecording = getUrlParameter("pushrecording") || '1';
    this.setState({ highlightTransferBtn: false });

    //In case of POD 
    if (["POD"].indexOf(getUrlParameter("transfer_type")) > -1 && localStorage.getItem('ReassignedLead') == bookingid) {
      toast('Lead is already assigned to the agent', { type: 'error' });
      return false;
    }


    if ((["religarehealth"].indexOf(campaign) > -1 || ["starhealth"].indexOf(campaign) > -1 || ["maxhealth"].indexOf(campaign) > -1
      || ["abhihealth"].indexOf(campaign) > -1) && this.state.IsBlocked == false) {
      toast("Please block any agent, first!!!", { type: 'error' });
      return false;
    }

    if (["transfer_salesservice", "sales_to_service"].indexOf(transfer_type) > -1
      && (["termmax"].indexOf(campaign) > -1 || ["termtata"].indexOf(campaign) > -1 || ["hdfclife"].indexOf(campaign) > -1)
      && this.state.IsBlocked == false) {
      toast("Please block any agent, first!!!", { type: 'error' });
      return false;
    }

    if (["service_to_salesagent"].indexOf(transfer_type) > -1 && this.state.IsBlocked == false && !getUrlParameter('customernumber')) {
      toast("Please block any agent, first!!!", { type: 'error' });
      return false;
    }

    // let tpn = _.where(thirdPartyNumbers.tpn, { id: getUrlParameter("insurerid") })

    // if (tpn.length == 0) {
    //   toast("Insurer not found", { type: 'error' });
    //   return;
    // }

    this.setState({ TransferLoader: true, grade: grade });
    (!this.state.IsRMInsurer) && transfer.classList.add("disabled");

    let agent = getUrlParameter("agent");
    //let thirdpartynumber = tpn[0].number;
    let thirdpartynumber = (!this.state.IsRMInsurer) ? getUrlParameter("thirdpartynumber", 1) : this.state.selectedRow['ContactNo'];
    let isCreateLead = getUrlParameter("iscreatelead");
    //let bookingid = getUrlParameter("bookingid");
    let dtmf_no = getUrlParameter("dtmf_no");
    let insurer = getUrlParameter("insurer");
    let application_number = getUrlParameter("application_number");
    let transfer_agents = getUrlParameter("transfer_agents");

    let claimid = getUrlParameter("claimid");
    let claim_callid = getUrlParameter("claim_callid");
    let productid = getUrlParameter("productid");
    let salesagent = getUrlParameter("salesagent");
    let isenc = getUrlParameter("isenc");
    let countrycode = getUrlParameter("countrycode");
    let dialercode = getUrlParameter("dialercode");
    var context = getUrlParameter("campaign");

    if (getUrlParameter("customernumber")) {
      thirdpartynumber = getUrlParameter("customernumber");
    }

    let blockedAgentid = this.state.AgentCode;
    if (!grade) {
      grade = '';
    }
    TransferCallThirdParty(agent, thirdpartynumber, campaign, bookingid, transfer_type, dtmf_no, insurer,
      application_number, transfer_agents, grade, blockedAgentid, claimid, claim_callid, productid, salesagent, isenc, countrycode, dialercode, isCreateLead, pushrecording, function (results) {

        if (results && results.data && results.data.status) {
          //debugger;
          this.setState({ TransferLoader: false, ConfirmationOpen: false });

          if (results.data.status == 200) {
            toast("Success, Call is being transferred", { type: 'success' });

            //merge.classList.remove("disabled");
            //unhold.classList.remove("disabled");

            //In case of POD 
            this.setState({ IsConferenceSuccussful: true, highlightMergeBtn: true });
            /*if( ["POD"].indexOf(getUrlParameter("transfer_type")) > -1 ) {
              localStorage.setItem('ReassignedLead', bookingid);
            }*/
          }
          //In case of POD 
          else if ((["POD"].indexOf(getUrlParameter("transfer_type")) > -1) && (results.data.status == 405) && (results.data.message == 'NoIdealAgentAvailable')) {
            //this.reassignPODLead();
            toast('All agents are busy now, Please set a call back.', { type: 'warning' });
            this.setState({ IsConCall: true });
            this.setState({ IsConferenceSuccussful: false });
          } else {

            let ResponseMessage = _.where(message.status, { status: results.data.status })
            if (ResponseMessage.length > 0) {
              toast(ResponseMessage[0].message, { type: 'error' });
            } else {
              toast(results.data.message, { type: 'error' });
            }

          }


        }
        this.setState({ TransferData: results.data });
      }.bind(this));

  }

  clickMerge() {
    var merge = document.getElementById("merge");
    merge.classList.add("disabled");
    this.setState({ MergeLoader: true, highlightMergeBtn: false });

    let agent = getUrlParameter("agent");
    //var transfer_type = getUrlParameter("transfer_type");
    //var context = getUrlParameter("campaign");

    MergeCallThirdParty(agent, function (results) {
      if (results && results.data && results.data.status) {
        //debugger;
        this.setState({ MergeLoader: false });
        merge.classList.remove("disabled");

        if (results.data.status == 200) {
          toast(results.data.message, { type: 'success' });
        }
        else {
          toast(results.data.message, { type: 'error' });
        }

      }
      this.setState({ MergeData: results.data });

    }.bind(this));

  }

  clickUnHold() {
    var unhold = document.getElementById("unhold");
    var merge = document.getElementById("merge");
    //merge.classList.add("disabled");
    unhold.classList.add("disabled");
    this.setState({ UnholdLoader: true, highlightMergeBtn: false });

    let agent = getUrlParameter("agent");

    UnHoldCallThirdParty(agent, function (results) {
      unhold.classList.remove("disabled");
      this.setState({ UnholdLoader: false });
      if (results && results.data && results.data.status) {
        if (results.data.status == 200) {
          toast('Success, Call is Unhold.', { type: 'success' });
        }
        else {
          toast(results.data.message, { type: 'error' });
        }
      }
      this.setState({ UnHoldData: results.data });
    }.bind(this));
  }

  CheckLoader(action) {
    if (action == 'transfer') {
      if (this.state.TransferLoader)
        return <Loader />;
    } else if (action == 'merge') {
      if (this.state.MergeLoader)
        return <Loader />;
    } else if (action == 'unhold') {
      if (this.state.UnholdLoader)
        return <Loader />;
    } else if (action == 'callagent') {
      if (this.state.CallAgentLoader)
        return <Loader />;
    }

  }

  renderTransferData() {
    var transferheading = ''
    if (["claim_transfer"].indexOf(getUrlParameter("transfer_type")) > -1) {
      transferheading = 'Claim Transfer'
    }
    else if (["POD"].indexOf(getUrlParameter("transfer_type")) > -1) {
      transferheading = 'POD Transfer'
    }
    else if (["engageinsurerrm"].indexOf(getUrlParameter("transfer_type")) > -1) {
      transferheading = 'RM Insurer Transfer'
    }
    else if (["tltransfer"].indexOf(getUrlParameter("transfer_type")) > -1) {
      transferheading = 'Call Transfer'
    }
    else {
      transferheading = 'Service Agent Transfer'
    }

    var colSize = 4;
    var enableCallback = false;
    if (["POD"].indexOf(getUrlParameter("transfer_type")) > -1 && this.state.IsConCall && this.state.IsConferenceSuccussful === false) {
      enableCallback = true;
      colSize = 3;
    }

    const campaign = this.state.languageQueue ? this.state.languageQueue : getUrlParameter("campaign");
    const transfer_type = getUrlParameter("transfer_type");

    return <Row>
      <Col md="12">
        <Card>
          <CardHeader>
            <Row>
              <Col md={12} className="d-flex justify-content-between align-items-center">
                <div className="text-center flex-grow-1">
                  <CardTitle tag="h5">
                    {(!this.state.IsRMInsurer) ?
                      <center>
                        {transferheading}
                      </center>
                      : transferheading
                    }
                  </CardTitle>
                </div>
                <div className="text-right">
                  <p><strong>Campaign:</strong> {campaign} | <strong>Transfer Type:</strong> {transfer_type}</p>
                </div>
              </Col>
            </Row>
          </CardHeader>
          <CardBody>
            <Row>
              {(!this.state.IsRMInsurer) &&
                <Col md={colSize}>
                  <button id="transfer" disabled={this.state.IsAgentBlockedToTransferCall} onClick={(e) => this.transferCall()} className={this.state.highlightTransferBtn == true ? "btn btn-primary full-width blink" : "btn btn-primary full-width"}>Transfer Call {this.CheckLoader('transfer')}</button>
                </Col>
              }
              <Col md={colSize}>
                <button id="merge" onClick={(e) => this.clickMerge()} className={this.state.highlightMergeBtn == true ? "btn btn-primary full-width blink" : "btn btn-primary full-width"}>Merge Call {this.CheckLoader('merge')}</button>
              </Col>
              <Col md={colSize}>
                <button id="unhold" onClick={(e) => this.clickUnHold()} className="btn btn-primary full-width">UnHold {this.CheckLoader('unhold')}</button>
              </Col>
              {(enableCallback) && (
                <Col md={colSize}>
                  <button id="scheduleCall" onClick={(e) => this.scheduleCallback(e)} className="btn btn-primary full-width">Set Call</button>
                </Col>
              )
              }
            </Row>
            {(!this.state.TransferSteps) && <Row>
              <Col md={12} className={"BlockAgentNoticeMsg " + (this.state.IsRMInsurer ? "align-left" : "")}>
                <br />
                Please brief the new
                {(!this.state.IsRMInsurer) ? ' advisor ' : ' RM Insurer '}
                with customer details before merging the call into conference.
              </Col>
            </Row>
            }
            {(this.state.TransferSteps) && <><br /><br /><Row>
              <Col md={14}> <img alt="CallTransfer" src={this.state.CallTransferImage} />
              </Col>
            </Row></>
            }
          </CardBody>
        </Card>
      </Col>
    </Row>

  }

  handleInitialWarningPopup(e) {
    this.setState({ InitialWarningPopup: false });
  }

  fnDatatableCol() {
    var columns = (!this.state.IsRMInsurer) ? fnDatatableCol(this.columnlist) : fnDatatableCol(this.RMInsurerColumnlist);
    return columns;
  }

  getMessagePopUp() {
    let message = "Please make sure you have informed the customer about merging this call with another"
    if (!this.state.IsRMInsurer) {
      return message + ' advisor'
    } else {
      return message + ' RM Insurer'
    }
  }


  handleRegionalTransferPopup = () => {
    this.setState({ isRegionalTransfer: false });
  }


  render() {
    const columns = this.fnDatatableCol();
    const { PageTitle, showAlert, AlertMsg, AlertVarient, TransferType } = this.state;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>

          {(
            this.state.InitialWarningPopup &&
            <AlertDialog
              onConfirm={(e) => this.handleInitialWarningPopup(e)}
              onCancel={(e) => this.handleInitialWarningPopup(e)}
              buttonLabel="OK"
              title="Confirm"
              message={this.getMessagePopUp()}
              show={true}
            />
          )}

          {(
            this.state.isRegionalTransfer &&
            <ModalPopup
              show={this.state.isRegionalTransfer}
              onClose={this.handleRegionalTransferPopup}
              title={"Please Note"}
              showCancelButton={false}
              confirmText="OK"
            >
              <p>
                If the transfer is unsuccessful, the lead will be automatically assigned to another advisor.
                
              </p>
              <p><b>Please do not reject this lead.</b></p>
            </ModalPopup>
          )}

          {this.renderTransferData()}
          {(TransferType !== 'tltransfer') && (
            <Row>
              <Col md="12">
                <Card>

                  <CardHeader>
                    <Row>
                      <Col md={7}>
                        <CardTitle tag="h5">
                          {PageTitle} {(!this.state.isLoaded && <Loader />)}
                        </CardTitle>
                      </Col>
                    </Row>
                    {(!this.state.IsRMInsurer) && <Row>
                      <Col>
                        <div>
                          Last Refresh Time : {this.state.lastRefreshTime}
                        </div>
                      </Col>
                    </Row>}
                  </CardHeader>

                  <CardBody className="BlockAgentContainer">
                    {(
                      this.state.ConfirmationOpen &&
                      <ConfirmDialog
                        onConfirm={(e) => this.handleConfirmOk(e)}
                        onCancel={() => this.hideConfirmationModal()}
                        title="Confirm"
                        message={(!this.state.IsRMInsurer) ? "Are you sure you wish to block this agent?" : "Are you sure you wish to call to RM Insurer?"}
                        show={true}
                      />
                    )}

                    {(this.state.isLoaded &&
                      <DataTable
                        columns={columns}
                        data={(!this.state.IsRMInsurer) ? this.state.AgentData : this.state.items}
                        extention={false}
                        pagination={false}
                      />
                    )}
                  </CardBody>
                </Card>
              </Col>
            </Row>
          )
          }
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    addRecord
  }
)(BlockAgent);