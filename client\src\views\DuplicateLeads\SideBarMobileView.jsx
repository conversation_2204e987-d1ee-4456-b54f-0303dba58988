import React, { useState } from 'react';
import { Form } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Offcanvas from 'react-bootstrap/Offcanvas';
import { Col, Row } from 'reactstrap';
import { DateRangeFilter, GetFirstDateOfMonth } from "./Utility";

function SideBarMobileView(props) {
  const { ProductsList, ProductDetails } = props;
  const [show, setShow] = useState(false);
  const [BookingMonth, setBookingMonth] = useState();
  const [ProductSelected, setProductSelected] = useState(null);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  const DateFilter = DateRangeFilter();

  const HandleBookingMonthChange = (e) => {
    setBookingMonth(e.target.value);
  }

  const HandleProductSelectionChange = (e) => {
    setProductSelected(e.target.value);
  }

  const HandleSaveFilter = () => {
    props.handleBookingMonthMobile(BookingMonth);
    props.handleProductChangeMobile(ProductSelected);
    setShow(false);
  }

  return (
    <div className="sidebarMobileView">
      <Button variant="primary" className="dropdownBtn" onClick={handleShow}>
        Filter
      </Button>

      <Offcanvas show={show} onHide={handleClose} backdrop="static" placement="end">
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Filters</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <div className="FilterSelection">

            <Row>
              <Col md={12}>
                <label>Period Selection</label>
                <Form.Select aria-label="Default select"
                  value={BookingMonth}
                  onChange={HandleBookingMonthChange}>
                  {
                    DateFilter && DateFilter.length > 0 &&
                    DateFilter.map((item, index) => {
                      return <option key={index} value={item.value}>{item.month + "-" + item.year}</option>
                    })
                  }
                </Form.Select>
              </Col>
              <Col md={12}>
                  <label>Product</label>
                  <Form.Select aria-label="Default select" onChange={HandleProductSelectionChange} value={ProductSelected} >
                    {
                      ProductsList && ProductsList.length > 0 &&
                      ProductsList.map((item, index) => {
                        return <option key={index} value={JSON.stringify(item)}>{item.Display}</option>
                      })
                    }
                  </Form.Select>
                </Col>
              {/* <Col md={12}>
                <label>Select TL/Supervisor</label>
                <Form.Select aria-label="Default select">
                  <option value="1">0 - 7 Days</option>
                  <option value="2">Two</option>
                  <option value="3">Three</option>
                </Form.Select>
              </Col> */}
              {/* <Col md={12}>
                <label>Select Agent</label>
                <Form.Select aria-label="Default select">
                  {
                      MyAgents && MyAgents.length > 0 &&
                      MyAgents.map(item => {
                        return <option value={item.UserId}>{item.EmployeeId + " " + item.UserName}</option>
                      })
                    }
                  <option value="0">hfddjfh</option>
                </Form.Select>
              </Col> */}
            </Row>
          </div>
          {/* <Row> */}
            {/* <Col> */}
              <div className="filterFooter">
                <button className="cancelBtn" onClick={handleClose}>Cancel</button>
                <button className="save" onClick={HandleSaveFilter}>Save</button>
              </div>
            {/* </Col> */}
          {/* </Row> */}
        </Offcanvas.Body>
      </Offcanvas>
    </div>

  );
}
export default SideBarMobileView
