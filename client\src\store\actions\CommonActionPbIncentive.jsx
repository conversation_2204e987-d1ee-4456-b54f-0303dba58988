import axios from "axios";
import config from "../../config.jsx";
import constants from "../../Constants/Constants.jsx";

axios.interceptors.response.use(response => {
  return response;
}, error => {
 return error;
});

const GetCommonData_fetched = Todos => {

  if (Todos.dataToSend.state && Todos.dataToSend.statename) {
    localStorage.setItem(Todos.dataToSend.statename, JSON.stringify(Todos.data));
  }

  return {
    type: constants.GET_COMMON_SUCCESS,
    payload: Todos.data,
    root: Todos.dataToSend.statename ?? Todos.dataToSend.root
  };
};

const GetCommonData_fetch_error = error => {
  return {
    type: constants.GET_COMMON_FAIL,
    payload: error
  };
};

export const GetCommonData = (dataToSend, cb) => {
  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }
  try {
    if ((dataToSend.state && localStorage.getItem(dataToSend.statename)) || localStorage.getItem(dataToSend.statename)) {

      return function (dispatch, getState) {
        let data = JSON.parse(localStorage.getItem(dataToSend.statename));
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
        
      }
    }
  }
  catch (e) {

  }


  return function (dispatch, getState) {
    axios
      .get(config.api.base_url + "/PBIncentive/list/", {
        params: dataToSend
      })
      .then(data => {
        if (dataToSend.state && dataToSend.statename) {
          localStorage.setItem(dataToSend.statename, JSON.stringify(data.data.data));
        }
        if (cb) {
          cb(data && data.data && data.data.data);
        }
        dispatch(GetCommonData_fetched({data: data && data.data && data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetCommonspData = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    debugger;
    axios
      .get(config.api.base_url + "/PBIncentive/listsp/", {
        params: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};

export const GetCommonspDataV2 = (dataToSend, cb) => {
  try {
    let endpoint = dataToSend.root || '';
    if (dataToSend.data != null || dataToSend.data != undefined) {
      return function (dispatch, getState) {
        dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
      };
    }
  
    return function (dispatch, getState) {
      axios
        .get(config.api.base_url + "/PBIncentive/listspV2/" + endpoint, {
          params: dataToSend
        })
        .then(data => {
          if (cb) {
            cb(data);
          }
          dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
        })
        .catch(error => {
          dispatch(GetCommonData_fetch_error(error, dataToSend));
        });
    };
  } catch (err) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetch_error(err, dataToSend));
    };
  }
};

const InsertData_fetched = Todos => {

  return {
    type: constants.INSERT_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const InsertData_fetch_error = error => {
  return {
    type: constants.INSERT_COMMON_FAIL,
    payload: error
  };
};

export const InsertData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/PBIncentive/insert/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(InsertData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(InsertData_fetch_error(error));
      });
  };
};


const UpdateData_fetched = Todos => {

  return {
    type: constants.UPDATE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const UpdateData_fetch_error = error => {
  return {
    type: constants.UPDATE_COMMON_FAIL,
    payload: error
  };
};

export const UpdateData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/PBIncentive/update/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(UpdateData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(UpdateData_fetch_error(error));
      });
  };
};

export const UpdateDataV2 = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/PBIncentive/updatev2/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(UpdateData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(UpdateData_fetch_error(error));
      });
  };
};

const DeleteData_fetched = Todos => {

  return {
    type: constants.DELETE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const DeleteData_fetch_error = error => {
  return {
    type: constants.DELETE_COMMON_FAIL,
    payload: error
  };
};

export const DeleteData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/PBIncentive/delete", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(DeleteData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(DeleteData_fetch_error(error));
      });
  };
};


export const GetDataDirect = (dataToSend, cb) => {

  try {
    if ((dataToSend.state && localStorage.getItem(dataToSend.statename)) || localStorage.getItem(dataToSend.statename)) {

      let data = JSON.parse(localStorage.getItem(dataToSend.statename));
      cb(data);
      return;
    }
  }
  catch (e) {

  }
  //return function (dispatch, getState) {
  axios
    .get(config.api.base_url + "/PBIncentive/list/", {
      params: dataToSend
    })
    .then(data => {
      if (dataToSend.state && dataToSend.statename) {
        localStorage.setItem(dataToSend.statename, JSON.stringify(data.data.data));
      }
      cb(data.data.data);

      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};


export const listsp = (dataToSend, cb, method = 'get') => {
  let url = config.api.base_url + "/PBIncentive/listsp/";
  if (method === 'post') {
    axios
      .post(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  } else {
    axios
      .get(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  }

};

export const ContestMapping = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/PBIncentive/ContestMapping";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};
