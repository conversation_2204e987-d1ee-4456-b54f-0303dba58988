.Backdrop {
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.7;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  position: fixed;
  z-index: 9;
}

.flexbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.bt-spinner {
  margin-left:-50px;
  margin-top: -50px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: transparent;
  border: 4px solid #222;
  border-top-color: #009688;
  -webkit-animation: 1s spin linear infinite;
  animation: 1s spin linear infinite;
  position: absolute;
  top:50%;
  left:50%;
}
@-webkit-keyframes spin {
  -webkit-from {
      -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
      transform: rotate(0deg);
  }

  -webkit-to {
      -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
      transform: rotate(360deg);
  }
}

@-webkit-keyframes spin {
  from {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
  }

  to {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
  }
}

@keyframes spin {
  from {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
  }

  to {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
  }
}