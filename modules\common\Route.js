const express = require("express");
const router = express.Router();

const CommonController = require('./CommonController');
const {ACLlayer} = require("../ACLlayer");

router.get('/GetCommonApi/:root',CommonController.GetCommonApi);
router.post('/PostCommonApi/:root',CommonController.PostCommonApi);
router.post('/UpdateHierarchy', CommonController.UpdateHierarchy);
router.post('/PostCommonApiV2/:root',CommonController.PostCommonApiV2);
router.get('/FetchProcTableWithMenu' , CommonController.FetchProcTableWithMenu);
router.post('/EditMenuEntityMapping' ,CommonController.EditMenuEntityMapping);
router.get('/AutoDebitPrompt',CommonController.AutoDebitPrompt);

module.exports = router;
