import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { OpenSalesView, getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import './customStyling.css';
//import DateRange from "./Common/DateRange"
import DropDown from './Common/DropDown';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import moment from "moment";
import { If, Then, Else } from 'react-if';
import ProductList from "./SelectList/ProductList"
import Datetime from 'react-datetime';
import Date from "./Common/Date"
import 'react-datetime/css/react-datetime.css'

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col,
  FormGroup,
  Input,
} from "reactstrap";


class RejectedLeadsList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Rejected Leads",
      RejectedLeads: [],
      UserGroupsList: [],
      IsLoading: false,
      StartDate: moment().subtract(1, 'months').format("YYYY-MM-DD"),
      F_FromDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
      F_ToDate: moment().format("YYYY-MM-DD"),
      F_RejectedDate: moment().format("YYYY-MM-DD"),
      F_ProductId:"",
      F_GroupId:0,
      F_TalktimeValue:10,
      //F_LeadId:0,
    };
    this.dtRef = React.createRef();
    this.AgentType = null;
    this.dateRangeRef = React.createRef();

    this.TalkTimeList = [ 
      //{ Id: 1, Display: "Under 5 Min" },
      { Id: 5, Display: "Above 5 Min" },
      { Id: 10, Display: "Above 10 Min" },
      { Id: 15, Display: "Above 15 Min" },
      { Id: 20, Display: "Above 20 Min" },
      { Id: 30, Display: "Above 30 Min" },
      { Id: 60, Display: "Above 60 Min" }
    ];
    
    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadId", 
        searchable: false,
        cell: row => <a className="pointer" href={OpenSalesView(row.CustomerId, row.LeadId, this.state.F_ProductId)} target="_blank">{row.LeadId}</a>
      },
      {
        name: "Customer Name",
        selector: "CustomerName",  
      },
      {
        name: "Lead Created Date",
        selector: "CreatedOn",
        //sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.CreatedOn}</Moment>
      },
      {
        name: "Lead Rejected Date",
        selector: "RejectedOn",
        //sortable: true,
        cell: row => <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.RejectedOn}</Moment>
      },
      {
        name: "Rejection Reason",
        selector: "SubStatusName",  
      },
      {
        name: "Talktime (H:M:S)",
        selector: "Talktime",  
        cell: row => row.Talktime > 0 ? moment.duration(row.Talktime, 'seconds').format("hh:mm:ss", { trim: false }) : '00:00:00'
      }
    ];
  }
  
  componentWillReceiveProps(nextProps) {
    
    if (!nextProps.CommonData.isError) {
      if (nextProps.CommonData["GetAllRejectedLeads"]) {
        let CBList = nextProps.CommonData["GetAllRejectedLeads"];
        this.setState({ RejectedLeads: CBList, IsLoading: false });
      }

    }
  }
  
  componentDidMount() {
    //this.fetchCallBackData(0);
    this.getUserGroups();
  }
  
  getUserGroups(productId) {
    let that = this;
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetUniqueGroups",
      params: [{
        AgentId: getuser().UserID,
        ProductId: productId != undefined ? productId : this.state.F_ProductId
      }]
    }, function(result) {
      that.setState({ UserGroupsList : result.data.data[0] || [] });
    });
  }

  fetchCallBackData() {    
    let dtrange = this.getSelectedDateRange();
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetAllRejectedLeads",
      params: [{
        UserId:getuser().UserID,
        //FromDate: dtrange.startdate,
        //ToDate: dtrange.enddate,
        ProductId: this.state.F_ProductId,
        TalktimeValue: this.state.F_TalktimeValue,
        //LeadId: this.state.F_LeadId,
        RejectedDate: this.state.F_RejectedDate,
        GroupId: this.state.F_GroupId,
      }]
    });

  }

  handleStartDateChange = (e, props) => {
      if (e._isAMomentObject) {
          this.setState({ F_RejectedDate: e.format("YYYY-MM-DD"), F_FromDate: e.format("YYYY-MM-DD"), F_ToDate: e.add(1, 'days').format("YYYY-MM-DD") }, function () {
          });
      }
  };

  handleEndDateChange = (e, props) => {
      if (e._isAMomentObject) {
          this.setState({ F_ToDate: e.format("YYYY-MM-DD"), F_FromDate: e.add(-1, 'days').format("YYYY-MM-DD") }, function () {
              //this.fetchCallBackData();
          });
      }
  };

  validation = (currentDate) => {
      return currentDate.isBefore(moment()) && currentDate.isAfter(moment(this.state.StartDate));
  };

  validationEndDate = (currentDate) => {
      
      if (!currentDate.isBefore(moment(this.state.F_ToDate))) {
          return false;
      }

      if(currentDate.isBefore(moment(this.state.F_FromDate))) {
          return false;
      }
      
      return true;
      
  };

  getSelectedDateRange() {
      return {
          startdate: this.state.F_FromDate,
          enddate: this.state.F_ToDate
      }
  }

  productchange = (e) => {
    this.setState({
      F_ProductId: e.target.value,
      UserGroupsList : [],
      F_GroupId: 0
    });
    this.getUserGroups(e.target.value);
  };

  groupChange = (e) => {
    this.setState({
      F_GroupId: e.target.value
    });
  };

  /*handleLeadIdChange = (e) => {
    this.setState({ F_LeadId: e.target.value });
  };*/

  talktimeFilterchange(e){
    if (e.target.value && parseInt(e.target.value) > 0) {
      this.setState({
        F_TalktimeValue: e.target.value
      });
    } else {
      toast.error("Talktime field is required");
      return;
    }
  };

  /*openSalesviewPage(customerId, leadId){
    var encrytedvalue = btoa(customerId + '/' + this.state.F_ProductId + '/' + leadId + '/o');
    window.open(config.api.progressiveUrl+'SV/LeadContent.htm#/Sales/' + encrytedvalue,"_blank");
  };*/

  submitFilters(e){

    /*if(this.state.F_FromDate === "" || this.state.F_ToDate === "") {
      toast.error("Select valid date"); 
      return;
    }*/

    if(this.state.RejectedDate === "") {
      toast.error("Select rejection date"); 
      return;
    }

    if(this.state.F_ProductId === "") {
      toast.error("Select a product"); 
      return;
    }


    this.setState({
      IsLoading: true, 
      RejectedLeads: []
    });
    this.fetchCallBackData(e.target.value);
  };

  render() {
    const columns = this.columnlist; 
    const { PageTitle, RejectedLeads, showAlert, AlertMsg, AlertVarient, UserGroupsList } = this.state;

    return (
      <>
        <div className="content noExportButton">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={2}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={3}>
                      <Row>
                        <Col md={12}>
                          <Form.Group controlId="startdate_field">
                              <Form.Label>Rejection Date :</Form.Label>
                              <Datetime value={new Date()}
                                  dateFormat="YYYY-MM-DD"
                                  value={this.state.F_RejectedDate}
                                  isValidDate={this.validation.bind(this)}
                                  onChange={moment => this.handleStartDateChange(moment)}
                                  utc={true}
                                  timeFormat={false}
                                  className="form-group"
                              />
                          </Form.Group>
                        </Col>
                        {/*
                        <DateRange ref={this.dateRangeRef} onStartDate={this.handleStartDate.bind(this)} onEndDate={this.handleEndDate.bind(this)}>
                        </DateRange>*/}
                        {/*<Col md={6}>
                            <Form.Group controlId="startdate_field">
                                <Form.Label>From Date :</Form.Label>
                                <Datetime value={new Date()}
                                    dateFormat="YYYY-MM-DD"
                                    value={this.state.F_FromDate}
                                    isValidDate={this.validation.bind(this)}
                                    onChange={moment => this.handleStartDateChange(moment)}
                                    utc={true}
                                    timeFormat={false}
                                    className="form-group"
                                />
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group controlId="enddate_field">
                                <Form.Label>To Date :</Form.Label>
                                <Datetime value={new Date()}
                                    dateFormat="YYYY-MM-DD"
                                    value={this.state.F_ToDate}
                                    isValidDate={this.validationEndDate.bind(this)}
                                    onChange={moment => this.handleEndDateChange(moment)}
                                    utc={true}
                                    timeFormat={false}
                                    className="form-group right0"
                                    readOnly={true}
                                />
                            </Form.Group>
                        </Col>
                        */}

                        <Col md={12}>
                          <small><strong>Please Note</strong>: You can only check for Rejected Leads which were created in last month.</small>
                        </Col>

                      </Row>
                    </Col>
                    <Col md={3}>
                      <div className="form-group">                      
                        <label className="form-label">Product :</label>
                          <ProductList firstoption="Select" productchange={this.productchange} >
                          </ProductList>
                      </div>
                    </Col>
                    <Col md={3}>
                      <div className="form-group">
                        <label className="form-label">User Group :</label>
                        <Form.Control as="select" value={this.state.F_GroupId} onChange={(e) => this.groupChange(e) }>
                          <option key={0} value={0}>Select Group</option>
                          {UserGroupsList.map(item => (
                              <option key={item.groupId} value={item.groupId}>{item.GroupName}</option>
                          ))}
                        </Form.Control>
                      </div>
                    </Col>
                    <Col md={3}>
                      <div className="form-group">                      
                        <label className="form-label">Talktime :</label>
                        <DropDown firstoption={false} value={this.state.F_TalktimeValue} items={this.TalkTimeList} onChange={(e) => this.talktimeFilterchange(e)}></DropDown>
                      </div>
                    </Col>
                    {/*<Col md={2}>
                      <FormGroup>
                        <label>LeadId :</label>
                        <Input
                          defaultValue=""
                          placeholder=""
                          type="text"
                          onBlur={(e) => this.handleLeadIdChange(e)}
                        />
                      </FormGroup>
                    </Col>*/}
                    <Col md={2} className="py-4">
                      <ButtonGroup>
                        <Button onClick={(e) => this.submitFilters(e)}>Submit</Button>
                      </ButtonGroup>
                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                <If condition={this.state.IsLoading}>
                <Then>
                  <i class="fa fa-spinner fa-spin"></i>
                </Then>
                <Else>
                <DataTable
                    columns={columns}
                    data={(RejectedLeads && RejectedLeads.length > 0) ? RejectedLeads[0] : []}
                    defaultSortField="RejectedOn"
                    defaultSortAsc={false}
                    export={false}
                    //selectableRows={true}
                    //ref={this.dtRef}
                    //onSelectedRows={this.onSelectedRows.bind(this)}
                  />
                </Else>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(RejectedLeadsList);