import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import Moment from 'react-moment';
//import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer } from 'react-toastify';
import './customStyling.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { Button, Modal, Form } from 'react-bootstrap';
import moment from "moment";
import { If, Then, Else } from 'react-if';
import DropDownList from "./Common/DropDownList";
import DateRange from "./Common/DateRange"
import FOSSelfieDetails from "./FOS/FOSSelfieDetails";

class FOSSelfiePanel extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "FOS Selfie Panel",
      FOSSelfieData: [],
      IsLoading: false,
      StartDate: moment().subtract(7, 'days').format("YYYY-MM-DD"),
      EndDate: moment().format("YYYY-MM-DD 23:59:59"),
      clickImage: false,
      clickModalUrl: null,
      productId: null,
      appointmentId: null,
      statusId: null,
      row: [],
      // SelectedSupervisors: [],
      // SelectedRows: [],
      items: {},
      reviewStatus: 0,
      substatus: -1,
    };
    this.onReviewStatus = this.onReviewStatus.bind(this);
    this.onStatus = this.onStatus.bind(this);
    //this.handleShow = this.handleShow.bind(this);
    this.dtRef = React.createRef();
    this.dateRangeRef = React.createRef();


    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 24) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );
    this.columnlist = [
      {
        name: "LeadId",
        selector: "LeadID",
      },
      {
        name: "AppointmentDateTime",
        selector: "Appointment_Datetime",
        sortable: true,
        type: "datetime",
        cell: row => (row.Appointment_Datetime == null) ? 'N/A' : <Moment utc={true} format="MMM D YYYY h:mma">{row.Appointment_Datetime}</Moment>,
        format: 'MMM D YYYY h:mma',
      },
      {
        name: "AppointmentId",
        selector: "AppointmentId"
      },
      {
        name: "Product",
        selector: "ProductName",
      },
      {
        name: "OTPVerifiedDistance",
        selector: "Distance",
        cell: row => (row.Distance == null) ? 'N/A' : row.Distance + ' KM'
      },
      {
        name: "ApptCompletedDistance",
        selector: "ApptCompletedDistance",
        cell: row => (row.ApptCompletedDistance == null) ? 'N/A' : row.ApptCompletedDistance + ' KM'
      },
      {
        name: "ApptCompletedOn",
        selector: "ApptCompletedOn",
        sortable: true,
        //type: "datetime",
        cell: row => (row.ApptCompletedOn == null || row.ApptCompletedOn == '1990-01-01 00:00:00') ? 'N/A' : row.ApptCompletedOn,
        excelValue: "FosApptCompletedOn"
      },
      {
        name: "ApptCompletionRemarks",
        selector: "LastComment",
        sortable: true,
        //type: "datetime",
        cell: row => (row.LastComment) ? <Cell v={row.LastComment} /> : 'N.A',
        excelValue: "LastComment",
        width: "180px",
      },
      {
        name: "Status",
        selector: "Status",
        cell: row => this.getStatus(row),
        sortable: false,
        width: "60px",
        excelValue: "FosSelfieStatus"
      },
      {
        name: "Agent Details",
        selector: "AgentName",
        cell: row => <Cell v={row.AgentName} />,
        width: "180px",
        sortable: true,
        searchable: true
      },
      {
        name: "Agent Process",
        selector: "ProcessName",
        cell: row => <Cell v={row.ProcessName?row.ProcessName:"-"} />,
        width: "90px",
        sortable: true,
      },
      {
        name: "1st Reporting",
        selector: "IstReporting",
        cell: row => <Cell v={row.IstReporting} />,
        width: "200px",
        sortable: true,
        searchable: true
      },
      {
        name: "2nd Reporting",
        selector: "SecondReporting",
        cell: row => <Cell v={row.SecondReporting} />,
        sortable: true,
        width: "200px",
        searchable: true
      },
      {
        name: "Image Upload Time",
        selector: "ImageUploadTime",
        sortable: true,
        type: "datetime",
        cell: row => (row.ImageUploadTime == null) ? 'N/A' : <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">{row.ImageUploadTime}</Moment>,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      {
        name: "Approver's Details",
        selector: "ApproverDetails",
        cell: row => <Cell v={row.ApproverDetails} />,
        width: "200px",
        sortable: true,
      },
    
      {
        name: "Image Link",
        selector: row => row.URL == '' ? 'No Image Found' : "",
        cell: row => (row.URL) ?
          <div className="listenUserDetails">
            <Button onClick={(e) => this.getImageLink(e, row)} variant="primary">View Image</Button>
          </div> : 'No Image Found',
        editable: false,
        addable: false,
        hideonmodal: true,

      },
    ];



  }

  getSlot(row) {
    return row.StartTime + ' to ' + row.EndTime
  }

  getStatus(row) {
    if (row.Status == 0)
      return 'Fail'
    else if (row.Status == 1)
      return 'Pass'
    else
      return 'N.A'
  }

  getImageLink(e, row) {
    this.setState({ clickImage: true, clickModalUrl: row.URL, appointmentId: row.AppointmentId, statusId: row.Status, row: row });
  }

  handleStartDate = (StartDateValue) => {
    this.setState({ StartDate: StartDateValue });
    const date = new Date()
    const startDate = new Date(StartDateValue)

    startDate.setDate(startDate.getDate() + 7);
    if (date - startDate <= 0) {
      this.setState({ EndDate: moment().format("YYYY-MM-DD 23:59:59") });
    }
    else {
      this.setState({ EndDate: moment(StartDateValue).add(7, 'days').format("YYYY-MM-DD 23:59:59") });
    }
  }

  handleEndDate = (EndDateValue) => {
    this.setState({ EndDate: EndDateValue });
  }

  componentDidMount() {
    // this.fetchSelfieData();
    var url_string = document.location;
    var url = new URL(url_string);
    var c = url.searchParams.get("mode");
    console.log(c);
    this.view = c

  }


  fetchSelfieData() {
    this.setState({ IsLoading: true })
    this.props.GetCommonspData({
      limit: 10,
      skip: 0,
      root: "GetFOSSelfieData",
      // ManagerIds: SelectedSupervisors, 
      params: [{ ProductId: this.state.productId }, { fromdate: this.state.StartDate },
      { todate: this.state.EndDate }, { reviewStatus: this.state.reviewStatus }, { substatus: this.state.substatus }]
    }, function (result) {
      if (result && result.data && result.data.data) {
            let item = result.data.data[0];       
            const appointmentsWithStatus = item.filter(appointment => appointment.Status !== null);
            const appointmentsWithoutStatus = item.filter(appointment => appointment.Status === null);
          
            const sortedAppointmentsWithoutStatus = [...appointmentsWithoutStatus].sort((a, b) => b.AppointmentId - a.AppointmentId);
            const sortedAppointmentsWithStatus = [...appointmentsWithStatus].sort((a, b) => b.AppointmentId - a.AppointmentId);
            const totalAppts = sortedAppointmentsWithoutStatus.concat(sortedAppointmentsWithStatus);

            this.setState({ FOSSelfieData: totalAppts, IsLoading: false });
      }
    }.bind(this));

  }


  ProductChange(e) {
    this.setState({ productId: e.target.value })
  }


  handleStartDateChange = (e, props) => {
    if (e._isAMomentObject) {
      this.setState({ StartDate: e.format("YYYY-MM-DD") }, function () {
      });
    }
  }

  handleEndDateChange = (e, props) => {
    if (e._isAMomentObject) {
      this.setState({ EndDate: e.format("YYYY-MM-DD") }, function () {
      });
    }
  }

  handleOnCloseImage() {
    this.setState({ clickImage: false });
  }

  handleOnSubmitResponse(){
    this.setState({ clickImage: false });
    this.fetchSelfieData();
  }

  // handleShow(e) {

  //   this.setState({ SelectedSupervisors: e.SelectedSupervisors });
  //   setTimeout(function () {
  //     this.fetchSelfieData();
  //   }.bind(this), 500);

  // }

  onReviewStatus(e) {
    this.setState({ reviewStatus: e.target.value })
  }

  onStatus(e) {
    this.setState({ substatus: e.target.value })
  }

  render() {
    const columns = this.columnlist;
    console.log('columns', columns);
    const {  PageTitle, FOSSelfieData, showAlert, AlertMsg, AlertVarient } = this.state;

    return (
      <>
        <div className="content TLPodLeadsContainer">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={2}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    {/* <Col md={2}>
                      <CardTitle tag="h6">
                      </CardTitle>
                    </Col> */}
                  </Row>

                  <Row>

                    <Col md={4}>
                      <Form.Group>
                        <Form.Label>Select Product <span>*</span></Form.Label>
                        <DropDownList
                          visible={true}
                          valueTobefiltered={[2, 115, 7, 117]}
                          col={{
                            name: "ProductId",
                            label: "ProductId",
                            type: "dropdown",
                            config: {
                              root: "Products",
                              cols: ["ID AS Id", "ProductName AS Display"],
                              con: [{ "Isactive": 1 }],
                            },
                          }}
                          filterkey={null}
                          filtervalue={null}
                          onChange={this.ProductChange.bind(this)}
                          required={true}
                        />
                      </Form.Group>
                    </Col>
                    <DateRange days={7} FromDate={"Start Date "} ToDate={"End Date "}
                      startDate={this.state.StartDate} endDate={this.state.EndDate} ref={this.dateRangeRef} onStartDate={this.handleStartDate} onEndDate={this.handleEndDate}
                      EndDateFixed={true} LimitStartdate={30}>
                    </DateRange>

                  </Row>
                  <Row>
                    <Col md={4}>
                      <Form.Label>Review Status </Form.Label>
                      <Form.Select class="form-select" as="select" value={this.state.reviewStatus} onChange={this.onReviewStatus}>
                        <option value="0">Select</option>
                        <option value="1">Approved</option>
                        <option value="2">Not Approved</option>
                        <option value="3">No Image found</option>
                      </Form.Select>
                    </Col>

                    {this.state.reviewStatus === "1" &&
                      <Col md={4}>
                        <Form.Label>Sub Status </Form.Label>

                        <Form.Select as="select" value={this.state.substatus} onChange={this.onStatus} >
                          <option value="-1">Select</option>
                          <option value="0">Fail</option>
                          <option value="1">Pass</option>
                        </Form.Select>
                      </Col>
                    }

                    <Col md={1}>
                      <Form.Label>Fetch </Form.Label>
                      <Button variant="primary" onClick={() => this.fetchSelfieData()}>Fetch</Button>
                    </Col>
                  </Row>

                  {/* <ManagerHierarchy
                    handleShow={this.handleShow} value={/UserID/g}
                  >
                  </ManagerHierarchy> */}

                </CardHeader>

                <CardBody>

                  <If condition={this.state.IsLoading}>
                    <Then>
                      <i class="fa fa-spinner fa-spin"></i>
                    </Then>
                    <Else>
                      <DataTable
                        columns={columns}
                        data={(FOSSelfieData && FOSSelfieData.length > 0) ? FOSSelfieData : []}
                        defaultSortAsc={false}
                        export={false}
                        fileName={"FOS Selfie Data"}
                      />
                    </Else>
                  </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={this.state.clickImage} onHide={() => this.setState({ clickImage: false })} dialogClassName="modal-98w fosSelfiePopop">
            {/* <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header> */}
            <img src="/close.svg" className="closeBtn" onClick={this.handleOnCloseImage.bind(this)} alt=""/>
            <Modal.Body className="viewImage">
              <FOSSelfieDetails row={this.state.row} URL={this.state.clickModalUrl} EmployeeId={this.state.row.EmployeeId} UserName={this.state.row.UserName} LeadID={this.state.row.LeadID}
               statusId={this.state.statusId} appointmentId={this.state.appointmentId} view={this.view} onSubmitResponse={this.handleOnSubmitResponse.bind(this)} source='fosselfie'             
               />
            </Modal.Body>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(FOSSelfiePanel);