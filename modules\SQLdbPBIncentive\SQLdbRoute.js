var express = require("express");
const router = express.Router();
const controller = require("./SQLdbController");

router.post("/insert/*", controller.insert);
router.post("/update/*", controller.update);
router.post("/updatev2/*", controller.updatev2);

router.get("/list/*", controller.getdata);
router.post("/delete", controller.delete);

router.get("/listsp/*", controller.getdatasp);
router.get("/listspV2/:root*", controller.getdataspV2);
router.post("/ContestMapping", controller.ContestMapping);
module.exports = router;
