
import React from "react";
import { Form } from 'react-bootstrap';

import {
    GetCommonData, GetCommonspData, GetDataDirect, GetExportQuiz
} from "../../store/actions/CommonAction";
import { getUrlParameter, getuser } from '../../utility/utility.jsx';


import { Row, Col } from 'react-bootstrap';
import 'react-checkbox-tree/lib/react-checkbox-tree.css';
import CheckboxTree from 'react-checkbox-tree';

import ProductList from "../SelectList/ProductList"
import CourseList from "../SelectList/CourseList"
import QuizList from "../SelectList/QuizList"
import DropDown from "../Common/DropDown"
import DateRange from "../Common/DateRange"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';



class MoodleManagerHierarchy extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            nodes: [],
            checked: [],
            expanded: [],
            ReportType: 1,
            ProductId: null,
            QuizSelectList: [],
            ExportQuizData: [],
            CourseSelect: '',
            StartDate: moment().subtract(30, 'days').format("YYYY-MM-DD"),
            EndDate: moment().format("YYYY-MM-DD"),

        };
        this.reporttypechange = this.reporttypechange.bind(this);

        this.ReportTypeList = {
            config:
            {
                root: "ReportTypeList",
                data: [{ Id: 1, Display: "Quiz Report" }, { Id: 2, Display: "Course Report" }, { Id: 3, Display: "LMS Visit" }],
            }
        };

        this.productchange = this.productchange.bind(this);
        this.onSummaryClick = this.onSummaryClick.bind(this);
        this.dateRangeRef = React.createRef();
    }
    componentDidMount() {
        let UserId = getuser().RoleId == 2 ? 75 : getuser().UserID;

        if(UserId)
        {
        GetDataDirect({
            root: "Hierarchy",
            ManagerId: UserId,
            statename: "Hierarchy-" + UserId,
            Hierarchy : 1,
            value: this.props.value,
            state: true
        }, function (result) {

            let str = JSON.stringify(result);
            var res = str.replace(/UserName/g, "label");
            res = res.replace(this.props.value, "value");

            this.setState({ nodes: JSON.parse(res) });
        }.bind(this));
    }
    }
    componentWillReceiveProps(nextProps) {//debugger;
        console.log(this.props.QuizData);
        console.log(nextProps.QuizData);
    }


    onButtonClick() {


        this.props.handleSelectManager({
            SelectedSupervisors: this.state.checked
        });
        this.forceUpdate();
    }
    onMouseEnter(e) {
        document.getElementById("floating").style.right = 0
        document.getElementById("handle").style.right = "395px"
        //e.target.style.right = 0;
    }
    onMouseOut(e) {

        setTimeout(function () {
            document.getElementById("floating").style.right = "-400px"
            document.getElementById("handle").style.right = "-15px"
        }, 700);
    }
    RemoveChecked(checkeditem) {
        const { checked } = this.state;
        let index = checked.indexOf(checkeditem);
        if (index > -1) {
            checked.splice(index, 1);
        }
        this.setState(checked);
    }

    reporttypechange(e) {
        this.setState({ ReportType: e.target.value });
    }
    productchange(e, props) {
        console.log("ProductId", e.target.value);
        this.props.onProductId(e.target.value);
        this.setState({
            ProductId: e.target.value
        }, function () {
            //this.fetchCourseData();
        });
    }


    onExportClick() {
        debugger;

        if (this.state.checked.length > 0) {
            GetDataDirect({
                root: "GetAgentsUnderManagers",
                ManagerId: this.state.checked.join(),
            }, function (result) {
                this.setState({
                    SelectedSupervisors: this.state.checked,
                    SelectedUsers: result
                });
                this.props.handleSelectManager({
                    SelectedSupervisors: this.state.checked,
                    SelectedUsers: result
                });
                if (this.state.ReportType == 1) {
                    if(this.state.ProductId == null){
                    toast("Please select Product", { type: 'error' });
                    return;  
                    }
                    if (this.state.QuizSelectList == '') {
                    toast("Please select Quiz", { type: 'error' });
                    return;
                    }
                    this.props.onExportQuiz(this.state.QuizSelectList);
                }
                if (this.state.ReportType == 2) {
                    if(this.state.ProductId == null){
                    toast("Please select Product", { type: 'error' });
                    return;  
                    }
                    if (this.state.CourseSelect == '') {
                    toast("Please select Course", { type: 'error' });
                    return;
                    }
                    this.props.onExportCourse(this.state.CourseSelect, this.state.StartDate, this.state.EndDate);
                }
                if (this.state.ReportType == 3) {
                    let dtrange = this.dateRangeRef.current.getSelectedDateRange()
                    this.props.onExportLMS(dtrange.startdate, dtrange.enddate);
                }
            }.bind(this));
        }
        else {
            this.setState({
                SelectedSupervisors: [],
                SelectedUsers: []
            });
            this.props.handleSelectManager({
                SelectedSupervisors: [],
                SelectedUsers: []
            });
            if (this.state.ReportType == 1) {
                if(this.state.ProductId == null){
                toast("Please select Product", { type: 'error' });
                return;  
                }
                if (this.state.QuizSelectList == '') {
                toast("Please select Quiz", { type: 'error' });
                return;
                }
                this.props.onExportQuiz(this.state.QuizSelectList);
            }
            if (this.state.ReportType == 2) {
                if(this.state.ProductId == null){
                toast("Please select Product", { type: 'error' });
                return;  
                }
                if (this.state.CourseSelect == '') {
                toast("Please select Course", { type: 'error' });
                return;
                }
                this.props.onExportCourse(this.state.CourseSelect, this.state.StartDate, this.state.EndDate);
            }
            if (this.state.ReportType == 3) {
                let dtrange = this.dateRangeRef.current.getSelectedDateRange()
                this.props.onExportLMS(dtrange.startdate, dtrange.enddate);
            }
        }


    }

    onSummaryClick() {

        if (this.state.checked.length > 0) {
            GetDataDirect({
                root: "GetAgentsUnderManagers",
                ManagerId: this.state.checked.join(),
            }, function (result) {
                this.setState({
                    SelectedSupervisors: this.state.checked,
                    SelectedUsers: result
                });
                this.props.handleSelectManager({
                    SelectedSupervisors: this.state.checked,
                    SelectedUsers: result
                });
                if (this.state.ReportType == 1) {
                    if(this.state.ProductId == null){
                    toast("Please select Product", { type: 'error' });
                    return;  
                    }
                    if (this.state.QuizSelectList == '') {
                    toast("Please select Quiz", { type: 'error' });
                    return;
                    }
                    this.props.onSummaryQuiz(this.state.QuizSelectList);
                }
                else if (this.state.ReportType == 2) {
                    if(this.state.ProductId == null){
                    toast("Please select Product", { type: 'error' });
                    return;  
                    }
                    if (this.state.CourseSelect == '') {
                    toast("Please select Course", { type: 'error' });
                    return;
                    }
                    this.props.onSummaryCourse(this.state.CourseSelect, this.state.StartDate, this.state.EndDate);
                }
                else if (this.state.ReportType == 3) {
                    let dtrange = this.dateRangeRef.current.getSelectedDateRange()
                    this.props.onSummaryLMS(dtrange.startdate, dtrange.enddate);
                }

            }.bind(this));
        }
        else {
            this.setState({
                SelectedSupervisors: [],
                SelectedUsers: []
            });
            this.props.handleSelectManager({
                SelectedSupervisors: [],
                SelectedUsers: []
            });
            if (this.state.ReportType == 1) {
                if(this.state.ProductId == null){
                toast("Please select Product", { type: 'error' });
                return;  
                }
                if (this.state.QuizSelectList == '') {
                toast("Please select Quiz", { type: 'error' });
                return;
                }
                this.props.onSummaryQuiz(this.state.QuizSelectList);
            }
            else if (this.state.ReportType == 2) {
                if(this.state.ProductId == null){
                toast("Please select Product", { type: 'error' });
                return;  
                }
                if (this.state.CourseSelect == '') {
                toast("Please select Course", { type: 'error' });
                return;
                }
                this.props.onSummaryCourse(this.state.CourseSelect, this.state.StartDate, this.state.EndDate);
            }
            else if (this.state.ReportType == 3) {
                let dtrange = this.dateRangeRef.current.getSelectedDateRange()
                this.props.onSummaryLMS(dtrange.startdate, dtrange.enddate);
            }
        }



    }

    handleQuizSelect = (QuizValue) => {//debugger;
        this.setState({ QuizSelectList: QuizValue });

        //this.props.onQuizValue(QuizValue);
    }

    handleCourseSelect = (CourseValue) => {//debugger;
        this.setState({ CourseSelect: CourseValue });

    }


    handleStartDate = (StartDateValue) => {
        this.setState({ StartDate: StartDateValue });
    }

    handleEndDate = (EndDateValue) => {
        this.setState({ EndDate: EndDateValue });
    }


    render() {
        const { nodes, checked } = this.state;
        // if (nodes.length == 0) {
        //     return null;
        // }
        return (

            <div id="floating" className="floating" onMouseLeave={this.onMouseOut}>

                <Row>
                    <Col md="4"><div id="handle" className="handle" onClick={this.onMouseEnter}>Filter</div></Col>
                    <Col md="4"><input type="button" className="btn btn-primary" onClick={this.onSummaryClick.bind(this)} value="Summary" /></Col>
                    <Col md="4"><input type="button" className="btn btn-primary" onClick={this.onExportClick.bind(this)} value="Export" /></Col>

                </Row>
                <br />
                <Row>
                    <Col md="6">
                        <Form.Group controlId="protype_dropdown">
                            <Form.Label>Products :</Form.Label>
                            <ProductList productchange={this.productchange} >
                            </ProductList>
                        </Form.Group>
                    </Col>
                    <Col md="6">
                        <Form.Group controlId="reporttype_dropdown">
                            <Form.Label>Report Type :</Form.Label>
                            <DropDown firstoption="Select Report Type" value={this.state.ReportType} items={this.ReportTypeList.config.data} onChange={this.reporttypechange}>
                            </DropDown>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>

                    <DateRange ref={this.dateRangeRef} onStartDate={this.handleStartDate.bind(this)} onEndDate={this.handleEndDate.bind(this)}>
                    </DateRange>

                </Row>
                <Row>
                    <Col md="6">
                        <Form.Group controlId="course_dropdown">
                            <Form.Label>Course :</Form.Label>
                            <CourseList onSelectCourse={this.handleCourseSelect.bind(this)} disabled={this.state.ReportType != 2} ReportType={this.state.ReportType} ProductId={this.state.ProductId} StartDate={this.state.StartDate} EndDate={this.state.EndDate}>
                            </CourseList>
                        </Form.Group>
                    </Col>
                    <Col md="6">
                        <Form.Group controlId="quiz_dropdown">
                            <Form.Label>Quiz :</Form.Label>
                            <div id="quizmultiselect">
                                <QuizList onSelectQuiz={this.handleQuizSelect.bind(this)} disabled={this.state.ReportType != 1} ReportType={this.state.ReportType} ProductId={this.state.ProductId} StartDate={this.state.StartDate} EndDate={this.state.EndDate}>
                                </QuizList>
                            </div>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Form.Group controlId="namagers">
                            <Form.Label>Managers :</Form.Label>
                            <div className="managers">
                                <CheckboxTree
                                    nodes={nodes}
                                    checked={this.state.checked}
                                    expanded={this.state.expanded}
                                    checkModel="all"
                                    name="UserName"
                                    showNodeIcon={false}
                                    onCheck={checked => this.setState({ checked })}
                                    onExpand={expanded => this.setState({ expanded })}
                                    showExpandAll={true}
                                />
                            </div>
                        </Form.Group>
                    </Col>
                </Row>



            </div>

        )
    }
}

export default MoodleManagerHierarchy;
