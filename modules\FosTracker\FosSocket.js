const Utility = require('../../Libs/Utility');
const socket_constants = require('./socket_constants');
const cache= require('memory-cache');
const {StoreSocketId, RemoveSocketId}= require("./MongoSocketMaintenance");

async function ConnectSocket(server) {
  console.log("Here at ConnectSocket")
 
  global.io
    .on("connection", socket => {

      console.log("Socket Server Established");

    // try{
    //   global.io.to('dbwui2XCxIiwIABUAAAF').emit("UpdatedMessage", "newMessage",(ack) => {
    //     if (ack === 'received') {
    //         console.log('Message received by the client');
    //     } else {
    //         console.log('Message delivery failed');
    //     }
    // });
    // }
    // catch(e)
    // {
    //     console.error("error ",e.message);
    // }
      // console.log("Maybe Emitted");

      socket.on('new_user', async data => {
         console.log("socketData: ",data, socket.id);
         await StoreSocketId(data, socket.id);
      })

      socket.on("message",(data)=>{
        // console.log("The msg emitted from the client is ", data, socket.id);
      })

      socket.on(socket_constants.RECONNECT, async data => {
     
        console.log(data, socket.id);
        await StoreSocketId(data, socket.id);
        
      });
   
      socket.on('remove',async (data)=>{
        console.log("Now remove from the cache", data, socket.id);
        await RemoveSocketId(data);
      })


      socket.on('ping',()=>{
        global.io.to(socket.id).emit("pong");
      })

    
      socket.on("disconnect", async() => {
        console.log("The disconnection is happening");
        await RemoveSocketId(null,socket.id);
      });
    });
    
}

module.exports = {
  ConnectSocket: ConnectSocket
}