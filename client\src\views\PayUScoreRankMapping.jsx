
import React, { useEffect, useState } from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { GetCommonData, InsertData, PayUScoreRankFormData, UpdateData } from "../store/actions/CommonAction.jsx";
import { addRecord } from "../store/actions/CommonMongoAction.jsx";
import { If, Then } from 'react-if';
import { connect } from "react-redux";
import DataTableV1 from './Common/DataTableWithFilterV1.js';
import AlertBox from './Common/AlertBox.jsx';
import { fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getAgentId } from '../utility/utility.jsx';

// reactstrap components
import { Card, CardHeader, CardBody, CardTitle, Row, Col } from "reactstrap";
import { Link } from "react-router-dom";


const PayUScoreRankMapping = (props) => {  
    
    const [ state, setState ] = useState({
        showModal: false,
        items: [],
        FormTitle: "",
        formvalue: {},
        event: "",
        ModalValueChanged: false
    });
    const [selectedFile,setselectedFile] = useState(null);
    const [uploadBtn, setUploadBtn] = useState(<></>);
    const downloadedFile ='/SampleExcelfiles/PayUScoreRankMapping.xlsx';
    const selectedrow = { "Id": 0, "IsActive": false, "CreatedOn": new Date() }
    const root = "PayUScoreRankMapping";
    const PageTitle = "PayU Score";

    const columnlist = [
        {
            name: "Id",
            label: "Id",
            type: "hidden",
            hide: true,
        },
        {
            name: "AffluenceScore",
            label: "Affluence Score",
            type: "int"
        },  
        {
            name: "PropensityScore",
            label: "Propensity Score",
            type: "int"
        },
        {
            name: "PbRank",
            label: "Pb Rank",
            type: "number"     
        },
        {
            name: "FinalRank",
            label: "Final Rank",
            type: "number"
        },
        {
            name: "IsActive",
            label: "IsActive",
            type: "bool"
        },
        {
            name: "CreatedOn",
            label: "CreatedOn",
            type: "datetime",
            hide: true,        
        }
    ];

    useEffect(() => {
        columnlist.map(col => (
            fnBindRootData(col, props)
        ));

        props.GetCommonData(    
            {
                limit: 10,
                skip: 0,
                root: root,
                cols: GetJsonToArray(columnlist, "name"),
                c: "L"
            }
        );
    }, []); 

    useEffect(() => {
        setState(prev => ({...prev, items: props.CommonData[root] }));

        if (props.CommonData && props.CommonData.InsertSuccessData && props.CommonData.InsertSuccessData.status) {
            if (props.CommonData.InsertSuccessData.status != 200) {
                setState(prev => ({...prev, showAlert: true, AlertMsg: props.CommonData.InsertSuccessData.error, AlertVarient: "danger" }));
            } else {
                setState(prev => ({...prev, showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" }));
            }
        }
    
        if (props.CommonData && props.CommonData.UpdateSuccessData && props.CommonData.UpdateSuccessData.status) {
            if (props.CommonData.UpdateSuccessData.status != 200)
                setState(prev => ({...prev, showAlert: true, AlertMsg: props.CommonData.UpdateSuccessData.error, AlertVarient: "danger" }));
            else {
                setState(prev => ({...prev, showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" }));
            }
        }

        setTimeout(function () {
            setState(prev => ({...prev, showAlert: false, AlertMsg: "", AlertVarient: "" }));
        }, 2000);
        
    }, [props]);

    const fnDatatableColumn = () => {

        var columns = fnDatatableCol(columnlist);

        columns.push({
            name: "Action",
            cell: row => <ButtonGroup aria-label="Basic example">
                <Button variant="secondary" onClick={() => handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
                <Button variant="secondary" onClick={() => handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
            </ButtonGroup>
        });
        return columns;
    }

    const handleCopy = (row) => {
        setState(prev => ({...prev, formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" }));
    }

    const handleEdit = (row) => {
        setState(prev => ({...prev, od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" }));
    }

    const handleClose = () => {
        setState(prev => ({...prev, showModal: false }));
    }

    const handleShow = () => {
        setState(prev => ({...prev, formvalue: selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" }));
    }

    const handleSave = () => {
        if (document.getElementsByName("frmPayUScoreRankMapping").length > 0 &&
        document.getElementsByName("frmPayUScoreRankMapping")[0].reportValidity()) {

            let formvalue = JSON.parse(JSON.stringify(state.formvalue));
            funcCleanData(formvalue);
            let id = formvalue["Id"];
                delete formvalue["Id"]
            if (state.event === "Edit") {
                funcCleanData(formvalue);

                props.UpdateData({
                    root: root,
                    body: formvalue,
                    querydata: { "Id": id },
                    c: "L"
                });
               
                props.addRecord({
                    root: "History",
                    body: {
                        module: root,
                        od: state.od,
                        nd: formvalue,
                        ts: new Date(),
                        by: getAgentId()
                    }
                });
            } else if (state.event === "Copy") {
                formvalue["CreatedOn"] = new Date();
                funcCleanData(formvalue);

                props.InsertData({
                    root: root,
                    body: formvalue,
                    c: "L"
                });
            } else {
                formvalue["CreatedOn"] = new Date();
                
                props.InsertData({
                    root: root,
                    body: formvalue,
                    c: "L"
                });
            }
            setTimeout(function () {
                props.GetCommonData({
                    root: root,
                    cols: GetJsonToArray(columnlist, "name"),
                    c: "L"
                });
            }, 2000);
            setState(prev => ({...prev, showModal: false }));
        }
        return false;
    }

    const handleChange = (e, props) => {
        let formvalue = state.formvalue;

        if (e.target && e.target.type === "checkbox") {
            formvalue[e.target.id] = e.target.checked;
        }
        else if (e._isAMomentObject) {
            formvalue[props] = e.format()
        }
        else {
            formvalue[e.target.id] = e.target.value;
        }
        setState(prev => ({...prev, formvalue: formvalue, ModalValueChanged: true }));
    }

    const funcCleanData = (formvalue) => {
        formvalue = fnCleanData(columnlist, formvalue);
        setState(prev => ({...prev, formvalue: formvalue }));
    }

    const getType =() => {
        var loc = window.location.href;
        let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);

        lastUrl = lastUrl.split('?');
        return lastUrl[0];    
    }

    const renderDownloadFile = () => {
        if (downloadedFile) {
            return  <Link style={{ fontSize: '14px' }} to={downloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    const onFileChange = (event) => {
        setselectedFile(event.target.files[0])
    };

    const onFileUpload = (e) => {
      
        e.preventDefault();
        if (selectedFile == null) {
            setState(prev => ({...prev, showAlert: true, AlertMsg: "Please choose Excel File",  AlertVarient: "danger" }));
            return;
        }
        const formData = new FormData();

        formData.append( 
            "myFile",
            selectedFile,
            selectedFile.name,
        ); 
        formData.append('type', getType());

        setUploadBtn(<i className="fa fa-spinner fa-spin"></i>);
       
        PayUScoreRankFormData(formData, function (results) {
            document.getElementById('files-upload').value = null;
            if (results && results.data && results.data.data && results.data.data[0] && results.data.data[0].status == 1) {
                setState(prev => ({...prev, showAlert: true, AlertMsg: "File Uploaded Successfully", AlertVarient: "success" }));
                setselectedFile(null);
                setUploadBtn(<></>);

                setTimeout(function () {
                    props.GetCommonData({
                        root: root,
                        cols: GetJsonToArray(columnlist, "name"),
                        c: "L"
                    });
                }, 2000);
                
            } else {
                setselectedFile(null);
                setUploadBtn(<></>);                
                setState(prev => ({...prev, showAlert: true, AlertMsg: results.data.data[0].Message ? results.data.data[0].Message : "File Upload Failed!", AlertVarient: "danger" }));
                return;
            }
        });

        setTimeout(function () {
            setState(prev => ({...prev, showAlert: false, AlertMsg: "", AlertVarient: "" }));
        }, 2000);
    }

    const columns = fnDatatableColumn();
    const { items, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event} = state;

    return (
        <div className="content">
            <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
            <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={11}>
                                    <CardTitle tag="h4">{PageTitle}</CardTitle>
                                </Col>
                                <Col md={1}>
                                    <Button variant="primary" onClick={handleShow}>ADD</Button>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <Row>
                                <Col md = {10}>
                                    <form onSubmit={onFileUpload}>
                                        <input type="file" id="files-upload" accept=".xls,.xlsx" onChange={onFileChange} />
                                        <button type="submit" id="uploadbutton" className="btn btn-primary">Upload! {uploadBtn}</button>             
                                    </form>
                                </Col>    
                            </Row>
                            <p style={{ fontSize: '14px' }}>(NOTE: Upto 500 records can be uploaded at a time)</p>
                            {renderDownloadFile()}
                        
                            <DataTableV1
                                columns={columns}
                                data={items}
                            />
                        </CardBody>
                    </Card>
                </Col>
            </Row>

            <Modal show={showModal} onHide={handleClose} dialogClassName="modal-90w">
                <Modal.Header closeButton>
                    <Modal.Title>{FormTitle}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form name="frmPayUScoreRankMapping">
                        <Row>
                            {columnlist.map(col => (
                                fnRenderfrmControl(col, formvalue, handleChange, event)
                            ))}
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <If condition={ModalValueChanged}>
                        <Then>
                            <input type="submit" value="Save Changes" className="btn btn-primary" onClick={handleSave} />
                        </Then>
                    </If>
                </Modal.Footer>
            </Modal>
        </div>
    );
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
  }
  
export default connect(
mapStateToProps,
{
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
}
)(PayUScoreRankMapping);