import React, { Component } from 'react';

import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { GetCommonData,GetCommonspData, InsertData, UpdateData, GetFileExists } from "../../../store/actions/CommonAction";
import { getUrlParameter, getuser } from '../../../utility/utility.jsx';

import OtpInput from "react-otp-input";
import { If, Then, Else } from 'react-if';
import { connect } from "react-redux";
import moment from 'moment';
import StarRating from "../StarRating";

class AdvisorInfo extends Component{

    constructor(props) {
        super(props);
        this.state = {
            bgImg: '/Graphic.svg' ,
            advImg: '/Logo.svg' ,
            enterSmallImg:'/enterSmall.svg',
            verifybtniconImg:'/verifybtnicon.svg',
            happystar:'/happy-star.png',
            CertifiedBadge:'/CertifiedBadge.svg',
            pointerImg:'/Pointer.svg',
            advisorData: [],
            advisorDataRating: null,
            StarRating : 0,
            rating:'',
            currentRating: "0",

    }
    }

    back  = (e) => {
        e.preventDefault();
        this.props.prevstep();
    }

    async componentDidMount() {//debugger;
      
        var body = document.body;

        body.classList.add("advisorInfo");
        console.log('agentid',this.props.agentid)
        
      }
      

      setRating = rating => {
        var ratingshow = document.getElementById("ratingshow");
  
          if(Number(this.props.StarRatingValue) != Number(rating)){
            ratingshow.classList.remove("disabled");
          }else{
            ratingshow.classList.add("disabled");
          }
          this.setState({ rating: rating });
          console.log('parent rating',rating);
      };

    async saveRating(){
        let advisorData = this.props.advisorData;
        await this.props.GetCommonspData({
          root: 'InsertAdvisorInfoStarRating',
          c: "L",
          params: [{"UserID": this.props.agentid , "CustomerId" : this.props.customerId, "StarRating" : this.state.rating }],
            }, function (result) {
            if (result.data.status == 200) {
                alert('Rating submitted successfully')
                var ratingshow = document.getElementById("ratingshow");
                ratingshow.classList.add("disabled");
              }else{
                alert('Rating not submitted')
              }
           }.bind(this));  


    }  

    loadAlternative(element) {
      
      var list = element.target.getAttribute('alternative').split(/,/);
      var image = new Image();
    
      image.onload = function() {
        document.getElementById("profilepic").src = this.src;
      }
    
      image.onerror = function() {
        if (list.length) {
          document.getElementById("profilepic").src = "/no-image.jpeg"; 
          //this.loadAlternative();
        }
      }
    
      //  pick off the first url in the list
      image.src = list.shift();
    }

    render(){
        const { advisorData, agentid, StarRatingValue, customerId, kycinfo, IsLoading, type } = this.props;
        const flag = getUrlParameter('flag');
        console.log('advisordate',this.props.advisorData);
        return(
          <>
          {agentid && customerId &&
            <Row className='otp-layout contentCenter'>
              {(!(this.props.type == 'VC')) && <Col md={6}  xs={12}>
                 <div className="verifybgImg"><img src={this.state.bgImg}/>  </div>  </Col>
              }
                  <Col md={6} xs={12}>
                  {this.props.IsLoading && <div className="LoaderAdvisorInfo"> <i class="fa fa-spinner fa-spin"></i></div>}
                  <Row> 
                  {advisorData && (advisorData.length > 0 ) && 
                  <Col md={12} xs={12} lg={9 }>
                      <Row className="blueBg">
                          <div className="mobileViewBgBlue">
                          { (advisorData[0].AVCertified == true ) && <div className="CertifiedBadge">  <img src={this.state.CertifiedBadge}/> <p>Insurance Institute of India Certified </p></div> } 
                    <div className="emiProfile"> 
                    <img id="profilepic" src={"https://policystatic.policybazaar.com/ProfilePic/"+advisorData[0].Empcode+".jpg"} 
                    alternative={"https://policystatic.policybazaar.com/ProfilePic/"+advisorData[0].Empcode+".JPG, /no-image.jpeg"} 
                    onError={this.loadAlternative.bind(this)}></img>
      
                    <p className="emiId">{advisorData[0].Empcode}</p>
                    <h2 className="emiName">{advisorData[0].EmpName}</h2>
                    <p className="message">{advisorData[0].BookingCount} <br/> {advisorData[0].Tenure} with Policybazaar</p>  
                    </div>
                    </div>
                    </Row>
                    <Row>
                          <div className="mobileViewBgwhite">
                    <div className="emiDetails">
                        <span>About {advisorData[0].EmpName} <img src={this.state.pointerImg}/> </span>
                        <p>{advisorData[0].EmpInfo}</p>
                    </div>
                    <div className="ratingBox">
                    <img src={this.state.happystar}/> 
                    <p>More than <b>{advisorData[0].CSATRating}% customers</b> have rated their experience as <b>Very Good</b> with {advisorData[0].EmpName}</p>         
                    </div>
                    {kycinfo && kycinfo.IsRatingAllowed &&
                     <div className="sharefeedback">
                    <span>Share your feedback<img src={this.state.pointerImg}/> </span>
                    <p>
                    <StarRating
                    numberOfStars="5"
                    currentRating={StarRatingValue}
                    onClick={this.setRating}
                    key={StarRatingValue}
                    />
                        {/* <i class="fa fa-star " ></i> <i class="fa fa-star " ></i> <i class="fa fa-star " ></i> <i class="fa fa-star " ></i><i class="fa fa-star-o " ></i>  */}
                        </p>
                        <div className="ratingMsg">Very Good</div>
                      <div className="actions">
                      <button type="submit" id="ratingshow" onClick={this.saveRating.bind(this)} className="disabled">
                          Submit Rating
                      </button>
                      </div>   
                    
                    </div>}
                    </div>
                    </Row>
              </Col>}
                </Row>
                  </Col>  
                  </Row>
        }
                 
           </>
        
        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      InsertData,
      UpdateData,
    }
  )(AdvisorInfo);