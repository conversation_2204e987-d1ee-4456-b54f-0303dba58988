import React, { Component } from 'react';
import Tabs from '../Tabs/Tabs';
import AgentBooking from './AgentBooking';
import ProcessAgentBooking from './ProcessAgentBooking';
import Query from './Query';
const tabs = [{name: 'Agent Bookings'}, {name: 'Process Agent Incentive'}, {name: 'Query'}];
class Agent extends Component{
    constructor(props){
        super(props);
        this.state = {
            activeTab : 'Agent Bookings'
        }
        this.handleTab = this.handleTab.bind(this);
    }
    
    handleTab(curTab){
        this.setState({
            activeTab : curTab
        })
    }

    render(){
        return (<React.Fragment>
            <Tabs tabs={tabs} onConfirm={this.handleTab} activeTab={this.state.activeTab} />
            {this.state.activeTab == 'Agent Bookings' && <AgentBooking/>}
            {this.state.activeTab == 'Process Agent Incentive' && <ProcessAgentBooking/>}
            {this.state.activeTab == 'Query' && <Query/>}
        </React.Fragment>);
    }
}
export default Agent;