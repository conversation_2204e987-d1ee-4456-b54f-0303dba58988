import React, { useState, useEffect, useRef } from 'react';

import {
  Routes,
  Route
} from "react-router-dom";

// import Footer from "components/Footer/Footer.jsx";

import Unauthorized from '../views/MUICommon/Unauthorized.jsx';
import { getUrlParameter, getCookie, setCookie, getuser } from '../utility/utility.jsx';
import {
  GetDataDirect
} from "../store/actions/CommonAction.jsx";
import routes from "../routes.js";
import config from "../config.jsx";

const MuiAdminLayout = (props) => {

  const [user, setUser] = useState({});
  const [Menu, setMenu] = useState(null);
  const [location, setLocation] = useState(null);

  const Logout = () => {
    setCookie("userid", "", -1);
    localStorage.clear();
    window.location.href = config.site_base_url;
  }

 

  useEffect(() => {
    // var userid = getCookie("AgentId");
    // if(getUrlParameter("u") != userid){
    //   this.props.history.push('/client/UnAuthenticated')
    // }
    let location = true;
    setLocation(location)
    let userid = null;
    userid = getUrlParameter("u") != "" ? getUrlParameter("u") : getCookie("AgentId");
   
    if (userid != "") {
      GetDataDirect({
        root: "UserDetails",
        UserID: userid,
        statename: "Users-" + userid,
        state: true
      }, function (result) {
        setUser(result[0]);
      });

      GetDataDirect({
        root: "FetchMenuMaster",
        UserId: userid,
        statename: "Menu-" + userid,
        state: false
      }, function (result) {
        setMenu(result?.[0]);
      });

    }
    else {
      Logout();
    }
  }, []);

  const CheckAuthentication = (prop, key) => {

    // return (<Route
    //   path={prop.path}
    //   element={prop.component}
    //   key={key} />)

  
    // const RoleIdCheck = prop?.RoleIdCheck || false;
    const RoleIds = prop?.RoleId || [];
    let IsRoleIdAuthorized = true;
    const { RoleId } = getuser() || {};

    // if (RoleIdCheck && !RoleIds.includes(RoleId)) {

    if (!RoleIds.includes(RoleId)) {
      IsRoleIdAuthorized = false;
    }
   
    if (config.byMenuSecurity) {
      return <Route
        path={prop.path}
        element={prop.component}
        key={key} />
    }

    if (!Menu) {
      return;
    }

    let hasMenu = false;

    for (let index = 0; index < Menu.length; index++) {
      const element  = Menu[index];
      let path = prop.path.replace("/", "");
      if (element.URL.includes(path)) {
        hasMenu = true
        break;
      }
    }
   

    if (IsRoleIdAuthorized && (hasMenu || !prop.authCheck)) {

      return (<Route
        path={prop.path}
        element={prop.component}
        key={key} />)
    }
    else {
 
      return (<Route
        path={prop.path}
        element={<Unauthorized />}
        key={key} />)

    }
  }


  return (
    <div className="wrapper">
      
      <div className={(location) ? "main-panel full" : ""} 
        // ref={this.mainPanel}
      >
        
        <Routes>
          {
            routes.map((prop, key) => {
              return CheckAuthentication(prop, key)
            })
          }
        </Routes>
        {/* <Footer fluid /> */}
      </div>
    </div>
  );
}

export default MuiAdminLayout;
