
import React from "react";
import {
  GetCommonData, GetCommonspData, GetDataDirect, GetFileExists, GetAwsRecordingUrl
} from "../store/actions/CommonAction";
import {
  GetMySqlData
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { OpenSalesView, getUrlParameter, getuser, fnDatatableCol, hhmmss } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDown';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'
import Loader from './Common/Loader';
import DropDownList from './Common/DropDownList';



// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class C2CTrackingDataService extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      root: "conference",
      PageTitle: "C2CTrackingDataService",
      C2CTrackingDataService: [],
      ProductId: 0,
      ReportDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
      ReportTime: null,
      SelectedSupervisors: [],
      ConfType: 'sales_to_service',
      startdate: moment().format("YYYY-MM-DD"),
      // enddate: moment().format("YYYY-MM-DD"),
      //startdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
      enddate: moment().add(1, "days").format("DD-MM-YYYY"),
      // maxdate: moment().subtract(60, 'days').format("YYYY-MM-DD"),
      showMoreInfoModal: false,
      MoreInfoData: [],
      addClass: "fa fa-play-circle",
      confTypes: [],
      C2CData: [],
      C2CLoader: false,
    };
    this.columnlist = [
      {
        label: "LeadId",
        name: "LeadId",
        searchable: true
      },
      {
        label: "Dispostion",
        name: "Dispostion",
        cell: row => <div className="Dispostion">{row.Dispostion ? row.Dispostion : "N.A"}</div>,
      },
      {
        label: "Context",
        name: "Context",
        cell: row => <div className="Context">{row.Context ? row.Context : "N.A"}</div>,
      },
      {
        label: "Duration",
        name: "Duration",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.Duration),
      },
      {
        label: "ActualCallDate",
        name: "ActualCallDate",
        cell: row => <div className="calldate">{row.ActualCallDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.ActualCallDate}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",
      },
      {
        label: "ActualCallTime",
        name: "ActualCallTime",
        cell: row => <div className="calldate">{row.ActualCallTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.ActualCallTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",
      },
      {
        label: "CustomerRequestDate",
        name: "CustomerRequestDate",
        cell: row => <div className="calldate">{row.CustomerRequestDate ? <Moment format="YYYY-MM-DD" utc={true}
        >{row.CustomerRequestDate}</Moment> : "N.A"}</div>,
        type: "date",
        "sortable": true,
        width: "150px",

      },
      {
        label: "CustomerRequestTime",
        name: "CustomerRequestTime",
        cell: row => <div className="calldate">{row.CustomerRequestTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.CustomerRequestTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "RequestPlaced",
        name: "RequestPlaced",
        cell: row => <div className="calldate">{row.RequestPlaced ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.RequestPlaced}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        label: "LastCallTime",
        name: "LastCallTime",
        cell: row => <div className="calldate">{row.LastCallTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}
        >{row.LastCallTime}</Moment> : "N.A"}</div>,
        type: "datetime",
        "sortable": true,
        width: "150px",

      },
      {
        name: "Retry",
        label: "Retry",
        type: "string",
        cell: row => <div className="Retry">{row.Retry ? row.Retry : "N.A"}</div>,
      },
      {
        name: "TalkTime",
        label: "TalkTime",
        type: "string",
        cell: row => <div className="TalkTime">{row.TalkTime ? row.TalkTime : "N.A"}</div>,
      },
      {
        name: "WaitingTime",
        label: "WaitingTime",
        type: "string",
        cell: row => <div className="WaitingTime">{row.WaitingTime ? row.WaitingTime : "N.A"}</div>,
      },
      {
        label: "call_back_classification_mins",
        name: "call_back_classification_mins",
      },
      {
        label: "employeeid",
        name: "employeeid",
        searchable: true
      },
      {
        label: "processing",
        name: "processing",
      },
      {
        label: "type_request",
        name: "type_request",
      },
      {
        label: "username",
        name: "username",
      },
    ];

    this.ProdTypeList = {
      config:
      {
        root: "Productcustom",
        data: [{ Id: "117", Display: "Motor" }, { Id: "2", Display: "Health" }],
      }
    };
  }

  componentDidMount() {
   

  }

  CheckLoader(action) {
    if (action == 'C2C') {
      if (this.state.C2CLoader)
        return <Loader />;
    } 
  }

  componentWillReceiveProps(nextProps) {
    //debugger;
    if (!nextProps.CommonData.isError) {
      //this.setState({ C2CTrackingDataService: nextProps.CommonData[this.state.root] });
      // console.log(nextProps.CommonData[this.state.root]);
    }
  }

  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);
    return columns;
  }

  handleStartDateChange = (e, props) => {
    if (e._isAMomentObject) {
      //this.props.onStartDate(e.format("YYYY-MM-DD"));
      this.setState({ startdate: e.format("YYYY-MM-DD"), enddate: e.add(7, 'days').format("YYYY-MM-DD") }, function () {
      });
    }
  }

  handleEndDateChange = (e, props) => {
    if (e._isAMomentObject) {
      //this.props.onEndDate(e.format("YYYY-MM-DD"));
      this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
        //this.fetchCallBackData();
      });
    }
  }

  validation = (currentDate) => {
    return currentDate.isBefore(moment());
  };

  validationEndDate = (currentDate) => {


    if (!currentDate.isBefore(moment(this.state.enddate))) {
      return false;
    }

    if (currentDate.isBefore(moment(this.state.startdate))) {
      return false;
    }

    return true;

  };

  fetchC2CData(){
    if(this.state.ProductId == 0){
      toast("Please enter Product", { type: 'error' });
      return false;
    }
    this.setState({ C2CLoader : true});
    var json = {
        "FromDate" : this.state.startdate,//moment().format("YYYY-MM-DD 00:00:00"),
        "ToDate" : this.state.enddate,//moment().format("YYYY-MM-DD 23:59:59"),
        "ProductID" : this.state.ProductId
       };
       this.props.GetCommonspData({
        root: 'C2CTrackingDataService',
        c: "L",
        params: [json],
    }, function (result) {
      this.setState({ C2CLoader : false});
        if (result.data && result.data.data[0] ) {debugger;
            // console.log(result.data.data[0]);
            this.setState({ C2CData: result.data.data[0] });
      }
    }.bind(this));
  }

  protypechange(e){
    this.setState({ ProductId : e.target.value});
  }
  render() {
    //const columns = this.columnlist;
    const columns = this.fnDatatableCol();
    const { items, PageTitle,  showAlert, AlertMsg, AlertVarient, C2CData } = this.state;

    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={2}>
                        <Form.Group controlId="ProductID">
                            <DropDownList firstoption="Select Product" value={this.state.ProductId} col={this.ProdTypeList} onChange={this.protypechange.bind(this)}>
                            </DropDownList>
                        </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.startdate}
                        isValidDate={this.validation}
                        onChange={moment => this.handleStartDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.enddate}
                        isValidDate={this.validationEndDate}
                        onChange={moment => this.handleEndDateChange(moment)}
                        utc={true}
                        timeFormat={false}
                      />
                    </Col>
                    
                    <Col md={1}>
                      <Button variant="primary" onClick={() => this.fetchC2CData()}>Fetch{this.CheckLoader('C2C') }</Button>
                    </Col>
                   
                  </Row>

                </CardHeader>
                <CardBody>
                {/* {this.CheckLoader('C2C') } */}
                <If condition={( this.state.C2CData.length > 0)}>
                <Then>
                  <DataTable
                    columns={columns}
                    data={(C2CData && C2CData.length > 0) ? C2CData : []}
                  />
                </Then>
                </If>
                </CardBody>
              </Card>
            </Col>
          </Row>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetMySqlData
  }
)(C2CTrackingDataService);