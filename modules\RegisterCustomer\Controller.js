const Utility = require("../../Libs/Utility");
const LoggerMethods = require("../Loggerdb/Methods");
const LoggerController = require("../Loggerdb/Controller");
const moment = require("moment");
const config = require("../../env_config");

//Code By <PERSON><PERSON><PERSON><PERSON>
async function RegisterCustomer(req, res) {
  let requestTime = new Date().toISOString();

    try {

      let url = config.REGISTER_CUSTOMER;
      let headers = setHeaders();
      let params = setApiParameters(req.body);
      let returnJson = [];
      returnJson = await Utility.API_POST(url, params, headers);
      let logData = {};
      let responseTime = new Date().toISOString();

      //logData.Url = JSON.stringify(url)
      logData.TrackingId = JSON.stringify(req.body.MobileNo)
      logData.headers = JSON.stringify(headers)
      logData.Method = "RegisterCustomer";
      logData.Application = "MatrixDashboard";
      logData.Channel = "FOSCreateLead";
      logData.RequestText = params+JSON.stringify(params);
      logData.ResponseText =JSON.stringify(returnJson);
      logData.Requesttime = requestTime;
      logData.CreatedOn = responseTime;
      logData.CreatedBy = "MatrixRegisterCustomer";
      logData.Headers = req.headers;
        

      LoggerMethods.LogKafka(logData);
      if(returnJson) {
        res.send({
            status: 200,
            data: returnJson
        });
      }
    }
    catch (e) {
      console.log('loggercontroller', e.toString());
      let logData = {};
      let responseTime = new Date().toISOString();

      logData.TrackingID = JSON.stringify(req.query.ReferralId)
      logData.Exception = e.toString();
      logData.Method = "RegisterCustomer";
      logData.Application = "MatrixDashboard";
      logData.Channel = "FOSCreateLead";
      logData.RequestText = JSON.stringify(req.query);
      logData.ResponseText = e;
      logData.Requesttime = requestTime;
      logData.Responsetime = responseTime;
      logData.CreatedOn = responseTime;
      logData.CreatedBy = "MatrixRegisterCustomer";
      logData.Headers = req.headers;
        

    LoggerMethods.LogKafka(logData);
      console.log(e);
      res.send({
          status: 500,
          message: e
      });
    }
    finally {
      
     
      return;
    }
}

function setHeaders() {
  return {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'Access-Control-Allow-Origin' : '*',
    //'Authorization': '3008c1de-2129-42f2-8876-fc20ba39822a a5UU2dQvLlFgS4NNHkm3AQ%3D%3D drmtsc'
    'clientkey': config.REGISTER_CUST_CLIENTKEY,
    'authkey': config.REGISTER_CUST_AUTHKEY
  };
}

function setApiParameters(params){

  return JSON.stringify({
    "CustomerId": "",
    "MobileNo": params.MobileNo || null,
    "ProviderId": "0",
    "Email": params.EmailId || null,
    "DOB": moment(params.DateOfBirth,'DD-MM-YYYY').format('YYYY-MM-DD') || null,
    "Age":params.Age || null,
    "FirstName": params.Name || null,
    "MiddleName": params.MiddleName || null,
    "LastName": params.LastName || null,
    "FamilyName": params.FamilyName || null,
    "GenderID": params.Gender || null,
    "IsNRI": params.IsNRI || null,
    "CountryID": params.Country || 392,
    "CityID": params.CityId || null,
    "IncomeGroupID": params.Income || null,
    "MaritalStatusID": params.MaritalStatus || null,
    "CustMemId": params.CustomerId || null,
    "IsVerified": params.IsVerified || null,
    "VerifiedBy": params.VerifiedBy || null,
    "CustomerCount": params.CustomerCount || null,
  });
}

module.exports = {
    RegisterCustomer: RegisterCustomer
};