var express = require("express");
const router = express.Router();
const LotteryController = require("./LotteryController");

router.get("/getLotteryTickets", LotteryController.GetUserLotteryTickets);
router.get("/getBuList", LotteryController.GetBuList);
router.get("/getRewardsList", LotteryController.GetRewardsList);
router.get("/getTicketCount", LotteryController.GetTicketCount);
router.post("/getWinnerDetail", LotteryController.GetWinnerDetail);
router.post("/getWinnerDetailJag", LotteryController.GetWinnerDetailJag);
router.get("/GetWinners", LotteryController.GetWinners);
router.get("/GetRewardDetails", LotteryController.GetRewardDetails);
router.get("/GetTickets", LotteryController.GetTickets);
router.get("/getCurrentQuiz", LotteryController.getCurrentQuiz);
router.get("/GetLottery", LotteryController.GetLottery);
module.exports = router;