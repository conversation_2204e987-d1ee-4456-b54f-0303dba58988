
const sqlHelper = require("../../Libs/sqlHelper");


const IsBookingSearchAllowed = async (Data) => {
  try {
    let IsAllowed = 0; BookingId = 0;
    let proc = '[MTX].[IsBookingSearchAllowedTest]';

    let sqlparams = [
      { key: 'SearchText', value: Data.SearchText },
      { key: 'SearchByField', value: Data?.SearchByField },
      { key: 'UserId', value: Data?.UserId },
      { key: 'BookingType', value: parseInt(Data?.BookingType)},
      { key: 'IsAllowed', value: "", type: "out" },
      { key: 'BookingId', value: "", type: "out" }
    ];

    const response = await sqlHelper.sqlProcedure('R', proc, sqlparams);

    // console.log("The response is ",response);

    if (response && response.output) {
      IsAllowed = response.output.IsAllowed;
      BookingId =  response.output.BookingId;
    }
    return { IsAllowed, BookingId };
  }
  catch (err) {
    console.log("getLeadIdsSecondaryAgent error ", err);
    return 0;
  }
}


module.exports = {
  IsBookingSearchAllowed,
}
