.wheelOfFortuneWapper {
    width: 100%;
    text-align: center;
    margin-top: 78px;
}
.wheelOfFortune {
    display: inline-block;
    position: relative;
    overflow: hidden;
    /* border-radius: 50%;
    box-shadow: 0 0px 100px 60px #643867; */
}

.wheel {
    display: block;
}

.spin {
    font: 1.5em/0 sans-serif;
    user-select: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30%;
    height: 30%;
    margin: -15%;
    background: #fff;
    color: #fff;
    box-shadow: 0 0 0 8px #fff, 0 0px 15px 5px rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    transition: 0.8s;
}

.spin::after {
    content: "";
    position: absolute;
    top: -17px;
    border: 10px solid transparent;
    border-bottom-color: #fff;
    border-top: none;
}
