
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData, GetFileExists, GetAwsRecordingUrl, GetRecordingName
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';

import Moment from 'react-moment';
import moment from 'moment';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { Compare<PERSON>son, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class UserCallDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "UserCallDetails",
      PageTitle: "User Call Details",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      prolist: '',
      agentranklist: '',
      leadranklist: '',
      empid: '',
      leadid: '',
      CallDate: moment().format("YYYY-MM-DD"),
      maxdate: moment().subtract(59, 'days').format("YYYY-MM-DD"),
      AwsRecordingUrl: '',
      RecordingName: '',
      addClass: "fa fa-play-circle",
      callid: '',
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);

    this.empchange = this.empchange.bind(this);
    this.leadchange = this.leadchange.bind(this);
    this.selectedrow = { "CallDataId": 0, "CallID": null, "LeadID": null }
    this.columnlist = [
      {
        name: "CDH.CallDataId",
        label: "CallDataId",
        alias: "calldataid",
        type: "hidden",
        hide: true,
      },
      {
        name: "CDH.callid",
        label: "callid",
        alias: "callid",
        type: "hidden",
        hide: true,
      },
      {
        name: "UD.EmployeeId",
        label: "EmployeeId",
        alias: "empid",
        type: "string",
      },
      {
        name: "CDH.LeadID",
        label: "LeadID",
        alias: "leadid",
        type: "string",
      },
      {
        name: "CallDate",
        label: "CallDate",
        type: "datetime",
        width: "150px",
      },
      {
        name: "CDH.Duration",
        label: "Duration",
        alias: "duration",
        type: "number"
      },
      {
        name: "CDH.talktime",
        label: "talktime",
        alias: "talktime",
        type: "number"
      },
      {
        name: "CDH.CallType",
        label: "CallType",
        alias: "calltype",
        type: "string"
      },
      {
        name: "CDH.Disposition",
        label: "Disposition",
        alias: "disposition",
        type: "string"
      },
      {
        name: "CDH.Context",
        label: "Context",
        alias: "context",
        type: "string"
      },
      {
        name: "CDH.CallDataId",
        label: "AgentListen",
        alias: "CallDataId",
        cell: row =>
          <div className="listenUserDetails">
            <span id={"span_agent" + row.CallDataId} onClick={(e) => this.CreateRecordingURL(e, row, 'agent')}>
              <i class="fa fa-play-circle listen"></i>
            </span>
          </div>
      },
      {
        name: "CDH.CallDataId",
        label: "CustomerListen",
        alias: "CallDataId",
        cell: row =>
          <div className="listenUserDetails">
            <span id={"span_customer" + row.CallDataId} onClick={(e) => this.CreateRecordingURL(e, row, 'customer')}>
              <i class="fa fa-play-circle listen"></i>
            </span>
          </div>
      },
      {
        name: "CDH.CallDataId",
        label: "CombinedListen",
        alias: "CallDataId",
        cell: row =>
          <div className="listenUserDetails">
            <span id={"span_combined" + row.CallDataId} onClick={(e) => this.CreateRecordingURL(e, row, 'combined')}>
              <i class="fa fa-play-circle listen"></i>
            </span>
          </div>
      }

    ];
    let count = 0;
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }
  }

  CreateRecordingURL(e, row, recordtype) {
    //debugger;

    //let url = "http://20.80.10.88:8080/api/v1/db/awsfile?key=PW21900-659064683-192039657-20200517-000006.wav";
    this.play(row.CallDataId, row, recordtype);

    //debugger;


  }

  play(number, row, recordtype) {
    debugger;
    console.log(recordtype);
    var extension = '';
    if(recordtype == 'agent'){
      extension = '-out.wav';
    }else if(recordtype == 'customer'){
      extension = '-in.wav';
    }else if(recordtype == 'combined'){
      extension = '.wav';
    }
    var audio = document.getElementById('audio1');
    //var icon = document.getElementById("play" + number);
    if (audio.paused) {

      let calldataid = row.callid;
      this.state.AwsRecordingUrl = '';
      document.getElementById('span_'+ recordtype + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      //debugger;
      GetRecordingName(calldataid, function (results) {
        //console.log("results", results);   
        var CheckFile = '';
        if(recordtype == 'agent'){
          CheckFile = results.data.agent_file;
        }else if(recordtype == 'customer'){
          CheckFile = results.data.customer_file;
        }else if(recordtype == 'combined'){
          CheckFile = results.data.file_available;
        }

        if (CheckFile == true) {
          //this.setState({ RecordingName: results.data.record });
          GetAwsRecordingUrl(results.data.record+extension, 'newcctecbuckt', function (results) {
            //debugger;
            console.log("results", results);
            if (results.data.status == 200) {
              this.setState({ AwsRecordingUrl: results.data.data });
              audio.src = results.data.data;
              document.getElementById('span_'+ recordtype + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

              audio.onloadedmetadata = function() {
                console.log(audio.duration)
                //setTimeout(function () {
                  audio.play();
                  console.log(audio.duration);
                  if(audio.paused == false && audio.duration > 0 && audio.duration != 'Infinity' && audio.duration != 'NaN'){
                  document.getElementById('span_'+ recordtype + number).innerHTML = '<i class="fa fa-stop-circle listen"></i>';
                 
                  audio.onended = function() {
                    document.getElementById('span_'+ recordtype + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
                  };
                 
                  }else{
                  document.getElementById('span_'+ recordtype + number).innerHTML = 'No File Found'; 
                  }
               // }.bind(this), 500);
              };
              
            } else {
              document.getElementById('span_'+ recordtype + number).innerHTML = 'No File Found';
            }
          }.bind(this));
        } else {
          document.getElementById('span_'+ recordtype + number).innerHTML = 'No File Name Found';
        }

      }.bind(this));

    } else {
      //debugger;
      audio.pause();
      audio.currentTime = 0;
      document.getElementById('span_'+ recordtype + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';

      //debugger;
    }
  }

  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    // columns.push({
    //   name: "Action",
    //   cell: row => <ButtonGroup aria-label="Basic example">
    //     <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
    //     <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
    //   </ButtonGroup>
    // });
    return columns;
  }
  handleCopy(row) {
    this.setState({ formvalue: Object.assign({}, row, {}), event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }


  empchange(e, props) {
    this.setState({ empid: e.target.value });
  }
  leadchange(e, props) {
    this.setState({ leadid: e.target.value });
  }
  callIdchange(e, props) {
    this.setState({ callid: e.target.value });
  }
  CallDateChange(e, props) {
    if (e._isAMomentObject) {
      this.setState({ CallDate: e.format("YYYY-MM-DD") }, function () {
      });
    }
  }
  fetchData() {
    console.log(this.state.leadid + this.state.empid);
    let condition = [];
    if (this.state.leadid != '') {
      condition.push({ "CDH.LeadID": this.state.leadid })
    }
    if (this.state.callid != '') {
      condition.push({ "CDH.CallID": this.state.callid })
    }
    if (this.state.empid != '') {
      condition.push({ "UD.EmployeeId": this.state.empid })
    }
    // else{
    //   toast("Please enter Employee Id", { type: 'error' });
    //   return;
    // }
    if (this.state.CallDate != '') {
      condition.push({ "CDH.CallDate": this.state.CallDate })
    }
    else{
      toast("Please select Date", { type: 'error' });
      return;
    }


    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name"),
      c: "L",
      con: condition,
      //con: [{ "CDH.LeadID": this.state.leadid }],
    });
  }

  validationdate = (currentDate) => {
    return !currentDate.isBefore(moment(this.state.maxdate));
  };

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event } = this.state;
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    {/* <Col md={4}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col> */}
                    <Col md={2}>
                      <Form.Control type="text" onChange={this.empchange} placeholder={"Enter EmployeeId"} />
                    </Col>
                    <Col md={2}>
                      <Form.Control type="text" onChange={this.leadchange} placeholder={"Enter LeadId"} />
                    </Col>
                    <Col md={2}>
                      <Form.Control type="text" onChange={this.callIdchange.bind(this)} placeholder={"Enter CallId"} />
                    </Col>
                    <Col md={2}>
                      <Datetime value={new Date()}
                        dateFormat="YYYY-MM-DD"
                        value={this.state.CallDate}
                        isValidDate={this.validationdate}
                        onChange={moment => this.CallDateChange(moment)}
                        utc={true}
                        timeFormat={false} />

                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={() => this.fetchData()}>Fetch</Button>
                    </Col>
                    <Col md={1}>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
          <audio src="" id={"audio1"}></audio>
          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>

            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
  }
)(UserCallDetails);