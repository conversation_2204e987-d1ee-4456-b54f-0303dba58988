
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import { fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Table,
  Row,
  Col
} from "reactstrap";

class AlertMaster extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      activePage: 1,
      root: "AlertMaster",
      PageTitle: "Alert Master",
      FormTitle: "",
      formvalue: {},
      event: "",
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.selectedrow = { "Id": 0, "type": "", "alerttitle": "", "querytype": "", "databasename": "", "collection": "", "dbserver": "Replica", "tomail": "", "tomobile": "", "count": 0, "operation": "", "starttime": new Date(), "frequency": 0, "IsActive": true, "reporttype": null, "endtime": new Date(), "nextruntime": new Date(), "createddate": new Date(), "createdby": "", "updateddate": new Date(), "updatedby": null, "ExcelSheetNames": "", "querysource": "", "ccmail": null, "bccmail": null, "frommail": null }
    this.columnlist = [
      {
        name: "Id",
        label: "Id",
        type: "hidden",
        hide: true,
      },
      {
        name: "type",
        label: "Type",
        type: "string"
      },
      {
        name: "alerttitle",
        label: "Alert Title",
        type: "string",
        searchable: true,
      },
      {
        name: "querytype",
        label: "Query Type",
        type: "string"
      },
      {
        name: "starttime",
        label: "Start Time",
        type: "time"
      },
      {
        name: "endtime",
        label: "End Time",
        type: "time"
      },


      {
        name: "createdby",
        label: "Created By",
        type: "string",
        hide: true
      },

      {
        name: "tomobile",
        label: "tomobile",
        type: "string",
        hide: true
      },
      {
        name: "tomail",
        label: "tomail",
        type: "string",
        hide: true
      },
      {
        name: "ccmail",
        label: "ccmail",
        type: "string",
        hide: true
      },
      {
        name: "bccmail",
        label: "Bcc Mail",
        type: "string",
        hide: true
      },
      {
        name: "frommail",
        label: "From Email",
        type: "string",
        hide: true
      },
      {
        name: "nextruntime",
        label: "Next Run Time",
        type: "datetime"
      },
      {
        name: "databasename",
        label: "Database Name",
        type: "string",
        hide: true
      },
      {
        name: "collection",
        label: "Collection",
        type: "string",
        hide: true
      },
      {
        name: "dbserver",
        label: "dbserver",
        type: "string",
        hide: true
      }, {
        name: "frequency",
        label: "Frequency",
        type: "int"
      }, {
        name: "count",
        label: "count",
        type: "int",
        hide: true
      }, {
        name: "operation",
        label: "operation",
        type: "string",
        hide: true
      }, {
        name: "querysource",
        label: "querysource",
        type: "textarea",
        hide: true
      }, {
        name: "ExcelSheetNames",
        label: "ExcelSheetNames",
        type: "textarea",
        hide: true
      }, {
        name: "reporttype",
        label: "Report Type",
        type: "textarea",
        hide: true
      }, {
        name: "updatedby",
        label: "updatedby",
        type: "string",
        hide: true
      }, {
        name: "createddate",
        label: "createddate",
        type: "datetime",
        editable: false
      }, {
        name: "updateddate",
        label: "updateddate",
        type: "datetime",
        hide: true,
        editable: false
      },
      {
        name: "IsActive",
        label: "Is Active",
        type: "bool"
      }
    ];
    let count = 0;
  }



  componentDidMount() {


    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name")
    });

  }

  componentWillReceiveProps(nextProps) {
    
    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
    }

    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200)
        alert(nextProps.CommonData.InsertSuccessData.error);
      else {
        this.setState({ showModal: false });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        alert(nextProps.CommonData.UpdateSuccessData.error);
      else {
        this.setState({ showModal: false });
      }
    }

  }


  fnDatatableCol() {
    var columns = fnDatatableCol(this.columnlist);
    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }


  handleCopy(row) {
    this.setState({ formvalue: row, event: "Copy", showModal: true, FormTitle: "Copy Record" });
  }
  handleEdit(row) {
    this.setState({ formvalue: row, event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }

  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {


    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" });
  }

  handleSave() {
    let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
    debugger;
    this.fnCleanData(formvalue);
    
    //return;
    if (this.state.event == "Edit") {
      formvalue["updateddate"] = new Date();
      this.fnCleanData(formvalue);
      let id = formvalue["Id"];
      delete formvalue["Id"]
      this.props.UpdateData({
        root: this.state.root,
        body: formvalue,
        querydata: { "Id": id }
      });
    } else if (this.state.event == "Copy") {
      formvalue["Id"] = this.getMax(this.state.items, "Id").Id + 1;
      formvalue["createddate"] = new Date();
      this.fnCleanData(formvalue);
      this.props.InsertData({
        root: this.state.root,
        body: formvalue
      });
      setTimeout(function () {
        this.props.GetCommonData({
          limit: 10,
          skip: 0,
          root: this.state.root,
          cols: GetJsonToArray(this.columnlist, "name")
        });
      }.bind(this), 2000);

    } else {

      formvalue["Id"] = this.getMax(this.state.items, "Id").Id + 1;
      formvalue["createddate"] = new Date();
      
      this.props.InsertData({
        root: this.state.root,
        body: formvalue
      });
    }
  }
  handleChange = (e,props) => {    
    let formvalue = this.state.formvalue;
    
    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if(e._isAMomentObject){
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }

    this.setState({ formvalue: formvalue });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }

  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue } = this.state;
    
    return (
      <>
        <div className="content">
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form>
                <Row>
                  {this.columnlist.map(col => (
                    fnRenderfrmControl(col, formvalue, this.handleChange,this.state.event)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <Button variant="primary" onClick={this.handleSave}>
                Save Changes
          </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData
  }
)(AlertMaster);