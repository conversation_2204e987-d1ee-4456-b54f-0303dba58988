
import React from "react";
import { Button } from 'react-bootstrap';

import { GetDashboardUrl, GetCommonspData } from "../store/actions/CommonAction";

import  {GetCommonData}  from "../store/actions/CommonMongoAction";

import { connect } from "react-redux";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class QuickSightVisualization extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "QuickSight Visualization",
      QuicksightUrl: [],
      mapping: {},
      prevprop: {}
    };
    this.ecode = null;
    

    // this.mapping = {
      // 'Motor': {
      //   "DashboardId": "69489666-583f-4147-81a0-5e02dae0a382",
      //   "users": {
      //     "PW00603": "GG",
      //     "PW12049": "Amol",
      //     "PW08726": "Puneet Bhatia",
      //     "ET01115": "Pardeep yadav",
      //     "PW12750": "Shaukat Ali",
      //     "AU00720": "test",
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",
      //     "PW08588": "Nitin Kumar"

      //   }
      // },
      // 'Investment': {
      //   "DashboardId": "9c8e7da4-0c30-4d52-933e-8c131f0b6763",
      //   "users": {
      //     "PW01088": "Akshay Sahni",
      //     "PW15063": "Aritra Saha",
      //     "PW00512": "Joy Bhatnagar",
      //     "PW00547": "Kushagra Ghai",
      //     "PW00448": "simmerpreet Singh",
      //     "ET01115": "Pardeep yadav",
      //     "PW12049": "Amol Sawant",
      //     "PW12750": "Shaukat Ali",
      //     "PW15064": "Ashish Kumar Sharma",
      //     "PW00455": "Swati Sharma",
      //     "PW09184": "Aayush Arora",
      //     "PW00560": "Aman Deep Singh",
      //     "PW11620": "Amit Sharma",
      //     "PW00537": "Geeta Bhadouria",
      //     "PW00462": "Prithvi Khurana",
      //     "PW19049": "Sandeep Pandey",
      //     "PW01169": "Sidharth Sahoonja",
      //     "PW17295": "Lav Udas",
      //     "PW25781": "Nipun Grover",
      //     "PW00609": "Tarun Khanna",
      //     "PW01088": "Akshay Sahni",
      //     "PW15063": "Aritra Saha",
      //     "PW13291": "David Oscar",
      //     "PW00547": "Kushagra Ghai",
      //     "PW13870": "Harish Poshal",
      //     "PW13508": "Ramesh Rangarajan Iyangar",
      //     "PW12750": "Shaukat Ali Barkat Alla Shaikh",
      //     "AU00720": "test",
      //     "PW26876": "Sulagno Chatterjee",
      //     "PW00633": "Ravi Choudhary",
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",
      //     "PW26202": "Uday Mishra",
      //     "PW00905": "Naresh Sindhi",
      //     "PW32004": "Sagar Rauth",
      //     "PW28930": "Dinesh Singh",
      //     "PW33224": "Suhail Abdul Kader",
      //     "PW12750": "Shaukat Ali Shaikh",
      //     "PW13870": "Harish Poshal",
      //     "PW13508": "Ramesh Iyanger",
      //     "PW13509": "Shritej Khondelker",
      //   }
      // },
      // 'Term': {
      //   "DashboardId": "1590a560-7fff-44d3-bb33-5e8ace69cb18",
      //   "users": {
      //     "PW12926": "Umang",
      //     "PW00446": "Devender Singh",
      //     "PW12049": "Amol",
      //     "PW01455": "Justin Thomas",
      //     "PW00547": "Kushagra Ghai",
      //     "ET01115": "Pardeep yadav",
      //     "PW20500": "Smruti Shetty",
      //     "PW08961": "Punit Gandhi",
      //     "PW10255": "Anesh Abraham",
      //     "PW00648": "Kuldeep Singh",
      //     "PW02765": "Kuldeep (KD)",
      //     "PW08802": "Ankit Lal",
      //     "PW08788": "Priyanka Srivastava",
      //     "PW00499": "Roshan Jha",
      //     "PW01459": "Niharika Verma",
      //     "PW07557": "Rajat Kohli",
      //     "PW00469": "Pardeep",
      //     "PW08680": "Deepak Kewalkrishan Kalra",
      //     "PW00446": "Devender Singh",
      //     "PW08084": "Harsh Suri",
      //     "PW13875": "Kunal Dewett",
      //     "PW07888": "Sujata Miglani",
      //     "PW00447": "Vishal Kumar",
      //     "PW12018": "Arafat Iqbal Memon",
      //     "PW12050": "Jimmy Mahendra Shetty",
      //     "PW20500": "Smruthi Shetty",
      //     "PW11373": "Ankit Grover",
      //     "PW12516": "Utsav Shashank",
      //     "PW08961": "Punit Gandhi",
      //     "PW12516": "Utsav Shashank",
      //     "AU00720": "test",
      //     "PW26876": "Sulagno Chatterjee",
      //     "PW00633": "Ravi Choudhary",
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",
      //   }
      // },
      // 'Health': {
      //   "DashboardId": "a94a49c0-0479-41ad-b916-0813273c9c99",
      //   "users": {
      //     "PW14905": "Amit Ahuja",
      //     "PW12049": "Amol Sawant",
      //     "PW18603": "Mohit Arora",
      //     "PW01420": "Shailja Charan",
      //     "PW00637": "Anand",
      //     "ET01115": "Pardeep yadav",
      //     "PW20500": "Smruti Shetty",
      //     "PW00231": "Roberta Davies",
      //     "PW00550": "Davinderjit Singh",
      //     "PW00592": "Sanjay Gangola",
      //     "PW00629": "Mokshendra Singh Tanwar",
      //     "PW00457": "Jogender Singh",
      //     "PW00458": "Rahul Kumar Singh",
      //     "PW00461": "Karan Sahni",
      //     "PW00467": "Dilip Khurana",
      //     "PW00475": "Khushboo Kumari",
      //     "PW01420": "Shailja Charan",
      //     "PW01687": "Manjar Kumar",
      //     "PW01952": "Asish Kumar Mohapatra",
      //     "PW02670": "Vinod Pathak",
      //     "PW03396": "Asif Saifi",
      //     "PW07946": "Gourav",
      //     "PW08521": "Param Khanna",
      //     "PW08652": "Kushagra Pahwa",
      //     "PW08947": "Vishal Verma",
      //     "PW10234": "Puneet Trehan",
      //     "PW11659": "Deepak Gupta",
      //     "PW12261": "Sushant Kalra",
      //     "PW13859": "Gurvinder Singh",
      //     "PW14905": "Amit Ahuja",
      //     "PW16483": "Puneet Ranjan",
      //     "PW16500": "Tarun Joshi",
      //     "PW18603": "Mohit Arora",
      //     "PW19540": "Saksham Chhabra",
      //     "PW22156": "Surabhi Kishore",
      //     "AU00720": "test",
      //     "PW00616": "Swaraj kumar Swain",
      //     "PW01155": "Bhaskar Mishra",
      //     "PW00959": "Nishant Kumar Raj",
      //     "PW00896": "Gulshan Kumar Sharma",
      //     "PW01162": "Dhanajay Singh Tomar",
      //     "PW16237": "Leena Shetty",
      //     "PW00456": "Preeti Sinha",
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",
      //     "PW29459": "Apoorv Gupta",
      //   }
      // },
      // 'Term-2': {
      //   "DashboardId": "58469714-c3a8-499a-a8f1-aadc0b194e16",
      //   "users": {
      //     "PW12926": "Umang",
      //     "AU00720": "test",
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",

      //   }
      // },
      // 'Health-Renewal': {
      //   "DashboardId": "d19f8983-fea8-4b57-9f5b-c274e7a54248",
      //   "users": {
      //     "PW00603": "GG",
      //     "AU00720": "test",
      //     "PW08726": "Puneet Bhatia",
      //     "PW01420": "Shailja",   
      //     "ET01557": "Amit Chouhan",
      //     "PW03120": "Harsh Sachdeva",     
      //     "ET01938": "Siddharth Singhal",     
      //     "PW24927": "Aditya Goyal",     
      //     "PW26851": "Prakash Sawhney",     
      //     "PW06937": "Sajjan Singh",     
      //     "PW00454": "Shubhank Chauhan",
      //     "PW00565": "ANOOP BIND",
      //     "PW00556": "Kanhaiya Pathak",
      //     "PW06940": "Abhishek Thakur",
      //     "PW06549": "Geeta Joshi",
      //     "PW04250": "Mahesh",
      //     "PW05831": "Subrata Paul",
      //     "PW18444": "Rajat Sharma",
      //     "PW06600": "Kiran Uniyal",
      //     "PW03258": "Sameer Mathur",
      //     "PW14585": "Shah Fahad Nafees",
      //     "PW07872": "Shiv Pratap Singh",
      //     "PW31828": "Ashish Gupta",
      //     "PW04119": "Naeem Khan",
      //     "PW03272": "Mohit Dhingra",
      //     "PW08888": "Harshi katiyal",
      //   }
      // },
      // 'Health Chat Team - Login Logout Times': {
      //   "DashboardId": "3f2e9dc5-24ed-4534-ac89-0638e76262fc",
      //   "users": {
      //     "AU00720": "test",
      //     "PW30481": "Rohan Gupta",
      //     "PW00231": "Roberta Davies",
      //     "PW22156": "Surabhi Kishore",
      //     "PW00637": "Anand Sinha",
      //     "ET01614": "Khyati Choudhary",
      //   }
      // },
    // };
  }

  
  componentDidMount() {
    // console.log("getuser", getuser());
    // let ecode = getuser().EmployeeId;
    // this.ecode = ecode;
    // let mapping = this.mapping;
    // for (let department in mapping) {
    //   debugger;
    //   if (mapping[department]['users'][ecode]) {
    //     this.setState((prevState) => ({
    //       QuicksightUrl: [...prevState.QuicksightUrl, {
    //         department
    //       }]
    //     }));
    //   }
    // }
    let dataToSend = {
      root: "Settings",
      cols: JSON.stringify(["value"]),
      con: {application:"matrixdashboard_1",key:"quicksightvisualization" },
      c: "M",
    }
    this.props.GetCommonData(dataToSend)
    
  }

  componentWillReceiveProps(nextProps) {
    //this.setState({ prevprop: nextProps });
    //console.log(oldProps);
    //console.log("getuser", getuser());
    let ecode = getuser().EmployeeId;
    this.ecode = ecode;
    if (!nextProps.CommonData.isError  && this.state.QuicksightUrl.length==0) {
        if (nextProps.CommonData.Settings) {
          let data = nextProps.CommonData.Settings[0].value
          this.setState({ mapping: data });
          for (let department in data) {
            if (data[department]['users'][ecode]) {
              this.setState((prevState) => ({
                QuicksightUrl: [...prevState.QuicksightUrl, {
                  department
                }]
              }));
            }
          }
        }
    }
  }

  handleOpen(department) {
    console.log('----------department', department);
    GetDashboardUrl({
      "userid": this.ecode,
      "DashboardId": this.state.mapping[department]['DashboardId']
    }, (result) => {
      console.log('-----result', result);
      if (result && result.data && result.data.QuicksightUrl) {
        window.open(result.data.QuicksightUrl, '_blank');
      }
    });
    try {
      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "InsertAgentIncentiveLog",
        c: "L",
        params: [{
          UserId: getuser().UserID,
          SuperGroupId: 0,
          ProductId: 0,
          incentiveMonth: '03-03-2021',
          Source: 'MatrixDashboard',
          PageName: 'QuickSight',
          EventName: department

        }]
      }, function () {

      })
    }
    catch (e) { }
  }

  render() {
    const { QuicksightUrl, PageTitle } = this.state;
    console.log('-------QuicksightUrl-------', QuicksightUrl);
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h3">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  {QuicksightUrl && QuicksightUrl.map((item) => {
                    return <div>
                      <h5>{item.department}</h5>
                      <Button onClick={() => this.handleOpen(item.department)} variant="primary" style={{ marginBottom: "3.5%" }}>Click to view dashboard</Button>
                    </div>
                  })}
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData


  }
)(QuickSightVisualization);