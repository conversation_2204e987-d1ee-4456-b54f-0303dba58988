
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import moment from 'moment';
import config from '../config'

import { GetCommonData, InsertData, UpdateData, DeleteData, GetChatMessages} from "../store/actions/CommonAction";

import { If, Then } from 'react-if';

import { connect } from "react-redux";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownListMongo';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

const Message = (props) => {
    console.log('-----props' , props);
    let time = new Date(props.time);
    let mesg = '';
    if(props.msg.indexOf("http://") == 0 || props.msg.indexOf("https://") == 0) {
      mesg= <a href={props.msg} target="blank" className="date-line"><strong>{props.msg}</strong></a> ;
    } else {
        mesg= props.msg;
    }
    return (<div className="chatPattern">
        <b>{props.name}</b> <span className="msg-time"> {time.toLocaleTimeString()}</span>
        <br/>
        <p>{mesg}</p>
    </div>)
} 

class chatRoomHistory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
    messages: [],
    PageTitle: "Chat History"
    }
}


  componentDidMount() {
    let rid = getUrlParameter("rid");
    let dep = getUrlParameter("dep");
    
    GetChatMessages(rid, dep, (result) => {
        console.log('-----result', result.data.data);
        if(Array.isArray(result.data.data)){
          this.setState(() => ({
            messages: result.data.data
          }));
        }
    });
  }

  render() {
    const { messages, PageTitle } = this.state;
    console.log('-------messages-------', messages); 
    let tdate; 
    if(messages && messages[0]){
        tdate = new Date(messages[0].ts); 
        tdate = tdate.toLocaleDateString();
    }
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="8">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h3">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  {messages.length==0 ? <h6>no message found</h6> :
                    <> 
                    <h6>Start of conversation {tdate&&<span>({tdate})</span>}</h6>
                      {/* <ul aria-live="polite"> */}
                          <div className="main-section">
                              {messages && messages.map((message) => {
                                  return (
                                      <Message
                                          msg={message.msg}
                                          name={message.name}
                                          time={message.ts}
                                      />
                                  )
                              })}
                          </div>
                      {/* </ul> */}
                    </>}
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    DeleteData
  }
)(chatRoomHistory);