const {  GetParsedConfigFromCache } = require('./modules/common/CommonMethods');

async function IpAuth(req, res, next) {
  try {
   
    if(process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      const SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      // console.log('Inside Auth ENV: ', process.env.ENVIRONMENT_MTX_DASH)
    } else {
      const SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
    }
    let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;
    let whiteListIPs =  await matrixdb.collection('WhiteListIPs').find({}).toArray();
    let IPLists = whiteListIPs.map((item) => item.ip);
    // console.log('Client IP Auth: ', IP);
    // console.log('whiteListIPs: ', IPLists);

    if(IPLists.includes(IP)) {
      // console.log('auth verified');
      next();
    } else {
      res.status(401).json({
        status: 401,
        message: 'Forbidden'
      })
    }

  } catch (err) {
    console.log('Inside IP Auth', err);
    res.status(500).json({
      status: 500,
      message: err.toString()
    })
  }
}

module.exports = {
  IpAuth: IpAuth,

}