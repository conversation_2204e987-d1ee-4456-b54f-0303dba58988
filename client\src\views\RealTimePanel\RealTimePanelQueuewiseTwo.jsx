import React from "react";
import {
  GetMySqlData
} from "../../store/actions/CommonMysqlAction";
import {
  GetCommonData, GetCommonspData, GetRealTimeAgentData, GetRealTimeTotalData, GetRealTimeQueueData, GetFileExists,
  GetAwsRecordingUrl, GetRecordingName, GetWaitingAssignedCallData, getqueuetwo
} from "../../store/actions/CommonAction";
import { getUrlParameter, hhmmss, getuser } from '../../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from '../Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { Web } from "sip.js";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  addRecord, UpdateData, gaEventTracker
} from "../../store/actions/CommonMongoAction";
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col,
  Table
} from "reactstrap";
import { Form, Modal, Glyphicon } from 'react-bootstrap';
import { If, Then, Else } from 'react-if';
import _, { bind } from 'underscore';

class RealTimePanelQueuewise extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "RealTime Panel",
      AgentData: [],
      TotalData: [],
      items: [],
      key: "ALL",
      onBarge: false,
      SelectedSupervisors: [],
      winactive: 0,
      BargeWith: "",
      queues: [],
      QueueData: [],
      MoreInfoData: [],
      OtherInfoData: [],
      waitingassignedCallsData: [],
      contextName: null,
      showModal: false,
      showMoreInfoModal: false,
      showWaitingAssignedModal: false,
      addClass: "fa fa-play-circle",
      queueServerIp: "",
      clickedQueue: "",
      wraptime: "",
      showRefreshTime: false,
      lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      online: true,
      Leads: null,
      body: [],
      AgentCode: "",
      AgentName: "",
      currLeadId: ""
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    this._handleKeyDown = this._handleKeyDown.bind(this);
    this.saveBargingLogs = this.saveBargingLogs.bind(this);
    //this.bargecall = this.bargecall.bind(this);
    this.userAgent = null;
    this.winactive = 0;
    this.schdular = null;

    this.unansweredlist = [
      { name: "Callid", selector: "callid" },
      { name: "leadid", selector: "leadid" },
      { name: "Calltime", width: "150px", cell: row => <div className="calltime">{row.calltime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.calltime}</Moment> : "N.A"}</div> },
      { name: "Status", cell: row => <div className="abandon">{this.state.ModalTitle.indexOf("Unanswered") > -1 ? "ABANDON" : "ANSWERED"}</div> },
      { name: "Waiting Time", selector: "waittime" },
      {
        name: "EnterTime", cell: row => <div className="calltime">{row.entertime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.entertime}</Moment> : "N.A"}</div>,
        width: "150px",
      },
      {
        name: "Main Queue EnterTime", cell: row => <div className="calltime">{row.mainqueueentertime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.mainqueueentertime}</Moment> : "N.A"}</div>,
        width: "150px",
      },
      { name: "IVR Time", selector: "ivrtime" },
      { name: "Call Type", selector: "call_type" },
      {
        name: "Listen", cell: row =>
          <div className="listenUserDetails">
            {this.getHtmlListen(row)}
          </div>
      },
      {
        name: "More Info", cell: row => <div className="moreinfo"><a onClick={(e) => this.clickMoreinfo(e, row)} className="detailsinfo">
          <i className="fa fa-eye"></i></a>
        </div>
      },
    ];
    this.moreinfolist = [
      { name: "callid", selector: "callid" },
      { name: "leadid", selector: "leadid" },
      { name: "Calldate", width: "150px", cell: row => <div className="calldate">{row.calldate ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss">{row.calldate}</Moment> : "N.A"}</div> },
      { name: "agentno", selector: "agentno" },
      { name: "agentid", selector: "agentid" },
      { name: "isassignedagent", selector: "isassignedagent" },
      { name: "duration", selector: "ringtime" },
      {
        name: "disposition",
        //selector: "custom_disposition",
        cell: row => this.state.ModalTitle.indexOf("Unanswered") > -1 && row.custom_disposition === "ANSWERED" ? "NO ANSWER" : row.custom_disposition
      },
      {
        name: "Listen", cell: row => (['Playback', 'Hangup'].indexOf(row.custom_disposition) > -1) ? 'No file found' :
          <div className="listen">
            {this.getHtmlListenMoreInfo(row)}
          </div>
      },
      // {
      //   name: "Listen", cell: row =>
      //     <div className="listen">
      //       {this.CreateRecordingURL(row)}
      //     </div>
      //}
    ];
    this.waitingassignedCallsList = [
      { name: "EmpId", selector: "empid" },
      { name: "CallId", selector: "callid" },
      { name: "LeadId", selector: "leadid" },
      { name: "CallTime", cell: row => <div className="calltime">{row.calltime ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss">{row.calltime}</Moment> : "N.A"}</div> },
      { name: "CallStatus", selector: "callstatus" },
      { name: "EnterTime", cell: row => <div className="calltime">{row.entertime ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss">{row.entertime}</Moment> : "N.A"}</div> },
      { name: "WaitingTime", selector: "waitingtime" },
      { name: "CallType", selector: "calltype" },
      { name: "Istransfer", selector: "istransfer" },
      { name: "QueueName", selector: "queuename" },
    ];
    this.columnlist = [
      {
        name: "Barging",
        selector: "Barging",
        sortable: true,
        width: "80px",
        cell: row =>
          <div className={row.Status == "BUSY" ? "" : "hide"} >
            <button onClick={(e) => this.bargecall(e, row)} className={row.Barge ? "hide" : "show"}><i className="fa fa-volume-up" aria-hidden="true"></i></button>
          </div>
      },
      {
        name: "Agent Code",
        selector: "AgentCode",

        sortable: true,
      },
      {
        name: "Status",
        selector: "Status",
        sortable: true,

        cell: row => <div className={this.displayStatus(row) + " RealtimeStatus"}>{this.displayStatus(row)}</div>
      },
      
      {
        name: "Agent Name",
        selector: "AgentName",
        sortable: true,
      },
      {
        name: "TL Name",
        selector: "TLName",
        sortable: true,
      },
      {
        name: "Call Type",
        selector: "CallType",
      },
      {
        name: "Lead Id",
        selector: "LeadId",
        sortable: true,
      },
      {
        name: "DIDNo",
        selector: "DIDNo",

        sortable: true,
      },

      // {
      //   name: "Last Updated On",
      //   selector: "LastUpdatedOn",
      //   width: "130px",
      //   sortable: true,
      //   cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment format="HH:mm:ss A">{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      // },
      {
        name: "Since",
        selector: "LastUpdatedOn",
        sortable: true,
        width: "130px",
        cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment fromNow>{this.getLastUpdatedOn(row)}</Moment> : "N.A"}</div>

      },
      {
        name: "T Calls",
        selector: "TotalCalls",
        width: "70px",
        sortable: true,
      },
      {
        name: "U Dials",
        selector: "UniqueDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "C Dials",
        selector: "ConnectedDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "T TalkTime",
        selector: "TotalTalkTime",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.TotalTalkTime),
      },
      {
        name: "Is Mobile",
        selector: "IsWFH",
        cell: row => <div>{row.IsWFH ? "true" : "false"}</div>
      },
      {
        name: "D.C.",
        selector: "CallingCompany",

        sortable: true,
      },
      {
        name: "Asterisk_Url",
        selector: "Asterisk_Url",
        width: "120px",
        sortable: true,
      },
    ];
  }

  componentWillReceiveProps(nextProps) {
    //debugger;
    if ((nextProps.queues && nextProps.queues != this.props.queues)) {

      const user = getuser();
      this.setState({ SelectedSupervisors: [getUrlParameter("m") == "" ? user.EmployeeId : getUrlParameter("m")] }, function () {

        this.getqueue();
        this.UserList();
        this.queueList();
      }.bind(this));

    }


    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //if (!this.state.onBarge) {
        //if ((new Date()).getHours() >= 21 || (new Date()).getHours() < 9) {
        if (this.state.winactive == 1 || document.hasFocus()) {
          //this.getqueue();
          this.UserList();
          this.totalList();
          this.queueList();
        }
        // }
        // else {
        //   this.UserList();
        //   this.totalList();
        // }
        //}
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }

  }

  componentDidMount() {

    const user = getuser();
    this.setState({ SelectedSupervisors: [getUrlParameter("m") == "" ? user.EmployeeId : getUrlParameter("m")] }, function () {

      this.getqueue();
      this.UserList();
      this.queueList();
    }.bind(this));


    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //if (!this.state.onBarge) {
        //if ((new Date()).getHours() >= 21 || (new Date()).getHours() < 9) {
        if (this.state.winactive == 1 || document.hasFocus()) {
          //this.getqueue();
          this.UserList();
          this.totalList();
          this.queueList();
        }
        // }
        // else {
        //   this.UserList();
        //   this.totalList();
        // }
        //}
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }

  }

  getHtmlListenMoreInfo(row) {
    // <span id={"span_" + row.row_num}><audio src={url} id={"audio" + "_" + row.row_num}></audio>
    //       <i className={this.state.addClass} id={"play" + row.row_num} onClick={(e) => this.play(row.row_num, e)}></i>
    //     </span>
    return (<span id={"span_" + row.row_num} onClick={(e) => this.CreateMoreInfoRecordingURL(e, row)}>
      <i class="fa fa-play-circle listen"></i>
    </span>)

  }

  getHtmlListen(row) {
    //debugger;
    //var diff = (new Date() - new Date(row.hanguptime)) / 1000;
    var now = moment().format("YYYY-MM-DD HH:mm:ss");
    var then = moment.utc(new Date(row.hanguptime)).format("YYYY-MM-DD HH:mm:ss");

    //var then = moment(row.hanguptime).format("YYYY-MM-DD HH:mm:ss");
    var diff = (moment(now, "YYYY-MM-DD HH:mm:ss").diff(moment(then, "YYYY-MM-DD HH:mm:ss"))) / 1000;

    //if (row.orig_callid == 0){
    //  return <i class="fa fa-info-circle infotooltip" data-toggle="tooltip" title="Transferred Call- Recording available next day on AI Portal" aria-hidden="true"></i>
    //}
    if (row.call_type == 1 && diff < 600) {
      return <i class="fa fa-info-circle infotooltip" diff={diff} data-toggle="tooltip" title="Transferred Call- Recording available after 10 minutes of Call" aria-hidden="true"></i>
    }
    else if (diff < 600) {
      return <i class="fa fa-info-circle infotooltip" data-toggle="tooltip" title="IB - Recording available after 10 minutes of Call" aria-hidden="true"></i>
    } else {
      return (<span id={"span_" + row.row_num} onClick={(e) => this.CreateNCallersRecordingURL(e, row)}>
        <i class="fa fa-play-circle listen"></i>
      </span>)
    }
  }

  CreateMoreInfoRecordingURL(e, row) {
    var audio = document.getElementById('audio2');
    var number = row.row_num;
    if (audio.paused) {
      document.getElementById('span_' + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      let userfield = row.userfield;
      let dstchannel = row.dstchannel;
      let date = moment(new Date(userfield)).format("DD-MM-YYYY");
      let hour = moment(new Date(userfield)).format("H");
      let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
      let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
      let callid = row.callid;
      console.log(date, hour, datetime, phoneNo, callid);
      let recfilename = "recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
      //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";


      if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
        document.getElementById('span_' + number).innerHTML = 'No File Found';
        return;
      }
      //debugger;
      GetAwsRecordingUrl(recfilename, 'asterisk-log', function (results) {

        console.log("results", results);
        if (results.data.status == 200) {
          let url = results.data.data;
          audio.src = results.data.data;
          document.getElementById('span_' + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
          GetFileExists(url, function (params) {
            //debugger;
            if (params && params.status && params.status != 404) {
              audio.onloadedmetadata = function () {
                console.log(audio.duration)
                //setTimeout(function () {
                audio.play();
                console.log(audio.duration);
                if (audio.paused == false && audio.duration > 0 && audio.duration != 'Infinity' && audio.duration != 'NaN') {
                  document.getElementById('span_' + number).innerHTML = '<i class="fa fa-stop-circle listen"></i>';

                  audio.onended = function () {
                    document.getElementById('span_' + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
                  };

                } else {
                  document.getElementById('span_' + number).innerHTML = 'No File Found';
                }
                // }.bind(this), 500);
              };
            } else {
              try {
                document.getElementById("span_" + row.row_num).innerHTML = "File not found";
              } catch (e) {
                //console.log('error', e);        
              }
            }
          });


        } else {
          document.getElementById('span_' + number).innerHTML = 'No File Found';
        }
      }.bind(this));



    } else {
      //debugger;
      audio.pause();
      audio.currentTime = 0;
      document.getElementById('span_' + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';

    }

  }
  CreateNCallersRecordingURL(e, row) {
    var audio = document.getElementById('audio1');
    var number = row.row_num;
    if (audio.paused) {

      let calldataid = row.orig_callid;
      this.state.AwsRecordingUrl = '';
      document.getElementById('span_' + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      //debugger;
      GetRecordingName(calldataid, function (results) {
        //console.log("results", results);   
        var CheckFile = '';
        var extension = '';
        if (results.data.file_available == true) {
          extension = '.wav';
          CheckFile = results.data.file_available;
        } else {
          extension = '-out.wav';
          CheckFile = results.data.agent_file;
        }

        if (CheckFile == true) {
          //this.setState({ RecordingName: results.data.record });
          GetAwsRecordingUrl(results.data.record + extension, 'newcctecbuckt', function (results) {
            //debugger;
            //console.log("results", results);
            if (results.data.status == 200) {
              this.setState({ AwsRecordingUrl: results.data.data });
              audio.src = results.data.data;
              document.getElementById('span_' + number).innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

              audio.onloadedmetadata = function () {
                //console.log(audio.duration)
                //setTimeout(function () {
                audio.play();
                //console.log(audio.duration);
                if (audio.paused == false && audio.duration > 0 && audio.duration != 'Infinity' && audio.duration != 'NaN') {
                  document.getElementById('span_' + number).innerHTML = '<i class="fa fa-stop-circle listen"></i>';

                  audio.onended = function () {
                    document.getElementById('span_' + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';
                  };

                } else {
                  document.getElementById('span_' + number).innerHTML = 'No File Found';
                }
                // }.bind(this), 500);
              };

            } else {
              document.getElementById('span_' + number).innerHTML = 'No File Found';
            }
          }.bind(this));
        } else {
          document.getElementById('span_' + number).innerHTML = 'No File Name Found';
        }

      }.bind(this));

    } else {
      //debugger;
      audio.pause();
      audio.currentTime = 0;
      document.getElementById('span_' + number).innerHTML = '<i class="fa fa-play-circle listen"></i>';

    }

  }

  // CreateRecordingURL(row) {debugger;

  //   let userfield = row.userfield;
  //   let dstchannel = row.dstchannel;
  //   let date = moment(new Date(userfield)).format("DD-MM-YYYY");
  //   let hour = moment(new Date(userfield)).format("H");
  //   let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
  //   let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
  //   let callid = row.callid;
  //   console.log(date, hour, datetime, phoneNo, callid);
  //   let recfilename = date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";
  //   //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";


  //   if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
  //     return <span>File not found</span>
  //   }

  //  GetFileExists(url, function (params) {
  //    //debugger;
  //    if (params && params.status && params.status != 404) {
  //    } else {
  //      try {
  //        document.getElementById("span_" + row.row_num).innerHTML = "File not found";
  //      } catch (e) {
  //       //console.log('error', e);        
  //     }
  //   }
  // });

  // //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/12-05-2020/10/1589258668.10883-20200512101428-07509883158.wav";
  //return <span id={"span_" + row.row_num}><audio src={url} id={"audio" + "_" + row.row_num}></audio>
  //  <i className={this.state.addClass} id={"play" + row.row_num} onClick={(e) => this.play(row.row_num, e)}></i>
  //</span>;
  // }
  saveBargingLogs(body) {
    this.props.addRecord({
      root: "BargeLogs",
      body: body
    });
  };
  totalList() {
    let context = getUrlParameter("c");
    if (context != "") {
      GetRealTimeTotalData(context, function (results) {
        this.setState({ TotalData: results.data });
      }.bind(this));
    }
  }

  queueList() {
    // debugger;
    let queues = this.state.queues;

    var queuestring = queues.map(function (val) {
      return val.queuename;
    });

    var serverip = queues.map(function (val) {
      return val.server_ip;
    });

    //var contextName = this.state.contextName ?? queuestring[0];

    //this.setState({ queueServerIp: serverip[0], contextName: contextName });
    this.setState({ queueServerIp: serverip[0] });

    GetRealTimeQueueData(queuestring.join(","), function (results) {
      //console.log("results", results);
      this.setState({ QueueData: results.data });
    }.bind(this));
  }
  getqueue() {
    //debugger;
    // let type = getUrlParameter("type") == "" ? "sales" : getUrlParameter("type");
    // let proid = getUrlParameter("product") == "" ? "1" : getUrlParameter("product");
    let is_mobile = getUrlParameter("IsMobile") == "" ? "0" : getUrlParameter("IsMobile");
    let is_claim = getUrlParameter("IsClaim") == "" ? "0" : getUrlParameter("IsClaim");
    // let queues =  getUrlParameter("queues");
    let type = this.props.type == "" ? "sales" : this.props.type;
    let proid = this.props.product == "" ? "1" : this.props.product;
    let queues = this.props.queues;

    // this.props.GetMySqlData({
    //   root: "getqueue",
    //   ProductType: type,
    //   ProductId: proid,
    //   IsMobile: is_mobile,
    //   IsClaim:  is_claim,
    // });

    // this.props.GetMySqlData({
    //   root: "getqueuetwo",
    //   ProductType: type,
    //   ProductId: proid,
    //   IsMobile: is_mobile,
    //   IsClaim: is_claim,
    //   queues: queues,
    // }, function (result) {
    //   //console.log("result",result);
    //   this.setState({ queues: result.data.data[0] });
    // }.bind(this));


    getqueuetwo(type, proid, queues, function (result) {

      if (result) {
        let item = Object.values(result.data.data)

        this.setState({ queues: item });
      }
    }.bind(this));
  }

  UserList() {
    let managerId = getUrlParameter("m");
    let context = this.state.contextName ?? getUrlParameter("c");
  
    if (context === 'breakinmotor') {
      context = 'BreakinMotor';
    }
  
    const queues = this.state.queues;
    const matchedQueue = queues.find(
      (queue) => queue.queuename.toLowerCase() === context?.toLowerCase()
    );
  
    let newContext = '';
    if (matchedQueue) {
      this.setState({ wraptime: matchedQueue.wraptime });
      newContext = matchedQueue.agentTrackerQueues;
    }
  
    const user = getuser();
  
    // If supervisors are selected, override managerId
    if (this.state.SelectedSupervisors.length > 0) {
      managerId = this.state.SelectedSupervisors.join();
    }
  
    if (!managerId && !context) {
      managerId = user.EmployeeId;
      this.setState({ SelectedSupervisors: [managerId] });
    }
  
    // Fetch real-time agent data if either managerId or context is available
    if (managerId || context) {
      GetRealTimeAgentData(managerId, newContext, (results) => {
        if (!results.hasOwnProperty('data') && String(results).includes('Error')) {
          this.setState({ online: false });
        } else {
          this.setState({
            AgentData: results.data,
            lastRefreshTime: moment().format("YYYY-MM-DD HH:mm:ss"),
            online: true
          });
        }
      });
    }
  }

  handleShow(e) {
    this.setState({ SelectedSupervisors: e.SelectedSupervisors });
  }

  componentWillUnmount() {
    clearInterval(this.schdular);
    if (this.userAgent != null)
      this.userAgent.hangup();
  }

  changeContext(e, row) {
    this.setState({ contextName: row.context, showRefreshTime: true });
  }

  clickUnanswered(e, row) {
    var unsweredcol = this.unansweredlist;
    unsweredcol = unsweredcol.filter((res) => res.name !== "Assigned Agent");

    this.unansweredlist = [
      ...unsweredcol.slice(0, 8),
      { name: "Assigned Agent", selector: "assignedagent" },
      ...unsweredcol.slice(8)
    ];
    let queuename = row.context;
    this.setState({ clickedQueue: queuename });
    this.props.GetMySqlData({
      root: "unanswered",
      queues: queuename,
    }, function (result) {
      ////debugger;
      this.setState({ showModal: true, UnansweredData: result?.data?.data, ModalTitle: "Unanswered Calls" });
    }.bind(this));

  }
  clickanswered(e, row) {
    this.unansweredlist = this.unansweredlist.filter((res) => res.name !== "Assigned Agent");
    let queuename = row.context;
    this.setState({ clickedQueue: queuename });

    this.props.GetMySqlData({
      root: "answered",
      queues: queuename,
    }, function (result) {
      ////debugger;
      this.setState({ showModal: true, UnansweredData: result?.data?.data, ModalTitle: "Answered Calls" });
    }.bind(this));

  }

  clickWaitingAssignedCalls(e, row) {
    let queuename = row.context;
    GetWaitingAssignedCallData(queuename, function (result) {
      this.setState({ showWaitingAssignedModal: true, waitingassignedCallsData: result.data.data, ModalTitle: "Waiting Assigned Calls" });
    }.bind(this));

  }

  clickMoreinfo(e, row) {//debugger;
    let uniqueid = row.callid;
    let queues = this.state.queues;
    let queue = this.state.clickedQueue;

    const queueData = queues.find((item) => item.queuename.toLowerCase() === queue.toLowerCase());

    // if (queue == 'breakinmotor'){
    //   var queueServerIp = "**********";
    // }else{
    var queueServerIp = queueData.server_ip;
    // }

    this.props.GetMySqlData({
      root: "moreinfo",
      uniqueid: uniqueid,
      queueServerIp: queueServerIp,
    }, function (result) {////debugger;

      this.setState({ showMoreInfoModal: true, MoreInfoData: result?.data?.data });
    }.bind(this));

    if (row.recserverip && row.assignagentcallid) {
      this.props.GetMySqlData({
        root: "getOtherInfoFromCdr",
        connectionIp: row.recserverip,
        callId: row.assignagentcallid,
      }, function (result) {
        this.setState({ OtherInfoData: result?.data?.data });
      }.bind(this));
    } else {
      this.setState({ OtherInfoData: [] });
    }

  }

  getLastUpdatedOn(row) {
    if (this.state.wraptime && row.Status == "IDLE") {
      let lastupdatetime = moment(row.LastUpdatedOn).format('YYYY-MM-DD HH:mm:ss');
      let currentTime = moment(row.ServerTime).format('YYYY-MM-DD HH:mm:ss');//moment().format("YYYY-MM-DD HH:mm:ss");
      var diff = (moment(currentTime, "YYYY-MM-DD HH:mm:ss").diff(moment(lastupdatetime, "YYYY-MM-DD HH:mm:ss"))) / 1000;

      if (diff > this.state.wraptime) {
        let lastsincetime = moment(row.LastUpdatedOn).add(this.state.wraptime, 'seconds').format('YYYY-MM-DD HH:mm:ss');
        console.log('agent', row.AgentCode, 'since', lastsincetime, 'currenttime', currentTime);
        console.log('agent', row.AgentCode, moment(lastsincetime).fromNow());
        return lastsincetime;
      } else {
        return row.LastUpdatedOn;
      }

    } else {
      return row.LastUpdatedOn;
    }

  }

  displayStatus(row) {
    // var diff = (new Date() - new Date(row.LastUpdatedOn)) / 1000;
    // if (diff > 60 && row.Status == "IDLE") {
    //   return "Away";
    // } else return row.Status;
    let BargeWithAgent = this.state.BargeWithAgent;
    if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status != "BUSY") {

      // if (this.userAgent) {
      //   this.userAgent.hangup();
      //   this.userAgent = null;        
      // }
      // this.setState({ onBarge: false, BargeWith: "" });
      this.unbargecall();
    }
    else if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status == "BUSY") {
      // if (this.userAgent == null) {
      //   this.bargecall(null, BargeWithAgent);
      // }
    }
    console.log('wraptime', this.state.wraptime);
    let status = row.Status;
    if (this.state.wraptime && row.Status == "IDLE") {
      let lastupdatetime = moment(row.LastUpdatedOn).format('YYYY-MM-DD HH:mm:ss');
      let currentTime = moment(row.ServerTime).format('YYYY-MM-DD HH:mm:ss');//moment().format("YYYY-MM-DD HH:mm:ss");
      var diff = (moment(currentTime, "YYYY-MM-DD HH:mm:ss").diff(moment(lastupdatetime, "YYYY-MM-DD HH:mm:ss"))) / 1000;

      if (diff < this.state.wraptime) {
        console.log('agent', row.AgentCode, 'lastupdatetime', lastupdatetime, 'currenttime', currentTime, 'diff', diff);
        status = "OnWrap";
      } else {
        status = row.Status;
      }

    }
    return status.toUpperCase()
  }

  unbargecall(e) {
    if (this.userAgent) {
      try {
        if(this.state.body != []){
        let UnbargeTimestamp = new Date();
        let unbargeLog = {
          Agent: this.state.body.Agent,
          BargedBy: getuser().EmployeeId,
          AgentName: this.state.body.AgentName,
          LeadID: sessionStorage.getItem("BargeWithLeadId"),
          Type: "Unbarge",
          Timestamp: UnbargeTimestamp,
        }
        gaEventTracker("BargeCall", JSON.stringify(unbargeLog), getuser().EmployeeId);
      }
      
      }
      catch (e) {

      }
      this.userAgent.hangup();
      this.userAgent = null;


    }
    sessionStorage.setItem("BargeWith", "");
  }


  bargecall(e, row) {
    try {
      if (this.userAgent) {
        this.userAgent.hangup();
        this.userAgent = null
        sessionStorage.setItem("BargeWith", row.AgentCode);
      
      }

      //if (!this.state.onBarge) {

      let user = {
        Display: getuser().EmployeeId,
        User: getuser().EmployeeId,
        Pass: getuser().EmployeeId,
        Realm: row.Asterisk_Url,
        WSServer: "wss://" + row.Asterisk_Url + ":8089/ws"
      }
      this.state.AgentName = row.AgentName
      this.state.AgentCode = row.AgentCode
      this.LoginAsteriskServer(user, function () {
        setTimeout(function () {

          if (this.userAgent) {
            let target = "*222" + row.AgentCode;
            if (row.IsWFH || (row.CallingCompany == "WFH")) {
              target = "*222" + row.DIDNo;
            }

            // if (row.CallingCompany == "WFH" || row.CallingCompany == "KNOWLARITY") {
            //   target = "*222" + row.DIDNo;
            // }

            this.userAgent.call(target);
          }


          this.setState({ onBarge: true, BargeWith: row.AgentCode, BargeWithAgent: row });

          // setTimeout(function () {
          //   //this.forceUpdate();

          // }.bind(this), 500);

        }.bind(this), 1000);
      }.bind(this), function () {
        document.getElementById(row.AgentCode).checked = false;
      }.bind(this));
     
      
        let bargeTimestamp = new Date();
        let bargeLog = {
          Agent: row.AgentCode,
          BargedBy: getuser().EmployeeId,
          AgentName: row.AgentName,
          LeadID: row.LeadId,
          Type: "Barge",
          Timestamp: bargeTimestamp,
        }
        // setting leadid
        sessionStorage.setItem("BargeWithLeadId", row.LeadId);
        this.setState({body: bargeLog})
        gaEventTracker("BargeCall", JSON.stringify(bargeLog), getuser().EmployeeId);
      
    } catch (e) {

    }

  }



  LoginAsteriskServer(user, onsuccess, onerror) {

    if (user) {
      var config = {
        media: {
          remote: {
            //video: document.getElementById('remoteVideo'),
            // This is necessary to do an audio/video call as opposed to just a video call
            audio: document.getElementById('audioRemote')
          }
        },
        ua: {
          uri: user.User + '@' + user.Realm,
          wsServers: [user.WSServer],
          authorizationUser: user.Display,
          password: user.Pass
        }
      }

      if (Web) {
        this.userAgent = new Web.Simple(config);
        // this.userAgent = new Web.SimpleUser(config);

        //let remoteElem = document.getElementById('audioRemote');
        //let localElem = document.getElementById('audioLocal');
        this.userAgent.on('connected', function (e) {
          toast("Barging Connected!", { type: 'success' });
        });
        this.userAgent.on('disconnected', function (e) {

        });
        this.userAgent.on('registered', function (e) {
          if (onsuccess) {
            onsuccess();
          }

        });
        this.userAgent.on('registrationFailed', function (e) {
          toast("Make sure your VPN is connected!", { type: 'error' });
        });
        this.userAgent.on('unregistered', function (e) {
          toast("Dialer issue please contact administrator!", { type: 'error' });
          if (onerror) {
            onerror();
          }

        });
        this.userAgent.on('userMediaFailed', function (e) {

        });
        this.userAgent.on('userMediaRequest', function (e) {

        });
        this.userAgent.on('userMedia', function (e) {

        });
        this.userAgent.on('invite', function (e) {

        });
        this.userAgent.on('addStream', function (stream) {

        });
        this.userAgent.on('ended', function (stream) {

        });
      }
    }

    setTimeout(function () {
      if (this.userAgent && this.userAgent.ua && this.userAgent.ua.isRegistered() == false) {
        toast("Make sure your VPN is connected!", { type: 'error' });
      }
    }.bind(this), 10000);
    return this.userAgent;
  }
  // componentWillReceiveProps(nextProps) {
  //   if (!nextProps.CommonData.isError) {
  //     //this.setState({ queues: nextProps.CommonData["getqueue"] });
  //     //this.setState({ UnansweredData: nextProps.CommonData["unanswered"] });
  //   }
  // }

  play(number) {
    var audio = document.getElementById('audio_' + number);
    var icon = document.getElementById("play" + number);
    if (audio.paused) {
      audio.play();
      icon.classList.remove("fa-play-circle");
      icon.classList.add("fa-stop-circle");
      this.CheckAudioFinishedPlay(audio, icon);

    } else {
      audio.pause();
      audio.currentTime = 0
      icon.classList.remove("fa-stop-circle");
      icon.classList.add("fa-play-circle");
    }
  }

  CheckAudioFinishedPlay(audio, icon) {//debugger;
    audio.onended = function () {
      icon.classList.remove("fa-stop-circle");
      icon.classList.add("fa-play-circle");
    };
  }

  statuschange(e) {
    this.setState({ key: e.target.value });
  }

  filterdata(e) {

    let alldata = this.state.AgentData;

    // Map all data first to check HOLD status for blinking
    let processedData = alldata.map(element => {
        let diff = (new Date() - new Date(element.LastUpdatedOn)) / 1000;

        return {
            ...element,
            shouldBlink: element.Status.toUpperCase() === "HOLD" && diff > 60
        };
    });

    // If "ALL" is selected, return everything (including blinking HOLD rows)
    if (this.state.key === "ALL") {
        return processedData;
    }

    // If "AWAY" is selected, filter out IDLE agents with LastUpdatedOn > 60s
    if (this.state.key === "AWAY") {
        return processedData.filter(element => {
            let diff = (new Date() - new Date(element.LastUpdatedOn)) / 1000;
            return diff > 60 && element.Status === "IDLE";
        });
    }

    // Filter based on the selected status keys
    let conditionArray = this.state.key.split(',');
    return processedData.filter(element => conditionArray.includes(element.Status.toUpperCase()));
  }
  _handleKeyDown(e) {

    if (e.key === 'Enter') {
      this.setState({ SelectedSupervisors: [e.target.value] });
    }
  }

  _handleOnClick(e) {


    this.setState({ SelectedSupervisors: [document.getElementById("EmpId").value] });

  }

  renderTotalData() {


    let tr = [];
    let QueueData = this.state.QueueData;
    if (QueueData && QueueData.length > 0) {

      QueueData.forEach(element => {
        tr.push(<tr className={this.state.contextName == element.context ? "active" : ""}>
          <td>{<a onClick={(e) => this.changeContext(e, element)} data-myattribute="check" className="abutton clickMe">{element.context}</a>}</td>
          {/* <td>{element.answered}</td> */}
          <td>{<a onClick={(e) => this.clickanswered(e, element)} className="abutton">{element.answered}</a>}</td>
          <td>{<a onClick={(e) => this.clickUnanswered(e, element)} className="abutton">{element.unanswered}</a>}</td>
          <td>{element.customerschedulectc}</td>
          <td className={element.waitingibcalls > 0 ? "waiting_call_gt0" : ""}>{element.waitingibcalls}</td>
          <td className={element.waitingctccalls > 0 ? "waiting_call_gt0" : ""}>{element.waitingctccalls}</td>
          <td>{<a onClick={(e) => this.clickWaitingAssignedCalls(e, element)} className={element.waitingassignedcalls > 0 ? "waiting_call_gt0 abutton" : "abutton"}>{element.waitingassignedcalls}</a>}</td>
          <td>{element.totalcalls}</td>
        </tr>)
      });
      return <Table responsive>
        <thead className="text-primary">
          <tr>
            <th>context</th>
            <th>answered</th>
            <th>unanswered</th>
            <th>Customer_schdeuledIBCTC</th>
            <th>waitingibcalls</th>
            <th>waitingctccalls</th>
            <th>waitingassignedcalls</th>
            <th>totalcalls</th>
          </tr>
        </thead>
        <tbody>
          {tr}
        </tbody>
      </Table>
    }
    else {
      return null;
    }
  }

  getCountStatus(data) {
    var onwrap = document.querySelectorAll('.ONWRAP').length;
    var uniqueStatus = _.countBy(data, "Status");
    let status = Object.entries(uniqueStatus).map(([key, value], index) => {
      if (key === 'IDLE') value = value - onwrap;
      return (<div>{key + " : " + value + "   "}</div>);
    })
    if (onwrap > 0) {
      status.push(<div>{'ONWRAP : ' + onwrap + "   "}</div>)
    }
    return status;
  }

  render() {
    const columns = this.columnlist;

    const unansweredcolumns = this.unansweredlist;
    const waitingassignedCallsList = this.waitingassignedCallsList;
    const moreinfocolumns = this.moreinfolist;

    const totalcolumns = this.totalcolumnlist;
    const data = this.filterdata();
    const managerid = getUrlParameter("m");
    const context = getUrlParameter("c");

    const { PageTitle, UnansweredData, MoreInfoData, OtherInfoData, waitingassignedCallsData, showRefreshTime, online } = this.state;

    //console.log('More info data', MoreInfoData);
    //console.log('Other info data', OtherInfoData);
    //Merge MoreInfoData and OtherInfoData to show in same datatable
    const modifiedMoreInfoData = [...MoreInfoData, ...OtherInfoData];

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">Queues</CardTitle>
                    </Col>
                    <Col md={9}>
                      <div style={(online) ? { display: "none" } : { display: "block", color: "red" }}>Internet is not working</div>
                    </Col>
                  </Row>

                </CardHeader>

                <CardBody>
                  <div className="queuetable">
                    {this.renderTotalData()}
                  </div>
                </CardBody>

              </Card>




            </Col>

          </Row>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>

                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <div>
                        <b>Queue Name </b> : {this.state.contextName}
                      </div>
                    </Col>

                    <Col md={3}>
                      <div className="input-group hide">

                        <Form.Control required type="text" name="EmpId" id="EmpId" onKeyDown={this._handleKeyDown} onChange={(e) => this.setState({ username: e.target.value })} value={this.state.username} placeholder={"Enter Supervisor Id"} />
                        <div className="input-group-append">
                          <button onClick={(e) => this._handleOnClick(e)} className="btn btn-primary input-group-button"><i className="fa fa-search" aria-hidden="true"></i></button>

                        </div>
                      </div>
                    </Col>
                    <Col md={2}>
                      <div className="form-group ">
                        <select class="form-select" onChange={this.statuschange}>
                        <option value="ALL">ALL</option>
                          <option value="IDLE">IDLE</option>
                          <option value="AWAY">AWAY</option>
                          <option value="BUSY">BUSY</option>
                          <option value="HOLD">HOLD</option>
                          <option value="UNAVAILABLE">UNAVAILABLE</option>
                          <option value="LUNCH,TEA,TRAINING,MEETING,DAY END,BIO BREAK">BREAK</option>
                          <option value="PAUSE">PAUSE</option>
                          <option value="AUTO LOGOUT,LOGOUT">LOGOUT</option>
                        </select>
                      </div>
                      {
                        (managerid == '' && context == ' ') ? <ManagerHierarchy handleShow={this.handleShow} value={/EmployeeId/g} ></ManagerHierarchy> : null
                      }
                      <button id="BargeWith" onClick={(e) => this.unbargecall(e)} className={this.state.BargeWith == "" ? "hide" : "btn btn-primary hangupwith show"} ><i className="fa fa-volume-off" aria-hidden="true"></i> Hang Up With: {this.state.BargeWith}</button>
                    </Col>

                  </Row>
                  <Row>
                    <Col md={5}>
                      <div style={(showRefreshTime) ? { display: "block" } : { display: "none" }}>
                        Last Refresh Time : {this.state.lastRefreshTime}
                      </div>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={12} >
                      <div className="count_status">
                        {this.getCountStatus(data)}
                      </div>
                    </Col>
                  </Row>
                </CardHeader>

                <CardBody>
                  {/* {this.renderTotalData()} */}

                  <div className="statusdata realTimepanelStatus">
                    <DataTable
                      columns={columns}
                      data={data}
                      pagination={false}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense
                      conditionalRowStyles={[
                        {
                          when: row => row.shouldBlink,
                          classNames: ['blink']
                        }
                      ]}
                    />

                  </div>
                </CardBody>

              </Card>
            </Col>
            <audio id="audioRemote"></audio>
            <audio id="audioLocal"></audio>

          </Row>
          <audio src="" id={"audio1"}></audio>
          <audio src="" id={"audio2"}></audio>

          <Modal show={this.state.showModal} onHide={() => this.setState({ showModal: false })} dialogClassName="modal-70w">
            <Modal.Header closeButton>
              <Modal.Title>{this.state.ModalTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modaldata">
                <DataTable
                  columns={unansweredcolumns}
                  data={(UnansweredData && UnansweredData.length > 0) ? UnansweredData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense
                  pagination={true}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>

          <Modal show={this.state.showWaitingAssignedModal} onHide={() => this.setState({ showWaitingAssignedModal: false })} dialogClassName="modal-70w">
            <Modal.Header closeButton>
              <Modal.Title>{this.state.ModalTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modaldata">
                <DataTable
                  columns={waitingassignedCallsList}
                  data={(waitingassignedCallsData && waitingassignedCallsData.length > 0) ? waitingassignedCallsData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense
                  pagination={true}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
          <Modal show={this.state.showMoreInfoModal} onHide={() => this.setState({ showMoreInfoModal: false })} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                <DataTable
                  columns={moreinfocolumns}
                  data={(modifiedMoreInfoData && modifiedMoreInfoData.length > 0) ? modifiedMoreInfoData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense

                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetMySqlData,
    addRecord,
    UpdateData,
    gaEventTracker
  }
)(RealTimePanelQueuewise);




