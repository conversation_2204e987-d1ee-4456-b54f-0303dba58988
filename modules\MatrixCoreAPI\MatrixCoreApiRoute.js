var express = require("express");
const router = express.Router();
const controller = require("./MatrixCoreApiController");
const { ACLlayer } = require("../ACLlayer");

router.post("/UpdateFosCity", controller.UpdateFOSCityAssignment);
router.post("/RejectLead", controller.RejectLead);
router.post("/CreateLead", controller.CreateLead);
router.get("/GetCouponRaiseRequest", controller.GetCouponRaiseRequest);
router.get("/FetchAgentPredefinedUrl", controller.FetchAgentPredefinedUrl);
router.post("/UpdateCouponRaiseRequest", controller.UpdateCouponRaiseRequest);
router.get("/GetCouponDataByLeadId", controller.GetCouponDataByLeadId);
router.post("/SendDuplicateOtp", controller.SendDuplicateOtp);
router.post("/VerifyDuplicateOtp", controller.VerifyDuplicateOtp);
router.get("/GetLeadOnlyURL", controller.GetLeadOnlyURL);
router.post("/UploadFileToS3BucketPvt", controller.UploadFileToS3BucketPvt);


module.exports = router;