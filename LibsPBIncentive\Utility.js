
const conf = require("../env_config");
const nodemailer = require('nodemailer');
const moment = require("moment");
const request = require('request');
const https = require('https');
const http = require('http')
const axios = require('axios')
const reader = require('xlsx')

const fs = require('fs');
const path = require('path');
var jsonxml = require('jsontoxml');

const xlsxFile = require('read-excel-file/node');

async function sendEmail(mailOptions) {
  const AWSSecretConfig = global.SecretConfig;

  //console.log('Inside sendEmail', AWSSecretConfig.SMTPGoogle);
  
  var transporter = nodemailer.createTransport(AWSSecretConfig.SMTPGoogle);
  try {
    transporter.sendMail(mailOptions, function (error, info) {
      if (error) {
        console.log(error);
      } else {
        // console.log('Email sent: ' + info.response);
      }
    });

  } catch (err) {
    console.log(err);
  } finally {
  }

}

async function readXlsxFileNew(file, option) {

  if (fs.existsSync(file)) {
    const workbook = reader.readFile(file);
    const sheet_name_list = workbook.SheetNames;
    let jsonPagesArray = [];
    sheet_name_list.forEach(function (sheet) {
      const jsonPage = {
        name: sheet,
        content: reader.utils.sheet_to_json(workbook.Sheets[sheet], { defval: "" })
      };
      jsonPagesArray.push(jsonPage);
    });

    return jsonPagesArray;


    // let workSheetsFromFile = "";
    // if (option.sheetRows != 0) {
    //   workSheetsFromFile = xlsx.parse(file, option);
    // }
    // else {
    //   workSheetsFromFile = xlsx.parse(file);

    // }
    // return workSheetsFromFile;
  }
  else {
    return null;
  }
}
async function uploadFile(file, location) {
  //console.log('__dirname', __dirname)
 // console.log(location);
  await fs.existsSync(location) || fs.mkdirSync(location);
  const myFile = file.myFile;

  var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');

  fs.copyFileSync(`${myFile.tempFilePath}`, location + myFileName);
 //  console.log("uploaded")
  return { filePath: location + myFileName, filename: myFileName };
}



async function fileDownload(url, path, callback) {
  return new Promise(resolve => {
    let response = request(url).pipe(fs.createWriteStream(path));
    response.on('finish', resolve);
  });
}

async function API_GET(url, headers) {
  let options = {};
  if (headers) {
    options = {
      headers: headers
    }
  }
  
  let p = new Promise((resolve, reject) => {
    https.get(url, options, (resp) => {
      let data = '';

      // A chunk of data has been received.
      resp.on('data', (chunk) => {
        data += chunk;
      });

      // The whole response has been received. Print out the result.
      resp.on('end', () => {
        //console.log(JSON.parse(data));
        try {
          // console.log('API_GET',url,headers,data);
          JSON.parse(data);
          resolve(JSON.parse(data));
        } catch (e) {
          return resolve(data);
        }
      });

    }).on("error", (err) => {
      console.log("Error: " + err.message);
      reject(err);
    });
  });

  return await p;
}

async function http_API_GET(url, headers) {
  let options = {};
  if (headers) {
    options = {
      headers: headers
    }
  }
  let p = new Promise((resolve, reject) => {
    http.get(url, options, (resp) => {
      let data = '';

      // A chunk of data has been received.
      resp.on('data', (chunk) => {
        data += chunk;
      });

      // The whole response has been received. Print out the result.
      resp.on('end', () => {
        
        try {
          // console.log('http_API_GET',url,headers,data);
          JSON.parse(data);
          resolve(JSON.parse(data));
        } catch (e) {
          return resolve(data);
        }
      });

    }).on("error", (err) => {
      console.log("Error: " + err.message);
      reject(err);
    });
  });

  return await p;
}

async function API_POST(url, params, headers) {

  let p = new Promise((resolve, reject) => {
    axios.post(
      url, 
      params,
      {
        headers: headers,
      },

      )
      .then(function (response) {
        //console.log("^^^^^^^^^^^^^^^^^^^^^^^^^");
        //console.log(response.data);
        return resolve(response.data);
      })
      .catch(function (error) {
        return resolve(error.response.data);
      });
    
  });

  return await p;
}

module.exports = {
  sendEmail: sendEmail,
  fileDownload: fileDownload,
  API_GET: API_GET,
  http_API_GET: http_API_GET,
  API_POST: API_POST,
  readXlsxFileNew: readXlsxFileNew,
  uploadFile: uploadFile
};
