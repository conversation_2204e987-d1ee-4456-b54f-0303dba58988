import React, { Component } from 'react';

import { <PERSON>tonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { GetCommonData, InsertData, UpdateData, GetFileExists,GetCommonspData } from "../../../store/actions/CommonAction";
import { getUrlParameter, getuser } from '../../../utility/utility.jsx';

import OtpInput from "react-otp-input";
import { If, Then, Else } from 'react-if';
import { connect } from "react-redux";
import moment from 'moment';
import StarRating from "../StarRating";

class AdvisorInfo extends Component{

    constructor(props) {
        super(props);
        this.state = {
            bgImg: '/Graphic.svg' ,
            advImg: '/Logo.svg' ,
            enterSmallImg:'/enterSmall.svg',
            verifybtniconImg:'/verifybtnicon.svg',
            happystar:'/happy-star.png',
            CertifiedBadge:'/CertifiedBadge.svg',
            pointerImg:'/Pointer.svg',
            advisorData: [],
            advisorDataRating: null,
            StarRating : 0,
            rating:'',
            currentRating: "0",
            IsLoading: true,
    }
    }

    back  = (e) => {
        e.preventDefault();
        this.props.prevstep();
    }

    async componentDidMount() {//debugger;
        var body = document.body;

        body.classList.add("advisorInfo");
        console.log('agentid',this.props.agentid)
        const flag = getUrlParameter("flag");
        var empid = flag ? getuser().EmployeeId : this.props.agentid;
        // setTimeout(function () {
         flag === '' && await this.props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'AdvisorInfoStarRating',
            con: [{ "UserID": this.props.userIdEmp , "CustomerId" : this.props.CustomerId , "IsActive" : 1}]

          }, function (results) {
            if (results.data.status == 200) {
                console.log('advisorstarrating',results.data.data[0]);
            this.setState({ advisorDataRating: results.data.data[0], StarRating : results.data.data[0][0].StarRating });
            }
        }.bind(this));
        // }.bind(this), 10000);

         await this.props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'AdvisorInfo',
            con: [{ "Empcode": empid, "IsActive" : 1 }]

          }, function (results) {
            if (results.data.status == 200) {
                console.log('advisorinfo',results.data.data[0] );
            this.setState({ advisorData: results.data.data[0] , IsLoading : false});
            }
        }.bind(this));

        
      }
      

     setRating = rating => {
        this.setState({ rating: rating });
        console.log('parent rating',rating);
      };

      async saveRating(){
        let advisorData = this.state.advisorData;
		await this.props.GetCommonspData({
          root: 'InsertAdvisorInfoStarRating',
          c: "L",
          params: [{"UserID": advisorData[0].UserID , "CustomerId" : this.props.CustomerId, "StarRating" : this.state.rating }],
            }, function (result) {
            if (result.data.status == 200) {
                alert('Rating submitted successfully')
              }else{
                alert('Rating not submitted')
              }
          }.bind(this));
    }  

    loadAlternative(element) {debugger;
      
      var list = element.target.getAttribute('alternative').split(/,/);
      var image = new Image();
    
      image.onload = function() {
        document.getElementById("profilepic").src = this.src;
      }
    
      image.onerror = function() {
        if (list.length) {
          document.getElementById("profilepic").src = "/no-image.jpeg"; 
          //this.loadAlternative();
        }
      }
    
      //  pick off the first url in the list
      image.src = list.shift();
    }

    render(){
        const { advisorData, advisorDataRating } = this.state;
        const flag = getUrlParameter('flag');
        console.log('advisordate',this.state.advisorData);
        return(
            <>
            <Row className={flag ? 'otp-layout contentCenter' : ''}>
            {flag === '' && <Col md={6}  xs={12}>
            <button type="button" onClick={this.back} class="backCallLog btn btn-primary"><i class="fa fa-arrow-left"></i></button>
                 <div className="verifybgImg"><img src={this.state.bgImg}/>  </div>  </Col>}
            <Col md={6} xs={12}>
           {this.state.IsLoading && <div className="LoaderAdvisorInfo"> <i class="fa fa-spinner fa-spin"></i></div>}
            <Row className={flag ?"contentCenter" : ''}> 
            {this.state.advisorData && (this.state.advisorData.length > 0 ) && 
            <Col md={12} xs={12} lg={9 }>
                <Row className="blueBg">
                    <div className="mobileViewBgBlue">
                    {flag === '' && <button type="button" onClick={this.back} class="backBtn btn btn-primary"><i class="fa fa-arrow-left"></i></button>}
                    { (this.state.advisorData[0].AVCertified == true && flag === '') && <div className="CertifiedBadge">  <img src={this.state.CertifiedBadge}/> <p>Insurance Institute of India Certified </p></div> } 
              <div className="emiProfile"> 
              <img id="profilepic" src={"https://policystatic.policybazaar.com/ProfilePic/"+this.state.advisorData[0].Empcode+".jpg"} 
              alternative={"https://policystatic.policybazaar.com/ProfilePic/"+this.state.advisorData[0].Empcode+".JPG, /no-image.jpeg"} 
              onError={this.loadAlternative.bind(this)}></img>
			
			  <p className="emiId">{this.state.advisorData[0].Empcode}</p>
              <h2 className="emiName">{this.state.advisorData[0].EmpName}</h2>
              {flag === '' && <p className="message">{this.state.advisorData[0].BookingCount} <br/> {this.state.advisorData[0].Tenure} with Policybazaar</p>  }
              </div>
              </div>
              </Row>
              <Row>
                    <div className="mobileViewBgwhite">
              <div className="emiDetails">
                  <span>About {this.state.advisorData[0].EmpName} <img src={this.state.pointerImg}/> </span>
                  <p>{this.state.advisorData[0].EmpInfo}</p>
              </div>
              {flag === '' && <div className="ratingBox">
              <img src={this.state.happystar}/> 
              <p>More than <b>{this.state.advisorData[0].CSATRating}% customers</b> have rated their experience as <b>Very Good</b> with Manohar</p>         
              </div>}
              {flag === '' && this.state.advisorDataRating &&
              <div className="sharefeedback">
              <span>Share your feedback<img src={this.state.pointerImg}/> </span>
              <p>
                  <StarRating
                    numberOfStars="5"
                    currentRating={this.state.StarRating}
                    onClick={this.setRating}
                />
                  {/* <i class="fa fa-star " ></i> <i class="fa fa-star " ></i> <i class="fa fa-star " ></i> <i class="fa fa-star " ></i><i class="fa fa-star-o " ></i>  */}
                  </p>
                  <div className="ratingMsg">Very Good</div>
                <div className="actions">
                <button type="submit" onClick={this.saveRating.bind(this)}>
                    Submit Rating
                </button>
                </div>   
              </div>}
              </div>
              </Row>
        </Col>}
          </Row>
            </Col>  
            </Row>
      
            </>
      
        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      InsertData,
      UpdateData,
    }
  )(AdvisorInfo);