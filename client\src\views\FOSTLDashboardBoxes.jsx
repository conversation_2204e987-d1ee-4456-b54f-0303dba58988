import { useEffect, useState } from "react";
import React from "react";

import { getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Row,
    Col
} from "react-bootstrap";

const FOSTLDashboardBoxes = (props) => {

    const [category, setCategory] = useState(1)
    const user = getuser();
    const userId = user.UserID
    const [count1, setCount1] = useState([0]);
    const [count2, setCount2] = useState([0]);
    const [count3, setCount3] = useState([0]);
    const [count4, setCount4] = useState([0]);
    const [count5, setCount5] = useState([0]);
    const [count8, setCount8] = useState([0]);

    //console.log(props)
    useEffect(() => {
        setCount1(0)
        setCount2(0)
        setCount3(0)
        setCount4(0)
        setCount5(0)
        setCount8(0)
        setCategory(props.Category);
        {props.Count && props.Count.map(obj => {
            if (obj.Category == 1) {
                setCount1(obj.COUNT)
            }
            if (obj.Category == 2) {
                setCount2(obj.COUNT)
            }
            if (obj.Category == 3) {
                setCount3(obj.COUNT)
            }
            if (obj.Category == 4) {
                setCount4(obj.COUNT)
            }
            if (obj.Category == 5) {
                setCount5(obj.COUNT)
            }
            if (obj.Category == 8) {
                setCount8(obj.COUNT)
            }
        })
        }

    }, [props])

    const handleClick = (e)=>{
        setCategory(e);
        props.handleCount(e)
    }


    return (
        <>

            <div className="content categoryBox">
                <Row>
                    <Col md={2}>
                        <div className={category == 1 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(1)}>
                            <h2>Total Scheduled <br/> Visit</h2>
                            <p >{count1}</p>
                        </div>
                    </Col>
                    <Col md={2}>
                        <div className={category == 2 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(2)}>
                            <h2>Visit Done</h2>
                            <p >{count2}</p>
                        </div>
                    </Col>

                    <Col md={2}>
                        <div className={category == 8 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(8)}>
                            <h2 className="yellowbg">In Progress</h2>
                            <p >{count8}</p>
                        </div>
                    </Col>
                
                    <Col md={2}>
                        <div className={category == 3 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(3)}>
                            <h2 className="pingbg">Current and Next Slot Visits</h2>
                            <p >{count3}</p>
                        </div>
                    </Col>
                    <Col md={2}>
                        <div className={category == 4 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(4)}>
                            <h2 className="pingbg">Past Appointment <br/>(No Visit)</h2>
                            <p >{count4}</p>
                        </div>
                    </Col>
                    <Col md={2}>
                        <div className={category == 5 ? "FosTlDashboard activeCategory": "FosTlDashboard"} onClick={() => handleClick(5)}>
                            <h2 className="pingbg">Status Updated without Called</h2>
                            <p >{count5}</p>
                        </div>
                    </Col>
                </Row>
            </div>

        </>
    );


}


export default FOSTLDashboardBoxes;
