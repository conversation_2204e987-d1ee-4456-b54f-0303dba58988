import React, { useRef } from "react";
import { GetCommonData, GetCommonspData, GetCommonspDataV2 } from "../../store/actions/CommonActionPbIncentive";
import { connect } from "react-redux";
import { useEffect, useState } from "react";
import { getuser } from '../../utility/utility.jsx';
import './gaming/ContestReward.css';
import Spinner from '../../components/Loaders/Spinner';

const ContestReward = (props) => {
    // const userId = useRef();
    const [contestReward, setContestReward] = useState([]);
    const [Loader, setLoader]= useState(true);

    useEffect(() => {
        let { UserID } = getuser();
        
        props.GetCommonspDataV2({
            root: 'GetRewards',
            params: [{ PrizeType: 1 }],
            c: "R",
        }, function (data) {
            // setContestReward(data[0]);
       
            setLoader(false);
            if (Array.isArray(data?.data?.data[0]) && data.data.data[0].length > 0) {
                setContestReward(data.data.data[0]);
            }
        });
    }, []);
    
    return (
            Loader ?  <div className="ContestLoader"><h4>Loading...</h4> </div>:
            <div className="content">
            <h4 className="ContestHeading">Contest Rewards</h4>
            <div className="margintop75">
            {
                contestReward && contestReward.map((rewards) => (                   
                        <div className="SpinRewardList">
                            <div>
                                <h2>{rewards.ContestName}</h2>
                                {/* <h3><strong>Win</strong> Vouchers</h3>
            <h4>Upto <strong>₹ 251</strong></h4> */}
                                <h4>{rewards.RewardText}</h4>
                            </div>
                            <img src="/lottery/mobile.png " className="rewardImg" />
                        </div>
                ))
                
            }
             {
                contestReward?.length  === 0 && 
                <div className="SpinRewardList"> 
                <h4>No Rewards</h4></div>
                
            }
            </div>
           
        </div>
        
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(ContestReward);