
import React from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
// reactstrap components

import {
    GetCommonData
} from "../../store/actions/CommonAction";

import _ from 'underscore';
import {MultiSelect} from "react-multi-select-component";


class MultiSelectList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            items: [],
            selectedValue: (this.props.value)?this.props.value:[{ label: "Select Values", value: "0" }],
            name: '',
        }
    }
    componentDidMount() {
        if (this.state.items && this.state.items.length === 0) {
            this.props.GetCommonData({
                root: this.props.col.config.root,
                cols: this.props.col.config.cols,
                con: this.props.col.config.con,
                data: this.props.col.config.data,
                statename: this.props.col.config.statename,
                state: this.props.col.config.state == false ? false : true,
            });
        }
    }
    componentWillReceiveProps(nextProps) {
        let multiItems = [];
        if(nextProps.filtervalue != this.props.filtervalue){
            this.setState({ selectedValue :  [{ label: "Select Values", value: "0" }]})
        }
        if (!nextProps.CommonData.isError) {
            let items = nextProps.CommonData[this.props.col.config.statename ?? this.props.col.config.root];

            this.setState({ name: this.props.col.name });
            if (items && nextProps.valueTobefiltered) {
                items = items.filter(function (n) {
                    return (nextProps.valueTobefiltered.indexOf(n.Id) > -1);
                });
            }

            // if (nextProps.col.distinct) {
            //     //debugger;
            //     items = _.uniq(items, function (x) {
            //         return x.Id;
            //     });
            // }
            if(items && items.length > 0){
                items.map(item => (
                multiItems.push({
                    value: item.Id,
                    label: item.Display,
                    ProductID: item.ProductID,
                })
            ))
            }
            this.setState({ items: multiItems });

        }
    }

    displayoption(item) {
        return <option key={item.Id} value={item.Id}>{item.Display}</option>
    }

    onSelect(selectedList, selectedItem) {
        var newselectedList = selectedList.filter(task => task.label !== 'Select Values');
        let selectAll = false;
        if(this.state.items.length == selectedList.length){
             selectAll = true;
        }
        this.setState({ selectedValue: newselectedList });
        this.props.onChange({ id: this.props.id, newselectedList, selectAll: selectAll, type: "multiselect" })
    }

    setItemMultiselect(items){
        let multiItems = [];
        let filter = [];
        let filtervalue = parseInt(this.props.filtervalue);
        if (this.props.filterkey && this.props.filtervalue) {
        items.map(function(item, i) {
            if (this.props.filtervalue == 7) {
            if(item.ProductID == 7 || item.ProductID == 1000){
                filter = multiItems.filter(task => task.value == item.value);
                if(filter.length == 0){
                multiItems.push({
                value: item.value,
                label: item.label,
            })
            }
            }
        }
            else if (item.ProductID == filtervalue ) {               
                multiItems.push({
                    value: item.value,
                    label: item.label,
                }) 
            
            } 
    }.bind(this))
    }else{
        multiItems = items;
    }
    let selectedValue = this.state.selectedValue;
    if(multiItems.length > 0 && selectedValue && selectedValue.length > 0 && selectedValue[0].value == '*'){
        this.setState({ selectedValue : multiItems});
        console.log('...............................',this.state.selectedValue);
    }
    return multiItems;
    }

    render() {

        let { items } = this.state;
        const { value, onChange, visible, name } = this.props;
        if (!items) {
            items = [];
        }
        if (visible == false) {
            return null;
        }
        let multiitem = this.setItemMultiselect(items);
        return (

             <div>
                
                 <MultiSelect
                    options={multiitem} // Options to display in the dropdown
                    value={this.state.selectedValue} // Preselected value to persist in dropdown
                    onChange={this.onSelect.bind(this)} // Function will trigger on select event  
                    disabled={this.props.disabled}  
                    className={(this.props.disabled)?"multi-select-disabled":this.props.className}
                                                              
                // labelledBy={"Select IVR Queues"}
                // selectAllLabel={"Select ALL IVR Queues"}
                />
             </div>

         );
     }
}
function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData
    }
)(MultiSelectList);

