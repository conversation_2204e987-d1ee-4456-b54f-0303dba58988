import React from "react";
import {
  GetCommonData, GetCommonspData, GetComunicationData, GetCommonspDataV2, GetAgentDetailsByManager
} from "../store/actions/CommonAction";
import { getuser,fnDatatableCol, getBaseUrl } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import DropDown from './Common/DropDown';
import { Button, Form } from 'react-bootstrap';
import { Loader } from "react-bootstrap-typeahead";
import DataTableWithFilter from "./Common/DataTableWithFilter";

class PaymentAttemptLeadsSupervisor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "Payment Attempts Dashboard",
      items: {},
      SelectedSupervisors: [],
      SelectedRows: [],
      clearSelected: false,
      key: "-1",
      productList: [{Id: 2, Display: 'Health'},{Id: 7, Display: 'TermLife'},{Id: 115, Display: 'Investments'}],
      PaymentLoader: false,
      ProductId: 0,
      showFilterCount: false,
      UsersGroup: [],
      Users: []
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);

    // const Cell = ({ v }) => (
    //   <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    // );
    this.columnlist = [

      {
        name: "EmployeeId",
        label: "EmployeeId",
        sortable: true,
      },
      {
        name: "UserId",
        label: "UserId",
        hide: true,
        type: "number",
        sortable: true,
      },
      {
        name: "UserName",
        label: "UserName",
        sortable: true,
      },
      {
        name: "Groupname",
        label: "GroupName",
        sortable: true,
      },
      {
        name: "Link",
        label: "Dashboard link",
        type: "string",
        editable: true,
        cell: row => <a  style={{cursor: "pointer",color: "#0075FF"}} target="_blank" onClick={(e) => this.CreateDashboardURL(e, row)}>View Dashboard</a>,
        width: "200px"
      },
      
    ];
  }

  async CreateDashboardURL(e, row){
    //let domain = getCookie("cdomain");
    let tokenId  = row._tid;
    let base_url = await getBaseUrl();
    let url = base_url+"PaymentAttemptLeads?tId="+tokenId;
    window.open(url, "_blank");
  }
  
  handleShow(e) {
    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchData();
    }.bind(this), 500);
  }

  filterdata(e) {

    let alldata = (this.state.items)?this.state.items:[];
    if (this.state.key === "-1") {
      return alldata;
    }

    let AgentData = [];
    alldata.forEach(element => {
      if (this.state.key != -1 && this.state.key.indexOf(element.GroupId) > -1) {
        AgentData.push(element);
      }      
    });

    if(AgentData && AgentData.length == 0){
      
    }
    return AgentData;
  }

  componentDidMount() {
    //this.GetProductList();
    const user = getuser();
    this.setState({ ReportTime: new Date(), Users: user });
  }

  
  // GetProductList(){
  //   this.props.GetCommonspDataV2({
  //     root: "GetUserProductList"
  //   }, function (result) {
  //     if(result && result.data && result.data.data){
  //       var array = result.data.data[0];
  //       var productids = array.map(function(elm) {
  //         return { Id: elm.ProductId, Display: elm.ProductDisplayName};
  //       });

  //       this.setState({productList : productids});
  //     }
  //     }.bind(this));
  // }

  fetchData = async () => {
    if(this.state.ProductId == 0){
      toast("Please enter Product", { type: 'error' });
      return false;
    }
    let that = this;
    //var SelectedSupervisors = this.state.SelectedSupervisors;
    this.setState({ PaymentLoader : true});

    await GetAgentDetailsByManager({productId: this.state.ProductId, SupervisorIds: (this.state.SelectedSupervisors)?this.state.SelectedSupervisors.join(','):[]}, await function (data) {
       this.setState({ PaymentLoader : false});
      if (data && data.data && data.data.data && data.data.data.length > 0){
        if (Array.isArray(data.data.data[0])) {
          this.setState({items : [] });
          return;
        }
        let resultUsers = data.data.data;
        // Create a Set to store unique group objects based on GroupId
        const uniqueGroups = new Set(resultUsers.map(obj => JSON.stringify({ GroupId: obj.GroupId, Groupname: obj.Groupname })));

        // Convert the Set back to an array of unique group objects
        const GroupArray = Array.from(uniqueGroups).map(JSON.parse);
        //const GroupArray = resultUsers.map(obj => ({ GroupId: obj.GroupId, Groupname: obj.Groupname }));
        this.setState({items : data.data.data, UsersGroup : GroupArray});
      }
    }.bind(this));

    if (that.state.SelectedRows.length > 0) {
      that.dtRef.current.handleClearRows();
    }

  }

  statuschange(e) {
    this.setState({ key: e.target.value });
  }

  productchange= (e) => {
    this.setState({ ProductId: e.target.value });
  }

  CheckLoader(action) {
    if (action === 'payment') {
      if (this.state.PaymentLoader)
        return <Loader />;
    } 
  }


  render() {
    const columns = fnDatatableCol(this.columnlist);

    const {  PageTitle, UsersGroup } = this.state;
    const data = this.filterdata();

    let ddl = [];
    if (UsersGroup) {
        UsersGroup.forEach(item => {
        ddl.push(<option value={item.GroupId}>{item.Groupname}</option>)
      });
    }

    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={5}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    {this.state.Users && ![12,13].includes(this.state.Users.RoleId) &&<ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                     </ManagerHierarchy>}                                          
                  </Row>
                  <Row>  
                    <Col md={3}>
                    <span>Select Product</span>
                    <Form.Group controlId="prod_dropdown">
                    <DropDown firstoption="Select Product" items={this.state.productList} onChange={this.productchange}>
                    </DropDown>
                    </Form.Group> 
                    </Col>
                   
                    <Col md={2}>
                    {[12,13].includes(this.state.Users.RoleId) && <Button variant="primary" className="mt-4" onClick={() => this.fetchData()}>Fetch{this.CheckLoader('payment') }</Button>}
                    {![12,13].includes(this.state.Users.RoleId) && this.CheckLoader('payment') }
                    </Col>
                    <Col md={5}></Col>

                  </Row>
                    <Row>
                    <Col md={10}></Col>
                    <Col md={2}>
                     <span>Select Group</span>
                      <div className="form-group">
                        <select className="form-control" onChange={this.statuschange}>
                          <option value="-1">ALL</option>
                          {ddl}
                        </select>
                      </div>
                    </Col>
                    </Row>

                </CardHeader>

                <CardBody>

                  {<div className="statusdata">
                    <DataTableWithFilter
                      columns={columns}
                      data={ data && data.length > 0 ? data : []}
                      pagination={true}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense
                      ref={this.dtRef}
                    />
                  </div>}
                </CardBody>

              </Card>
            </Col>

          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetComunicationData,
    GetCommonspDataV2
  }
)(PaymentAttemptLeadsSupervisor);




