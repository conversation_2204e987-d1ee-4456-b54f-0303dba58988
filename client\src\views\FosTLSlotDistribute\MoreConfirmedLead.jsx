import { useState } from 'react';
import { Col, Dropdown, Form, Modal, Row } from 'react-bootstrap';
import TogglePopup from './TogglePopup';
import moment from 'moment';

import { TimeConversion } from '../../utility/utility.jsx';


const MoreConfirmedLead = (props) => {
  // console.log("The prop is ", leadData);
  // const props= leadData;
  const { leadsData, AppointmentSlots, activeTab, IsSuccess, EmployeeFlag } = props;
  // console.log("the lead data is", leadData);

  return (
    <Modal
      {...props}
      size="md"
      aria-labelledby="contained-modal-title-vcenter"
      centered
      className="UnassignedLeadPopup"
    >
      <Modal.Header closeButton>
        {/* {Array.isArray(leadsData) && leadsData.length > 0 &&  */}
        {leadsData && leadsData.length > 0 && leadsData[0].EmployeeId && <h3>
          {leadsData[0].UserName}
          (Emp ID: {leadsData[0].EmployeeId})
        </h3>}
        {/* } */}
        <p>Appointment Slot</p>
        <Row className="justify-content-center">
          <Col xs={4} sm={4}>
            {/* <Form.Select aria-label="Default select example">
              <option>8 Mar 2023</option>
              <option value="1">8 Mar 2023</option>
              <option value="2">8 Mar 2023</option>

            </Form.Select> */}

            {leadsData && leadsData.length > 0 && <p>{moment.utc(leadsData[0].AppDateTime).local(true).format("DD MMM YYYY")}</p>}

          </Col>
          <Col xs={5} sm={5}>
            {/* <Form.Select aria-label="Default select example">
              <option>2:00 PM - 4:00 PM</option>
              <option value="1">8:00 PM - 10:00 PM</option>
              <option value="2">10:00 PM - 12:00 PM</option>
              <option value="3">4:00 PM - 6:00 PM</option>
            </Form.Select> */}
            {/* {leadsData && leadsData.length > 0 && AppointmentSlots && AppointmentSlots && 
              leadsData[0].SlotId==AppointmentSlots.SlotId && <p>{TimeConversion(AppointmentSlots.StartTime)} - {TimeConversion(AppointmentSlots.EndTime)}</p>} */}
            {
              leadsData && leadsData.length > 0 && AppointmentSlots && Array.isArray(AppointmentSlots) && AppointmentSlots.length > 0 && AppointmentSlots.map((slots, index) => {

                if (leadsData[0].SlotId == slots.Id) {
                  return (
                    <p>{TimeConversion(slots.StartTime)} - {TimeConversion(slots.EndTime)}</p>
                  )
                }
              })
            }
          </Col>
        </Row>
      </Modal.Header>
      <Modal.Body>
        {
          leadsData.map((data) => (
            // <div className="confrimLead" >
            //   <b>Lead ID :</b>{data.LeadId}
            //   <p><b>Status : </b>{data.SubStatusName} 
            //   {/* <img src="/fosTlDashboard/confrim.svg" /> */}
            //   <TogglePopup Details = { data } AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={IsSuccess} EmployeeFlag={EmployeeFlag}/> 
            //   </p>
            // </div>
            <div className="confrimLead">
              <b>Lead ID :</b>{data.LeadId} {data.AssignmentId == 1 && <b>(Store)</b>}
              {data.SubstatusId == 2002 && <p><b>Status :</b>{"Appt. Book"} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={IsSuccess} EmployeeFlag={EmployeeFlag} />
              {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}
                </p>}

              {data.SubstatusId != 2002 && <p><b>Status :</b>{data.SubStatusName.replace("Appointment", " ")} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab} IsSuccess={IsSuccess} EmployeeFlag={EmployeeFlag} />
              {data.CallDate && <img src="/fosTlDashboard/Calling_72.png" title={`Last call: ${moment.utc(data.CallDate).local(true).format("DD-MMM-YYYY HH:mm")}`} aria-hidden="true" style={{color:'black', position:'absolute', top:"24px", right:"5px", cursor:"pointer" }} />}
                </p>}
              {/* <p><b>Status :</b> {data.SubStatusName} <TogglePopup Details={data} AppointmentSlots={AppointmentSlots} activeTab={activeTab}/></p> */}
            </div>
          ))
        }

      </Modal.Body>

    </Modal>
  )

}

export default MoreConfirmedLead;