.MuiDialog-container{
    padding: 0px !important;
    margin: 0px !important;
    overflow: hidden;
    font-family: '<PERSON>o',sans-serif;

    .MuiDialog-paper{
        position: absolute;
        top: 50%;
        transform: translate(0%, -50%) !important;
        left: 0;
        right: 0;
        margin: auto;
        max-width: 380px;
        font-family: '<PERSON><PERSON>',sans-serif;
        max-height: 442px;
        border-radius: 8px;
        padding: 24px;
        height: 100%;
        box-shadow: none;
        -webkit-box-shadow: none;
        transition: none;
        -webkit-transition: none;

        .MuiDialogTitle-root{
            padding: 0px 0px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
             
            span{
              text-align: center;
              flex: 1;
              font-size: 20px;
              color: #253858e3;
              font-weight: 600;
            }

            button{
                padding: 0;
                margin: 0;
                outline: none;
                opacity: 0.8;

                &:hover{
                    background: transparent;
                    opacity: 1;
                }
            }
        }

        .MuiDialogContent-root{
            padding: 0;
            border: 0;
            overflow: hidden;

            .search{
                background: #f5f5f5;
                border-radius: 8px;
                padding: 9px 16px;
                display: flex;
                gap: 8px;
                align-items: center;
                margin-bottom: 15px;
                line-height: normal;

               .searchButton, .searchTerm{
                border: 0px;
                background: transparent;
                color: #25385899;
                padding: 0;
                outline: none;
                font-size: 18px;
                font-weight: inherit;
               }

               .searchTerm{
                font-size: 14px;
                font-weight: 500;
                width: 100%;

                .MuiInput-root::before, .MuiInput-root::after{
                    border: 0;
                    transition: none;
                    -webkit-transition: none

                }

                .MuiInputBase-input{
                    border: 0;
                    animation: none;
                    -webkit-animation: none;
                    padding: 0;
                    color: #253858e3;
                    font-weight: 400;
                    
                    &::-webkit-input-placeholder{
                        color: #25385899;
                        margin-top: 2px;
                        font-weight: 500;
                    }

                }

               }

            }

            ul{
                list-style: none;
                padding: 0;
                margin: 0;
                overflow-y: auto;
                height: 330px;

                &::-webkit-scrollbar, &::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track{
                    width: 0px;
                    background: transparent;
                    border-radius: 0;
                }

                li{
                    font-size: 14px;
                    color: #253858e3 !important;
                    font-weight: 500;
                    padding: 15px 16px;
                    margin: 0px;

                  &.active{
                    background: #DDECFF;
                    border-radius: 8px;
                  }

                }
            }

        }
    }
}

