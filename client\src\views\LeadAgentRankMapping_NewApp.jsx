
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
  GetCommonData, InsertData, UpdateData
} from "../store/actions/CommonAction";
import {
  addRecord
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTableV1 from './Common/DataTableWithFilterV1';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import { CompareJson, fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getMax, joinObject, getAgentId } from '../utility/utility.jsx';
// reactstrap components
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CardT<PERSON>le,
  <PERSON>,
  <PERSON>,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import TimePicker from "./Common/TimePicker";
import { ToastContainer, toast } from "react-toastify";
import moment from "moment";
class LeadAgentRankMapping_NewApp extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      showAlert: false,
      items: [],
      filteredItems: [],
      store: [],
      activePage: 1,
      root: "LeadAgentRankMapping",
      PageTitle: "Lead Agent Rank Mapping",
      FormTitle: "",
      formvalue: {
        AgentRank: "",
        LeadRank: "",
        Coeff: "",
        ProductID: "",
        Priority: "",
        TempCount: "",
        StartTime: "",
        EndTime: "",
        IsTempOverflow: false
      },
      event: "",
      ModalValueChanged: false,
      user: {},
      productItem: '',
      agentrank: '',
      leadrank: '',
      startTime: {
        hours: "0",
        minutes: "0",
      },
      endTime: {
        hours: "23",
        minutes: "59",
      },
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);
    this.leadranklistchange = this.leadranklistchange.bind(this);
    this.agentranklistchange = this.agentranklistchange.bind(this);
    this.selectedrow = { "Id": 0, "ProductID": "", "TempCount": "", "AgentRank": "", "LeadRank": "", "Coeff": "", "Priority": "", "IsTempOverflow": false }

    this.startHoursChange = this.startHoursChange.bind(this);
    this.startMinutesChange = this.startMinutesChange.bind(this);
    this.endHoursChange = this.endHoursChange.bind(this);
    this.endMinutesChange = this.endMinutesChange.bind(this);

    

    this.columnlist = [
      {
        name: "Id",
        label: "Id",
        type: "hidden",
        hide: true,
      },
      {
        name: "AgentRank",
        label: "AgentRank",
        type: "int",
        filterType: "number",
        config:
        {
          root: "LeadAgentRankMapping",
          cols: ['DISTINCT AgentRank AS Id', 'AgentRank AS Display'],
          statename: "agentrankmapping",
          state: true
        },
        searchable: true
      },
      {
        name: "LeadRank",
        label: "LeadRank",
        type: "int",
        filterType: "number",
        config:
        {
          root: "LeadAgentRankMapping",
          cols: ['DISTINCT LeadRank AS Id', 'LeadRank AS Display'],
          statename: "leadrankmapping",
          state: true
        },
        searchable: true
      },
      {
        name: "Coeff",
        label: "Coeff",
        type: "decimal"
      },
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        config: {
          root: "UserProducts",
          cols: ["DISTINCT PDT.ID AS Id", "PDT.ProductName AS Display"],
          con: [{ "PDT.Isactive": 1 },{ "UBM.IsActive" : 1 },{ "UBM.UserId": getAgentId() }]
        },
        searchable: true,
        editable: false     
      },
      {
        name: "Priority",
        label: "Priority",
        type: "number"
      },
      {
        name: "TempCount",
        label: "TempCount",
        type: "string"
      },
      {
        name: "IsTempOverflow",
        label: "Experimental",
        type: "bool"
      },
      {
        name: "StartTime",
        label: "StartTime",
        type: "time"
      },
      {
        name: "EndTime",
        label: "EndTime",
        type: "time"
      }
    ];
  }

  checkValidations() {
    if (this.state.formvalue.AgentRank === "") {
      toast("Please enter AgentRank...", { type: "error" });
      return false;
    }

    if (this.state.formvalue.LeadRank === "") {
      toast("Please enter LeadRank...", { type: "error" });
      return false;
    }

    if (this.state.formvalue.Coeff === "") {
      toast("Please enter Coeff...", { type: "error" });
      return false;
    }

    if (this.state.formvalue.ProductID === "") {
      toast("Please enter Product...", { type: "error" });
      return false;
    }

    if (this.state.formvalue.Priority === "") {
      toast("Please enter Priority...", { type: "error" });
      return false;
    }

    if (this.state.formvalue.TempCount === "") {
      toast("Please enter TempCount...", { type: "error" });
      return false;
    }

    if(this.state.startTime.hours === ""){
      toast("Please enter starttime...", { type: "error" });
      return false;
    }
    if(this.state.startTime.minutes === ""){
      toast("Please enter starttime...", { type: "error" });
      return false;
    }
    if(this.state.endTime.hours === ""){
      toast("Please enter endtime...", { type: "error" });
      return false;
    }
    if(this.state.endTime.minutes === ""){
      toast("Please enter endtime...", { type: "error" });
      return false;
    }

    if(parseInt(this.state.startTime.hours) > parseInt(this.state.endTime.hours)){
      toast("StartTime can't be greater than EndTime...", {type: 'error'});
      return false;

    } else if(parseInt(this.state.startTime.hours) === parseInt(this.state.endTime.hours)){

      if(parseInt(this.state.startTime.minutes) > parseInt(this.state.endTime.minutes)){
        toast("StartTime can't be greater than EndTime...", {type: 'error'});
        return false;
      }
    }   

    return true;
  }

  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      cols: GetJsonToArray(this.columnlist, "name"),
      c: "L", // L for live connection, R for Replica
    });
    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(nextProps.CommonData[this.state.root], nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if(prevState.items !== this.state.items){
      const productId = this.state.productItem;
      const leadRank = this.state.leadrank;
      const agentRank = this.state.agentrank;
      const filteredData = this.filterData(productId, leadRank, agentRank);
      this.setState({ filteredItems: filteredData });
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      const items = nextProps.CommonData[this.state.root];
      this.setState({ items: items });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    // if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
    //   if (nextProps.CommonData.InsertSuccessData.status != 200) {
    //     //alert(nextProps.CommonData.InsertSuccessData.error);
    //     this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
    //   }
    //   else {
    //     //this.setState({ showModal: false });
    //     this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
    //   }
    // }

    // if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
    //   if (nextProps.CommonData.UpdateSuccessData.status != 200)
    //     this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
    //   else {
    //     //this.setState({ showModal: false });
    //     this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
    //   }
    // }

  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }
  handleCopy(row) {
    let starthours = parseInt(row.StartTime.substr(11,2));
    let startminutes = parseInt(row.StartTime.substr(14,2));
    let endhours = parseInt(row.EndTime.substr(11,2));
    let endminutes = parseInt(row.EndTime.substr(14,2));

    this.setState({ 
      showAlert: false,
      formvalue: Object.assign({}, row, {}),
      event: "Copy",
      showModal: true,
      FormTitle: "Copy Record", 
      startTime: {hours: starthours, minutes: startminutes},
      endTime: {hours: endhours, minutes: endminutes}
    });
  }
  handleEdit(row) {
    let starthours = parseInt(row.StartTime.substr(11,2));
    let startminutes = parseInt(row.StartTime.substr(14,2));
    let endhours = parseInt(row.EndTime.substr(11,2));
    let endminutes = parseInt(row.EndTime.substr(14,2));

    this.setState({ 
      showAlert: false,
      od: Object.assign({}, row, {}),
      formvalue: Object.assign({}, row, {}),
      event: "Edit",
      showModal: true,
      FormTitle: "Edit Record", 
      startTime: {hours: starthours, minutes: startminutes},
      endTime: {hours: endhours, minutes: endminutes}
    });
  }
  handleClose() {
    this.setState({
      showModal: false,
      showAlert: false, 
      startTime: {hours: "0", minutes: "0"}, 
      endTime: {hours: "23", minutes: "59"} 
    });
  }

  handleShow() {
    this.setState({ 
      showAlert: false,
      formvalue: this.selectedrow, 
      event: "Add", showModal: true, 
      FormTitle: "Add New Record",
    });
  }

  handleSave() {
    if (!this.checkValidations()) {
      return;
    }
    if (document.getElementsByName("frmLeadAgentRankMapping_NewApp").length > 0 &&
      document.getElementsByName("frmLeadAgentRankMapping_NewApp")[0].reportValidity()) {

      let StartTime = this.state.startTime.hours + ":" + this.state.startTime.minutes;
      let EndTime = this.state.endTime.hours + ":" + this.state.endTime.minutes;

      let formValue = this.state.formvalue;

      formValue["StartTime"] = StartTime;
      formValue["EndTime"] = EndTime == "0:0" ? "23:59" : EndTime;
      formValue["UpdatedOn"] = moment().format("YYYY-MM-DD HH:mm:ss");

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      let id = formvalue["Id"];
        delete formvalue["Id"]

      let alertMessage = "";

      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);
        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { "Id": id },
          c: "L",
        });
        
        alertMessage = "Record Updated Successfully";

        this.props.addRecord({
          root: "History",
          body: {
            module: "LeadAgentRankMapping_NewApp",
            od: this.state.od,
            nd: formvalue,
            ts: new Date(),
            by: getuser().UserID
          }
        });
      } else if (this.state.event == "Copy") {
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        //formvalue["CreatedOn"] = new Date();
        this.fnCleanData(formvalue);
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });
        alertMessage = "Record Added Successfully";
      } else {
        //formvalue["Id"] = getMax(this.state.items, "Id").Id + 1;
        //formvalue["CreatedOn"] = new Date();
        
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          c: "L",
        });

        alertMessage = "Record Added Successfully";
      }
      setTimeout(function () {
        this.props.GetCommonData({
          root: this.state.root,
          cols: GetJsonToArray(this.columnlist, "name"),
          c: "L",
          // con: [{ "Id": id }],
        });
      }.bind(this), 2000);
      this.setState({
        showModal: false,
        showAlert: true, 
        AlertMsg: alertMessage, 
        AlertVarient: "success",
        formValue: "", 
        startTime: {hours: "0", minutes: "0"}, 
        endTime: {hours: "23", minutes: "59"} 
      });

      this.selectedrow="";
    }
    return false;
  }
  handleChange = (e, props) => {
    let value = JSON.stringify(this.state.formvalue);
    let formvalue = JSON.parse(value);

    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }

  filterData(productId,leadRank, agentRank){
    let tableData = this.state.items;
    let filteredData = []
    
    if(productId === "" && leadRank === "" && agentRank === ""){
      return tableData;

    } else if(productId === "" && leadRank === "" && agentRank !== ""){
        tableData.forEach((data) => {
          if(data["AgentRank"].toString().startsWith(agentRank)){
            filteredData.push(data);
          }
        })
    } else if(productId === "" && leadRank !== "" && agentRank === ""){
        tableData.forEach((data) => {
          if(data["LeadRank"].toString().startsWith(leadRank)){
            filteredData.push(data);
          }
        })
    } else if(productId === "" && leadRank !== "" && agentRank !== ""){
        tableData.forEach((data) => {
          if(data["LeadRank"].toString().startsWith(leadRank) && data["AgentRank"].toString().startsWith(agentRank)){
            filteredData.push(data);
          }
        })
    } else if(productId !== "" && leadRank === "" && agentRank === ""){
        tableData.forEach((data) => {
          if(data["ProductID"] === parseInt(productId)){
            filteredData.push(data);
          }
        })
    } else if(productId !== "" && leadRank === "" && agentRank !== ""){
        tableData.forEach((data) => {
          if(data["ProductID"] === parseInt(productId) && data["AgentRank"].toString().startsWith(agentRank)){
            filteredData.push(data);
          }
        })
    } else if(productId !== "" && leadRank !== "" && agentRank === ""){
        tableData.forEach((data) => {
          if(data["LeadRank"].toString().startsWith(leadRank) && data["ProductID"] === parseInt(productId)){
            filteredData.push(data);
          }
        })
    } else if(productId !== "" && leadRank !== "" && agentRank !== ""){
        tableData.forEach((data) => {
          if(data["ProductID"] === parseInt(productId) && data["LeadRank"].toString().startsWith(leadRank) && data["AgentRank"].toString().startsWith(agentRank)){
            filteredData.push(data);
          }
        })
    } 
    return filteredData;
  }

  productchange(e, props) {

    let productId = (e.target.value);
    let leadRank = this.state.leadrank;
    let agentRank = this.state.agentrank;

    const filteredData = this.filterData(productId, leadRank, agentRank);
    this.setState({filteredItems: filteredData, productItem: productId, showAlert: false});
    
  }
  leadranklistchange(e, props) {
    let productId = this.state.productItem;
    let leadRank = (e.target.value);
    let agentRank = this.state.agentrank;

    const filteredData = this.filterData(productId, leadRank, agentRank);
    this.setState({filteredItems: filteredData, leadrank: leadRank, showAlert: false});
  }
  agentranklistchange(e, props) {
    let productId = this.state.productItem;
    let leadRank = this.state.leadrank;
    let agentRank = (e.target.value);

    const filteredData = this.filterData(productId, leadRank, agentRank);
    this.setState({filteredItems: filteredData, agentrank: agentRank, showAlert: false});
  }

  startHoursChange(e) {
    this.setState({
      startTime : {...this.state.startTime, hours: e.target.value},
      ModalValueChanged: true,
    })
  }

  startMinutesChange(e) {
    this.setState({
      startTime : {...this.state.startTime, minutes: e.target.value},
      ModalValueChanged: true,
    })
  }
  endHoursChange(e) {
    this.setState({
      endTime : {...this.state.endTime, hours: e.target.value},
      ModalValueChanged: true,
    })
  }
  endMinutesChange(e) {
    this.setState({
      endTime : {...this.state.endTime, minutes: e.target.value},
      ModalValueChanged: true,
    })
  }

  render() {
    const columns = this.fnDatatableCol();
    const { filteredItems, PageTitle, showModal, FormTitle, formvalue, showAlert, AlertMsg, AlertVarient, ModalValueChanged,event } = this.state;

    let size = this.columnlist.length;

    return (
      <>
        <div className="content">
          <ToastContainer />
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={11}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={1}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTableV1
                    columns={columns}
                    data={filteredItems}
                    getDataOnProdSearch={true}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmLeadAgentRankMapping_NewApp">
                <Row>
                  {this.columnlist.map(col => {
                    size--;
                    if(size == 1){
                      size++;
                      return null;
                    }
                    return fnRenderfrmControl(col, formvalue, this.handleChange, event)
  })}
                  <TimePicker
                    heading="Start Time"
                    hours={this.state.startTime.hours}
                    minutes={this.state.startTime.minutes}
                    hoursChange={this.startHoursChange}
                    minutesChange={this.startMinutesChange}
                  />
                  <TimePicker
                    heading="End Time"
                    hours={this.state.endTime.hours}
                    minutes={this.state.endTime.minutes}
                    hoursChange={this.endHoursChange}
                    minutesChange={this.endMinutesChange} />
                </Row>
                
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
          </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord
  }
)(LeadAgentRankMapping_NewApp);