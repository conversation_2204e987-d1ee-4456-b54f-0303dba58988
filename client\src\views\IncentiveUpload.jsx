import { useEffect, useRef, useState } from "react";
import React from "react";

import {
    GetCommonData, GetCommonspData, HniLogicMaster,
    PostIncentiveData, UpdateData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from './Common/DataTableWithFilter';
import {  getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'
import 'react-datetime/css/react-datetime.css'
import * as XLSX from 'xlsx';
import DropDown from "./Common/DropDown";
import { ButtonGroup, Modal, Form } from 'react-bootstrap';
import ReactDatetimeClass from "react-datetime";
import moment from "moment";
import Loader from './Common/Loader';
import { Button } from "reactstrap";


const IncentiveUpload = (props) => {    
    const [ProductId, setProductId] = useState("");
    const [StartDate, setStartDate] = useState(Date.now());
    const [File, setFile] = useState(null);
    const [TabName, setTabName] = useState("");
    const [IncentiveData, setIncentiveData] = useState([]);
    const [IncentiveLoader, setIncentiveLoader] = useState(false);
    const [DeleteLoader, setDeleteLoader] = useState(false);
    const fileInputRef = useRef(null); // Ref for the file input
    // const handleAddRow = (i) => {
    //     setRows([...rows, { id: i, fileName: "", file: null }]);
    // };
    
    // const handleDeleteRow = (id) => {
    // setRows(rows.filter((row) => row.id !== id));
    // };
    useEffect(() => {
        if (ProductId && StartDate) {
            fetchIncentiveSheets();
        }
    }, [ProductId,StartDate]);

    const handleReset = () => {
        setFile(null); // Reset the selected file state
        if (fileInputRef.current) {
            fileInputRef.current.value = null; // Reset the input file value
        }
    };

    const handleFileChange = (event) => {        
        setFile(event.target.files[0]);
    };

    const handleUpload = async () => {
        if (!ProductId) {
            alert("Please select a product before uploading.");
            return;
        }

        if (!TabName) {
            alert("Please enter a Sheet name before uploading.");
            return;
          }

        if (!File) {
          alert("Please select a file before uploading.");
          return;
        }
        const isPDF = File.name.toLowerCase().endsWith(".pdf");
        if(!isPDF){
            alert("Please select a valid PDF file."); // Error message
            return;
        }
    
        const formData = new FormData();
        formData.append(
            "file",
            File,
            File.name
          );    
        try {
            formData.append("ProductId", ProductId);
            formData.append("StartDate", moment(StartDate).format('YYYY-MM-01 00:00:00'));
            formData.append("TabName", TabName);
            setIncentiveLoader(true);

            let resultsop = await PostIncentiveData(formData);
            if(resultsop && resultsop.data && resultsop.data.data && resultsop.data.data
                && resultsop.data.data.status == 1
              ){
                alert('File uploaded');
                handleReset();
                fetchIncentiveSheets();
                setIncentiveLoader(false);
              }
        } catch (error) {
          console.error("Error uploading file:", error);
          alert("Failed to upload file. Please try again.");
          setIncentiveLoader(false);
        }
    };

    const fetchIncentiveSheets = () => {
        props.GetCommonData({
          root: 'SuperGroupCriteriaHtml',
          c: "L",
          order: 1,
          con: [{ "ProductId": ProductId, "CriteriaMonth": moment(StartDate).format("YYYY/MM/01") , "IsActive": 1}],
        }, function (result) {
          if (result.data && result.data.data && result.data.data[0]) {
            setIncentiveData(result.data.data[0]);
          }
        });
    }

    const ProductList =
    [{ Id: "2", Display: "Health" },{ Id: "147", Display: "Health Renewals" } ,{ Id: "7", Display: "TermLife" },
         { Id: "115", Display: "Investments" }, { Id: "131", Display: "SME/GMC" },{Id:"117",Display:"Motor"},{Id:"139",Display:"Commercial Insurance"}]

    const ProductChange = (e) => {
        setProductId(e.target.value);
    }

    const handleStartChange = (e) => {
        setStartDate(moment(e._d).format("YYYY/MM/DD"))
    }

    const handleChange = (name, e) => {
            switch(name){
                case 'tabname':
                    setTabName(e.target.value);
                    break;
                default:
                    break;
            }
    }

    const DownloadPDF = (PDFUrl, TabName) => {
        const link = document.createElement("a");
        link.href = PDFUrl;
        link.download = TabName.trim(); // Suggested file name for the downloaded PDF
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    const onDelete = (Id) => {
        const confirmDelete = window.confirm("Are you sure you want to delete this item?");
        if(confirmDelete){
        setDeleteLoader(true);
        props.UpdateData({
            root: "SuperGroupCriteriaHtml",
            body: {'IsActive': 0, 
            'UpdatedOn': moment().format("YYYY-MM-DD HH:mm:ss"),
            'UpdatedBy': getuser().UserID},        
            querydata: { "Id": Id },
            c: "L",
          },() => {
            setDeleteLoader(false); 
            fetchIncentiveSheets();
        });
        }
    }

    return (
        <>

        <div className="content fosCityPanel">
            <ToastContainer />
            <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={11}>
                                    <CardTitle tag="h4">Upload Incentive Criterias</CardTitle>                                   
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>

                            <Row>
                                <Col md={4}>
                                    <Form.Label><i>*</i> Product </Form.Label>
                                    <DropDown firstoption="Select Product" items={ProductList} onChange={ProductChange}>
                                    </DropDown><br></br>
                                </Col>
                                <Col md={4}>
                                    <Form.Label>Incentive Month</Form.Label>
                                    <ReactDatetimeClass timeFormat={false} id="StartDate" value={StartDate} name="StartDate" onChange={handleStartChange} />
                                </Col>    

                            </Row>
                            <div>
                                {/* <h4 className="mb-3">Upload Incentive Criterias</h4> */}
                        <table>
                            <thead>
                            <tr>
                                <th>Sheet Name</th>
                                <th>Upload File</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            {
                                <tr>
                                <td>
                                    <input
                                    type="text"
                                    value={TabName}
                                    placeholder="Tabname"
                                    onChange={(event) => handleChange('tabname', event)}
                                    name="tabname"
                                    />
                                </td>
                                <td>
                                    <input
                                    type="file"
                                    accept=".pdf"
                                    onChange={(event) => handleFileChange(event)}
                                    ref={fileInputRef}
                                    />
                                </td>
                                <td>
                                    <button onClick={() => handleUpload()} className="UploadBtn">{IncentiveLoader ? "Uploading..." : "Upload"}{IncentiveLoader && <i className="fa fa-spinner fa-spin" />}</button>
                                </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                        </div>

                        <div>
                       
                        {IncentiveData && IncentiveData.length > 0 && 
                        <><h4 className="mb-3 mt-5">Incentive Criterias Data</h4>
                        <Col md={6}>
                        <table className="table">
                            <thead>
                            <tr>
                                <th>Tab Name</th>
                                <th>Uploaded On</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                        {
                        IncentiveData.map((data, index) => (
                            <tr>
                              <td>{data.TabName  ? data.TabName : 'N.A'}</td>
                              <td>{data.CreatedOn  ? moment.utc(data.CreatedOn).format("YYYY/MM/DD HH:mm:ss") : 'N.A'}</td>
                              <td><div className="delete-incentive d-flex gap-2">
                              <Button variant="danger" className="btn btn-secondary btn-sm UploadBtn" onClick={() => { DownloadPDF(data.CriteriaHtml, data.TabName) }}>DownLoad</Button>
                              <Button variant="danger" className="btn btn-danger btn-sm d-flex align-items-center" onClick={() => onDelete(data.Id)} >
                                <i className="bi bi-trash"></i>
                                Delete
                              </Button></div>
                              </td>
                            </tr>
                          ))
                        }
                            </tbody>
                        </table>
                        </Col></>}
                        </div>
                  
                        </CardBody>
                    </Card>
                </Col>
            </Row>

        </div>
    </>
    );

}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonData,
        UpdateData
    }
)(IncentiveUpload);