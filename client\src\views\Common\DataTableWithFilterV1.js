
import React, { Suspense } from 'react';
import DataTable from 'react-data-table-component';
import { Form } from 'react-bootstrap';
import DropDownList from './DropDownList';
import { Col } from "reactstrap";
import DropDownAutoComplete from './DropDownAutoComplete';

const customStyles = {
    rows: {
        style: {
        minHeight: '72px', // override the row height
        }
    },
    headCells: {
        style: {
        paddingLeft: '8px', // override the cell padding for head cells
        paddingRight: '8px',
        },
    },
    cells: {
        style: {
        paddingLeft: '8px', // override the cell padding for data cells
        paddingRight: '8px',
        },
    },
};

const FilterTextComponent = ({ col, filterText, onFilter }) => (
    <>
        <Form.Group as={Col} md={3} style={{ marginRight:10 , marginBottom:10 }} controlId={col.selector} key={col.selector} >
            <Form.Control 
                type={col.filterType}
                id={col.selector}
                placeholder={"Enter " + col.name}
                value={filterText}
                onChange={(e) => onFilter(e, col)}  
            />
        </Form.Group>
    </>
);

const FilterSelectComponent = ({ col, onFilter }) => (
    <>
        <Form.Group as={Col} md={3} style={{ marginRight:10, marginBottom:10 }} controlId={col.selector} key={col.selector}>
            <DropDownList 
                firstoption={"Select "+col.name} 
                col={col} 
                onChange={(e) => onFilter(e, col) } 
            />
        </Form.Group>
    </>
);

const FilterAutoDropdownComponent = ({ col, onFilter }) => (
    <>
        <Form.Group as={Col} md={3} style={{ marginRight:10, marginBottom:10 }} controlId={col.selector} key={col.selector}>
            <DropDownAutoComplete
                firstoption={"Select "+col.name} 
                col={col} 
                onChange={(e) => onFilter(e, col) } 
            />
        </Form.Group>
    </>
);

const PrintExcelColumn = React.lazy(() => import('./PrintExcelColumn'));

class DataTableWithFilterV1 extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            toggledClearRows: false,
            filtereddata: this.props.data,
            data: this.props.data,
            columns: this.props.columns,
            subHeader: false,
            setFilter: [],
            getDataOnProdSearch : this.props.getDataOnProdSearch || false,
            productSelected: false
        }
        this.handleClearRows = this.handleClearRows.bind(this);
    }

    onSelectedRowsChange(state) {
        this.props.onSelectedRows(state.selectedRows);
    }

    handleClearRows() {
        this.setState({ toggledClearRows: !this.state.toggledClearRows });
    }

    componentWillMount() {
        this.setState({
            filtereddata: this.props.data,
            data: this.props.data,
            columns: this.props.columns
        });
        if (this.props.data && this.props.data.length > 0) {
            this.fnSubHeder();
        } else{
            this.setState({ subHeader: false, subHeaderComponent: [] });
        }
    }

    componentWillUpdate() {

    }

    componentWillReceiveProps(newprops, oldprops) {
        this.setState({
            filtereddata: newprops.data,
            data: newprops.data,
            columns: newprops.columns
        });

        this.fnFilteredData(newprops.data);
        if (newprops.data && newprops.data.length > 0) {
            this.fnSubHeder();
        } else{
            this.setState({ subHeader: false, subHeaderComponent: [] });
        }
    }

    onTextFilter(e, col) {
        let filters = this.state.setFilter;
        filters[col.selector] = e.target.value;
        this.setState({ setFilter: filters });
        console.log("filters", filters);
        this.fnFilteredData(this.state.data, filters);
    }

    onSelectFilter(e, col) {
        let filters = this.state.setFilter;
        var index = e.target.selectedIndex;
        if (index == 0) {
            console.log("filters", filters);
            if(this.state.getDataOnProdSearch && col.name === "Product"){
                this.setState({"productSelected" : false})
            }
            delete filters[col.selector];
        } else {
            filters[col.selector] = e.target[index].text;
            if(this.state.getDataOnProdSearch && col.name === "Product" ){
                this.setState({"productSelected" : true})
            }
        }
        this.setState({ setFilter: filters });
        console.log("filters", filters);
        this.fnFilteredData(this.state.data, filters);
    }

    onAutoDropdownFilter(e, col) {
        let filters = this.state.setFilter;
        if(e.target.value == ''){
            delete filters[col.selector];
        } else {
            filters[col.selector] = e.target.text;
        }
        this.setState({ setFilter: filters });
        console.log("filters", filters);
        this.fnFilteredData(this.state.data, filters);
    }

    handleTextClear() {

    }

    fnSubHeder() {
        let searchables = [];
        let that = this;
        this.state.columns.forEach((col,index) => {
            if (col.searchable) {
                if (col.type === "dropdown" && col.config) {
                    searchables.push(<FilterSelectComponent
                        col={col}
                        onFilter={that.onSelectFilter.bind(this)}
                        key={index}
                    />);
                } 
                else if(col.type === "autodropdown" && col.config) {
                    searchables.push(<FilterAutoDropdownComponent
                        col={col}
                        onFilter={that.onAutoDropdownFilter.bind(this)}
                        key={index}
                    />);
                } 
                else {
                    searchables.push(<FilterTextComponent
                        col={col}
                        onFilter={that.onTextFilter.bind(this)}
                        onClear={that.handleTextClear}
                        key={index}
                    /> );
                }
            }
        });
        if (searchables.length > 0) {
            this.setState({ subHeader: true, subHeaderComponent: searchables });
        }
    }

    fnFilteredData(data, Filter) {
        data = data ?? this.state.data;
        Filter = Filter ?? this.state.setFilter;
        for (var key in Filter) {
            data = this.fnFilteredItems(data, key, Filter[key]);
        }
        this.setState({ filtereddata: data });
    }

    fnFilteredItems(items, filterName, filterValue) {
        if (filterValue == "") {
            return items;
        }

        if (typeof (items) != 'undefined') {
            const filteredItems = items.filter(item => String(item[filterName]).toString().toUpperCase().indexOf(filterValue.toUpperCase()) > -1);
            return filteredItems;
        } else {
            return items;
        }
    }

    render() {
        let extention = this.props.extention === undefined ? true : this.props.extention;
        var d = new Date();
        var n = d.getTime();
        if (extention)
            return (
                <div className="content">
                    <Suspense fallback={<div>Loading...</div>}>
                    { this.props.printexcel == false ? '' : <PrintExcelColumn data={this.state.filtereddata} columnNames={this.state.columns} fileName={this.props.fileName? this.props.fileName : document.title ? document.title : n.toString()} /> }
                        <DataTable
                            style={{ marginBottom:'0px', paddingBottom:'0px' }}
                            columns={this.state.columns}
                            data={(!this.state.getDataOnProdSearch || this.state.productSelected) ? this.state.filtereddata : []}
                            defaultSortField={this.props.defaultSortField}
                            defaultSortAsc={this.props.defaultSortAsc}
                            striped={true}
                            noHeader
                            fixedHeader={false}
                            pagination={this.props.pagination === undefined ? true : this.props.pagination}
                            paginationPerPage={this.props.paginationPerPage === undefined ? 100 : this.props.paginationPerPage}
                            paginationRowsPerPageOptions={[100, 150, 200, 300]}
                            dense
                            highlightOnHover
                            responsive={true}
                            overflowY={true}
                            customStyles={customStyles}
                            selectableRows={this.props.selectableRows === undefined ? false : this.props.selectableRows}
                            onSelectedRowsChange={this.onSelectedRowsChange.bind(this)}
                            clearSelectedRows={this.state.toggledClearRows}
                            selectableRowsNoSelectAll={this.props.selectableRowsNoSelectAll === undefined ? true : this.props.selectableRowsNoSelectAll}
                            subHeader={this.state.subHeader}
                            subHeaderAlign={'left'}
                            subHeaderComponent={this.state.subHeaderComponent}
                            exportCSV={true}
                        />
                    </Suspense>
                </div>
            );
        else {
            return (
                <div className="content">
                    <DataTable
                        defaultSortField={this.props.defaultSortField}
                        defaultSortAsc={this.props.defaultSortAsc}
                        columns={this.state.columns}
                        data={(!this.state.getDataOnProdSearch || this.state.productSelected) ? this.state.data : []}
                        striped={true}
                        noHeader={true}
                        pagination={this.props.pagination === undefined ? true : this.props.pagination}
                        paginationPerPage={100}
                        paginationRowsPerPageOptions={[100, 150, 200, 300]}
                        dense
                        highlightOnHover
                        responsive={true}
                        overflowY={true}
                        customStyles={customStyles}
                        selectableRows={this.props.selectableRows === undefined ? false : this.props.selectableRows}
                        onSelectedRowsChange={this.onSelectedRowsChange.bind(this)}
                        clearSelectedRows={this.state.toggledClearRows}
                        //selectableRowsNoSelectAll={true}
                        selectableRowsNoSelectAll={this.props.selectableRowsNoSelectAll === undefined ? true : this.props.selectableRowsNoSelectAll}
                    />
                </div>
            );
        }
    }
}

export default DataTableWithFilterV1;
