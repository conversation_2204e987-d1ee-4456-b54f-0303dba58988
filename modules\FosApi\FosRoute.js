var express = require("express");
const router = express.Router();
const controller = require("./FosController");
const { ACLlayer } = require("../ACLlayer");


// router.get("getAppointmentSlots", controller.getAppointmentSlots);
// router.get("getAppointmentsBySlotId", controller.getAppointmentsBySlotId);
router.post("/SetAppointmentData",ACLlayer ,controller.SetAppointmentData);
router.post("/SlotLeadAssignment",ACLlayer , controller.SlotLeadAssignment);
router.post("/FOSRealTimeStatus" ,controller.FosRealTimeStatus);
router.post("/FOSCallingStatus" ,controller.FOSCallingStatus);
router.post("/FetchAgentLatLong" ,controller.FetchAgentLatLong);
router.post("/FetchAgentLatLongByTime" , controller.FetchAgentLatLongByTime);
router.post("/OTSData", controller.OTSData);
router.get("/GetFosUserDetails", controller.GetFosUserDetails);
router.post("/IsSocialMediaAgent", controller.IsSocialMediaAgent);
//router.post("/ValidateAddLeadToPriorityQueue", controller.ValidateAddLeadToPriorityQueue);

module.exports = router;

