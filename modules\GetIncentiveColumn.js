const moment = require("moment");

function GetIncentiveColumn(FileType, ProductID ){
    let columnList = []
    let collection = ""
    if(FileType == "1" && ProductID == "7"){
        collection = "TermAgentLevelData"
        columnList =  [
        {name :"Ecode", type: "string"},
        {name: "Salary", type: "numericalString"},
        {name: "Process", type: "string"},
        {name: "Supergroupid", type: "int"},
        {name: "SourcedAPE", type: "int"},
        {name: "IssuedAPE", type: "int"},
        {name: "WeightedAPE", type: "float"},
        {name: "SourceBKGs", type: "int"},
        {name: "IssuedBkg", type: "int"},
        {name: "AmountMade", type: "float"},
        {name: "FinalIncentive", type: "numericalString"},
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType == "2" && ProductID == "7"){
        collection = "TermBookingLevelData"
        columnList = [
            {name :"BookingDate", type: "date"},
            {name :"LeadID", type: "int"},
            {name :"Product", type: "string"},
            {name :"APE", type: "int"},
            {name :"StatusName", type: "string"},
            {name :"PaymentPeriodicity", type: "string"},
            {name :"IsNRI", type: "int"},
            {name :"IssuanceFlag", type: "int"},
            {name :"SalesAgent", type: "string"},
            {name :"WeightageAPE", type: "float"},
            {name :"CreditStatus", type: "string"},
            {name :"ISEligibleforIncentive", type: "int"},
            {name :"ReasonForNonEligible", type: "string"},
            {name :"IncentiveMonth", type: "date"},
        ]
    }
    if(FileType == "1" && ProductID == "115"){
        collection = "InvestmentAgentLevelData"
        columnList =  [
        {name :"Ecode", type: "string"},
        {name: "Process", type: "string"},
        {name: "Supergroupid", type: "int"},
        {name: "SourcedAPE", type: "int"},
        {name: "IssuedAPE", type: "int"},
        {name: "WeightedAPE", type: "int"},
        {name: "Salary", type: "int"},
        {name: "AmountMade", type: "int"},
        {name: "FinalIncentive", type: "int"},
        {name: "SourceBKGs", type: "int"},
        {name: "IssuedBkg", type: "int"},
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType == "2" && ProductID == "115"){
        collection = "InvestmentBookingData"
        // BookingDate	LeadID	Product	StatusName	SalesAgent	PaymentPeriodicity	CreditStatus	APE	WeightageAPE	InsurerCategory	IssuanceFlag	ISEligibleforIncentive	ReasonForNonEligible	IncentiveMonth
        columnList =  [
            {name :"BookingDate", type: "date"},
            {name :"LeadID", type: "int"},
            {name :"Product", type: "string"},
            {name :"StatusName", type: "string"},
            {name :"SalesAgent", type: "string"},
            {name :"PaymentPeriodicity", type: "string"},
            {name :"CreditStatus", type: "string"},
            {name :"APE", type: "int"},
            {name :"WeightageAPE", type: "float"},
            {name :"InsurerCategory", type: "string"},
            {name :"IssuanceFlag", type: "int"},
            {name :"ISEligibleforIncentive", type: "int"},
            {name :"ReasonForNonEligible", type: "string"},
            {name :"IncentiveMonth", type: "date"}            
    ]}
    if(FileType == "1" && ProductID == "2"){
        collection = "HealthAgentLevelData"
        //Ecode	Process	Supergroupid	Slab	SourcedAPE	IssuedAPE	WeightedAPE	SourceBKGs	IssuedBkg	EligibleBkg	AmountMade	FinalIncentive	Salary	IncentiveMonth
        columnList =  [
        {name :"Ecode", type: "string"},
        {name: "Process", type: "string"},
        {name: "Supergroupid", type: "int"},
        {name: "Slab", type: "int"},
        {name: "SourcedAPE", type: "int"},
        {name: "IssuedAPE", type: "int"},
        {name: "WeightedAPE", type: "float"},
        {name: "SourceBKGs", type: "int"},
        {name: "IssuedBkg", type: "int"},
        {name: "EligibleBkg", type: "int"},
        {name: "AmountMade", type: "float"},
        {name: "FinalIncentive", type: "float"},
        {name: "Salary", type: "float"},
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType == "1" && ProductID == "117"){
        collection = "MotorAgentLevelData"
       //ECode	Process	Supergroupid	Salary	TotalIncentive	IncentiveWithJustification	FinalIncentive	IncentiveMonth	QualityScore	TotalBookings
        columnList =  [
        {name :"ECode", type: "string"},
        {name: "Process", type: "string"},
        {name: "Supergroupid", type: "int"},
        {name: "Salary", type: "float"},
        {name: "TotalIncentive", type: "float"},
        {name: "FinalIncentive", type: "float"},
        {name: "IncentiveWithJustification", type: "int"},
        {name: "QualityScore", type: "float"},
        {name: "TotalBookings", type: "int"},        
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType === "5" && ProductID === "117"){
        collection = "MotorPolicyTypeData"
        columnList =  [
        {name :"Ecode", type: "string"},
        {name: "TP", type: "int"},
        {name: "FosTP", type: "int"},
        {name: "Com", type: "int"},
        {name: "FosCom",type: "int"},
        {name: "TotalBookings", type: "int"},
        {name: "Slab_TP_FosTP", type: "int"},
        {name: "Slab_Com_FosCom", type: "int"},
        {name: "IncentivePool", type: "int"},
        {name: "IncentiveMonth", type: "date"}

    ]}
    if(FileType == "2" && ProductID == "117"){
        collection = "MotorBookingLevelData"
       //BookingDate	Leadid	InsurerShortName	Planname	Status	Eligible	InsurerType	Issued	PlanType	SalesAgent	IncentiveMonth
        columnList =  [
            {name :"BookingDate", type: "date"},
            {name :"Leadid", type: "int"},
            {name :"InsurerShortName", type: "string"},
            {name :"Planname", type: "string"},
            {name :"Status", type: "string"},
            {name :"Eligible", type: "int"},
            {name :"InsurerType", type: "string"},
            {name :"Issued", type: "int"},
            {name :"PlanType", type: "string"},
            {name :"SalesAgent", type: "string"},
            {name :"IncentiveMonth", type: "date"},
            
    ]}
    if(FileType == "2" && ProductID == "2"){
        collection = "HealthBookingLevelData"
        //BookingDate	LeadID	InsurerShortName	IsaddOn	APE	StatusName	PaymentPeriodicity	SalesAgent	EligFlag	WeightageAPE	MissComt	FreeLook	IssuanceFlag	IssuedAPE	EligibleAPE	Reason	IncentiveMonth
        columnList =  [
            {name :"BookingDate", type: "date"},
            {name :"LeadID", type: "int"},
            {name :"InsurerShortName", type: "string"},
            {name :"IsaddOn", type: "int"},
            {name :"APE", type: "int"},
            {name :"StatusName", type: "string"},
            {name :"PaymentPeriodicity", type: "string"},
            {name :"SalesAgent", type: "string"},
            {name :"EligFlag", type: "int"},
            {name :"WeightageAPE", type: "int"},
            {name :"MissComt", type: "int"},
            {name :"FreeLook", type: "int"},
            {name :"IssuanceFlag", type: "int"},
            {name :"IssuedAPE", type: "int"},
            {name :"EligibleAPE", type: "int"},
            {name :"Reason", type: "string"},
            {name :"IncentiveMonth", type: "date"}            
    ]}
    if(FileType == "1" && ProductID == "147"){
        collection = "HealthRenewalAgentLevelData"
        columnList =  [
        {name :"Employeeid", type: "string"},
        {name :"Product", type: "string"},
        {name: "Process", type: "string"},
        {name: "Subcategory", type: "string"},
        {name: "Qualifier", type: "int"},
        {name: "Target", type: "int"},
        {name: "AchievedLeads", type: "int"},
        {name: "IssuedLeads", type: "int"},
        {name: "AchivedBkgsPending", type: "int"},
        {name: "AdditionBkgIncentive", type: "numericalString"},
        {name: "AdditionIncentivePerBkg", type: "int"},
        {name: "TotalLeads", type: "int"},
        {name: "IssuedConvPercentage", type: "float"},
        {name: "AddonUpsellIncentive", type: "numericalString"},
        {name: "AddonUpsellAmt", type: "numericalString"},
        {name: "GraceUpsellAmt", type: "numericalString"},
        {name: "UpsellAmt", type: "numericalString"},
        {name: "UpsellIncentivePre", type: "int"},
        {name: "UpsellIncentive", type: "int"},
        {name: "Incentive", type: "int"},
        {name: "SameMonthIncentive", type: "int"},
        {name: "NOPTargetMet", type: "int"},
        {name: "TotalIncentive", type: "int"},
        {name: "FinalIncentive", type: "numericalString"},
        {name: "AddOnAssignedLeads", type: "int"},
        {name: "AddonAchivedLeads", type: "int"},
        {name: "AchievedConvPerncetage", type: "float"},
        {name: "AddonIncentive", type: "int"},
        {name: "IncentiveMonth", type: "date"},
        {name: "AddonBookingTarget", type: "int"}
        
    ]}

    if(FileType == "6"){
        collection = "MissSellData"
        columnList =  [
        {name :"Ecode", type: "string"},
        {name :"LeadId", type: "int"},
        {name: "BookingDate", type: "date"},
        {name: "RollingCycleStartdate", type: "date"},
        {name: "RollingCycleEndDate", type: "date"},
        {name: "IdentifiedMonth", type: "string"},
        {name: "IdentifiedBy", type: "string"},
        {name: "IncentiveMonth", type: "date"},
        {name: "Remark", type: "string"},
    ]}

    if(FileType == "1" && ProductID == "217"){
        collection = "MotorRenewalAgentLevelData"
       //ECode	Process	Supergroupid	Salary	TotalIncentive	IncentiveWithJustification	FinalIncentive	IncentiveMonth	QualityScore	TotalBookings
        columnList =  [
        {name :"Ecode", type: "string"},
        {name :"Agentid", type: "int"},
        {name :"UserName", type: "string"},
        {name: "Process", type: "string"},
        {name: "Supergroupid", type: "int"},
        {name: "Salary", type: "float"},
        {name: "IncentivePool", type: "float"},
        {name: "IncentiveAfterJustification", type: "float"},
        {name: "FinalIncentive", type: "float"},
        {name: "TotalBookings", type: "int"},    
        {name: "TargetBooking", type: "int"},    
        {name: "Achievement", type: "float"},
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType == "5" && ProductID == "217"){
        collection = "MotorRenewalPolicyTypeData"
        columnList =  [
        {name :"Ecode", type: "string"},
        {name: "YearsTPPrevYear", type: "int"},
        {name: "Iffco", type: "int"},
        {name: "OthersTP", type: "int"},
        {name: "Rollover", type: "int"},
        {name: "PB", type: "int"},
        {name: "NonPB", type: "int"},
        {name: "Type", type: "string"},
        {name: "FinalIncentive", type: "float"},
        {name: "TotalBooking", type: "int"},
        {name: "IncentiveMonth", type: "date"},

    ]}
    if(FileType == "2" && ProductID == "217"){
        collection = "MotorRenewalBookingLevelData"
       //BookingDate	Leadid	InsurerShortName	Planname	Status	Eligible	InsurerType	Issued	PlanType	SalesAgent	IncentiveMonth
        columnList =  [
        {name :"BookingDate", type: "date"},
        {name :"LeadId", type: "int"},
        {name :"Product", type: "string"},
        {name :"SupplierName", type: "string"},
        {name :"StatusName", type: "string"},
        {name :"PaymentPeriodicity", type: "string"},
        {name :"PolicyType", type: "string"},
        {name :"Ecode", type: "string"},
        {name :"Category", type: "string"},
        {name :"Eligible", type: "string"},
        {name :"Remark", type: "string"},
        {name: "IncentiveMonth", type: "date"},
    ]}
    if(FileType == "7" && ProductID == "217"){
        collection = "MotorRenewalSlabSystem"
       //BookingDate	Leadid	InsurerShortName	Planname	Status	Eligible	InsurerType	Issued	PlanType	SalesAgent	IncentiveMonth
        columnList =  [
        
        {name :"Process", type: "string"},
        {name :"Supergroupid", type: "int"},
        {name :"YearsTPPrevYear", type: "int"},
        {name :"Iffco", type: "int"},
        {name :"OthersTP", type: "int"},
        {name :"Rollover", type: "int"},
        {name :"PB", type: "int"},
        {name :"NonPB", type: "int"},
        {name: "IncentiveMonth", type: "date"},
    ]}

    return [columnList, collection]

}
function unWantedCharacters(value, columnName){
    var format = /[@#$%^&*\[\]{};:\\|\/?]+/;
    if( ["ReasonForNonEligible", "Reason", "Remark"].includes(columnName)){
        return false;
    }
    if(format.test(value)){
        return true;
      } else {
        return false;
      }
}
function dateCheck(month, incentiveMonth){
    let myDate = new Date(Math.round((month - 25569)*86400*1000))
    let dateValue = moment(myDate).format('DD-MM-YYYY');
    if(dateValue == incentiveMonth){
        return true
    }
    return false
}
function typeConvert(value, datatype){
    if(datatype == "string"){
        return value
    }
    if(datatype == "numericalString"){
        return value.toLocaleString().toString()
    }
    if(datatype == "int"){
        return parseInt(value).toString()
    }
    if(datatype == "float"){
        return parseFloat(value).toFixed(2).toString()
    }
    if(datatype == "date"){
        let myDate = new Date(Math.round((value - 25569)*86400*1000))
        let dateValue = moment(myDate).format('DD-MM-YYYY');
        return dateValue
    }
}

module.exports ={
    GetIncentiveColumn,
    typeConvert,
    unWantedCharacters,
    dateCheck
}