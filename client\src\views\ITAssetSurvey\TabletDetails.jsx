import React, { Component } from 'react';

import RadioButton from '../Common/RadioOptions';
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import {InsertData} from "../../store/actions/CommonAction";
import {
    Row,
    Col
} from "reactstrap";
import moment from 'moment';
import { connect } from "react-redux";



class AssetDetails extends Component{
    constructor(props) {
        super(props);
    }
    saveAndContinue = (e) => {
        e.preventDefault();
        const { values } = this.props;

        if (this.props.onValidation("part2", values)) {
        this.props.SaveAssetDetails(values);   
        this.props.nextStep(values);
         }
    }


    back  = (e) => {
        e.preventDefault();
        this.props.prevStep();
    }

    render(){
        this.QuestionTwoAnswer = [["8 Inch", "8-Inch"], ["10 Inch", "10-Inch"]]       

        const { values, errors } = this.props
        return(
        <Form color='blue' >
            <Col md={12} className="pull-left mt-4">
            <Form.Label>Serial Number <span>* </span> <span style={{color: "blue"}}><a onClick={this.props.showdetails.bind(this)} >
            What's this ?</a></span></Form.Label>
            <Form.Control type="text" placeholder="Your answer" name="serialno" value={values.serialno} className="surveytextfield" id="serialno" onChange={this.props.handleChange("serialno")} />
            <span style={{color: "red"}}>{errors.serialno}</span>
            </Col>                                        
            <Col md={12} className="pull-left mt-4">
            <Form.Text className="questions" id="question2">
                    <p class="survey-text mb-3">Tablet Size <span>*</span></p>
            </Form.Text>
            <RadioButton 
                name= "question2"
                changed={this.props.handleChange("question2")}//{ this.setAnswerTwo.bind(this) } 
                items = {this.QuestionTwoAnswer}
                isSelected={ values.question2} 
            />
            <span style={{color: "red"}}>{errors.question2}</span>
            </Col>
           
            <Col md={4} className="pull-left mt-4">
            <Button onClick={this.back}>Back</Button>
            </Col>
            <Col md={6} className="pull-left mt-4">
            <Button onClick={this.saveAndContinue}>Submit Details </Button>
            </Col>
        </Form>
        )
    }
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        InsertData,
    }
)(AssetDetails)
