@import './variables.scss';
body{
    background-color: #F4F6F8;
}
.page-container {
    padding: 60px 20px;
}

.pagination {
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem;
    justify-content: center;
}
.page-item.active .page-link
{
    background-color: #4556ac;
    border-color: #4556ac;
}

.page-link {
    color: #4556ac; 
}

.single-panel-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .upload-panel{
        display: flex;
        margin-top: 30px;
    }

    .upload-section {
        border-right: 1px solid #aaa;
        padding-top: 25px;
        width: 50%;
        margin-right:20px;
    }

    .drop-section {
        border: 1px dashed #aaa;
        border-radius: 5px;
        color: #73879C;
        font-size: 18px;
        height: 70px;
        line-height: 30px;
        margin-left: 20px;
        padding-top: 35px;
        text-align: center;
        width: 100%;

        label {
            color: $link-color;
            margin-right: 0px;
            text-decoration: underline;
        }

        label:hover {
            cursor: pointer;
            text-decoration: underline;
        }
    }

    .file-drop-msg {
        font-size: 14px;
    }

    .btn-group {
        justify-content: center;
        margin-top: 25px;
    }

    .create-ruleset-input {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
}

.browse-label {
    background-color: $cancel-btn-color;
    border-radius: 5px;
    color: $btn-color;
    font-size: 16px;
    font-family: $form-font-family;
    height: 40px;
    line-height: 40px;
    margin-top: 20px;
    outline:none;
    padding-top: 0;
    text-align: center;
    width: 140px;
}

.home-container {
    display: flex;
    flex-direction: column;
    height: 90vh;
    justify-content: space-between;
}

.footer-container {
    display: flex;

    &.home-page {
        flex-direction: row-reverse;

        a {
            font-size: 14px;
        }
    }

    &.sidenav {
        flex-direction: column-reverse;

        a {
            color: $btn-color;
            font-size: 14px;
        }
    }

    div {
        margin-left: 20px;
        margin-bottom: 20px;
    }
    
}
