<svg width="1690" height="451" viewBox="0 0 1690 451" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9185_267)">
<g filter="url(#filter0_f_9185_267)">
<mask id="mask0_9185_267" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="16" y="16" width="461" height="419">
<rect x="16" y="16" width="461" height="419" rx="209.5" fill="#001B49"/>
</mask>
<g mask="url(#mask0_9185_267)">
<rect x="80" y="-6.44641" width="64" height="210.034" rx="32" fill="#F6D420"/>
<rect x="12" y="-6.44641" width="64" height="210.034" rx="32" fill="#F6D420" fill-opacity="0.5"/>
<rect x="352" y="-6.44641" width="64" height="210.034" rx="32" fill="#20B6F6"/>
<rect x="420" y="-6.44641" width="64" height="210.034" rx="32" fill="#20B6F6" fill-opacity="0.5"/>
<rect x="284" y="-6.44641" width="64" height="210.034" rx="32" fill="#F620D4"/>
<rect x="216" y="-6.44641" width="64" height="210.034" rx="32" fill="#20F69C"/>
<rect x="148" y="-6.44641" width="64" height="210.034" rx="32" fill="#F68720"/>
</g>
</g>
<g filter="url(#filter1_f_9185_267)">
<mask id="mask1_9185_267" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="656" y="16" width="461" height="419">
<rect x="656" y="16" width="461" height="419" rx="209.5" fill="#001B49"/>
</mask>
<g mask="url(#mask1_9185_267)">
<rect x="664" y="-42" width="85" height="281" rx="42.5" fill="#F6D420"/>
<rect x="573" y="-42" width="85" height="281" rx="42.5" fill="#F6D420" fill-opacity="0.5"/>
<rect x="1027" y="-42" width="85" height="281" rx="42.5" fill="#20B6F6"/>
<rect x="936" y="-42" width="85" height="281" rx="42.5" fill="#F620D4"/>
<rect x="845" y="-42" width="86" height="281" rx="43" fill="#20F69C"/>
<rect x="755" y="-42" width="85" height="281" rx="42.5" fill="#F68720"/>
</g>
</g>
<g filter="url(#filter2_f_9185_267)">
<mask id="mask2_9185_267" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1213" y="16" width="461" height="419">
<rect x="1213" y="16" width="461" height="419" rx="209.5" fill="#001B49"/>
</mask>
<g mask="url(#mask2_9185_267)">
<rect x="1342" y="34" width="39" height="129" rx="19.5" fill="#F6D420"/>
<rect x="1300" y="34" width="39" height="129" rx="19.5" fill="#F6D420" fill-opacity="0.5"/>
<rect x="1509" y="34" width="39" height="129" rx="19.5" fill="#20B6F6"/>
<rect x="1551" y="34" width="39" height="129" rx="19.5" fill="#20B6F6" fill-opacity="0.5"/>
<rect x="1467" y="34" width="39" height="129" rx="19.5" fill="#F620D4"/>
<rect x="1425" y="34" width="40" height="129" rx="20" fill="#20F69C"/>
<rect x="1384" y="34" width="39" height="129" rx="19.5" fill="#F68720"/>
</g>
</g>
</g>
<rect x="0.5" y="0.5" width="1689" height="450" rx="4.5" stroke="#9747FF" stroke-dasharray="10 5"/>
<defs>
<filter id="filter0_f_9185_267" x="-84" y="-84" width="661" height="619" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_9185_267"/>
</filter>
<filter id="filter1_f_9185_267" x="556" y="-84" width="661" height="619" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_9185_267"/>
</filter>
<filter id="filter2_f_9185_267" x="1113" y="-84" width="661" height="619" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_9185_267"/>
</filter>
<clipPath id="clip0_9185_267">
<rect width="1690" height="451" rx="5" fill="white"/>
</clipPath>
</defs>
</svg>
