
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import moment from 'moment';
// import {
//   GetCommonData, addRecord, UpdateData, DeleteData
// } from "../store/actions/CommonMongoAction";

import { GetCommonData, InsertData, UpdateData, DeleteData } from "../store/actions/CommonAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownListMongo';

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";

class AgentSurveyQuestions extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoaded: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      root: "SurveyQuestionMaster",
      departmentId: "0",
      valid_sid: true,
      ParentOption: "",

      PageTitle: "",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      editFieldOptions: false
    };
    this.handleRemove = this.handleRemove.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleAddNested = this.handleAddNested.bind(this);
    this.selectedrow = {
      "QuestionText": "", "FieldOptions": "", "SurveyID": getUrlParameter("sid")
      // , "isOutbound": false, "chatbot": false, "offline": false, "newcar": false 
    }
    this.FieldOptionsList = ['textarea', 'number', 'string', 'email', 'empty', 'link', 'embedded'];
    this.SurveyMasterList = [
      {
        name: "SurveyId",
        label: "SurveyId",
        type: "number"
      },
      {
        name: "SurveyName",
        label: "SurveyName",
        type: "string"
      }
    ];
    this.columnlist = [
      {
        name: "QuestionID",
        label: "QuestionID",
        type: "number",
        searchable: true,
        editable: false
      },
      {
        name: "QuestionText",
        label: "QuestionText",
        type: "string",
        searchable: true,
        required: true
        // editable: false
      },
      {
        name: "SurveyID",
        label: "SurveyID",
        type: "number",
        // searchable: true,
        editable: false
      },
      {
        name: "FieldType",
        label: "FieldType",
        type: "string",
        searchable: true,
        editable: false
      },
      {
        name: "FieldOptions",
        label: "FieldOptions",
        type: "string",
        searchable: true,
        required: true
      },
      {
        name: "IsRequired",
        label: "IsRequired",
        type: "bool",
        searchable: true,
        required: true
      }
    ];
  }



  componentDidMount() {
    let sid = getUrlParameter("sid");
    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: this.state.root,
      order: 1,
      cols: GetJsonToArray(this.columnlist, "name"),
      con: [{ "SurveyID": sid }],
      c: "L",
    });

    this.props.GetCommonData({
      limit: 10,
      skip: 0,
      root: "SurveyMaster",
      cols: GetJsonToArray(this.SurveyMasterList, "name"),
      con: [{ "SurveyId": sid }],
      c: "L",
    });

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);
  }



  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {

      if (nextProps.CommonData['SurveyMaster'] && nextProps.CommonData['SurveyMaster'][0]) {
        this.setState(() => ({
          valid_sid: false,
          PageTitle: nextProps.CommonData['SurveyMaster'][0].SurveyName
        }))
      }

      this.setState({ items: nextProps.CommonData[this.state.root] });

      this.setState({ store: nextProps.CommonData });

    }

  }


  fnDatatableCol() {

    var columns = fnDatatableCol(this.columnlist);

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleAddNested(row)} disabled={row.FieldType != 'single select' && row.FieldType != 'dropdown'}>
          <i className="fa fa-plus" aria-hidden="true"></i>
        </Button>
        <Button variant="secondary" onClick={() => { if (window.confirm('Are you sure you wish to delete this quesiton?')) this.handleRemove(row) }}><i className="fa fa-trash" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }

  handleRemove(row) {

    this.props.DeleteData({
      root: this.state.root,
      query: { QuestionID: row.QuestionID }
    }, function (data) {
      if (data.data.status === 200)
        toast("Question removed!", { type: 'success' });
      else
        toast.error("Question could not be removed");
    })

    this.props.DeleteData({
      root: "SurveyNestedQuestionMaster",
      query: { NestedQuestionId: row.QuestionID }
    })

    setTimeout(function () {
      let sid = getUrlParameter("sid");
      this.props.GetCommonData({
        limit: 10,
        skip: 0,
        order: 1,
        root: this.state.root,
        cols: GetJsonToArray(this.columnlist, "name"),
        con: [{ "SurveyID": sid }],
        c: "L",
      });
    }.bind(this), 1000);

  }
  handleEdit(row) {
    if (!this.FieldOptionsList.includes(row.FieldType)) {
      this.setState(() => ({
        editFieldOptions: true
      }));
    } else {
      this.setState(() => ({
        editFieldOptions: false
      }));
    }
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Question" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: { ...this.selectedrow }, event: "Add", showModal: true, FormTitle: "Add New Question" });
  }
  handleAddNested(row) {
    this.setState({ formvalue: { ...this.selectedrow, "ParentQuestionID": row.QuestionID }, event: "AddNested", ParentOption: row.FieldOptions, showModal: true, FormTitle: "Add New Nested Question" });
  }

  handleSave() {
    if (document.getElementsByName("frmChatAgentConfigure").length > 0 &&
      document.getElementsByName("frmChatAgentConfigure")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      console.log("formvalue", formvalue);
      this.fnCleanData(formvalue);
      let qid = formvalue.QuestionID;
      delete formvalue["QuestionID"];

      formvalue['FieldOptions'] = formvalue['FieldOptions'].replace(/\s*,\s*/g, ",");
      formvalue['FieldOptions'] = formvalue['FieldOptions'].trim();

      if (formvalue.FieldType == 'single select') formvalue.FieldType = 'radio';
      if (formvalue.FieldType == 'multi-select') formvalue.FieldType = 'checkbox';

      if (this.FieldOptionsList.includes(formvalue.FieldType)) {
        formvalue.FieldOptions = "";
      }


      let ParentQuestionID;
      let ParentOptionSelected;
      if (formvalue.ParentQuestionID) {
        ParentQuestionID = formvalue.ParentQuestionID;
        ParentOptionSelected = formvalue.ParentOption;
        console.log('---nested----', formvalue.ParentQuestionID, formvalue.ParentOption);
        delete formvalue["ParentOption"];
        delete formvalue["ParentQuestionID"];
      }

      if (this.state.event == "Edit") {
        this.fnCleanData(formvalue);

        let res = this.props.UpdateData({
          root: this.state.root,
          body: formvalue,
          querydata: { QuestionID: qid },
          c: "L",

        }, function (data) {
          toast("Question Saved Successfully!", { type: 'success' });
        });

      } else {
        formvalue.CreatedOn = moment().format("YYYY-MM-DD HH:mm:ss");
        this.props.InsertData({
          root: this.state.root,
          body: formvalue,
          scope: true
        }, (data) => {
          if (data.data.status != 200) {
            toast.error(data.data.message);
          } else {
            if (ParentQuestionID) {
              let NestedQuestionId = data.data.data.recordset[0][""];
              console.log('////////', ParentOptionSelected, ParentQuestionID, NestedQuestionId);
              this.props.InsertData({
                root: "SurveyNestedQuestionMaster",
                body: {
                  NestedQuestionId,
                  ParentQuestionId: ParentQuestionID,
                  SelectedOption: ParentOptionSelected
                }
              }, (result) => {
                if (result.data.status != 200) {
                  toast.error(result.data.message);
                } else {
                  toast("Question Added Successfully!", { type: 'success' });
                }
              });
            } else
              toast("Question Added Successfully!", { type: 'success' });
          }
        });
        console.log('---------', formvalue);
      }
      // let departmentid = formvalue["departmentId"];

      setTimeout(function () {
        let sid = getUrlParameter("sid");
        this.props.GetCommonData({
          limit: 10,
          skip: 0,
          order: 1,
          root: this.state.root,
          cols: GetJsonToArray(this.columnlist, "name"),
          con: [{ "SurveyID": sid }],
          c: "L",
        });
      }.bind(this), 1000);

      this.setState({ showModal: false });
    }
    return false;
  }

  handleChange = (e, props) => {
    if (e.target.id == 'FieldType') {
      if (!this.FieldOptionsList.includes(e.target.value)) {
        this.setState(() => ({
          editFieldOptions: true
        }));
      } else {
        this.setState(() => ({
          editFieldOptions: false
        }));
      }
    }
    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.type === 'number' ? parseInt(e.target.value) : e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  fnCleanData(formvalue) {
    // formvalue = fnCleanData(this.columnlist, formvalue);
    // this.setState({ formvalue: formvalue });
  }


  render() {
    const columns = this.fnDatatableCol();
    const { items, PageTitle, showModal, FormTitle, formvalue, ModalValueChanged, event } = this.state;
    console.log("renderitems", items, formvalue);
    if (items) {
      for (let i = 0; i < items.length; i++) {
        if (items[i].FieldType == 'radio') items[i].FieldType = 'single select';
        if (items[i].FieldType == 'checkbox') items[i].FieldType = 'multi-select';
      }
    }


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>

                    <Col md={2}>
                      <Button disabled={this.state.valid_sid} variant="primary" onClick={this.handleShow}>Add New Question</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={items}
                    extention={true}
                    export={false}
                    print={false}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmChatAgentConfigure">
                <Row>
                  {this.columnlist.map(col => {
                    if (col.name == 'FieldType') {
                      return <Form.Group as={Col} md={3} controlId={col.name} key={col.name}>
                        <Form.Label>{col.label}</Form.Label>
                        <Form.Control as="select" onChange={this.handleChange} defaultValue={formvalue.FieldType}
                          placeholder="Select" required>
                          <option key={0} value="" disabled selected>Select your option</option>
                          <option key={1} value="string">string</option>
                          <option key={2} value="textarea">textarea</option>
                          <option key={3} value="multi-select">multi-select</option>
                          <option key={4} value="single select">single select</option>
                          <option key={5} value="number">number</option>
                          <option key={6} value="dropdown">dropdown</option>
                          <option key={7} value="email">email</option>
                          <option key={8} value="empty">empty</option>
                          <option key={9} value="link">link</option>
                          <option key={10} value="embedded">embedded</option>
                        </Form.Control>
                      </Form.Group>
                    }
                    if (col.name == 'FieldOptions' && !this.state.editFieldOptions)
                      return false;

                    return col.name == 'SurveyID' || col.name == 'QuestionID' ? false :
                      fnRenderfrmControl(col, formvalue, this.handleChange, event)
                  })}
                  {event == "AddNested" &&
                    <Form.Group as={Col} md={3} controlId="ParentOption" key="ParentOption">
                      <Form.Label>ParentOption</Form.Label>
                      <Form.Control as="select" onChange={this.handleChange}
                        placeholder="Select" required>
                        <option key={0} value="" disabled selected>Select your option</option>
                        {this.state.ParentOption.split(',').map((option, index) => {
                          return <option key={index + 1} value={option}>{option}</option>
                        })}
                      </Form.Control>
                    </Form.Group>
                  }
                  {event == "AddNested" &&
                    <Form.Group as={Col} md={2} controlId="ParentQuestionID" key="ParentQuestionID">
                      <Form.Label>ParentQuestionID</Form.Label>
                      <Form.Control required
                        disabled
                        type="number" onChange={this.handleChange} value={formvalue["ParentQuestionID"]} />
                    </Form.Group>
                  }
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <input type="submit" value="Save Changes" className="btn btn-primary" onClick={this.handleSave} />
                </Then>
              </If>
            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    DeleteData
  }
)(AgentSurveyQuestions);