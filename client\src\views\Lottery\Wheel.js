import React, { Suspense, useEffect, useState } from "react";
import './Wheel.css';



const Wheel = (props) => {
    const { spin, sectors, onResult } = props;


    const [mustSpin, setMustSpin] = useState(false);
    const [showNumber, setShowNumber] = useState("");
    const [id, setId] = useState(Math.floor(Math.random() * 10000));



    useEffect(() => {
        console.log('id', id)
        console.log('sectors', sectors)

        const rand = (m, M) => Math.random() * (M - m) + m;
        const tot = sectors.length;
        const EL_spin = document.querySelector("#spin" + id);
        const ctx = document.querySelector("#wheel" + id).getContext('2d');
        const dia = ctx.canvas.width;
        const rad = dia / 2;
        const PI = Math.PI;
        const TAU = 2 * PI;
        const arc = TAU / sectors.length;

        const friction = 0.991; // 0.995=soft, 0.99=mid, 0.98=hard
        let angVel = 0; // Angular velocity
        let ang = 0; // Angle in radians

        const getIndex = () => Math.floor(tot - ang / TAU * tot) % tot;

        function drawSector(sector, i) {
            const ang = arc * i;
            ctx.save();
            // COLOR
            ctx.beginPath();
            ctx.fillStyle = sector.color;
            ctx.moveTo(rad, rad);
            ctx.arc(rad, rad, rad, ang, ang + arc);
            ctx.lineTo(rad, rad);
            ctx.fill();
            // TEXT
            ctx.translate(rad, rad);
            ctx.rotate(ang + arc / 2);
            ctx.textAlign = "right";
            ctx.fillStyle = "#fff";
            ctx.font = "bold 30px sans-serif";
            ctx.fillText(sector.label, rad - 10, 10);
            //
            ctx.restore();
        };

        function rotate(e) {
            const idx = getIndex();
            const sector = sectors[idx];
            ctx.canvas.style.transform = `rotate(${ang - PI / 2}rad)`;
            // if(!angVel){
            //     console.log(EL_spin);
            //     console.log(sectors);
            //     console.log(sector);
            //     console.log(idx);                
            // }

            //EL_spin.textContent = !angVel ? "SPIN" : sector.label;
            EL_spin.textContent = !angVel ? sector.label : sector.label;
            //EL_spin.textContent = sector.label;
            EL_spin.style.background = sector.color;

            // try {
            //     if (e != 1) {
            //         if (!angVel) {
            //             onResult({
            //                 id,
            //                 angVel,
            //                 label: sector.label
            //             })
            //         }
            //     }
            // }
            // catch (e) {

            // }
        }

        function frame() {
            if (!angVel) return;
            angVel *= friction; // Decrement velocity by friction
            if (angVel < 0.002) angVel = 0; // Bring to stop
            ang += angVel; // Update angle
            ang %= TAU; // Normalize angle
            rotate();
        }

        function engine() {
            frame();
            requestAnimationFrame(engine)
        }

        // INIT
        sectors.forEach(drawSector);
        rotate(1); // Initial rotation
        engine(); // Start engine
        EL_spin.addEventListener("click", () => {
            if (!angVel) angVel = rand(0.25, 0.35);
        });

    }, [sectors]);


    // useEffect(() => {
    //     if (spin) {
    //         const EL_spin = document.querySelector("#spin" + id);
    //         EL_spin.click();
    //     }
    // }, [spin]);


    return (
        <div className="wheelOfFortuneWapper">
            <img src="/lottery/tower.svg" className="towerImage" />
            <div id={"wheelOfFortune" + id} className="wheelOfFortune">

                <canvas id={"wheel" + id} width="200" height="200" className="wheel"></canvas>
                <div id={"spin" + id} className="spin">SPIN</div>
            </div>
        </div>
    );
}

export default Wheel;
