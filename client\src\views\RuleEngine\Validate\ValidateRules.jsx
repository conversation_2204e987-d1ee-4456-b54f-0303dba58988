import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Panel from '../Panel/Panel';
import InputField from '../Forms/InputField';
import SelectField from '../Forms/SelectedMenuField';
import Button from '../Button/Button';
import Table from '../Table/Table';
import Banner from '../Panel/Banner';
import * as Message from '../../../Constants/RuleConstants/Messages';
import { validateRuleset } from '../../../rule-validations/RuleValidation';
import Loader from '../Loader/Loader';
import { ViewOutcomes } from '../Attributes/ViewAttributes';
import axios  from 'axios';
import config  from '../../../config';

class ValidateRules extends Component {

    constructor(props) {
        super(props);
        const conditions = props.attributes.filter(attr => attr.type !== 'object' && ({
            name: attr.name,
            value: '',
            field: attr.field || '',
            values: attr.values || [],
            factValueType: attr.factValueType || '',
            actualname: attr.actualname
        }));
        

        this.state = { 
            attributes: [],
            conditions,
            message: Message.NO_VALIDATION_MSG,
            loading: false,
            outcomes: [],
            error: false,
        };
        this.handleAttribute = this.handleAttribute.bind(this);
        this.handleValue = this.handleValue.bind(this);
        this.handleAdd = this.handleAdd.bind(this);
        this.validateRules = this.validateRules.bind(this);
    }

    handleAttribute(e, index) {
        const attribute = { ...this.state.conditions[index], name: e.target.value };
        const conditions = [ ...this.state.conditions.slice(0, index), attribute, ...this.state.conditions.slice(index + 1)];
        this.setState({ conditions });
    }

    handleValue(e, index) {
        let ActualName = this.state.conditions[index].actualname;
        const attribute = { ...this.state.conditions[index], name: ActualName, value: e.target.value };
        const conditions = [ ...this.state.conditions.slice(0, index), attribute, ...this.state.conditions.slice(index + 1)];
        this.setState({ conditions });
    }

    handleAdd() {
        this.setState({ conditions: this.state.conditions.concat([{name: ''}])});
    }

    validateRules(e) {
        e.preventDefault();
        let facts = {};
        const { decisions, attributes } = this.props;
        this.setState({loading: true});
        this.state.conditions.forEach(condition => {
           const attrProps = attributes.find(attr =>  attr.actualname == condition.actualname );
           if (attrProps.type === 'number') {
            facts[condition.name] = Number(condition.value);
           } else if (condition.value && condition.value.indexOf(',') > -1) {
            facts[condition.name] = condition.value.split(',');
           } else {
            facts[condition.name] = condition.value;
           }
        });
        
        let headers = {'Content-Type': 'application/json'};
        let url = config.api.base_url + '/ruleengine/evaluateRule';
        let body = { facts, decisions };    

        axios.post(url, body, headers).then(response => {

           let outcomes = {};
           if(response !== null && response.data !== undefined &&
                response.data!== null && response.data.data !== undefined){
                    outcomes = response.data.data;
                }

            if( outcomes !== undefined || outcomes !== null) {
                this.setState({loading: false, outcomes,  result: true, error: false, errorMessage: '',});

            }
        }).catch((e) => {
            this.setState({loading: false, error: true, errorMessage: e.error, result: true, });
        });
    }

    attributeItems = () => {
        const { conditions, loading, outcomes, result, error, errorMessage } = this.state;
        const { attributes } = this.props;
        const options = attributes.map(att => att.name);

        const formElements = conditions.map((condition, index) =>
            (
            <tr key={condition.actualname + index || 'item'+index}>
                <td>
                    <InputField
                        // options={options}
                        onChange={(e) => this.handleAttribute(e, index)}
                        value={condition.name} readOnly
                    />
                </td>
                <td colSpan='2'>
                    {condition.field === 'input' && 
                        <InputField 
                            onChange={e => this.handleValue(e, index)}
                            value={condition.value}
                        />}
                    {condition.field === 'dropdown' && condition.factValueType !== 'key' &&
                         <SelectField 
                            options={condition.values}
                            onChange={(e) => this.handleValue(e, index) }
                            value={condition.value}
                        />}
                        {condition.field === 'dropdown' && condition.factValueType === 'key' &&
                         <SelectField 
                            options={condition.values}
                            onChange={(e) => this.handleValue(e, index) }
                            value={condition.value}
                            dropDownKeyValue={true}
                        />}
                    {condition.field === 'date' &&
                        <div className='form-field'>
                            <input
                                type="date"
                                value={condition.value}                            
                                label="Value"
                                onChange={(e) => this.handleValue(e, index)}
                            >
                            </input>
                        </div>  
                    }
                </td>
            </tr>)
        );

        let message;
        if (result) {
            if (error) {
                message = <div className="form-error">Problem occured when processing the rules. Reason is {errorMessage}</div>
            } else if (outcomes === null ) {
                message = <div>No results found</div>
            } else if (outcomes && outcomes.events !== undefined &&

                outcomes.events.length > 0) {
                message = (<div className="view-params-container">
                                <h5>Result: {outcomes.result}</h5>

                                <ViewOutcomes  items={outcomes} />
                            </div>)
            } else {
                message = undefined;
            }
        }
        return (
        <React.Fragment>
            <Table columns={['Name', 'Value']}>
                     {formElements}
            </Table>
            <div className="btn-group">
               <Button label={'Validate Ruleset'} onConfirm={this.validateRules} classname="primary-btn" type="submit" />
           </div>
            <hr/>
                { loading && <Loader /> }
                { !loading && message }
        </React.Fragment>)
    }

    render() {
        return (<React.Fragment>
        {this.props.decisions.length < 1 && <Banner message={this.state.message}/> }
        {this.props.decisions.length > 0 &&
        <Panel>
            <form>
                <div>
                    {this.attributeItems()}
                </div>
            </form>
        </Panel>}
        </React.Fragment>);
    }
}

ValidateRules.defaultProps = ({
    attributes: [],
    decisions: [],
});

ValidateRules.propTypes = ({
    attributes: PropTypes.array,
    decisions: PropTypes.array,
});

export default ValidateRules;