import { Button, Modal, <PERSON> } from 'react-bootstrap';

const VerificationComment = (props) => {
  const { current } = props;

  return (
    <>
      <Modal
        {...props}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        onHide={props.onVerificationModalCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            View Verification Comment
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {current && current.Remarks || "NA"}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={props.onVerificationModalCancel}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default VerificationComment;