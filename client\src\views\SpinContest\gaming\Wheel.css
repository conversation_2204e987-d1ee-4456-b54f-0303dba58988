@import url('https://fonts.googleapis.com/css2?family=Big+Shoulders+Display:wght@700&family=Poppins:wght@400;500;600;700&display=swap');

body {
  background-color: #f6c861;
}

.wheelOfFortuneWapper {
  width: 100%;
  text-align: center;
  margin-top: 39px;
  display: flex;
  justify-content: center;

}

.bhdLno {
  max-width: 325px !important;
  max-height: 325px !important;
}



.SpinWheelBtn {
  background: #4F200D 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 12px #7C7C7C29;
  border-radius: 4px;
  opacity: 1;
  font: normal normal bold 16px/28px Roboto;
  letter-spacing: 0px;
  color: #FFFFFF;
  padding: 4px 80px;
  margin-top: 100px;
}

.wheelOfFortune {
  display: inline-block;
  position: relative;
  overflow: hidden;
  /* border-radius: 50%;
  box-shadow: 0 0px 100px 60px #643867; */
}

.wheel {
  display: block;
  width: 360px;
  height: 360px;

}

.ContestCard {
  background: #9B6BBF 0% 0% no-repeat padding-box;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  margin: 0px 0px;
  padding: 10px 20px;
  justify-content: space-between;


}


.ContestCard h2 {
  text-align: left;
  font: normal normal 700 26px/28px Poppins;
  letter-spacing: 0px;
  color: #210062;
  opacity: 1;
  padding-top: 10px;
}

.ContestCard h3 strong {
  font: normal normal bold 26px/28px Poppins;
}

.ContestCard h3 {
  text-align: left;
  font: normal normal bold 20px/28px Poppins;
  letter-spacing: 0px;
  color: #FFFFFF;
  opacity: 1;
  margin-top: 19px;
  margin-bottom: 0px;
}

.ContestCard h4 strong {
  font: normal normal bold 29px/28px Poppins;
  letter-spacing: 0px;
  color: #FFD100;
  position: relative;
  top: 4px;
}

.ContestCard h4 {
  text-align: left;
  font: normal normal 600 20px/28px Poppins;
  letter-spacing: 0px;
  color: #F8D347;
  opacity: 1;
}

.spin {
  font: 1em/1em sans-serif;
  user-select: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 45%;
  left: 45%;
  width: 25%;
  height: 25%;
  margin: -7%;
  background: #ffc668;
  color: #694623;
  box-shadow: 0 0 0 8px #ffc668, 0 0px 15px 5px rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  transition: 0.8s;
}

.spin::after {
  content: "";
  position: absolute;
  top: -17px;
  border: 10px solid transparent;
  border-bottom-color: #ffc668;
  border-top: none;
}

.AttemptsMsg {
  position: absolute;
  left: 0px;
  right: 0px;
  bottom: 0px;
  top: 0px;
  height: 52px;
  margin: auto;
  text-align: center;
}

@media screen and (max-width:767px) {
  .wheel {
    width: 100%;
    height: auto;
  }

  .spin {
    font-size: 1em;
    line-height: 18px;
  }

  .wheelOfFortune {
    width: 88%;
  }

  .wheelOfFortuneWapper {
    margin-top: 43px;
  }

}