import React from "react";
import {
    GetCommonData, GetCommonspData, GetFileExists, PostIncentiveFormData, PostAgentChatFileData, UpdateAgentChatParams
} from "../store/actions/CommonAction";
import {
    GetMySqlData, GetDataDirect
} from "../store/actions/CommonMysqlAction";
import { connect } from "react-redux";
import { getuser, fnDatatableCol, joinObject } from '../utility/utility.jsx';
import DropDownListMysql from './Common/DropDownListMysql';
import {MultiSelect} from "react-multi-select-component";
import DropDown from './Common/DropDownList';
import RealTimePanel from './RealTimePanel/RealTimePanel';
import RealTimePanelQueuewise from './RealTimePanel/RealTimePanelQueuewiseTwo';
import { If, Then, Else } from 'react-if';
import AlertBox from './Common/AlertBox';
import DataTable from './Common/DataTableWithFilter';
import Date from "./Common/Date"
import moment from 'moment';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Link } from 'react-router-dom'


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
} from "reactstrap";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';
import { func, string } from "prop-types";

class UploadAgentChatData extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",
            selectedIvrProduct: "",
            ivrType: "0",
            ivrProducts: [],
            ivrQueues: [],
            SelectedQueues: [],
            addClass: '',
            UserId: '',
            RealTimeUrl: '',
            selectedValue: [{ label: "Select IVR Queues", value: "0" }],
            selectedFile: null,
            uploadFile: '',
            ManagerId: '',
            DownloadedFile: '/SampleExcelfiles/AgentChatUploadData.xlsx',
            ResponseData:'',
            UploadStatusColumns: [],
            StartDate: moment().format("YYYY-MM-01"),
        };

        this.UploadFileList = {
            config:
            {
                data: [{ Id: 1, Display: "First Premium Paid only" }, { Id: 2, Display: "Free Look Cancellation" }, { Id: 3, Display: "Quality Score" }
                    , { Id: 4, Display: "Warning Letters" }, { Id: 5, Display: "Arrears and Claw backs" }],
            }
        };

        this.dateRangeRef = React.createRef();

    }

    componentDidMount() {
        this.UserList();
    }

    fnDatatableCol(columnlist) {

        var columns = fnDatatableCol(columnlist);
        return columns;
    }

    UserList() {debugger;
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ManagerId : managerid});
    }

    // On file select (from the pop up) 
    onFileChange(event) { debugger;
        console.log(event.target.files[0]);
        // Update the state 
        this.setState({ selectedFile: event.target.files[0] }); 
       
    }; 

    // On file upload (click the upload button) 
    onFileUpload(e)
    {   
        e.preventDefault();
         debugger;
        // if (this.state.uploadFile == '') {
        // toast("Please select Upload File Type", { type: 'error' });
        // return;
        // }
        if (this.state.selectedFile == null) {
        toast("Please choose Excel File", { type: 'error' });
        return;
        }
        // Create an object of formData 
        const formData = new FormData(); 
        console.log(formData);
        // Update the formData object 
        formData.append( 
          "myFile", 
          this.state.selectedFile, 
          this.state.selectedFile.name 
        ); 
        // formData.append('TypeId', this.state.uploadFile); 
        // formData.append('UserId', this.state.ManagerId); 
        // formData.append('ValidFrom', moment(this.state.StartDate).format("YYYY-MM-01"));    
        
       
        // Details of the uploaded file 
        console.log(this.state.selectedFile);      
        // Request made to the backend api 
        // Send formData object 
        PostAgentChatFileData(formData, function (results) {
            console.log(results);
            this.setState({ ResponseData: results.data.data }, () =>
            this.AgentChatAPICall());
        //     if(results.data.status == 200){
        //     toast(results.data.data, { type: 'success' });
        //     }else{
        //    // toast(results.data.data, { type: 'error' });    
        //     }
        }.bind(this));
    }

    AgentChatAPICall(){
        let requestdata = this.state.ResponseData;
        requestdata.forEach(element => {
            console.log(element.EmployeeID);
            console.log(element.ChatLimit);
            console.log(element.DailyLimit);
            var json = {
                "employeeId" : element.EmployeeID,
               "departmentname":"Health",
               "chatlimit":element.ChatLimit,
               "dailylimit":element.DailyLimit
               };
               UpdateAgentChatParams(json, function (results) {
    
                this.setState({ UpdateAgentList: results.data.data})
    
            }.bind(this));
        });
        
    }

    fileData() 
    {      debugger;
        if (this.state.selectedFile) { 
            
          return ( 
            <div> 
              {/* <span>File Details:</span>  */}
              <span>File Name: {this.state.selectedFile.name}</span> 
              <p>File Type: {this.state.selectedFile.type}</p> 
              {/* <p> 
                Last Modified:{" "} 
                {this.state.selectedFile.lastModifiedDate.toDateString()} 
              </p>  */}
            </div> 
          ); 
        } else { 
          return ( 
            <div> 
              <br /> 
              <h4>Choose before Pressing the Upload button</h4> 
            </div> 
          ); 
        } 
    } 

    uploadfilechange(e, props){debugger;
        this.setState({
            uploadFile: e.target.value,
            DownloadedFile: '/SampleExcelfiles/FirstPremPaid.xlsx'
         });
       
    }

    renderDownloadFile(){
        if (this.state.DownloadedFile) {
            return  <Link to={this.state.DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    renderUploadStatus() {debugger;

        if (this.state.ResponseData && this.state.ResponseData.length > 0) {
            let UploadStatusColumns = [];
            let ResponseData = this.state.ResponseData;
      
            Object.entries(ResponseData[0]).map(([key,value])=>{
                    if(key == 'Status'){                       
                    UploadStatusColumns.push({ label: "Status",
                                name: "Status",
                                cell: row => <div className="Status">{row.Status ? row.Status : "N.A"}</div>,
                    })       
                    }else if(key == 'FirstPremPaid' || key == 'FLC'){
                        UploadStatusColumns.push({ label: key.toString(),
                                name: key.toString(),
                                type: "bool",
                    })  
                    }else{
                    UploadStatusColumns.push({
                    "name": key.toString(),
                    "label": key.toString(),
                     searchable: true,                    
                    })
                    }
              })

            const columns = this.fnDatatableCol(UploadStatusColumns);

            return <Row>
                <Col md="12">
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col md={12}>
                                    <CardTitle tag="h6">Upload Data Status</CardTitle>
                                </Col>
                            </Row>
                        </CardHeader>
                        <CardBody>
                            <DataTable columns={columns} data={this.state.ResponseData} />
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        }

        return null;

    }

    renderMonthField(){
        let type = this.state.uploadFile
        if(type == 3 || type == 4 || type ==5){
            return  <Col md={2}><Date onStartDate={this.handleStartDate.bind(this)}> </Date></Col>
        }
    }

    handleStartDate = (StartDateValue) => {debugger;
        this.setState({ StartDate: StartDateValue });
    }

    render() {
        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>                                    
                                    <Row>
                                    {/* <Col md={3}>
                                            <Form.Group controlId="upload_file_dropdown">
                                                <DropDown firstoption="Select Upload File" value={this.state.uploadFile} col={this.UploadFileList} onChange={this.uploadfilechange.bind(this)}>
                                                </DropDown>
                                            </Form.Group>
                                    </Col>                                  */}
                                    {/* {this.renderMonthField()}    */}
                                    <form ref="form" onSubmit={this.onFileUpload.bind(this)}>
                                            <input type="file" onChange={this.onFileChange.bind(this)} /> 
                                            <button type="submit" className="btn btn-primary">Upload!</button>
                                    </form>

                                    {/* <Col md={3}>
                                            <Form.Group controlId="Incentivefile_upload" >
                                                <Form.Label><i>*</i> File Upload </Form.Label>
                                                <input type="file" onChange={this.onFileChange.bind(this)} /> 
                                            </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                    <Button onClick={this.onFileUpload.bind(this)}> Upload! </Button> 
                                    </Col> */}
                                 
                                    </Row>

                                </CardHeader>
                                <CardBody>
                                {this.renderDownloadFile()}   
                                {this.fileData()}
                                {this.renderUploadStatus()}
                                </CardBody>
                            </Card>
                        </Col>
                    </Row>
                 </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetMySqlData,
    }
)(UploadAgentChatData);