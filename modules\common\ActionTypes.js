const GET_BMS_URL = 'GET_BMS_URL';
const AUTO_PAY_STATUS = 'AUTO_PAY_STATUS';
const BMS_VERIFICATION_STATUS = 'BMS_VERIFICATION_STATUS';
const ADD_CALL_TO_QUEUE = 'ADD_CALL_TO_QUEUE';
const DOC_POINT_BMS_URL = 'DOC_POINT_BMS_URL';
const MY_BOOKINGS_BMS_API = 'MY_BOOKINGS_BMS_API';
const MY_BOOKINGS_BMS_API2 = 'MY_BOOKINGS_BMS_API2';
const PENDING_PF_LEAD = 'PENDING_PF_LEAD';
const PENDING_DOC_LEAD = 'PENDING_DOC_LEAD';
const PENDING_DOCUMENT_LEADLIST = 'PENDING_DOCUMENT_LEADLIST';
const GET_CJ_URL = 'GET_CJ_URL';
const GET_VERIFICATION_CAMPAIGN = 'GET_VERIFICATION_CAMPAIGN';
const GET_CONTINUE_JOURNEY_URL = 'GET_CONTINUE_JOURNEY_URL';
const ADD_TO_VERIFICATION_QUEUE = 'ADD_TO_VERIFICATION_QUEUE';
const OPT_FOR_WA='OPT_FOR_WA';
const AUTODEBIT_PROMPT = 'AUTODEBIT_PROMPT'

module.exports ={
  GET_BMS_URL: GET_BMS_URL,
  AUTO_PAY_STATUS: AUTO_PAY_STATUS,
  BMS_VERIFICATION_STATUS: BMS_VERIFICATION_STATUS,
  ADD_CALL_TO_QUEUE: ADD_CALL_TO_QUEUE,
  DOC_POINT_BMS_URL: DOC_POINT_BMS_URL,
  MY_BOOKINGS_BMS_API: MY_BOOKINGS_BMS_API,
  MY_BOOKINGS_BMS_API2: MY_BOOKINGS_BMS_API2,
  PENDING_PF_LEAD : PENDING_PF_LEAD,
  PENDING_DOC_LEAD : PENDING_DOC_LEAD,
  PENDING_DOCUMENT_LEADLIST : PENDING_DOCUMENT_LEADLIST,
  GET_CJ_URL : GET_CJ_URL,
  GET_VERIFICATION_CAMPAIGN : GET_VERIFICATION_CAMPAIGN,
  GET_CONTINUE_JOURNEY_URL: GET_CONTINUE_JOURNEY_URL,
  ADD_TO_VERIFICATION_QUEUE: ADD_TO_VERIFICATION_QUEUE,
  OPT_FOR_WA: OPT_FOR_WA,
  AUTODEBIT_PROMPT: AUTODEBIT_PROMPT
}