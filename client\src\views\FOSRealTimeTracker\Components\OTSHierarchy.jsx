import React, { useState, useContext, useEffect } from "react";
import CheckboxTree from "react-checkbox-tree";
import "react-checkbox-tree/lib/react-checkbox-tree.css";

import ExpandMoreTwoToneIcon from "@mui/icons-material/ExpandMoreTwoTone";
import ChevronRightTwoToneIcon from "@mui/icons-material/ChevronRightTwoTone";
import AddTwoToneIcon from "@mui/icons-material/AddTwoTone";
import RemoveTwoToneIcon from "@mui/icons-material/RemoveTwoTone";
import FolderTwoToneIcon from "@mui/icons-material/FolderTwoTone";
import FolderOpenTwoToneIcon from "@mui/icons-material/FolderOpenTwoTone";
import DescriptionTwoToneIcon from "@mui/icons-material/DescriptionTwoTone";

import { GetDataDirect } from '../../../store/actions/CommonAction';
import { getUserDetails } from "utility/utility";
import { FosContext } from "../FosContext";
import { Box, Chip, Typography } from "@mui/material";


const OTSHierarchy = () => {

    const { otsAvgVal } = useContext(FosContext);

    const [expanded, setExpanded] = useState([]);

    const [nodes, setNodes] = useState([]);

    // console.log("The ots avg val ", otsAvgVal);

    const searchChildNodes = (node) => {

        for (let i = 0; i < node.length; i++) {

            if (otsAvgVal[parseInt(node[i].value)]) {
                let name = node[i].label;
                node[i].label = <Box gap={1} className="HierachyTree">

                    <Typography variant="body1">
                        {name}
                    </Typography>
                    <Chip
                        label={Number(parseInt(otsAvgVal[parseInt(node[i].value)]?.val)) || '-'  }
                        className="Chip"
                    />
                </Box>;

            }
            else {
                let name = node[i].label;
                node[i].label = <Box gap={1} className="HierachyTree">

                    <Typography variant="body1">
                        {name}
                    </Typography>
                    
                </Box>;
            }

            if (node[i].hasOwnProperty('children')) {
                searchChildNodes(node[i].children);
            }

        }
        return;
    }


    useEffect(() => {

        let UserId = parseInt(getUserDetails('UserId'));

        GetDataDirect({
            root: "Hierarchy2",
            ManagerId: UserId,
            statename: "Hierarchy-" + UserId,
            value: /UserID/g,
            state: true
        }, function (result) {
            let str = JSON.stringify(result);
            var res = str.replace(/UserName/g, "label");
            res = res.replace(/UserID/g, "value");

            let nodesData = JSON.parse(res);

            for (let i = 0; i < nodesData.length; i++) {

                // <Box gap={1} className="HierachyTree">

                //     <Typography variant="body1">
                //         {name}
                //     </Typography>
                // </Box>;
                if (otsAvgVal[parseInt(nodesData[i].value)]) {
                    let name = nodesData[i].label;
                    nodesData[i].label =
                        <Box gap={1} className="HierachyTree">

                            <Typography variant="body1">
                                {name} 
                            </Typography>

                            <Chip
                                label={Number(parseInt(otsAvgVal[parseInt(nodesData[i].value)]?.val)) || '-' }
                                className="Chip"
                            />
                        </Box>
                        ;


                }
                else {
                    let name = nodesData[i].label;
                    nodesData[i].label =
                        <Box gap={1} className="HierachyTree">
                            <Typography variant="body1">
                                {name}
                            </Typography>
                        </Box>
                        ;
                }
                if (nodesData[i].hasOwnProperty("children")) {
                    searchChildNodes(nodesData[i].children);
                }
            }

            // console.log("The nodes data is ", nodesData);



            setNodes(nodesData);
        })

    }, [])





    return (
        <>
            {
                nodes.length > 0 ?
                    <div className="scrollBar">
                        <CheckboxTree
                            icons={{
                                expandClose: <ChevronRightTwoToneIcon className="downArrow" />,
                                expandOpen: <ExpandMoreTwoToneIcon className="downArrow" />,
                                expandAll: <AddTwoToneIcon />,
                                collapseAll: <RemoveTwoToneIcon />,
                                parentClose: <FolderTwoToneIcon />,
                                parentOpen: <FolderOpenTwoToneIcon />,
                                leaf: <DescriptionTwoToneIcon />,
                                check: null,
                                uncheck: null,

                            }}
                            nodes={nodes}
                            expanded={expanded}
                            onExpand={(newExpanded) => setExpanded(newExpanded)}
                            showNodeIcon={false}
                        />
                    </div>
                    
                    :
                    null
            }
        </>
    );
};

export default OTSHierarchy;