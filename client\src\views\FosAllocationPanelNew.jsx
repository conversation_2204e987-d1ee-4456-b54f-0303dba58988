import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetFosUserDetails, GetCommonspData, AllocationFormData,
    GetCommonspDataV2
} from "../store/actions/CommonAction";
import {
    addRecord
} from "../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import Moment from 'react-moment';
import DataTable from './Common/DataTableWithFilter';
import { OpenSalesView, OpenNewSalesView, GetJsonToArray, getUrlParameter, getuser } from '../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import { If, Then } from 'react-if';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import DropDownAgent from './Common/DropDownAgent';
import DropDownGroup from './Common/DropDownGroup';
import DropDown from './Common/DropDown';
import ModalFosAllocationPanel from './Common/ModalFosAllocationPanel';
import { Link } from 'react-router-dom'
import DateRange from "./Common/DateRange"
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'
import moment from 'moment';


const FosAllocationPanelNew = (props) => {
    
    
    const PageTitle = "FOS Assignment Panel"
    
    //const [StartDate,setStartDate] = useState('moment().format("YYYY-MM-DD")')
    //let [item, setitem] = useState([]);
    const [Product, setProduct] = React.useState();
    const [startDate, setstartDate] = useState(moment().subtract(7, 'days').format("YYYY-MM-DD"));
    const [selectedFile,setselectedFile] = useState(null)
    const [addClass,setaddClass] = useState('btn btn-primary')
    const DownloadedFile ='/SampleExcelfiles/FosLeadAllocation.xlsx'
    const [items,setitems] = useState()
    const [unassignleads,setunassignleads] = useState()
    const [endDate,setendDate] = useState(moment().format("YYYY-MM-DD 23:59:59"));
    // Add near other state declarations
    const [productItems, setProductItems] = useState([]);
    const [processItems, setProcessItems] = useState([]);
    const [SelectedProcess, setSelectedProcess] = React.useState();
    const [processList, setProcessList] = useState([]);
    const [productsList, setProductsList] = useState([]);
    const [processLoading, setProcessLoading] = useState(false);
    const [AssignedLeadStatus, setAssignedLeadStatus] = useState([]);
    const [ProductLoading, setProductLoading] = useState(false);
    
    let dateRangeRef = React.createRef();

    const user = getuser();
    const columnlist = [
        {
            name: 'LeadID',
            selector: 'LeadID',
            type: "string",
            width: '100px',
            cell: row => <div>
                <a href={OpenNewSalesView(row.CustomerId, row.LeadID, row.productID)} target="_blank">{row.LeadID} </a>

            </div>,
            editable: false,
            sortable: true,
        },
        {
            name: 'Customer Name',
            selector: 'Name',
            width: '150px',
            type: "string",
            editable: false,
            sortable: true,
        },
        {
            name: 'Select City',
            selector: 'City',
            width: '100px',
            type: "string",
            editable: false,
            sortable: true,
            searchable: true
        },
        {
            name: 'Pincode',
            selector: 'Pincode',
            type: "string",
            width: '100px',
            searchable: true
        },

        {
            name: 'AssignmentType',
            selector: 'AssignmentType',
            type: "string",
            width: '120px',
            searchable: true
        },
        {
            name: 'AppointmentType',
            selector: 'AppointmentType',
            type: "string",
            width: '150px',
            searchable: true
        },
        {
            name: 'Appointment Date Time',
            selector: 'AppointmentDateTime',
            type: "datetime",
            cell: row => <div>{row.AppointmentDateTime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.AppointmentDateTime}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            format:'YYYY-MM-DD HH:mm:ss',
            searchable: true
        },
        {
            name: 'Created On',
            selector: 'Createdon',
            type: "datetime",
            cell: row => <div>{row.Createdon ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.Createdon}</Moment> : "N.A"}</div>,
            sortable: true,
            width: '150px',
            format:'YYYY-MM-DD HH:mm:ss',
            searchable: true
        },

        {
            name: 'Product Name',
            selector: 'ProductName',
            type: "string",
            sortable: false,
            width: '100px',
        },

        {
            name: 'AssignedTo',
            selector: 'EmployeeId',
            cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: 'Call Center Agent',
            selector: 'Call Center Agent',
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: '1st Reporting',
            selector: '1st Reporting',
            //cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        {
            name: '2nd Reporting',
            selector: '2nd Reporting',
            //cell: row => <div>{row.EmployeeId ? row.EmployeeId + "/" + row.UserName : ""}</div>,
            type: "string",
            sortable: false,
            width: '150px',
            searchable:true,
        },
        
    ];

    const AssignedLeadColumns = [
        {
            name: 'LeadID',                 
            selector: 'LeadID',
            type: "string",
            width: '100px',          
        },
        {
            name: 'AssignTo',          
            selector: 'AssignTo',
            width: '100px',
        },
        {
            name: 'Status',          
            selector: 'Status',
            width: '100px',
            cell: row => <div>{row.Status === 1 ? "SUCCESS" : "FAIL"}</div>,
        },
        {
            name: 'Reason',          
            selector: 'Reason',
            width: '350px',
            cell: row => <div style={{ color: row.Status === 1 ? 'green' : 'red' }}>{row.Reason ? row.Reason : "N.A"}</div>,
        }
    ]

    useEffect(() => {
    GetProductList();
    fetchProcessDetails();
    }, []);

    const GetProductList = () => {
        setProductLoading(true);
      
        // First API: fetch all available products
        props.GetCommonspData(
          {
            root: "Fos_FosAssignmentProducts"
          },
          function (data) {
            setProductLoading(false);
      
            if (data && data.data && data.data.data) {
              const itemsData = data.data.data[0]; // array of { Id, Display }
              setitems(itemsData);
      
              // ✅ Check RoleID before filtering
             // Get user info
                if (user.RoleId === 2) {
                // Admin - allow all items
                setProductsList(itemsData.map(p => p.Id));
                setProductItems(
                  itemsData.map(item => ({
                    Id: item.Id,
                    Display: item.Display
                  }))
                );
              } else {
                // Non-admin - fetch allowed product list
                setProductLoading(true);
                props.GetCommonspDataV2(
                  {
                    root: "GetUserProductList"
                  },
                  function (result) {
                    setProductLoading(false);
      
                    if (
                      result && result.data && result.data.data &&
                      result.data.data.length > 0
                    ) {
                      const productList = result.data.data[0]; // array of { ProductId, ProductDisplayName }
      
                      const allowedIds = productList.map(p => p.ProductId);
      
                      const filteredItems = itemsData.filter(item =>
                        allowedIds.includes(item.Id)
                      );
      
                      setProductsList((filteredItems && filteredItems.map(p => p.Id)) || []);
                      setProductItems(
                        filteredItems.map(item => ({
                          Id: item.Id,
                          Display: item.Display
                        }))
                      );
                    }
                  }.bind(this)
                );
              }
            }
          }
        );
      };
      
      

    useEffect(() => {
     
        if (!props.CommonData.isError) {
           GetData();
        }

    }, [Product, startDate, endDate, SelectedProcess])

    const fetchProcessDetails = () => {
        // If user is admin show all processes
           if (user.RoleId === 2) {
               const allProcesses = [
                   { Id: 6, Display: "Dedicated FOS" },
                   { Id: 7, Display: "Store FOS" },
                   { Id: 8, Display: "Self FOS" }
               ];
               setProcessItems(allProcesses);
               setProcessList([6, 7, 8]);
               return;
           }
        setProcessLoading(true); // Start loading
        GetFosUserDetails((response, error) => {
            setProcessLoading(false); // Stop loading after API call finishes
            if (error) {
                setProcessItems([]);
                console.error("Error fetching user details:", error);
                return;
            }
            if (response && response.data) {
                setProcessList(response.data.ProcessList || []);                 
                const processData = response?.data?.ProcessList;

                if (Array.isArray(processData) && processData.length > 0) {
                    const processList = processData
                    .filter(process => [6, 7, 8].includes(process))
                   .map(process => ({
                        Id: process,
                        Display: process === 6 ? "Dedicated FOS" :
                                 process === 7 ? "Store FOS" :
                                 "Self FOS" // since only 6, 7, 8 allowed
                    }));
                    setProcessItems(processList);
                } else {
                    // Handle empty or undefined process list
                    setProcessItems([]); // Or show a default message
                }
            }
        });
    };


    const GetData = () => {
      
        if(Product) {
            props.GetCommonspData({
                root:'GetFosAllocationPanelNew',
                c: "R",
                params: [{ ProductId: Product }, {ProcessID : SelectedProcess}, {AppointmentDateTime: startDate},{AppointmentEndDate: endDate}],
                //params: [{ ProductId: Product }, {ProcessID : SelectedProcess}],
            }, function (data) {
                if (data && data.data && data.data.data) {
                    let item = data.data.data[0]
                    setunassignleads(item)
                }
            })
        }

    }

    
    const onFileChange = (event) => {
        // Update the state 
        setselectedFile(event.target.files[0])
    };

    const onFileUpload = (e) => {
      
        e.preventDefault();
        setaddClass('btn btn-primary fa fa-spinner');

        if (selectedFile == null) {
            toast("Please choose Excel File", { type: 'error' });
            setaddClass('btn btn-primary');
            return;
        }
        // Create an object of formData
        const User = getuser(); 
        var type =  getType();
        const formData = new FormData(); 
        // Update the formData object 
        formData.append( 
            "myFile",
            selectedFile,
            selectedFile.name,
        ); 
        formData.append('type', type);
         
        // formData.append('UserId', 101714); 

        formData.append('ProductList', productsList);
        formData.append('ProcessList', processList);
        formData.append('UserId', User.UserID); 

        
        // Details of the uploaded file 
        // Request made to the backend api 
        // Send formData object 
        AllocationFormData(formData, function (results) {
            
            document.getElementById('files-upload').value = null;
            if (results.data.status == 200 && results?.data?.data) {
                const responseItems = Object.values(results.data.data); // [obj0, obj1, obj2, ...]
                const AssignedLeadStatus = responseItems.flatMap(item => item.recordsets?.[0] || []);           
                setAssignedLeadStatus(AssignedLeadStatus);
                alert('File uploaded');
                setaddClass('btn btn-primary');
                setselectedFile(null);
                GetData() 
            } else {
                setselectedFile(null);
                toast(results.data.message, { type: 'error' });
                return;
            }
        });
    }


    const getType =() => {
        var loc = window.location.href;
        let lastUrl = loc.substring(loc.lastIndexOf("/") + 1, loc.length);
        
        lastUrl = lastUrl.split('?');
        //console.log('lasturl',lastUrl[0]);
        return lastUrl[0];    
    }

    const renderDownloadFile = () => {
        if (DownloadedFile) {
            return  <Link style={{ fontSize: '14px' }} to={DownloadedFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }
    
    const handleStartDate = (StartDateValue) => {
        setstartDate(StartDateValue);
        setendDate(moment(StartDateValue).add(8, 'days').format("YYYY-MM-DD"))
    }
    
    const handleEndDate = (EndDateValue) => {
        setendDate (EndDateValue);
    }

    return (
        <>

            <div className="content fosAgent" >
                <ToastContainer />

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                            <Row>
                                <Col md={3}>
                                    <Form.Label><i>*</i> Product List</Form.Label>
                                    {ProductLoading ? (
                                        <div>
                                            <i className="fa fa-spinner" style={{ marginTop: '5px', display: 'block' }}></i>
                                        </div>
                                    ) : productItems && productItems.length > 0 ? <DropDown 
                                        firstoption="Select Product" 
                                        items={productItems} 
                                        onChange={(e) => {
                                            const selectedProductId = e.target.value;
                                            setProduct(selectedProductId);
                                            if (selectedProductId === "2") {
                                                setSelectedProcess(); // Clear process items
                                            }
                                        }}
                                    />: (
                                        <div style={{ fontSize: '0.9em', color: '#999' }}>No product available</div>
                                    )}
                                </Col>
                                {Product !== "2" && (
                                <Col md={3}>
                                    <Form.Label><i>*</i> Process List</Form.Label>
                                    {processLoading ? (
                                        <div>
                                            <i className="fa fa-spinner" style={{ marginTop: '5px', display: 'block' }}></i>
                                        </div>
                                    ) : processItems && processItems.length > 0 ? (
                                        <DropDown 
                                            firstoption="Select Process" 
                                            items={processItems} 
                                            onChange={(e) => setSelectedProcess(e.target.value)}
                                        />
                                    ) : (
                                        <div style={{ fontSize: '0.9em', color: '#999' }}>No process available</div>
                                    )}
                                </Col>
                                )}
                                <Row>
                                
                                <DateRange days={7} FromDate= {"Appointment From"} ToDate= {"Appointment To"}
                                startDate={startDate} endDate={endDate} ref={dateRangeRef} onStartDate={handleStartDate} onEndDate={handleEndDate}>
                                </DateRange>
                  
                                </Row>
                                
                            </Row>
                                <Row>
                                    <Col md = {10}>
                                        <form onSubmit={onFileUpload}>
                                            
                                                <label for="files-upload">Upload Excel To Assign Leads</label>&ensp;
                                                <input type="file" id="files-upload" onChange={onFileChange} />                                         
                                                <button type="submit" id="uploadbutton"  className={addClass}>Upload!</button>                                                             
                                        </form>
                                    </Col>                                                        
                                </Row>
                               {renderDownloadFile()} 
                               {(AssignedLeadStatus && AssignedLeadStatus.length > 0) && console.log('AssignedLeadStatus', AssignedLeadStatus)}

                                {(AssignedLeadStatus && AssignedLeadStatus.length > 0) && <div className="TableWidthShort"><DataTable 
                                    columns={AssignedLeadColumns}
                                    data = {AssignedLeadStatus}
                                    printexcel={true}
                                /></div>}
                                {(unassignleads && unassignleads.length > 0) && <DataTable
                                    columns={columnlist}
                                    data = {unassignleads}
                                    printexcel={true}
                                />}

                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );


}



function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonspDataV2
    }
)(FosAllocationPanelNew);