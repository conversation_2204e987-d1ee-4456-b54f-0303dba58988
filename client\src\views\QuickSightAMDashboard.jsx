
import React from "react";
import { <PERSON><PERSON> } from 'react-bootstrap';

import { GetCommonData, GetDashboardUrl, GetCommonspData, resolveRedirectURL } from "../store/actions/CommonAction";


import { connect } from "react-redux";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { getuser } from "../utility/utility.jsx";
import { func } from "prop-types";

// import { embedSession } from 'amazon-quicksight-embedding-sdk';
import { embedSession } from "../utility/utility.jsx";


class QuickSightAMDashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "QuickSight Visualization",
      QuicksightUrl: []
    };
    this.ecode = null;
    this.mapping = {
      // 'TL Dashboard': {
      //   "DashboardId": "3ee52d5c-44e6-4f2a-b97d-e91d3846ba99",
      // }
      'AM Dashboard': {
        "DashboardId": "a6059828-f383-4196-99df-99da9af13c74",
      }
    };
  }


  componentDidMount() {
    console.log("getuser", getuser());
    let ecode = getuser().EmployeeId;
    let RoleId = getuser().RoleId;
    this.ecode = ecode;

    let DashboardId = "a6059828-f383-4196-99df-99da9af13c74"
    let parameters = {
      AM: this.ecode
    }

    

    GetDashboardUrl({
      "userid": this.ecode,
      "DashboardId": DashboardId
    }, (result) => {
      console.log('-----result', result);
      debugger
      if (result && result.data && result.data.QuicksightUrl) {
        let embedURL = result.data.QuicksightUrl

        var containerDiv = document.getElementById("embeddingContainer");
        var options = {
          url: embedURL,
          container: containerDiv,
          parameters: parameters,
          //sheetId : "e7cbd8ed-294f-40d9-ac1f-dcd24cf58e2e_177b567a-079f-4e42-a7e4-bdc937454b19",
          scrolling: "no",
          height: "700px",
          width: "100%",
          locale: "en-US",
          footerPaddingEnabled: true
        };

        const session = embedSession(options);

      }
    });
    try {
      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "InsertAgentIncentiveLog",
        c: "L",
        params: [{
          UserId: getuser().UserID,
          SuperGroupId: 0,
          ProductId: 0,
          incentiveMonth: '03-03-2021',
          Source: 'MatrixDashboard',
          PageName: 'QuickSight - AM',
          EventName: DashboardId

        }]
      }, function () {

      })
    }
    catch (e) { }

  }






  handleOpen(department) {
    console.log('----------department', department);

    GetDashboardUrl({
      "userid": this.ecode,
      "DashboardId": this.mapping[department]['DashboardId']
    }, (result) => {
      console.log('-----result', result);
      //debugger
      if (result && result.data && result.data.QuicksightUrl) {
        let url = result.data.QuicksightUrl


        // resolveRedirectURL({
        //   url
        // }, function (data) {
        //   console.log(data);
        // })


        // let url = result.data.QuicksightUrl + "#p.TL=" + this.ecode
        // let win  = window.open(url, '_blank');
        // setTimeout(function(){
        //   debugger;
        //   console.log(win);

        // }, 2000)
      }
    });
    try {
      this.props.GetCommonspData({
        limit: 10,
        skip: 0,
        root: "InsertAgentIncentiveLog",
        c: "L",
        params: [{
          UserId: getuser().UserID,
          SuperGroupId: 0,
          ProductId: 0,
          incentiveMonth: '03-03-2021',
          Source: 'MatrixDashboard',
          PageName: 'QuickSight',
          EventName: department

        }]
      }, function () {

      })
    }
    catch (e) { }
  }

  render() {
    const { QuicksightUrl, PageTitle } = this.state;
    console.log('-------QuicksightUrl-------', QuicksightUrl);
    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={10}>
                      <CardTitle tag="h3">{PageTitle}</CardTitle>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <div id="embeddingContainer"></div>

                  {/* {QuicksightUrl && QuicksightUrl.map((item) => {
                    return <div>
                      <h5>{item.department}</h5>
                      <Button onClick={() => this.handleOpen(item.department)} variant="primary" style={{ marginBottom: "3.5%" }}>Click to view dashboard</Button>
                    </div>
                  })} */}
                </CardBody>
              </Card>
            </Col>
          </Row>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData


  }
)(QuickSightAMDashboard);