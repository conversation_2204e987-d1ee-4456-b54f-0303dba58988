import { useEffect, useState } from "react";
import React from "react";
import { ButtonGroup, Button, Modal, Form } from 'react-bootstrap';

import {
    GetCommonData, GetCommonspData, InsertData, ContestMapping
} from "../../store/actions/CommonActionPbIncentive";

import { connect } from "react-redux";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getuser } from "../../utility/utility";
import { Link } from 'react-router-dom'
import * as xlsx from 'xlsx';

import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col,
} from "reactstrap";
import DropDown from '../Common/DropDown';
import 'react-bootstrap-typeahead/css/Typeahead.css';

const ContestAgentMapping = (props) => {
    const [items, setitems] = useState([])
    const PageTitle = "Agent Mapping"
    const [selected, setSelected] = useState([]);
    const [employeeId, setEmployeeId] = useState();
    const [SuccessData, setSuccessData] = useState([]);
    const [showbutton, setshowbutton] = useState(false);
    const [showUploadExcelButton, setShowUploadExcelButton] = useState(false);
    const [LoadClass, setLoadClass] = useState("")
    const [LoadClassAll, setLoadClassAll] = useState("")
    const [disClass, setdisClass] = useState(false);
    const [ModalOpen, setModalOpen] = useState(false);

    const [optionId, setOptionId] = useState();
    const [applicationid, setApplicationid] = useState();
    const [user, setUser] = useState({});
    const [DownloadFile, setDownloadFile] = useState("/SampleExcelfiles/SampleContestAgentMapping.xlsx")
    const [ApplicationMaster, setApplicationMaster] = useState([]);

    let dataArray = []
    let text = ""

    const columnlist = [
        {
            name: 'Employee Id',
            selector: 'EmployeeId',
            type: "string",
            editable: false,
        },
        {
            name: 'Status',
            selector: 'status',
            cell: row => <div>{row.status == 0 ? "Failure" : row.status == "Pending" ? "Pending" : ""}</div>,
            type: "string",
            editable: false,
        },

    ];

    useEffect(() => {
        const user = getuser();

        setUser(user);
        props.GetCommonData({
            root: 'ContestApplicationMaster',
            con: [{ "IsActive": 1 }],
            c: "R"
        }, function (data) {
            if (Array.isArray(data[0]) && data[0].length > 0) {
                setApplicationMaster(data[0]);
            }
        })
    }, [])

    useEffect(() => {

        let appid = parseInt(applicationid);
        if (applicationid) {

            props.GetCommonData({
                limit: 10,
                skip: 0,
                root: 'ContestMaster',
                cols: ["ContestId AS Id", "ContestName AS Display"],
                con: [{ "Isactive": 1, "ApplicationId": appid }],
                order: 'CreatedOn',
                c: "R",
            }, function (data) {
                if (data) {

                    if (data.length > 0) {
                        let item = data;
                        item.sort(function (a, b) {
                            return b.Id - a.Id;
                        });

                        setitems(item[0]);
                    }
                    else {
                        setitems(data);
                    }
                }
            });

        }
        else {
            setitems([]);
        }

    }, [applicationid])


    const ContestChange = (e) => {
        let selected = parseInt(e.target.value);
        setSelected(selected);
    }

    const Getdata = (e) => {
        e.preventDefault();
        if (!employeeId || selected == 0) {
            toast.error("Please fill all the required fields!")
        }

        if (employeeId && selected > 0) {
            let match = employeeId.split(',')
            for (let i = 0; i < match.length; i++) {
                let emid = match[i].trim();
                if (emid == "") {
                    continue
                }

                let arr = { EmployeeId: emid, UserName: emid }
                dataArray.push(arr)
                setSuccessData(dataArray)
                setshowbutton(true);
            }
        }
    }


    const handleSubmit = () => {

        if (selected == 0) {
            toast.error("Please select a contest!");
            return;
        }
        //setaddClass('btn btn-primary fa fa-spinner');
        setLoadClass('spinner-border spinner-border-sm');
        setdisClass(true)

        let updateArray = [...SuccessData]

        for (let i = 0; i < updateArray.length; i++) {
            if (updateArray[i].EmployeeId && updateArray[i].UserId) {
                updateArray[i] = { ...updateArray[i], ContestId: selected, CreatedBy: parseInt(user.UserID) }
            }
            else {
                toast.error("Enter all fields properly");
                setLoadClass("");
                setdisClass(false);
                return;
            }
        }


        ContestMapping(updateArray, function (results) {
            setLoadClass("");
            setdisClass(false);
            if (results.data.status == 200) {

                toast.success('Saved Successfully');

            } else {
                toast(results.data.message, { type: 'error' });

                return;
            }
        });

    }


    const renderDownloadFile = () => {
        if (DownloadFile) {
            return <Link to={DownloadFile} target="_blank" download>Download Sample Excel File</Link>
        }
    }

    const handleChangeExcelFile = (e) => {
        setShowUploadExcelButton(false);
        if (e.target.files) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = e.target.result;
                const workbook = xlsx.read(data, { type: "array" });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const json = xlsx.utils.sheet_to_json(worksheet);

                for (let i = 0; i < json.length; i++) {
                    let empid = json[i].EmployeeId.replace(/ /g, '');
                    let uid = json[i].UserId;
                    let target = json[i].Target;
                    let visits = json[i].Visits;
                    let APE = json[i].APE;
                    let isNum = /^\d+$/.test(json[i].Target) && /^\d+$/.test(json[i].Visits) && /^\d+$/.test(json[i].APE);
                    if (!isNum) {
                        toast.error("Target, Visits and APE can only contain numeric values");
                        dataArray = [];
                        break;
                    }
                    json[i].Target = parseInt(target);
                    json[i].Visits = parseInt(visits);
                    json[i].APE = parseInt(APE);
                    dataArray.push({ EmployeeId: empid, UserId: uid, Target: target, Visits: visits, Ape: APE })
                }


                if (dataArray.length > 0) {
                    setSuccessData(dataArray);
                    setShowUploadExcelButton(true);
                }
            };

            reader.readAsArrayBuffer(e.target.files[0]);
        }
    }


    const selectedOptionTeam = (e) => {
        setOptionId("Select")
        setApplicationid(e.target.value);
    }

    return (
        <>
            <ToastContainer />
            <div className="content">

                <Row>
                    <Col md="12">
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={11}>
                                        <CardTitle tag="h4">{PageTitle}</CardTitle>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Col md={12}>
                                        <Form.Label>Select Application</Form.Label>
                                        <Form.Select onChange={selectedOptionTeam} value={applicationid}>
                                            <option value="SelectApplication">Select Application</option>
                                            {ApplicationMaster && ApplicationMaster.map((item) =>
                                                <option value={item.ApplicationId}>{item.ApplicationName}</option>
                                            )}
                                        </Form.Select>
                                        <Form >
                                            <Form.Label>Contest</Form.Label>
                                            {/* <DropDown AutoList={true} firstoption="Select" items={items} onChange={SurveyChange} ></DropDown> */}
                                            <DropDown firstoption="Select" items={items} onChange={ContestChange} ></DropDown>
                                        </Form>
                                    </Col>
                                </Row>
                                <br />

                                <div>
                                    <form>
                                        <Row>
                                            <Col md={2}>
                                                <label htmlFor="upload excel">Upload Excel</label>
                                                <input type="file" id='file-upload' name="upload excel" disabled={disClass} onChange={handleChangeExcelFile} onClick={(event) => { event.target.value = null }} />
                                            </Col>
                                            {renderDownloadFile()}
                                            {showUploadExcelButton &&
                                                <Col md={2}>
                                                    <Button style={{ marginLeft: "10px" }} className="btn btn-primary" onClick={handleSubmit}>
                                                        <span className={LoadClass}></span>
                                                        Publish
                                                    </Button>
                                                </Col>
                                            }
                                        </Row>
                                    </form>
                                </div>
                                <br></br>



                            </CardBody>
                        </Card>
                    </Col>
                </Row>

            </div>
        </>
    );
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        InsertData,
        ContestMapping
    }
)(ContestAgentMapping);