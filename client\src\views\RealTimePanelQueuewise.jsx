import React from "react";
import {
  GetMySqlData
} from "../store/actions/CommonMysqlAction";
import {
  GetCommonData, GetCommonspData, GetRealTimeAgentData, GetRealTimeTotalData, GetRealTimeQueueData, GetFileExists
} from "../store/actions/CommonAction";
import { getUrlParameter, hhmmss, getuser } from '../utility/utility.jsx';
import { connect } from "react-redux";
import DataTable from 'react-data-table-component';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import { Web } from "sip.js";
// import { Web } from "sip.js/lib";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  addRecord, UpdateData, gaEventTracker
} from "../store/actions/CommonMongoAction";
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col,
  Table
} from "reactstrap";
import { Form, Modal, Glyphicon } from 'react-bootstrap';
import { If, Then, Else } from 'react-if';

class RealTimePanelQueuewise extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "RealTime Panel",
      AgentData: [],
      TotalData: [],
      items: [],
      key: "ALL",
      onBarge: false,
      SelectedSupervisors: [],
      winactive: 0,
      BargeWith: "",
      queues: [],
      QueueData: [],
      MoreInfoData: [],
      contextName: null,
      showModal: false,
      showMoreInfoModal: false,
      addClass: "fa fa-play-circle",
      queueServerIp: "",
      clickedQueue: "",
      Leads : null,
      body : [],
      AgentCode : "",
      AgentName : "",
      currLeadId: ""
    };
    this.handleShow = this.handleShow.bind(this);
    this.statuschange = this.statuschange.bind(this);
    this._handleKeyDown = this._handleKeyDown.bind(this);
    this.saveBargingLogs = this.saveBargingLogs.bind(this);
    //this.bargecall = this.bargecall.bind(this);
    this.userAgent = null;
    this.winactive = 0;
    this.schdular = null;

    this.unansweredlist = [
      { name: "Callid", selector: "callid" },
      { name: "leadid", selector: "leadid" },
      { name: "Calltime", cell: row => <div className="calltime">{row.calltime ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.calltime}</Moment> : "N.A"}</div> },
      { name: "Status", cell: row => <div className="abandon">{this.state.ModalTitle.indexOf("Unanswered") > -1 ? "ABANDON" : "ANSWERED"}</div> },
      {
        name: "Is Mobile", selector: "IsWFH", cell: row => <div>{row.IsWFH ? "true" : "false"}</div>
      },
      {
        name: "Waiting Time", selector: "waittime"
      },
      {
        name: "More Info", cell: row => <div className="moreinfo"><a onClick={(e) => this.clickMoreinfo(e, row)} className="detailsinfo">
          <i className="fa fa-eye"></i></a>
        </div>
      },
    ];
    this.moreinfolist = [
      { name: "callid", selector: "callid" },
      { name: "leadid", selector: "leadid" }, { name: "Calldate", cell: row => <div className="calldate">{row.calldate ? <Moment utc={true} format="YYYY-MM-DD HH:mm:ss">{row.calldate}</Moment> : "N.A"}</div> },
      { name: "agentno", selector: "agentno" }, { name: "agentid", selector: "agentid" },
      { name: "duration", selector: "ringtime" }, { name: "disposition", selector: "custom_disposition" },
      {
        name: "Listen", cell: row =>
          <div className="listen">
            {this.CreateRecordingURL(row)}
          </div>
      }
    ];
    this.columnlist = [
      {
        name: "Barging",
        selector: "Barging",
        sortable: true,
        width: "80px",
        cell: row =>
          <div className={row.Status == "BUSY" ? "" : "hide"} >
            <button onClick={(e) => this.bargecall(e, row)} className={row.Barge ? "hide" : "show"}><i className="fa fa-volume-up" aria-hidden="true"></i></button>
          </div>
      },
      {
        name: "Agent Code",
        selector: "AgentCode",

        sortable: true,
      },
      {
        name: "Status",
        selector: "Status",
        sortable: true,

        cell: row => <div className={this.displayStatus(row) + " RealtimeStatus"}>{this.displayStatus(row)}</div>
      },
      {
        name: "Agent Name",
        selector: "AgentName",
        sortable: true,
      },
      {
        name: "Call Type",
        selector: "CallType",
      },
      {
        name: "Lead Id",
        selector: "LeadId",
        sortable: true,
      },
      {
        name: "D.C.",
        selector: "CallingCompany",

        sortable: true,
      },
      {
        name: "DIDNo",
        selector: "DIDNo",

        sortable: true,
      },
      // {
      //   name: "Asterisk_Url",
      //   selector: "Asterisk_Url",
      //   width: "120px",
      //   sortable: true,
      // },

      // {
      //   name: "Last Updated On",
      //   selector: "LastUpdatedOn",
      //   width: "130px",
      //   sortable: true,
      //   cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment format="HH:mm:ss A">{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      // },
      {
        name: "Since",
        selector: "LastUpdatedOn",
        sortable: true,
        width: "130px",
        cell: row => <div className={row.AgentCode}>{row.LastUpdatedOn ? <Moment fromNow>{row.LastUpdatedOn}</Moment> : "N.A"}</div>
      },
      {
        name: "T Calls",
        selector: "TotalCalls",
        width: "70px",
        sortable: true,
      },
      {
        name: "U Dials",
        selector: "UniqueDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "C Dials",
        selector: "ConnectedDials",
        width: "70px",
        sortable: true,
      },
      {
        name: "T TalkTime",
        selector: "TotalTalkTime",
        sortable: true,
        width: "130px",
        cell: row => hhmmss(row.TotalTalkTime),
      },

    ];


  }

  componentDidMount() {



    const user = getuser();
    this.setState({ SelectedSupervisors: [getUrlParameter("m") == "" ? user.EmployeeId : getUrlParameter("m")] }, function () {
      this.getqueue();
      this.UserList();
      this.queueList();
    }.bind(this));

    if (this.schdular == null) {
      this.schdular = setInterval(function () {
        //if (!this.state.onBarge) {
        //if ((new Date()).getHours() >= 21 || (new Date()).getHours() < 9) {
        if (this.state.winactive == 1 || document.hasFocus()) {
          //this.getqueue();
          this.UserList();
          this.totalList();
          this.queueList();
        }
        // }
        // else {
        //   this.UserList();
        //   this.totalList();
        // }
        //}
      }.bind(this), 2500)

      window.addEventListener("message", function (event) {
        if (event.data.type == "checkactive") {
          this.setState({ winactive: event.data.winactive })
          this.winactive = event.data.winactive;
        }
      }.bind(this));
    }
  }

  CreateRecordingURL(row) {

    let userfield = row.userfield;
    let dstchannel = row.dstchannel;
    let date = moment(new Date(userfield)).format("DD-MM-YYYY");
    let hour = moment(new Date(userfield)).format("H");
    let datetime = moment(new Date(userfield)).format("YYYYMMDDHHmmss");
    let phoneNo = dstchannel.substring(dstchannel.indexOf("/") + 1, dstchannel.indexOf("-"));
    let callid = row.callid;
    console.log(date, hour, datetime, phoneNo, callid);
    let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/" + date + "/" + hour + "/" + callid + "-" + datetime + "-" + phoneNo + ".wav";


    if (date == "Invalid date" || hour == "Invalid date" || datetime == "Invalid date" || phoneNo == null) {
      return <span>File not found</span>
    }

    GetFileExists(url, function (params) {
      debugger;
      if (params && params.status && params.status != 404) {
      }
      else {
        document.getElementById("span_" + row.row_num).innerHTML = "File not found";
      }
    });

    //let url = "https://asterisk-log.s3.ap-south-1.amazonaws.com/recording/12-05-2020/10/1589258668.10883-20200512101428-07509883158.wav";
    return <span id={"span_" + row.row_num}><audio src={url} id={"audio" + "_" + row.row_num}></audio>
      <i className={this.state.addClass} id={"play" + row.row_num} onClick={(e) => this.play(row.row_num, e)}></i>
    </span>;
  }
  saveBargingLogs(row) {
    try{
      // inserting first record on barge
      let bargeTimestamp = new Date();
      let bargeLog = {
        Agent: row.AgentCode,
        BargedBy: getuser().EmployeeId,
        AgentName: row.AgentName,
        LeadID: row.LeadId,
        Type: "Barge",
        Timestamp: bargeTimestamp,
      }
      
      // setting leadid
      console.log("GA-Start: ",bargeLog)
      sessionStorage.setItem("BargeWithLeadId", row.LeadId);
      
      gaEventTracker("BargeCall", JSON.stringify(bargeLog), getuser().EmployeeId);
      console.log("GA-END")
  
      this.setState({body: bargeLog})
  
    }
    catch(e){
      console.log(e);
    }
   
  };
  totalList() {
    let context = getUrlParameter("c");
    if (context != "") {
      GetRealTimeTotalData(context, function (results) {
        this.setState({ TotalData: results.data });
      }.bind(this));
    }
  }

  queueList() {
    let queues = this.state.queues;
    console.log("queues", queues);

    var queuestring = queues.map(function (val) {
      return val.queuename;
    });

    var serverip = queues.map(function (val) {
      return val.server_ip;
    });

    //var contextName = this.state.contextName ?? queuestring[0];

    //this.setState({ queueServerIp: serverip[0], contextName: contextName });
    this.setState({ queueServerIp: serverip[0] });

    GetRealTimeQueueData(queuestring.join(","), function (results) {
      //console.log("results", results);
      this.setState({ QueueData: results.data });
    }.bind(this));
  }
  getqueue() {
    let type = getUrlParameter("type") == "" ? "sales" : getUrlParameter("type");
    let proid = getUrlParameter("product") == "" ? "1" : getUrlParameter("product");
    let is_mobile = getUrlParameter("IsMobile") == "" ? "0" : getUrlParameter("IsMobile");
    let is_claim = getUrlParameter("IsClaim") == "" ? "0" : getUrlParameter("IsClaim");


    this.props.GetMySqlData({
      root: "getqueue",
      ProductType: type,
      ProductId: proid,
      IsMobile: is_mobile,
      IsClaim: is_claim,
    });
  }

  UserList() {
    let managerid = getUrlParameter("m");
    let context = this.state.contextName == null ? getUrlParameter("c") : this.state.contextName;
    if (context == 'breakinmotor') {
      context = 'BreakinMotor';
    }
    if (context == 'claimofflinemotorib') {
      context = "claimofflinemotornia,claimofflinemotorprivate,claimofflinemotoruiic,claimofflinemotornao";
    }
    // if(context == 'claimonlinemotorib'){
    //   context = "claimonlinemotordc,claimonlinemotorqc,claimonlinemotorpayment";
    // }
    const user = getuser();
    if (this.state.SelectedSupervisors.length > 0) {
      managerid = this.state.SelectedSupervisors.join()
    }

    if (managerid == "" && context == "") {
      managerid = user.EmployeeId;
      this.setState({ SelectedSupervisors: [managerid] });
    }
    if (managerid || context) {
      GetRealTimeAgentData(managerid, context, function (results) {
        this.setState({ AgentData: results.data });
      }.bind(this));
    }

  }

  handleShow(e) {
    this.setState({ SelectedSupervisors: e.SelectedSupervisors });
  }

  componentWillUnmount() {
    clearInterval(this.schdular);
    if (this.userAgent != null)
      this.userAgent.hangup();
  }

  changeContext(e, row) {
    this.setState({ contextName: row.context });
  }

  clickUnanswered(e, row) {
    let queuename = row.context;
    this.setState({ clickedQueue: queuename });
    this.props.GetMySqlData({
      root: "unanswered",
      queues: queuename,
    }, function (result) {
      //debugger;
      this.setState({ showModal: true, UnansweredData: result.data.data[0], ModalTitle: "Unanswered Calls" });
    }.bind(this));

  }
  clickanswered(e, row) {
    let queuename = row.context;
    this.setState({ clickedQueue: queuename });

    this.props.GetMySqlData({
      root: "answered",
      queues: queuename,
    }, function (result) {
      //debugger;
      this.setState({ showModal: true, UnansweredData: result.data.data[0], ModalTitle: "Answered Calls" });
    }.bind(this));

  }

  clickMoreinfo(e, row) {
    let uniqueid = row.callid;
    let queues = this.state.queues;
    let queue = this.state.clickedQueue;

    const queueData = queues.find((item) => item.queuename.toLowerCase() === queue.toLowerCase());

    // if (queue == 'breakinmotor'){
    //   var queueServerIp = "**********";
    // }else{
    var queueServerIp = queueData.server_ip;
    // }

    this.props.GetMySqlData({
      root: "moreinfo",
      uniqueid: uniqueid,
      queueServerIp: queueServerIp,
    }, function (result) {//debugger;
      this.setState({ showMoreInfoModal: true, MoreInfoData: result.data.data[0] });
    }.bind(this));

  }

  displayStatus(row) {
    // var diff = (new Date() - new Date(row.LastUpdatedOn)) / 1000;
    // if (diff > 60 && row.Status == "IDLE") {
    //   return "Away";
    // } else return row.Status;
    let BargeWithAgent = this.state.BargeWithAgent;
    if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status != "BUSY") {

      // if (this.userAgent) {
      //   this.userAgent.hangup();
      //   this.userAgent = null;        
      // }
      // this.setState({ onBarge: false, BargeWith: "" });
      this.unbargecall();
    }
    else if (BargeWithAgent && BargeWithAgent.AgentCode == row.AgentCode && row.Status == "BUSY") {
      // if (this.userAgent == null) {
      //   this.bargecall(null, BargeWithAgent);
      // }
    }
    return row.Status.toUpperCase()
  }

  unbargecall(e) {
    if (this.userAgent) {
      try {
        if(this.state.body != []){
        let UnbargeTimestamp = new Date();
        let unbargeLog = {
          Agent: this.state.body.Agent,
          BargedBy: getuser().EmployeeId,
          AgentName: this.state.body.AgentName,
          LeadID: sessionStorage.getItem("BargeWithLeadId"),
          Type: "Unbarge",
          Timestamp: UnbargeTimestamp,
        }
        gaEventTracker("BargeCall", JSON.stringify(unbargeLog), getuser().EmployeeId);
      }
      
      }
      catch (e) {

      }
      this.userAgent.hangup();
      this.userAgent = null;


    }
    sessionStorage.setItem("BargeWith", "");
  }


  bargecall(e, row) {
    try {
      if (this.userAgent) {
        this.userAgent.hangup();
        this.userAgent = null
      }
      //if (!this.state.onBarge) {
      let user = {
        Display: getuser().EmployeeId,
        User: getuser().EmployeeId,
        Pass: getuser().EmployeeId,
        Realm: row.Asterisk_Url,
        WSServer: "wss://" + row.Asterisk_Url + ":8089/ws"
      }
      this.state.AgentName = row.AgentName
      this.state.AgentCode = row.AgentCode
      this.LoginAsteriskServer(user, function () {
        setTimeout(function () {

          if (this.userAgent) {
            let target = "*222" + row.AgentCode;
            if (row.IsWFH || (row.CallingCompany == "WFH")) {
              target = "*222" + row.DIDNo;
            }

            // if (row.CallingCompany == "WFH" || row.CallingCompany == "KNOWLARITY") {
            //   target = "*222" + row.DIDNo;
            // }

            this.userAgent.call(target);
          }
         
          this.saveBargingLogs(row)
          this.setState({ onBarge: true, BargeWith: row.AgentCode, BargeWithAgent: row });

          // setTimeout(function () {
          //   //this.forceUpdate();

          // }.bind(this), 500);

        }.bind(this), 1000);
      }.bind(this), function () {
        if (document.getElementById(row.AgentCode)) {
          document.getElementById(row.AgentCode).checked = false;
        }
        sessionStorage.setItem("BargeWith", "");
      }.bind(this));

      // }
      // else {
      //   toast("Close previous call barging", { type: 'error' });
      //   e.target.checked = false;
      //   return false;
      // }
      
       
      
      

    } catch (e) {

    }

  }

 
  
  LoginAsteriskServer(user, onsuccess, onerror) {

    if (user) {
      var config = {
        media: {
          remote: {
            //video: document.getElementById('remoteVideo'),
            // This is necessary to do an audio/video call as opposed to just a video call
            audio: document.getElementById('audioRemote')
          }
        },
        ua: {
          uri: user.User + '@' + user.Realm,
          wsServers: [user.WSServer],
          authorizationUser: user.Display,
          password: user.Pass
        }
      }

      if (Web) {
        this.userAgent = new Web.Simple(config);
        // this.userAgent = new Web.SimpleUser(config);

        //let remoteElem = document.getElementById('audioRemote');
        //let localElem = document.getElementById('audioLocal');
        this.userAgent.on('connected', function (e) {
          toast("Barging Connected!", { type: 'success' });
        });
        this.userAgent.on('disconnected', function (e) {

        });
        this.userAgent.on('registered', function (e) {
          if (onsuccess) {
            onsuccess();
          }

        });
        this.userAgent.on('registrationFailed', function (e) {
          toast("Make sure your VPN is connected!", { type: 'error' });
        });
        this.userAgent.on('unregistered', function (e) {
          toast("Dialer issue please contact administrator!", { type: 'error' });
          if (onerror) {
            onerror();
          }

        });
        this.userAgent.on('userMediaFailed', function (e) {

        });
        this.userAgent.on('userMediaRequest', function (e) {

        });
        this.userAgent.on('userMedia', function (e) {

        });
        this.userAgent.on('invite', function (e) {

        });
        this.userAgent.on('addStream', function (stream) {

        });
        this.userAgent.on('ended', function (stream) {

        });
      }
    }

    setTimeout(function () {
      if (this.userAgent && this.userAgent.ua && this.userAgent.ua.isRegistered() == false) {
        toast("Make sure your VPN is connected!", { type: 'error' });
      }
    }.bind(this), 10000);
    return this.userAgent;
  }
  componentWillReceiveProps(nextProps) {
    if (!nextProps.CommonData.isError) {
      this.setState({ queues: nextProps.CommonData["getqueue"] });
      //this.setState({ UnansweredData: nextProps.CommonData["unanswered"] });
    }
  }

  play(number) {
    var audio = document.getElementById('audio_' + number);
    var icon = document.getElementById("play" + number);
    if (audio.paused) {
      audio.play();
      icon.classList.remove("fa-play-circle");
      icon.classList.add("fa-stop-circle");
      this.CheckAudioFinishedPlay(audio, icon);

    } else {
      audio.pause();
      audio.currentTime = 0
      icon.classList.remove("fa-stop-circle");
      icon.classList.add("fa-play-circle");
    }
  }

  CheckAudioFinishedPlay(audio, icon) {
    debugger;
    audio.onended = function () {
      icon.classList.remove("fa-stop-circle");
      icon.classList.add("fa-play-circle");
    };
  }

  statuschange(e) {
    this.setState({ key: e.target.value });
  }

  filterdata(e) {

    let alldata = this.state.AgentData
    let that = this;
    if (this.state.key === "ALL") {
      return alldata;
    }
    if (this.state.key === "Away") {
      let AgentData = [];
      alldata.forEach(element => {
        var diff = (new Date() - new Date(element.LastUpdatedOn)) / 1000;
        if (diff > 60 && element.Status === "IDLE") {
          AgentData.push(element);
        }
      });
      return AgentData;
    }

    let AgentData = [];
    alldata.forEach(element => {
      if (this.state.key.indexOf(element.Status) > -1) {
        AgentData.push(element);
      }
    });
    return AgentData;
  }
  _handleKeyDown(e) {

    if (e.key === 'Enter') {
      this.setState({ SelectedSupervisors: [e.target.value] });
    }
  }

  _handleOnClick(e) {


    this.setState({ SelectedSupervisors: [document.getElementById("EmpId").value] });

  }

  renderTotalData() {


    let tr = [];
    let QueueData = this.state.QueueData;
    if (QueueData && QueueData.length > 0) {

      QueueData.forEach(element => {
        tr.push(<tr className={this.state.contextName == element.context ? "active" : ""}>
          <td>{<a onClick={(e) => this.changeContext(e, element)} data-myattribute="check" className="abutton clickMe">{element.context}</a>}</td>
          {/* <td>{element.answered}</td> */}
          <td>{<a onClick={(e) => this.clickanswered(e, element)} className="abutton">{element.answered}</a>}</td>
          <td>{<a onClick={(e) => this.clickUnanswered(e, element)} className="abutton">{element.unanswered}</a>}</td>
          <td className={element.waitingibcalls > 0 ? "waiting_call_gt0" : ""}>{element.waitingibcalls}</td>
          <td className={element.waitingctccalls > 0 ? "waiting_call_gt0" : ""}>{element.waitingctccalls}</td>
          <td>{element.totalcalls}</td>
        </tr>)
      });
      return <Table responsive>
        <thead className="text-primary">
          <tr>
            <th>context</th>
            <th>answered</th>
            <th>unanswered</th>
            <th>waitingibcalls</th>
            <th>waitingctccalls</th>
            <th>totalcalls</th>
          </tr>
        </thead>
        <tbody>
          {tr}
        </tbody>
      </Table>
    }
    else {
      return null;
    }
  }


  render() {
    const columns = this.columnlist;

    const unansweredcolumns = this.unansweredlist;
    const moreinfocolumns = this.moreinfolist;

    const totalcolumns = this.totalcolumnlist;
    const data = this.filterdata();
    const managerid = getUrlParameter("m");
    const context = getUrlParameter("c");

    const { PageTitle, UnansweredData, MoreInfoData } = this.state;


    return (
      <>
        <div className="content">
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">Queues</CardTitle>
                    </Col>
                    <Col md={9}>

                    </Col>
                  </Row>

                </CardHeader>

                <CardBody>
                  <div className="queuetable">
                    {this.renderTotalData()}
                  </div>
                </CardBody>

              </Card>




            </Col>

          </Row>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>

                  <Row>
                    <Col md={3}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={4}>
                      <div>
                        Queue Name : {this.state.contextName}
                      </div>
                    </Col>

                    <Col md={3}>
                      <div className="input-group hide">

                        <Form.Control required type="text" name="EmpId" id="EmpId" onKeyDown={this._handleKeyDown} onChange={(e) => this.setState({ username: e.target.value })} value={this.state.username} placeholder={"Enter Supervisor Id"} />
                        <div className="input-group-append">
                          <button onClick={(e) => this._handleOnClick(e)} className="btn btn-primary input-group-button"><i className="fa fa-search" aria-hidden="true"></i></button>

                        </div>
                      </div>
                    </Col>
                    <Col md={2}>
                      <div className="form-group ">
                        <select className="form-control" onChange={this.statuschange}>
                          <option value="ALL">ALL</option>
                          <option value="IDLE">IDLE</option>
                          <option value="Away">Away</option>
                          <option value="BUSY">BUSY</option>
                          <option value="Lunch,Tea,Training,Meeting,Day End">Break</option>
                          <option value="PAUSE">PAUSE</option>
                          <option value="Auto Logout,LOGOUT">LOGOUT</option>
                        </select>
                      </div>
                      {
                        (managerid == '' && context == ' ') ? <ManagerHierarchy handleShow={this.handleShow} value={/EmployeeId/g} ></ManagerHierarchy> : null
                      }
                      <button id="BargeWith" onClick={(e) => this.unbargecall(e)} className={this.state.BargeWith == "" ? "hide" : "btn btn-primary hangupwith show"} ><i className="fa fa-volume-off" aria-hidden="true"></i> Hang Up With: {this.state.BargeWith}</button>
                    </Col>

                  </Row>

                </CardHeader>

                <CardBody>
                  {/* {this.renderTotalData()} */}

                  <div className="statusdata">
                    <DataTable
                      columns={columns}
                      data={data}
                      pagination={false}
                      striped={true}
                      noHeader={true}
                      highlightOnHover
                      dense

                    />

                  </div>
                </CardBody>

              </Card>
            </Col>
            <audio id="audioRemote"></audio>
            <audio id="audioLocal"></audio>

          </Row>

          <Modal show={this.state.showModal} onHide={() => this.setState({ showModal: false })} dialogClassName="modal-70w">
            <Modal.Header closeButton>
              <Modal.Title>{this.state.ModalTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modaldata">
                <DataTable
                  columns={unansweredcolumns}
                  data={(UnansweredData && UnansweredData.length > 0) ? UnansweredData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense
                  pagination={true}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
          <Modal show={this.state.showMoreInfoModal} onHide={() => this.setState({ showMoreInfoModal: false })} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                <DataTable
                  columns={moreinfocolumns}
                  data={(MoreInfoData && MoreInfoData.length > 0) ? MoreInfoData : []}
                  pagination={false}
                  striped={true}
                  noHeader={true}
                  highlightOnHover
                  dense

                />
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>
        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetMySqlData,
    addRecord,
    UpdateData,
    gaEventTracker
  }
)(RealTimePanelQueuewise);




