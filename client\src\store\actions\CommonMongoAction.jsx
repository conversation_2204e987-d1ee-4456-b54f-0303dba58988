import axios from "axios";
import config from "../../config.jsx";
import constants from "../../Constants/Constants.jsx";

const GetCommonData_fetched = Todos => {

  return {
    type: constants.GET_COMMON_SUCCESS,
    payload: Todos.data,
    root: Todos.dataToSend.root
  };
};

const GetCommonData_fetch_error = error => {
  return {
    type: constants.GET_COMMON_FAIL,
    payload: error
  };
};

export const GetCommonData = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    let url = config.api.base_url + "/mdb/list/";
    if(dataToSend.type == "count"){
      url = config.api.base_url + "/mdb/count/";
    }

    axios
      .post(url, dataToSend)
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
  
};


const InsertData_fetched = Todos => {

  return {
    type: constants.INSERT_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const InsertData_fetch_error = error => {
  return {
    type: constants.INSERT_COMMON_FAIL,
    payload: error
  };
};
export const gaEventTracker = (eventAction, eventLabel, leadId, eventCategory='matrixdashboard') => {
  try {
      const data = {
          event: 'eventTracking',
          eventCategory: eventCategory,
          eventAction: eventAction,
          eventLabel: eventLabel,
          eventValue: 1,
          leadid: leadId,
          //customerid: ""  //todo
      };

      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push(data);
  }
  catch (e) {
      // eslint-disable-next-line no-console
      console.error('gaError error: ', e);
  }
};

export const addRecord = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/mdb/insert", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(InsertData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(InsertData_fetch_error(error));
      });
  };
};



const UpdateData_fetched = Todos => {

  return {
    type: constants.UPDATE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const UpdateData_fetch_error = error => {
  return {
    type: constants.UPDATE_COMMON_FAIL,
    payload: error
  };
};

export const UpdateData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/mdb/update/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(UpdateData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(UpdateData_fetch_error(error));
      });
  };
};

const DeleteData_fetched = Todos => {

  return {
    type: constants.DELETE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const DeleteData_fetch_error = error => {
  return {
    type: constants.DELETE_COMMON_FAIL,
    payload: error
  };
};

export const DeleteData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/mdb/delete", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(DeleteData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(DeleteData_fetch_error(error));
      });
  };
};

export const GetMongoData = (dataToSend, cb) => {
    let url = config.api.base_url + "/mdb/list/";

    axios
      .post(url, dataToSend)
      .then(data => {
        if (cb) {
          cb(data);
        }
        
      })
      .catch(error => {
        cb(error);
      });

};