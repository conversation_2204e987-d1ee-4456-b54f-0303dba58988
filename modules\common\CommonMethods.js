const CryptoJS = require("crypto-js");
const crypto = require('crypto');
const config = require('../../env_config');
const AWS = require('aws-sdk');
const Algorithm = 'aes-256-cbc';
const cache = require('memory-cache');
const sqlHelper = require('../../Libs/sqlHelper');
const LoggerMethods = require("../Loggerdb/Methods");
const SQLdbMethods = require("../SQLdb/SQLdbMethods");

const IsJsonString = (str) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

const GetDataFromCache = ({ key }) => {
  try {
    let data = cache.get(key);
    return data;
  } catch (err) {
    console.log('Inside GetDataFromCache', err);
    return null
  }
}

const AesEncryption = ({ data, key, iv }) => {
  try {
    let secret_key = key;
    secret_key = CryptoJS.enc.Base64.parse(secret_key);
    secret_key = CryptoJS.enc.Utf8.stringify(secret_key);

    let secret_iv = iv;
    secret_iv = CryptoJS.enc.Base64.parse(secret_iv);
    secret_iv = CryptoJS.enc.Utf8.stringify(secret_iv);

    let cipher = crypto.createCipheriv(Algorithm, secret_key, secret_iv);
    let encrypted = cipher.update(data.toString());
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return encrypted.toString('base64');

  } catch (err) {
    console.log('Inside AesEncryption: ', err);
  }
}


const GetAWSSecretData = async ({ SecretId }) => {
  try {
    const SecretManager = new AWS.SecretsManager({
      region: config.AWS_REGION || 'ap-south-1'
    });

    const SecretData = await SecretManager.getSecretValue({ SecretId: SecretId }).promise();
    const Result = JSON.parse(SecretData.SecretString);
    return Result;
  } catch (err) {
    console.log('Inside GetAWSSecretData', err);
    return null;
  }
}

const GetParsedConfigFromCache = async ({ key }) => {
  try {
    let cachedData = cache.get(key);

    if (cachedData) {
      return cachedData;
    }
    else {
      const AWSSecretConfig = await GetAWSSecretData({ SecretId: config.AWS_ENV_CONFIG_SECRET })
      if (AWSSecretConfig) {
        let ParsedConfig = {};

        for (let key in AWSSecretConfig) {
          const IsJson = IsJsonString(AWSSecretConfig[key]);
          if (IsJson) {
            ParsedConfig[key] = JSON.parse(AWSSecretConfig[key]);
          } else {
            ParsedConfig[key] = AWSSecretConfig[key]
          }
        }
        cache.put(key, ParsedConfig, (1 * 60 * 60 * 1000));
        return ParsedConfig;
      } else {
        return null
      }
    }
  } catch (err) {
    console.log("Inside GetParsedConfigFromCache", err)
    return null;
  }
}


const GetParsedConfigLocal = async () => {
  try {
    let data = JSON.parse(process.env.CONNECTION || '{}');
    return data;
  } catch (err) {
    console.log('Inside GetParsedConfigLocal', err);
    return null
  }
}
const InsertDataMongo = async ({ Collection, Database, Data}) => {
let db = null;
  try {
   
    switch (Database) {
      case 'MATRIX_DASHBOARD_DB':
        db = matrixdashboarddb;
        let re1=await db.collection(Collection).updateOne({ UserID: Data.UserID}, {$set: Data}, {upsert: true});
        break;
    
      default:
        break;
    }
    return null;
    
  } catch (err) {
    return err;
  }
}

const GetMongoData = async ({ Collection, Database, Params}) => {
  let db = null;
    try {
      switch (Database) {
        case 'MATRIX_DASHBOARD_DB':
          db = matrixdashboarddb;
          var query={UserID:Params.toString()}
          result = await db.collection(Collection).find(query).project({_id:0}).toArray();
          return result || [];
        
        default:
          break;
      }
      return [];
      
    } catch (err) {
      console.log(err);
      return [];
    }
  }


function IterateChildren(array,UserId){
  for(let i=0;i<array.length;i++)
  {
      if(array[i].UserID==UserId)
      {
           return array[i];
      }
      if(array[i].hasOwnProperty('children'))
      {
          let ans= IterateChildren(array[i].children,UserId);
          if(ans[0]==1)
          {
              continue;
          }
          return ans;
      }
  }
  return [1];
  
  }

  const DeleteMongoHierarchy = async ({ Collection, Database, params}) => {
    let db = null;
      try {
        switch (Database) {
          case 'MATRIX_DASHBOARD_DB':
            db = matrixdashboarddb;
            let option={UserID:{$ne:params}}
            // var query={UserID:Params.toString()}
            await db.collection(Collection).deleteMany(option);
            break;
        
          default:
            break;
        }
        return null;
        
      } catch (err) {
        console.log(err);
        return err;
      }
    }

const GetAgentDetails = async ({ userId, cacheTime }) => {
  try {
    let cachedData = cache.get('RequestUserInfo_' + userId);
    if (cachedData) {
      return cachedData;
    } else {
      const Query = `Select EmployeeId from crm.userdetails where UserID = @UserID and IsActive = 1`;
      let sqlParams = [];
      sqlParams.push({ key: 'UserID', value: userId });

      const result = await sqlHelper.sqlquery('R', Query, sqlParams);
      let data = result?.recordset[0] || {};

      // Fetch productList and processList
      const ProcessData = await SQLdbMethods.GetProcessList(userId);
      const processList = ProcessData?.processList || [];

      // Combine data
      const combinedData = {
        EmployeeId: data.EmployeeId,
        processList: processList
      };

      cache.put('RequestUserInfo_' + userId, combinedData, cacheTime);
      return combinedData;
    }
  } catch (err) {
    console.error("Common GetAgentDetails", err);
    return {};
  }
}

const encrypt= ({message, key, iv}) =>{
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
  let encrypted = cipher.update(message, 'utf-8', 'base64');
  encrypted += cipher.final('base64');
  return encrypted;
    
}

const decrypt= ({message,key,iv})=>{
  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
  let decrypted = decipher.update(message, 'base64', 'utf-8');
  decrypted += decipher.final('utf-8');
  return decrypted;

}

const FindHierarchyToUse= async()=>{
  try{
    let cachedData = cache.get("ManagerHierarchy");
    if(cachedData!==undefined && cachedData!=null){
      return cachedData
    }
    else{
      let result= await FetchMatrixDashboardConfig("ManagerHierarchy");
      if(result!=null && [0,1].includes(result))
      {
        cache.put("ManagerHierarchy",result, 5*60*1000);
        return result;
      }
      else{
        cache.put("ManagerHierarchy",0,5*60*1000);
        return 0;
      }
    }
  }
  catch(err){
    console.log("FindHierarchy error ", err);
    return 0;
  }
}


const FindMyBookingsAPI= async()=>{
  try{
    let cachedData = cache.get("MyBookings");
    if(cachedData!==undefined && cachedData!=null){
      return cachedData
    }
    else{
      let result= await FetchMatrixDashboardConfig("MyBookings");
      if(result!=null && [0,1].includes(result))
      {
        cache.put("MyBookings",result, 5*60*1000);
        return result;
      }
      else{
        cache.put("MyBookings",0,5*60*1000);
        return 0;
      }
    }
  }
  catch(err){
    console.log("MyBookings error ", err);
    return 0;
  }
}

const FetchMatrixDashboardConfig=async (key)=>{
  try{
    let value=null;
    let db = matrixdashboarddb;
    let result = await db.collection("MatrixDashboardConfig").findOne({Key:key});
    // console.log("The result is ", result.Value);
    if(result)
    {
        value= result.Value;
        return value
    }
    else{
      return null
    }
    
  }
  catch(err){
    console.log("Mongo MatrixDashboard Error ",err);
    return null;
  }

}

const InsertMongoLogData= (user, role)=>{
  try{
  let db = matrixdashboarddb;
  let data={
    Key: user ,
    Value: role
  }
  db.collection("ManagerHierarchyLog").insertOne(data,(err, result)=>{
    if(err)
    {
      console.log("The error in ManagerHierarchyLog is ", err);
    }
  });
  return;
  }
  catch(err){
    return;
  }
}

const getLeadIdsSecondaryAgent= async(Data, userId)=>{
  try{
    let bookingMonth= Data?.FromDate ? Data.FromDate : Data.BookingMonth 
    let proc = '[MTX].[GetSecondaryAgentBooking]';
    // console.log(userId,Data);
    let sqlparams = [
      { key: 'AgentID', value: userId },
      { key: 'BookingType', value: Data?.BookingType },
      { key: 'BookingMonth', value: bookingMonth },
      { key: 'ManagerIds', value: Data?.IsSupervisor ? userId.toString() : Data?.ManagerIds },
      { key: 'FilterProduct', value: Data?.FilterProduct? Data?.FilterProduct : 0 },
      { key: 'ProductPresent', value: Data?.ProductPresent },
      { key: 'ProductIds', value: Data?.ProductIds }
    ];
    // console.log("Sql PARAMS ARE ", sqlparams)
    const response = await sqlHelper.sqlProcedure('L', proc, sqlparams);
    return response;
  }
  catch(err){
    console.log("getLeadIdsSecondaryAgent error ", err);
    return 0;
  }
}

const putLogsBmsSecondaryAgentBooking= async(Data, userId)=>{
  try{
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = JSON.stringify(parseInt(userId, 10))
    logData.Method = "BmsSecondaryAgentBooking";
    logData.Application = "MatrixDashboard";
    logData.Channel = "BMS-Matrix-Separation";
    logData.RequestText = JSON.stringify(Data);
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "BMS-Matrix-Separation";
    // console.log(logData);
    let response = await LoggerMethods.LogKafka(logData);
  }
  catch(err){
    console.log("putLogsBmsSecondaryAgentBooking error ", err);
    return 0;
  }
}

const DeleteMongoData = async (Database, Collection, KeyName ,Key) =>{
  let db = null;
      try {
        switch (Database) {
          case 'MATRIX_DASHBOARD_DB':
            db = matrixdashboarddb;
            let option={};
            option[KeyName]= Key;
            await db.collection(Collection).deleteMany(option);
            break;
          default:
            break;
        }
        return null;
        
      } catch (err) {
        console.log("Error in DeleteMongoData: ",err);
        return err;
      }
}

const GetSpecificMongoData= async (Database, Collection,KeyName,Key, ProjectionKey ) =>{
  let db = null;
  try {
    switch (Database) {
      case 'MATRIX_DASHBOARD_DB':
        db = matrixdashboarddb;
        let query={};
        query[KeyName] = Key;
        let projection={};
        projection[ProjectionKey]=1;
        projection['_id']=0;
        
        let result = await db.collection(Collection).find(query).project(projection).toArray();
        // let result = await db.collection(Collection).find(query,projection).toArray();
        // console.log("The result is ", result);
        return result || [];
    
      default:
        break;
    }
    return [];
    
  } catch (err) {
    console.log("Error in GetSpecificMongoData: ",err);
    return [];
  }
}


const FetchMultipleDocuments = async (Database, Collection, KeyName, List, ProjectionList) => {
  let db = null;
  try {
      switch (Database) {
        case 'MATRIX_DASHBOARD_DB':
          db = matrixdashboarddb;
          break;
        
        case 'FOS_DB':
          db= fosdb;
          break;

        default:
          break;
      }
      let query = {};
      if(KeyName)
      {
      query[KeyName] = { $in: List };
      }
      let result = [];
        // console.log("The projection list is ", ProjectionList);
      if (Array.isArray(ProjectionList) && ProjectionList.length > 0) {
          let projection = {};
          projection['_id'] = 0;
          ProjectionList.map((projectionKey) => {
            projection[projectionKey] = 1;
          })
          // console.log("The query is ", query);
          // console.log("The projection is , ", projection);
          result = await db.collection(Collection).find(query).project(projection).toArray();
          return result;
       }
      result = await db.collection(Collection).find(query).toArray();
      if(Array.isArray(result) && result.length>0)
      {
        return result;
      }
      return [];
    }
    

  catch (err) {
    console.log("Error in FetchMultipleDocuments: ",err);
    return [];
  }
}


const PipelineQuery= async (Database, Collection,pipeline, ProjectionList ) =>{
  let db = null;
  try {
    switch (Database) {
      case 'MATRIX_DASHBOARD_DB':
        db = matrixdashboarddb;
        break;
      
      case 'FOS_DB':
        db= fosdb;
        break;
        
      default:
        break;
    }

    let result=[];

    if (Array.isArray(ProjectionList) && ProjectionList.length > 0) {
      let projection = {};
      projection['_id'] = 0;
      ProjectionList.map((projectionKey) => {
        projection[projectionKey] = 1;
      })

      result = await db.collection(Collection).aggregate(pipeline).project(projection).toArray();
      return result;
    }
      result = await db.collection(Collection).aggregate(pipeline).toArray();
      return result;
 
    
  } catch (err) {
    console.log("Error in PipelineQuery: ",err);
    return [];
  }
}

const GetMongoDataMultipleQueryParams = async (Database, Collection, SearchQuery, ProjectionList, latest) => {
  let db = null;
  try {
    switch (Database) {
      case 'MATRIX_DASHBOARD_DB':
        db = matrixdashboarddb;
        break;

      case 'FOS_DB':
        db = fosdb;
        break;

      default:
        break;
    }

    let result = [];

    if (Array.isArray(ProjectionList) && ProjectionList.length > 0) {
      let projection = {};
      projection['_id'] = 0;
      ProjectionList.map((projectionKey) => {
        projection[projectionKey] = 1;
      });
      
      // console.log("The query is ", SearchQuery);
      // console.log("The projection is , ", projection);
      if(latest)
      {
        result = await db.collection(Collection).find(SearchQuery).sort({'_id':-1}).project(projection).toArray();
      }
      else{
      result = await db.collection(Collection).find(SearchQuery).project(projection).toArray();
      }
   
      return result;
    }

    if(latest)
    {
    result = await db.collection(Collection).find(query).sort({'_id':-1}).toArray();
    }
    else{
      result = await db.collection(Collection).find(query).toArray();
    }
    if (Array.isArray(result) && result.length > 0) {
      return result;
    }
    return [];
  } 
  
  catch (err) {
    console.log("Error in GetMongoDataMultipleQueryParams: ",err);
    return [];
  }
}

function isInputSafe(input) {
  const blacklist = [
      /--/,
      /;/,          
      /UNION SELECT/i,  
      /OR '.*'='.*'/i,
      /SELECT.*FROM/i,
      /DROP/i,
      /TRUNCATE/i,
      /EXEC/i,
      /EXECUTE/i,
      /xp_cmdshell/i,
      /sp_executesql/i,
      /INFORMATION_SCHEMA/i,
      /WAITFOR DELAY/i,
      /BENCHMARK/i,
      /SLEEP/i,
      /DELETE/i,        
      /ALTER/i,
      /DECLARE/i,
      /CAST/i,
      /CONVERT/i,
      /sys\.databases/i,
      /sys\.tables/i,
      /sys\.columns/i,
      /sys\.views/i,
      /sys\.schemas/i,
      /sys\.sql_modules/i,
      /sys\.objects/i,
      /sys\.server_principal_credentials/i,
      /master\./i,
      /msdb\./i,
      /sysdatabases/i,
      /sysusers/i,
      /sysobjects/i,
      /syscolumns/i,
      /SHOW DATABASES/i,
      /SHOW TABLES/i,
      /SHOW COLUMNS/i,
      /DESCRIBE/i,
      /DATABASE\(\)/i,
      /sys\.dm_exec_sessions/i,
      /sys\.dm_exec_connections/i,
      /sys\.dm_exec_sql_text/i,
      /sys\.dm_exec_query_stats/i,
      /sys\.dm_os_performance_counters/i,
      /sys\.configurations/i,
      /sys\.server_principals/i,
      /sys\.database_principals/i,
      /sys\.database_files/i,
      /sys\.filegroups/i,
      /sys\.database_permissions/i,
      /sys\.server_permissions/i,
      /sys\.assemblies/i,
      /sys\.certificates/i,
      /sys\.symmetric_keys/i,
      /sys\.asymmetric_keys/i,
      /sys\.credentials/i,
      /sys\.triggers/i,
      /sys\.procedures/i,
      /sys\.linked_servers/i,
      /sys\.messages/i,
      /sys\.traces/i,
      /sys\.configurations/i,
      /BACKUP/i,
      /RESTORE/i,
      /RECONFIGURE/i,
      /SHUTDOWN/i,
      /KILL/i,
      /DBCC/i,
      /BULK INSERT/i,
      /OPENROWSET/i,
      /OPENQUERY/i,
      /xp_reg/i,
      /xp_instance_reg/i,
      /xp_servicecontrol/i,
      /xp_enumerrorlogs/i,
      /xp_loginconfig/i,
      /sp_configure/i,
      /sp_addlogin/i,
      /sp_droplogin/i,
      /sp_adduser/i,
      /sp_dropuser/i,
      /sp_grantdbaccess/i,
      /sp_revokedbaccess/i,
      /sp_addrolemember/i,
      /sp_droprolemember/i,
      /sp_password/i,
      /sp_attach/i,
      /sp_detach/i,
      // Additional patterns to prevent DB details exposure
      /SELECT\s+@@version/i,
      /SELECT\s+SERVERPROPERTY/i,
      /SELECT\s+HOST_NAME/i,
      /SELECT\s+DB_NAME/i,
      /SELECT\s+USER_NAME/i,
      /SELECT\s+CURRENT_USER/i,
      /SELECT\s+SYSTEM_USER/i,
      /SELECT\s+loginame\s+FROM\s+master\.dbo\.sysprocesses/i,
      /SELECT\s+\*\s+FROM\s+fn_my_permissions/i,
      /SELECT\s+\*\s+FROM\s+fn_db_version/i,
      /SELECT\s+sql\s+FROM\s+syscomments/i,
      /SELECT\s+\*\s+FROM\s+sys\.dm_server_registry/i,
      /xp_dirtree/i,
      /xp_fixeddrives/i,
      /xp_availablemedia/i,
      /xp_subdirs/i,
      /xp_filelist/i,
      /xp_getnetname/i
  ];
  
  return !blacklist.some(pattern => pattern.test(input));
}

function isValidQuery(query, type) {

  const patterns = {
    select: [/\bINSERT\b/i, /\bUPDATE\b/i, /\bDELETE\b/i, /\bDROP\b/i],
    insert: [/\bSELECT\b/i, /\bUPDATE\b/i, /\bDELETE\b/i, /\bDROP\b/i],
    update: [/\bSELECT\b/i, /\bINSERT\b/i, /\bDELETE\b/i, /\bDROP\b/i]
  };

  const blacklistedPatterns = patterns[type] || [];

  const hasInvalidStatements = blacklistedPatterns.some(pattern => pattern.test(query));

  if (hasInvalidStatements) {
    return false;
  }

  const additionalStatement = query.trim().toUpperCase().slice(6).match(new RegExp(`\\b${type.toUpperCase()}\\b`, 'i'));

  return !additionalStatement;
}

const ValidateUserForUpsertAccess =async (userId, endpoint)=>{
  try{


  if(!userId || !endpoint)
  {
      return false;
  }
  else{

      let cachedValue = cache.get(userId+endpoint);
      if(cachedValue!==null)
      {
        return cachedValue;
      }
      
      let sqlparams = [
          { key: 'UserId', value: userId },
          { key: 'Entity', value: endpoint}
        ]
      const response = await sqlHelper.sqlProcedure('R', '[CRM].[UserDataAccess]', sqlparams);
      // console.log("The response is ", response);
      let resp  = (Array.isArray(response.recordset) && response.recordset.length > 0  && response.recordset[0] ) || {} ;

      if(resp.access)
      {
          cache.put(userId+endpoint,true,3*60*1000);
          return true;
      }

      // if([2,11,12].includes(role.roleid))
      // {
      //     return true;
      // }
      cache.put(userId+endpoint,false, 3*60*1000);
      return false;
  }
  }
  catch(e)
  {
      console.log("Error in ValidateUserForUpsertAccess ",e);
      return false;
  }

}


const APIListCollection=async (apiPath,method)=>{
  try{

    let db=matrixdashboarddb;


    if(['/api/health.html','/api/v1/Fos/FOSRealTimeStatus','/api/v1/Fos/FOSCallingStatus','/api/v1/Fos/FetchAgentLatLongByTime'].includes(apiPath))
    {
      return
    }
    // let splitApiPath  = apiPath.split('/');

    // let endPoint = splitApiPath[splitApiPath.length-1];

    // let breakPoint  = endPoint.indexOf('&');
    // let endPointWithoutQueryString = endPoint.slice(0,breakPoint>0? breakPoint : endPoint.length);

    // let key = splitApiPath[splitApiPath.length-2]+'/'+ endPointWithoutQueryString;

    // breakPoint = apiPath.indexOf("&");

    // let apiPathToPush = apiPath.slice(0, breakPoint>0 ? breakPoint: apiPath.length);

    
    let Collection = "MatrixDashboardAPILog"; 
    // console.log( { "EndPoint": key },
    //   { $setOnInsert: { "EndPoint" : key, "API": apiPath, "Method" : method  } },
    //   { upsert: true } );
    if(db)
    {
    await db.collection(Collection).insertOne(
      { "API": apiPath, "Method" : method  } 
    );
  }
  return
  }
  catch(e)
  {
    console.log("Error occurred in adding API to the collection ",e);
    return
  }

}

const getSecondaryAgentDetails= async(BookingId)=>{
  try{

    if(!BookingId)
    {
      return null
    }
    
    let sqlparam = [];
    sqlparam.push({ key: "LeadID", value: BookingId });

    let query = 'select UD.UserName, UD.UserID, UD.EmployeeId, SULC.LeadID, SULC.Category from MTX.SecondaryUserLeadCredit as SULC inner join CRM.UserDetails as UD on SULC.SecondaryUserID = UD.UserID  where SULC.LeadID=@LeadID'

    let response = await sqlHelper.sqlquery("R", query, sqlparam);
    return response;
  }
  catch(err){
    console.log("getLeadIdsSecondaryAgent error ", err);
    return 0;
  }
}

const FosRTTUntrackabilityTime= async()=>{
  try{
    let cachedData = cache.get("UntrackabilityTime");
    if(cachedData!==undefined && cachedData!=null){
      return cachedData
    }
    else{
      let result= await FetchMatrixDashboardConfig("UntrackabilityTime");
      if(result!=null)
      {
        cache.put("UntrackabilityTime",parseInt(result), 5*60*1000);
        return parseInt(result);
      }
      else{
        cache.put("UntrackabilityTime",15,5*60*1000);
        return 10000;
      }
    }
  }
  catch(err){
    console.log("MatrixDashboardConfig Untrackability error ", err);
    return 10000;
  }
}

const FetchMatrixDashboardConfigAndCache=async (key, cacheDuration)=>{
  try{
    let cachedData = cache.get(key);
    if(cachedData!==undefined && cachedData!=null){
      return cachedData
    }

    let value=null;
    let db = matrixdashboarddb;
    let result = await db.collection("MatrixDashboardConfig").findOne({Key:key});
   
    if(result)
    {
        value= result.Value;
        cache.put(key, value, cacheDuration);
        return value
    }
    else{
      return null
    }
  }
  catch(err){
    console.log("Mongo MatrixDashboard Error ",err);
    return null;
  }
}


function CheckIfColIsValid(cols) {
 
  // console.log("IsCalled ", cols);
  if (!Array.isArray(cols)) {
    return false;
  }

  let validColumns = global.sqlCols;
  
 
  return !cols.some(col => {
    
    if (!col) {
      return true;
    }
    return !validColumns.has(col.toLowerCase());
  });
}

module.exports = {
  AesEncryption: AesEncryption,
  GetAWSSecretData: GetAWSSecretData,
  GetParsedConfigFromCache: GetParsedConfigFromCache,
  GetDataFromCache: GetDataFromCache,
  GetParsedConfigLocal: GetParsedConfigLocal,
  InsertDataMongo:InsertDataMongo,
  GetMongoData:GetMongoData,
  IterateChildren:IterateChildren,
  DeleteMongoHierarchy:DeleteMongoHierarchy,
  GetAgentDetails: GetAgentDetails,
  encrypt: encrypt,
  decrypt: decrypt,
  FindHierarchyToUse: FindHierarchyToUse,
  InsertMongoLogData: InsertMongoLogData,
  getLeadIdsSecondaryAgent: getLeadIdsSecondaryAgent,
  putLogsBmsSecondaryAgentBooking: putLogsBmsSecondaryAgentBooking,
  DeleteMongoData: DeleteMongoData,
  GetSpecificMongoData: GetSpecificMongoData,
  FetchMultipleDocuments: FetchMultipleDocuments,
  PipelineQuery: PipelineQuery,
  GetMongoDataMultipleQueryParams:GetMongoDataMultipleQueryParams,
  FindMyBookingsAPI: FindMyBookingsAPI,
  isInputSafe: isInputSafe,
  isValidQuery: isValidQuery,
  ValidateUserForUpsertAccess: ValidateUserForUpsertAccess,
  APIListCollection : APIListCollection,
  getSecondaryAgentDetails : getSecondaryAgentDetails,
  FosRTTUntrackabilityTime: FosRTTUntrackabilityTime,
  CheckIfColIsValid: CheckIfColIsValid,
  FetchMatrixDashboardConfigAndCache:FetchMatrixDashboardConfigAndCache
}
