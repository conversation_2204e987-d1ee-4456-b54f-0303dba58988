import React, { useState, useEffect } from 'react';
import {
  GetCommonspData
} from "../../store/actions/CommonAction";
import { connect } from 'react-redux';
import { Form } from 'react-bootstrap';
const RadioItems = (props) => {
  // State to store questions and options
  const [questionsWithOptions, setQuestionsWithOptions] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});
  useEffect(() => {
    props.GetCommonspData({
      root: props.root,
      c: "R",
    }, function (data) {
      if (data && data.data && data.data.data) {
        let rawData = data.data.data[0]
        const questionsMap = new Map();

        // Transform the raw data into the desired structure
        rawData.forEach(({ QuestionId, QuestionText, OptionId, OptionText }) => {
          if (!questionsMap.has(QuestionId)) {
            questionsMap.set(QuestionId, { QuestionId, QuestionText, Options: [] });
          }

          const question = questionsMap.get(QuestionId);
          question.Options.push({ OptionId, OptionText });
        });
        const formattedQuestions = [...questionsMap.values()];

        setQuestionsWithOptions(formattedQuestions);
      }
    })

  }, []);

  const handleRadioOptionsChange = (questionId, value) => {
    setSelectedOptions(prevOptions => ({
      ...prevOptions,
      [questionId]: value,
    }));
    if(props.setRadioOptionValue){
      props.setRadioOptionValue(questionId, value)
    }
  };


  const handleQuestionClick = (event) => {
    if (props.onDivClick) {
      props.onDivClick(event);
    }
  };

  return (
    <div>
      {/* Map through questions and options to render */}
      {questionsWithOptions.map((question, index) => {
        const { QuestionId, QuestionText, Options } = question;
        return (<div className={`divquestion_${QuestionId}`} onClick={handleQuestionClick}>
          <h4>{QuestionText}</h4>
          <ul>
            {/* Map through options to render radio buttons */}
            {Options.map(({ OptionId, OptionText }) => (
              <>
                <li>
                  <Form.Check
                    inline
                    label={OptionText}
                    name={`question_${QuestionId}`}
                    type="radio"
                    value={OptionId}
                    onChange={() => handleRadioOptionsChange(QuestionId, OptionId)}
                    checked={(props.checkedItems && Object.keys(props.checkedItems).length > 0) ? (props.checkedItems[QuestionId]==OptionId):(selectedOptions && selectedOptions[QuestionId] === OptionId) }
                    disabled={(props.view || [0, 1].includes(props.status))}
                    //disabled={((!props.view) && !props.viewOnly && !([0, 1].includes(props.status)))}
                 />
                  
                </li>

              </>
            ))}
          </ul>
        </div>
        );
      })

      }
    </div>
  );
};

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData,
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonspData
  }
)(RadioItems);