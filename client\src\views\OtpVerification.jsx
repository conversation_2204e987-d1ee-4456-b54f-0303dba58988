import React from "react";
import {
    CustAgentVerifyOTP, GetAgentCallDetails, GetAgentCallLogs
} from "../store/actions/CommonAction";
import { getuser, getCookie } from '../utility/utility.jsx';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// import AppendScript from './Common/AppendScript';
import OtpDetails from './Common/OtpVerification/OtpDetails';
import Verification from './Common/OtpVerification/Verification';
import CallLogs from './Common/OtpVerification/CallLogs';
import OtpEnterVerification from './Common/OtpVerification/OtpEnterVerification';
import { hhmmss } from '../utility/utility.jsx';


// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    Row,
    Col
} from "reactstrap";
import { Modal } from 'react-bootstrap';

class OtpVerification extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            root: "",
            PageTitle: "",       
            addClass: '',
            UserId: '',       
            ManagerId: '',
            ResponseData:'',
            LogoImg: '/Logo_pb.svg',                  
            lineImg:'/line1.svg',
            linetwoImg:'/line2.svg',
            processOneImg:'/1.svg',
            processTwoImg:'/2.svg',
            processThreeImg:'/3.svg',
            processFourImg:'/4.svg',
            processFiveImg:'/5.svg',
            enterotpImg:'/enterotp.png',
            ProceedValueChanged: false,
            InitialValue: true,
            SubmitValueChanged: false,
            otp: '',
            UserInfo: '',
            IsSuccess: '',
            Error: false,
            knowMore: false,
            howItWorks: false,
            CallLogs: false,
            CentralLoginSuccess:[],
            isCookieExists: false,
            step: 1,
            loginCookie: '',
            AgentCallDetails: [],
            CustomerId: '',
        };
       

    }

    componentDidMount() {
        this.UserList();
        // var element = document.getElementsByClassName('wrapper');
        // if(element.length > 0){
        // element[0].className = "wrapper otpLayoutSection";
        // }
        var body = document.body;

        body.classList.add("otpLayoutSection");
        // AppendScript("/js/Modal.js");
        document.addEventListener("centralLogin", () => { // arrow function
          
                this.onLoginSuccess(); // now you can use this
           
        });
        var pbcentrallogin = getCookie("pbcentrallogin");
        
        if (pbcentrallogin != "") {
            this.setState({ isCookieExists : true, loginCookie: pbcentrallogin});
        }

    }


    componentWillMount(){
        document.title = "Policybazaar Adviser Call History Verification | How Advisor Verification works";    
        var meta = document.createElement('meta');
        meta.name = "description";
        meta.content = "Check your call history with us at any time and ensure you have been speaking with a genuine Policybazaar advisor.";
        document.getElementsByTagName('head')[0].appendChild(meta);
    }

    UserList() {
        const user = getuser();
        var managerid = user.UserID;
        this.setState({ManagerId : managerid});
    }

    APICallFunction(){
        if(this.state.IsSuccess == true){
            this.setState({ step:2, Error:false})
        }else{
            this.setState({ step : 4,  Error:true})
        }
    }


       onClickSubmit = (otp) =>{debugger;
        var json ={
            "OTP": otp,
            "EmployeeId": 1,
            "MobileNo": 1
          }
           CustAgentVerifyOTP(json, function (results) {   
            this.setState({ UserInfo: results.data, IsSuccess : results.data.IsSuccess}, () =>
            this.APICallFunction());

        }.bind(this));

    }

    handleChange = otp => this.setState({ otp });

    knowmore() {       
        this.setState({ knowMore: true });
    }
    howitworks() {       
        this.setState({ howItWorks: true });
    }

    onLoginSuccess(){debugger;
        var pbcentrallogin = getCookie("pbcentrallogin");
        this.setEventUserLoggedIn(pbcentrallogin);
     }

    checkCallHistory= (loginCookie) =>{debugger;
        console.log('loginCookie',loginCookie);

        if(this.state.isCookieExists){
        this.eventOnClick();
        }
       
        var json ={
            "pbcentrallogin": loginCookie,         
        }
        GetAgentCallLogs(json, function (results) {   
            this.setState({ AgentCallDetails: results.data.objAgentCallDetails, CustomerId: results.data.CustomerId, step : 3});

        }.bind(this));   

       

    }

    async setEventUserLoggedIn(pbcentrallogin){
        let elem = document.getElementById('elem');  
        await this.checkCallHistory(pbcentrallogin);

        if(elem){
            let event = new CustomEvent("onUserLoggedIn", {detail: { CustomerId: this.state.CustomerId },bubbles: true}); // (2)
            elem.dispatchEvent(event);
            }


    }

    eventOnClick= () =>{
    let elem = document.getElementById('elem');
        if(elem){
        let event = new CustomEvent("onClickCheckCallLog", {bubbles: true}); // (2)
        elem.dispatchEvent(event);
        }
    }
    openOtpVerify= () =>{
        this.setState({ step : 4});
    }

    prevStep = (stepcount) => {
        const { step } = this.state
        this.setState({
            step: stepcount
        })
    }

    renderSwitch(step) {
        const { Error, UserInfo } = this.state;

        switch (step) {
            case 1:
              
                return  <OtpDetails
                knowmore= {this.knowmore.bind(this)}
                howitworks= {this.howitworks.bind(this)}
                isCookieExists= {this.state.isCookieExists}
                loginCookie= {this.state.loginCookie}
                checkCallHistory= {this.checkCallHistory}
                eventOnClick= {this.eventOnClick}
                openOtpVerify= {this.openOtpVerify}                   
                />
                
                
            case 2:
                
                    return   <Verification
                    verifyImg= {this.state.verifyImg}
                    bgImg= {this.state.bgImg}
                    UserInfo= {UserInfo}
                    prevStep={this.prevStep}
                    />
            case 3:
                return <CallLogs
                AgentCallDetails= {this.state.AgentCallDetails}
                hhmmss= {hhmmss}
                prevStep={this.prevStep}
                CustomerId={this.state.CustomerId}
                />

                case 4:
                    return <OtpEnterVerification   
                    Error= {Error} 
                    otp= {this.state.otp}
                    onClickSubmit= {this.onClickSubmit}
                    handleChange= {this.handleChange}
                    prevStep= {this.prevStep}
                    isCookieExists= {this.state.isCookieExists}
                    loginCookie= {this.state.loginCookie}
                    checkCallHistory= {this.checkCallHistory}
                    knowmore= {this.knowmore.bind(this)}
                    howitworks= {this.howitworks.bind(this)}
                    />
        }
    }


    render() {
        const { step } = this.state;

        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Card className="otp-layout"> 
                            <CardHeader>                             
                            <a href="https://www.policybazaar.com/"><img src={this.state.LogoImg}/></a>                            
                            </CardHeader>
                                <CardBody>
                                {this.renderSwitch(step)}    
                                </CardBody>
                               
                            </Card>
                    <div> 
                    {this.state.CentralLoginSuccess.IsSuccess}
                    </div>            
                    <Modal show={this.state.knowMore} onHide={() => this.setState({ knowMore: false })} dialogClassName="modal-50w howItWorkPoupup  modal-dialog-centered">
                    <Modal.Header closeButton>
                        <Modal.Title>About Advisor Verification</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                    <h3 className="title">We want you to be safe</h3>
                          <p className="aboutAdvisor">Policybazaar is India’s largest insurance web aggregator with over 100 million registered customers. But some fraudsters have started misusing Policybazaar’s name to mislead customers. <br/> <br/>Through this service, you can instantly check your call history with us at anytime <b>  “or”</b> verify whether the call is from policy bazaar or a fraud by asking the advisor to share the six digit verification code that you can enter and authenticate his/her identity</p>
                    </Modal.Body>
                    
                    </Modal>

                    <Modal show={this.state.howItWorks} onHide={() => this.setState({ howItWorks: false })} dialogClassName="modal-50w modal-dialog-centered howItWorkPoupup">
                    <Modal.Header closeButton>
                        <Modal.Title>How Advisor Verification works</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                    {/* <h3 className="title">How to check call history</h3> */}
                    <Row> 
                                <Col md={4} xs={4}>   
                                <img src={this.state.processOneImg}/>   
                                </Col>
                                <Col md={8} xs={8}>   
                                    <p className="processOne">Sign in with your Registered Mobile Number</p>
                                    <div>
                                    <img src={this.state.lineImg}/> 
                                    </div>
                                </Col>
                            </Row>   
                            <Row> 
                            <Col md={8} xs={8}>   
                            <p className="processOne Two">After Signing in, you can see details of <b> Last 5 calls </b> with Policybazaar Advisors</p>                                      
                                </Col>
                                <Col md={4} xs={4}>   
                                <img src={this.state.processTwoImg}/>   
                                </Col>                                    
                            </Row> 
                    {/* <h3 className="title">How to verify agent</h3>
                            <Row> 
                                <Col md={4} xs={4}>   
                                <img src={this.state.processThreeImg}/>   
                                </Col>
                                <Col md={8} xs={8}>   
                                    <p className="processOne">Ask your advisor to share verification code</p>
                                    <div>
                                    <img src={this.state.lineImg}/> 
                                    </div>
                                </Col>
                            </Row>   
                            <Row> 
                            <Col md={8} xs={8}>   
                            <p className="processOne Two">Enter the six digit PIN and press <b>“Verify now“</b></p>                                      
                                </Col>
                                <Col md={4} xs={4}>   
                                <img src={this.state.processFourImg}/>   
                                </Col>                                    
                            </Row> 
                            <Row> 
                                <Col md={4} xs={4}>   
                                <img className="mt-3" src={this.state.processFiveImg}/>   
                                </Col>
                                <Col md={8} xs={8}>  
                                <img src={this.state.linetwoImg}/>  
                                <p className="processOne">If the caller is from Policybazaar, their name & employee code will be displayed</p>
                                    <div>                             
                                    </div>
                                </Col>
                            </Row> 
                   */}
                    </Modal.Body>
            
                    </Modal>                    
                        
                 </div>
            </>
        );
    }
}



export default OtpVerification;