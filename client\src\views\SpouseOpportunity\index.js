/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
import ManagerHierarchy from "views/Common/ManagerHierarchy";
import config from "../../config";
import { getuser } from "utility/utility";
import axios from "axios";
import { ValidateAddLeadToPriorityQueue } from "store/actions/CommonAction";
import moment from "moment";
import { LeadContentView } from "utility/utility";
import * as XLSX from 'xlsx';


// styling components
import { Button, Card, CardBody, CardHeader, CardTitle, Col, Form, Row } from "react-bootstrap";
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer, toast } from "react-toastify";
import DataTable from "react-data-table-component";
import { getAgentId } from "utility/utility";


const SpouseOpportunity = () => {
	const [ supervisorIds, setSupervisorIds ] = useState([]);
	const [rows, setRows] = useState([]);
    const [selectedMonth, setSelectedMonth] = useState(moment().format('MM-YYYY'));
    const [error, setError] = useState(false);

	const RoleId = getuser().RoleId;

	const column = [
		{
			name: 'Sno',
			cell: (row, index) => index + 1,
			width: "60px!important"
		},
		{
			name: "BookingID",
			selector: row => row.BookingID,
			cell: row => <div>{row.BookingID ?  <Button variant="link" className="clickBtn" onClick={() => window.open(LeadContentView(row.CustomerId, row.BookingID, row.ProductID))}>{row.BookingID}</Button> : ""}</div>
		},
		{
			name: "Husband name",
			selector: row => row.CustomerName,
			cell: row => row.CustomerName ? row.CustomerName : "",
		},
		{
			name: "Booking Date",
			selector: row =>  row.BookingDate,
			cell: row => row.BookingDate ? moment(row.BookingDate).format('DD/MM/YYYY h:mm:ss a') : ""
		},
		{
			name: "Status",
			selector: row => row.Status,
			cell: row => row.Status ? row.Status : 'N.A'
		},
		{
			name: "Spouse Working",
			selector: row => row.IsSpouseWorking,
			cell : row => row.IsSpouseWorking ? "Yes" : "No"
		},
		{
			name: "IsPitched",
			selector: row => row.IsHWPitched,
			cell: row => <input type="checkbox"
							id="IsHWPitched"
							checked={row.IsHWPitched ? true : false}
							onChange={(e) => updateData(e, row.BookingID)}
							disabled = {RoleId !== 13}
						/>
		},
		{
			name: "IsBooked",
			selector: row => row.IsSpousePlanBooked,
			cell: row => <input type="checkbox"
							id="IsSpousePlanBooked"
							checked={row.IsSpousePlanBooked ? true : false}
							onChange={(e) => updateData(e, row.BookingID)}
							disabled = {RoleId !== 13}
						/>
		},
		{
			name: "Add To Queue",
			omit: RoleId !== 13,
			cell: row => <div>
					<button 
						onClick={() => { AddLeadToQueue(row) }} 
						className="addLeadQueueBtn"
						disabled={RoleId !== 13}
					>
						Add To Queue
					</button>
				</div>
		},
		{
			name: "AgentName",
			omit: RoleId === 13,
			cell: row => <div>{row.AgentName}<br/>{row.EmployeeId}</div>
		}
	  ];  

	useEffect(() => {
        setError(false);
        setSelectedMonth(moment().format('MM-YYYY'));

		const userId = getAgentId() || 0;

		const agentId = RoleId === 13 ? userId : 0;	
		const sprVisorIds = supervisorIds.length > 0 ? supervisorIds.join() : (userId)?.toString();
	
		const url = `${config.api.MatrixCore}api/SalesView/GetHWEligibleData?AgentId=${agentId}&SupervisorIds=${sprVisorIds}`
		const headers = {
			"AgentId": userId,
			"Source": "matrix",
            "Content-Type": "application/json"
		}

		axios.get(url, {headers, withCredentials : true}).then(response => {
			if (response.status === 200) {
				setRows(response.data);
				setError(false);
			} else {
				setRows([]);
				setError(true);
			}
		})
		.catch(() => {
			setRows([]);
			setError(true);
		})
    }, [supervisorIds]);


    const updateData = (e, BookingID) => {

		const url = config.api.MatrixCore + "api/WebSiteService/PushHWEligibleData"
		const headers = {
			"AgentId": getAgentId(),
			"Source": "matrix",
            "Content-Type": "application/json"
		}
		const reqData = { 
			"LeadId": BookingID, 
			[e.target.id]: e.target.checked 
		}

		axios.post(url, reqData, {headers, withCredentials: true}).then(res => {
			if(res){
				toast("Record Updated Successfully.", { type: 'success' });
            } else {
				toast("Something went wrong.", { type: 'error' });
            }
		})
		.catch(() => {
			toast("Something went wrong.", { type: 'error' });
		})

        setRows(
            rows.map((row) => {
                if (row.BookingID === BookingID) {
                    row[e.target.id] = e.target.checked ? 1 : 0;
                }
                return row;
            })
        );
    }

    const AddLeadToQueue = (row) => {

        var priorityLead = {
            "LeadId" : row.BookingID,
            "Name" : row.CustomerName,
            "CustomerId" : row.CustomerId,
            "UserID" : parseInt(getAgentId()),
            "Priority" : 1,
            "ProductId" : row.ProductID,
            "Reason" : 'Spouse Opportunity Lead',
            "ReasonId" : 73,
            "CallStatus" : "",
            "IsAddLeadtoQueue" : 1,
            "IsNeedToValidate" : 1
        }

		var reqData = {
			"UserId": parseInt(getAgentId()),
			"Leads": [priorityLead]
		};

        ValidateAddLeadToPriorityQueue(reqData,function (resultData)
        {
			try {
				if (resultData != null) {
					if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
						toast("Lead (" + row.BookingID + ") Added successfully", { type: 'success' });
					} 
					else {
						let error = (resultData && resultData.data.data.message && resultData.data.data.message !== '')
                        ? (row.BookingID + " : " + resultData.data.data.message)
                        : "Error while adding lead: " + row.BookingID;
						toast(`${error}`, { type: 'error' });
					}
				}
			} catch(err){
				toast(`${err}`, { type: 'error' });
			}
        })
	}

	const handleShow = (e) => {
		setSupervisorIds(e.SelectedSupervisors);
	};

	const getLastThreeMonths = () => {
        const months = [];
        for (let i = 0; i < 3; i++) {
            const date = moment().subtract(i, 'month');
			months.push(<option key={date.format('MM-YYYY')} value={date.format('MM-YYYY')}>{date.format('MMM YYYY')}</option>)
        }
		months.push(<option key='All' value='All'>All</option>)
        return months;
    };

	const handleExport = (data) => {

        const newArray = Array.isArray(data) && data.map(obj => {
			const { BookingID, CustomerName, BookingDate, Status, IsSpouseWorking, IsHWPitched, IsSpousePlanBooked, AgentName, EmployeeId} = obj;
            return { BookingID, CustomerName, BookingDate: BookingDate ? moment(BookingDate).format('DD/MM/YYYY h:mm:ss a') : "", Status, IsSpouseWorking, IsHWPitched, IsSpousePlanBooked, AgentName, EmployeeId };
        });
        if (Array.isArray(newArray) && newArray.length > 0) {
            const sheet = XLSX.utils.json_to_sheet(newArray);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
            XLSX.writeFile(workbook, 'SpouseOpportunity.xlsx');
        }
    };

	const filterdata = () => {
        if (selectedMonth === "All") {
            return rows;
        }
    
        const [selectedMonthIndex, selectedYear] = selectedMonth.split('-').map(Number);
    
        const startMonth = new Date(selectedYear, selectedMonthIndex - 1, 1);
        const endMonth = new Date(selectedYear, selectedMonthIndex, 1);
    
        return rows.filter(row => {
            const bookingDate = new Date(row.BookingDate);
            return bookingDate >= startMonth && bookingDate < endMonth;
        });
    };

	const data = filterdata();
	const monthFilter = getLastThreeMonths();

	return (
		<div className="content">
			<ToastContainer />
			<Row>
				<Col md="12">
					<Card className="spouseOpp">
						<CardHeader>
							<Row>
								<Col md={9}>
									<CardTitle tag="h4">Spouse Opportunity</CardTitle>
								</Col>
								<Col md={2}>
									<Form.Group>
										<Form.Label>Select Booking Month</Form.Label>
										<Form.Select
											as="select"
											value={selectedMonth} 
											name="month"
											onChange={(e) => setSelectedMonth(e.target.value)}
										>
											{monthFilter}
										</Form.Select>
									</Form.Group>
								</Col>
								<Col md={1}>
									{RoleId !== 13 ? <ManagerHierarchy handleShow={handleShow} value={/EmployeeId/g} ></ManagerHierarchy> : null}
								</Col>
							</Row>
							<Row>
								<Col md={12}>
									{error ? <h3 className="nodataFound">Something went wrong</h3> : null}
								</Col>
							</Row>
						</CardHeader>
						<CardBody>
							{RoleId !== 13 && Array.isArray(data) && data.length > 0 ? <span onClick={()=> handleExport(data)} className="downloadExcel"></span> : ''}
							<DataTable
								columns={column}
								data={data}
								pagination={false}
								striped={true}
								noHeader={true}
								highlightOnHover
							/>
						</CardBody>
					</Card>
				</Col>
			</Row>
		</div>
	);

};

export default SpouseOpportunity;