
const sqlHelper = require("../../LibsPBIncentive/sqlHelper");
const Utility = require("../../LibsPBIncentive/Utility");
var _ = require('underscore');

exports.ContestMapping = async function (req, res) {
    try {
        
        let results = [];
        let data = req.body;
        if (data) {
            for (let index = 0; index < data.length; index++) {
                //console.log( "StartDate",new Date());
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
                sqlparam.push({ key: "UserId", value:data[index].UserId  });
                sqlparam.push({key: "ContestId", value: data[index].ContestId});
                sqlparam.push({key:"Target", value: data[index].Target});
                sqlparam.push({key:"CreatedBy", value:data[index].CreatedBy});
                sqlparam.push({key:"Visits",value:data[index].Visits });
                sqlparam.push({key:"Ape", value:data[index].Ape});
                let result = await sqlHelper.sqlProcedure("L", "[ENC].[InsertContestAgentMapping]", sqlparam);

                results.push(result.recordset[0]);
                // console.log(result.recordset[0]);
                
            }
 
               await res.send({
                    status: 200,
                    data: results
                });
           
        }
        
    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}

