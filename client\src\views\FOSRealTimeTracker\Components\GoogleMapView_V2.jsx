import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Marker, InfoWindow, Polyline } from '@react-google-maps/api';
import moment from 'moment';
import { AppointmentsContext } from '../AppointmentsContext';
import AppointmentCard from './AppointmentCard';
import InfoWindowCard from './InfoWindowCard';


const options = {
    // strokeColor: '#FF0000',
    strokeColor: '#FF0000',
    strokeOpacity: 0,
    icons: [
        {
            icon: {
                path: 'M 0,-1 0,1',
                strokeOpacity: 1,
                scale: 2,
            },
            offset: '0',
            repeat: '10px',
        },
    ],
};

const options2 = {
    strokeColor: '#008000',
    // strokeColor: '#808080',
    strokeOpacity: 0,
    icons: [
        {
            icon: {
                path: 'M 0,-1 0,1',
                strokeOpacity: 1,
                scale: 2,
            },
            offset: '0',
            repeat: '10px',
        },
    ],
};


function getRandomNumber(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

const GoogleMapView_V2 = React.memo(({
    latLong,
    currentAppointment,
    nonServingLocations, showSingleAgentData,
    currentLocation,
    appointmentHistory,
    chosenAgent,
    centerBaseOn,
    baseLocation,
    setInitialCenterLatLong,
    showDifferentSlot,
    nonCurrentSlot
}) => {

    // if(localStorage.getItem('GoogleMapView'))
    // {
    //     localStorage.setItem('GoogleMapView', parseInt(localStorage.getItem('GoogleMapView'))+1);
    // }
    // else{
    //     localStorage.setItem('GoogleMapView',1);
    // }

    // console.log("Google Map View");
    const [selectedMarker, setSelectedMarker] = useState(null);

    const [polylineCurrent, setPolylineCurrent] = useState({
        current: null,
        destination: null
    })

    const [infoData, setInfoData] = useState([]);

    const [infoWindow, setInfoWindow] = useState(null);

    const handleMarkerClick = (positionItem) => {
        setSelectedMarker(positionItem);
    };

    const GenerateInfoText = (data) => {
        let text = '';


        switch (data['subStatusId']) {
            case 2193:
                text = 'Started Journey at ' + moment(data.insertedAt).format('hh:mm:ss A');
            case 2194:
                text = 'Reached Customer Location at ' + moment(data.insertedAt).format('hh:mm:ss A');
            case 2124:
                text = 'Started Appointment on at ' + moment(data.insertedAt).format('hh:mm:ss A');
            case 2003:
                text = 'Meeting Over at ' + moment(data.insertedAt).format('hh:mm:ss A');
        }
        return <>{data.LeadId}<h6>{text}</h6></>

    }

    const FindInitials = (username) => {



        try {
            const words = username.split(' ');



            if (words.length === 1) {
                return words[0]; // Return the entire first word
            } else {


                let initials = '';

                words.forEach(word => {
                    if (word.length > 0) {
                        initials += word[0];
                    }
                });
                return initials.toUpperCase();
            }


        }
        catch (e) {
            // console.log("The exception is ", e)
        }
    }


    useEffect(() => {


        setPolylineCurrent(
            {
                current: null,
                destination: null
            }
        )

        if (Array.isArray(appointmentHistory) && appointmentHistory.length > 0) {
            if (currentLocation && currentLocation.RealTimeStatusId == 1) {
                appointmentHistory.map((appointment) => {
                    if (appointment.subStatusId == 2193) {
                        setPolylineCurrent({
                            current: {
                                lat: currentLocation.Lat,
                                lng: currentLocation.Long
                            },
                            destination: {
                                lat: appointment.Lat,
                                lng: appointment.Long
                            }
                        })
                    }
                })
            }
            if (currentLocation && currentLocation.RealTimeStatusId == 2) {
                let sjCoords = null;
                let ejCoords = null;
                appointmentHistory.map((appointment) => {
                    if (appointment.subStatusId == 2193) {
                        sjCoords = { lat: appointment.Lat, lng: appointment.Long }
                    }
                    else if (appointment.subStatusId == 2194) {
                        ejCoords = { lat: appointment.Lat, lng: appointment.Long }
                    }
                })
                if (sjCoords && ejCoords) {

                    setPolylineCurrent({
                        current: sjCoords,
                        destination: ejCoords
                    })
                }
            }
            if (currentLocation && currentLocation.RealTimeStatusId == 6) {
                let sjCoords = null;
                let ejCoords = null;
                appointmentHistory.map((appointment) => {
                    if (appointment.subStatusId == 2193) {
                        sjCoords = { lat: appointment.Lat, lng: appointment.Long }
                    }
                    else if (appointment.subStatusId == 2194) {
                        ejCoords = { lat: appointment.Lat, lng: appointment.Long }
                    }
                })

                if (sjCoords && ejCoords) {

                    setPolylineCurrent({
                        current: sjCoords,
                        destination: ejCoords
                    })
                }
            }
        }
        // FindCentre();

    }, [appointmentHistory, currentLocation])


    useEffect(() => {

        FindCentre();

    }, [centerBaseOn])


    const findIconImage = (StatusId) => {
        switch (StatusId) {
            case 1:
                return '/Images/current-travelling.png'

            case 2:
                return '/Images/meeting-appointment.png'

            case 3:
                return '/Images/calling-appointment.png'

            case 4:
                return '/Images/idle-agent.png'

            case 5:
                return '/Images/loggedoff-appointment.png'

            case 6:
                return '/Images/idle-agent.png'

            case 2193:
                return '/Images/start-travelling.png'

            case 2194:
                return '/Images/stop-travelling.png'

            case 2124:
                return '/Images/meeting-appointment.png'

            case 2003:
                return '/Images/start-appointment.png'
        }
    }


    const FindClass = (StatusId) => {
        if ([2194, 2003, 2004].includes(StatusId)) {
            return 'DoneCXLocation'
        }
        return 'CXLocation';
    }

    const FindCentre = () => {
        if (showSingleAgentData) {


            if (currentLocation) {
                const { Lat: currentLat, Long: currentLng } = currentLocation;

                // const hasCurrentLocation = currentLocation !== undefined;



                const hasAppointmentHistory = Array.isArray(appointmentHistory) && appointmentHistory.length > 0;
                const app = centerBaseOn && hasAppointmentHistory ? appointmentHistory.find(item => item.subStatusId === centerBaseOn) : null;

                // let centreChangeOn = centerBaseOn && ([2193,2194,2024].includes(centerBaseOn) ? 'subStatus' : 'nonServingLocation') || null;


                // let app = null;

                // let coords = {
                //     lat: null,
                //     lng: null
                // };

                // switch(centreChangeOn){
                //     case 'subStatus':
                //         app =  hasAppointmentHistory ? appointmentHistory.find(item => item.subStatusId === centerBaseOn) : null ;
                //         coords.lat = app ? app.Lat : currentLat;
                //         coords.lng = app ? app.Long : currentLng;
                //         break;
                //     case 'nonServingLocation':
                //         app = nonServingLocations ? nonServingLocations.find((app)=> app.LeadId === centerBaseOn) : null;
                //         coords.lat = app ? app.CxLat : currentLat;
                //         coords.lng = app ? app.CxLong : currentLng;
                //         break;
                //     default:
                //         break;
                // }

                // const app = centerBaseOn== hasAppointmentHistory ? appointmentHistory.find(item => item.subStatusId === centerBaseOn) : null

                // app = Array.isArray(nonServingLocations) && nonServingLocations.length>0 ? nonServingLocations[getRandomNumber(0, nonServingLocations.length-1)] : null;

                // // console.log("The app is ", app)

                // coords.lat = app ? app.CxLat : currentLat;
                // coords.lng = app ? app.CxLong : currentLng;    


                // setInitialCenterLatLong(
                // coords
                // //     {
                // // lat: app ? app.Lat : currentLat,
                // // lng: app ? app.Long : currentLng
                // //     }
                // );
                setInitialCenterLatLong({
                    lat: app ? app.Lat : currentLat,
                    lng: app ? app.Long : currentLng
                });

            }


            // if (currentLocation ) {

            //     setInitialCenterLatLong({
            //         lat: currentLocation.Lat,
            //         lng: currentLocation.Long
            //     })

            // }
            else {

                if (chosenAgent) {
                    let lat = null;
                    let long = null;
                    let agentData = latLong.find(item => parseInt(item.AgentId) === chosenAgent)

                    if (agentData) {
                        lat = agentData.Lat;
                        long = agentData.Long;
                    }

                    if (lat && long) {
                        setInitialCenterLatLong(
                            {
                                lat: lat,
                                lng: long
                            }
                        )

                    }

                }
            }



        }

    }

    const OpenInfoWindow = useCallback((e,data) => {

        // console.log("InfoWindow data currentLocation ", data);
        setInfoWindow(null);
        // console.log("InfoWindow Data is ", [
        //     {
        //         LeadId: data?.LeadId,
        //         CustomerName: data?.CustomerName,
        //         DateTime: null,
        //         Current: true
        //     }
        // ])
        setInfoData([
            {
                LeadId: data?.LeadId,
                CustomerName: data?.CustomerName,
                DateTime: null,
                Current: true
            }
        ])

        setInfoWindow(data?.LeadId);
    }, [currentLocation]);

    const CloseInfoWindow = useCallback(() => {

        setInfoWindow(null);

    }, []);

    const CustomMarkerAgent = ({ data, prop }) => {

        if (prop == 1) {
            // console.log("History is plottinG, ", data);

            return (
                <>
                    <Marker
                        position={{ lat: data?.Lat, lng: data?.Long }}
                        icon={{
                            url: findIconImage(data?.subStatusId),
                            scaledSize: new window.google.maps.Size(30, 30),
                            labelOrigin: new window.google.maps.Point(18, -10),
                        }}

                    >

                    </Marker>
                </>
            )
        }
        else if (prop == 2) {


            return (
                <>

                    <Marker
                        //   onClick={() => handleMarkerClick(positionItem)}
                        label={{
                            text: FindInitials(data?.UserName || ''),
                            className: 'AgLocationlabel'
                        }}

                        position={{ lat: data.Lat, lng: data.Long }}
                        icon={{
                            url: findIconImage(data.RealTimeStatusId),
                            scaledSize: new window.google.maps.Size(30, 30),
                            labelOrigin: new window.google.maps.Point(-5, 15),
                        }}

                    >
                    </Marker>
                </>
            )
        }
        else if (prop == 3) {
  
            if (data.IsServingApp) {
                return (
                    <AppointmentsContext.Provider value={{ appointmentCard: infoData, currentStatus: currentLocation?.RealTimeStatusId }}>
                        <Marker
                            label={
                                {
                                    text: data?.CustomerName ? data.CustomerName : "NA",
                                    className: 'cxLocationlabel',
                                }
                            }
                            onClick={(e)=>{OpenInfoWindow(e,data)}}
                            // onMouseOver={(e)=>{OpenInfoWindow(e,data)}}
                            position={{ lat: data.CxLat, lng: data.CxLong }}

                        >
                            {
                                data.LeadId==infoWindow &&
                                <>
                                    <InfoWindow
                                        style={{}}
                                        onCloseClick={CloseInfoWindow}
                                        position={{ lat: data.CxLat, lng: data.CxLong}}
                                    >
                                        <InfoWindowCard handleClose={CloseInfoWindow} serving={true} />
                                    </InfoWindow>
                                    <style>{`
                                    .gm-style-iw-ch{
                                    display: none !important
                                    }
                                    .gm-ui-hover-effect {
                                      display: none !important;
                                    }
                                  `}</style>
                                </>
                            }
                        </Marker>
                    </AppointmentsContext.Provider>
                )
            }
            else {

                return (
                    <AppointmentsContext.Provider value={{ appointmentCard: infoData, currentStatus: currentLocation?.RealTimeStatusId }}>
                        <Marker
                            label={
                                {
                                    text: data?.CustomerName ? data.CustomerName : "NA",
                                    className: FindClass(data?.AppStatus),
                                }
                            }
                            onClick={(e)=>{OpenInfoWindow(e,data)}}
                            // onMouseOver={(e)=>{OpenInfoWindow(e,data)}}
                            position={{ lat: data.CxLat, lng: data.CxLong }}
                        >
                          {
                                 data.LeadId==infoWindow &&
                                <>
                                    <InfoWindow
                                        style={{}}
                                        onCloseClick={CloseInfoWindow}
                                        position={{ lat: data.CxLat, lng: data.CxLong }}
                                    >
                                        <InfoWindowCard handleClose={CloseInfoWindow} serving={false}  />
                                    </InfoWindow>
                                    <style>{`
                                    .gm-style-iw-ch{
                                    display: none !important
                                    }
                                    .gm-ui-hover-effect {
                                      display: none !important;
                                    }
                                  `}</style>
                                </>
                            }

                        </Marker>
                    </AppointmentsContext.Provider>
                )
            }
        }
        else if (prop == 4) {
            // console.log("CurrentLocationprop", data)

            return (
                <>
                    <Marker

                        position={{ lat: data.Lat, lng: data.Long }}
                        // label={{
                        //     text: FindInitials(data?.UserName || 'Advisor'),
                        //     className: 'AgLocationlabel'
                        // }}
                        icon={{
                            url: '/Images/current-location.png',
                            scaledSize: new window.google.maps.Size(30, 30),
                            labelOrigin: new window.google.maps.Point(-5, 15),
                        }}

                    >

                    </Marker>
                </>
            )
        }
        else if (prop == 5) {

            let lat = null;
            let long = null;


            const agentData = latLong.find(item => parseInt(item.AgentId) === chosenAgent);

            if (agentData) {
                lat = agentData.Lat;
                long = agentData.Long;
            }




            if (agentData && agentData.RealTimeStatusId && lat && long) {


                return (
                    <>
                        <Marker

                            label={{
                                text: FindInitials(agentData?.UserName || ''),
                                className: 'AgLocationlabel'
                            }}

                            position={{ lat: lat, lng: long }}
                            icon={{
                                url: findIconImage(agentData.RealTimeStatusId),
                                scaledSize: new window.google.maps.Size(30, 30),
                                labelOrigin: new window.google.maps.Point(-5, 15),
                            }}>
                        </Marker>
                    </>
                )


            }
            else {
                return null;
            }


        }

        else if (prop == 6) {

            return (
                <>
                    <Marker

                        position={{ lat: parseFloat(data.lat), lng: parseFloat(data.lng) }}
                        icon={{
                            url: '/Images/homelocation.png',
                            scaledSize: new window.google.maps.Size(30, 30),
                            labelOrigin: new window.google.maps.Point(-5, 15),
                        }}
                    >
                    </Marker>
                </>
            )

        }


    };

    return (

        <>

            {
                showSingleAgentData ?
                    <>
                        {
                            !showDifferentSlot ?
                                <>

                                    {/* {console.log("Non Serving Locations are ", nonServingLocations)} */}

                                    {
                                        Array.isArray(nonServingLocations) && nonServingLocations.length > 0 &&
                                        nonServingLocations.map((location, index) => {
                                            return (
                                                <CustomMarkerAgent
                                                    data={location}
                                                    prop={3}
                                                />
                                            )
                                        })

                                    }

                                    {/* {console.log("The appointment history is ", appointmentHistory)} */}

                                    {
                                        currentLocation &&
                                        Array.isArray(appointmentHistory) && appointmentHistory.length > 0 &&
                                        appointmentHistory.map((subStatusData, index) => {

                                            if (currentLocation.RealTimeStatusId == 1 && [2193].includes(subStatusData.subStatusId)) {
                                               
                                                return (
                                                    <CustomMarkerAgent
                                                        data={subStatusData}
                                                        prop={1}
                                                    />
                                                )
                                            }
                                            if (currentLocation.RealTimeStatusId == 2 && [2193, 2194, 2124].includes(subStatusData.subStatusId)) {
                                                
                                                return (
                                                    <CustomMarkerAgent
                                                        data={subStatusData}
                                                        prop={1}
                                                    />
                                                )
                                            }

                                            if (currentLocation.RealTimeStatusId == 6 && [2193, 2194].includes(subStatusData.subStatusId)) {
                                                
                                                return (
                                                    <CustomMarkerAgent
                                                        data={subStatusData}
                                                        prop={1}
                                                    />
                                                )

                                            }
                                        })
                                    }

                                    {/* {console.log("The current Appointment is ", currentAppointment, currentLocation)} */}
                                    {
                                        currentLocation &&
                                        // ![5].includes(currentLocation.RealTimeStatusId) &&
                                        <>
                                            {
                                                currentAppointment &&
                                                <CustomMarkerAgent
                                                    data={currentAppointment}
                                                    prop={3}
                                                />
                                            }
                                            <CustomMarkerAgent
                                                data={currentLocation}
                                                prop={4}
                                            />
                                        </>
                                    }

                                    {/* {
                                currentLocation &&  ![5].includes(currentLocation.RealTimeStatusId) &&
                                <CustomMarkerAgent
                                    data={currentLocation}
                                    prop={4}
                                />
                            } */}
                                    {
                                        currentLocation && currentAppointment && [1, 2, 6].includes(currentLocation.RealTimeStatusId)

                                        &&
                                        <>
                                            {/* {                                
                                <>  */}
                                            {/* {console.log("It is drawwing a polyline ")} */}
                                            <Polyline
                                                path={[{ lat: currentLocation.Lat, lng: currentLocation.Long }, { lat: currentAppointment.CxLat, lng: currentAppointment.CxLong }]}
                                                options={options}
                                            />
                                            {/* </>
                                } */}
                                        </>
                                    }
                                    {/* {console.log("Current ", currentLocation)}
                            {console.log("Appoitnm,ent ", appointmentHistory)}p
                            {console.log("Polylinecurrent is ", polylineCurrent)} */}
                                    {
                                        polylineCurrent.current && polylineCurrent.destination && currentLocation && [1, 2, 6].includes(currentLocation.RealTimeStatusId)
                                        &&
                                        <>
                                            <Polyline
                                                path={[polylineCurrent.current, polylineCurrent.destination]}
                                                options={options2}
                                            />
                                        </>

                                    }
                                    {/* {console.log("CurrentLocation in 538 ", currentLocation)} */}
                                    {
                                        !currentLocation &&
                                        chosenAgent &&

                                        <CustomMarkerAgent
                                            data={chosenAgent}
                                            prop={5}
                                        />

                                    }
                                    {
                                        baseLocation?.lat && baseLocation.lng &&
                                        <CustomMarkerAgent
                                            data={baseLocation}
                                            prop={6}
                                        />
                                    }

                                </>
                                :
                                <>
                                    {
                                        Array.isArray(nonCurrentSlot) && nonCurrentSlot.length > 0 &&
                                        nonCurrentSlot.map((location, index) => {
                                            // console.log("The nonCurrentSlots are ", location);
                                            return (
                                                <CustomMarkerAgent
                                                    data={location}
                                                    prop={3}
                                                />
                                            )
                                        })

                                    }
                                    {
                                        !currentLocation &&
                                        chosenAgent &&

                                        <CustomMarkerAgent
                                            data={chosenAgent}
                                            prop={5}
                                        />

                                    }
                                </>
                        }
                    </>
                    :

                    <>
                        {/* {console.log("sjhbshjd ", latLong)} */}
                        {
                            Array.isArray(latLong) && latLong.length > 0 &&
                            latLong.map((agent, index) => {
                                return (
                                    <CustomMarkerAgent
                                        data={agent}
                                        prop={2}
                                    />
                                )
                            })
                        }
                    </>


            }


        </>
    )

})

export default GoogleMapView_V2;
