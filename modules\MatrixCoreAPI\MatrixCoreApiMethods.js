const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");

exports.UpdateFOSCityAssignment = async function (req, res) {
    const url = conf.MATRIXCOREAPI + "/fos/api/FOS/UpdateFosCity"
    const { AgentId, Data } = req.body

    const headers = {
        "AgentId": AgentId,
        "Source": "dashboard",
        "Content-Type": "application/json",
        "authkey": conf.FOSAUTHKEY,
        "clientkey": conf.FOSCLIENTKEY

    }
    // console.log("UpdateFOSCityAssignment", url);

    let response = await axios.post(url, Data, { headers: headers });
    // console.log("UpdateFOSCityAssignment", response);
    try {

        res.send(response.data);

    }
    catch (e) {
        console.log("Error in MatrixCoreApiMethods: ", e);
        res.send({
            status: 500,
            message: "Error"
        });
    }
}

exports.RejectLead = async function (req, res) {
    const url = conf.MATRIXCOREAPI + "/coremrs/api/LeadDetails/RejectLeads"
    const Data = req.body;
    const headers = {
        "AgentId": req.user.userId,
        "Source": "matrix",
        "Content-Type": "application/json",
        "authkey": conf.FOSAUTHKEY,
        "clientkey": conf.FOSCLIENTKEY
    }
    try {
        let response = await axios.post(url, Data, { headers: headers });
        res.status(200).json({
            data: response.status == 200 ? response.data : null
        })
    }
    catch (e) {
        res.send({
            Data: false,
            ErrorCode: 0,
            Message: "Error"
        });
    }
}

exports.CreateLead = async function (req, res) {
    const url = conf.MATRIXCOREAPI + "/api/WebSiteService/CreateLead"
    const Data = req.body;
    const headers = {
        "AgentId": req.user.userId,
        "Source": "dashboard",
        "Content-Type": "application/json",
        "authkey": conf.FOSAUTHKEY,
        "clientkey": conf.FOSCLIENTKEY
    }
    try {
        let response = await axios.post(url, Data, { headers: headers });
        res.status(200).json({
            data: response.status == 200 ? response.data : null
        })
    }
    catch (e) {
        res.send({
            IsLeadCreated: false,
            LeadId: 0,
            CustId: 0,
            Message: "Error"
        });
    }
}

exports.GetCouponRaiseRequest = async function (req, res) {
    const url = conf.MATRIXCOREAPI + "/api/WebsiteService/GetCouponRaiseRequest";
    let request = req.url;
    let parts = request.split('?UserID=');

    if (parts.length > 1) {
        let AgentID = parts[1];
        const headers = {
            "AgentId": AgentID,
            "Content-Type": "application/json",
            "source": "dashboard",
            "authKey": conf.FOSAUTHKEY,
            "clientKey": conf.FOSCLIENTKEY
        }

        try {
            await axios.get(url, { headers: headers }).then(response => {
                // console.log(response);
                try {
                    if (response.status == 200) {
                        res.send({
                            status: 200,
                            data: response.data
                        });
                    }
                    else {
                        res.send({
                            status: 500,
                            data: response
                        });
                    }
                }
                catch (e) {
                    res.send({
                        status: 500,
                        message: "error"
                    });
                }

            });
        }
        catch (e) {
            console.log("Error in GetCouponRaiseRequest: ", e);
            res.send({
                status: 500,
                message: "error"
            });
        }
    }


}
exports.FetchAgentPredefinedUrl = async function (req, res) {

    let request = req.url;
    let parts = request.split('?RequestId=');

    if (parts.length > 1) {
        let RequestId = parts[1];
        const headers = {
            "Content-Type": "application/json",
            "source": "dashboard",
            "authKey": conf.FOSAUTHKEY,
            "clientKey": conf.FOSCLIENTKEY
        }
        const url = conf.MATRIXCOREAPI + "/coremrs/api/ProposalService/FetchAgentPredefinedUrl?RequestId=" + RequestId;

        try {
            await axios.get(url, { headers: headers }).then(response => {
                // console.log(response);
                try {
                    if (response.status == 200) {
                        res.send({
                            status: 200,
                            data: response.data
                        });
                    }
                    else {
                        res.send({
                            status: 500,
                            data: response
                        });
                    }
                }
                catch (e) {
                    res.send({
                        status: 500,
                        message: "error"

                    });
                }

            });
        }
        catch (e) {
            console.log("Error in GetCouponRaiseRequest: ", e);
            res.send({
                status: 500,
                message: "error"
            });
        }
    }


}

exports.UpdateCouponRaiseRequest = async function (request, res) {
    const url = conf.MATRIXCOREAPI + "/api/WebsiteService/UpdateCouponRaiseRequest"
    let body = request.body;
    body['ReleasedBy'] = request.user?.userId || 123;
    const headers = {
        "Content-Type": "application/json",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY
    }
    try {
        axios.post(url, body, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: "error"
                });
            }

        });
    }
    catch (e) {
        console.log("Error in UpdateCouponRaiseRequest: ", e);
        res.send({
            status: 500,
            message: "error"
        });
    }
}
exports.SendDuplicateOtp = async function (request, res) {
    const url = conf.MATRIXCOREAPI + "/api/SalesView/LeadVerifyOtp"
    let body = request.body;
    const headers = {
        "Content-Type": "application/json",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY,
        "AgentId": request.user?.userId || 0
    }
    try {
        axios.post(url, body, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: "error"
                });
            }

        });
    }
    catch (e) {
        console.log("Error in SendDuplicateOtp: ", e);
        res.send({
            status: 500,
            message: "error"
        });
    }
}
exports.VerifyDuplicateOtp = async function (request, res) {
    const url = conf.MATRIXCOREAPI + "/api/SalesView/LeadVerifyOtp"
    let body = request.body;
    const headers = {
        "Content-Type": "application/json",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY,
        "AgentId": request.user?.userId || 0
    }
    try {
        axios.post(url, body, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: "error"
                });
            }

        });
    }
    catch (e) {
        console.log("Error in VerifyDuplicateOtp: ", e);
        res.send({
            status: 500,
            message: "error"
        });
    }
}
exports.GetCouponDataByLeadId = async function (req, res) {
    let request = req.url;
    let parts = request.split('?LeadId=');

    if (parts.length > 1) {
        let LeadId = parts[1];
        const headers = {
            "Content-Type": "application/json",
            "source": "dashboard",
            "authKey": conf.FOSAUTHKEY,
            "clientKey": conf.FOSCLIENTKEY
        }
        const url = conf.MATRIXCOREAPI + "/api/WebsiteService/GetCouponDataByLeadId/" + LeadId;

        try {
            await axios.get(url, { headers: headers }).then(response => {
                // console.log(response);
                try {
                    if (response.status == 200) {
                        res.send({
                            status: 200,
                            data: response.data
                        });
                    }
                    else {
                        res.send({
                            status: 500,
                            data: response
                        });
                    }
                    // console.log(response.data);
                }
                catch (e) {
                    res.send({
                        status: 500,
                        message: e
                    });
                }

            });
        }
        catch (e) {
            console.log("Error in GetCouponDataByLeadId: ", e);
            res.send({
                status: 500,
                message: e
            });
        }
    }
}

exports.GetLeadOnlyURL = async function (req, res) {
    let parts = req.url.split('?Leadid=');
    let UserId = req.user?.userId || 0;
    const url = conf.MATRIXCOREAPI + `/coremrs/api/LeadDetails/GetLeadOnlyURL/${parts[1]}/LeadOnly`;

    if (parts.length > 1) {
        const headers = {
            "AgentId": UserId,
            "Content-Type": "application/json",
            "source": "matrix",
            "authKey": conf.FOSAUTHKEY,
            "clientKey": conf.FOSCLIENTKEY
        }

        try {
            await axios.get(url, { headers: headers }).then(response => {
                // console.log(response);
                try {
                    if (response.status == 200) {
                        res.send({
                            status: 200,
                            data: response.data
                        });
                    }
                    else {
                        res.send({
                            status: 500,
                            data: response
                        });
                    }
                }
                catch (e) {
                    res.send({
                        status: 500,
                        message: "error"
                    });
                }

            });
        }
        catch (e) {
            console.log("Error in GetLeadOnlyURL: ", e);
            res.send({
                status: 500,
                message: "error"
            });
        }
    }
}

exports.UploadFileToS3BucketPvt = async function (req, res) {
    const headers = {
        "Content-Type": "multipart/form-data",
        "source": "dashboard",
        "authKey": conf.FOSAUTHKEY,
        "clientKey": conf.FOSCLIENTKEY
    }
    let url = conf.MATRIXCOREAPI + "/api/UploadFile/UploadFileToS3BucketPvt";

    let formData = new FormData();

    Object.keys(req.body).map((key) => {
        formData.append(key, req.body[key]);
    })

    let fileStream = fs.createReadStream(req.files);
    formData.append("UploadedFile", fileStream)

    try {
        axios.post(url, formData, { headers: headers }).then(response => {
            // console.log(response);
            try {
                if (response.status == 200) {
                    res.send({
                        status: 200,
                        data: response.data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {
                res.send({
                    status: 500,
                    message: "error"
                });
            }

        });
    }
    catch (e) {
        console.log("Error in UploadFileToS3BucketPvt: ", e);
        res.send({
            status: 500,
            message: "error"
        });
    }

}