
.innerDetails-box {
    position: absolute;
    bottom: 0px;
    z-index: 0;
    width: 93.5%;
    margin: 0;
    left: 0;
    right: 0;
    padding: 24px;
   
    // height: 500px;

    // @media (max-width:1280px) {
    //     bottom: 80px;
    // }

    .MuiAccordion-root{
        background: #fff;
        border-radius: 8px !important;
        border: 2px solid #ffffff66;
        margin: 0 !important;
        padding: 10px 16px;
        z-index: -1;
        box-shadow: none;
        animation: slideBottom 400ms ease-in-out;
        -webkit-animation: slideBottom 400ms ease-in-out;
        -moz-animation: slideBottom 400ms ease-in-out;
        
        // &.Mui-expanded{
        //     height: 245px;
        // }

        &.Mui-expanded.idle{
            height: 210px;

        }

        @keyframes slideBottom {
            0%{
                transform: translateY(100%);
                -webkit-transform: translateY(100%);
                -moz-transform: translateY(100%);
            }

            100%{
                transform: translateY(0%);
                -webkit-transform: translateY(0%);
                -moz-transform: translateY(0%);
            }
        }

        &::before{
            display: none;
        }

        .MuiAccordionSummary-root{
            padding: 0;
            // pointer-events: none;
            min-height: inherit;
            width: 100%;
          

            .MuiAccordionSummary-content{
                margin: 0;

                .MuiTypography-root{
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;

                    h5{
                        font-size: 12px;
                        color: #25385899;
                        font-weight: 500;
                        line-height: 20px;
                        text-transform: capitalize;
            
                        strong{
                            color: #0065FF;
                            // text-decoration: underline;
                            // font-size: 12px;
                            text-transform: none;
                        }
                    }
            
                    h3{
                        margin: 0;
                        font-size: 14px;

                        strong{
                            color: #0065FF;
                            // text-decoration: underline;
                            text-transform: none;
                        }
                    }
                }
            }
        }
        .MuiCollapse-root{
            width: 100%;
        }
    }

    //progress-bar
    .progress-bar-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0;
        cursor: default;
       
      
      .progress-bar {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        justify-content: space-between;
        gap: 4px;
        flex: 1;
      
            .progress-step {
                display: flex;
                align-items: flex-start;
                position: relative;
                flex-direction: column-reverse;
                gap: 12px;
                width: 100%;
                margin-top: 20px;

                &:last-child .progress-line{
                    background: transparent;
                    border-radius: 0;
                }

                span.time {
                    position: absolute;
                    top: -22px;
                    z-index: 111;
                    color: #253858E3;
                    font-size: 12px;
                    font-weight: 700;
                }

                .step-label {
                    margin: 0;
                    font-weight: 500;
                    font-size: 13px;
                    white-space: normal;
                    text-align: left;
                    color: #25385861;
                    line-height: normal;
                    cursor: default;

                    // .underline{
                    //     cursor: pointer;
                    //     text-decoration: underline;
                    // }


                }

                .underline{
                    cursor: pointer;
                    text-decoration: underline;
                }
                
                .progress-line {
                    width: 150px;
                    height: 4px;
                    background: #D9D9D9;
                    margin: 5px 0px 6px 19px;
                    border-radius: 4px;
                    transition: background-color 0.3s;

                    &::before {
                        content: "";
                        background: #D9D9D9;
                        position: absolute;
                        left: 0px;
                        top: 0;
                        width: 15px;
                        height: 15px;
                        border-radius: 50%;
                    }
                }
                
                &.completed {

                    .step-label {
                        color: #253858e3;
                    }

                    .progress-line{
                        background: #29B706;

                        &::before {
                            background: #29B706;
                        }
                    }


                }


                // .progress-line-end {
                //     width: -webkit-fill-available;
                //     height: 4px;
                //     background: #D9D9D9;
                //     margin: 5px 0px 6px 19px;
                //     border-radius: 4px;
                //     transition: background-color 0.3s;
                //     position: relative;

                //     &::before {
                //         content: "";
                //         background: #D9D9D9;
                //         position: absolute;
                //         left: 0px;
                //         top: 0;
                //         width: 15px;
                //         height: 15px;
                //         border-radius: 50%;
                //     }
                // }
                
                // &.completed {

                //     .step-label {
                //         color: #253858e3;
                //     }

                //     .progress-line{
                //         background: #29B706;

                //         &::before {
                //             background: #29B706;
                //         }
                //     }

                // }
                // &.endappointment {
                //     .progress-line {
                //       background: none; /* Remove background for progress line */
                //     }
                
                //     .progress-line-end {
                //       background: none; /* Remove background for progress line end */
                //     }
                //   }

            }  
        }  
    }    

    // .innerDetails-box .progress-bar-container .progress-bar .progress-step:last-child .progress-line {
    //     background: transparent !important;
    //     border-radius: 0 !important;
    // }

    //appointment-details
    .appointment-details {
        border-top: 1px dashed #0000001F;
        margin-top: 16px;

       ul{
        padding: 16px 0px 0px;
        margin: 0;
        border: 0;
        justify-content: space-between;
         
        li{
            background: transparent;
            border-radius: 0;
            font-size: 13px;
            box-shadow: none;
            padding: 0;
            max-width: inherit;
            font-weight: 500;
            
          label{
            color: #25385899;
            font-size: 12px;
            font-weight: 600;
            display: block;
            margin: 0px 0px 2px;
          }

        }

       }


    }

    //advisor
    .advisor-idle{
        // gap: 56px;
        text-align: center;

        p{
            margin: 4px 0px 0px;
            font-size: 12px;
            color: #25385899;
            font-weight: 400;
        }
        .advisor-details{
            flex: 1;
        }
    }

    //meeting
    .MuiAccordion-root.meeting {

        .progress-step.completed{

            .progress-line{
                background: #AF5F14;

                &::before {
                    background: #AF5F14;
                }
            }

        }

    }
    

    .ryt-content{
        display: flex;
        gap: 12px;
        flex: 1;
        justify-content: flex-end;

        button{
            display: flex;
            gap: 8px;
            padding: 3px 10px;
            justify-content: center;
            align-items: center;
            border: 0;
            border-radius: 16px;
            background: #F5F5F5;
            font-size: 12px;
            color: #253858E3;
            font-weight: 600;
            line-height: normal;
            outline: none !important;
            box-shadow: none !important;
            pointer-events: all;

            &.live-icon{
                background: #29B706;
                color: #fff;
                padding: 5px 13px;
                pointer-events: none;
                cursor: pointer;
                font-weight: 500;
            }

            &.out-bounds{
                font-weight: 500;
                padding: 5px 24px;
            }


        }
    }


}    


.fullDetailview .innerDetails-box 
{
    width: 70%;
    margin: auto;
    bottom: 14px;
    // left: 40px;
    // right: 0;
} 