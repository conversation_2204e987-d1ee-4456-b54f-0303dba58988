import { useEffect, useRef, useState } from "react";
import { Form } from "react-bootstrap";
import { connect } from 'react-redux';
import { toast, ToastContainer } from 'react-toastify';
import { Col, Row } from "reactstrap";
// import "slick-carousel/slick/slick-theme.css";
// import "slick-carousel/slick/slick.css";
import { GetCommonApiData, GetCommonspData, GetCommonspDataV2, GetDuplicateLeadsData, GetIsAllowedDuplicateSearch, GetDuplicateLeadsDataByProductId, PostCommunicationData, LeadOnlyView, IsSocialMediaAgent } from "../../store/actions/CommonAction";
import { GetCommonData } from "../../store/actions/CommonMongoAction";
import { getuser, getUserDetails, IsMobile, LeadContentView, OpenNewSalesView, LeadView } from "../../utility/utility";
import NavbarMobileView from "./NavBarMobileView";
import ValidateBookedLeads from "./ValidateBookedLeads";
import OpenWebUrlModal from "./OpenWebUrlModal";
import Pagination from "./Pagination";
import ShimmerLoader from "./ShimmerLoader";
import SideBarMobileView from "./SideBarMobileView";
import TableDataFormat from "./TableDataFormat";
import { ALL, BmsDocPointUrl, DateRangeFilter, EMAILID_SEARCHED_TEXT, FilterSearchedTextDuplicateLeads, GetBmsUrlStatus, GetFirstDateOfMonth, GetSalesViewurl, GetVerificationStatusUrl, InsertVerificationStatus, ISADMIN, LEADID_SEARCHED_TEXT, MOBILE_SEARCHED_TEXT, ModifyType, NavBarButtonsSlider, PerformActionsOnLinkClick, SECONDARY, TableColumnsDuplicateLeads,TableColumnsSearchLeads } from "./Utility";
import config from "../../config";
import { getCookie } from "../../utility/utility";

const DuplicateLeads = (props) => {

  let DateValue = GetFirstDateOfMonth();

  const [MySpanBookings, setMySpanBookings] = useState([]);
  const [MySpanBookingsClone, setMySpanBookingsClone] = useState([]);
  const [AllBookingsCount, setAllBookingsCount] = useState(0);
  const [BookingsMap, setBookingsMap] = useState([]);
  const [BadgeCounter, setBadgeCounter] = useState([]);
  const [AllBookingsCountSecondary, setAllBookingsCountSecondary] = useState(0);
  const [BookingsMapSecondary, setBookingsMapSecondary] = useState([]);
  const [BadgeCounterSecondary, setBadgeCounterSecondary] = useState([]);
  const [SearchText, setSearchText] = useState("");
  const [DebouncedSearchText, setDebouncedSearchText] = useState("");
  const [ActiveButtonIds, setActiveButtonIds] = useState([ALL]);
  const [CurrentPage, setCurrentPage] = useState(1);
  const [RowsPerPage, setRowsPerPage] = useState(10);
  const [itemOffset, setItemOffset] = useState(0);
  const [BookingMonth, setBookingMonth] = useState(DateValue);
  const [IsLoading, setIsLoading] = useState(false);
  const [ActiveTabParent, setActiveTabParent] = useState('Primary');
  const [SelectedPage, setSelectedPage] = useState(0);
  const [CurrentRow, setCurrentRow] = useState("");
  const [OpenWebUrlModalShow, setOpenWebUrlModalShow] = useState(false);
  const [ToggleSearchDate, setToggleSearchDate] = useState(false);
  const [FilterProductId, setFilterProductId] = useState(null);
  const [ProductDetails, setProductDetails] = useState("");
  const [ProductsList, setProductsList] = useState([]);
  const [OpenedLead, setOpenedLead] = useState(null);
  const [MobileSearchDescription, setSearchDescription] = useState(MOBILE_SEARCHED_TEXT);
  const [LeadIdSearchDescription, setLeadIdSearchDescription] = useState(LEADID_SEARCHED_TEXT);
  const [EmailIdSearchDescription, setEmailIdSearchDescription] = useState(EMAILID_SEARCHED_TEXT);
  const [WebUrl, setWebUrl] = useState(null);
  const [WebUrlTitle, setWebUrlTitle] = useState("");
  const [AgentID, setAgentID] = useState(null);
  const [AgentInfo, setAgentInfo] = useState(null);
  const [IsMobileDevice, setIsMobileDevice] = useState(null);
  const IsInitialMount = useRef(true);
  const [SearchByDetails, setSearchByDetails] = useState("");
  const [SearchByList, setSearchByList] = useState([]);
  const [AllDuplicateLeads, setAllDuplicateLeads] = useState([]);
  const [CurrentError, setCurrentError] = useState("");
  const [FilterText, setFilterText] = useState("");
  const [MyDuplicateLeadsClone, setMyDuplicateLeadsClone] = useState([]);
  const [IsSearched, setIsSearched] = useState(false);
  const pagesVisited = SelectedPage * RowsPerPage;
  const [UserInfo, setUserInfo] = useState({});
  const [CustomeIdOuptut,setCustomeIdOuptut] = useState(0);
  const [IsNRICust,setIsNRICust] = useState(0);
  const [SearchAllowedAgent, setSearchAllowedAgent] = useState(0);
  const [UserRoleId,setUserRoleId] = useState(0);

  const [IsSmeUser, setIsSmeUser] = useState(false);
  const [IsSmeAgent, setIsSmeAgent] = useState(false);
  const RoleId = 13;
  const endOffset = itemOffset + RowsPerPage;
  let currentItems = AllDuplicateLeads.slice(itemOffset, endOffset);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'desc' });
  const [sortedItems, setSortedItems] = useState(currentItems);
  const [IsBOokedLead, setIsBOokedLead] = useState(false);
  const [IsFosSMEAgent, setIsFosSMEAgent] = useState(false);
  const [IsSocialMediaUser, setIsSocialMediaUser] = useState(false);

  const pageCount = Math.ceil(AllDuplicateLeads.length / RowsPerPage);
  let SERIAL = itemOffset + 1;

  // Calculating display range
  const startRecord = pagesVisited + 1;
  const endRecord = Math.min(pagesVisited + RowsPerPage, AllDuplicateLeads.length);
  const totalRecords = AllDuplicateLeads.length;

  const handleSearchByChange = (e) => {
    const SearchByInfo = JSON.parse(e.target.value);
    setSearchByDetails(e.target.value);
  }

  const handleBookingMonthMobile = (month) => {
    setBookingMonth(month);
  }

  const handlePageClick = (event) => {
    setSelectedPage(parseInt(event.selected));
    const newOffset = (event.selected * RowsPerPage) % AllDuplicateLeads.length;
    setItemOffset(newOffset);
  };

  const filterBookings = ({ activeIds }) => {
    let bookings = [];
    setSelectedPage(1)

    if (activeIds.includes(ALL)) {
      setActiveTabParent('Primary')
      for (let key in BookingsMap) {
        bookings = [...bookings, ...BookingsMap[key]];
      }
    } else if (activeIds.includes(SECONDARY)) {
      setActiveTabParent('Secondary')
      for (let key in BookingsMapSecondary) {
        bookings = [...bookings, ...BookingsMapSecondary[key]];
      }
    } else {
      if (ActiveTabParent === 'Primary') {
        for (let key in BookingsMap) {
          const arr = JSON.parse(key);
          const id = activeIds && activeIds.length > 0 && activeIds[0] || 0;
          if (arr.includes(parseInt(id))) {
            bookings = [...bookings, ...BookingsMap[key]];
          }
        }
      } else {
        for (let key in BookingsMapSecondary) {
          const arr = JSON.parse(key);
          const id = activeIds && activeIds.length > 0 && activeIds[0] || 0;
          if (arr.includes(parseInt(id))) {
            bookings = [...bookings, ...BookingsMapSecondary[key]];
          }
        }
      }
    }
    bookings = bookings.sort((a, b) => a.Id - b.Id);
    setMySpanBookings(bookings);
    setMySpanBookingsClone(bookings);
  }

  const toggleNavButtons = (id, description) => {
    const index = ActiveButtonIds.indexOf(id);
    setSelectedPage(1);
    setItemOffset(0);
    setSearchText("");
    //setBookingDescription(description)
    if (index > -1) {
    } else {
      setActiveButtonIds([id])
      filterBookings({ activeIds: [id] });
    }
  }

  const handleRowsCountChange = (e) => {
    const element = document.getElementById(`tr-${e.target.value - 1}`);
    element && element.scrollIntoView();
    setRowsPerPage(parseInt(e.target.value));
  }

  const handleChangeDebounceSearchText = (e) => {
    let text = e.target.value;
    text = text && text.trim();
    setDebouncedSearchText(text && text.toLowerCase())
  }

  const GetIsAllowedSearch = async () => {
    return GetIsAllowedDuplicateSearch({ SearchText: DebouncedSearchText, SearchType: SearchByDetails, RoleId: UserRoleId}).then(function (data) {
      if (data && data.data && data.data.data && Array.isArray(data.data.data) && data.data.data.length > 0) {
         let IsSearchAllowed = data.data.data[0].IsSearchAllowed;
         let CustomerID = data.data.data[0].CustomerID;
         let IsNRI = data.data.data[0].IsNRICust;
         // call get duplicateleaddata
         setCustomeIdOuptut(CustomerID);
         setSearchAllowedAgent(IsSearchAllowed);
         setIsNRICust(IsNRI);
         return IsSearchAllowed;
      }else{
        setSearchAllowedAgent(1);
        return 1;
      }
    })
    
  }
 

  const GetDuplicateLeadDataValidation = async (SearchByDetails, DebouncedSearchText) => {
    setSelectedPage(0);
    setFilterText('');
    DebouncedSearchText = DebouncedSearchText.trim();
    let SearchAllowed = 0;

    if (SearchByDetails == 0 && DebouncedSearchText.length == 0) {
      alert("Please enter LeadID");
      return;
    }
    if (SearchByDetails == 0 && DebouncedSearchText.length < 6) {
      setCurrentError('Enter minimum six digits');
      return;
    }
    if (SearchByDetails == 0 && DebouncedSearchText.length > 0) {
      if (!/^[0-9]+$/.test(DebouncedSearchText) || DebouncedSearchText.length > 9) {
        alert("Please enter valid LeadID");
        return;
      }
    }
    if (SearchByDetails == 1 && DebouncedSearchText.length == 0) {
      alert("Please enter MobileNo");
      return;
    }
    if (SearchByDetails == 1 && DebouncedSearchText.length > 0) {
      if (!/^[0-9]+$/.test(DebouncedSearchText)) {
        alert("Please enter valid mobileno");
        return;
      }
    }
    if (SearchByDetails == 1 && DebouncedSearchText.length < 7) {
      setCurrentError('Enter minimum seven digits');
      return;
    }
    if (SearchByDetails == 2 && DebouncedSearchText.length == 0) {
      alert("Please enter EmailID");
      return;
    }
    if (SearchByDetails == 2 && DebouncedSearchText.length > 0) {
      if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(DebouncedSearchText)) {
        alert("Please enter valid email");
      }
    }
    if (SearchByDetails == 3 && DebouncedSearchText.length == 0) {
      alert("Please enter CIN");
      return;
    }
    if (SearchByDetails == 3 && DebouncedSearchText.length > 0) {
      if (!/^[a-zA-Z0-9]{21}$/.test(DebouncedSearchText)) {
        alert("Please enter valid CIN");
        return;
      }
    }
    // if (SearchByDetails == 0 && getuser().ProcessID && parseInt(getuser().ProcessID) === 7
    //   && getuser().RoleId && [12, 13].includes(parseInt(getuser().RoleId))) {
    //   alert("You are not authorised to Search by LeadId");
    //   return;
    // }
    setAllDuplicateLeads([]);
    setIsLoading(true);
    setIsSearched(true);
    const Offset = (CurrentPage - 1) * RowsPerPage;
    setItemOffset(Offset);
    setSearchText("");
    const ids = ActiveButtonIds.filter(item => item === ALL || item === SECONDARY);
    setActiveButtonIds(ids);
    if(getuser()){
      if(props.type == "SearchLeads"){
        GetDuplicateLeadData();
      }
      else {
        if(IsSocialMediaUser == true)
        {
          GetDuplicateLeadData();
        }
        if(UserRoleId == 13){//advisor
          if(IsSmeUser == true){
            GetDuplicateLeadData();
          }
          else{
            (async () => {
              let IsSearchAllowedCheck = await GetIsAllowedSearch();
              if (IsSearchAllowedCheck == 0) {
                setIsBOokedLead(true);
              }else{
                GetDuplicateLeadData();
              }
            })();
          }
        }
        else if (UserRoleId == 12){//supervisor
          if(IsSmeUser == true)
          {
            GetDuplicateLeadData();
          }
          else{
            (async () => {
              let IsSearchAllowedCheck = await GetIsAllowedSearch();
              if (IsSearchAllowedCheck == 0) {
                if(CustomeIdOuptut > 0){
                setIsLoading(false);
                setCurrentError("You have reached the maximum search limit! Try again after an hour!");
                }
                else{
                  setIsLoading(false);
                  setCurrentError("No leads Exists for this search!");
                }
              }else{
                GetDuplicateLeadData();
              }
            })();
          }
        }
        else{//AMs/TLs/Managers/Admins
          (async () => {
            let IsSearchAllowedCheck = await GetIsAllowedSearch();
            if (IsSearchAllowedCheck == 0) {
              if(CustomeIdOuptut > 0){
              setIsLoading(false);
              setCurrentError("You have reached the maximum search limit! Try again after an hour!");
              }
              else{
                setIsLoading(false);
                setCurrentError("No leads Exists for this search!"); 
              }
            }else{
              GetDuplicateLeadData();
            }
          })();
        }
      }
    } 
  } 

  const ValidateOtp = () => {
    setIsBOokedLead(false);
    GetDuplicateLeadData();
  }

    // props.GetCommonspDataV2({
    //   root: 'GetDuplicateLeads',
    //   c: "R",
    //   params: [{ UserInput: DebouncedSearchText, SearchType: SearchByDetails }],

    // }, function (data) {
   const GetDuplicateLeadData = () => { 
    if(IsSmeUser)
    {
      GetDuplicateLeadsDataByProductId({ UserInput: DebouncedSearchText, SearchType: SearchByDetails, ProductId: "131" }, function (data) {
        if (data && data.data && data.data.data) {
          let duplicateLeads = data.data.data && data.data.data.length > 0 && data.data.data[0];
          let Userinfo = data.data.info;
          setIsLoading(false);
          if (duplicateLeads.length == 0) {
            setCurrentError("No leads Exists for this search");
            return;
          }
          setCurrentError('');
          setAgentInfo(data.data.info);
          setToggleSearchDate(true);
          if (data.data.data[0][0].InvalidMobile) {
            setCurrentError(data.data.data[0][0].InvalidMobile);
          } else
            setAllDuplicateLeads(duplicateLeads);
          setMyDuplicateLeadsClone(duplicateLeads);
          setUserInfo(Userinfo);
        }
      })
    }
    else
    {
      GetDuplicateLeadsData({ UserInput: DebouncedSearchText, SearchType: SearchByDetails }, function (data) {
        if (data && data.data && data.data.data) {
          let duplicateLeads = data.data.data && data.data.data.length > 0 && data.data.data[0];
          let Userinfo = data.data.info;
          setIsLoading(false);
          if (duplicateLeads.length == 0) {
            setCurrentError("No leads Exists for this search");
            return;
          }
          setCurrentError('');
          setAgentInfo(data.data.info);
          setToggleSearchDate(true);
          if (data.data.data[0][0].InvalidMobile) {
            setCurrentError(data.data.data[0][0].InvalidMobile);
          } else
          {
            const sortedLeads = [...duplicateLeads].sort((a, b) => Number(b.LeadID) - Number(a.LeadID));
            setAllDuplicateLeads(sortedLeads);
          }
          setMyDuplicateLeadsClone(duplicateLeads);
          setUserInfo(Userinfo);
        }
      })
    }
  }


  useEffect(async () => {
    const type = IsMobile();
    setIsMobileDevice(type);
    setAgentID(getuser().UserID);
    GetProductList();
    IsSMEFosAgent();
  }, []);

  useEffect(() => {
    if(IsFosSMEAgent){
      setSearchByList([{ Id: 0, Display: 'LeadId' }, { Id: 1, Display: 'MobileNo' }, { Id: 2, Display: 'EmailId' }, { Id: 3, Display: 'CIN' }]);
    }
  }, [IsSmeUser, IsFosSMEAgent]);

  useEffect(() => {
    if (ProductsList && ProductsList.length > 0) {
      try {
        const productIds = ProductsList.map(product => product.ProductId);

        if ([12,13].includes(UserRoleId) && productIds.includes(131)) {
          setSearchByList([{ Id: 0, Display: 'LeadId' }, { Id: 1, Display: 'MobileNo' }, { Id: 2, Display: 'EmailId' }]);
          setIsSmeUser(true);
          if([13].includes(UserRoleId))
          {
            setIsSmeAgent(true);
          }
          return;
        }
        
        //Check if user is social media user
        let UserInfo = {userId : getuser().UserID };
        IsSocialMediaAgent(UserInfo, (data) => {
          if (data === true) {
            setIsSocialMediaUser(true);
            setSearchByList([
              { Id: 0, Display: 'LeadId' },
              { Id: 1, Display: 'MobileNo' },
              { Id: 2, Display: 'EmailId' },
            ]);
          }
          return;
        });

        setSearchByList([{ Id: 0, Display: 'LeadId' }, { Id: 1, Display: 'MobileNo' }])
      }
      catch {
        setSearchByList([{ Id: 0, Display: 'LeadId' }, { Id: 1, Display: 'MobileNo' }])
      }
    }
  }, [ProductsList]);

  const IsSMEFosAgent = () => {
    try {
      props.GetCommonspDataV2({
        root: 'GetGroupsByUserId',
        c: "R",
        params: [{}],
      }, (groupData) => {
        if (groupData && Array.isArray(groupData?.data?.data) && groupData.data.data.length > 0 && (getuser().RoleId === 13)) {
          let groupList = groupData.data.data[0];
          props.GetCommonData({
            root: 'SMEConfig',
            c: 'MATRIX_DASHBOARD_CLIENT',
          }, function ({ data }) {
            if (data && data.data && (data.data.length > 0)) {
              data.data.filter((d) => {
                if (d.key === 'SMEFosGroupIds') {
                  console.log('a', getuser());
                  let smeFosGroups = d.data;
                  if (Array.isArray(smeFosGroups) && Array.isArray(groupList)) {
                    for (var i = 0, len = groupList.length; i < len; i++) {
                      if (smeFosGroups.indexOf(groupList[i].GroupId) > -1) {
                        setIsFosSMEAgent(true);
                        break;
                      }
                    }
                  }
                }
              })
            }
          })
        }
      }
      )
    }
    catch (e) {
      console.log(JSON.stringify(e));
    }
  };

  const GetProductList = () => {
    props.GetCommonspDataV2({
      root: "GetUserProductList"
    }, function (result) {
      if (result && result.data && result.data.data && result.data.data.length > 0) {
        var productids = result.data.data[0];
        var roleIds = result.data.data[1]
        setProductsList(productids);
        setUserRoleId(roleIds[0].RoleId);
      }
    }.bind(this));
  }

  const handleLinkClick = async (e, data, col) => {
    e.preventDefault();
    const { Type, ErrorStatus, Data } = await PerformActionsOnLinkClick({ data, col, FilterProductId });
    if (Type === 'Toast') {
      if (ErrorStatus) {
        toast(Data, { type: 'error' });
      } else {
        toast(Data, { type: 'success' });
      }
    }
  }

  const handlePopUp = async (e, data, col) => {
    setCurrentRow(data);
    e.preventDefault();
    if(props.type == "SearchLeads"){
      if (col.name === 'LeadID') {
        const productIds = ProductsList.map(product => product.ProductId);
        const LeadOnlyProductIds = [2,117,139,114];
        let url = '';
        let domain = getCookie("cdomain");
        let baseurl = document.referrer;
        
        if ( [12,13].includes(UserRoleId) && productIds.includes(data.ProductID) && LeadOnlyProductIds.includes(data.ProductID)) {
          let result =  await LeadOnlyView(data.LeadID);
          if(result && result.data.data && result.data.data.Data){
            if(domain){
              url = "https://" + domain + "/pgv/newsv/LeadOnly/" + result?.data?.data?.Data || '';
            }else if(baseurl != "" && !baseurl.includes('matrixdashboard')) {
              url = baseurl + "pgv/newsv/LeadOnly/" + result?.data?.data?.Data || '';
            } else {
              url = config.api.newSalesviewUrl + "LeadOnly/" + result?.data?.data?.Data || '';
            }
          }
        } 
        else if ( ![12,13].includes(UserRoleId)) {
          let result =  await LeadOnlyView(data.LeadID);
          if(result && result.data.data && result.data.data.Data){
            if(domain){
              url = "https://" + domain + "/pgv/newsv/LeadOnly/" + result?.data?.data?.Data || '';
            }else if(baseurl != "" && !baseurl.includes('matrixdashboard')) {
              url = baseurl + "pgv/newsv/LeadOnly/" + result?.data?.data?.Data || '';
            } else {
              url = config.api.newSalesviewUrl + "LeadOnly/" + result?.data?.data?.Data || '';
            }
          }
        }
        
        if (url) {
          if (!IsMobileDevice) {
            window.open(url);
          } else {
            window.location.href = url;
          }
        }
      }
    }
    else{
      if (col.name === 'LeadID') {
        const productIds = ProductsList.map(product => product.ProductId);
        let url = ''
        if ([12,13].includes(UserRoleId) && productIds.includes(data.ProductID)) {
          //url = await GetSalesViewurl({ data: data, AgentInfo: UserInfo });
          url = await LeadContentView(data.CustID, data.LeadID, data.ProductID);
  
          // url = await OpenNewSalesView(data.CustID, data.LeadID, data.ProductID);
        } else if (![12,13].includes(UserRoleId)) {
          url = await OpenNewSalesView(data.CustID, data.LeadID, data.ProductID);
        }
        if (url) {
          if (!IsMobileDevice) {
            window.open(url);
          } else {
            window.location.href = url;
          }
        }
      }
    }
    
  }

  const HandleChangeOpenedLead = (e) => {
    if (OpenedLead) {
      setOpenedLead(null);
      const id = e.target.id;
      const element = document.getElementById("tr-" + id.split('-')[1]);
      element.scrollIntoView();
    } else {
      setOpenedLead(e.target.value);
    }
  }

  const handleChangeFilterText = (e) => {
    const filterval = e.target.value;
    setFilterText(filterval && filterval.toLowerCase())
  }

  // Function to get the sorting arrow
  const getSortArrow = (key) => {
    //if (sortConfig.key === key) {
    return sortConfig.direction === 'asc' ? '▲' : '▼';
    //}
  };

  const handleSort = (key) => {
    let direction = 'asc';

    // Toggle the direction if the column is already being sorted
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    // Sort the items based on the key and direction
    const sorted = [...AllDuplicateLeads].sort((a, b) => {
      if (key === 'CreatedOn') {
        let fa = new Date(a.CreatedOn).getTime();
        let fb = new Date(b.CreatedOn).getTime();

        return direction === 'asc' ? fa - fb : fb - fa;
      } else {
        // Sort by any string or other data types
        let fa = a[key] ? a[key].toString().toLowerCase() : ''; // Ensure it's a string and handle null/undefined
        let fb = b[key] ? b[key].toString().toLowerCase() : ''; // Ensure it's a string and handle null/undefined

        // Use localeCompare for accurate string sorting
        return direction === 'asc' ? fa.localeCompare(fb) : fb.localeCompare(fa);
      }
    });

    //setSortedItems(sorted);
    setItemOffset(0);
    setAllDuplicateLeads(sorted);
    setMyDuplicateLeadsClone(sorted);
    setSortConfig({ key, direction });
    setSelectedPage(0);

  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevents form submission or other default behavior
      GetDuplicateLeadDataValidation(SearchByDetails, DebouncedSearchText)
    }
  };

  const handleValidateBookedLeadsClose = () => {
    setIsBOokedLead(false);
    setIsLoading(false);
    setCurrentError("You are not authorized to view the data!");
  };

  useEffect(() => {

    if (FilterText && FilterText.length > 0) {
      const FilteredDuplicateLeads = FilterSearchedTextDuplicateLeads({ DuplicateLeads: MyDuplicateLeadsClone, TextToSearch: FilterText })
      setItemOffset(0);
      setAllDuplicateLeads(FilteredDuplicateLeads);
      setSelectedPage(0);
    } else {
      setAllDuplicateLeads(MyDuplicateLeadsClone);
    }
  }, [FilterText]);

  const DateFilter = DateRangeFilter();
  const MobileViewProps = {
    AllBookingsCount,
    AllBookingsCountSecondary,
    ActiveTabParent,
    BadgeCounter,
    BadgeCounterSecondary,
    FilterProductId
  }
  // console.log('bkg', MySpanBookings)

  let SliderProductkey = "", SliderFilters = [], TableColumnsProductWise = []
  if (IsSmeAgent) {
    if (props.type == "SearchLeads" && TableColumnsSearchLeads && TableColumnsSearchLeads.length > 0) {
      TableColumnsProductWise = TableColumnsSearchLeads.filter((e) => e.name != "LeadSource");
    }
    else {
      if (TableColumnsDuplicateLeads && TableColumnsDuplicateLeads.length > 0) {
        TableColumnsProductWise = TableColumnsDuplicateLeads.filter((e) => e.name != "LeadSource");
      }
    }

  }
  else {
    if (props.type == "SearchLeads")
      TableColumnsProductWise = TableColumnsSearchLeads;
    else
      TableColumnsProductWise = TableColumnsDuplicateLeads;
  }


  return (
    <div className="content MySpanBooking">
      <Row>
        <ToastContainer />
        <Col md={12}>
          <h3 className="heading">{IsSMEFosAgent ? 'Lead Navigator' : 'Duplicate Leads'}</h3>
        </Col>

        <OpenWebUrlModal
          show={OpenWebUrlModalShow}
          onWebUrlModalCancel={() => setOpenWebUrlModalShow(false)}
          current={CurrentRow}
          webUrl={WebUrl}
          WebUrlTitle={WebUrlTitle}
        />

        <Col md={12}>
          <nav>
            {
              <Col className="secondCol">
                {
                  SliderFilters && SliderFilters.length > 0 && SliderFilters.map((item, index) => {
                    let active = ActiveButtonIds.includes(item.id) || false;
                    let defaultCss = "";
                    if (item.css) {
                      defaultCss = item.css
                    }
                    let statusType = item.BookingStatusType;
                    let BadgeCounterCurrent = ActiveTabParent === 'Primary' ? BadgeCounter : BadgeCounterSecondary;
                    let Badge = BadgeCounterCurrent && BadgeCounterCurrent.length > 0 && BadgeCounterCurrent.filter(item => item.BookingStatusType == statusType) || {};
                    let ActiveClass = item.activeClass || " activeBtn";
                    return <button key={index} className={active ? defaultCss + ActiveClass : defaultCss} onClick={() => toggleNavButtons(item.id, item.description)} > {item.text} {item.BookingStatusType != 100 && <span className="badge bg-primary">{Badge.length > 0 && Badge[0].Count || 0}</span>}</button>
                  })
                }
              </Col>
            }
          </nav>
          {/* <NavbarMobileView
            handleChangeDebounceSearchText={handleChangeDebounceSearchText}
            value={DebouncedSearchText}
            toggleNavButtons={toggleNavButtons}
            MobileViewProps={MobileViewProps}
          />

          <SideBarMobileView
            handleBookingMonthMobile={handleBookingMonthMobile}
            ProductDetails={ProductDetails}
            ProductsList={ProductsList}
          /> */}

          <div className="SpanBookingDataTable duplicateLead">
            <div className="FilterSelectionDuplicate">

              <Row>
                <Col md={2}>
                  <label>Search By :</label>
                  <Form.Select aria-label="Default select" onChange={handleSearchByChange} value={SearchByDetails}>
                    {
                      SearchByList && SearchByList.length > 0 &&
                      SearchByList.map((item, index) => {
                        return <option key={index} value={item.Id}>{item.Display}</option>
                      })
                    }
                  </Form.Select>
                </Col>

                <Col md={3}>
                  <label>Enter Search Text</label>
                  <div className="form-group has-search">
                    <span className="fa fa-search form-control-feedback"></span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search LeadId, MobileNo …"
                      onChange={handleChangeDebounceSearchText}
                      onKeyDown={handleKeyDown}
                      value={DebouncedSearchText}
                    />
                  </div>

                </Col>
                <Col md={3}>
                  <label></label>
                  <div className="form-group has-search">
                    <button onClick={() => GetDuplicateLeadDataValidation(SearchByDetails, DebouncedSearchText)}
                      className="searchBtn">Search</button>
                  </div>
                </Col>
              </Row>
            </div>

            {IsLoading &&
              <>
                <ShimmerLoader />
                <ShimmerLoader />
              </>
            }
            {!IsLoading && CurrentError && <span style={{ color: "red" }}>{CurrentError}</span>}
            {
              !IsLoading && IsSearched && !CurrentError &&
              <>
                <div className="FilterSelection">
                  <Row>
                    <Col md={9}>
                      {SearchByDetails && SearchByDetails == 1 && <h4 className="heading">{MobileSearchDescription}</h4>}
                      {SearchByDetails && SearchByDetails == 0 && <h4 className="heading">{LeadIdSearchDescription}</h4>}
                      {SearchByDetails && SearchByDetails == 2 && <h4 className="heading">{EmailIdSearchDescription}</h4>}
                    </Col>
                    <Col md={3}>
                      {/* <input
                        type="text"
                        className="form-control"
                        onChange={handleChangeFilterText}
                        value={FilterText}
                        placeholder="Search"
                      /> */}
                      <div className="form-group has-search mb-0">
                        <span className="fa fa-search form-control-feedback"></span>
                        <input
                          type="text"
                          className="form-control"
                          onChange={handleChangeFilterText}
                          value={FilterText}
                          placeholder="Search product, leadid, customer name..."
                        />
                      </div>
                    </Col>
                  </Row>
                </div>

                <div className="spanbooking">
                  <table>
                    <thead>
                      <tr>
                        {
                          TableColumnsProductWise && TableColumnsProductWise.length > 0 &&
                          TableColumnsProductWise.map((item, index) => {
                            if (RoleId === 13 && item.name === 'SalesAgent') {
                            } else {
                              if (item.sortable) {
                                return <th key={index} onClick={() => handleSort(item.accessor)}>{item.name} {getSortArrow(item.accessor)}</th>
                              }
                              else {
                                return <th key={index}>{item.name}</th>
                              }
                            }
                          })
                        }
                      </tr>
                    </thead>
                    <tbody>
                      {
                        !IsLoading && currentItems && currentItems.length > 0 &&

                        currentItems.map((data, serial) => {
                          let INDEX = SERIAL++;
                          return (
                            TableColumnsProductWise && TableColumnsProductWise.length > 0 &&
                            <>
                              <tr key={serial} id={`tr-${serial}`} className={OpenedLead === data.LeadID ? "" : "mobileViewToggle"} >
                                {
                                  TableColumnsProductWise.map((item, index) => {
                                    const ModifiedColumnData = ModifyType(data[item.accessor], item.type);
                                    return (
                                      <TableDataFormat
                                        key={serial + "-" + index}
                                        item={item}
                                        data={data}
                                        index={index}
                                        RoleId={RoleId}
                                        Index={INDEX}
                                        ModifiedColumnData={ModifiedColumnData}
                                        handleLinkClick={handleLinkClick}
                                        handlePopUp={handlePopUp}
                                        ProductsList={ProductsList}
                                        SearchType = {props.type}
                                      />
                                    )
                                  })
                                }
                              </tr>
                              <div className="ViewMoreBtn">
                                <button id={`view-${serial}`} value={data.LeadID} onClick={(e) => HandleChangeOpenedLead(e)}> {OpenedLead === data.LeadID ? 'View Less' : 'View More'}   <i className="fa fa-angle-down" aria-hidden="true"></i>
                                </button>
                              </div>
                            </>
                          );
                        })
                      }
                    </tbody>
                  </table>
                </div>
                {
                  pageCount > 0 && <>
                    <div id="bottomPagination" className="bottomPagination">
                      <p>
                        {`Displaying ${startRecord} to ${endRecord} of ${totalRecords} Records`}
                      </p>
                      <div>
                        <Form.Select aria-label="Default select" width="20%" onChange={handleRowsCountChange} value={RowsPerPage}>
                          <option key={10} value="10">10</option>
                          <option key={20} value="20">20</option>
                          <option key={30} value="50">50</option>
                        </Form.Select>
                        <Pagination handlePageClick={handlePageClick} pageCount={pageCount} forcePage={SelectedPage} />
                      </div>
                    </div>
                  </>
                }
              </>
            }
          </div>
        </Col>
      </Row>
      {IsBOokedLead && IsBOokedLead == true && <ValidateBookedLeads open={IsBOokedLead} onClose={handleValidateBookedLeadsClose} duplicateLeads={ValidateOtp} customerId={CustomeIdOuptut} IsNRICust={IsNRICust}/>}
    </div>
  );
}

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonspData,
    GetCommonspDataV2,
    GetCommonApiData,
    PostCommunicationData,
    GetCommonData
  }
)(DuplicateLeads);