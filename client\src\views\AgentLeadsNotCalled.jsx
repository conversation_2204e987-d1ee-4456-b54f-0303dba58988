
import React from "react";
import {
  GetCommonData, GetCommonspData
} from "../store/actions/CommonAction";
import { connect } from "react-redux";
import { getuser } from '../utility/utility.jsx';
import DataTable from './Common/DataTableWithFilter';
import DropDownList from './Common/DropDownList';
import DropDown from './Common/DropDown';
import ManagerHierarchy from './Common/ManagerHierarchy';
import Moment from 'react-moment';
import moment from 'moment';
import AlertBox from './Common/AlertBox';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import 'react-datetime/css/react-datetime.css'



// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardTitle,
  Row,
  Col
} from "reactstrap";
import { Form } from 'react-bootstrap';

class AgentLeadsNotCalled extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      PageTitle: "AgentLeadsNotCalled",
      AgentLeadsNotCalled: [],
      ProductId: 0,
      ReportDate: moment().subtract(1, 'days').format("YYYY-MM-DD"),
      ReportTime: null,
      SelectedSupervisors: [],
      LastAssigned: 3,
      substatusTypes: [],
      filterVal: '',
      showTD: false,
      showSS: false,
      showTT: false,
      filterKey: '',
      productList: [],
    };
    this.productchange = this.productchange.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.columnlist = [
      {
        "name": "EmployeeId",
        "selector": "EmployeeId"
      },
      {
        "name": "UserName",
        "selector": "UserName"
      },
      {
        "name": "LeadID",
        "selector": "LeadID"
      },
      {
        "name": "LastAssignDate",
        cell: row => <div>{row.LastAssignDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.LastAssignDate}</Moment> : "N.A"}</div>,
        width: "150px"
      },
      {
        "name": "TotalDials",
        "selector": "callattempts"
      },
      {
        "name": "StatusName",
        "selector": "StatusName"
      },
      {
        "name": "SubStatus",
        "selector": "SubStatusName"
      },
      {
        "name": "Total talktime (mins)",
        "selector": "TalkTimeInMin"
      }, 
      {
        "name": "Lead createdOn",
        "selector": "LeadCreationDate"
      },
      {
        "name": "PolicyExpireDate",
        cell: row => <div>{row.PolicyExpireDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.PolicyExpireDate}</Moment> : "N.A"}</div>,
        width: "150px"
      },
      {
        "name": "LastCallDate",
        cell: row => <div>{row.LastCallDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.LastCallDate}</Moment> : "N.A"}</div>,
        width: "150px"
      },
      {
        "name": "LastCallBackDate",
        cell: row => <div>{row.LastCallBackDate ? <Moment format="YYYY-MM-DD HH:mm:ss" utc={true}>{row.LastCallBackDate}</Moment> : "N.A"}</div>,
        width: "150px"
      },

      {
        "name": "NotCalledDays",
        "selector": "NotCalledDays"
      }
    ]

    
    this.TalkTimeList = {
      config:
      {
          root: "TTlist",
          data: [ {Id:'All', Display:'All'}, {Id:'0-2', Display:'0-2 Min'}, {Id:'2-5', Display:'2-5 Min'}, {Id:'5-8', Display:'5-8 Min'}
          , {Id:'8-12', Display:'8-12 Min'}, {Id:'12-20', Display:'12-20 Min'}, {Id:'20-30', Display:'20-30 Min'}, {Id:'30-45', Display:'30-45 Min'}
          , {Id:'45&abv', Display:'45 Min abv'} ],
      }
  };

  this.TotalDialList = {
    config:
    {
        root: "TDlist",
        data: [{Id: 'All', Display: 'All'},{Id: '0', Display: '0'},{Id: '1-2', Display: '1-2'},{Id: '3-5', Display: '3-5'},
        {Id: '6-10', Display: '6-10'},{Id: '11-15', Display: '11-15'},{Id: '16-20', Display: '16-20'},{Id: '21&Abv', Display: '21&Abv'}]               
    }
  };

  this.FilterList = {
    config:
    {
        root: "Filterlist",
        data: [{Id: 'TT', Display: 'Talktime'},{Id: 'TD', Display: 'Total Dials'},{Id: 'SS', Display: 'Sub-status'}]               
    }
  };
  }

  componentDidMount(){debugger;

  }

  fetchSubStatusData(productId){
    var resultArray = [{Display: 'All', Id: 'All'}];
    if(productId){
      var subStatusArray = [];
    this.props.GetCommonspData({
      root: "GetSubStatus",
      params: [{ ProductId: productId, StatusId: 0, LeadSourceId: 0, RoleId: 0, UserId: 0 }],
    }, function (result) {
      if(result && result.data && result.data.data){
        var array = result.data.data[0];
         array.map(function(elm) {
         // var index = resultArray.findIndex(x => x.SubStatusName==elm.SubStatusName); 
          resultArray.push({ Display: elm.SubStatusName, Id: elm.SubStatusName})
          //return { Display: elm.SubStatusName, Id: elm.SubStatusName};
          console.log('resultarray',resultArray)
       });
       this.setState({ substatusTypes: resultArray  },function(results){
         console.log('substatustypes',this.state.substatusTypes);
       }.bind(this)) 

      }
      
    }.bind(this));
  
  }
  
  }
  componentWillMount() {
    setTimeout(function () {
      this.fetchCallBackData();
    }.bind(this), 500);
  }

  componentWillReceiveProps(nextProps) {
    debugger;
    if (!nextProps.CommonData.isError) {
      this.setState({ AgentLeadsNotCalled: nextProps.CommonData["AgentLeadsNotCalled"] });
    }
  }
  handleShow(e) {

    this.setState({ ReportTime: new Date(), SelectedSupervisors: e.SelectedSupervisors });
    setTimeout(function () {
      this.fetchCallBackData();
    }.bind(this), 500);

  }



  fetchCallBackData() {
    var SelectedSupervisors = this.state.SelectedSupervisors;
    const user = getuser();
    let LastAssigned = 3;
    if ([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].indexOf(user.RoleId) > -1) {
      LastAssigned = 5;
    }
    this.setState({ LastAssigned: LastAssigned });
    

    if (user.RoleId == 12) {
      SelectedSupervisors.push(user.UserID);
    }

    this.props.GetCommonspData({
      root: "AgentLeadsNotCalled",
      params: [{ ManagerIds: SelectedSupervisors.join(),//11762,
         LastAssigned: LastAssigned }],
    });   
  }
  
  productchange= (e) => {
    this.setState({
      ProductId: e.target.value
    }, function () {
      this.fetchSubStatusData(this.state.ProductId);
    });

  }
  handleChange = (e, props) => {

    if (e._isAMomentObject) {
      this.setState({ ReportDate: e.format("YYYY-MM-DD") }, function () {
        this.fetchCallBackData();
      });
    }


  }

  filterchange = (e) => {
    this.setState({ filterVal : e.target.value })
    if(e.target.value == 'TT'){
      this.setState({ showTT: true, showTD: false, showSS: false})
    }else if(e.target.value == 'TD'){
      this.setState({ showTT: false, showTD: true, showSS: false})
    }else if(e.target.value == 'SS'){
      this.GetProductList();
      this.setState({ showTT: false, showTD: false, showSS: true})
    }
  }

  talktimechange = (e) => {
    this.setState({ filterKey : e.target.value});
  }

  substatuschange = (e) => {
    this.setState({ filterKey : e.target.value})
  }

  GetProductList(){
    this.props.GetCommonspData({
      root: "GetUserProductList",
      params: [{ UserId: getuser().UserID }],
    }, function (result) {
      if(result && result.data && result.data.data){
        var array = result.data.data[0];
        var productids = array.map(function(elm) {
          return { Id: elm.ProductId, Display: elm.ProductDisplayName};
        });

        this.setState({productList : productids});
      }
      }.bind(this));
  }


  totaldialchange = (e) => {
    this.setState({ filterKey : e.target.value})
  }

  filterData = (rows) => {debugger;
      let alldata = rows
      let filterVal = this.state.filterVal
        if( filterVal == ''){
            return alldata;
        }else if (filterVal == 'SS'){
            return this.filterSS(alldata);

        }else{
            return this.filterTD(alldata);
        }
  }

   filterTD = (alldata) => {debugger;
    let key = this.state.filterKey;
    let Filter = this.state.filterVal;
    if (key === "All" || key === "") {
        return alldata;
      }
      let AgentData = [];
      if(key === "0"){
          alldata.forEach(element => {
        if (element.callattempts && element.callattempts == 0) {
          AgentData.push(element);
        }
      });
      }

      if (key.indexOf('-') > -1)
      {
          const FilterArray = key.split("-");
          alldata.forEach(element => {
            let filteredVal = (Filter == "TD")?(element.callattempts)?element.callattempts:0:
            (Filter == "TT")?(element.TotalTaktime)?Math.floor(element.TotalTaktime / 60):0:0;

            let comp1 = Number(FilterArray[0]);
            let comp2 =    (Filter == "TD")?Number(FilterArray[1]):Number(FilterArray[1])-1;     
              if (filteredVal >= comp1 && filteredVal <= comp2) {
                AgentData.push(element);
              }
            });
      }
      if (key.indexOf('&') > -1)
      {
          const FilterArray = key.split("&");

          alldata.forEach(element => {
            let filteredVal = (Filter == "TD")?(element.callattempts)?element.callattempts:0:
            (Filter == "TT")?(element.TotalTaktime)?Math.floor(element.TotalTaktime / 60):0:0;
         
              if (filteredVal >= Number(FilterArray[0]) ) {
                AgentData.push(element);
              }
            });
      }
      return AgentData;
  }

   filterSS = (alldata) => {
    if (this.state.filterKey === "" || this.state.filterKey === "0" || this.state.filterKey === "All") {
        return alldata;
      }
      let AgentData = [];

            alldata.forEach(element => {
            let filteredVal = (element.SubStatusName)?element.SubStatusName:'None';
              if (filteredVal === this.state.filterKey ) {
                AgentData.push(element);
              }
            });
      
      return AgentData;
  }



  render() {
    const columns = this.columnlist;
    const { items, PageTitle, AgentLeadsNotCalled, showAlert, AlertMsg, AlertVarient, LastAssigned, showSS, showTD, showTT } = this.state;
    console.log(this.state);
    let selectedLeads = [];


    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <ToastContainer />
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={2}>
                      <CardTitle tag="h4">{LastAssigned} Days Not Called Report</CardTitle>
                    </Col>
                    <Col md={3}>
                    <Form.Group as={Col} md={12} controlId="filter_dropdown">
                    <DropDownList firstoption="Select Filter" col={this.FilterList} onChange={this.filterchange}>
                    </DropDownList>
                    </Form.Group>
                    </Col>
                    <Col md={3}>
                    <Form.Group as={Col} md={12} controlId="tt_dropdown" style={(showTT)?{display:"block"}:{display : "none"}}>
                    <DropDownList firstoption="Select TalkTime" col={this.TalkTimeList} onChange={this.talktimechange}>
                    </DropDownList>
                    </Form.Group>
                    <Form.Group as={Col} md={12} controlId="td_dropdown" style={(showTD)?{display:"block"}:{display : "none"}}>
                    <DropDownList firstoption="Select TotalDials" col={this.TotalDialList} onChange={this.totaldialchange}>
                    </DropDownList>
                    </Form.Group>
                    <Form.Group as={Col} md={12} controlId="prod_dropdown" style={(showSS)?{display:"block"}:{display : "none"}}>
                    <DropDown firstoption="Select Product" items={this.state.productList} onChange={this.productchange}>
                    </DropDown>
                    </Form.Group>                  
                    </Col>
                    <Col md={3}>
                    <Form.Group as={Col} md={12} controlId="ss_substatus" style={(showSS)?{display:"block"}:{display : "none"}}>
                    <DropDown firstoption="Select Substatus" items={this.state.substatusTypes} onChange={this.substatuschange}>
                    </DropDown>
                    </Form.Group>
                    </Col>  
                    <Col md={2}>
                      <ManagerHierarchy
                        handleShow={this.handleShow} value={/UserID/g}
                      >
                      </ManagerHierarchy>

                    </Col>
                  </Row>

                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={(AgentLeadsNotCalled && AgentLeadsNotCalled.length > 0) ? this.filterData(AgentLeadsNotCalled[0]) : []}


                  />
                </CardBody>
              </Card>
            </Col>
          </Row>




        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData
  }
)(AgentLeadsNotCalled);