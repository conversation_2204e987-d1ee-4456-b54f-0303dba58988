const axios = require('axios');
const { GetHeaders, GetUrl, GetBody } = require('./Helper');
const { AUTO_PAY_STATUS, MY_BOOKINGS_BMS_API, PENDING_DOCUMENT_LEADLIST, MY_BOOKINGS_BMS_API2, AUTODEBIT_PROMPT } = require("./ActionTypes")
const sqlHelper = require("../../Libs/sqlHelper");
const tblList = require("../constants");
const { Base64Encoding } = require('../../auth');
const CommonMethods=require('../common/CommonMethods');
const SQLdbMethods = require('../SQLdb/SQLdbMethods');
const { DEFAULT_TIMEOUT, SUCCESS_CODES } = require('./Constants');
const moment = require('moment');

const GetCommonApi = async (req, res) => {
  try {
    const { Type, Data, Timeout } = req.query || {};
    const User = req.user || {};

    const headers = GetHeaders({ User: User, Type: Type, Data: Data || {}});
    const url = GetUrl({ User: User, Type: Type, Data: Data || {}});

    const config = {
      headers: headers,
      timeout: parseInt(Timeout) || DEFAULT_TIMEOUT
    };
    
    if (url && headers) {
      const result = await axios.get(url, config);
      return res.status(200).json({
        message: 'success',
        status: 200,
        data: result.data
      })
    } else {
      return res.status(400).json({
        status: 400,
        data: 'Bad Request'
      })
    }


  } catch (err) {
    console.log('GetCommonApi :: ', err);
    return res.status(408).json({
      status: 408,
      data: null
    })
  }
}

const FetchAgentsList = async(ManagerId, IsAdvisor) =>{
  try{
    
    let sqlParams = [
      { key: 'UserId', value: ManagerId },
      { key: 'IsAdvisor', value: IsAdvisor }
    ]
    // console.log("The paramsa  ", sqlParams);
    // if(IsAdvisor)
    // {
    //     Query= `Select CAST(UserID as int) as AgentId, UserName, EmployeeId from crm.userdetails where  UserID = @UserID and IsActive = 1`;
    //     sqlParams.push({ key: 'UserID', value: ManagerId });
      
    // }
    // else{
    //   Query=`Select CAST(UserID as int) as AgentId, UserName, EmployeeId from crm.userdetails where  ManagerId = @ManagerId and IsActive = 1`;
    //   sqlParams.push({ key: 'ManagerId', value: ManagerId });
    // }
    const result = await sqlHelper.sqlProcedure('R', "MTX.FetchMyBookingsAgentList", sqlParams);

    // console.log("the result is ", result);
    let data = result?.recordset || [];
    if(Array.isArray(data) && data.length>0)
    {
      let UserList = data.map((val)=>{
        return parseInt(val.AgentId);
      })
      // console.log("QueryList ",UserList,data )
      return {UserList : UserList, AgentData: data};
    }
    // console.log("The data is ", result.recordset);
    return {UserList : [], AgentData: []};
  }
  catch(err){
    console.log("The err is ", err);
    return {UserList : [], AgentData: []};
  }
}

const PostCommonApi = async (req, res) => {
  try {
    const { Type, Data, Timeout } = req.body || {};
    const User = req.user || {};
    const { userId, token, roleId } = req.user || {};
    if(Type === PENDING_DOCUMENT_LEADLIST && roleId == 13){
      return res.status(401).json({
        message: 'User Not Authorised to see the content.',
        status: 401,
        data: [],
      })
    }
    let info = "";
    let SecondaryleadsArray = [];
    const UserInfo = Base64Encoding(JSON.stringify({ userId, token }));

    if(Type === MY_BOOKINGS_BMS_API) {
      info = UserInfo;
      if(Data.BookingType == 2 ){
        
        if(!Data.SkipFetchLead)
        {
        let SecondaryAgentLeads = await CommonMethods.getLeadIdsSecondaryAgent(Data, userId);
        SecondaryleadsArray =  SecondaryAgentLeads?.recordsets?.length > 0 && SecondaryAgentLeads.recordsets[0] || {};
        const leadIds = SecondaryleadsArray && SecondaryleadsArray.map(lead => parseInt(lead.LeadID, 10))
        Data.LeadIDs = leadIds;
        let resultLogs = await CommonMethods.putLogsBmsSecondaryAgentBooking(Data, userId);
        }
        else{
         let SecondaryAgentLeads = await CommonMethods.getSecondaryAgentDetails(req.body.BookingId);
         SecondaryleadsArray =  SecondaryAgentLeads?.recordsets?.length > 0 && SecondaryAgentLeads.recordsets[0] || {};
        }
      }
    }
    const headers = GetHeaders({ User: User, Type: Type });
    const url = GetUrl({ User: User, Type: Type, Data: Data });
    const body = GetBody({ User: User, Type: Type, Data: Data });

    const config = {
      headers: headers,
      timeout: Timeout || DEFAULT_TIMEOUT
    };

    if (url && headers && body) {

      let result = await axios.post(url, body, config);
      let actionOnResult = await PerformActionOnResult({ Type: Type, Data: Data, Result: result, User: User, SecondaryleadsArray: SecondaryleadsArray });
       if(actionOnResult){
         result = {status : 201, message : 'ok', data : actionOnResult};
       }
      if(result?.status && SUCCESS_CODES.includes(result.status)) {
        return res.status(201).json({
          message: 'success',
          status: 201,
          data: result && result.data,
          info
        })
      } else {
        return res.status(201).json({// third party error code : ignore newrelic
          message: 'Response API:: '+ url,
          status: result?.status|| 500,
          data: result && result.data,
        })
      }
    } else {
      return res.status(201).json({// third party error code : ignore newrelic
        status: 400,
        data: 'Bad Request'
      })
    }

  } catch (err) {
    console.log('PostCommonApi :: ', err);
    return res.status(200).json({ // third party error code : ignore newrelic
      status: 408,
      data: null,
      message: err.toString()
    })
  }
  
}

const UpdateHierarchy= async (req,res)=>{
  try{
            
            // CommonMethods.DeleteMongoHierarchy({Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise"});
          //   let AdminHierarchy= await  SQLdbMethods.GetTreeUtility(75);
          //   let Data={
          //       UserID: "75",
          //       RoleName:"Admin",
          //       UserName:"Admin",
          //       children:AdminHierarchy,
          //       inserttime:new Date()
          //   }
          //   let obj75={ Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", Data: Data };
          //  await CommonMethods.InsertDataMongo(obj75);
          await SQLdbMethods.createMongoHierarchyV2();
           CommonMethods.DeleteMongoHierarchy({Database: "MATRIX_DASHBOARD_DB", Collection: "ManagerHierarchyUserWise", params:'75'});

           console.log("UpdateHierarchy Triggered At ", new Date());
            return res.status(200).json({
              status:200,
              data:null,
              message:'Success'
            })

  }
  catch(err){
    console.log('UpdateHierarchy :: ',err);
    return res.status(500).json({
      status:500,
      data:null,
      message:err.toString()
    })
  }
}

const PostCommonApiV2 = async (req,res)=>{

  try {
    let { Type, Data, Timeout } = req.body || {};
    const User = req.user || {};
    const { userId, token, roleId } = req.user || {};
    if(roleId!=12)
    {
        Data.IsSupervisor = false;
    }
    if(Type === PENDING_DOCUMENT_LEADLIST && roleId == 13){
      return res.status(401).json({
        message: 'User Not Authorised to see the content.',
        status: 401,
        data: [],
      })
    }
    let info = "";
    let SecondaryleadsArray = [];
    let AgentList =[];
    const UserInfo = Base64Encoding(JSON.stringify({ userId, token }));
    if(Type === MY_BOOKINGS_BMS_API2) {
      if(!Data.Isadvisor)
      {
        let {UserList, AgentData} =await FetchAgentsList(Data.ManagerIds ? parseInt(Data.ManagerIds): parseInt(userId),0);
        Data.AgentIds = UserList;
        AgentList= AgentData;

      }
      else{

        let {UserList,AgentData}=await FetchAgentsList(parseInt(userId),1)
        Data.AgentIds = UserList;
        AgentList = AgentData;

      }
      
      info = UserInfo;
      if(Data.BookingType == 2){
        let SecondaryAgentLeads = await CommonMethods.getLeadIdsSecondaryAgent(Data, userId);
        // SecondaryAgentLeads.recordsets[0].map((lead)=>{
        //   console.log("Secondary Leads are ",lead.LeadID);
        // })
        SecondaryleadsArray =  SecondaryAgentLeads?.recordsets?.length > 0 && SecondaryAgentLeads.recordsets[0] || {};
        const leadIds = (Array.isArray(SecondaryleadsArray) && SecondaryleadsArray.map(lead => parseInt(lead.LeadID, 10))) || [];
        Data.LeadIDs = leadIds;
        let resultLogs = await CommonMethods.putLogsBmsSecondaryAgentBooking(Data, userId);
      }
      let productIds = Data.ProductIds.split(",");
      let productList =productIds.map((productId)=>
      {
        return parseInt(productId);
      }
      )
      Data.ProductIds= productList;
    }
   
    const headers = GetHeaders({ User: User, Type: Type });
    const url = GetUrl({ User: User, Type: Type, Data: Data });
    const body = GetBody({ User: User, Type: Type, Data: Data });

    const config = {
      headers: headers,
      timeout: Timeout || DEFAULT_TIMEOUT
    };

    
    if (url && headers && body) {

      let result = null;

      const isBookingType2 = body.BookingType === 2;
      const hasLeadIDs = Array.isArray(body.LeadIDs) && body.LeadIDs.length > 0;
      if(!isBookingType2 || (isBookingType2 && hasLeadIDs))
      {
        result = await axios.post(url, body, config); 
      }
      // if (isBookingType2 && hasLeadIDs) {
      //     result = await axios.post(url, body, config); 
      // }
     
      
      let actionOnResult = await PerformActionOnResult({ Type: Type, Data: Data, Result: result, User: User, SecondaryleadsArray: SecondaryleadsArray, AgentList: AgentList });

       if(actionOnResult){
         result = {status : 201, message : 'ok', data : actionOnResult};
       }
      if(result?.status && SUCCESS_CODES.includes(result.status)) {
        return res.status(201).json({
          message: 'success',
          status: 201,
          data: result && result.data,
          info
        })
      } else {
        return res.status(201).json({// third party error code : ignore newrelic
          message: 'Response API:: '+ url,
          status: result?.status|| 500,
          data: result && result.data,
        })
      }
    } else {
      return res.status(201).json({// third party error code : ignore newrelic
        status: 400,
        data: 'Bad Request'
      })
    }

  } catch (err) {
    console.log('PostCommonApiV2 :: ', err);
    return res.status(200).json({ // third party error code : ignore newrelic
      status: 408,
      data: null,
      message: err.toString()
    })
  }

}

const PerformActionOnResult = async ({ Type, Data, Result, User, SecondaryleadsArray, AgentList }) => {
  try {
    switch (Type) {
      
      case AUTO_PAY_STATUS:
        {
        let proc = tblList['UpdatePGAutoPayStatus'];
        const { userId } = User;
        const AutoPayStatus =  Result?.data?.data?.autoPayStatus || null;
        const RegistrationDate = Result?.data?.data?.registrationDate || null;
        // let payStatus= Data?.IsSI? "":"";
        if (RegistrationDate && AutoPayStatus && AutoPayStatus != Data?.IsSI) {
          let sqlparams = [
            { key: 'BookingID', value: Data?.BookingID },
            { key: 'PGAutoPayStatus', value: AutoPayStatus },
            { key: 'UpdatedBy', value: userId },
            { key: 'registrationDate', value: RegistrationDate }
          ];
          const response = await sqlHelper.sqlProcedure('L', proc, sqlparams);
        }
      return
        }
      
      case MY_BOOKINGS_BMS_API:
        {
          const updatedBookingDetails = Array.isArray(Result?.data?.BookingDetailsPrimaryList) && Result.data.BookingDetailsPrimaryList.map(booking => {
            const match =Array.isArray(SecondaryleadsArray) && SecondaryleadsArray.length>0 && SecondaryleadsArray.find(item => item.LeadID == booking.BookingID);
            return {
              ...booking,
              UserName: match ? match.UserName : booking.UserName,
              UserID: match ? match.UserID : booking.UserID,
              EmployeeId: match ? match.EmployeeId : booking.EmployeeId,
              Category : match? match.Category : ""
            };
          });
          const updatedResult = {
            ...Result.data,
            BookingDetailsPrimaryList: updatedBookingDetails
          };

      return updatedResult;
        }



      case MY_BOOKINGS_BMS_API2:
        {
          let RawBookingDetails = Result?.data?.MyBookingList || [];
          let BookingStatusTypeList=new Array();
          let updatedBookingDetails =new Array();
          let CustomerIds=new Set();
          if(Array.isArray(RawBookingDetails) && RawBookingDetails.length>0)
          {
            let BookingStatusMap = new Map();
            BookingStatusMap.set("MyBookings2",0);
            updatedBookingDetails = RawBookingDetails.map((booking)=>{
              CustomerIds.add(booking.CustomerID);
              booking['BookingStatusType']=new Array();
              if(booking.ProductID==117)
              {
                if([41,42,43,44].includes(booking.StatusID)){

                  booking['BookingStatusType'].push(1)

                  if(BookingStatusMap.has(1))
                  {
                    
                    BookingStatusMap.set(1, BookingStatusMap.get(1)+1)
                  }
                  else{
                    BookingStatusMap.set(1, 1);
                  }
                }
                if([12,6,22,47,8,11].includes(booking.SupplierId))
                {
                  booking['BookingStatusType'].push(2);
                  if(BookingStatusMap.has(2))
                  {
                      BookingStatusMap.set(2, BookingStatusMap.get(2)+1);
                  }
                  else{
                      BookingStatusMap.set(2,1);
                  }
                }
                if(![12,6,22,47,8,11].includes(booking.SupplierId))
                {
                  booking['BookingStatusType'].push(3);
                  if(BookingStatusMap.has(3))
                    {
                        BookingStatusMap.set(3, BookingStatusMap.get(3)+1);
                    }
                    else{
                        BookingStatusMap.set(3,1);
                    }
                }
                if(booking.StatusID==37){
                  booking['BookingStatusType'].push(4);
                  if(BookingStatusMap.has(4))
                  {
                    BookingStatusMap.set(4, BookingStatusMap.get(4)+1)
                  }
                  else{
                    BookingStatusMap.set(4, 1);
                  }
                }
                if(booking.StatusID==51){
                  booking['BookingStatusType'].push(5)
                  if(BookingStatusMap.has(5))
                  {
                    BookingStatusMap.set(5, BookingStatusMap.get(5)+1)
                  }
                  else{
                    BookingStatusMap.set(5, 1);
                  }
                }
                if(booking.StatusID==17){
                  booking['BookingStatusType'].push(6);
                  if(BookingStatusMap.has(6))
                  {
                    BookingStatusMap.set(6, BookingStatusMap.get(6)+1)
                  }
                  else{
                    BookingStatusMap.set(6, 1);
                  }
                }
                if([38,40, 45,46,47,48,49].includes(booking.StatusID)){
                  booking['BookingStatusType'].push(7);
                  if(BookingStatusMap.has(7))
                  {
                    BookingStatusMap.set(7, BookingStatusMap.get(7)+1)
                  }
                  else{
                    BookingStatusMap.set(7, 1);
                  }
                }
              }
              else
              {
                if(booking.ProductID==2)
                { 
                  
                  if([2,6,7,10,12].includes(booking.KYCStatusId))
                  {
                    booking['BookingStatusType'].push(11);
                    if(BookingStatusMap.has(11))
                      {
                        BookingStatusMap.set(11, BookingStatusMap.get(11)+1)
                      }
                      else{
                        BookingStatusMap.set(11, 1);
                      }
                  }
                  if(booking.VerificationStatus=='Pending')
                  {
                    booking['BookingStatusType'].push(12);
                    if(BookingStatusMap.has(12))
                      {
                        BookingStatusMap.set(12, BookingStatusMap.get(12)+1)
                      }
                      else{
                        BookingStatusMap.set(12, 1);
                      } 
                  }
                }

                if(booking.ProductID==115 && booking.VerificationStatus=='Pending' && [null,'Not Attempted','Not Contactable','Wrong Number','Call Back Later'].includes(booking.VerificationSubStatus))
                {
                  booking['BookingStatusType'].push(12);
                  if(BookingStatusMap.has(12))
                    {
                      BookingStatusMap.set(12, BookingStatusMap.get(12)+1)
                    }
                    else{
                      BookingStatusMap.set(12, 1);
                    }
                }

                if(booking.StatusType!='Active' && !['annual','annually','annualy','SINGLE','Single Premium','Single','Yearly'].includes(booking.PaymentPeriodicity) && ![46,49,50].includes(booking.StatusID))
                {
                  booking['BookingStatusType'].push(1);
                  if(BookingStatusMap.has(1))
                    {
                      BookingStatusMap.set(1, BookingStatusMap.get(1)+1)
                    }
                    else{
                      BookingStatusMap.set(1, 1);
                    }
                }
                if(booking.StatusID==13){
                  booking['BookingStatusType'].push(2);
                  if(BookingStatusMap.has(2))
                  {
                    BookingStatusMap.set(2, BookingStatusMap.get(2)+1)
                  }
                  else{
                    BookingStatusMap.set(2, 1);
                  }
                }
                if(booking.StatusID==55 && [295,296,297,379,470,471,472,473,474,475,476,477,478,1427,1428,1429,1469,1508,1594,1595,1596,1605,1606,1607,1608,1941,1944,1947,1950,1956,1962,1963,1964,1965,1966,1967,1968,1972].includes(booking.SubStatusID)){
                  booking['BookingStatusType'].push(3);
                  if(BookingStatusMap.has(3))
                  {
                    BookingStatusMap.set(3, BookingStatusMap.get(3)+1)
                  }
                  else{
                    BookingStatusMap.set(3, 1);
                  }
                }
                if(booking.StatusID==76){
                  booking['BookingStatusType'].push(4);
                  if(BookingStatusMap.has(4))
                  {
                    BookingStatusMap.set(4, BookingStatusMap.get(4)+1)
                  }
                  else{
                    BookingStatusMap.set(4, 1);
                  }
                }
                if([17,39].includes(booking.StatusID)){
                  if([85,106,112,160,303,311,503,1385,1388,1390,1487,1498,1499,1502,1503,1504,1539,1857,1891,1928,1929,1930,1931,1932,1933,1540,1541,1686,1505,1500,1501,1488,304,261].includes(booking.SubStatusID))
                  {
                    booking['BookingStatusType'].push(5);
                  if(BookingStatusMap.has(5))
                  {
                    BookingStatusMap.set(5, BookingStatusMap.get(5)+1)
                  }
                  else{
                    BookingStatusMap.set(5, 1);
                  }
                }
                if([438,479,480,484,1858,1937,1938, 1506].includes(booking.SubStatusID))
                  {
                    booking['BookingStatusType'].push(6);
                  if(BookingStatusMap.has(6))
                  {
                    BookingStatusMap.set(6, BookingStatusMap.get(6)+1)
                  }
                  else{
                    BookingStatusMap.set(6, 1);
                  }
                }
                }
                if([41,42,43,44].includes(booking.StatusID)){
                  booking['BookingStatusType'].push(7);
                  if(BookingStatusMap.has(7))
                  {
                    BookingStatusMap.set(7, BookingStatusMap.get(7)+1)
                  }
                  else{
                    BookingStatusMap.set(7, 1);
                  }
                }

                if([7].includes(booking.ProductID) && [41,42,43,44].includes(booking.StatusID) && ((new Date(booking.StatusDate) - new Date(booking.BookingDate)) / (1000 * 60 * 60 * 24) <= 20) )
                  {
                    booking['BookingStatusType'].push(18);
                    if(BookingStatusMap.has(18))
                      {
                        BookingStatusMap.set(18, BookingStatusMap.get(18)+1)
                      }
                      else{
                        BookingStatusMap.set(18, 1);
                      }
                  }

                if(booking.VerificationStatus=='Verification Failed' && booking.VerificationSubStatus=='Fatal (Red Category)')
                {
                  booking['BookingStatusType'].push(8);
                  if(BookingStatusMap.has(8))
                    {
                      BookingStatusMap.set(8, BookingStatusMap.get(8)+1)
                    }
                    else{
                      BookingStatusMap.set(8, 1);
                    } 
                }
                if((booking.VerificationStatus=='Verification Failed' && ['Mis-Commitment (Yellow Category)','Yellow Category_Reject'].includes(booking.VerificationSubStatus)) 
                  // || (booking.VerificationStatus=='Verification Successful' && booking.VerificationSubStatus=='Yellow Category_Accept') 
                  || (booking.VerificationStatus=='Pending' && booking.VerificationSubStatus=='Solved by Agent') )
                  {
                    booking['BookingStatusType'].push(9);
                    if(BookingStatusMap.has(9))
                      {
                        BookingStatusMap.set(9, BookingStatusMap.get(9)+1)
                      }
                      else{
                        BookingStatusMap.set(9, 1);
                      } 

                  }
                  if(booking.VerificationStatus=='SLA Breach' && booking.VerificationSubStatus=='SLA Breach')
                  {
                    booking['BookingStatusType'].push(10);
                    if(BookingStatusMap.has(10))
                      {
                        BookingStatusMap.set(10, BookingStatusMap.get(10)+1)
                      }
                      else{
                        BookingStatusMap.set(10, 1);
                      } 
                  }
                  if(booking.FreeLookCancellation || ([45,16].includes(booking.StatusID) && [289, 439, 1650].includes(booking.SubStatusID)))
                  {
                    booking['BookingStatusType'].push(13);
                    if(BookingStatusMap.has(13))
                      {
                        BookingStatusMap.set(13, BookingStatusMap.get(13)+1)
                      }
                      else{
                        BookingStatusMap.set(13, 1);
                      } 
                  }
                  if([38,40, 45,46,47,48,49].includes(booking.StatusID))
                  {
                    booking['BookingStatusType'].push(14);
                    if(BookingStatusMap.has(14))
                      {
                        BookingStatusMap.set(14, BookingStatusMap.get(14)+1)
                      }
                      else{
                        BookingStatusMap.set(14, 1);
                      } 
                  }
                  if([55,17,39].includes(booking.StatusID) && [295,296,297,379,470,471,472,473,474,475,476,477,478,1427,1428,1429,1469,1508,1594,1595,1596,1605,1606,1607,1608,1941,1944,1947,1950,1956,1962,1963,1964,1965,1966,1967,1968,1972,
                    85,106,112,160,303,311,503,1385,1388,1390,1487,1498,1499,1502,1503,1504,1539,1857,1891,1928,1929,1930,1931,1932,1933,1540,1541,1686,1505,1500,1501,1488,304,261].includes(booking.SubStatusID))
                    {
                      booking['BookingStatusType'].push(15);
                      if(BookingStatusMap.has(15))
                        {
                          BookingStatusMap.set(15, BookingStatusMap.get(15)+1)
                        }
                        else{
                          BookingStatusMap.set(15, 1);
                        } 
                    }
                  if(["Pending with Sales","Not Accepted","Pending with Quality"].includes(booking.MissellingStatus))
                  {
                    booking['BookingStatusType'].push(16);
                    if(BookingStatusMap.has(16))
                      {
                        BookingStatusMap.set(16, BookingStatusMap.get(16)+1)
                      }
                      else{
                        BookingStatusMap.set(16, 1);
                      } 
                    
                  }
              }
            const match = SecondaryleadsArray.find(item => item.LeadID == booking.BookingID);
 
            return {
              ...booking,
              UserName: match ? match.UserName : booking.UserName,
              UserID: match ? match.UserID : booking.UserID,
              EmployeeId: match ? match.EmployeeId : booking.EmployeeId,
              Category : match? match.Category : "",
              ClaimTaken : false,
              ClaimAmount: 0
            };
             })
            let CustomerIdString = [...CustomerIds].join(",");
            let sqlparams = [
              { key: 'CustomerIds', value: CustomerIdString }
            ];
            let result  = await sqlHelper.sqlProcedure("R","[MTX].[GetClaimStatus]",sqlparams);
           
            if(Array.isArray(result?.recordset) && result.recordset.length>0)
            {
              result.recordset.forEach(claim=>{
                
                const filteredData = updatedBookingDetails.filter(item => item.CustomerID == claim.CustomerId);

                filteredData.forEach(match=>{
                  match.ClaimTaken = true;
                  match.ClaimAmount = claim.SettlementAmount;

                  match['BookingStatusType'].push(17);
                })
                if(BookingStatusMap.has(17))
                {
                      BookingStatusMap.set(17, BookingStatusMap.get(17)+filteredData.length)
                }
                else{
                      BookingStatusMap.set(17, filteredData.length);
                }


              })
            }
             BookingStatusMap.forEach((val,key)=>{
                    BookingStatusTypeList.push({"BookingStatusType":key,"Count":val})
             })
          }
          // console.log(Result.data.MyBookingList)
          // const updatedBookingDetails = Array.isArray(RawBookingDetails) && RawBookingDetails.map(booking => {
          //   const match = SecondaryleadsArray.find(item => item.LeadID == booking.BookingID);
          //   return {
          //     ...booking,
          //     UserName: match ? match.UserName : booking.UserName,
          //     UserID: match ? match.UserID : booking.UserID,
          //     EmployeeId: match ? match.EmployeeId : booking.EmployeeId
          //   };
          // });
          const updatedResult = {
            // ...Result.data,
            BookingDetailsPrimaryList: updatedBookingDetails,
            BookingStatusTypeList: BookingStatusTypeList,
            AgentList: AgentList
          };

        return updatedResult;

        }

      default:
        return

    }
  } catch (err) {
    console.log('Inside PerformActionOnResult :: ', err);
    return
  }
}


const FetchProcTableWithMenu=async (req, res)=>{
  try{
  let spresult = await sqlHelper.sqlProcedure("R", "[CRM].[FetchMenuEntityList]");
  let data= {} ;

  // console.log("Spresult is :: ", spresult.recordsets);
  if(Array.isArray(spresult.recordsets) && spresult.recordsets.length>0)
  {
      if(spresult.recordsets[0])
      {
        data['MenuList'] = spresult.recordsets[0]
      }
      if(spresult.recordsets[1])
      {
        data['EntityList'] = spresult.recordsets[1];
      }
      if(spresult.recordsets[2])
      {
        data['MenuEntityMapping'] = spresult.recordsets[2];
      }

  }
  return res.status(200).json({
    data: data
  })
}
catch(e)
{
  return res.status(500).json({
    errorStatus:1
  })
}
}

const EditMenuEntityMapping=async (req, res)=>{
  try{
  let userId= req.user?.userId || null;
  if(!userId)
  {
    return res.status(500).json({
      errorStatus:1
    })
  }
  let {MenuId, EntityId}= req.body;
  

  let menuid = Array.isArray(MenuId) && MenuId.join(',');
 
  let sqlParams = [
    { 
      key: 'MenuIds', value: menuid 
    },
    {
      key: 'EntityId', value : parseInt(EntityId)
    },
    { 
      key: 'UpdatedBy', value: userId 
    }
  ]
  let spresult = await sqlHelper.sqlProcedure("L", "[CRM].[EditMenuEntityMapping]", sqlParams);
  let data= {} ;

  // console.log("Spresult is :: ", spresult.recordsets);
  if(Array.isArray(spresult.recordsets) && spresult.recordsets.length>0)
  {
      if(spresult.recordsets[0])
      {
        data['MenuEntityMapping'] = spresult.recordsets[0];
      }

  }
  return res.status(200).json({
    data: data
  })
}
catch(e)
{
  return res.status(500).json({
    errorStatus:1
  })
}
}


const AutoDebitPrompt = async (req, res) => {
  try {
    const { Type, Data, Timeout } = req.query || {};
    const User = req.user || {};

    const headers = GetHeaders({ User: User, Type: Type, Data: Data || {}});
    const url = GetUrl({ User: User, Type: Type, Data: Data || {}});
    const body = GetBody({ User: User, Type: Type, Data: Data || {}});

    const config = {
      headers: headers,
      timeout: parseInt(Timeout) || DEFAULT_TIMEOUT
    };
    
    if (url && headers  && body) {
      const result = await axios.post(url,body, config);
      // console.log("the result is ",result.data);
      if(result.data && result.data.status)
      {
        return res.status(200).json({
          msg: 'E-Mandate Link sent',
          errorStatus: 0,
          message:  result.data?.message || ""
        })
      }
      else{
        return res.status(200).json({
          msg: 'Unable to send E-Mandate Link',
          errorStatus: 1,
          message:  result.data?.message || ""
        })
      }
    } else {
      return res.status(200).json({
        msg: "url/header not present",
        errorStatus:1
      })
    }


  } catch (err) {
    console.log('AutoDebitPrompt :: ', err);
    return res.status(500).json({
      msg: String(err),
      errorStatus:1
    })
  }
}

module.exports = {
  GetCommonApi: GetCommonApi,
  PostCommonApi: PostCommonApi,
  PerformActionOnResult: PerformActionOnResult,
  UpdateHierarchy:UpdateHierarchy,
  PostCommonApiV2: PostCommonApiV2,
  FetchProcTableWithMenu: FetchProcTableWithMenu,
  EditMenuEntityMapping: EditMenuEntityMapping,
  AutoDebitPrompt: AutoDebitPrompt
}