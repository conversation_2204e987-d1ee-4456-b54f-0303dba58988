const { ValidateUserForUpsertAccess } = require("./common/CommonMethods");

async function ACLlayer(req, res, next) {
    
    try{
        let api = req.originalUrl.split('/').slice(-2); 
        let apiRoute = api.join('/');  
        // console.log("the route is ", apiRoute);
        let userId = req.user?.userId ? req.user.userId : null;  

        const hasAccess = await ValidateUserForUpsertAccess(userId, apiRoute);

        if (hasAccess) {
            next();  
        } else {
            
            res.status(401).send("Unauthorized attempt to access Data");
        }
    }
    catch (err) {
        console.log('Inside Auth', err);
        createLog(TrackingId, "Auth", "", "", "", err, IP);
        res.status(500).send("Auth Error");
      }
    
}



module.exports = {
    ACLlayer: ACLlayer
  }