import Axios from "axios";
import React from "react";
import config from "../../config.jsx";
import { Button, Form } from "react-bootstrap";
import { getuser } from "utility/utility";
import { GetCommonspDataV2 } from "../../store/actions/CommonAction";
import { GetCommonData } from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";
import DataTable from "../Common/DataTableWithFilter";
import { fnBindRootData, fnDatatableCol } from "../../utility/utility.jsx";
import { CardTitle, Row, Col } from "reactstrap";
import "react-toastify/dist/ReactToastify.css";
import Moment from "react-moment";
import DropDown from '../../views/Common/DropDown';
import moment from 'moment';
import { MultiSelect } from "react-multi-select-component";
import { ToastContainer, toast } from "react-toastify";

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';

const uuid = require("uuid");

let AxiosInstanceUpload = Axios.create({
    baseURL: "",
    header: {},
    withCredentials: true
});

class ChurnAssignment extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            items: [],
            Useritems: [],
            Leaditems: [],
            GetRenewalLeads: "Renewal_AssignedToAgent",
            AssignRenwalLead: "AssignedToRenewalAgent",
            event: "",
            fetchagent: [],
            startDate: moment().format("YYYY-MM-DD"),
            endDate: moment().add(31, 'days').format("YYYY-MM-DD"),
            assigndisbled: true,
            selectedRows: [],
            promises: [],
            userSelectValue: [],
            SupplierList: [],
            selectedSupplier: 0,
            selectedRN: 0,
            selectedType: 0,
            selectedProduct: 2
        };
        this.FetchRequestlist = [
            {
                name: "id",
                label: "Sno",
                type: "int",
                width: "70px",
            },
            {
                name: "LeadID",
                label: "Lead ID",
                type: "string",
                width: "130px",
            },
            {
                name: "ParentID",
                label: "Parent ID",
                type: "string",
                width: "130px",
            },
            {
                name: "CustID",
                label: "Customer ID",
                type: "string",
                width: "130px",
            },
            {
                name: "LeadCreatedOn",
                label: "LeadCreatedOn",
                type: "datetime",
                cell: (row) =>
                    row.createdon == null ? (
                        "N/A"
                    ) : (
                        <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
                            {row.createdon}
                        </Moment>
                    ),
                format: "DD/MM/YYYY HH:mm:ss",
                width: "150px",
            },
            {
                name: "Name",
                label: "Name",
                type: "string",
                width: "130px",
            },
            {
                name: "Product",
                label: "Product",
                type: "string",
                width: "130px",
            },
            {
                name: "SupplierName",
                label: "Supplier Name",
                type: "string",
                width: "130px",
            },
            {
                name: "NoticePremium",
                label: "Notice Premium",
                type: "string",
                width: "130px",
            },
            {
                name: "RenewalYear",
                label: "Renewal Year",
                type: "string",
                width: "130px",
            },
            {
                name: "PolicyExpiryDate",
                label: "PolicyExpiryDate",
                type: "datetime",
                cell: (row) =>
                    row.createdon == null ? (
                        "N/A"
                    ) : (
                        <Moment utc={true} format="DD/MM/YYYY HH:mm:ss">
                            {row.createdon}
                        </Moment>
                    ),
                format: "DD/MM/YYYY HH:mm:ss",
                width: "150px",
            },
            {
                name: "AssignedTo",
                label: "AssignedTo",
                type: "string",
                width: "130px",
            },
            {
                name: "AssignedFrom",
                label: "AssignedFrom",
                type: "string",
                width: "130px",
            },
            {
                name: "UniqueId",
                label: "UniqueId",
                type: "string",
                hide: true
            }
        ];
    }

    componentDidMount() {
        var url = "coremrs/api/MRSCore/GetUserList?ProductId=2&RoleId=13"
        this.fetchuserslist(url)
            .then((result) => {
                if (result && result.UserListResult && result.UserListResult.Data == null) {
                    toast("No User found", { type: "error" });
                }
                else {
                    this.setState({
                        Useritems: result.UserListResult.Data.map(({ UserId, EmployeeId, UserName }) => ({
                            value: UserId,
                            label: UserName + '(' + EmployeeId + ')'
                        }))
                    });
                }
            })
            .catch((err) => {
                toast("No User found", { type: "error" });
            });
        var url = "coremrs/api/Master/Suppliers"
        this.fetchsupplierlist(url)
            .then((result) => {
                if (result && result == null) {
                    toast("No Supplier found", { type: "error" });
                }
                else {
                    this.setState({
                        SupplierList: result
                            .filter((x) => x.ProductId === 2)
                            .map(({ OldSupplierId, SupplierName }) => ({
                                Id: OldSupplierId,
                                Display: SupplierName
                            })),
                    });
                }
            })
            .catch((err) => {
                toast("No Supplier found", { type: "error" });
            });
    }

    componentWillReceiveProps(nextProps) {
        if (!nextProps.CommonData.isError && !nextProps.CommonData[this.state.GetRenewalLeads][0][0].errormessage) {
            this.setState({
                Leaditems: nextProps.CommonData[this.state.GetRenewalLeads],
                assigndisbled: false
            });
        }
        else {
            this.setState({
                Leaditems: []
            });
        }
    }

    setAssignedToAgent(list) {
        this.setState({ userSelectValue: list });
    }

    setAssignedFromAgent(list) {
        if (list.length > 1)
            toast("Select one Churn Employee ID ", { type: "error" })
        else
            this.setState({ fetchagent: list });
    }

    onSupplierChange = (event) => {
        this.setState({
            selectedSupplier: event.target.value,
        });
    }

    onRNChange = (event) => {
        this.setState({
            selectedRN: event.target.value,
        });
    }

    onRenewalTypeChange = (event) => {
        this.setState({
            selectedType: event.target.value,
        });
    }

    onProductChange = (event) => {
        this.setState({
            selectedProduct: event.target.value,
        });
    }

    onStartDatetChange = (event) => {
        this.setState({
            startDate: event.format("YYYY-MM-DD")
        }, () => {
            const startMoment = moment(this.state.startDate); // Convert startDate to a moment object
            this.setState({
                endDate: startMoment.add(31, 'days').format("YYYY-MM-DD")
            });
        });
    }
    onEndDatetChange = (event) => {
        this.setState({
            endDate: event.format("YYYY-MM-DD")
        }, () => {

            const startMoment = moment(this.state.startDate); // Convert startDate to a moment object
            const endMoment = moment(this.state.endDate);
            const differenceInDays = endMoment.diff(startMoment, 'days');

            if (differenceInDays > 31) {
                toast("The date range is more than 31 days.", { type: "error" });
                this.setState({
                    endDate: startMoment.add(31, 'days').format("YYYY-MM-DD")
                });
            }

            if (differenceInDays < 0) {
                toast("Select end date greater than start date.", { type: "error" });
                this.setState({
                    endDate: startMoment.add(31, 'days').format("YYYY-MM-DD")
                });
            }
        });
    }

    getLeads = () => {
        const AssignedTo = this.state.userSelectValue.reduce((acc, item, index) => {
            return acc + (index > 0 ? ',' : '') + item.value;
        }, '');
        const AssignedFrom = this.state.fetchagent.reduce((acc, item, index) => {
            return acc + (index > 0 ? ',' : '') + item.value;
        }, '');
        var uniqueId = uuid.v1().toUpperCase();

        if (this.state.userSelectValue != [] && this.state.fetchagent != '' && this.state.userSelectValue.length != 0 && this.state.fetchagent.length != 0) {
            if (this.state.fetchagent.length === 1) {
                this.FetchRequestlist.map((col) => fnBindRootData(col, this.props));
                this.props.GetCommonspDataV2({
                    root: this.state.GetRenewalLeads,
                    c: "L",
                    params: [
                        {
                            AssignedToList: AssignedTo,
                            StartDate: this.state.startDate,
                            EndDate: this.state.endDate,
                            AgentId: AssignedFrom,
                            SupplierID: this.state.selectedSupplier,
                            NoticePremium: this.state.selectedRN,
                            RenewalType: this.state.selectedType,
                            ProductId: this.state.selectedProduct,
                            UniqueId: uniqueId.trim()
                        }],
                }, function (data) {
                    if (data.data.data[0][0].errormessage)
                        toast(data.data.data[0][0].errormessage, { type: "error" })
                });
            }
            else {
                toast("Select one Churn Employee ID ", { type: "error" })
            }
        }
        else {
            toast("Select respective Employee ID", { type: "error" })
        }
    }

    AssignLeads = () => {
        if (this.state.Leaditems.length > 0 && this.state.Leaditems[0].length > 0) {
            this.props.GetCommonspDataV2({
                root: this.state.AssignRenwalLead,
                c: "L",
                params: [
                    {
                        UniqueId: this.state.Leaditems[0][0].UniqueId
                    }],
            },
                function (data) {
                    if (data?.data && data.data.status === 200) {
                        toast("Lead(s) assigned successfully", { type: "success" });
                    }
                }
            );
        }
    }

    ClearPage = () => {
        window.location.reload();
    }

    fetchuserslist = (url) => {
        const input = {
            url,
            method: "GET",
            service: "fetchusersURL",
        };
        let a = this.CALL_API(input);
        return a;
    };

    fetchsupplierlist = (url) => {
        const input = {
            url,
            method: "GET",
            service: "fetchusersURL",
        };
        let a = this.CALL_API(input);
        return a;
    };

    CALL_API = async (input) => {
        // Set defaults
        input.timeout = 3000; // timeout s/m/l/(number of millisec)
        input.method = input.method || "GET";
        input.cache = input.cache || false;
        input.service = input.service || "core";
        let URL, Headers;
        //Set service and url
        debugger;
        switch (input.service) {
            case "fetchusersURL":
                URL = config.api.MatrixCoreURL + input.url;
                Headers = {
                    ...input.headers,
                    AgentId: getuser().UserID,
                    source: "dashboard",
                    "Access-Control-Allow-Origin": "*",
                };

                break;

            default:
                URL = config.api.MatrixCoreURL + input.url;
                Headers = {
                    "Content-Type": "application/json",
                };
                break;
        }
        let timeout = 3000;
        // Prepare reqData
        var reqData = {
            method: input.method,
            url: URL,
            headers: Headers,
            cache: input.cache,
            timeout,
        };
        // Add payload if provided
        if (input.requestData !== undefined) {
            reqData.data = JSON.stringify(input.requestData);
        }
        if (input.formData) {
            reqData.data = input.formData;
        }
        return new Promise((resolve, reject) => {
            AxiosInstanceUpload(reqData)
                .then((res) => {
                    // ;
                    if (input.acceptOnly200 === true && res.status !== 200) {
                        reject(res.statusText);
                        return;
                    }
                    resolve(res.data);
                })
                .catch((error) => {
                    // ;
                    if (error.response) {
                        reject(error.response);
                    } else if (error.request) {
                        reject(error.request);
                    } else {
                        reject(error.message);
                    }
                });
        });
    };

    fnDatatableColsUploaded() {
        var UploadedColumn = fnDatatableCol(this.FetchRequestlist);
        return UploadedColumn;
    }

    render() {
        const UploadedColumn = this.fnDatatableColsUploaded();
        const {
            startDate,
            endDate,
            Leaditems,
            assigndisbled,
            userSelectValue,
            Useritems,
            SupplierList,
            fetchagent
        } = this.state;
        return (
            <div className="content">
                <div class="container-fluid">
                    <Row>
                        <Col md={3}>
                            <CardTitle tag="h4" className="mb-4">Renewal leads re-assignment</CardTitle>
                        </Col>
                    </Row>
                    <Row>
                        <Col md={3}>
                            <label>Policy Start Date</label>
                            <Datetime
                                dateFormat="YYYY-MM-DD"
                                value={startDate}
                                name="startDate"
                                onChange={moment => this.onStartDatetChange(moment)}
                                utc={true}
                                timeFormat={false} />
                        </Col>
                        <Col md={3}>
                            <label>Policy End Date</label>
                            <Datetime
                                dateFormat="YYYY-MM-DD"
                                name="endDate"
                                value={endDate}
                                onChange={moment => this.onEndDatetChange(moment)}
                                utc={true}
                                timeFormat={false} />
                        </Col>
                        <Col md={3}>
                            <label>Supplier List</label>
                            <DropDown firstoption="ALL" name="supplierid" items={SupplierList} onChange={this.onSupplierChange} ></DropDown>
                        </Col>
                        <Col md={3}>
                            <label>Premium {'>'} 150 </label>
                            <select
                                className="form-select"
                                value={this.state.selectedRN}
                                onChange={this.onRNChange}
                            >
                                <option value="0">All</option>
                                <option value="1">1</option>
                                <option value="2">0</option>
                            </select>
                        </Col>
                    </Row>
                    <Row>
                        <Col md={3}>
                            <label>Renewal Type </label>
                            <select
                                className="form-select"
                                value={this.state.selectedType}
                                onChange={this.onRenewalTypeChange}
                            >
                                <option value="0">All</option>
                                <option value="1">Fresh</option>
                                <option value="2">Renewal</option>
                            </select>
                        </Col>
                        <Col md={3}>
                            <label>Product </label>
                            <select
                                className="form-select"
                                value={this.state.selectedProduct}
                                onChange={this.onProductChange}
                            >
                                <option value="2">Health</option>
                                <option value="106">Critical Illness</option>
                                <option value="130">Super Topup</option>
                                <option value="118">Personal Accident</option>
                            </select>
                        </Col>
                        <Col md={3}>
                            <label>Select Employee ID</label>
                            <MultiSelect
                                options={Useritems}
                                value={fetchagent}
                                onChange={this.setAssignedFromAgent.bind(this)}
                            />
                        </Col>
                        <Col md={3}>
                            <label>Assign To Employee ID</label>
                            <MultiSelect
                                options={Useritems}
                                value={userSelectValue}
                                onChange={this.setAssignedToAgent.bind(this)}
                            />
                        </Col>
                    </Row>
                    <Row>

                        <Col md={3}>
                            <label> </label>
                            <Button
                                onClick={this.getLeads}
                                className="btn btn-success btnSmall"
                            >
                                Fetch Leads
                            </Button>
                        </Col>
                        <Col md={3}>
                            <label> </label>
                            <Button
                                onClick={this.AssignLeads}
                                disabled={assigndisbled}
                                className="btn btn-success btnSmall"
                            >
                                Assign Leads
                            </Button>
                        </Col>
                        <Col md={3}>
                            <label> </label>
                            <Button
                                onClick={this.ClearPage}
                                className="btn btn-success btnSmall"
                            >
                                Clear
                            </Button>
                        </Col>

                    </Row>
                    <>
                        <ToastContainer />
                        <div className="content">
                            <Form className="uploadRelewalLeadTable">
                                <DataTable
                                    columns={UploadedColumn}
                                    data={
                                        Array.isArray(Leaditems) && Leaditems.length > 0
                                            ? Leaditems[0]
                                            : []
                                    }
                                    paginationPerPage={500}
                                />
                            </Form>
                        </div>
                    </>

                </div>
            </div>
        );
    }
}
function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}
export default connect(mapStateToProps, {
    GetCommonData,
    GetCommonspDataV2
})(ChurnAssignment);