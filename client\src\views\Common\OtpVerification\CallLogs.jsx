import React, { Component } from 'react';
import { Button } from 'react-bootstrap';
import {
    Table,
    Row,
    Col
} from "reactstrap";

import moment from 'moment';
import AdvisorInfo from "./AdvisorInfo";

class CallLogs extends Component{

    constructor(props) {
        super(props);
        this.state = {
            bgImg: '/Graphic.svg' ,   
            noLogImg: '/noLog.svg' , 
            step: 1,
            userIdEmp : null,
    }
    }

    back  = () => {
        window.location.reload();
    }

    prevstep = () => {
        const { step } = this.state
        this.setState({
            step: step - 1
        })
    }

    checkAgentInfo(element){debugger;

            if (element.AVCertified && element.AVCertified == true) {
                this.setState({step : 2, agentid : element.EmployeeID, userIdEmp : element.UserId})
            }else{
                //return false
            }
    }

    displayCallDetails(AgentCallDetail) {debugger;

        let tr = [];
        if (AgentCallDetail && AgentCallDetail.length > 0) {
        let elem = document.getElementById('elem');
        if(elem){
        let event = new CustomEvent("onCallLogVisible", {detail: { CustomerId: this.props.CustomerId },bubbles: true}); // (2)
        elem.dispatchEvent(event);
        }
            AgentCallDetail.forEach(element => {
                let duration = this.props.hhmmss(element.Duration).replace(/[^\d]/g, " ").trim();
                tr.push(<tr>
                    <td>{moment(new Date(element.CallDate)).format("DD MMM")}<p>{moment(new Date(element.CallDate)).format("hh:mm A")}</p></td>
                    <td><a className={(element.AVCertified == true)?"hyperlinkcl":""} onClick={() => this.checkAgentInfo(element) }>{element.Name}</a><p>{element.ProductName}</p></td>
                    <td><p>{(element.CallingNo)?element.CallingNo: 'N.A'}</p></td>
                    <td><p className={(element.Duration == 0)?"activecl":""}>{(element.Duration == 0)?"Active Call":duration.split(/[ ]+/).join(':')}</p></td>
                </tr>)
            });
  
            return  <Table responsive>
                <thead className="text-primary">
                    <tr>
                        <th>Date/Time</th>
                        <th>Advisor</th>
                        <th>Calling Number</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {tr}
                </tbody>
            </Table> 
           
          
        }
        else {
            let elem = document.getElementById('elem');
            if(elem){
            let event = new CustomEvent("onCallLogNotVisible", {detail: { CustomerId: this.props.CustomerId },bubbles: true, custId: '222'}); // (2)
            elem.dispatchEvent(event);
            }
            return <Col md={12} xs={12} className="text-center"> <img  src={this.state.noLogImg}/>
            <h4>No Previous Calls</h4>
            <p>Details of your calls with our advisors will be shown here</p>
            </Col>
        }      
    }

    renderCallLogs(AgentCallDetails){
        
        return(<>
        <Row>             
            <Col md={6} xs={12}>    
            <Button className="backCallLog" onClick={this.back}><i className="fa fa-arrow-left"></i></Button>        
            {/* <div className={(AgentCallDetails.length==0)?"verifybgImg2":"verifybgImg3"}>  */}
            <div className="verifybgImg2">             
                <img src={this.state.bgImg}/>  
              </div>
              </Col>
              <Col md={6} xs={12}> 
              <div className="lastfiveCl">
              <Row>
              <h3>Connected calls </h3> 
              <p className="caption">Your conversation details with Policybazaar advisors in the recent past</p>
              </Row>
                <Row>
         {this.displayCallDetails(AgentCallDetails) }
            </Row>
            </div>
            </Col>            
          
            </Row>
        </>)
    }

    renderSwitch(step,AgentCallDetails){
        switch (step) {
            case 1:
              
                return this.renderCallLogs(AgentCallDetails);
            case 2:
                
                    return   <AdvisorInfo
                    agentid = {this.state.agentid}
                    CustomerId = {this.props.CustomerId}
                    prevstep = {this.prevstep}
                    userIdEmp = {this.state.userIdEmp}
                    />
            
        }
    }

    render(){//debugger;

        const {AgentCallDetails } = this.props
        const {step} = this.state;

       return(
           this.renderSwitch(step,AgentCallDetails)
       )
      
    }
}


export default CallLogs;