import React, { Suspense, useEffect, useState } from "react";

import './SlotMachine.css';

const { createRef, Component } = React;

class Slots extends Component {


    constructor(props) {
        super(props);

        let newTickets = [...this.props.Tickets];
            if(newTickets.length<100){
                while(newTickets.length<100){
                    newTickets.push(...this.props.Tickets)
                }
            } 

        this.state = {
            fruit1: "1", rolling: false, defaultProps: {
                fruits: newTickets
            }

        };


        // get ref of dic onn which elements will roll
        this.slotRef = [createRef()];
    }

    componentWillReceiveProps(nextProps) {
        
        if (nextProps.Tickets && nextProps.Tickets.length>0) {
            let newTickets = [...nextProps.Tickets];
            
            if(newTickets.length<100){
                while(newTickets.length<100){
                    newTickets.push(...nextProps.Tickets)
                }
            
            } 

            let defaultProps = {
                fruits: newTickets
            }
            this.setState({defaultProps:defaultProps})
        }

    }

    // to trigger roolling and maintain state
    roll = () => {
        this.setState({
            rolling: true
        });
        setTimeout(() => {
            this.setState({ rolling: false });
            this.props.onResult(this.state.fruit1);
        }, 5500);

        // looping through all 3 slots to start rolling
        this.slotRef.forEach((slot, i) => {
            // this will trigger rolling effect
            const selected = this.triggerSlotRotation(slot.current);
            this.setState({ [`fruit${i + 1}`]: selected });
        });

    };

    // this will create a rolling effect and return random selected option
    triggerSlotRotation = ref => {
        function setTop(top) {
            ref.style.top = `${top}px`;
        }
        let options = ref.children;
        let randomOption = Math.floor(
            Math.random() * this.state.defaultProps.fruits.length
        );
        let choosenOption = options[randomOption];
        setTop(-choosenOption.offsetTop + 2);
        return this.state.defaultProps.fruits[randomOption];
    };

    render() {
        let defaultProps = this.state.defaultProps;
        return (
            <div className= { this.props.changeMachine ? "slotM" : "slotDown"}>
                
                {/* <img src="/lottery/Slot_1.svg" className="slotmachineImage" /> */}
                {this.props.changeMachine ? <div  className="slotmachineImageUp" ></div> :
                <div  className="slotmachineImageDown" ></div>}
            <div className="SlotMachine">
                {/* Total Tikets{defaultProps.fruits.length}
                 */}
                <div className="slot">
                    <section>
                        <div className="slot-container" ref={this.slotRef[0]}>
                            {defaultProps.fruits.map((fruit, i) => (
                                <div key={i}>
                                    <span>{fruit}</span>
                                </div>
                            ))}
                        </div>
                    </section>
                </div>
                
                {/* <div className="slot">
          <section>
            <div className="container" ref={this.slotRef[1]}>
              {Slots.defaultProps.fruits.map(fruit => (
                <div>
                  <span>{fruit}</span>
                </div>
              ))}
            </div>
          </section>
        </div>
        <div className="slot">
          <section>
            <div className="container" ref={this.slotRef[2]}>
              {Slots.defaultProps.fruits.map(fruit => (
                <div>
                  <span>{fruit}</span>
                </div>
              ))}
            </div>
          </section>
        </div> */}
                {/* <div
                    className={!this.state.rolling ? "roll rolling" : "roll"}
                    onClick={this.roll}
                    disabled={this.state.rolling}
                >
                    {this.state.rolling ? "Rolling..." : "ROLL"}
                </div> */}
                {/* {this.state.fruit1} */}
            </div>
            </div>
        );
    }
}

export default Slots;