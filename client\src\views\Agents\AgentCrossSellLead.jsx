
import React from "react";
import {
    GetCommonspDataV2
} from "../../store/actions/CommonAction";
import {
    addRecord
} from "../../store/actions/CommonMongoAction";
import { connect } from "react-redux";

import DataTable from '../Common/DataTableWithFilter';
import { fnDatatableCol } from '../../utility/utility.jsx';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";

import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Form } from "react-bootstrap";
import moment from "moment";
import Datetime from 'react-datetime';
import * as XLSX from 'xlsx';
import { LeadView, getuser } from '../../utility/utility.jsx';

class AgentCrossSellLead extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isLoaded: false,
            activePage: 1,
            root: "GetAgentCrossSellLead",
            PageTitle: "Cross-Sell/Referral Leads",
            FormTitle: "",
            formvalue: {},
            StartDate: moment().subtract(7, 'days').format("YYYY-MM-DD"),
            EndDate: moment().format("YYYY-MM-DD"),
            AgentCrossSellLeadItems: [],
            clickSearch: false,
            selectedtype: 1
        };
        this.HandleSearchClick = this.HandleSearchClick.bind(this)
        const Cell = ({ v }) => (
            <span title={v}>{(v) ? v.substring(0, 25)
                .toLowerCase()
                .split(' ')
                .map(word => {
                    return word.charAt(0).toUpperCase() + word.slice(1);
                })
                .join(' ') : ''}{(v && v.length > 25) ? '...' : ''}</span>
        );
        this.userId = getuser().UserID


        this.columnlist = [
            {
                name: 'Sno',
                label: 'S.No.',
                cell: (row, index) => index + 1,
                width: "60px!important"
            },
            {

                name: 'LeadID',
                label: "LeadID",
                type: "string",
                cell: row =>
                    <a href={LeadView(row.CustomerID, row.LeadID, row.ProductID, this.userId)} target="_blank">{row.LeadID} </a>
                ,
                sortable: true,
                width: '120px!important',
            },
            {
                name: "CustomerName",
                label: "Customer Name",
                cell: row => row.CustomerName ? <Cell v={row.CustomerName} /> : "N.A",
                sortable: true,
                searchable: false
            },
            {
                name: "AssignedUser",
                label: "Assigned User",
                cell: row => row.AssignedUser ? <Cell v={row.AssignedUser + ' (' + row.AssignedEmpId + ')'} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "AssignedGroup",
                label: "Assigned Group",
                cell: row => row.AssignedGroup ? <Cell v={row.AssignedGroup} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "Status",
                label: "Status",
                cell: row => row.Status ? <Cell v={row.Status} /> : "N.A",
                sortable: true,
                //searchable: true
            },
            {
                name: "ProductID",
                label: "Product ID",
                sortable: true,
                //searchable: true
            },
            {
                name: "LeadCreatedOn",
                label: "Lead CreatedOn",
                cell: row => row.LeadCreatedOn ? row.LeadCreatedOn : "N.A",
                type: "datetime",
                sortable: true,
            },
            {
                name: "LastContactedOn",
                label: "Last ContactedOn",
                cell: row => row.CallCreatedOn ? row.CallCreatedOn : "N.A",
                type: "datetime",
                sortable: true,
            }
        ];
    }

    handleExport = () => {

        const newArray = Array.isArray(this.state.AgentCrossSellLeadItems) && this.state.AgentCrossSellLeadItems.map(obj => {
            const { LeadID, CustomerName, AssignedUser, AssignedGroup, Status, LeadCreatedOn, LastContactedOn } = obj; // Use destructuring to remove extra keys
            return { LeadID, CustomerName, AssignedUser, AssignedGroup, Status, LeadCreatedOn, LastContactedOn };
        });
        if (Array.isArray(newArray) && newArray.length > 0) {
            const sheet = XLSX.utils.json_to_sheet(newArray);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
            XLSX.writeFile(workbook, 'AgentCrossSellLeadReport.xlsx');
        }
    };

    componentDidMount() {
        this.getAgentCrossSellLeads();
    }

    componentWillReceiveProps(nextProps) {

        if (!nextProps.CommonData.isError) {
            this.setState({ items: nextProps.CommonData[this.state.root] });
        }

    }

    fnDatatableCol() {
        var columns = fnDatatableCol(this.columnlist);
        return columns;
    }

    getAgentCrossSellLeads() {
        this.props.GetCommonspDataV2({
            root: 'AgentCrossSellLeads',
            params: [{ FromDate: this.state.StartDate, ToDate: this.state.EndDate }],
            c: "R",
        }, function (data) {
            this.setState({ clickSearch: false })
            if (data?.data?.data && data.data.data.length > 0 && data.data.data[0].length > 0) {
                let CrossSellLeadData = data.data.data[0];
                this.setState({ AgentCrossSellLeadItems: CrossSellLeadData.filter(item => item.LeadType == this.state.selectedtype) })
            } else {
                this.setState({ AgentCrossSellLeadItems: [] })
            }
        }.bind(this));

    }

    handleStartDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.setState({ StartDate: e.format("YYYY-MM-DD") }, function () {
            });
        }
    }

    handleEndDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.setState({ EndDate: e.format("YYYY-MM-DD") }, function () {
            });
        }
    }

    validation = (currentDate) => {
        const currentDatePlus45Days = new Date();
        currentDatePlus45Days.setDate(currentDatePlus45Days.getDate() - 45);
        if (currentDate.isBefore(moment(currentDatePlus45Days).format("YYYY-MM-DD"))) {
            return false;
        }
        if (!currentDate.isBefore(moment())) {
            return false;
        }
        return true;
    };

    validationEndDate = (currentDate) => {

        const currentDatePlus45Days = new Date();
        currentDatePlus45Days.setDate(currentDatePlus45Days.getDate() - 45);
        if (currentDate.isBefore(moment(currentDatePlus45Days).format("YYYY-MM-DD"))) {
            return false;
        }
        if (!currentDate.isBefore(moment())) {
            return false;
        }
        if (currentDate.isBefore(moment(this.state.StartDate))) {
            return false;
        }

        return true;

    };

    HandleSearchClick() {
        this.setState({ clickSearch: true })
        this.getAgentCrossSellLeads()
    }
    handleChange = (e) => {
        this.setState({ selectedtype: e.target.value })
        this.getAgentCrossSellLeads();
    };

    render() {
        const columns = this.fnDatatableCol();
        const { AgentCrossSellLeadItems, PageTitle, FormTitle, formvalue } = this.state;

        return (
            <>
                <div className="content">
                    <ToastContainer />
                    <Row>
                        <Col md="12">
                            <Card>
                                <CardHeader>
                                    <Row>
                                        <Col md={11}>
                                            <CardTitle tag="h4">{PageTitle}</CardTitle>
                                        </Col>
                                        <Col md={1}>

                                        </Col>
                                    </Row>
                                </CardHeader>
                                <CardBody>
                                    <Row>
                                        <Col md={2}>
                                            <Form.Label>Lead Type</Form.Label>
                                            <select
                                                className="form-select"
                                                value={this.state.selectedtype}
                                                onChange={this.handleChange}
                                            >
                                                <option value="1">Cross-sell Leads</option>
                                                <option value="2">Referral Leads</option>
                                            </select>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col md={2}>
                                            <Form.Label>Created From</Form.Label>

                                            <Datetime
                                                // value={new Date()}
                                                dateFormat="YYYY-MM-DD"
                                                value={this.state.StartDate}
                                                isValidDate={this.validation}
                                                onChange={moment => this.handleStartDateChange(moment)}
                                                utc={true}
                                                timeFormat={false}
                                                closeOnSelect={true}
                                            />
                                        </Col>
                                        <Col md={2}>
                                            <Form.Label>Created To</Form.Label>

                                            <Datetime
                                                // value={new Date()}
                                                dateFormat="YYYY-MM-DD"
                                                value={this.state.EndDate}
                                                isValidDate={this.validationEndDate.bind(this)}
                                                onChange={moment => this.handleEndDateChange(moment)}
                                                utc={true}
                                                timeFormat={false}
                                                closeOnSelect={true}
                                            />
                                        </Col>
                                        <Col md={2}>
                                            <button id='searchbtn' className="SearchBTN" onClick={this.HandleSearchClick}> Search <i class={this.state.clickSearch ? "fa fa-spinner fa-spin" : ""}></i></button>
                                        </Col>
                                    </Row>
                                    <Col md={12} className="mt-2">
                                        Lead Count: {AgentCrossSellLeadItems.length}
                                    </Col>
                                    {Array.isArray(AgentCrossSellLeadItems) && AgentCrossSellLeadItems.length > 0 ? <span onClick={this.handleExport} class="downloadExcel"></span> : ''}
                                    <DataTable
                                        columns={columns}
                                        data={Array.isArray(AgentCrossSellLeadItems) && AgentCrossSellLeadItems.length > 0 ? AgentCrossSellLeadItems : []}
                                        printexcel={false}
                                        pagination
                                        paginationPerPage={15}
                                    />

                                </CardBody>
                            </Card>
                        </Col>
                    </Row>

                </div>
            </>
        );
    }
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspDataV2,
    }
)(AgentCrossSellLead);